{"env": {"browser": true, "es6": true}, "globals": {"ga": true, "chrome": true, "DTOpenData": true, "wx": true, "WWOpenData": true}, "plugins": ["html", "json"], "extends": ["elemefe", "plugin:vue/recommended", "plugin:yxtcom/recommended"], "rules": {"no-restricted-globals": ["error", "event", "fdescribe"], "no-debugger": "off", "vue/multiline-html-element-content-newline": 0, "vue/singleline-html-element-content-newline": 0, "vue/max-attributes-per-line": ["error", {"singleline": 3, "multiline": {"max": 1}}], "vue/html-indent": ["error", 2, {"attribute": 1, "baseIndent": 1, "closeBracket": 0, "alignAttributesVertically": true, "ignores": []}], "prefer-promise-reject-errors": ["error", {"allowEmptyReject": true}], "vue/mustache-interpolation-spacing": "error", "vue/no-multi-spaces": "error", "vue/attributes-order": ["error", {"order": ["DEFINITION", "LIST_RENDERING", "CONDITIONALS", "RENDER_MODIFIERS", "GLOBAL", "UNIQUE", "TWO_WAY_BINDING", "OTHER_DIRECTIVES", "OTHER_ATTR", "EVENTS", "CONTENT"], "alphabetical": false}], "vue/require-valid-default-prop": 0, "vue/require-prop-types": 0, "vue/multi-word-component-names": 0, "vue/order-in-components": 0, "vue/require-default-prop": 0, "vue/no-mutating-props": 0, "vue/no-v-html": 0, "vue/html-self-closing": ["error", {"html": {"void": "never", "normal": "never", "component": "always"}, "svg": "always", "math": "always"}], "no-duplicate-imports": ["error", {"includeExports": true}], "no-redeclare": "error", "radix": ["error", "as-needed"]}, "parserOptions": {"ecmaVersion": 10, "ecmaFeatures": {"jsx": true}}}