import { udpApi, coreApi } from '@/core/apis';
import { Encrypt } from '@/core/crypto';

export const debugLogin = () => {
  return udpApi.post('users/login', {
    // domain: 'dingfei1981.qida.yunxuetang.com.cn',
    // username: 'dingf',
    domain: 'http://gu-phx.yunxuetang.com.cn/',
    username: 'yxtadmin',
    password: '112233'
  });
};

export const debugLogin2 = () => {
  return coreApi.post('auth/password', {
    // domain: 'dingfei1981.qida.yunxuetang.com.cn',
    // username: 'dingf',
    domain: 'xx-phx.yunxuetang.com.cn',
    userName: 'admin',
    password: '123456',
    randstr: '',
    ticket: ''
  });
};

export const debugLoginEncrypt = () => {
  const timeStamp = new Date().valueOf();
  return coreApi.post('auth/password', {
    // domain: 'dingfei1981.qida.yunxuetang.com.cn',
    // username: 'dingf',
    domain: 'pro-phx.yunxuetang.com.cn',
    userName: window.btoa('admin'),
    password: Encrypt('Chengjiukehu@2023' + '/' + timeStamp),
    randstr: '',
    ticket: ''
  });
};
