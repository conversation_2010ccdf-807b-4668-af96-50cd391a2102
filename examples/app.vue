<template>
  <div id="app" :class="{ 'is-component': isComponent }">
    <main-header v-if="lang !== 'play' && !isTemplate"></main-header>
    <div class="main-cnt">
      <router-view></router-view>
    </div>
    <!-- <main-footer v-if="lang !== 'play' && !isComponent"></main-footer> -->
  </div>
</template>

<script>
  import { use } from 'main/locale';
  import zhLocale from 'main/locale/lang/zh-CN';
  import enLocale from 'main/locale/lang/en';
  import esLocale from 'main/locale/lang/es';
  import frLocale from 'main/locale/lang/fr';

  const lang = location.hash.replace('#', '').split('/')[1] || 'zh-CN';
  const localize = lang => {
    switch (lang) {
      case 'zh-CN':
        use(zhLocale);
        break;
      case 'es':
        use(esLocale);
        break;
      case 'fr-FR':
        use(frLocale);
        break;
      default:
        use(enLocale);
    }
  };
  localize(lang);

  export default {
    name: 'app',

    computed: {
      lang() {
        return this.$route.path.split('/')[1] || 'zh-CN';
      },
      isComponent() {
        return /^component-/.test(this.$route.name || '');
      },
      isTemplate() {
        return !!this.$route.meta.isTemplate;
      }
    },

    watch: {
      lang(val) {
        localize(val);
      }
    },

    methods: {
    },

    mounted() {
      localize(this.lang);
    }
  };
</script>
