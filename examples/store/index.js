import Vue from 'vue';
import Vuex from 'vuex';
const debug = process.env.NODE_ENV !== 'production';

Vue.use(Vuex);
const store = new Vuex.Store({
  modules: {

  },
  state: {
    pageTitle: ''
  },
  mutations: {
    setPageTitle(state, title) {
      state.pageTitle = title;
    }
  },
  actions: {
    setPageTitle(state, title) {
      store.commit('setPageTitle', title);
    }
  },
  strict: debug
});
export default store;
