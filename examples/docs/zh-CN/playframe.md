## 播放框架
基于course-page播放



:::demo

:::## Playframe playframe

:::

### Attributes

| 参数      | 说明                                                                       | 类型    | 可选值 | 默认值 |
| --------- | -------------------------------------------------------------------------- | ------- | ------ | ------ |
| courseId    | 课程包id                       | String  |        |        |
| courseDetail      | 包信息\单知识信息   | Object  |        | 0      |
| catalogLists | 课程包树信息                                                                 | Object |        | true   |

 ### Methods

| 事件名       | 说明                                   | 入参                       |
| ------------ | -------------------------------------- | -------------------------- |
| setKng | 切换知识 | 切换知识id，冗余参数，默认返回 |
| playNext | 下一节 |  |
| playPrevious | 上一节 |  |
| fullScreen | 全屏 | 是否全屏、退出全屏 |
| updateProgress | 学习状态更新 | 学习状态，0未学习，1学习中，2已完成  |

