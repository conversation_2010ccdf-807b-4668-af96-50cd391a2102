## UacdPlayFrame 项目模型播放器


:::demo

```html
<template>
  <yxt-ulcd-sdk-uacd-play-frame :projectId="projectId" :taskId="taskId" actvRegId="proj_o2o"></yxt-ulcd-sdk-uacd-play-frame>
  
</template>
<script>
export default {
    data() {
      return{
        projectId: '0b9a7ead-0ee4-4b3a-8bfe-6c0df3f47caa',
        taskId: ''
        // taskId: '1856226189086132225'
      }
    },
    methods:{ 

    }
}
</script>
<style lang="scss">
.demo-o2o-play-frame {
  background-color: darkgray;
}

</style>
```
:::

### Attributes

| 参数      | 说明       | 类型      | 可选值 | 默认值 |
| --------- |----------|---------| ------ | ------ |
| actvRegId    | 业务注册的编号 | string  |        |        |
| inner | 是否沉浸式    | Boolean |        | true   |

### params - 原页面参数
| 参数       | 说明                            | 类型   | 必须 |
| ---------- | ------------------------------- | ------ | ---- |

### Methods

| 事件名       | 说明                                   | 入参                       |
| ------------ | -------------------------------------- | -------------------------- |
| updateProgress | 进度更新 | 学习状态，1学习中，2已完成 |

### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | ------------------ |
| fullScreen | 全屏 | 是否全屏、退出全屏 |
