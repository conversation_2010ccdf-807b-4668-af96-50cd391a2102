## SimpleCoursePage 课程播放页（没有学时）

### 组件依赖
#### - 业务组件依赖：
yxtbiz-user-name、yxtbiz-dept-name、yxtbiz-complain、yxtbiz-qrcode、yxtbiz-language-slot、yxtbiz-gratuity、yxtbiz-doc-viewer、yxtbiz-video、yxtbiz-kng-scorm-player、yxtbiz-watermark、yxtbiz-image-viewer、yxtbiz-upload、yxtbiz-attachment-check
#### - 第三方组件依赖：微课播放器，业务方需要加载对应资源，有两种加载方式

1.同步：html模版中直接加载对应资源
``` javascript
...
<%  var panguPcPath = feConfig.packages.panguPlayer3Umd.path; %>
...
<% if (panguPcPath) { %>
  <link rel="stylesheet" type="text/css" href="<%= panguPcPath %>style.css" />
<% } %>
...
<% if (panguPcPath) { %>
  <script type="text/javascript" src="<%= panguPcPath %>index.min.js"></script>
<% } %>
```

2.异步：需要在window上写入盘古播放器资源地址（目前播放器资源地址只支持编译环境下获取），由播放器播放微课前去加载资源
``` javascript
...
<%  var panguPcPath = feConfig.packages.panguPlayer3Umd.path; %>
...
<script> window.PAN_GU_PC_PATH = "<%= panguPcPath %>" </script>
...
```

:::demo
```html
<template>
  <div class="yxtulcdsdk-flex-center mb12">
    课程ID：<yxtf-input class="width480" v-model="newId"></yxtf-input>
    <yxtf-checkbox class="ml12" v-model="newTag">是否是课程包</yxtf-checkbox>
    <yxtf-button class="ml12" type="primary" @click=load>加载</yxtf-button>
  </div>
  <yxt-ulcd-sdk-simple-course-page v-if="id" :id='id' :isCourse="isCourse" :target="{}"></yxt-ulcd-sdk-simple-course-page>
</template>
<script>
  export default {
    data() {
      return {
        id:sessionStorage.kngId || 'd1d44e98-883c-4cb0-a44d-be25b81d718e',
        isCourse:sessionStorage.isCourse !== '0',
        newId:sessionStorage.kngId || 'd1d44e98-883c-4cb0-a44d-be25b81d718e',
        newTag:sessionStorage.isCourse !== '0'
      }
    },
    methods:{
      load() {
        this.id = ''
        this.$nextTick(() => {
          sessionStorage.isCourse = this.newTag ? '1' : '0'
          sessionStorage.kngId = this.newId
          this.isCourse = this.newTag
          this.id = this.newId
        })
      }
    }
  }
</script>
```
:::

### Attributes

| 参数      | 说明                  | 类型    | 是否必填 | 默认值 |
| -------- | -------------------- | ------- | ------- | ------ |
|    id    |          知识id       | String  |   是     |     - |
|    isCourse    |        是否是课程包    | Boolean  |   否     |     否 |
|    target    |  业务方使用在线课学习需要的数据对象：{targetId,targetCode,taskId,projectId,flipId,originOrgId,batchId}   | Object  |   否     |     {} |
| mode | 模式：预览（preview）、游客（visitor）| String | 否 | preview |
| previewType | 预览类型（mode为preview时使用） | Number | 否 | 1 |


### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | --------------- |
| updateSchedule | 课件进度变化回调 | 进度值，100制 |
