## Practicing 练习  

依赖的组件：
1. 【browser】  injectCommonFiles: ['browserjs']

### 下面是正常练习

:::demo

```html
<template>
  <div class="mb12 flex felx-center">
   <span>praId：</span><yxt-input v-model="params.praId" />
  </div>
  <div style="
    display: flex;
    flex-direction: column;
    align-items: center;
    width: calc(100% - 64px);
    height: 600px;
    padding: 16px 32px;
    overflow: auto;
    background-color: black;
" 
    class="yxtulcdsdk-ulcdsdk">
    <yxt-ulcd-sdk-practicing :key="params.praId" :params="params"></yxt-ulcd-sdk-practicing>
  </div>
</template>
<script>
export default {
    data() {
      return{ 
        params: { 
          praId: '7ebda2b5-315c-4d34-a267-793c87e6b2da'
        }
      }
    },
    methods:{ 
    }
}
</script>
```
:::

### 随堂练习接入

:::demo

```html
<template>
  <div class="mb12 flex felx-center">
   <span>praId：</span><yxt-input v-model="params.praId" />
  </div>

  <yxt-button type="primary" @click="step = 0;doneConfirm = !doneConfirm;">显示/隐藏(有预览页)</yxt-button>
  <yxt-button type="primary" @click="step = 1;doneConfirm = !doneConfirm;">显示/隐藏(无预览页)</yxt-button>

  <template v-if="doneConfirm">
    <yxtf-dialog
      padding-size="medium"
      :visible.sync="doneConfirm"
      title="随课练习"
      width="640px"
      custom-class="yxtulcdsdk-ulcdsdk yxtulcdsdk-testdialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="false"
    >
      <yxt-ulcd-sdk-practicing
        ref="practice"
        :key="params.praId"
        :params="params"
        :step="step"
        course-practice
        deep-study
        @success="successEvent"
        @complete="completeEvent"
        @finish="finishEvent"
        @error="errorEvent"
      />
    </yxtf-dialog>
  </template>
</template>
<script>
export default {
  data() {
    return{ 
      params: { 
        praId: '814bb064-b1d4-42ce-9a1d-ca6b8d735390'
      },

      doneConfirm: false,
      step: 0
    }
  },
  methods:{
    successEvent (info) {
      console.log('successEvent', info)
    },
    completeEvent (info) {
      console.log('completeEvent', info)
      this.doneConfirm = false;
    },
    finishEvent (info) {
      console.log('finishEvent', info)
    },
    errorEvent (info) {
      console.log('errorEvent', info)
    }
  }
}
</script>
```
:::


### Attributes

| 参数      | 说明                                                                       | 类型    | 可选值 | 默认值 |
| --------- | -------------------------------------------------------------------------- | ------- | ------ | ------ |
| params    | 原练习页面传的参数。 必要参数: praId 练习ID                        | Object  |        |        |
| step      | 当前练习的步骤,    0：练习预览   1：练习答题   | Number  |        | 0      |
| deepStudy | 是否沉浸式                                                                 | Boolean |        | true   |
| radius    | 组件边框是否需要圆角                                                       | Boolean |        | true   |
| coursePractice | 是否随堂练习                                                                | Boolean |        | true   |
| scrollInner | 内部滚动的模式                                                           | Boolean |        | true   |
| fullScreen | 是否显示全屏按钮                                                             | Boolean |        | true   |


### params - 练习预览
| 参数       | 说明                            | 类型   | 必须 |
| ---------- | ------------------------------- | ------ | ---- |
| praId  | 练习安排ID                      | string | 是   |
| btid       | 在线课堂批次ID                  | string |      |
| trackId    | ssp学习链路ID                   | string |      |
| gwnlUrl    | 岗位能力面包屑地址              | string |      |
| praBatchId    | 循环练习批次ID              | string |      |
| taskId     | o2o面包屑、上一个下一个用的参数 | string |      |
| targetId   | o2o面包屑、上一个下一个用的参数 | string |      |
| targetCode | o2o面包屑、上一个下一个用的参数 | string |      |
| masterId   | 第三方考试来源ID                | string |      |
| masterType | 第三方考试来源类型              | string |      |
| packageId  | 课程包ID                        | string |      |

   
 ### Methods

| 事件名       | 说明                                   | 入参                       |
| ------------ | -------------------------------------- | -------------------------- |
| confirmLeave | 如果要离开当前组件，调用此方法进行确认 | 1.是否能离开的回调确认方法 |

### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | ------------------ |
| fullScreen | 全屏 | 是否全屏、退出全屏 |
| success | 在线课-基础信息请求成功 | courseInfo |
| complete | 在线课-练习提交成功 | courseInfo |
| error | 在线课-练习报错回调 | courseInfo |


### CourseInfo
| 属性     | 说明 |
| ---------- | ---- |
| praId | 练习Id |
| praName | 练习Name |
| repeated | 是否允许重复练习 |
| type | preview(预览)、practing(练习中) |
| isFinished | 练习是否完成 |

