## WrongItem 错题本单题

### 错题本单题，不提供沉浸式使用，考试自身调用

:::demo

```html
<template>
  <yxt-ulcd-sdk-wrong-item
    :value="value"
    :mode="mode"
    :title-settings="titleSettings"
    :index="index"
    @select="selectSingle">
  </yxt-ulcd-sdk-wrong-item>
</template>
<script>
export default {
  data() {
    const QUES_TYPE_NAMES = ['pc_ote_lbl_singlechoice', 'pc_ote_lbl_multiplechoice', 'pc_ote_lbl_judgment', 'pc_ote_lbl_fillin', 'pc_ote_lbl_question', 'pc_ote_lbl_combinatorialquestions']

    return{ 
      value: {
        "id": "b89de072-af54-467c-9fa8-99aeb470e598", 
        "quesId": "aca4e005-21ff-41b9-b13e-d72f14345016", 
        "wrongNum": 24, 
        "quesType": 2, 
        "levelType": 0, 
        "explainText": "植物类", 
        "pointNames": [
          "默认考点"
        ], 
        "mediaType": 0, 
        "fileId": null, 
        "choiceItems": null, 
        "fillItems": null, 
        "subQues": 0, 
        "parentId": null, 
        "subQuesItem": null, 
        "content": "花是种子植物特有的器官。", 
        "quesRefreshTime": null, 
        "answerContent": null, 
        "answerKeyword": null, 
        "noOrderMark": 0, 
        "judgeAnswer": 0, 
        "currentStatus": null, 
        "playDetails": null, 
        "tranStatus": null, 
        "answerPlay": null, 
        "explainPlay": null, 
        "addBackup": 0, 
        "isShowMore": false, 
        "quesUISelected": false, 
        "isShowMoreText": false, 
        "userAnswers": [ ], 
        "isAnswered": false, 
        "correctAnswer": "错误"
      },
      mode: 'itemList',
      titleSettings: QUES_TYPE_NAMES.map(name => ({ title: this.$t(name) })),
      index: 0
    }
  },
  methods:{
    selectSingle(item, checked) {
      console.log(item)
      console.log(checked)
    }
  }
}
</script>
```
:::