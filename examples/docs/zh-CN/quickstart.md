## 快速上手

本节将介绍如何在项目中使用 yxt-ulcd-sdk。

### 引入 yxt-ulcd-sdk

你可以引入整个 Element，或是根据需要仅引入部分组件。我们先介绍如何引入完整的 Element。

#### 完整引入

在 main.js 中写入以下内容：

```javascript
import Vue from 'vue';
import YxtUlcdSdk from 'yxt-ulcd-sdk';
import 'yxt-ulcd-sdk/lib/theme-chalk/index.css';
import App from './App.vue';

Vue.use(YxtUlcdSdk, {
  env: 'dev',
  source: 101
});

new Vue({
  el: '#app',
  render: h => h(App)
});
```

以上代码便完成了 yxt-ulcd-sdk 的引入。需要注意的是，样式文件需要单独引入。
需指定当前环境变量env(可选参数dev、prod)及对应微服务的soure

#### 按需引入

借助 [babel-plugin-component](https://github.com/QingWei-Li/babel-plugin-component)，我们可以只引入需要的组件，以达到减小项目体积的目的。

首先，安装 babel-plugin-component：

```bash
npm install babel-plugin-component -D
```

然后，将 .babelrc 修改为：

```json
{
  "presets": [["es2015", { "modules": false }]],
  "plugins": [
    [
      "component",
      {
        "libraryName": "yxt-ulcd-sdk",
        "styleLibraryName": "theme-chalk"
      }
    ]
  ]
}
```

接下来，如果你只希望引入部分组件，比如 Button 和 Select，那么需要在 main.js 中写入以下内容：

```javascript
import Vue from 'vue';
import { Header, Footer } from 'yxt-ulcd-sdk';
import App from './App.vue';

Vue.component(Header.name, Header);
Vue.component(Footer.name, Footer);
/* 或写为
 * Vue.use(Header)
 * Vue.use(Footer)
 */

new Vue({
  el: '#app',
  render: h => h(App)
});
```

完整组件列表和引入方式（完整组件列表以 [components.json](http://*************/guyy/yxt-ulcd-sdk/blob/master/components.json) 为准）

```javascript
import Vue from 'vue';
import {
  Header,
  Footer
} from 'yxt-ulcd-sdk';

Vue.use(Header);
Vue.use(Footer);
```

### 开始使用

至此，一个基于 Vue 和 yxt-ulcd-sdk 的开发环境已经搭建完毕，现在就可以编写代码了。各个组件的使用方法请参阅它们各自的文档。

