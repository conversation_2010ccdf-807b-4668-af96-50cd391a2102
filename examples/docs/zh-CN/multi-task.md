## MultiTask 多班次列表

:::demo

```html
<template>
  <div style="background-color: #F5F5F5;">
    <yxt-ulcd-sdk-multi-task :project-id="projectId" :task-id="taskId" />
  </div>
</template>
<script>
  export default {
    data() {
      return {
        taskId: "1763123721790306305",
        projectId: '1759486381535528961',
        roomId: "827064321", // 直播id
        trackId:
          "MTAwMS4xNjU0NDMyODk3OTUyNzEwNjU4LjE2NTQ0MzMxMDU2MTY4OTYwMDEuMC4wLjA%3D",
      };
    },
  };
</script>
```

:::
