## Activity activity
:::demo

```html
<template>
  <yxt-ulcd-sdk-activity :taskId="taskId"></yxt-ulcd-sdk-activity>
  
</template>
<script>
export default {
    data() {
      return{ 
        taskId:'1637759595574263809'
      }
    },
    methods:{ 

    }
}
</script>
<style lang="scss">
.demo-o2oPlayFrame {
  background-color: darkgray;
}

</style>
```
:::

### Attributes

| 参数      | 说明                                                                       | 类型    | 可选值 | 默认值 |
| --------- | -------------------------------------------------------------------------- | ------- | ------ | ------ |
| taskId    | 任务id                      | String  |        |        |

   