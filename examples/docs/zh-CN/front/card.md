## 卡片

:::demo
```html
<yxtf-card class='yxtbizf-card-width-312 mb24' :body-style="{ padding: '24px'}" shadow='never'>
  <yxtf-row type='flex' justify='space-between' align='middle' class='mb24' style='height:26px'>
    <span class='standard-size-18 yxtf-weight-5 color-gray-10'>猜你喜欢</span>
    <yxtf-button type="text">偏好设置<i class="yxtf-icon-arrow-right ml8"/></yxtf-button>
  </yxtf-row>
  <div style='height:402px' class='bg-primary-3'></div>
</yxtf-card>

<yxtf-card class='yxtbizf-card-width-422 mb24' :body-style="{ padding: '24px 24px 32px 24px'}" shadow='never'>
  <yxtf-row type='flex' justify='space-between' class='mb32' style='height:26px'>
    <span class='standard-size-18 yxtf-weight-5 color-gray-10'>学习任务</span>
    <yxtf-button type="text">查看更多<i class="yxtf-icon-arrow-right ml8"/></yxtf-button>
    </yxtf-row>
  <div style='height:150px' class='bg-primary-3'></div>
</yxtf-card>


<yxtf-card :body-style="{ padding: '0 0 16px 0'}" class='yxtbizf-card-width-312' shadow='never'>
      <div class='bg-primary-3 text-center' style='height:176px'>图片区域</div>
      <div style='padding:12px 12px 0 12px'>
        <div class='standard-size-16 yxtf-weight-5 color-gray-10 mb4'>打造卓越PPT的逻辑思维——无逻PPT</div>
        <div class='standard-size-14 color-gray-8 mb24'>手把手带你学习从项目创建到项目开发，完美契合企业需求</div>
        <div class='standard-size-12 color-gray-7'>258人学习</div>
      </div>
</yxtf-card>

```
:::