## 弹框

:::demo
```html

<yxtf-button @click="dialogVisible = true">弹框</yxtf-button>

<yxtf-dialog
  :visible.sync="dialogVisible"
  width="640px">
  <div slot='title'>
  <span class='color-gray-10 mr16'>批量修改</span><span class='color-gray-7'>将应用于2个已选文件</span>
  </div>
   <yxtf-form :model="faceForm" status-icon :rules="faceRules" ref="faceForm" label-width="85px">
              <yxtf-form-item label="目录"  required>
                  <yxtf-select placeholder="请选择" v-model='value'>
                  </yxtf-select>
              </yxtf-form-item>
               <yxtf-form-item label="标签" >
               <yxtf-tag
                 class='mr16'
                 :key="tag"
                 type="info"
                 v-for="tag in dynamicTags"
                 closable
                 :disable-transitions="false"
                 @close="handleClose(tag)">
                 {{tag}}
               </yxtf-tag>
               <yxtf-input
                 style='width:96px'
                 class='lh32'
                 v-if="inputVisible"
                 v-model="inputValue"
                 ref="saveTagInput"
                 size="small"
                 @keyup.enter.native="handleInputConfirm"
                 @blur="handleInputConfirm"
               >
               </yxtf-input>
               <yxtf-button v-else @click="showInput">添加标签</yxtf-button>
               <div class='color-gray-6'>最多添加5个标签，每个标签在2~10个字符</div>
               <div>
               <span class='mr16 color-gray-10'>推荐</span>
               <yxtf-tag
                         :key="tag"
                         effect="special"
                         v-for="tag in dynamicTags">
                                {{tag}}
                              </yxtf-tag></div>
                </yxtf-form-item>

                <yxtf-form-item label="简介">
                  <yxtbiz-richeditor style='width:458px' :menus='menus' app-code="kng" org-code="xxv2" function-name="richeditor" module-name="test"></yxtbiz-richeditor>
                </yxtf-form-item>
                <yxtf-form-item label="作者">
                  <yxtf-row type='flex'>
                    <yxtf-input class='mr16'></yxtf-input>
                    <yxtf-checkbox>原创</yxtf-checkbox>
                  </yxtf-row>
                </yxtf-form-item>
                <div>
                <span class='mr16 color-gray-10'>分享权限</span>
                <yxtf-checkbox>允许Ta人分享</yxtf-checkbox>
                </div>
            </yxtf-form>
  <span slot="footer" class="dialog-footer">
    <yxtf-button @click="dialogVisible = false">取 消</yxtf-button>
    <yxtf-button type="primary" @click="dialogVisible = false">确 定</yxtf-button>
  </span>
</yxtf-dialog>

<script>
  export default {
    data() {
      return {
        dialogVisible: false,
        faceForm: {},
        faceRules: {},
        value: '',
        dynamicTags: ['培训', '学习'],
        inputVisible: false,
        inputValue: '',
        menus: ['head', 'bold', 'fontSize', 'italic', 'underline', 'strikeThrough', 'superscript', 'subscript', 'foreColor']
      };
    },
    methods: {
       handleClose(tag) {
              this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
            },

            showInput() {
              this.inputVisible = true;
              this.$nextTick(_ => {
                this.$refs.saveTagInput.$refs.input.focus();
              });
            },

            handleInputConfirm() {
              let inputValue = this.inputValue;
              if (inputValue) {
                this.dynamicTags.push(inputValue);
              }
              this.inputVisible = false;
              this.inputValue = '';
            }
    }
  };
</script>

```
:::



