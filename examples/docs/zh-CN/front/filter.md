::: demo

```html
<template>
    <div class="template5" @click="hidePanel">
        <div class="template5-filter2 bg-white">
            <div class="clearfix mt24 mutiple-filter">
                <div class="pull-left pr24">
                    <yxt-button plain @click.stop="showBox" class="filter-btn">
                        <yxt-svg width="20px" height="20px" icon-class="filter" class="filter-icon"/>
                        <span style="margin-left:15px;">更多筛选</span>
                        <i :class='boxVisible ? "yxt-icon-arrow-up": "yxt-icon-arrow-down"'></i></yxt-button>
                </div>
                <div class="pull-left pr24 mb24"
                     :class="[{'mb16': showPoint || showTypes || showFilter1 || showFilter2 || showStatus}]">
                    <span class='mr16 standard-size-14 d-in-block color-gray-10'>题型</span>
                    <yxtf-date-picker
                            style='width:260px'
                            v-model="value"
                            range-separator='~'
                            type="daterange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
                    </yxtf-date-picker>
                </div>
                <div class="pull-left pr24 mb24"
                     :class="[{'mb16': showPoint || showTypes || showFilter1 || showFilter2 || showStatus}]">
                    <yxtf-input
                            placeholder="请输入试题名称模糊搜索"
                            suffix-icon="yxt-icon-search"
                            v-model="searchTxt" style="width:250px;">
                    </yxtf-input>
                </div>
                <div class="pull-left pr24 filter-box-item mb16 input-left" v-if="showFilter1">
                   <yxt-row type='flex' align='middle'>
                    <span class="colorStyle">筛选：</span>
                    <yxtf-select v-model="filter1" placeholder="请选择" style="width:136px;">
                        <yxtf-option
                                v-for="item in filters1"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                        >
                        </yxtf-option>
                    </yxtf-select>
                  <yxtf-svg  @click.stop.native="showFilter1=false"
                                                                                remote-url='https://media-phx.yunxuetang.com.cn/common/pc_front_svg'
                                                                                class='color-gray-6 hover-primary-6 hand' width="14px" height="14px"
                                                                                icon-class="delete-facial" />
                    </yxt-row>
                </div>
                <div class="pull-left pr24 filter-box-item input-left" v-if="showFilter2"
                     :class='showPoint || showTypes || showFilter1 || showFilter2 || showStatus? "mb24": ""'>
                     <yxt-row type='flex' align='middle'>
                    <span class="colorStyle">筛选：</span>
                    <yxtf-select v-model="filter2" placeholder="请选择" style="width:136px;">
                        <yxtf-option
                                v-for="item in filters2"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                        >
                        </yxtf-option>
                    </yxtf-select>
                      <yxtf-svg  @click.stop.native="showFilter2=false"
                                                               remote-url='https://media-phx.yunxuetang.com.cn/common/pc_front_svg'
                                                               class='color-gray-6 hover-primary-6 hand' width="14px" height="14px"
                                                               icon-class="delete-facial" />
                      </yxt-row>
                </div>
                <div class="pull-left pr24 filter-box-item input-left" v-if="showStatus"
                     :class='showPoint || showTypes || showFilter1 || showFilter2 || showStatus? "mb24": ""'>
                    <yxt-row type='flex' align='middle'>
                      <span class="colorStyle">状态：</span>
                      <yxtf-select v-model="status" placeholder="请选择" style="width:136px;">
                          <yxtf-option
                                v-for="item in statuses"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                          >
                          </yxtf-option>
                      </yxtf-select>
                      <yxtf-svg  @click.stop.native="showStatus=false"
                               remote-url='https://media-phx.yunxuetang.com.cn/common/pc_front_svg'
                               class='color-gray-6 hover-primary-6 hand' width="14px" height="14px"
                               icon-class="delete-facial" />
                     </yxt-row>
                </div>
                <div class="pull-left pr24 filter-box-item input-left" v-if="showPoint"
                     :class='showPoint || showTypes || showFilter1 || showFilter2 || showStatus? "mb24": ""'>
                    <yxt-row type='flex' align='middle'>
                    <span class="colorStyle">考核点：</span>
                    <yxtf-select v-model="checkPoint" placeholder="请选择" style="width:120px;">
                        <yxtf-option
                                v-for="item in checkPoints"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                        >
                        </yxtf-option>
                        <yxtf-option class="option-edit" label="编辑" value="编辑" style="border-top: 1px solid #f0f0f0;"><i
                                class="yxt-icon-edit-outline" style="padding-right:5px"></i>编辑
                        </yxtf-option>
                    </yxtf-select>
                    <yxtf-svg  @click.stop.native="showPoint=false"
                                              remote-url='https://media-phx.yunxuetang.com.cn/common/pc_front_svg'
                                              class='color-gray-6 hover-primary-6 hand' width="14px" height="14px"
                                              icon-class="delete-facial" />
                      </yxt-row>
                </div>
                <div class="pull-left pr24 mySelect filter-box-item" v-if="showTypes"
                     style="border:1px solid #f0f0f0;width:198px;"
                     :class='showPoint || showTypes || showFilter1 || showFilter2 || showStatus? "mb16": ""'>
                    <span class="colorStyle">类型：</span>
                    <div class="mutiple-select" style="border:none;width:132px"
                         @click.stop="showDropdowm2=!showDropdowm2">
                        <span class="mutiple-select-span" style="padding:0;"
                              v-if="checkTypes.length">{{checkTypes[0]}}</span>
                        <span class="mutiple-select-span" v-else
                              style="color:rgb(186, 189, 196);padding-left:0">请选择</span>
                        <span class="mutiple-select-span" style="padding:0" v-if="checkTypes.length">已选{{checkTypes.length}}项</span>
                        <span class="mutiple-select-span" style="float:right">
                  <i :class='showDropdowm2 ? "yxt-icon-arrow-up": "yxt-icon-arrow-down"'></i>
                </span>
                        <div class="mutiple-select-dropdown" v-show="showDropdowm2">
                            <div style="border-bottom: 1px solid #f0f0f0;width:100%;">
                                <yxt-checkbox v-model="checkAll2" @change="handleCheckAllChange2"
                                              :indeterminate="isIndeterminate2">
                                    全选
                                </yxt-checkbox>
                            </div>
                            <yxt-checkbox-group v-model="checkTypes" @change="changeSelect2">
                                <div>
                                    <yxt-checkbox v-for="item in types" :label="item" :key="item">
                                        {{item}}
                                    </yxt-checkbox>
                                </div>
                            </yxt-checkbox-group>
                        </div>
                    </div>
                    <span><i class="yxt-icon-error" @click.stop="showTypes=false"></i></span>
                </div>
                <div class="clear pull-left"
                     v-if="showPoint || showStatus || showFilter2 || showFilter1 || showTypes" @click.stop="clearAll">
                     <yxtf-button type='text'>清空</yxtf-button>
                </div>
            </div>
            <div class="template5-filter-box clearfix yxtfbiz-filter-box " v-if="boxVisible">
                <div class="template5-filter-left" @click.stop="closeBox">
                    <yxt-button plain>关闭筛选</yxt-button>
                </div>
                <div class="pl20 template5-filter-right">
                    <div class="pr24 pb16 d-in-block">
                        <span class='mr16 standard-size-14 d-in-block color-gray-10'>状态</span>
                        <yxtf-select v-model="status" placeholder="请选择" style="width:136px;" @change="showStatus=true">
                            <yxtf-option
                                    v-for="item in statuses"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </yxtf-option>
                        </yxtf-select>
                    </div>
                    <div class="pr24 pb16 d-in-block">
                        <span class='mr16 standard-size-14 d-in-block color-gray-10'>筛选</span>
                        <yxtf-select v-model="filter1" placeholder="请选择" style="width:136px;" @change="showFilter1=true">
                            <yxtf-option
                                    v-for="item in filters1"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </yxtf-option>
                        </yxtf-select>
                    </div>
                    <div class="pr24 pb16 d-in-block">
                        <span class='mr16 standard-size-14 d-in-block color-gray-10'>筛选</span>
                        <yxtf-select v-model="filter2" placeholder="请选择" style="width:136px;" @change="showFilter2=true">
                            <yxtf-option
                                    v-for="item in filters2"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </yxtf-option>
                        </yxtf-select>
                    </div>
                    <div class="pr24 pb16 d-in-block">
                        <span class='mr16 standard-size-14 d-in-block color-gray-10'>题型</span>
                        <yxtf-select v-model="questionType" placeholder="请选择" style="width:136px;">
                            <yxtf-option
                                    v-for="item in questionTypes"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </yxtf-option>
                        </yxtf-select>

                    </div>
                    <div class="pr24 pb16 d-in-block">
                        <span class='mr16 standard-size-14 d-in-block color-gray-10'>考核点</span>
                        <yxtf-select v-model="checkPoint" placeholder="请选择" style="width:136px;"
                                    @change="showPoint=true">
                            <yxtf-option
                                    v-for="item in checkPoints"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                            >
                            </yxtf-option>
                            <yxtf-option class="option-edit" label="编辑" value="编辑"
                                        style="border-top: 1px solid #f0f0f0;"><i class="yxt-icon-edit-outline"
                                                                                  style="padding-right:5px"></i>编辑
                            </yxtf-option>
                        </yxtf-select>
                    </div>
                </div>
            </div>
            <yxtf-button type="primary">新建试题<i class="yxt-icon-arrow-down yxt-icon--right"></i></yxtf-button><yxtf-button plain class="submit">提交审核</yxtf-button>
        </div>
        <div class="template5-filter1 bg-white">
                    <div class="clearfix mt24">
                        <div class="pull-left pr24 mb24">
                            <span class='mr16 standard-size-14 d-in-block color-gray-10'>选择日期</span>
                            <yxtf-date-picker
                                    style='width:260px'
                                    v-model="value"
                                    range-separator='~'
                                    type="daterange"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :default-time="['00:00:00', '23:59:59']">
                            </yxtf-date-picker>
                        </div>
                        <div class="pull-left pr24">
                            <yxtf-input
                                    placeholder="请输入试题名称模糊搜索"
                                    suffix-icon="yxt-icon-search"
                                    v-model="searchTxt" style="width:300px;">
                            </yxtf-input>
                        </div>
                    </div>
                    <yxtf-button type="primary">新建试题<i class="yxt-icon-arrow-down yxt-icon--right"></i></yxtf-button><yxtf-button plain class="submit">提交审核</yxtf-button>
                </div>
    </div>
</template>
<script>
  export default {
    data() {
      return {
        value: '',
        showDropdowm: false,
        showDropdowm1: false,
        showDropdowm2: false,
        showDropdowm3: false,
        showDropdowm4: false,
        questionType: '',
        questionTypes: [
          {
            value: '选择题',
            label: '选择题'
          },
          {
            value: '判断题',
            label: '判断题'
          }
        ],
        checkDifficulties: ['困难'],
        difficulties: ['困难', '简单'],
        checkTypes: [],
        types: ['类型一', '类型二', '类型三'],
        searchTxt: '',
        filters1: [
          {
            value: '不限',
            label: '不限'
          },
          {
            value: '限制',
            label: '限制'
          }
        ],
        filter1: '',
        filters2: [
          {
            value: '关闭',
            label: '关闭'
          },
          {
            value: '启用',
            label: '启用'
          }
        ],
        filter2: '',
        statuses: [
          {
            value: '已保存',
            label: '已保存'
          },
          {
            value: '已取消',
            label: '已取消'
          }
        ],
        status: '',
        checkPoint: '',
        checkPoints: [
          {
            value: '全部',
            label: '全部'
          },
          {
            value: '字很多的样子',
            label: '字很多的样子'
          },
          {
            value: '选择三',
            label: '选择三'
          }
        ],
        boxVisible: false,
        showPoint: false,
        showStatus: false,
        showFilter1: false,
        showFilter2: false,
        isIndeterminate: false,
        checkAll: false,
        checkAll2: false,
        isIndeterminate2: false,
        showTypes: false
      }
    },
    methods: {
      showBox() {
        this.boxVisible = !this.boxVisible
      },
      closeBox() {
        this.boxVisible = false
      },
      clearAll() {
        this.showPoint = false
        this.showStatus = false
        this.showFilter1 = false
        this.showFilter2 = false
        this.showTypes = false
        this.checkTypes = []
        this.checkPoint = ''
        this.status = ''
        this.filter2 = ''
        this.filter1 = ''
      },
      handleCheckAllChange(val) {
        this.checkDifficulties = val ? this.difficulties : []
        this.isIndeterminate = false
        this.showDropdowm = false
        this.showDropdowm4 = false
      },
      changeSelect(value) {
        let checkedCount = value.length
        this.checkAll = checkedCount === this.difficulties.length
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.difficulties.length
        this.showDropdowm = false
        this.showDropdowm4 = false
      },
      handleCheckAllChange2(val) {
        this.checkTypes = val ? this.types : []
        this.isIndeterminate2 = false
        this.showTypes = true
        this.showDropdowm2 = false
        this.showDropdowm3 = false
      },
      changeSelect2(value) {
        let checkedCount = value.length
        this.checkAll2 = checkedCount === this.types.length
        this.isIndeterminate2 = checkedCount > 0 && checkedCount < this.types.length
        this.showTypes = true
        this.showDropdowm3 = false
        this.showDropdowm2 = false
      },
      hidePanel(el) {
        let myPanel = document.getElementsByClassName("mySelect")
        let myPanel2 = document.getElementsByClassName("mySelect2")
        if (myPanel[0]) {
          this.showDropdowm1 = myPanel[0].contains(el.target)
        }
        if (myPanel[1]) {
          this.showDropdowm = myPanel[1].contains(el.target)
        }
        if (myPanel[2]) {
          this.showDropdowm2 = myPanel[2].contains(el.target)
        }
        if (myPanel2[0]) {
          this.showDropdowm4 = myPanel2[0].contains(el.target)
        }
        if (myPanel2[1]) {
          this.showDropdowm3 = myPanel2[1].contains(el.target)
        }
      }
    }
  }
</script>
```

:::