## 导出

:::demo
```html


<yxt-button @click="success">导出成功</yxt-button>
<yxt-button @click="fail">导出失败</yxt-button>

<script>
export default {
  data() {
    return {
    };
  },
    methods: {
        success() {
          this.$confirm('正在下载导出文件，请在下载中心查看', '查看导出文件', {
            confirmButtonText: '查看',
            cancelButtonText: '取消',
            type: 'success',
          }).then(() => {

          }).catch(() => {

          });
        },
        fail() {
                  this.$confirm('请稍后再试', '导出文件失败', {
                   confirmButtonText: '我知道了',
                   showCancelButton: false,
                    type: 'error',
                  }).then(() => {
                  }).catch(() => {});
          }
      }
}
</script>

```
:::
















