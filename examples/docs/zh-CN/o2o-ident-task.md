## O2oIdentTask 鉴定任务

:::demo

```html
<template>
  <div>
    <yxt-input v-model="params.taskId" />
  <yxt-ulcd-sdk-o2o-ident-task
  inner
  :params="params" />
  </div>
  
</template>
<script>
export default {
    data() {
      return {
        params: {
          taskId: '1630122045395406849', // '1654413888356155393',
          pid: "1650322806022406145"
        }
      }
    },
    methods:{ 

    }
}
</script>
```
:::