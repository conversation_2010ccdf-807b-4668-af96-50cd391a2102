## surveying 调研

<pre>
<font color=#f5222d>
依赖的业务组件：
yxtbiz-video
yxtbiz-image-viewer
yxtbiz-doc-viewer
yxtbiz-upload
</font>

培训合并参加下需要额外依赖以下两个业务组件：
yxtbiz-o2o-multi-evaluate-dialog
yxtbiz-merge-evaluation
</pre>
### 培训参与调研

:::demo

```html
<template> 
  <div class="mb12 flex felx-center">
    <span>id：</span><yxt-input v-model="params.id" />
  </div>
  <div
    style=" 
    flex-direction: column;
    align-items: center; 
    overflow: auto;
    top:0;
    left:0;  
    background: #f5f5f5;"
    :style="{
      'position': fs ? 'fixed':'relative',
      'z-index': fs ? '1999':'0',
      'width': fs ? 'calc(100vw - 64px)' : 'calc(100%)',
      'height': fs ? 'calc(100vh - 32px)' : '600px'
    }"
    class="yxtulcdsdk-ulcdsdk"
  > 
    <yxt-ulcd-sdk-surveying
    style="width:calc(100% - 48px); margin:24px; "
      :params="params"
      :radius="false"
      :fullScreen="false"
      fullPage
      :scrollInner="false"
      @fullScreen="fullScreen"
      @back="back"
    ></yxt-ulcd-sdk-surveying>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        /*
 

    https://pro-phx.yunxuetang.com.cn/survey/#/fbi?type=2&id=1650315578431307777&tname=&tid=00f5760d-f921-424f-adaa-6a94b812baab&success=&mergeEvalProjectId=1650315570608599042&mergeEvalTaskId=1650315578431307777&showResult=1&btid=
       https://pro-phx.yunxuetang.com.cn/survey/#/answer?id=55270cec-1efe-4b1e-b2f1-5de4b84c7523&uamId=1650315858625740802&uaId=1650315860370571265&mergeEvalProjectId=1650315570608599042&mergeEvalTaskId=1650315579022704641&showResult=1
        */
        params: { 
          id: "1650315579022704641",
          type: 2,
          tId: '00f5760d-f921-424f-adaa-6a94b812baab',
          mergeEvalProjectId: '1650315570608599042',
          mergeEvalTaskId: '1650315579022704641', 
          hideBack: true,
          showResult: 1
        },
        fs: false,
      };
    },
    methods: {
      fullScreen(fs) {
        this.fs = fs;
      },
      back() {
        alert('返回');
      }
    },
  };
</script>
```

:::

### 报名、活动内嵌调研问卷 直连作答页面，无返回按钮

:::demo

```html
<template>
  <div class="mb12 flex felx-center">
    <span>id：</span><yxt-input v-model="params.id" />
  </div>
  <div class="bg-black flex" style="justify-content: flex-end">
    <div
      style="
      background-color:white;
      height: 700px;
      width: 500px;"
    > 
      <yxt-ulcd-sdk-surveying 
        :params="params"
        :step="0"
        :fullScreen="false"
        @updateProgress="updateProgress"
      ></yxt-ulcd-sdk-surveying>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        /*
        d6de7de3-3ba0-4207-9cd8-c4aa37f5b633  
        aa173afd-e79f-4301-859d-d706c1bad319  
        3240a4cf-46f5-49f8-8a1d-5cd01b51a493
        */
        params: {
          id: "1654785388183619585",
          type: 2,
          skipPreview: 1,
          hideBack: 1,
          customSuccess: 1,
          allowResult: 1
          // uamId: '1640911165259511809'
        }
      };
    },
    methods: {
      updateProgress(progress){
        progress === 2 && alert("提交了")
      }
    },
  };
</script>
```

:::

### 参与调研

:::demo

```html
<template>
  <pre>
d6de7de3-3ba0-4207-9cd8-c4aa37f5b633  
aa173afd-e79f-4301-859d-d706c1bad319  
3240a4cf-46f5-49f8-8a1d-5cd01b51a493 各题型做答
24ee8593-67bf-4d4d-86fa-e718bf0cfbfe 各题型结果查看
45183e45-8201-48a6-a6e3-5acf9d6bf10b 评价
  </pre>
  <div class="mb12 flex felx-center">
    <span>id：</span><yxt-input v-model="params.id" />
  </div>
    <button @click="confirmLeave" class="ml12">离开</button>
  <div
    style="
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 32px;
    overflow: auto;
    top:0;
    left:0; 
    background-color: black;"
    :style="{
      'position': fs ? 'fixed':'relative',
      'z-index': fs ? '1999':'0',
      'width': fs ? 'calc(100vw - 64px)' : 'calc(100% - 64px)',
      'height': fs ? 'calc(100vh - 32px)' : '600px'
    }"
    class="yxtulcdsdk-ulcdsdk"
  >
    <yxt-ulcd-sdk-surveying
      :params="params"
      ref="survey"
      :step="1"
      @fullScreen="fullScreen"
      @back="back"
    ></yxt-ulcd-sdk-surveying>
  </div>
</template>
<script>
  export default {
    data() {
      return { 
        params: {
          // id: '07fba476-8dd0-4a9f-b224-a3186ebc7257',
          // targetId: '1111eee'
          // batchId: '11',
          id: '24ee8593-67bf-4d4d-86fa-e718bf0cfbfe',
          // id: '27b66612-d3e2-49d0-89a5-bbd993b6566d',
          // uaId:'1654378119384612865',
          // uamId:'1654373430060247041'
        },
        fs: false,
      };
    },
    methods: {
      fullScreen(fs) {
        this.fs = fs;
      },
      back() {
        alert('返回');
      },
      confirmLeave(){
        this.$refs.survey.confirmLeave((leave) => { 
          this.$message(leave?'离开了':'不能离开');
        })
      }
    },
  };
</script>
```

:::

### Attributes

| 参数        | 说明                                                                                                  | 类型    | 可选值 | 默认值 |
| ----------- | ----------------------------------------------------------------------------------------------------- | ------- | ------ | ------ |
| params      | 原调研页面传的参数。 必要参数: id 调研项目 ID                                                         | Object  |        |        |
| step        | 当前调研的步骤, 0：调研中转页（一般第三方调研走这里，默认会跳过预览页面） 1：预览 2：作答 3：作答详情 | Number  |        | 0      |
| deepStudy   | 是否沉浸式。沉浸式为内嵌页面部分区域，走传参。独立调查走非沉浸，走路由。                              | Boolean |        | true   |
| fullScreen  | 是否显示全屏按钮。 沉浸式下默认显示。                                                                 | Boolean |        | true   |
| fullPage    | 直接撑满容器，去除内部自带的边距。   hideBack为true时，默认会变为true                                 | Boolean |        | false  |
| scrollInner | 内部滚动的模式                                                                                        | Boolean |        | true   |
| radius      | 组件是否有圆角                                                                                        | Boolean |        | true   |

### params - 调研中转页（外部对接的调查使用此方式） step：0

| 参数          | 说明                                                                                            | 类型    | 必须 |
| ------------- | ----------------------------------------------------------------------------------------------- | ------- | ---- |
| id            | 调研项目 ID                                                                                     | string  | 是   |
| type          | 调查类型 (1:调研中心独立调查项目, 2:业务项目)。默认为 1                                         | string  | 是   |
| tid           | 调查对象 Id（targetId），用于区分单人多次作答                                                   | string  | 否   |
| tname         | 调查对象名称                                                                                    | string  | 否   |
| customSuccess | 是否自定义提交成功的处理。                                                                      | Boolean | 否   |
| skipPreview   | 是否跳过调研默认的预览页面，step 走 0 时默认 1                                             | Boolean | 否   |
| allowResult   | 答过时是否允许直接查看结果. 默认0                                              | Boolean | 否   |
| hideBack      | 是否显示返回、退出的按钮。 显示时默认返回调研预览页面。如果跳过预览页面，则会对外暴露 back 事件 | Boolean | 否   |
| success       | 提交后的跳转地址。原先在非沉浸式下配合页面跳转使用。                                            | string  | 否   |
| taskType      | 任务类型 50:面授讲师评价学员  100:课程包                                         | Number  | 否   |
| masterId      | 用于项目下不同步进度的课程包。项目ID                                             | string  | 否   |
| packageId     | 用于项目下不同步进度的课程包。 课程包ID                                           | string  | 否   |

### params - 调研预览 step：1

| 参数          | 说明                                                                  | 类型    | 必须  |
| ------------- | --------------------------------------------------------------------- | ------- | ----- |
| id            | 调研项目 ID                                                           | string  | 是    |
| customSuccess | 是否自定义成功的处理。true 时问卷提交完成后需要使用方自己进行后续处理 | Boolean | false |

### Events

| 事件名         | 说明             | 回调参数                                                                  |
| -------------- | ---------------- | ------------------------------------------------------------------------- |
| fullScreen     | 全屏             | 是否全屏、退出全屏                                                        |
| back           | 返回、退出的事件 | 用于跳过调研预览页面时使用方自己处理返回的页面                            |
| updateProgress | 进度状态更新     | 1： 进行中 2：提交完成 <font color=#436bff>customSuccess</font>可在此处理 |
