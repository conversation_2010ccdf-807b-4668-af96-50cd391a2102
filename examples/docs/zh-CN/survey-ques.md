## SurveyQues 调研试题

<pre>
<font color=#f5222d>
依赖的业务组件：
yxtbiz-video
yxtbiz-image-viewer
yxtbiz-doc-viewer
yxtbiz-upload
</font>

</pre>

### 调研试题(无分页、无跳转逻辑)

:::demo

```html
<template> 
  <yxt-input v-model="apiQuesDatasTxt"></yxt-input>

  <div class="yxtulcdsdk-survey yxtulcdsdk-survey-container yxtulcdsdk-survey-container--ques">
      <yxt-ulcd-sdk-survey-ques v-for="(ques, j) in apiQuesDatas" :key="ques.id"
        :ques.sync="apiQuesDatas[j]"
        :disabled="false"
        emitInitAnswer
        :autoCheck="checkSubmit"
        :mode="2"
        :index="j+1"
        @user-submit="doQuestion"/> 
  </div>

  <yxt-button @click="submit">提交</yxt-button>
</template>
<script>

const SurveyQuesType = {
  singleChoose: 1, // 单选
  multiChoose: 2, // 多选
  judge: 3, // 判断
  classify: 4, // 分类
  fill: 5, // 填空
  essay: 6, // 简答
  multiFill: 7, // 多项填空
  upload: 8, // 文件上传
  scale: 9, // 量表
  multiScale: 10, // 多级量表
  sort: 11, // 排序
  nps: 12, // nps
  score: 13, // 打分
  evaluation: 14, // 评价
  textChoose: 15, // 文字单选
  multiTextChoose: 16, // 文字多选
  picChoose: 17, // 图片单选
  multiPicChoose: 18, // 图片多选
  videoChoose: 19, // 视频单选
  multiVideoChoose: 20, // 视频多选
  slideScore: 21, // 滑动打分
  name: 101, // 姓名
  sex: 102, // 性别
  phone: 103, // 手机
  email: 104, // 邮箱
  date: 105, // 日期
  city: 106, // 城市
  job: 107, // 职业
  school: 108, // 学校
  age: 109, // 年龄
  company: 110, // 公司
  department: 111, // 部门
  post: 112, // 岗位
  number: 113, // 工号
  page: 201, // 分页
  pageIntro: 202, // 分页说明
  divide: 203 // 分割线
}

export default {
  data () {
    return {
      isSinglePage: false,
      checkSubmit: false, // 做过提交动作
      quesPages: [], // 分页后的题目及序号
      apiQuesDatasTxt: '[{"projectId":null,"templateId":null,"questionId":"6dc6dee1-a25d-4989-a334-2c78f109c890","sortNo":null,"quesType":3,"mustAnswer":1,"content":"题目2","contentAttachList":null,"selectQuestion4Create":null,"multiSelectQuestion4Create":null,"judgeQuestion4Create":{"enabledScore":null,"scoreType":null,"rightScore":null,"wrongScore":null,"itemList":[{"itemId":"8c23f2d6-49fb-4032-ba59-1da3e183bae5","content":"正确","contentAttachList":null,"correctAnswer":null,"points":null,"itemNote":0,"availableNum":null,"remainNum":null,"sortNo":null,"userAnswerFlag":null,"answerExplain":null,"cnt":0,"percentage":0},{"itemId":"fc439b6a-6605-4767-830f-99859b7eb082","content":"错误","contentAttachList":null,"correctAnswer":null,"points":null,"itemNote":1,"availableNum":null,"remainNum":null,"sortNo":null,"userAnswerFlag":null,"answerExplain":null,"cnt":0,"percentage":0}],"enabledExplain":0,"explainText":"","explainAttachList":[],"scoreDimensional":null,"enabledJump":0,"jumpConfig":[],"layoutType":1},"classifyQuestion4Create":null,"orderQuestion4Create":null,"fillQuestion4Create":null,"multiFillQuestion4Create":null,"shortAnswerQuestion4Create":null,"uploadQuestion4Create":null,"scaleQuestion4Create":null,"multiScaleQuestion4Create":null,"npsScaleQuestion4Create":null,"scoreQuestion4Create":null,"feedBackQuestion4Create":null,"voteQuestion4Create":null,"baseInfoQuestion4Create":null,"slideScoreQuestion4Create":null,"score":null,"answerMode":null}]',
      // 接口给的原始数据
      apiQuesDatas: []
    }
  },
  created() {
      this.apiQuesDatas = JSON.parse(this.apiQuesDatasTxt)
  },
  watch: {
    apiQuesDatasTxt(){
      this.apiQuesDatas = JSON.parse(this.apiQuesDatasTxt)
    }
  },
  methods: {
    /**
         * 用户单题作答
         * @param {Boolean} ques 试题
         * @param {Boolean} answer 作答答案
         */
    doQuestion (ques, answer) {
      console.log("单题提交信息：", answer)
    },
 
    submit() {
      alert(this.checkCurrentPageHasAbnormal()? '还需要作答':'可提交')
    },
    /**
       * 检查当前页是否有异常作答题目
       * @returns 是否有异常 true false无异常
       */
    checkCurrentPageHasAbnormal() { 
      this.checkSubmit = true 
      // 如果没有分页、跳过这些逻辑，可以简单检查是否作答
     return this.apiQuesDatas.filter((q) => !(q.mustAnswer && q.answered) || q.errorMessage).length > 0;
    }
  },
} 
</script>
```

:::

### 调研试题(包含分页、跳转逻辑处理)

:::demo

```html
<template> 
  <yxt-input v-model="apiQuesDatasTxt"></yxt-input>

  <div class="yxtulcdsdk-survey yxtulcdsdk-survey-container yxtulcdsdk-survey-container--ques">
    <div v-for="(quesList, i) in quesPages" :key="i" >
      <div class="text-center mv12"> -------- 第{{i+1}}页 -------- </div>
      <yxt-ulcd-sdk-survey-ques v-for="(ques, j) in quesList" :key="ques.id"
        :ques.sync="quesList[j]"
        :disabled="false"
        emitInitAnswer
        :autoCheck="checkSubmit"
        :mode="2"
        :index="ques.quesNum"
        @user-submit="doQuestion"/>
    </div>
  </div>

  <yxt-button @click="submit">提交</yxt-button>
</template>
<script>

const SurveyQuesType = {
  singleChoose: 1, // 单选
  multiChoose: 2, // 多选
  judge: 3, // 判断
  classify: 4, // 分类
  fill: 5, // 填空
  essay: 6, // 简答
  multiFill: 7, // 多项填空
  upload: 8, // 文件上传
  scale: 9, // 量表
  multiScale: 10, // 多级量表
  sort: 11, // 排序
  nps: 12, // nps
  score: 13, // 打分
  evaluation: 14, // 评价
  textChoose: 15, // 文字单选
  multiTextChoose: 16, // 文字多选
  picChoose: 17, // 图片单选
  multiPicChoose: 18, // 图片多选
  videoChoose: 19, // 视频单选
  multiVideoChoose: 20, // 视频多选
  slideScore: 21, // 滑动打分
  name: 101, // 姓名
  sex: 102, // 性别
  phone: 103, // 手机
  email: 104, // 邮箱
  date: 105, // 日期
  city: 106, // 城市
  job: 107, // 职业
  school: 108, // 学校
  age: 109, // 年龄
  company: 110, // 公司
  department: 111, // 部门
  post: 112, // 岗位
  number: 113, // 工号
  page: 201, // 分页
  pageIntro: 202, // 分页说明
  divide: 203 // 分割线
}

export default {
  data () {
    return {
      isSinglePage: false,
      checkSubmit: false, // 做过提交动作
      quesPages: [], // 分页后的题目及序号
      apiQuesDatasTxt: window.localStorage.surveyQues || '[]',
      // 接口给的原始数据
      apiQuesDatas: []
    }
  },
  created() {
    const lst = JSON.parse(this.apiQuesDatasTxt)
    this.initQuesPages(lst)
  },
  watch: {
    apiQuesDatasTxt() {
      const lst = JSON.parse(this.apiQuesDatasTxt)
      window.localStorage.surveyQues = this.apiQuesDatasTxt
      this.initQuesPages(lst)
    }
  },
  methods: {
    /**
         * 用户单题作答
         * @param {Boolean} ques 试题
         * @param {Boolean} answer 作答答案
         */
    doQuestion (ques, answer) {
      console.log([ques.content, answer])
    },

    /**
        * 初始化试题分页
        * 分页本身也属于一道题目，题号得跳过分页
        * 
        * 如果没有分页、跳过等逻辑直接遍历原试题列表即可
        */
    initQuesPages (lst) {
      if(lst){
        this.apiQuesDatas = lst
      }
      const pages = []
      let pi = 0
      let qi = 0
      this.apiQuesDatas.forEach(question => { 
        if (!pages[pi]) {
          pages[pi] = []
        }

        // 普通试题，插入当前页
        if (question.quesType < SurveyQuesType.page) {
          question.quesNum = ++qi // 题号，跳过的题目题号也占用
          pages[pi].push(question)
          this.quesNum++
        }

        // 分页说明，分割线。无须作答制作呈现使用
        if (question.quesType > SurveyQuesType.page && !this.isSinglePage) {
          pages[pi].push(question)
        }

        // 分页或者H5的每页一题, 答题结果查看只需要一页
        if (!this.onePage && (question.quesType === SurveyQuesType.page || this.isSinglePage)) {
          pi++
        }
      })
      // 一题一页的情况下，去除空数据页(包括分页、分页说明等)
      for (let i = 0; i < pages.length; i++) {
        if (!pages[i].length) {
          pages.splice(i, 1)
          i--
        }
      }
      this.quesPages = pages
    },
    submit() {
      alert(this.checkCurrentPageHasAbnormal()? '必填未填':'可提交')
    },
    /**
       * 检查当前页是否有异常作答题目
       * @returns 是否有异常 true false无异常
       */
    checkCurrentPageHasAbnormal() { 
      this.checkSubmit = true
      let has = false;
      this.quesPages.forEach((page, index) => {
        if (has || index > this.pageIndex) {
          return;
        }
        // 检查试题是否必答且已经作答 或者非必达 或者跳过了。完备的校验
        const hasNow = page.filter((q) => !this.checkQuesIsMustAndAnswered(q) || (!this.checkQuesJumped(q) && q.errorMessage)).length > 0;

        // 如果没有分页、跳过这些逻辑，可以简单检查是否作答
        // const hasNow = page.filter((q) => !this.checkQuesIsAnswered(q) || q.errorMessage).length > 0;

        if (hasNow && !has) {
          this.pageIndex = index;
          has = hasNow;
        }
      });
      return has;
    },
    /**
       * 如果没有分页、跳过这些逻辑，可以简单检查是否作答
       * @param {object} q 试题
       * @returns 是否已作答
       */
    checkQuesIsAnswered(q) {
      return q.mustAnswer && q.answered;
    }, 
    /**
       * 检查试题是否必答且已经作答 或者非必达 或者跳过了
       * @param {object} q 试题
       * @returns 是否已作答
       */
    checkQuesIsMustAndAnswered(q) {
      return (q.mustAnswer && this.checkQuesEnable(q) &&  q.answered) || !q.mustAnswer || !this.checkQuesEnable(q);
    }, 
    /**
       * 试题是否可用（跳过、或者分页类不可用
       * @param {object} q 试题
       * @returns 是否可用
       */
    checkQuesEnable(q) {
      return this.checkQuesIsNeedWrite(q) && !this.checkQuesJumped(q);
    },
    /**
       * 试题是否跳过
       * @param {object} q 试题
       * @returns 是否可用
       */
    checkQuesJumped(q) {
      return q.jumpedIds && q.jumpedIds.length > 0;
    },
    /**
       * 试题是否是需要做的类别
       * @param {object} q 试题
       * @returns 是需要做的类别
       */
    checkQuesIsNeedWrite(q) {
      return q.quesType < SurveyQuesType.page;
    },
  },
} 
</script>
```

:::

### Attributes

| 参数      | 说明                                       | 类型    | 可选值 | 默认值 |
| --------- | ------------------------------------------ | ------- | ------ | ------ |
| ques      | 试题信息                                   | Object  |        |        |
| disabled  | 是否禁用状态（作答后查看，通常为禁用状态） | Number  |        | 0      |
| autoCheck | 是否显示错误信息 (例如必答的文字提示)      | Boolean |        | true   |
| mode      | 试题模式，2为答题模式                      | Number  |        | 0      |
| index     | 题号                                       | Boolean |        | false  |
| emitInitAnswer     | 初始化试题时若已做答是否触发一次做答动作                                       | Boolean |        | false  |
| hideQuesSort     | 是否隐藏题目序号                                       | Boolean |        | false  |


### Events

| 事件名      | 说明                       | 回调参数           |
| ----------- | -------------------------- | ------------------ |
| user-submit | 单题作答                   | 试题信息、作答信息 |
| jump        | 跳转动作，用于处理跳转逻辑 |                    |
