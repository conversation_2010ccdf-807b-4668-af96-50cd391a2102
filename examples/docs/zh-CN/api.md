## Api api

#### 全局引入Yxtbiz

```javascript
import Vue from 'vue';
import YxtUlcdSdk, { commonApi,ulcdApi } from 'yxt-ulcd-sdk';


Vue.use(YxtUlcdSdk, {
  env: 'dev',
  source: 501
});


```

#### 按需引入Api

```javascript
import Vue from 'vue';
import { commonApi,ulcdApi } from 'yxt-ulcd-sdk';

Api.setConfig({
  env: 'dev',
  source: 501
})

```

需指定当前环境变量env(可选参数dev、prod)及对应微服务的soure;

commonApi,ulcdApi 对应各微服务api实例

调用Api.create(options)可以创建自定义实例，options参数同axios
