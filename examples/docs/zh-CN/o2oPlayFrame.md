## O2oPlayFrame o2oPlayFrame

:::demo

```html
<template>
  <div style="background-color: darkgray;"><yxt-ulcd-sdk-o2o-play-frame></yxt-ulcd-sdk-o2o-play-frame></div>
  
</template>
<script>
export default {
    data() {
      return{ 
        params: { 
          arrangeId: '893392ae-a344-45cc-8789-44a868f7794a',
          // trackId:'MTAwMi4yM2NiN2FlNy02YmZkLTRhZjAtOTljMS02MTExOGM0NTE5Yjc= '
          // MTAwMi4yM2NiN2FlNy02YmZkLTRhZjAtOTljMS02MTExOGM0NTE5Yjc%3D%20&btid=&arrangeId=
        }
      }
    },
    methods:{ 

    }
}
</script>
<style lang="scss">
.demo-o2oPlayFrame{
  background-color: darkgray;
}

</style>
```
:::

### Attributes

| 参数      | 说明                                                                       | 类型    | 可选值 | 默认值 |
| --------- | -------------------------------------------------------------------------- | ------- | ------ | ------ |
| params    | 原考试页面传的参数。 必要参数: arrangeId 考试安排ID                        | Object  |        |        |
| step      | 当前考试的步骤,    0：考前预览   1：考试答题    2：考试结果    3：答卷详情 | Number  |        | 0      |
| deepStudy | 是否沉浸式                                                                 | Boolean |        | true   |

### params - 考前预览
| 参数       | 说明                            | 类型   | 必须 |
| ---------- | ------------------------------- | ------ | ---- |
| arrangeId  | 考试安排ID                      | string | 是   |
| masterId   | 第三方考试来源ID                | string |      |
| masterType | 第三方考试来源类型              | string |      |
| packageId  | 课程包ID                        | string |      |
| btid       | 在线课堂批次ID                  | string |      |
| gwnlUrl    | 岗位能力面包屑地址              | string |      |
| taskId     | o2o面包屑、上一个下一个用的参数 | string |      |
| targetId   | o2o面包屑、上一个下一个用的参数 | string |      |
| targetCode | o2o面包屑、上一个下一个用的参数 | string |      |
| trackId    | ssp学习链路ID                   | string |      |
| batchId    | 循环考试批次ID                  | string |      |
| uemId      | 用户考试ID                      | string |      |
   
 ### Methods

| 事件名       | 说明                                   | 入参                       |
| ------------ | -------------------------------------- | -------------------------- |
| confirmLeave | 如果要离开当前组件，调用此方法进行确认 | 1.是否能离开的回调确认方法 |

### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | ------------------ |
| fullScreen | 全屏 | 是否全屏、退出全屏 |
