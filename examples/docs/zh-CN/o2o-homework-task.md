## O2oHomeworkTask 培训作业任务


:::demo

```html
<template>
  <div>
    <yxt-input v-model="params.taskId" />
  <yxt-ulcd-sdk-o2o-homework-task 
  :params="params" />
  </div>
  
</template>
<script>
export default {
    data() {
      return {
        params: {
          taskId:"1818255317541160962"
        }
      }
    },
    methods:{ 

    }
}
</script>
```
:::

### Attributes

| 参数      | 说明                  | 类型    | 是否必填 | 默认值 |
| -------- | -------------------- | ------- | ------- | ------ |
|    params    |          作业参数       | Object  |   是     |     - |
### params - 作业参数

| 参数      | 说明                  | 类型    | 是否必填 | 默认值 |
| -------- | -------------------- | ------- | ------- | ------ |
|    taskId    |          作业id       | String  |   是     |     - |
|    groupId    |            | String  |      是 |     - |
|    isPdf    |   是否pdf下载样式  | Boolean  |   否     |     false |
|    preview    |     | Boolean  |   否     |     false |
|    bizzType    |     | Number  |   否     |     - |
|    factor    |   去版本  | Object  |   是     |   { transferText: false }   |
|    xuankeInfo    |     | Object  |   是     |   { taskId: '' }   |
|    checkCooperHwk    |   协同作业  | Boolean  |   否     |   false   |


### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | -------------- |
| hwInfo | 作业信息 | data |