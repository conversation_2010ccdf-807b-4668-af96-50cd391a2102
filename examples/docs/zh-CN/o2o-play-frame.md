## O2oPlayFrame o2o-play-frame


:::demo

```html
<template>
  <yxt-ulcd-sdk-o2o-play-frame></yxt-ulcd-sdk-o2o-play-frame>
  
</template>
<script>
export default {
    data() {
      return{ 
        params: {
          arrangeId: '893392ae-a344-45cc-8789-44a868f7794a',
          // trackId:'MTAwMi4yM2NiN2FlNy02YmZkLTRhZjAtOTljMS02MTExOGM0NTE5Yjc= '
          // MTAwMi4yM2NiN2FlNy02YmZkLTRhZjAtOTljMS02MTExOGM0NTE5Yjc%3D%20&btid=&arrangeId=
        }
      }
    },
    methods:{ 

    }
}
</script>
<style lang="scss">
.demo-o2o-play-frame {
  background-color: darkgray;
}

</style>
```
:::

### Attributes

| 参数      | 说明                                                                       | 类型    | 可选值 | 默认值 |
| --------- | -------------------------------------------------------------------------- | ------- | ------ | ------ |
| params    | 原页面传的参数。                      | Object  |        |        |
| inner | 是否沉浸式                                                                 | Boolean |        | true   |

### params - 原页面参数
| 参数       | 说明                            | 类型   | 必须 |
| ---------- | ------------------------------- | ------ | ---- |
   
 ### Methods

| 事件名       | 说明                                   | 入参                       |
| ------------ | -------------------------------------- | -------------------------- |
| updateProgress | 进度更新 | 学习状态，1学习中，2已完成 |

### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | ------------------ |
| fullScreen | 全屏 | 是否全屏、退出全屏 |
