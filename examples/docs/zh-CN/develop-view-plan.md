## DevelopViewPlan 制定查看计划


:::demo
```html
<template>
  <div class="yxtulcdsdk-flex-center mb12">
    课程ID：<yxtf-input class="width480" v-model="newId"></yxtf-input>
    <yxtf-button class="ml12" type="primary" @click="confirm('develop')">制定计划</yxtf-button>
    <yxtf-button class="ml12" type="primary" @click="confirm('view')">查看计划</yxtf-button>
  </div>
  <yxt-ulcd-sdk-develop-view-plan ref="plan" :kng-id="newId"></yxt-ulcd-sdk-develop-view-plan>
</template>
<script>
  export default {
    data() {
      return {
        visible: false,
        newId: 'd1d44e98-883c-4cb0-a44d-be25b81d718e'
      }
    },
    methods: {
      confirm (type) {
        this.$refs.plan.init(type, this.newId)
      }
    }
  }
</script>
```
:::

### Attributes

| 参数      | 说明                  | 类型    | 是否必填 | 默认值 |
| -------- | -------------------- | ------- | ------- | ------ |
|    visible    |          隐藏、显示       |  Boolean  |   否     |    false |



### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | --------------- |
