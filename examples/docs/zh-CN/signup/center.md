## 报名中心

:::demo

```html
<template>
  <div>
    <yxtf-button type="primary" style="margin-bottom: 24px;" @click.stop="dialogVisible = true">配置组件参数</yxtf-button>
    <yxtf-button type="primary" style="margin-bottom: 24px;">注入组件钩子</yxtf-button>
    <yxtf-button type="primary" style="margin-bottom: 24px;">注入组件插槽</yxtf-button>
    <yxtbiz-signup :mod="cfgs.mod" v-bind="cfgs.props[cfgs.mod]" v-on="cfgs.ons[cfgs.mod]">
      <template slot="content">简介</template>
       <template slot="condition">
        <div data-v-06327f81="" class=" mb32 ml32 mr32"><div data-v-06327f81="" class="c-cC26 fs16 mb8 fw500">添加个条件</div><div data-v-06327f81="" class="c-cC75 fs14">哈哈哈哈</div></div>
       </template>
    </yxtbiz-signup>
    <yxtf-dialog
      title="配置组件"
      :visible.sync="dialogVisible"
      :append-to-body="true">
      <yxtf-input v-model="data" type="textarea" placeholder="配置内容" rows="24" style="width: 100%;"></yxtf-input>
      <span slot="footer" class="dialog-footer">
        <yxtf-button @click="dialogVisible = false">取 消</yxtf-button>
        <yxtf-button type="primary" @click="ok">确 定</yxtf-button>
      </span>
    </yxtf-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      cfgValue: localStorage.centerProps,
      cfgs: {
        mod: 'center',
        // mod:'order',
        props: {
          center: {
            align: 'center'
          },
          details: {
            extraConditionArr:{},
            //  id:"1544167572123959298",
            align: 'center',
            showConditionTab:true,
            signBtnActive:false,
            extendedInfo: [{
              label: '认证类型',
              value: '一级讲师'
            }]
          },
          order: {
            extraConditions:{},
            align: 'center',
            //  id:"1544167572123959298",
            surveyUrl: 'https://xx-phx.yunxuetang.com.cn',
            extendedInfo: [{
              label: '招募时间',
              value: '2022-03-04 00:00 ～ 2022-04-14 23:59'
            }]
          },
          my: {
            align: 'center',
            infos: {
              giveupTip: '放弃后您将不能参加此次培训课程'
            },
            extendedInfo: [{
              label: '招募时间',
              value: '2022-03-04 00:00 ～ 2022-04-14 23:59'
            }]
          }
        },
        ons: {
          center: {
            toDetails: id => {
              this.cfgs.mod = 'details';
              this.cfgs.props[this.cfgs.mod] = {
                ...this.cfgs.props[this.cfgs.mod],
                id
              };
            }
          },
          details: {
            // extraCondition:(userList,id)=>{
            //   this.cfgs.props[this.cfgs.mod].extraConditionArr = [{
            //     ruleName:'额外的条件名称',
            //     ruleValueInfo:"额外的条件详情"
            //   }]
            // },
            toHome: () => {
              this.cfgs.mod = 'center';
            },
            toOrder: (id) => {
              this.cfgs.mod = 'order';
              this.cfgs.props[this.cfgs.mod] = {
                ...this.cfgs.props[this.cfgs.mod],
                id
              };
            },
            toMy: id => {
              this.cfgs.mod = 'my';
              this.$set(this.cfgs.props, this.cfgs.mod, {
                ...this.cfgs.props[this.cfgs.mod],
                id
              });
            }
          },
          order: {
            extraCondition:(userList,id)=>{
              this.cfgs.props[this.cfgs.mod].extraConditions = {
                  "00f5760d-f921-424f-adaa-6a94b812baab":[{
                    ruleName:'额外的条件名称',
                    ruleValueInfo:"额外的条件详情"
                  }]
                }
            },
            toDetails: id => {
              this.cfgs.mod = 'details';
              this.cfgs.props[this.cfgs.mod] = {
                ...this.cfgs.props[this.cfgs.mod],
                id
              };
            },
            toHome: () => {
              this.cfgs.mod = 'center';
            },
            toMy: id => {
              this.cfgs.mod = 'my';
              this.cfgs.props[this.cfgs.mod] = {
                ...this.cfgs.props[this.cfgs.mod],
                id
              };
            }
          },
          my: {
            toHome: () => {
              this.cfgs.mod = 'center';
            }
          }
        }
      }
    };
  },
  computed: {
    data: {
      get() {
        return this.cfgValue || JSON.stringify(this.cfgs.props, null, 4);
      },
      set(value) {
        this.cfgValue = value;
      }
    }
  },
  created() {
    window.feConfig = {
      common: {
        uploadConfig: 'https://media-phx.yunxuetang.com.cn/dynamic/common/upload-config-keys.js'
      }
    };
    if (this.cfgValue) {
      this.ok();
    }
  },
  methods: {
    ok() {
      try {
        this.cfgs.props = JSON.parse(this.cfgValue);
        localStorage.centerProps = this.cfgValue;
        this.dialogVisible = false;
      } catch(e) {
        alert('配置作用失败！');
      }
    }
  }
};
</script>
```
:::