## 配置中心
:::demo
```html
<template>
  <yxtbiz-signup :mod="mod" v-bind="props[mod]" v-on="ons[mod]"></yxtbiz-signup>
</template>

<script>
export default {
  data() {
    return {
      mod: 'list',
      // mod:'config',
      props: {
        list: {},
        manage: {
          isOperated:false,
          enableGroupCorp:true,
          targetOrgId:true,
          disabledOrgSelect:true,
          isOrgSelectAlone:true
        },
        config: {
          // id:"1544167572123959298"
        },
        detail: {
           isOperated:false,
           enableGroupCorp:true,
           visibleOrgSelector:true
        }
      },
      ons: {
        list: {
          toManage: id => {
            this.mod = 'manage';
            this.props[this.mod] = {
              ...this.props[this.mod],
              id
            };
          }
        },
        manage: {
          toDetail: (id, projectId) => {
            this.mod = 'detail';
            this.props[this.mod] = {
              ...this.props[this.mod],
              id,
              projectId
            };
          },
          toConfig: id=> {
            this.mod = 'config';
            this.props[this.mod] = {
              ...this.props[this.mod],
              id
            };
          }
        },
        config: {
          cancel:()=>{
            this.mod = 'manage';
            this.props[this.mod] = {
              ...this.props[this.mod],
              id
            };
          },
          save:(data)=>{
            console.log('save',data)
            this.mod = 'manage';
            this.props[this.mod] = {
              ...this.props[this.mod],
              id
            };
          }
        },
        detail: {
          toManage: id => {
            this.mod = 'manage';
            this.props[this.mod] = {
              ...this.props[this.mod],
              id
            };
          }
        }
      }
    };
  },
  created() {
    window.feConfig = {
      common: {
        uploadConfig: 'https://media-phx.yunxuetang.com.cn/dynamic/common/upload-config-keys.js',
        apiAudit: 'https://api-audit-phx.yunxuetang.com.cn/v2/'
      }
    }
  }
};
</script>
```
:::
