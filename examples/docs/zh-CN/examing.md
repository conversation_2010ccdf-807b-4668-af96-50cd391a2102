## Examing 考试

<pre>
必要业务组件：
yxtbiz-upload
yxtbiz-video
yxtbiz-image-viewer
yxtbiz-doc-viewer
yxtbiz-attachment-check
yxtbiz-watermark
yxtbiz-user-name
yxtbiz-qrcode

培训使用业务组件：
yxtbiz-skip-task


依赖的外部组件：
1. 【viewerjs】 

    "viewerjs": "^1.5.0", import 'viewerjs/dist/viewer.css

2. 【browser】
   
   injectCommonFiles: ['browserjs'] 

   或者

   <% 
   var feConfig = htmlWebpackPlugin.options.feConfig;
   var commonFiles = feConfig.commonFiles;
   %>
    
   <% if (commonFiles.browserjs) { %>
   < script type="text/javascript" src="<%= commonFiles.browserjs %>">< /script >
   <% } %> 
</pre>

:::demo

```html
<template>
  <yxt-ulcd-sdk-course-page id='4b2d15f9-2b4f-4185-bd7a-57385e6b36b0' :isCourse="true" :target="{}"></yxt-ulcd-sdk-course-page>
  <div class="mb12 flex flex-center">
    <div>
      <span>arrangeId：</span>
      <yxt-input v-model="params.arrangeId" style="width: 280px;" />
      <span class="ml12">
        893392ae-a344-45cc-8789-44a868f7794a 
        58b5bcac-688e-4749-8014-cc1b8918afcb
        1ebc7aeb-f75a-4d3a-bc30-6f6221ac71eb
        切屏：588495b7-ffcf-48bd-8635-4110f1b75ebd
      </span>
    </div>
    <button @click="confirmLeave" class="ml12">离开</button>
  </div> 
  <yxt-ulcd-sdk-examing :params="params" ref="exam" :fullScreen="false" :scrollInner="false"></yxt-ulcd-sdk-examing>
      <!-- http://localhost:8086/#/zh-CN/component/examing?batchId=&arrId=893392ae-a344-45cc-8789-44a868f7794a&signId=&uemId=c1cc5a18-cc6e-4809-9ec6-d615311c20d0 -->
     
    <div style=" 
        width: calc(100% - 64px); 
        min-width:800px;
        background-color: black;
        height: 600px;
        align-items: center; 
        overflow: auto;
        flex-direction: column;
        align-items: center;
        width: calc(100% - 64px);
        min-width: 800px;
        height: 600px;
        padding: 16px 32px;
        overflow: auto;
        background-color: black;"
      class="yxtulcdsdk-ulcdsdk">
      <yxt-ulcd-sdk-examing :params="params" ref="exam" :fullScreen="true"></yxt-ulcd-sdk-examing>
      <!-- http://localhost:8086/#/zh-CN/component/examing?batchId=&arrId=893392ae-a344-45cc-8789-44a868f7794a&signId=&uemId=c1cc5a18-cc6e-4809-9ec6-d615311c20d0 -->
      <!-- <yxt-ulcd-sdk-examing :key="params.arrangeId" :params="params" :deepStudy="false" :step="1"></yxt-ulcd-sdk-examing> -->
    </div>
</template>
<script>
export default {
    data() {
      return{ 
        params: { 
          arrangeId: 'a519eb9f-363c-43c9-8433-d9f3d0af79d7',
          // trackId:'MTAwMi4yM2NiN2FlNy02YmZkLTRhZjAtOTljMS02MTExOGM0NTE5Yjc= '
          // MTAwMi4yM2NiN2FlNy02YmZkLTRhZjAtOTljMS02MTExOGM0NTE5Yjc%3D%20&btid=&arrangeId=
        }
      }
    },
    methods:{ 
      confirmLeave(){
        this.$refs.exam.confirmLeave((leave) => { 
          this.$message(leave?'离开了':'不能离开');
        })
      }
    }
}
</script>
```
:::

### Attributes

| 参数        | 说明                                                                       | 类型    | 可选值 | 默认值 |
| ----------- | -------------------------------------------------------------------------- | ------- | ------ | ------ |
| params      | 原考试页面传的参数。 必要参数: arrangeId 考试安排ID                        | Object  |        |        |
| step        | 当前考试的步骤,    0：考前预览   1：考试答题    2：考试结果    3：答卷详情 | Number  |        | 0      |
| radius      | 组件边框是否需要圆角                                                       | Boolean |        | true   |
| deepStudy   | 是否沉浸式                                                                 | Boolean |        | true   |
| scrollInner | 内部滚动的模式                                                             | Boolean |        | true   |
| fullScreen  | 是否显示全屏按钮                                                           | Boolean |        | true   |

### params - 考前预览
| 参数       | 说明                            | 类型   | 必须 |
| ---------- | ------------------------------- | ------ | ---- |
| arrangeId  | 考试安排ID                      | string | 是   |
| masterId   | 第三方考试来源ID                | string |      |
| masterType | 第三方考试来源类型              | string |      |
| packageId  | 课程包ID                        | string |      |
| btid       | 在线课堂批次ID                  | string |      |
| gwnlUrl    | 岗位能力面包屑地址              | string |      |
| taskId     | o2o面包屑、上一个下一个用的参数 | string |      |
| targetId   | o2o面包屑、上一个下一个用的参数 | string |      |
| targetCode | o2o面包屑、上一个下一个用的参数 | string |      |
| trackId    | ssp学习链路ID                   | string |      |
| batchId    | 循环考试批次ID                  | string |      |
| uemId      | 用户考试ID                      | string |      |
   
 ### Methods

| 事件名       | 说明                                   | 入参                       |
| ------------ | -------------------------------------- | -------------------------- |
| confirmLeave | 如果要离开当前组件，调用此方法进行确认 | 1.是否能离开的回调确认方法 |

### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | ------------------ |
| fullScreen | 全屏 | 是否全屏、退出全屏 |
