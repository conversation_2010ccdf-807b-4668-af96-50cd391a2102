## AddStudentsToTeam 团队添加学员


:::demo
```html
<template>
  <div class="yxtulcdsdk-flex-center mb12">
    <div>
    课程ID：<yxtf-input class="width480" v-model="kngId"></yxtf-input>
    </div>
    <div>
    团队ID：<yxtf-input class="width480" v-model="teamId"></yxtf-input>
    </div>    
    <yxtf-button class="ml12" type="primary" @click="visible = true">显示弹窗</yxtf-button>
  </div>
  <yxt-dialog
    title="添加学员"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="720px"
    :show-back="true"
    @back="backHandler"
  >
    <yxt-ulcd-sdk-add-students-to-team :kng-id="kngId" :team-id="teamId" :showTip="true" ref="addTeam"></yxt-ulcd-sdk-add-students-to-team>
    <span slot="footer" class="dialog-footer">
      <yxtf-button @click="visible = false">取消</yxtf-button>
      <yxtf-button type="primary" :loading="loading" @click="confirm">确定</yxtf-button>
    </span>
  </yxt-dialog>
</template>
<script>
  export default {
    data() {
      return {
        visible: false,
        kngId:"d1d44e98-883c-4cb0-a44d-be25b81d718e",
        teamId:'',
        loading: false
      }
    },
    methods: {
      confirm () {
        this.loading = true
        this.$refs.addTeam.addStudents().then(()=>{
          this.visible = false
        }).finally(() => (this.loading = false));
      },
      backHandler(){
        this.visible = false
      }
    }
  }
</script>
```
:::

### Attributes

| 参数      | 说明                  | 类型    | 是否必填 | 默认值 |
| -------- | -------------------- | ------- | ------- | ------ |
|    visible    |          隐藏、显示       |  Boolean  |   否     |    false |



### Events

| 事件名     | 说明 | 回调参数           |
| ---------- | ---- | --------------- |
