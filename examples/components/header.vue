<style lang="scss" scoped>
  .headerWrapper {
    height: 80px;
  }

  .header {
    height: 80px;
    background-color: #fff;
    color: #fff;
    top: 0;
    left: 0;
    width: 100%;
    line-height: 80px;
    z-index: 100;
    position: relative;

    .container {
      height: 100%;
      box-sizing: border-box;
      border-bottom: 1px solid #DCDFE6;
    }

    .nav-lang-spe {
      color: #888;
    }

    h1 {
      margin: 0;
      float: left;
      font-size: 32px;
      font-weight: normal;

      a {
        color: #333;
        text-decoration: none;
        display: block;
      }

      span {
        font-size: 12px;
        display: inline-block;
        width: 34px;
        height: 18px;
        border: 1px solid rgba(255, 255, 255, .5);
        text-align: center;
        line-height: 18px;
        vertical-align: middle;
        margin-left: 10px;
        border-radius: 3px;
      }
    }

    .nav {
      float: right;
      height: 100%;
      line-height: 80px;
      background: transparent;
      padding: 0;
      margin: 0;
      &::before, &::after {
        display: table;
        content: "";
      }
      &::after {
        clear: both;
      }
    }

    .nav-gap {
      position: relative;
      width: 1px;
      height: 80px;
      padding: 0 20px;

      &::before {
        content: '';
        position: absolute;
        top: calc(50% - 8px);
        width: 1px;
        height: 16px;
        background: #ebebeb;
      }
    }

    .nav-logo,
    .nav-logo-small {
      vertical-align: sub;
    }

    .nav-logo-small {
      display: none;
    }

    .nav-item {
      margin: 0;
      float: left;
      list-style: none;
      position: relative;
      cursor: pointer;

      &.nav-algolia-search {
        cursor: default;
      }

      &.lang-item,
      &:last-child {
        cursor: default;
        margin-left: 34px;

        span {
          opacity: .8;
        }

        .nav-lang {
          cursor: pointer;
          display: inline-block;
          height: 100%;
          color: #888;

          &:hover {
            color: #409EFF;
          }
          &.active {
             font-weight: bold;
             color: #409EFF;
           }
        }
      }

      a {
        text-decoration: none;
        color: #1989FA;
        opacity: 0.5;
        display: block;
        padding: 0 22px;

        &.active,
        &:hover {
          opacity: 1;
        }

        &.active::after {
          content: '';
          display: inline-block;
          position: absolute;
          bottom: 0;
          left: calc(50% - 15px);
          width: 30px;
          height: 2px;
          background: #409EFF;
        }
      }
    }
  }

  .nav-dropdown {
    margin-bottom: 6px;
    padding-left: 18px;
    width: 100%;

    span {
      display: block;
      width: 100%;
      font-size: 16px;
      color: #888;
      line-height: 40px;
      transition: .2s;
      padding-bottom: 6px;
      user-select: none;

      &:hover {
         cursor: pointer;
       }
    }

    i {
      transition: .2s;
      font-size: 12px;
      color: #979797;
      transform: translateY(-2px);
    }

    .is-active {
      span, i {
        color: #409EFF;
      }
      i {
        transform: rotateZ(180deg) translateY(3px);
      }
    }

    &:hover {
      span, i {
        color: #409EFF;
      }
    }
  }

  .nav-dropdown-list {
    width: auto;
  }

  @media (max-width: 850px) {
    .header {
      .nav-logo {
        display: none;
      }
      .nav-logo-small {
        display: inline-block;
      }
      .nav-item {
        margin-left: 6px;

        &.lang-item,
        &:last-child {
          margin-left: 10px;
        }

        a {
          padding: 0 5px;
        }
      }
      .nav-theme-switch, .nav-algolia-search {
        display: none;
      }
    }
  }

  @media (max-width: 700px) {
    .header {
      .container {
        padding: 0 12px;
      }
      .nav-item {
        a {
          font-size: 12px;
          vertical-align: top;
        }

        &.lang-item {
          height: 100%;

          .nav-lang {
            display: flex;
            align-items: center;

            span {
              padding-bottom: 0;
            }
          }
        }
      }
      .nav-dropdown {
        padding: 0;
        span {
          font-size: 12px;
        }
      }
      .nav-gap {
        padding: 0 8px;
      }
      .nav-versions {
        display: none;
      }
    }
  }
</style>
<template>
  <div class="headerWrapper">
    <header class="header" ref="header">
      <div class="container">
        <h1><router-link :to="`/${ lang }`">
          <!-- logo -->
          <slot>
            <img
              src="../assets/images/yxt-logo.png"
              alt="yxt-logo"
              height="40px"
              class="nav-logo">
            <img
              src="../assets/images/yxt-logo-small.png"
              alt="yxt-logo"
              height="38px"
              class="nav-logo-small">
          </slot>

        </router-link></h1>

         <yxt-button plain class="ml10" @click="login">
          <span class="yxtpd-flex yxtpd-flex-align-center">
            {{hadLogin ? '已登录' : '登录'}}
            <yxt-svg v-if="hadLogin" class="ml4" width="14px" height="14px" icon-class="message-success" />
          </span>
        </yxt-button>

        <!-- nav -->
        <ul class="nav">
          <li class="nav-item">
            <a href="http://xuanxing-pc-ui.yunxuetang.com.cn/#/zh-CN/front-component/installation">前台组件</a>
          </li>
          <li class="nav-item">
            <a href="http://xuanxing-pc-ui.yunxuetang.com.cn/#/zh-CN/component/installation">后台组件</a>
          </li>
          <li class="nav-item">
            <a href="http://xuanxing-h5-ui.yunxuetang.com.cn">移动端基础组件</a>
          </li>
          <li class="nav-item nav-versions" v-show="isComponentPage">
            <yxt-dropdown
              trigger="click"
              :class="{ 'is-active': verDropdownVisible }">
              <span>
                <a style="display: inline; padding: 0; line-height: 22px;">业务组件</a>
              </span>
              <yxt-dropdown-menu slot="dropdown">
                <yxt-dropdown-item>
                  <a href="/">PC</a>
                </yxt-dropdown-item>
                <yxt-dropdown-item>
                  <a href="http://xuanxing-h5-biz.yunxuetang.com.cn">移动端</a>
                </yxt-dropdown-item>
              </yxt-dropdown-menu>
            </yxt-dropdown>
          </li>
          <li class="nav-item">
            <a href="http://demo.yunxuetang.com.cn/v2/yxt_demo_page.html" target="_blank">前端公共组件</a>
          </li>
        </ul>
      </div>
    </header>
    <yxt-dialog title="登录" append-to-body :visible.sync="visible">
      <yxt-form>
        <yxt-form-item label="登录方式">
          <yxt-radio v-model="type" label="1">密码登录</yxt-radio>
          <yxt-radio v-model="type" label="2">token登录</yxt-radio>
        </yxt-form-item>
      </yxt-form>
      <yxt-form v-show="type === '1'" ref="form1" :model="formData1" :rules="rules1">
        <yxt-form-item label="账号" prop="account">
          <yxt-input v-model="formData1.account"></yxt-input>
        </yxt-form-item>
        <yxt-form-item label="密码" prop="password">
          <yxt-input v-model="formData1.password"></yxt-input>
        </yxt-form-item>
        <yxt-form-item label="域名" prop="domain">
          <yxt-input v-model="formData1.domain"></yxt-input>
        </yxt-form-item>
      </yxt-form>
      <yxt-form v-show="type === '2'" ref="form2" :model="formData2" :rules="rules2">
        <yxt-form-item label="token" prop="token">
          <yxt-input type="textarea" v-model="formData2.token"></yxt-input>
        </yxt-form-item>
      </yxt-form>
      <span slot="footer" class="dialog-footer">
        <yxt-button @click="visible = false">取消</yxt-button>
        <yxt-button type="primary" @click="doLogin">确定</yxt-button>
      </span>
    </yxt-dialog>
  </div>
</template>

<script>
// import { udpApi, coreApi } from '../core/apis';
import { Encrypt } from '../core/crypto';
import { udpApi} from 'yxt-biz-pc';
import axios from 'axios';
export default {
  data() {
    return {
      verDropdownVisible: true,
      langs: {
        'zh-CN': '中文',
        'en-US': 'English',
        'es': 'Español',
        'fr-FR': 'Français'
      },
      visible: false,
      type: '1',
      formData1: {
        account: 'admin',
        password: 'Chengjiukehu@2023',
        domain: 'pro-phx.yunxuetang.com.cn'
      },
      rules1: {
        account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        domain: [{ required: true, message: '请输入域名', trigger: 'blur' }]
      },
      formData2: {
        token: ''
      },
      rules2: {
        token: [{ required: true, message: '请输入token', trigger: 'blur' }]
      }
    };
  },
  computed: {
    hadLogin() {
      return !!localStorage.token;
    },
    lang() {
      return this.$route.path.split('/')[1] || 'zh-CN';
    },
    isComponentPage() {
      return /^component/.test(this.$route.name);
    },
    isPassword() {
      return this.type === '1';
    },
    formTarget() {
      return this.isPassword ? this.$refs.form1 : this.$refs.form2;
    }
  },
  watch: {
    type() {
      this.formTarget.clearValidate();
    }
  },
  methods: {
    login() {
      this.visible = true;
    },
    doLogin() {
      this.formTarget.validate((passed) => {
        if (passed) {
          if (this.isPassword) this.loginPassword();
          else this.loginToken();
        } else {
          return false;
        }
      });
    },
    loginToken() {
      if (this.formData2.token) {
        localStorage.token = this.formData2.token;
        udpApi.get('users/self')
          .then(res => {
            localStorage.setItem('userId', res.id);
            for (let key in res) {
              localStorage.setItem(key, res[key]);
            }
            window.location.reload();
          });
      }
    },
    loginPassword() {
      const data = {
        userName: window.btoa(this.formData1.account),
        password: Encrypt(this.formData1.password),
        domain: this.formData1.domain
      };
      axios.post(
        `${window.feConfig.common.core}auth/password`,
        data,
        {
          headers: {
            source: 501,
            'Content-Type': 'application/json;charset=UTF-8'
          }
        }
      ).then(res => {
        localStorage.clear();
        sessionStorage.clear();
        for (let key in res.data.userInfo) {
          localStorage.setItem(key, res.data.userInfo[key]);
        }
        window.location.reload();
      });
    }
  }
};
</script>
