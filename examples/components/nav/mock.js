import MockJs from 'mockjs';
import templateConfig from '../../template.config.json';

MockJs.mock(/frw\/token\/init/, 'post', function() {

  function generateSubMenu(item) {
    return MockJs.mock({
      'id': '@guid()',
      'appFeatureId': '@guid()',
      'pageCode': item.path === '/template3' ? 'template' : MockJs.Random.string(),
      'parentId': '@guid()',
      'functionCode': '@string()',
      'featureName': item.title,
      'haName': item.title,
      'isVue': '1',
      'enName': 'Tasks',
      'friendlyName': '@csentence()',
      'virtualFullUrl': `/#/template${item.path}`,
      'featureImage': 'sty_mystudytask.png',
      'featureClass': 'nicon-template',
      'openMode': '_self',
      'isLoginShow': '1',
      'isAllowAnonymous': '0',
      'isCheckPermission': '1',
      'masterPageID': null,
      'labelOrTemplateEnName': null,
      'isHideLeftNavegation': '0',
      'keywords': null,
      'description': null,
      'isAnonymousShow': '0',
      'isNew': '1',
      'toolTip': '@csentence()',
      'haToolTip': '@toolTip',
      'enToolTip': '@toolTip',
      'isHideQuickNavegation': '1',
      'subMenuInfoList': []
    });
  }

  const subMenus = templateConfig.map(item => {
    return generateSubMenu(item);
  });

  // 模拟顶部数据
  const mainMenu = ['控制中心', '实验室', '内容商城', '帮助服务'];
  const mainMenuList = mainMenu.map(item => {
    return MockJs.mock({
      'id': '@guid()',
      'isVue': '1',
      'appFeatureId': '@guid()',
      'pageCode': '@string()',
      'parentId': null,
      'functionCode': '@pageCode',
      'featureName': item,
      'haName': item,
      'enName': '@pageCode',
      'friendlyName': item,
      'virtualFullUrl': '/#/template',
      'actualUrl': null,
      'featureImage': 'default.png',
      'featureClass': '000000000000',
      'openMode': '_self',
      'isLoginShow': '1',
      'isAllowAnonymous': '0',
      'isCheckPermission': '1',
      'masterPageID': null,
      'labelOrTemplateEnName': null,
      'isHideLeftNavegation': '1',
      'keywords': null,
      'description': null,
      'isAnonymousShow': '0',
      'isNew': '1',
      'toolTip': item,
      'haToolTip': item,
      'enToolTip': item,
      'isHideQuickNavegation': '1',
      'subMenuInfoList': []
    });
  });

  // 模拟左侧数据
  const sideMenu = [
    {
      name: '培训管理中心',
      icon: 'yxt-icon-office-building',
      children: [
        '培训项目', '讲师管理', '导师管理', '学习档案', '学分设置', '证书管理'
      ]
    },
    {
      name: '平台运营中心',
      icon: 'yxt-icon-data-line',
      children: [
        '运营位管理', '导航管理', '社区运营', '时刻管理', '活动运营', '公告管理', '资讯管理', '内容合规管理', '积分与等级', '积分商城'
      ]
    },
    {
      name: '内容资源中心',
      icon: 'yxt-icon-tickets',
      children: [
        '课程设计', '知识库', '已购课程', '调查投票', '课程市场'
      ]
    },
    {
      name: '人才发展中心',
      icon: 'yxt-icon-user',
      children: [
        '岗位能力模型', '能力测评', '入职发展', '绩效关联', '人才管理', '人才档案'
      ]
    }, {
      name: '系统管理中心',
      icon: 'yxt-icon-s-tools',
      children: [
        '个性化设置', '组织结构', '角色权限', '数据权限', '移动端配置', '通知配置', '第三方接口', '账号管理', '混合式部署', '集团设置'
      ]
    }, {
      name: '报表中心',
      icon: 'yxt-icon-s-data',
      children: [
        '报表设置'
      ]
    }
  ];

  const sideMenuList = sideMenu.map(item => {
    let child = item.children.map(value => {
      let temp = {path: ''};
      temp.title = value;
      return generateSubMenu(temp);
    });

    return MockJs.mock({
      'id': '@guid()',
      'appFeatureId': '@guid()',
      'pageCode': '@string()',
      'parentId': null,
      'functionCode': '@pageCode',
      'featureName': item.name,
      'haName': item.name,
      'isVue': '1',
      'enName': 'Tasks',
      'friendlyName': '@csentence()',
      'virtualFullUrl': null,
      'featureImage': 'sty_mystudytask.png',
      'featureClass': `${item.icon} font-size-20`,
      'openMode': '_self',
      'isLoginShow': '1',
      'isAllowAnonymous': '0',
      'isCheckPermission': '1',
      'masterPageID': null,
      'labelOrTemplateEnName': null,
      'isHideLeftNavegation': '0',
      'keywords': null,
      'description': null,
      'isAnonymousShow': '0',
      'isNew': '1',
      'toolTip': '模板分类',
      'haToolTip': '模板分类',
      'enToolTip': '模板分类',
      'isHideQuickNavegation': '1',
      'subMenuInfoList': child
    });
  });

  return {
    'userId': 'd7210fe5-ad63-4b3b-acdb-e06a4dac6c59',
    'userLevel': '1',
    'username': 'admin',
    'cnName': '管理员',
    'email': null,
    'isAdmin': '1',
    'isEmailValidated': '0',
    'isMobileValidated': '1',
    'mobile': '18362956577',
    'orgId': 'b399d1fc-6014-4e89-ad19-dd5a2ea7fe1f',
    'orgName': '架构测试平台',
    'orgCode': 'jgtest',
    'orgType': 'Enterprise',
    'ouId': null,
    'ouIds': '',
    'positionId': null,
    'currentMenuGroupType': 'Free',
    'currentMenuGroupId': '9ab65f41-7bc2-4243-a887-ea2371ddb544',
    'clientKey': 'null',
    'headPictureUrl': 'http://picobd-test.yunxuetang.cn/media/userfiles/userphotos/default/112.png',
    'applicationType': 'ELearning',
    'currentLanguage': 'zh',
    'subsidiaryId': null,
    'subsidiaryNum': null,
    'enableAppMenus': [
      ...mainMenuList,
      {
        'id': 'a22c7330-d3cd-4f59-ae0b-2fa5fd934975',
        'isVue': '1',
        'appFeatureId': '314e51d9-4fb2-4de3-bd11-d2c58f205854',
        'pageCode': 'template',
        'parentId': null,
        'functionCode': 'template',
        'featureName': '模板',
        'haName': '模板',
        'enName': 'Template',
        'friendlyName': '模板',
        'virtualFullUrl': '/template',
        'actualUrl': '~/Apps/Sty/homepage.aspx',
        'featureImage': 'default.png',
        'featureClass': '000000000000',
        'openMode': '_self',
        'isLoginShow': '1',
        'isAllowAnonymous': '0',
        'isCheckPermission': '1',
        'masterPageID': null,
        'labelOrTemplateEnName': null,
        'isHideLeftNavegation': '1',
        'keywords': null,
        'description': null,
        'isAnonymousShow': '0',
        'isNew': '1',
        'toolTip': '模板',
        'haToolTip': '模板',
        'enToolTip': 'Template',
        'isHideQuickNavegation': '1',
        'subMenuInfoList': [
          {
            'id': '314e51d9-4fb2-4de3-bd11-d2c58f205834',
            'appFeatureId': '314e5239-4fb2-4de3-bd11-d2c58f205854',
            'pageCode': 'template_code',
            'parentId': null,
            'functionCode': 'template_code',
            'featureName': '模板分类',
            'haName': '模板分类',
            'isVue': '1',
            'enName': 'Tasks',
            'friendlyName': '@csentence()',
            'virtualFullUrl': null,
            'featureImage': 'sty_mystudytask.png',
            'featureClass': 'yxt-icon-postcard font-size-20',
            'openMode': '_self',
            'isLoginShow': '1',
            'isAllowAnonymous': '0',
            'isCheckPermission': '1',
            'masterPageID': null,
            'labelOrTemplateEnName': null,
            'isHideLeftNavegation': '0',
            'keywords': null,
            'description': null,
            'isAnonymousShow': '0',
            'isNew': '1',
            'toolTip': '模板分类',
            'haToolTip': '模板分类',
            'enToolTip': '模板分类',
            'isHideQuickNavegation': '1',
            'subMenuInfoList': subMenus
          },
          ...sideMenuList
        ]
      },
      {
        'id': 'a22c7330-d3cd-4f59-ae0b-2125fd934975',
        'isVue': '1',
        'appFeatureId': '314e51d9-4fb2-4de3-1211-d2c58f205854',
        'pageCode': 'component',
        'parentId': null,
        'functionCode': 'component',
        'featureName': '组件',
        'haName': '组件',
        'enName': 'component',
        'friendlyName': '组件',
        'virtualFullUrl': '/#/component',
        'actualUrl': null,
        'featureImage': 'default.png',
        'featureClass': '000000000000',
        'openMode': '_self',
        'isLoginShow': '1',
        'isAllowAnonymous': '0',
        'isCheckPermission': '1',
        'masterPageID': null,
        'labelOrTemplateEnName': null,
        'isHideLeftNavegation': '1',
        'keywords': null,
        'description': null,
        'isAnonymousShow': '0',
        'isNew': '1',
        'toolTip': '首组件页',
        'haToolTip': '组件',
        'enToolTip': 'Component',
        'isHideQuickNavegation': '1',
        'subMenuInfoList': []
      }
    ],
    'pageCode': 'homepage',
    'pageFunctionCode': 'homepage',
    'parentPageCode': 'sty_taskcenter',
    'parentParentPageCode': 'sty_index',
    'currentOrgLogo': 'http://picobd-test.yunxuetang.cn/sys/jgtest/images/201805/ac1b3c5543794210b0bcf6411c70b704.png',
    'currentOrgIco': 'http://picobd-test.yunxuetang.cn/test/orgs/test20141031/sys/image/201705/606aef62f43f4064a59838029f3fb4aa.ico',
    'authenticated': true
  };
});
