$nav_active_border_width: 4px;
$nav_height: 65px;
$nav_color_active: rgba(67, 107, 255, 1);

html,
body,
#app {
  height: 100%;
  margin: 0;
  // overflow: auto;
}

body {
  overflow: auto;
}

.yxt-main-container {
  width: 100%;
  height: 100%;
}

.yxt-nav-bar {
  width: 100%;
  height: 65px;
  background: #fff;
}

.yxt-side-menu {
  float: left;
  box-sizing: border-box;
  width: 200px;
  background: #fff;

  ul,
  li {
    margin: 0;
    list-style: none;
  }

  .yxt-submenu__title {
    height: 40px;
    line-height: 40px;
  }

  .yxt-menu {
    border-right: 0;
  }

  .yxt-menu-item {
    height: 40px;
    padding-right: 24px;
    padding-left: 50px !important;
    line-height: 40px;
  }
}

.yxt-main-content {
  position: absolute;
  top: 65px;
  right: 0;
  bottom: 0;
  left: 200px;
  padding: 0 24px;
  overflow: auto;
  background: #f0f2f5;
  transition: left 0.2s ease 0s;
}

.yxt-smart-aside {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 900;
  width: 200px;
  height: 100%;
  padding-top: 65px;
  padding-bottom: 60px;
  background-color: #fff;
  box-shadow: 0 0 10px 1px rgba(100, 100, 100, 0.1);
  transition: width 0.2s ease 0s;

  .yxt-menu {
    border-right: none;
  }

  .yxt-submenu__title,
  .yxt-menu-item {
    height: 40px;
    overflow: hidden;
    line-height: 40px;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-wrap: normal;
    word-break: normal;

    &.is-active {
      color: $nav_color_active;
      background: rgba(67, 107, 255, 0.06);
      // background-color: #f1f8ff;
      // position: relative;
      // &::before {
      //   content: '';
      //   position: absolute;
      //   left: 0;
      //   top: 0;
      //   width: 4px;
      //   height: 40px;
      //   background-color: #1890ff;
      // }
    }
  }

  .yxt-menu-item {
    // todo
    //padding-left: 50px!important;
    color: #4d4d4d;

    &:hover {
      color: $nav_color_active;
      background: rgba(67, 107, 255, 0.06);
      // background-color: #f1f8ff;
    }

    .iconfont {
      color: #4d4d4d;
      font-size: 22px;
    }
  }

  .yxt-submenu__title {
    font-size: 14px;

    &:hover {
      background-color: #fff;
    }
  }

  .yxt-submenu [class^='el-icon-'] {
    margin-right: 0;
  }

  .yxt-submenu {
    .iconfont {
      margin-right: 5px;
      font-weight: normal;
      font-size: 22px;
    }

    & > .yxt-submenu__title i {
      margin-right: 10px;
    }

    &.is-active > .yxt-submenu__title i {
      // color: #1890ff;
    }
  }

  &.yxt-smart-aside__collapse.yxt-side-menu {
    width: 72px;

    .yxt-submenu__title,
    .yxt-menu-item {
      height: 42px;
      line-height: 42px;
      text-overflow: unset;
    }

    .yxt-menu--collapse {
      width: 72px;
    }

    .yxt-submenu__title,
    .yxt-menu-item {
      height: 50px;
      line-height: 50px;
    }

    .yxt-submenu {

      // background-color: #7f63f4;
      .iconfont {
        color: #fff;
        font-size: 24px;
      }

      & > .yxt-submenu__title {
        text-align: center;

        i {
          margin-right: 0;
        }
      }
    }

    .yxt-submenu__title {
      font-size: 14px;

      &:hover {
        background-color: transparent;
      }
    }

    .yxt-menu--collapse {

      // background-color: #7f63f4;
      .yxt-menu-item {
        i {
          color: #fff;
          font-size: 24px;
        }

        &.is-active,
        &:hover {
          background-color: transparent;
        }
      }
    }

    & + .yxt-main-content {
      left: 72px;
    }
  }
}

.yxt-menu--vertical {
  .yxt-menu-item {
    height: 42px;
    line-height: 42px;

    &.is-active {
      // background-color: rgba(230, 247, 255, 1);
      background: rgba(67, 107, 255, 0.06);
      // position: relative;
      // &::before {
      //   content: '';
      //   position: absolute;
      //   right: 0;
      //   top: 0;
      //   width: 4px;
      //   height: 42px;
      //   background-color: rgba(24, 144, 255, 1);
      // }
    }

    &:hover {
      background: rgba(67, 107, 255, 0.06);
      // background-color: rgba(230, 247, 255, 1);
    }
  }
}

.yxt-smart-aside-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60px;
  line-height: 60px;
  text-align: center;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 6px 0 rgba(225, 228, 233, 0.67);
}

.w72 {
  width: 72px !important;
}

.main-container__full {}

.smart-nav-newlogo {
  height: 65px;
  padding-right: 20px;
  padding-left: 20px;
  line-height: 65px;
}

.smart-nav-logo--img {
  width: 157px;
  height: 32px;
}

// .smart-nav-logo {
//   overflow: hidden;
//   .smart-nav-logo--img {
//     //height: 50px;
//     max-width: 180px;
//     max-height: 50px;
//   }

//   + .yxt-smart-nav-right {
//     margin-left: 240px;
//     transition: marginLeft 0.2s linear;
//     box-shadow: rgba(100, 100, 100, 0.1) 10px 0px 10px 1px;
//   }

//   .smart-nav-logo--collapse {
//     position: absolute;
//     right: 20px;
//     top: 23px;
//     transition: right 0.2s linear;
//   }

//   &.smart-nav-logo-collapse {
//     + .yxt-smart-nav-right {
//       margin-left: 72px;
//     }
//     .smart-nav-logo--collapse {
//       right: 26px;
//     }
//   }
// }
.smart-nav-logo__shadow {
  position: relative;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 66px;
  padding-left: 15px;
  line-height: 66px;
}

.yxt-smart-nav {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 901;
  width: 100%;
  min-width: 1320px;
  height: $nav_height;
  // z-index:2006;
  background-color: #fff;
  box-shadow: 0 0 6px 0 rgba(225, 228, 233, 0.67);

  .yxt-smart-nav--right {
    float: right;
  }
}

.main-container__aside {
  position: absolute;
  top: $nav_height;
  right: 0;
  bottom: 0;
  left: 240px;
  overflow: auto;
  background-color: rgba(240, 242, 245, 1);
  transition: left 0.2s ease 0s, min-width 0.2s ease 0s;
}

.main-container__inner {
  //background-color: #fff;
}

.yxt-smart-menus {
  float: right;
  height: $nav_height;
  padding-left: 24px;
  overflow: hidden;
  font-size: 14px;
  line-height: $nav_height;

  .yxt-smart-menu {
    float: left;
    padding: 0 24px;
    overflow: hidden;
    text-align: center;
    cursor: pointer;

    &:hover {
      // font-weight: bold;
      color: $nav_color_active;
    }

    .yxt-smart-menu--label {
      //min-width: $nav_height;
      //float: left;
      //position: relative;
      //left: 50%;
      //transform: translateX(-50%);
      line-height: $nav_height - $nav_active_border_width;
      border-bottom: $nav_active_border_width solid #fff;
    }

    &.yxt-smart-menu__active {
      color: $nav_color_active;
      font-weight: bold;

      .yxt-smart-menu--label {
        // border-bottom: $nav_active_border_width solid rgba(127, 99, 244, 1);
      }
    }
  }
}

.yxt-smart-nav--search {
  position: relative;
  top: ($nav_height - 40px)/2;
  width: 350px;
  padding-right: 25px;
  padding-left: 25px;

  .yxt-input__inner {
    padding-left: 20px;
    background-color: #f3f3f3;
    border: none;
  }

  .yxt-input-group__append {
    padding-right: 10px;
    padding-left: 10px;
    background-color: #f3f3f3;
    border: none;

    i {
      color: #aaa;
      font-size: 14px;
    }
  }
}

.yxt-smart-nav__img {
  //box-sizing: content-box;
  width: 32px;
  height: 32px;
  margin-top: ($nav_height - 32px)/2;
  overflow: hidden;
  border-radius: 50%;
  cursor: pointer;
}

.yxt-smart-nav--username {
  padding-top: ($nav_height - 20px)/2;
  font-size: 14px;
  line-height: 20px;
}

.box-content {
  box-sizing: content-box;
}

.yxt-tabs__header {
  margin-bottom: 20px;
}

.yxt-smart-nav-right-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-top: 21px;
  margin-right: 24px;
  background: url('./nav_icons.png') no-repeat;
  cursor: pointer;

  &.yxt-smart-nav-icon-search {
    background-position: -14px -14px;
  }

  &.yxt-smart-nav-icon-notice {
    background-position: -63px -14px;
  }

  &.yxt-smart-nav-icon-task {
    background-position: -114px -14px;
  }
}

.yxt-menu .yxt-submenu:first-child {
  height: 60px;
  line-height: 60px;
  border-bottom: 1px solid #e4e4e4;

  .yxt-submenu__title {
    height: 60px;
    line-height: 60px;

    .yxt-icon-arrow-down {
      display: none;
    }
  }
}

// .yxt-menu .yxt-submenu:last-child {
//   height: 60px;
//   box-shadow: 0px 0px 6px 0px rgba(225, 228, 233, 0.67);
//   line-height: 60px;
//   text-align: center;
//   position: fixed;
//   bottom: 0;
//   width: 200px;
//   .yxt-submenu__title {
//     height: 60px;
//     line-height: 60px;
//     .yxt-icon-arrow-down {
//       display: none;
//     }
//   }
// }

.demo-block .source {
  background-color: white;
}
