import axios from 'axios';
require('./mock');

let excGet = httpAxios => {
  // 重置了GET方法，包装params
  httpAxios.oldGet = httpAxios.get;
  httpAxios.get = (url, data) => {
    return httpAxios.oldGet(url, { params: data });
  };
};
const qidahttp = axios.create({
  baseURL: 'http://devinner.yunxuetang.com.cn/v1/',
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    source: 501
  },
  validateStatus: status => {
    return status < 400;
  }
});

excGet(qidahttp);

const req = {
  resolve: config => {
    return config;
  },
  reject: error => {
    // eslint-disable-next-line
    return Promise.reject('req', error)
  }
};

const res = {
  resolve: response => {
    if (response.status === 201) {
      response.data = response.data || {};
      response.data.Location = response.headers.location;
    }
    return response.data;
  },
  reject: error => {
    if (
      error.response &&
      error.response.data &&
      error.response.data.error &&
      error.response.data.error.key
    ) {
      // let errorText = locales[config.lang][error.response.data.error.key];
      // if (errorText && window.toast) {
      //   window.toast(errorText);
      // }
    }
    if (error.response.status === 401) {
      // eslint-disable-next-line
      return Promise.reject(null)
    }
    // eslint-disable-next-line
    return Promise.reject(error.response.data)
  }
};

// Add a request interceptor
qidahttp.interceptors.request.use(req.resolve, req.reject);
// Add a response interceptor
qidahttp.interceptors.response.use(res.resolve, res.reject);

export const setToken = token => {
  qidahttp.defaults.headers.common['token'] = token;
};

export const GetNavBytoken = data => {
  return qidahttp.post('frw/token/init', data);
};
export const GetUserInfo = () => {
  return qidahttp.get('frw/user/info');
};

export const GetSchedules = () => {
  return qidahttp.get('frw/msg/waithandle/list');
};
export const GetMessages = () => {
  return qidahttp.get('frw/msg/unread/list');
};
export const GetMessageCount = () => {
  return qidahttp.get('frw/msg/unread/count');
};
// export const MessageSetRead = id => {
//   return qidahttp.put(`frw/msg/unread/readone/${id}`);
// };
// export const MessageSetReadAll = () => {
//   return qidahttp.put('frw/msg/unread/setread');
// };
export const DeleteMessage = id => {
  return qidahttp.delete(`frw/msg/unread/delete/${id}`);
};
export const CheckPermission = data => {
  return qidahttp.post('frw/check/permission', data);
};
