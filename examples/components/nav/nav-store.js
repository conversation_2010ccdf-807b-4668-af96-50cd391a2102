import {
  GetNav<PERSON>ytoken,
  GetUserInfo,
  GetMessages,
  GetMessageCount,
  GetSchedules,
  CheckPermission
} from './service';
// import navStatic from './navStatic'
// import { loadScript, loadCss } from './utils'
const types = {
  SET_NAVS: 'SET_NAVS',
  GET_NAVS: 'GET_NAVS',
  GET_USERINFO: 'GET_USERINFO',
  SET_USERINFO: 'SET_USERINFO',
  GET_FOOTER: 'GET_FOOTER',
  SET_FOOTER: 'SET_FOOTER',
  SET_PATH: 'SET_PATH',
  SET_MESSSAGES: 'SET_MESSSAGES',
  SET_MESSSAGE_COUNT: 'SET_MESSSAGE_COUNT',
  SET_SCHEDULES: 'SET_SCHEDULES',
  DELETE_MESSAGE: 'DELETE_MESSAGE',
  CLEAR_MESSAGE: 'CLEAR_MESSAGE'
};

const state = {
  navs: {
    cnName: '',
    shortName: '',
    currentOrgLogo: '',
    enableAppMenus: [],
    userId: '',
    username: '',
    email: '',
    mobile: '',
    orgId: '',
    orgName: '',
    orgCode: ''
  },
  mainPathname: '',
  userInfo: {},
  footerData: {
    skinType: 'default'
  },
  messages: [],
  messageCount: 0,
  schedules: {
    delayPlanCount: 0,
    greetings: '您好',
    noLoginForWeek: 0,
    pendingMsgCount: 0,
    shareCount: 1,
    userName: '',
    waitCheckCount: 0
  },
  lastUpdateNavStamp: 0,
  lastUpdateUserStamp: 0,
  lastUpdateFooterStamp: 0,
  lastUpdateMessageStamp: 0,
  lastUpdateScheduleStamp: 0,
  lastUpdateMessageCountStamp: 0,
  pageCodes: ['', '', ''],
  userContent: {},
  hash: '',
  userInfoLoaded: false,
  collapse: false
};
const getPath = mainPathname => {
  let pathname = true
    ? mainPathname || state.mainPathname
    : location.pathname;
  let hash = state.hash ? state.hash : location.hash;
  if (hash) {
    if (hash.split('/').length > 2) {
      hash = hash.split('#/')[1];
      hash = '#/' + hash.substring(0, hash.indexOf('/'));
    } else if (hash === '#/') {
      // hash = '#/archive'
    }
  }
  pathname =
    pathname.lastIndexOf('/') > 0
      ? pathname.substring(0, pathname.lastIndexOf('/') + 1) + hash
      : pathname;
  return pathname;
};
const computePagecodes = pathname => {
  let codes = ['', '', ''];
  for (let i = 0; i < state.navs.enableAppMenus.length; i++) {
    if (codes[2]) break;
    let nav = state.navs.enableAppMenus[i];
    if (nav.virtualFullUrl === pathname) {
      codes[0] = nav.pageCode;
    }
    if (nav.subMenuInfoList && nav.subMenuInfoList.length) {
      let menu = nav.subMenuInfoList.filter(item => {
        return item.virtualFullUrl === pathname;
      })[0];
      if (menu) {
        codes[0] = nav.pageCode;
        codes[1] = menu.pageCode;
      } else {
        for (let j = 0; j < nav.subMenuInfoList.length; j++) {
          let subMenu = null;
          if (
            nav.subMenuInfoList[j].subMenuInfoList &&
            nav.subMenuInfoList[j].subMenuInfoList.length
          ) {
            subMenu = nav.subMenuInfoList[j].subMenuInfoList.filter(item => {
              return item.virtualFullUrl === pathname;
            })[0];
          }
          if (subMenu) {
            codes[0] = nav.pageCode;
            codes[1] = nav.subMenuInfoList[j].pageCode;
            codes[2] = subMenu.pageCode;
            break;
          }
        }
      }
    }
  }
  return codes;
};
const isChinese = letter => {
  return /^[\u4e00-\u9fa5]$/.test(letter);
};
const getShortName = name => {
  if (name.length < 4) return name;
  let arr = name.split('');
  let nameArr = [];
  let leng = 0;
  for (let index = 0; index < arr.length; index++) {
    let letter = arr[index];
    let weight = isChinese(letter) ? 2 : 1;
    if (leng + weight <= 4) {
      nameArr.push(letter);
      leng += weight;
    } else {
      nameArr.push('...');
      break;
    }
  }
  return nameArr.join('');
};
const actions = {
  getNavs: ({ commit, dispatch }, mainPathname) => {
    /* eslint-disable no-undef */
    return new Promise((resolve, reject) => {
      if (mainPathname) commit(types.SET_PATH, mainPathname);
      let pathname = getPath();
      if (pathname === '/' || pathname === '/#/index') {
        return false;
      }
      let pathData = {
        virtualFullUrl: pathname
      };
      GetNavBytoken(pathData)
        .then(data => {
          commit(types.SET_NAVS, data);
          dispatch('computePageCodes');
          resolve();
        })
        .catch(() => {
          reject(new Error('获取导航失败'));
        });
    });
  },
  computePageCodes({ commit }, pathname) {
    pathname = pathname || getPath();
    let codes = computePagecodes(pathname);
    commit('setPagecodes', codes);
  },
  getUserInfo: ({ commit }) => {
    GetUserInfo().then(data => {
      let keys = [
        'learnScores',
        'monthScores',
        'totalScores',
        'userDayPoints',
        'userDayScores',
        'userNeedScores',
        'userPoints',
        'yearScores'
      ];
      for (let key in data) {
        if (keys.indexOf(key) > -1) {
          data[key] = Number(data[key]).toFixed(2);
        }
      }
      commit(types.SET_USERINFO, data);
    });
  },
  // getFooterData: ({ commit }) => {
  //   GetFooterData().then(data => {
  //     commit(types.SET_FOOTER, data)
  //   })
  // },
  updateFrameWork: ({ dispatch }, mainPathname) => {
    if (mainPathname) {
      dispatch('getNavs', mainPathname);
    } else {
      if (!state.lastUpdateNavStamp && state.mainPathname) dispatch('getNavs');
    }
    if (!state.lastUpdateUserStamp) dispatch('getUserInfo');
    // if (!state.lastUpdateFooterStamp) dispatch('getFooterData')
    // if (!state.lastUpdateMessageStamp || (new Date().valueOf() - state.lastUpdateMessageStamp > 60000)) dispatch('getMessages')
    if (!state.lastUpdateMessageCountStamp) dispatch('getMessageCount');
    if (!state.lastUpdateScheduleStamp) dispatch('getSchedule');
  },
  setPath: ({ commit }, path) => {
    commit(types.SET_PATH, path);
  },
  getSchedule: ({ commit }, refresh) => {
    if (
      !refresh &&
      (state.lastUpdateScheduleStamp &&
        new Date().valueOf() - state.lastUpdateScheduleStamp < 60000)
    ) {
      return;
    }
    GetSchedules().then(data => {
      commit(types.SET_SCHEDULES, data);
    });
  },
  getMessages: ({ commit }, refresh) => {
    if (
      !refresh &&
      (state.lastUpdateMessageStamp &&
        new Date().valueOf() - state.lastUpdateMessageStamp < 60000)
    ) {
      return;
    }
    GetMessages().then(data => {
      commit(types.SET_MESSSAGES, data);
    });
  },
  getMessageCount: ({ commit }, refresh) => {
    if (
      !refresh &&
      (state.lastUpdateMessageCountStamp &&
        new Date().valueOf() - state.lastUpdateMessageCountStamp < 60000)
    ) {
      return;
    }
    GetMessageCount().then(data => {
      commit(types.SET_MESSSAGE_COUNT, data.msgCount);
    });
  },
  deleteMessage: ({ commit }, id) => {
    commit(types.DELETE_MESSAGE, id);
  },
  clearMessage: ({ commit }) => {
    commit(types.CLEAR_MESSAGE);
  },
  checkPermission: data => {
    return CheckPermission(data);
  },
  setHash({ commit }, hash) {
    commit('setHash', hash);
  },
  setCollapse({ commit }, status) {
    commit('setCollapse', status);
  }
};

const mutations = {
  [types.SET_NAVS](state, navs) {
    let data = navs;

    // 提取isVue=1的导航
    let navsDB = data.enableAppMenus;
    data.enableAppMenus = [];
    function getNav(arrOld, arrNew) {
      arrOld.forEach(nav => {
        if (nav.isVue === '1') {
          let navNew = {};
          Object.assign(navNew, nav);
          navNew.subMenuInfoList = [];
          arrNew.push(navNew);
          getNav(nav.subMenuInfoList, navNew.subMenuInfoList);
        }
      });
    }
    getNav(navsDB, data.enableAppMenus);

    navs.shortName = getShortName(navs.cnName);
    state.navs = navs;
    state.lastUpdateNavStamp = new Date().valueOf();
    if (navs.currentOrgIco) {
      let link =
        document.querySelector('link[rel*="icon"]') ||
        document.createElement('link');
      link.type = 'image/x-icon';
      link.rel = 'shortcut icon';
      link.href = navs.currentOrgIco;
      document.getElementsByTagName('head')[0].appendChild(link);
    }
  },
  [types.SET_USERINFO](state, userInfo) {
    userInfo.userDayPoints =
      userInfo.userDayPoints || userInfo.userDayPoint || 0;
    state.userInfo = userInfo;
    state.lastUpdateUserStamp = new Date().valueOf();
    state.userInfoLoaded = true;
  },
  // [types.SET_FOOTER] (state, footerData) {
  //   footerData.footerContent = footerData.footerContent.replace(/{{year}}/g, new Date().getFullYear())
  //   footerData.skinType = footerData.skinType || 'default'
  //   if (footerData && footerData.injectionContent) {
  //     if (typeof footerData.injectionContent === 'string') {
  //       let url = footerData.injectionContent
  //       footerData.injectionContent = [{
  //         url: url,
  //         type: 'css'
  //       }]
  //       if (url.toLowerCase().indexOf('.js') > -1) {
  //         footerData.injectionContent[0].type = 'js'
  //       }
  //     }
  //   }
  //   state.footerData = footerData
  //   state.lastUpdateFooterStamp = new Date().valueOf()
  //   if (footerData.injectionContent && footerData.injectionContent.length) {
  //     for (let i = 0; i < footerData.injectionContent.length; i++) {
  //       let file = footerData.injectionContent[i]
  //       if (typeof file === 'string') {
  //         let url = file
  //         file = {
  //           url: url,
  //           type: 'css'
  //         }
  //         if (url.toLowerCase().indexOf('.js') > -1) {
  //           file.type = 'js'
  //         }
  //       }
  //       if (file.type === 'js') {
  //         loadScript(file.url)
  //       } else {
  //         loadCss(file.url)
  //       }
  //     }
  //   }
  // },
  [types.SET_PATH](state, path) {
    state.mainPathname = path;
  },
  [types.SET_MESSSAGES](state, messages) {
    state.messages = messages;
    state.lastUpdateMessageStamp = new Date().valueOf();
  },
  [types.SET_MESSSAGE_COUNT](state, count) {
    state.messageCount = count;
    state.lastUpdateMessageCountStamp = new Date().valueOf();
  },
  [types.SET_SCHEDULES](state, schedules) {
    state.schedules = schedules;
    state.lastUpdateScheduleStamp = new Date().valueOf();
  },
  [types.DELETE_MESSAGE](state, id) {
    state.messages = state.messages.filter(message => {
      return message.id !== id;
    });
  },
  [types.CLEAR_MESSAGE](state) {
    state.messageCount = 0;
    state.messages = [];
  },
  setPagecodes(state, codes) {
    state.pageCodes = codes;
  },
  setHash(state, hash) {
    state.hash = hash;
  },
  setCollapse(state, status) {
    if (!status && status !== false) {
      state.collapse = !state.collapse;
    } else {
      state.collapse = status;
    }
  }
};

export default {
  namespaced: true,
  state,
  actions,
  mutations
};
