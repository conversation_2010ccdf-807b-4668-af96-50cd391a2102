<template>
  <div :class="['yxt-smart-aside', collapse ? 'yxt-smart-aside__collapse' : '']">
    <yxt-scrollbar>
      <yxt-menu
        v-if="currentNav&&currentNav.subMenuInfoList&&currentNav.subMenuInfoList.length"
        :default-active="mainPageCode"
        :collapse="collapse"
        :collapse-transition="false"
      >
        <yxt-submenu index="999">
          <template slot="title">
            <i class="yxt-icon-s-grid"></i>
            <span slot="title">全部分类</span>
            <i
              class="yxt-submenu__icon-arrow yxt-icon-arrow-left"
              style="margin-left:40px;"
            ></i>
          </template>
        </yxt-submenu>
        <yxt-submenu
          v-for="menu in currentNav.subMenuInfoList"
          :index="menu.pageCode"
          :key="menu.id"
        >
          <template slot="title">
            <i :class="['new-iconfont', menu.featureClass]"></i>
            <span>{{menu|Globalize('featureName')}}</span>
          </template>
          <yxt-menu-item
            v-for="submenu in menu.subMenuInfoList"
            :key="submenu.id"
            :index="submenu.pageCode"
            @click="toPage(submenu)"
          >
            <template slot="title">
              <span>{{submenu|Globalize('featureName')}}</span>
            </template>
          </yxt-menu-item>
        </yxt-submenu>
      </yxt-menu>
    </yxt-scrollbar>
    <div class="yxt-smart-aside-bottom">
      <img
        class="hand smart-nav-logo--collapse"
        src="./arrow.png"
        alt
        @click="setCollapse()"
        :style="{transform: collapse ? 'rotate(180deg)' : ''}"
      />
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
export default {
  name: 'yxt-manage-left-menu',
  components: {
  },
  data() {
    return {
    };
  },
  props: {
    // pageCodes: {
    //   type: Array,
    //   default: []
    // }
  },
  computed: {
    ...mapState({
      collapse: state => state.navManageStore.collapse
    }),
    currentNav() {
      return this.$store.state.navManageStore.navs.enableAppMenus.filter(menu => {
        return menu.pageCode === this.pageCodes[0];
      })[0];

      // let mainNav = this.$store.state.navManageStore.navs.enableAppMenus.filter(menu => {
      //   return menu.pageCode === this.pageCodes[0]
      // })[0]
      // if (!mainNav) {
      //   return []
      // }
      // let subNavs = mainNav.subMenuInfoList.filter(menu => {
      //   return menu.pageCode === this.pageCodes[1]
      // })[0]
      // return subNavs
    },
    pageCodes() {
      return this.$store.state.navManageStore.pageCodes;
    },
    mainPageCode() {
      if (this.$route) {
        return (this.$route && this.$route.meta && this.$route.meta.mainPageCode) || (this.$route.matched[0] && this.$route.matched[0].meta.vcode);
      }
      return '';
    },
    currentActive() {
      if (!this.pageCodes || !this.pageCodes[1]) {
        return '';
      }
      return this.pageCodes[1];
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    // toggle() {
    //   if (document.getElementsByClassName('yxt-smart-aside-bottom')[0].className === 'yxt-smart-aside-bottom') {
    //     document.getElementsByClassName('yxt-smart-aside-bottom')[0].className = 'yxt-smart-aside-bottom w72';
    //   } else {
    //     document.getElementsByClassName('yxt-smart-aside-bottom')[0].className = 'yxt-smart-aside-bottom';
    //   }
    // },
    ...mapActions('navManageStore', ['setCollapse']),
    showSubmenu(menu) {
      this.$set(menu, 'submenuHidden', !menu.submenuHidden);
    },
    toPage(page) {
      let pathname = this.$store.state.navManageStore.mainPathname;
      // if (this.config && (this.config.isDebug && (page.virtualFullUrl.indexOf(pathname) === -1))) return
      let url = page.virtualFullUrl;
      // if (this.config.isDebug) url = page.virtualFullUrl.split(pathname)[1] ? '/'+page.virtualFullUrl.split(pathname)[1] : url
      if (page.openMode === '_self') {
        if (page.virtualFullUrl.indexOf(pathname + '#/') > -1) {
          let arr = page.virtualFullUrl.split(pathname + '#/');
          url = arr[1];
          this.$router.push('/' + url);
        } else {
          location.href = url;
        }
      } else if (page.openMode === '_blank') {
        window.open(url);
      }
    }
  },
  filters: {
    Globalize(data, key) {
      let lang = localStorage.lang || 'ch';
      if (key === 'toolTip') {
        if (lang === 'ha') {
          key = 'haToolTip';
        } else if (lang === 'en') {
          key = 'enToolTip';
        }
      }
      if (key === 'featureName') {
        if (lang === 'ha') {
          key = 'haName';
        } else if (lang === 'en') {
          key = 'enName';
        }
      }
      return data[key];
    }
  }
};
</script>

<style scoped lang="scss">
</style>
