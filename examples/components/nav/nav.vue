<template>
  <div class="yxt-smart-nav clearfix">
    <!-- <div
      class="pull-left smart-nav-logo"
      :class="{'smart-nav-logo-collapse': collapse}"
      :style="collapseLogoStyle"
    >
      <div
        :style="{boxShadow: this.collapse ? '2px 0px 6px rgba(0, 21, 41, 0.34)' : ''}"
        class="smart-nav-logo__shadow"
      >
        <img
          class="smart-nav-logo--img"
          src="./logo.png"
          alt
          v-show="!collapse"
        />
        <img
          class="hand smart-nav-logo--collapse"
          src="./arrow.png"
          alt
          @click="setCollapse()"
          :style="{transform: collapse ? 'rotate(180deg)' : ''}"
        />
      </div>
    </div>-->
    <div class="clearfix yxt-smart-nav-right">
      <!-- <div
        class="pull-left smart-nav-logo"
        :class="{'smart-nav-logo-collapse': collapse}"
        :style="collapseLogoStyle"
      >
        <div
          :style="{boxShadow: this.collapse ? '2px 0px 6px rgba(0, 21, 41, 0.34)' : ''}"
          class="smart-nav-logo__shadow"
        >
          <img
            class="smart-nav-logo--img"
            src="./logo.png"
            alt
            v-show="!collapse"
          />
        </div>
      </div>-->
      <div class="pull-left smart-nav-newlogo">
        <img
          class="smart-nav-logo--img"
          src="./logo.png"
          alt
        />
      </div>
      <ul class="yxt-smart-menus">
        <li
          class="yxt-smart-menu"
          :class="{'yxt-smart-menu__active': menu.pageCode === pageCodes[0]}"
          v-for="menu in currentNav.enableAppMenus"
          :key="menu.id"
          @click="goSubPage(menu)"
        >
          <div class="yxt-smart-menu--label">{{menu | Globalize("featureName")}}</div>
        </li>
        <li
          class="yxt-smart-menu"
          style="margin-left:60px;"
        >
          <div class="yxt-smart-nav--right pr24 clearfix">
            <!-- <i class="yxt-smart-nav-right-icon yxt-smart-nav-icon-search pull-left"></i>
            <i class="yxt-smart-nav-right-icon yxt-smart-nav-icon-notice pull-left"></i>
            <i class="yxt-smart-nav-right-icon yxt-smart-nav-icon-task pull-left"></i>-->
            <img
              class="yxt-smart-nav__img pull-left mr10 ml5"
              v-if="userInfo.headPictureUrl"
              :src="userInfo.headPictureUrl"
              alt
            />
            <span
              class="pull-left yxt-smart-nav--username box-content pl15 pr15"
            >{{userInfo.userShortName}}</span>
          </div>
        </li>
      </ul>
      <!-- <div class="yxt-smart-nav--right pr24 clearfix">
        <i class="yxt-smart-nav-right-icon yxt-smart-nav-icon-search pull-left"></i>
        <i class="yxt-smart-nav-right-icon yxt-smart-nav-icon-notice pull-left"></i>
        <i class="yxt-smart-nav-right-icon yxt-smart-nav-icon-task pull-left"></i>
        <img
          class="yxt-smart-nav__img pull-left mr10 ml5"
          v-if="userInfo.headPictureUrl"
          :src="userInfo.headPictureUrl"
          alt
        />
        <span
          class="pull-left yxt-smart-nav--username box-content pl15 pr15"
        >{{userInfo.userShortName}}</span>
      </div>-->
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import './index.scss';
export default {
  name: 'yxt-manage-nav',
  components: {
  },
  data() {
    return {
      currentRoute: [],
      currentPageCode: '',
      navLoaded: false
    };
  },
  props: {
    // 是否导初始页
    isRedirectFirstPage: {
      type: Boolean,
      default: false
    },
    // 一级导航的code，导初始页时会寻找第一个此导航下有权限的页面
    firstPageCode: {
      type: String,
      default: 'archive_index'
    },
    mainPageCode: {
      type: String,
      default: 'homepage'
    },
    mainPathname: {
      type: String,
      default: '/homepage.htm'
    },
    hash: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      collapse: state => state.navManageStore.collapse
    }),
    collapseLogoStyle() {
      return {
        width: this.collapse ? '72px' : '200px',
        overflowY: 'hidden'
      };
    },
    mainMenuLength() {
      let lang = localStorage.lang || 'ch';
      let len = this.$store.state.navManageStore.footerData.mainMenuCount || (this.$store.state.navManageStore.footerData.skinType === 'dark' ? 5 : 8);
      return lang === 'en' ? len - 1 : len;
    },
    logo() {
      return this.$store.state.navManageStore.navs.currentOrgLogo;
    },
    menus() {
      return this.$store.state.navManageStore.navs.enableAppMenus.slice(0, this.mainMenuLength);
    },
    moreMenus() {
      return this.$store.state.navManageStore.navs.enableAppMenus.slice(this.mainMenuLength);
    },
    // todo 暂时顶部用二级导航
    schedules() {
      return this.$store.state.navManageStore.schedules;
    },
    homepage() {
      let url = '/homepage.htm';
      if (this.$store.state.navManageStore.navs.enableAppMenus.length && this.$store.state.navManageStore.navs.enableAppMenus[0].virtualFullUrl) url = this.$store.state.navManageStore.navs.enableAppMenus[0].virtualFullUrl;
      return url;
    },
    userInfo() {
      return {
        userName: this.$store.state.navManageStore.cnName,
        userShortName: this.$store.state.navManageStore.navs.shortName,
        headPictureUrl: 'https://ss1.bdstatic.com/70cFvXSh_Q1YnxGkpoWK1HF6hhy/it/u=2973069531,657782944&fm=26&gp=0.jpg' // this.$store.state.navManageStore.navs.headPictureUrl
      };
    },
    // todo
    pageCodes() {
      return this.$store.state.navManageStore.pageCodes;
    },
    currentNav() {
      if (this.$store.state.navManageStore.navs.enableAppMenus.length === 0) {
        return {};
      }
      this.navLoaded = true;
      return this.$store.state.navManageStore.navs;
    }
  },
  created() {
    this.$store.dispatch('navManageStore/getNavs', this.mainPathname);
    document.body.lang = localStorage.lang || 'ch';
  },
  mounted() {
  },
  methods: {
    ...mapActions('navManageStore', ['setCollapse']),
    showMoreMenu() {
      this.moreMenuVisible = true;
    },
    hideMoreMenu() {
      this.moreMenuVisible = false;
    },
    // searchKng () {
    //   if (this.config && this.config.isDebug) return
    //   let keyword = this.keyword.replace(/^\s+|\s+$/gm, '')
    //   if (keyword) {
    //     location.href = `/kngsearchresult.htm?kw=${encodeURIComponent(keyword)}`
    //   }
    // },
    redirectIndex() {
      location.href = this.homepage;
    },
    toPage(page) {
      // this.hideMoreMenu()
      let pathname = this.$store.state.navManageStore.mainPathname;
      if (this.config && (this.config.isDebug && (page.virtualFullUrl.indexOf(pathname) !== 0))) return;
      let url = page.virtualFullUrl;
      // if (this.config.isDebug) url = page.virtualFullUrl.split(pathname)[1] ? '/'+page.virtualFullUrl.split(pathname)[1] : url
      if (page.openMode === '_self') {
        if (page.virtualFullUrl.indexOf(pathname + '#/') > -1) {
          let arr = page.virtualFullUrl.split(pathname + '#/');
          url = arr[1];
          this.$router.push('/' + url);
        } else {
          location.href = url;
        }
      } else if (page.openMode === '_blank') {
        window.open(url);
      }
    },
    // todo
    goSubPage(menu) {
      if (this.pageCodes[0] === menu.pageCode) {
        return;
      }
      this.toSubPage(menu);
    },
    toSubPage(page) {
      // this.hideMoreMenu()
      let pathname = this.$store.state.navManageStore.mainPathname;
      if (this.config && (this.config.isDebug && (page.virtualFullUrl.indexOf(pathname) !== 0))) return;
      let url = page.virtualFullUrl;
      // if (this.config.isDebug) url = page.virtualFullUrl.split(pathname)[1] ? '/'+page.virtualFullUrl.split(pathname)[1] : url
      // if (page.openMode === '_self') {
      if (page.virtualFullUrl.indexOf(pathname + '#/') > -1) {
        let arr = page.virtualFullUrl.split(pathname + '#/');
        url = arr[1];
        this.$router.push('/' + url);
      } else {
        location.href = url;
      }
      // } else if (page.openMode === '_blank') {
      //   window.open(url)
      // }
    },
    toFirstPage() {
      if (this.isRedirectFirstPage && this.firstPageCode) {
        // 导初始页时会寻找第一个此导航下有权限的页面
        this.currentNav.enableAppMenus.forEach(navFirst => {
          if (navFirst.pageCode === this.firstPageCode) {
            let pageTo = navFirst.subMenuInfoList[0].subMenuInfoList[0];
            if (pageTo.virtualFullUrl.indexOf(this.mainPathname) >= 0) {
              let urlTo = pageTo.virtualFullUrl.replace(this.mainPathname + '#', '');
              if (pageTo) {
                this.$router.replace({
                  path: urlTo
                });
              } else {
                // 无权限
                this.$router.push({
                  name: 'errorPage',
                  query: { msg: 'apis.frw.role.HaveNo' }
                });
              }
            } else {
              window.location.replace(pageTo.virtualFullUrl);
            }
          }
        });
      }
    }
    // showSchedule () {
    //   this.scheduleVisible = !this.scheduleVisible
    //   if (this.scheduleVisible) this.$store.dispatch('navManageStore/getSchedule')
    // }
  },
  filters: {
    Globalize(data, key) {
      let lang = localStorage.lang || 'ch';
      if (key === 'toolTip') {
        if (lang === 'ha') {
          key = 'haToolTip';
        } else if (lang === 'en') {
          key = 'enToolTip';
        }
      }
      if (key === 'featureName') {
        if (lang === 'ha') {
          key = 'haName';
        } else if (lang === 'en') {
          key = 'enName';
        }
      }
      return data[key];
    }
    // Local (key) {
    //   let lang = localStorage.lang || 'ch'
    //   return locales[lang][key]
    // }
  },
  watch: {
    $route() {
      this.$store.dispatch('navManageStore/computePageCodes');
    },
    'navLoaded'(val, oldVal) {
      if (!oldVal && val) {
        this.toFirstPage();
      }
    },
    'isRedirectFirstPage'() {
      this.toFirstPage();
    }
  }
};
</script>

<style scoped lang="scss">
.smart-nav-logo {
  width: 240px;
  height: 66px;
  transition: width 0.2s ease 0s;
}
</style>
