export const themeList = [
  {
    name: 'Element',
    author: 'Element',
    theme: '{"global":{"$--color-primary":"#409EFF"},"local":{}}'
  }
];

export const eleThemeList = [
  {
    name: '<PERSON><PERSON>',
    author: 'Element',
    theme: '{"global":{"$--color-primary":"#1989FA","$--color-success":"#5CB87A","$--color-warning":"#E6A23C","$--color-danger":"#F56C6C","$--color-info":"#8896B3","$--font-size-small":"13px","$--font-size-base":"14px","$--font-size-medium":"16px","$--font-size-large":"22px","$--font-size-extra-large":"28px","$--font-line-height-secondary":"20px"},"local":{"$--button-primary-background-color":"$--color-primary","$--switch-font-size":"14px","$--datepicker-active-color":"$--color-primary","$--tooltip-border-color":"$--border-color-base","$--tooltip-color":"#FFFFFF","$--tooltip-padding":"10px","$--collapse-header-font-size":"14px","$--collapse-content-font-size":"14px","$--collapse-content-font-color":"$--color-text-regular","$--radio-input-background-color":"#FFFFFF","$--radio-icon-color":"$--color-warning","$--radio-button-checked-background-color":"$--color-primary","$--input-border-radius":"$--border-radius-base","$--select-input-focus-border-color":"$--color-primary","$--select-font-size":"14px","$--select-option-selected-font-color":"$--color-primary","$--select-input-font-size":"14px","$--select-option-height":"34px","$--slider-main-background-color":"$--color-primary","$--tag-font-color":"$--color-primary","$--tag-default-hover-background-color":"$--color-primary","$--message-warning-font-color":"$--color-warning","$--alert-title-font-size":"14px","$--alert-icon-size":"14px","$--alert-icon-large-size":"22px","$--alert-close-customed-font-size":"12px","$--notification-title-font-size":"16px","$--notification-content-font-size":"14px","$--menu-item-font-color":"$--color-primary"}}'
  }
];
