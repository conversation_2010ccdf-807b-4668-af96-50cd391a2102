<style lang="scss">
.component-preview {
  padding-right: 10px;
  &:last-of-type {
    padding-bottom: 20px;
  }
  h4 {
    font-size: 20px;
    margin: 40px 0 20px;
    color: #909399
  }
  .demo-item {
    margin-top: 10px;
    margin-right: 40px;
  }

  .demo-line {
    margin: 15px 0;
  }

  .yxt-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }

  .yxt-carousel__item:nth-child(2n + 1) {
    background-color: #d3dce6;
  }

  .yxt-avatar:not(:last-child) {
    margin-right: 20px;
  }

  .avatar-demo {
    display: flex;
    align-items: center;
  }
}
</style>
<template>
  <div class="component-preview">
    <h4>Button</h4>
    <yxt-row class="demo-line">
      <yxt-button>Default</yxt-button>
      <yxt-button type="primary">Primary</yxt-button>
      <yxt-button type="success">Success</yxt-button>
      <yxt-button type="info">Info</yxt-button>
      <yxt-button type="warning">Warning</yxt-button>
      <yxt-button type="danger">Danger</yxt-button>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-button plain>Plain</yxt-button>
      <yxt-button type="primary" plain>Primary</yxt-button>
      <yxt-button type="success" plain>Success</yxt-button>
      <yxt-button type="info" plain>Info</yxt-button>
      <yxt-button type="warning" plain>Warning</yxt-button>
      <yxt-button type="danger" plain>Danger</yxt-button>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-button round>Round</yxt-button>
      <yxt-button type="primary" round>Primary</yxt-button>
      <yxt-button type="success" round>Success</yxt-button>
      <yxt-button type="info" round>Info</yxt-button>
      <yxt-button type="warning" round>Warning</yxt-button>
      <yxt-button type="danger" round>Danger</yxt-button>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-button icon="yxt-icon-search" circle></yxt-button>
      <yxt-button type="primary" icon="yxt-icon-edit" circle></yxt-button>
      <yxt-button type="success" icon="yxt-icon-check" circle></yxt-button>
      <yxt-button type="info" icon="yxt-icon-message" circle></yxt-button>
      <yxt-button type="warning" icon="yxt-icon-star-off" circle></yxt-button>
      <yxt-button type="danger" icon="yxt-icon-delete" circle></yxt-button>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-button>Default</yxt-button>
      <yxt-button size="medium">Medium</yxt-button>
      <yxt-button size="small">Small</yxt-button>
      <yxt-button size="mini">Mini</yxt-button>
    </yxt-row>
    <h4>Radio</h4>
    <yxt-row class="demo-line">
      <yxt-radio v-model="radio" label="1">Option A</yxt-radio>
      <yxt-radio v-model="radio" label="2">Option B</yxt-radio>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-radio-group v-model="radio1">
        <yxt-radio-button label="New York"></yxt-radio-button>
        <yxt-radio-button label="Washington"></yxt-radio-button>
        <yxt-radio-button label="Los Angeles"></yxt-radio-button>
        <yxt-radio-button label="Chicago"></yxt-radio-button>
      </yxt-radio-group>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-radio v-model="radio2" label="1" border>Option A</yxt-radio>
      <yxt-radio v-model="radio2" label="2" border>Option B</yxt-radio>
    </yxt-row>
    <h4>Checkbox</h4>
    <yxt-row class="demo-line">
      <yxt-checkbox v-model="checked">Option</yxt-checkbox>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-checkbox-group v-model="checked1">
        <yxt-checkbox-button v-for="city in ['Shanghai', 'Beijing', 'Guangzhou', 'Shenzhen']" :label="city" :key="city">{{city}}</yxt-checkbox-button>
      </yxt-checkbox-group>
    </yxt-row>
    <yxt-row class="demo-line">
      <yxt-checkbox v-model="checked2" label="Option1" border></yxt-checkbox>
    </yxt-row>
    <h4>Input</h4>
    <yxt-row style="width: 180px">
      <yxt-input placeholder="Please input" v-model="input"></yxt-input>
    </yxt-row>
    <h4>InputNumber</h4>
    <yxt-row>
      <yxt-input-number v-model="inputNumber" :min="1" :max="10"></yxt-input-number>
    </yxt-row>
    <h4>Select</h4>
    <yxt-row>
      <yxt-select v-model="selectValue" placeholder="Select">
        <yxt-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></yxt-option>
      </yxt-select>
    </yxt-row>
    <h4>Cascader</h4>
    <yxt-row>
      <yxt-cascader :options="cascadeOptions" v-model="cascaderValue"></yxt-cascader>
    </yxt-row>
    <h4>Switch</h4>
    <yxt-row>
      <yxt-switch v-model="switchValue"></yxt-switch>
      <yxt-switch
        style="margin-left: 40px"
        v-model="switchValue"
        active-text="Pay by month"
        inactive-text="Pay by year">
      </yxt-switch>
    </yxt-row>
    <h4>Slider</h4>
    <yxt-row style="width: 380px">
      <yxt-slider v-model="slider"></yxt-slider>
    </yxt-row>
    <h4>DatePicker</h4>
    <yxt-row>
      <yxt-date-picker v-model="datePicker" type="date"></yxt-date-picker>
    </yxt-row>
    <h4>Rate</h4>
    <yxt-row>
      <yxt-rate class="demo-line" v-model="rate"></yxt-rate>
      <yxt-rate
        class="demo-line"
        v-model="rate"
        show-score
        text-color="#ff9900"
        score-template="{value} points">
      </yxt-rate>
    </yxt-row>
    <h4>Transfer</h4>
    <yxt-row>
      <yxt-transfer v-model="transfer" filterable :data="transferData">
        <yxt-button class="transfer-footer" slot="left-footer" size="small">Operation</yxt-button>
        <yxt-button class="transfer-footer" slot="right-footer" size="small">Operation</yxt-button>
      </yxt-transfer>
    </yxt-row>
    <h4>Table</h4>
    <yxt-row>
      <yxt-table :data="tableData" style="width: 70%">
        <yxt-table-column prop="date" label="Date" width="180"></yxt-table-column>
        <yxt-table-column prop="name" label="Name" width="180"></yxt-table-column>
        <yxt-table-column prop="address" label="Address"></yxt-table-column>
      </yxt-table>
    </yxt-row>
    <h4>Tag</h4>
    <yxt-row>
      <yxt-tag class="demo-item" closable>Tag One</yxt-tag>
      <yxt-tag class="demo-item" closable type="success">Tag Two</yxt-tag>
      <yxt-tag class="demo-item" closable type="info">Tag Three</yxt-tag>
      <yxt-tag class="demo-item" closable type="warning">Tag Four</yxt-tag>
      <yxt-tag class="demo-item" closable type="danger">Tag Five</yxt-tag>
    </yxt-row>
    <h4>Progress</h4>
    <yxt-row style="width: 380px">
      <yxt-progress :percentage="20"></yxt-progress>
      <yxt-progress :percentage="60" status="exception"></yxt-progress>
      <yxt-progress :percentage="100" status="success"></yxt-progress>
    </yxt-row>
    <h4>Tree</h4>
    <yxt-row style="width: 380px">
      <yxt-tree :data="treeData" :props="defaultTreeProps" ></yxt-tree>
    </yxt-row>
    <h4>Pagination</h4>
    <yxt-row>
      <yxt-pagination layout="prev, pager, next" :total="1000"></yxt-pagination>
    </yxt-row>
    <h4>Badge</h4>
    <yxt-row>
      <yxt-badge :value="12" class="demo-item">
        <yxt-button size="small">comments</yxt-button>
      </yxt-badge>
      <yxt-badge :value="3" class="demo-item">
        <yxt-button size="small">replies</yxt-button>
      </yxt-badge>
      <yxt-badge :value="1" class="demo-item" type="primary">
        <yxt-button size="small">comments</yxt-button>
      </yxt-badge>
      <yxt-badge :value="2" class="demo-item" type="warning">
        <yxt-button size="small">replies</yxt-button>
      </yxt-badge>
    </yxt-row>
    <h4>Alert</h4>
    <yxt-row style="width: 380px;">
      <yxt-alert class="demo-item" title="success alert" type="success" show-icon></yxt-alert>
      <yxt-alert class="demo-item" title="info alert" type="info" close-text="Gotcha" show-icon></yxt-alert>
      <yxt-alert class="demo-item" title="warning alert" type="warning" show-icon></yxt-alert>
      <yxt-alert
        class="demo-item"
        title="error alert"
        type="error"
        description="more text description"
        show-icon>
      </yxt-alert>
    </yxt-row>
    <h4>Loading</h4>
    <yxt-row>
      <yxt-table :data="tableData" style="width: 90%" v-loading="true">
        <yxt-table-column prop="date" label="Date" width="180"></yxt-table-column>
        <yxt-table-column prop="name" label="Name" width="180"></yxt-table-column>
        <yxt-table-column prop="address" label="Address"></yxt-table-column>
      </yxt-table>
    </yxt-row>
    <h4>Message</h4>
    <yxt-row>
      <div role="alert" class="demo-item yxt-message yxt-message--success yxt-message-fade-leave-active yxt-message-fade-leave-to" style="top: 0;left: 0;width: 100px; opacity: 1; position: relative;transform: none;"><i class="yxt-message__icon yxt-icon-success"></i><p class="yxt-message__content">Congrats, this is a success message.</p><!----></div>
      <div role="alert" class="demo-item yxt-message yxt-message--warning yxt-message-fade-leave-active yxt-message-fade-leave-to" style="top: 0;left: 0;width: 100px; opacity: 1; position: relative;transform: none;"><i class="yxt-message__icon yxt-icon-warning"></i><p class="yxt-message__content">Warning, this is a warning message.</p><!----></div>
      <div role="alert" class="demo-item yxt-message yxt-message--info yxt-message-fade-leave-active yxt-message-fade-leave-to" style="top: 0;left: 0;width: 100px; opacity: 1; position: relative;transform: none;"><i class="yxt-message__icon yxt-icon-info"></i><p class="yxt-message__content">This is a message.</p><!----></div>
      <div role="alert" class="demo-item yxt-message yxt-message--error is-closable yxt-message-fade-leave-active yxt-message-fade-leave-to" style="top: 0;left: 0;width: 100px; opacity: 1; position: relative;transform: none;"><i class="yxt-message__icon yxt-icon-error"></i><p class="yxt-message__content">Oops, this is a error message.</p><i class="yxt-message__closeBtn yxt-icon-close"></i></div>
    </yxt-row>
    <h4>MessageBox</h4>
    <yxt-row>
      <div class="yxt-message-box"><div class="yxt-message-box__header"><div class="yxt-message-box__title"><!----><span>Warning</span></div><button type="button" aria-label="Close" class="yxt-message-box__headerbtn"><i class="yxt-message-box__close yxt-icon-close"></i></button></div><div class="yxt-message-box__content"><div class="yxt-message-box__status yxt-icon-warning"></div><div class="yxt-message-box__message"><p>This will permanently delete the file. Continue?</p></div><div class="yxt-message-box__input" style="display: none;"><div class="yxt-input"><!----><input type="text" autocomplete="off" placeholder="" class="yxt-input__inner"><!----><!----><!----></div><div class="yxt-message-box__errormsg" style="visibility: hidden;"></div></div></div><div class="yxt-message-box__btns"><button type="button" class="yxt-button yxt-button--default yxt-button--small"><!----><!----><span>
          Cancel
        </span></button><button type="button" class="yxt-button yxt-button--default yxt-button--small yxt-button--primary "><!----><!----><span>
          OK
        </span></button></div></div>
    </yxt-row>
    <h4>Notification</h4>
    <yxt-row>
      <div role="alert" class="yxt-notification right" style="position: relative; left: 0;"><!----><div class="yxt-notification__group"><span class="yxt-notification__title">Notification</span><div class="yxt-notification__content"><div>This is a message </div></div><div class="yxt-notification__closeBtn yxt-icon-close"></div></div></div>
    </yxt-row>
    <h4>Menu</h4>
    <yxt-row>
      <yxt-menu :default-active="menu" class="yxt-menu-demo" mode="horizontal">
        <yxt-menu-item index="1">Processing Center</yxt-menu-item>
        <yxt-submenu index="2">
          <template slot="title">Workspace</template>
          <yxt-menu-item index="2-1">item one</yxt-menu-item>
          <yxt-menu-item index="2-2">item two</yxt-menu-item>
          <yxt-menu-item index="2-3">item three</yxt-menu-item>
          <yxt-submenu index="2-4">
            <template slot="title">item four</template>
            <yxt-menu-item index="2-4-1">item one</yxt-menu-item>
            <yxt-menu-item index="2-4-2">item two</yxt-menu-item>
            <yxt-menu-item index="2-4-3">item three</yxt-menu-item>
          </yxt-submenu>
        </yxt-submenu>
        <yxt-menu-item index="3" disabled>Info</yxt-menu-item>
        <yxt-menu-item index="4">
          <a href="https://www.yxt.com" target="_blank">Orders</a>
        </yxt-menu-item>
      </yxt-menu>
      <yxt-menu
        default-active="2"
        class="demo-line"
      >
        <yxt-submenu index="1">
          <template slot="title">
            <i class="yxt-icon-location"></i>
            <span>Navigator One</span>
          </template>
          <yxt-menu-item-group title="Group One">
            <yxt-menu-item index="1-1">item one</yxt-menu-item>
            <yxt-menu-item index="1-2">item one</yxt-menu-item>
          </yxt-menu-item-group>
          <yxt-menu-item-group title="Group Two">
            <yxt-menu-item index="1-3">item three</yxt-menu-item>
          </yxt-menu-item-group>
          <yxt-submenu index="1-4">
            <template slot="title">item four</template>
            <yxt-menu-item index="1-4-1">item one</yxt-menu-item>
          </yxt-submenu>
        </yxt-submenu>
        <yxt-menu-item index="2">
          <i class="yxt-icon-menu"></i>
          <span>Navigator Two</span>
        </yxt-menu-item>
        <yxt-menu-item index="3" disabled>
          <i class="yxt-icon-document"></i>
          <span>Navigator Three</span>
        </yxt-menu-item>
        <yxt-menu-item index="4">
          <i class="yxt-icon-setting"></i>
          <span>Navigator Four</span>
        </yxt-menu-item>
      </yxt-menu>
    </yxt-row>
    <h4>Tabs</h4>
    <yxt-row>
      <yxt-tabs v-model="tab" class="demo-item">
        <yxt-tab-pane label="User" name="first">User</yxt-tab-pane>
        <yxt-tab-pane label="Config" name="second">Config</yxt-tab-pane>
        <yxt-tab-pane label="Role" name="third">Role</yxt-tab-pane>
        <yxt-tab-pane label="Task" name="fourth">Task</yxt-tab-pane>
      </yxt-tabs>
      <yxt-tabs type="card" class="demo-item">
        <yxt-tab-pane label="User">User</yxt-tab-pane>
        <yxt-tab-pane label="Config">Config</yxt-tab-pane>
        <yxt-tab-pane label="Role">Role</yxt-tab-pane>
        <yxt-tab-pane label="Task">Task</yxt-tab-pane>
      </yxt-tabs>
    </yxt-row>
    <h4>Dialog</h4>
    <yxt-row>
      <div role="dialog" aria-modal="true" aria-label="Tips" class="yxt-dialog" style="margin: 0"><div class="yxt-dialog__header"><span class="yxt-dialog__title">Tips</span><button type="button" aria-label="Close" class="yxt-dialog__headerbtn"><i class="yxt-dialog__close yxt-icon yxt-icon-close"></i></button></div><div class="yxt-dialog__body"><span>This is a message</span> </div><div class="yxt-dialog__footer"><span class="dialog-footer"><button type="button" class="yxt-button yxt-button--default"><!----><!----><span>Cancel</span></button> <button type="button" class="yxt-button yxt-button--primary"><!----><!----><span>Confirm</span></button></span></div></div>
    </yxt-row>
    <h4>Tooltip</h4>
    <yxt-row>
      <div role="tooltip" x-placement="top" class="yxt-tooltip__popper is-dark" style="position: relative; width: 40px;text-align: center;">Dark<div x-arrow="" class="popper__arrow"></div>
      </div>
      <div role="tooltip" x-placement="top" class="yxt-tooltip__popper is-light" style="margin-top: 10px;position: relative; width: 40px;text-align: center;">Light<div x-arrow="" class="popper__arrow"></div>
      </div>
    </yxt-row>
    <h4>Popover</h4>
    <yxt-row>
      <div role="tooltip" x-placement="top" id="yxt-popover-2936" aria-hidden="true" class="yxt-popover yxt-popper yxt-popover--plain" tabindex="0" style="width: 200px; position: relative; "><div class="yxt-popover__title">Title</div>this is content, this is content, this is content<div x-arrow="" class="popper__arrow"></div></div>
    </yxt-row>
    <h4>Card</h4>
    <yxt-row>
      <yxt-card class="box-card">
        <div slot="header" class="clearfix">
          <span>Card name</span>
        </div>
      </yxt-card>
    </yxt-row>
    <h4>Carousel</h4>
    <yxt-row>
      <yxt-carousel height="150px">
        <yxt-carousel-item v-for="item in 4" :key="item">
          <h3>{{ item }}</h3>
        </yxt-carousel-item>
      </yxt-carousel>
    </yxt-row>
    <h4>Collapse</h4>
    <yxt-row>
      <yxt-collapse v-model="collapse">
        <yxt-collapse-item title="Consistent" name="1">
          <div>Consistent with real life: in line with the process and logic of real life, and comply with languages and habits that the users are used to;</div>
        </yxt-collapse-item>
        <yxt-collapse-item title="Feedback" name="2">
          <div>Operation feedback: enable the users to clearly perceive their operations by style updates and interactive effects;</div>
        </yxt-collapse-item>
      </yxt-collapse>
    </yxt-row>
    <h4>Avatar</h4>
    <yxt-row class="demo-line avatar-demo">
      <yxt-avatar icon="yxt-icon-user-solid"/>
      <yxt-avatar> avatar </yxt-avatar>
      <yxt-avatar shape="square" fit="contain" :src="avatarData.url"></yxt-avatar>
      <yxt-avatar size="large"> large </yxt-avatar>
      <yxt-avatar size="medium"> medium </yxt-avatar>
      <yxt-avatar size="small"> small </yxt-avatar>
    </yxt-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      radio: '1',
      radio1: 'Washington',
      radio2: '1',
      checked: true,
      checked1: ['Shanghai'],
      checked2: true,
      input: 'Element',
      inputNumber: 1,
      selectOptions: [
        {
          value: 'Option1',
          label: 'Option1'
        },
        {
          value: 'Option2',
          label: 'Option2'
        },
        {
          value: 'Option3',
          label: 'Option3'
        },
        {
          value: 'Option4',
          label: 'Option4'
        },
        {
          value: 'Option5',
          label: 'Option5'
        }
      ],
      selectValue: '',
      cascadeOptions: [
        {
          value: 'guide',
          label: 'Guide',
          children: [
            {
              value: 'disciplines',
              label: 'Disciplines',
              children: [
                {
                  value: 'consistency',
                  label: 'Consistency'
                },
                {
                  value: 'feedback',
                  label: 'Feedback'
                }
              ]
            }
          ]
        },
        {
          value: 'resource',
          label: 'Resource',
          children: [
            {
              value: 'axure',
              label: 'Axure Components'
            },
            {
              value: 'sketch',
              label: 'Sketch Templates'
            },
            {
              value: 'docs',
              label: 'Design Documentation'
            }
          ]
        }
      ],
      cascaderValue: [],
      switchValue: true,
      slider: 28,
      datePicker: '',
      rate: null,
      transferData: (() => {
        const data = [];
        for (let i = 1; i <= 15; i++) {
          data.push({
            key: i,
            label: `Option ${i}`,
            disabled: i % 4 === 0
          });
        }
        return data;
      })(),
      transfer: [1, 4],
      tableData: [
        {
          date: '2016-05-03',
          name: 'Tom',
          address: 'No. 189, Grove St, Los Angeles'
        },
        {
          date: '2016-05-02',
          name: 'Tom',
          address: 'No. 189, Grove St, Los Angeles'
        },
        {
          date: '2016-05-04',
          name: 'Tom',
          address: 'No. 189, Grove St, Los Angeles'
        },
        {
          date: '2016-05-01',
          name: 'Tom',
          address: 'No. 189, Grove St, Los Angeles'
        }
      ],
      menu: '1',
      tab: 'first',
      collapse: ['1'],
      treeData: [{
        label: 'Level one 1',
        children: [{
          label: 'Level two 1-1',
          children: [{
            label: 'Level three 1-1-1'
          }]
        }]
      }, {
        label: 'Level one 2',
        children: [{
          label: 'Level two 2-1',
          children: [{
            label: 'Level three 2-1-1'
          }]
        }, {
          label: 'Level two 2-2',
          children: [{
            label: 'Level three 2-2-1'
          }]
        }]
      }, {
        label: 'Level one 3',
        children: [{
          label: 'Level two 3-1',
          children: [{
            label: 'Level three 3-1-1'
          }]
        }, {
          label: 'Level two 3-2',
          children: [{
            label: 'Level three 3-2-1'
          }]
        }]
      }],
      defaultTreeProps: {
        children: 'children',
        label: 'label'
      },
      avatarData: {
        url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
      }
    };
  }
};
</script>
