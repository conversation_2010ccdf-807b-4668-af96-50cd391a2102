<template>
  <yxtf-badge :value="22" :max="99" class="yxtbiz-nav-top-stu__my-panel-item hover-primary-6-i color-gray-9">
    <yxt-svg width="24px" height="24px" :remote-url='"http://media-phx.yunxuetang.com.cn/common/pc_front_svg/"' icon-class="nav-msg" class=""/>
  </yxtf-badge>
</template>
<script>
import { FrontBadge } from 'yxt-pc';
import commonUtil from 'yxt-ulcd-sdk/packages/common-util';

export default {
  components: {
    YxtfBadge: FrontBadge
  },
  computed: {
    isOpenIm() {
      return commonUtil.checkTimeOutFnc(commonUtil.enmuFunctions.IM_CHAT) === 2;
    }
  }

};
</script>
