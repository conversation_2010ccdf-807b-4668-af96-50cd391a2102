<template>
  <div class='yxt-main-container clearfix'>
    <yxt-manage-nav mainPathname='/template' mainPageCode="template" />
    <yxt-manage-left-menu class='yxt-side-menu'></yxt-manage-left-menu>
    <div class='yxt-main-content'>
        <router-view></router-view>
    </div>
  </div>
</template>
<script>
import templateConfig from '../../template.config.json';
import yxtManageNav from '../../components/nav/nav';
import yxtManageLeftMenu from '../../components/nav/left-menu';

export default {
  data() {
    return {
      base: '/template',
      templateConfig
    };
  },
  components: {
    yxtManageNav,
    yxtManageLeftMenu
  }
};
</script>
<style type='scss'>
  .yxt-main-content {
    .demo-block {
      border:0;
      border-radius: 0;

      &:hover {
        box-shadow: none;
      }
    }

    .source {
      padding: 0;
    }
  }
</style>