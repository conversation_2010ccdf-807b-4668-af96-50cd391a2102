<template>
  <div class='yxt-main-container clearfix'>
    <yxtbiz-nav-main :navDatas="navDatas"
                       :isShowLeftNav="true"
                       :isShowTopNav="true"
                       :displayType="1"
                       :type="2"
                       :appName="'o2o'" :functionCode="functionCode">
      <yxt-layout>
        <yxt-layout-main>
          <yxt-layout-content size="medium">
            <router-view></router-view>
          </yxt-layout-content>
        </yxt-layout-main>
      </yxt-layout>
    </yxtbiz-nav-main>
  </div>
</template>
<script>
import templateConfig from '../../template.config.json';
import yxtManageNav from '../../components/nav/nav';
import yxtManageLeftMenu from '../../components/nav/left-menu';

export default {
  data() {
    return {
      base: '/template',
      templateConfig,
      navDatas: {
        datas: [{
          'id': '2',
          'parentId': '',
          'code': 'test112',
          'functionCode': 'test121',
          'name': '前台模版',
          'enName': '前台模版',
          'haName': 'nav top',
          'pageUrl': '/#/template/layout',
          'showed': true
        }, {
          'id': '1',
          'parentId': '',
          'code': 'test1',
          'functionCode': 'test1',
          'name': '后台模版',
          'enName': '后台模版',
          'haName': 'nav top',
          'showed': true
        }, {
          'id': '43',
          'parentId': '',
          'code': '5',
          'functionCode': 5,
          'name': '组件',
          'enName': '组件',
          'haName': 'nav top',
          'pageUrl': '/#/zh-CN/component/installation',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '11',
          'parentId': '1',
          'code': 'test22',
          'functionCode': 'test22',
          'name': '后台模版',
          'enName': '后台模版',
          'haName': 'nav top',
          'navIcon': 'yxt-icon-office-building',
          'showed': true
        }, {
          'id': '113',
          'parentId': '11',
          'code': '4',
          'functionCode': 4,
          'name': 'D01 抽屉/D02 表单居中',
          'enName': '表单',
          'haName': 'nav top',
          'pageUrl': '/#/template/template4',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '114',
          'parentId': '11',
          'code': '5',
          'functionCode': 5,
          'name': 'C11 筛选器',
          'enName': '筛选器',
          'haName': 'nav top',
          'pageUrl': '/#/template/template5',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '115',
          'parentId': '11',
          'code': '6',
          'functionCode': 6,
          'name': '图表',
          'enName': 'Chart',
          'haName': '图表',
          'pageUrl': '/#/template/g2',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '116',
          'parentId': '11',
          'code': '7',
          'functionCode': 6,
          'name': '上传模板',
          'enName': 'uploadTemplate',
          'haName': '上传模板',
          'pageUrl': '/#/template/uploadTemplate',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '22',
          'parentId': '1',
          'code': 'test11',
          'functionCode': 'test11',
          'name': '前台模版',
          'enName': '前台模版',
          'haName': 'nav top',
          'navIcon': 'yxt-icon-office-building',
          'showed': true
        }, {
          'id': '221',
          'parentId': '22',
          'code': '10',
          'functionCode': 10,
          'name': 'A03 圆角 公共样式',
          'enName': 'Style',
          'haName': '公共样式',
          'pageUrl': '/#/template/commonStyle',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '222',
          'parentId': '22',
          'code': '9',
          'functionCode': 9,
          'name': 'B11 结果提示',
          'enName': 'resultPage',
          'haName': '结果页样式',
          'pageUrl': '/#/template/resultPage',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '223',
          'parentId': '22',
          'code': '8',
          'functionCode': 8,
          'name': 'A04 布局',
          'enName': 'Layout',
          'haName': '布局',
          'pageUrl': '/#/template/layout',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '224',
          'parentId': '22',
          'code': '11',
          'functionCode': 11,
          'name': 'B14 头像',
          'enName': 'defaultAvatar',
          'haName': '默认头像',
          'pageUrl': '/#/template/front/avatar',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '225',
          'parentId': '22',
          'code': '15',
          'functionCode': 15,
          'name': 'B10 弹框/语音气泡',
          'enName': 'defaultEmpty',
          'haName': 'B10 弹框/语音气泡',
          'pageUrl': '/#/template/front/voice',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '226',
          'parentId': '11',
          'code': '13',
          'functionCode': 13,
          'name': '导出',
          'enName': 'export',
          'haName': '导出',
          'pageUrl': '/#/template/front/export',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '227',
          'parentId': '22',
          'code': '14',
          'functionCode': 14,
          'name': 'B09 筛选器',
          'enName': 'export',
          'haName': '筛选器',
          'pageUrl': '/#/template/front/filter',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '229',
          'parentId': '22',
          'code': '16',
          'functionCode': 16,
          'name': 'B15 卡片',
          'enName': 'card',
          'haName': 'B15 卡片',
          'pageUrl': '/#/template/front/card',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '230',
          'parentId': '22',
          'code': '17',
          'functionCode': 17,
          'name': '报名中心',
          'enName': 'signup',
          'haName': '报名中心',
          'pageUrl': '/#/template/signup/center',
          'openMode': '_self',
          'showed': true
        }, {
          'id': '231',
          'parentId': '11',
          'code': '18',
          'functionCode': 18,
          'name': '配置中心',
          'enName': 'signup',
          'haName': '配置中心',
          'pageUrl': '/#/template/signup/config',
          'openMode': '_self',
          'showed': true
        }]
      }
    };
  },
  computed: {
    functionCode() {
      return this.$route.meta.functionCode;
    },
    isShowLeftNav() {
      return this.$route.meta.isShowLeftNav;
    },
    isShowTopNav() {
      return this.$route.meta.isShowTopNav;
    }
  },
  methods: {
    changeNav(np) {
      this.functionCode = np ? 'test112-' : 'ote_examlibmgmt';
    },
    noPermission() {
      alert('暂无权限');
    }
  },
  components: {
    yxtManageNav,
    yxtManageLeftMenu
  }
};
</script>
<style type='scss'>
  .yxt-main-content {
    .demo-block {
      border:0;
      border-radius: 0;

      &:hover {
        box-shadow: none;
      }
    }

    .source {
      padding: 0;
    }
  }
</style>
