[{"lang": "zh-CN", "demo-block": {"hide-text": "隐藏代码", "show-text": "显示代码", "button-text": "在线运行", "tooltip-text": "前往 codepen.io 运行此示例"}, "footer": {"links": "链接", "repo": "代码仓库", "community": "社区", "changelog": "更新日志", "theme": "在线主题生成器", "faq": "常见问题", "gitter": "在线讨论", "starter": "脚手架", "feedback": "反馈建议", "contribution": "贡献指南", "eleme": "饿了么"}, "header": {"guide": "指南", "components": "组件", "theme": "主题", "resource": "资源"}, "nav": {"dropdown": "版本："}}, {"lang": "en-US", "demo-block": {"hide-text": "<PERSON>de", "show-text": "Expand", "button-text": "Try it!", "tooltip-text": "Run this demo on codepen.io"}, "footer": {"links": "Links", "repo": "GitHub", "community": "Community", "changelog": "Changelog", "theme": "Online Theme Roller", "faq": "FAQ", "gitter": "Gitter", "starter": "Starter kit", "feedback": "<PERSON><PERSON><PERSON>", "contribution": "Contribution", "eleme": "Eleme"}, "header": {"guide": "Guide", "components": "Component", "theme": "Theme", "resource": "Resource"}, "nav": {"dropdown": "Version: "}}, {"lang": "es", "demo-block": {"hide-text": "Ocultar", "show-text": "Mostrar", "button-text": "Probar", "tooltip-text": "Prueba este ejemplo en codepen.io"}, "footer": {"links": "Enlaces", "repo": "GitHub", "community": "Comunidad", "changelog": "Lista de cambios", "theme": "Online Theme Roller", "faq": "FAQ", "gitter": "Gitter", "starter": "<PERSON> de inicio", "feedback": "Comentarios", "contribution": "Contribución", "eleme": "Eleme"}, "header": {"guide": "Guía", "components": "Componentes", "theme": "Theme", "resource": "Recursos"}, "nav": {"dropdown": "Versión: "}}, {"lang": "fr-FR", "demo-block": {"hide-text": "<PERSON><PERSON><PERSON><PERSON>", "show-text": "<PERSON><PERSON><PERSON><PERSON>", "button-text": "Essayez!", "tooltip-text": "Essayer cette démo sur codepen.io"}, "footer": {"links": "<PERSON><PERSON>", "repo": "GitHub", "community": "Communauté", "changelog": "Changelog", "theme": "Online Theme Roller", "faq": "FAQ", "gitter": "Gitter", "starter": "<PERSON>", "feedback": "Commentaires", "contribution": "Contribution", "eleme": "Eleme"}, "header": {"guide": "Guide", "components": "Composants", "theme": "Theme", "resource": "Ressources"}, "nav": {"dropdown": "Version: "}}]