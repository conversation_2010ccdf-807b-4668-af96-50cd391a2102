[{"lang": "zh-CN", "display-name": {"border-color": "边框颜色", "font-color": "文字颜色", "background-color": "背景颜色", "font-weight": "文字粗细", "font-size": "文字大小", "font-line-height": "文字行高", "border-radius": "边框圆角", "vertical": "纵向", "horizontal": "横向", "padding": "内间距", "margin": "外间距", "icon": "图标", "placeholder": "占位符", "dropdown": "下拉菜单", "checked": "选中状态", "active": "激活状态", "hover": "鼠标悬停状态", "max": "最大", "medium": "中号", "small": "小号", "mini": "最小号", "min": "最小", "focus": "聚焦", "selected": "选中", "height": "高度", "size": "大小", "header": "头部", "group": "分组", "radius": "圆角", "width": "宽度", "color": "颜色", "title": "标题", "content": "内容", "success": "成功状态", "danger": "危险状态", "warning": "警告状态", "info": "提示状态", "customed": "客制化的", "disabled": "禁用状态", "default": "默认", "primary": "主要", "inrange": "日期范围"}, "action": {"require-them-name": "主题名称是必填项", "duplicate-them-name": "主题名称重复", "confirm-delete-theme": "确定要删除这个主题?", "max-user-theme": "已达自定义主题上限", "no-preview-config": "获取主题预览配置错误", "undo": "撤销", "redo": "重做", "notice": "提示", "confirm": "确定", "cancel": "取消", "load-local-theme-config": "正在恢复您上次编辑的自定义主题", "upload-theme": "点击上传主题", "rename-theme": "修改命名", "copy-theme": "复制主题", "last-modified": "最近修改", "reset-theme": "重置", "delete-theme": "删除主题", "download-theme": "下载", "theme-check": "查看", "theme-copy": "复制", "theme-edit": "编辑", "description-element": "默认主题", "description-napos": "深色主题", "description-kiwi": "KIWI 主题"}, "category": {"BrandColor": "品牌颜色", "FunctionalColor": "辅助颜色", "FontColor": "文字颜色", "BorderColor": "边框颜色", "BackgroundColor": "背景颜色", "Other": "其他", "Color": "颜色", "Border": "边框", "Font": "文字", "Radius": "边框圆角", "Shadow": "阴影", "Spacing": "间距", "FontSize": "文字大小", "FontWeight": "文字粗细", "LineHeight": "文字行高"}, "variable-name": {"color-primary": "主题色", "color-white": "基础白色", "color-black": "基础黑色", "color-success": "成功颜色", "color-warning": "警告颜色", "color-danger": "危险颜色", "color-info": "信息颜色", "color-text-primary": "主要文字颜色", "color-text-regular": "常规文字颜色", "color-text-secondary": "次要文字颜色", "color-text-placeholder": "占位文字颜色", "border-color-base": "一级边框颜色", "border-color-light": "二级边框颜色", "border-color-lighter": "三级边框颜色", "border-color-extra-light": "四级边框颜色", "background-color-base": "基础背景色", "border-radius-base": "大圆角", "border-radius-small": "小圆角", "border-radius-zero": "无圆角", "border-radius-circle": "圆形圆角", "box-shadow-base": "基础投影", "box-shadow-dark": "深色投影", "box-shadow-light": "浅色投影", "font-size-extra-large": "主标题文字大小", "font-size-large": "标题文字大小", "font-size-medium": "小标题文字大小", "font-size-base": "正文文字大小", "font-size-small": "正文（小）文字大小", "font-size-extra-small": "辅助文字大小", "font-weight-primary": "主要文字粗细", "font-weight-secondary": "次要文字粗细", "font-line-height-primary": "主要文字行高", "font-line-height-secondary": "次要文字行高", "tooltip-fill": "Dark 主题背景色", "tooltip-color": "Light 主题背景色", "slider-height": "滑块轨道高度", "datepicker-off-font-color": "不是当前月份的日期文字颜色"}}, {"lang": "en-US", "variable-name": {"color-primary": "primary color", "color-white": "basic white", "color-black": "basic black", "color-success": "success color", "color-warning": "warning color", "color-danger": "danger color", "color-info": "info color", "color-text-primary": "primary text color", "color-text-regular": "regular text color", "color-text-secondary": "secondary text color", "color-text-placeholder": "placeholder text color", "border-color-base": "border color base", "border-color-light": "border color light", "border-color-lighter": "border color lighter", "border-color-extra-light": "border color extra light", "background-color-base": "base background color", "border-radius-base": "border radius base", "border-radius-small": "border radius small", "border-radius-circle": "border radius circle", "box-shadow-base": "box shadow base", "box-shadow-dark": "box shadow dark", "box-shadow-light": "box shadow light", "font-size-extra-large": "extra large font size", "font-size-large": "large font size", "font-size-medium": "medium font size", "font-size-base": "base font size", "font-size-small": "small font size", "font-size-extra-small": "extra small font size", "font-weight-primary": "primary font weight", "font-weight-secondary": "secondary font weight", "font-line-height-primary": "primary font line height", "font-line-height-secondary": "secondary font line height"}, "display-name": {"border-color": "border color", "font-color": "font color", "background-color": "background color", "font-weight": "font weight", "font-size": "font size", "font-line-height": "font line height", "border-radius": "border radius"}, "action": {"require-them-name": "Theme name is required", "duplicate-them-name": "Duplicate them name", "confirm-delete-theme": "Are you sure you want to delete this theme?", "no-preview-config": "No preview config found", "max-user-theme": "Maxium user theme limit", "undo": "Undo", "redo": "Redo", "notice": "Notice", "confirm": "Confirm", "cancel": "Cancel", "load-local-theme-config": "Loading your last saved theme config", "last-modified": "Last modified", "upload-theme": "Click to upload theme", "reset-theme": "Reset", "rename-theme": "<PERSON><PERSON>", "copy-theme": "Copy", "delete-theme": "Delete", "download-theme": "Download", "theme-check": "Preview", "theme-copy": "Copy", "theme-edit": "Edit", "description-element": "Default theme", "description-napos": "Dark theme", "description-kiwi": "kiwi theme"}, "category": {"BrandColor": "Brand Color", "FunctionalColor": "Functional Color", "FontColor": "Font Color", "BorderColor": "Border Color", "BackgroundColor": "Background Color", "FontSize": "Font Size", "FontWeight": "Font Weight", "LineHeight": "Line Height"}}, {"lang": "es", "variable-name": {"color-primary": "primary color", "color-white": "basic white", "color-black": "basic black", "color-success": "success color", "color-warning": "warning color", "color-danger": "danger color", "color-info": "info color", "color-text-primary": "primary text color", "color-text-regular": "regular text color", "color-text-secondary": "secondary text color", "color-text-placeholder": "placeholder text color", "border-color-base": "border color base", "border-color-light": "border color light", "border-color-lighter": "border color lighter", "border-color-extra-light": "border color extra light", "background-color-base": "base background color", "border-radius-base": "border radius base", "border-radius-small": "border radius small", "border-radius-circle": "border radius circle", "box-shadow-base": "box shadow base", "box-shadow-dark": "box shadow dark", "box-shadow-light": "box shadow light", "font-size-extra-large": "extra large font size", "font-size-large": "large font size", "font-size-medium": "medium font size", "font-size-base": "base font size", "font-size-small": "small font size", "font-size-extra-small": "extra small font size", "font-weight-primary": "primary font weight", "font-weight-secondary": "secondary font weight", "font-line-height-primary": "primary font line height", "font-line-height-secondary": "secondary font line height"}, "display-name": {"border-color": "border color", "font-color": "font color", "background-color": "background color", "font-weight": "font weight", "font-size": "font size", "font-line-height": "font line height", "border-radius": "border radius"}, "action": {"require-them-name": "Theme name is required", "duplicate-them-name": "Duplicate them name", "confirm-delete-theme": "Are you sure you want to delete this theme?", "no-preview-config": "No preview config found", "max-user-theme": "Maxium user theme limit", "undo": "Undo", "redo": "Redo", "notice": "Notice", "confirm": "Confirm", "cancel": "Cancel", "load-local-theme-config": "Loading your last saved theme config", "last-modified": "Last modified", "upload-theme": "Click to upload theme", "reset-theme": "Reset", "rename-theme": "<PERSON><PERSON>", "copy-theme": "Copy", "delete-theme": "Delete", "download-theme": "Download", "theme-check": "Preview", "theme-copy": "Copy", "theme-edit": "Edit", "description-element": "Default theme", "description-napos": "Dark theme", "description-kiwi": "kiwi theme"}, "category": {"BrandColor": "Brand Color", "FunctionalColor": "Functional Color", "FontColor": "Font Color", "BorderColor": "Border Color", "BackgroundColor": "Background Color", "FontSize": "Font Size", "FontWeight": "Font Weight", "LineHeight": "Line Height"}}, {"lang": "fr-FR", "variable-name": {"color-primary": "primary color", "color-white": "basic white", "color-black": "basic black", "color-success": "success color", "color-warning": "warning color", "color-danger": "danger color", "color-info": "info color", "color-text-primary": "primary text color", "color-text-regular": "regular text color", "color-text-secondary": "secondary text color", "color-text-placeholder": "placeholder text color", "border-color-base": "border color base", "border-color-light": "border color light", "border-color-lighter": "border color lighter", "border-color-extra-light": "border color extra light", "background-color-base": "base background color", "border-radius-base": "border radius base", "border-radius-small": "border radius small", "border-radius-circle": "border radius circle", "box-shadow-base": "box shadow base", "box-shadow-dark": "box shadow dark", "box-shadow-light": "box shadow light", "font-size-extra-large": "extra large font size", "font-size-large": "large font size", "font-size-medium": "medium font size", "font-size-base": "base font size", "font-size-small": "small font size", "font-size-extra-small": "extra small font size", "font-weight-primary": "primary font weight", "font-weight-secondary": "secondary font weight", "font-line-height-primary": "primary font line height", "font-line-height-secondary": "secondary font line height"}, "display-name": {"border-color": "border color", "font-color": "font color", "background-color": "background color", "font-weight": "font weight", "font-size": "font size", "font-line-height": "font line height", "border-radius": "border radius"}, "action": {"require-them-name": "Theme name is required", "duplicate-them-name": "Duplicate them name", "confirm-delete-theme": "Are you sure you want to delete this theme?", "no-preview-config": "No preview config found", "max-user-theme": "Maxium user theme limit", "undo": "Undo", "redo": "Redo", "notice": "Notice", "confirm": "Confirm", "cancel": "Cancel", "load-local-theme-config": "Loading your last saved theme config", "last-modified": "Last modified", "upload-theme": "Click to upload theme", "reset-theme": "Reset", "rename-theme": "<PERSON><PERSON>", "copy-theme": "Copy", "delete-theme": "Delete", "download-theme": "Download", "theme-check": "Preview", "theme-copy": "Copy", "theme-edit": "Edit", "description-element": "Default theme", "description-napos": "Dark theme", "description-kiwi": "kiwi theme"}, "category": {"BrandColor": "Brand Color", "FunctionalColor": "Functional Color", "FontColor": "Font Color", "BorderColor": "Border Color", "BackgroundColor": "Background Color", "FontSize": "Font Size", "FontWeight": "Font Weight", "LineHeight": "Line Height"}}]