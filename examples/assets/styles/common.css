html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;

  &.is-component {
    overflow: hidden;
  }
}

#app {
  height: 100%;

  &.is-component {
    overflow-y: hidden;

    .main-cnt {
      height: 100%;
      min-height: auto;
      margin-top: 0;
      padding: 0;
    }

    .headerWrapper {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1500;
      width: 100%;

      .container {
        padding: 0;
      }
    }
  }
}

a {
  color: #409eff;
  text-decoration: none;
}

code {
  padding: 0 4px;
  background-color: #f9fafc;
  border: 1px solid #eaeefb;
  border-radius: 4px;
}

button,
input,
select,
textarea {
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.hljs {
  margin-bottom: 25px;
  padding: 18px 24px;
  font-size: 12px;
  font-family: Menlo, Monaco, Consolas, Courier, monospace;
  line-height: 1.8;
  background-color: #fafafa;
  border: solid 1px #eaeefb;
  border-radius: 4px;
  -webkit-font-smoothing: auto;
}

.main-cnt {
  box-sizing: border-box;
  min-height: 100%;
  margin-top: -80px;
  padding: 80px 0 0;
}

.container,
.page-container {
  width: calc(100% - 40px);
  margin: 0 auto;
  padding: 0;
}

.page-container {
  padding-top: 55px;

  h2 {
    margin: 0;
    color: #1f2d3d;
    font-size: 28px;
  }

  h3 {
    font-size: 22px;
  }

  h2,
  h3,
  h4,
  h5 {
    color: #1f2f3d;
    font-weight: normal;

    &:hover a {
      opacity: .4;
    }

    a {
      float: left;
      margin-left: -20px;
      cursor: pointer;
      opacity: 0;

      &:hover {
        opacity: .4;
      }
    }
  }

  /* p {
    color: #5e6d82;
    font-size: 14px;
    line-height: 1.5em;
  } */

  .tip {
    margin: 20px 0;
    padding: 8px 16px;
    background-color: #ecf8ff;
    border-left: #50bfff 5px solid;
    border-radius: 4px;

    code {
      color: #445368;
      background-color: rgba(255, 255, 255, .7);
    }
  }

  .warning {
    margin: 20px 0;
    padding: 8px 16px;
    background-color: #fff6f7;
    border-left: #fe6c6f 5px solid;
    border-radius: 4px;

    code {
      color: #445368;
      background-color: rgba(255, 255, 255, .7);
    }
  }
}

.demo {
  margin: 20px 0;
}

@media (max-width: 1140px) {
  .container,
  .page-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .container,
  .page-container {
    padding: 0 20px;
  }

  #app.is-component .headerWrapper .container {
    padding: 0 12px;
  }
}

.mr4 {
  margin-right: 4px;
}

.pl10 {
  padding-left: 10px;
}

.pr10 {
  padding-right: 10px;
}
