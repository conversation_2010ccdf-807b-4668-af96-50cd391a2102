import axios from 'axios';

let apiBaseUrl = {
  core: 'https://api-core-phx.yunxuetang.com.cn/v2/',
  udp: 'https://api-udp-phx.yunxuetang.com.cn/v2/',
  cc: 'https://api-cc-phx.yunxuetang.com.cn/',
  ote: 'https://api-ote-phx.yunxuetang.com.cn/v2/',
  cer: 'https://api-cer-phx.yunxuetang.com.cn/v2/',
  te: 'https://api-te-phx.yunxuetang.com.cn/v2/',
  qida: 'https://api-core-phx.yunxuetang.com.cn/v2/',
  common: 'https://api-component.yunxuetang.com.cn/v1/'
};

if (window.feConfig && window.feConfig.common) {
  apiBaseUrl = window.feConfig.common;
}
class ApiConfig {
  constructor() {
    this.baseURL = '';
    this.headers = {
      source: 501,
      'Content-Type': 'application/json;charset=UTF-8'
    };
    this.validateStatus = status => {
      return status >= 200 && status < 300;
    };
    this.params = Math.random();
  }

  setBaseUrl(module) {
    this.baseURL = apiBaseUrl[module];
  }

  static checkLogin(data) {
    if (data) {
      if (
        data.error.key === 'global.token.invalid' ||
        data.error.key === 'global.token.empty' ||
        data.error.key === 'global.user.kicked.out'
      ) {
        try {
          window.clearlocalStorageForKey();
        } catch (e) {
          window.clearCookieForKey();
        }
        if (window.isApp) {
          window.AppProtocol.tokenExpired();
        } else {
          if (window.isCustomThirdScan) {
            window.location.hash = '/customerr';
            return;
          }
          // 如果存在returnurl，则不赋值
          if (!window.getLocalStorage('returnUrl')) {
            window.setLocalStorage('returnUrl', window.location.href);
          }
          window.setTimeout(function() {
            window.location.replace(
              window.getAuthUrlByOrgId('og', 'orgid').href
            );
          }, 1000);
        }
      }
    }
  }
}
class Core extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('core');
  }
}

class Udp extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('udp');
  }
}

class Cc extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('cc');
  }
}

class Ote extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('ote');
  }
}
class Orginit extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('orginit');
  }
}

class Qida extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('qida');
  }
}

class Common extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('common');
  }
}

class Cer extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('cer');
  }
}
class Te extends ApiConfig {
  constructor() {
    super();
    this.setBaseUrl('te');
  }
}

const qidaApi = axios.create(new Qida());
const oteApi = axios.create(new Ote());
const orginitApi = axios.create(new Orginit());
const udpApi = axios.create(new Udp());
const visitorUdpApi = axios.create(new Udp());
const ccApi = axios.create(new Cc());
const commonApi = axios.create(new Common());
const cerApi = axios.create(new Cer());
const teApi = axios.create(new Te());
const coreApi = axios.create(new Core());

const resolveFunc = function(response) {
  return response.data;
};
const rejectFunc = function(error) {
  if (error.response && error.response.status === 401) {
    // ApiConfig.checkLogin(error.response.data); // 检查token
  }
  /* eslint-disable no-undef */
  return Promise.reject(
    // eslint-disable-line
    error.response &&
      error.response.data &&
      error.response.data.error &&
      (error.response.data.error.key || error.response.data.error.message)
  );
};
const reqResolveFunc = config => {
  config.headers.token = localStorage.getItem('token');
  return config;
};

const reqRejectFunc = error => {
  return Promise.reject(error); // eslint-disable-line
};
// 请求拦截器
qidaApi.interceptors.response.use(resolveFunc, rejectFunc);
udpApi.interceptors.request.use(reqResolveFunc, reqRejectFunc);
visitorUdpApi.interceptors.request.use(config => {
  config.headers.token = localStorage.getItem('vToken');
  return config;
}, reqRejectFunc);
ccApi.interceptors.request.use(reqResolveFunc, reqRejectFunc);
coreApi.interceptors.request.use(reqResolveFunc, reqRejectFunc);

// 响应拦截
udpApi.interceptors.response.use(resolveFunc, rejectFunc);
visitorUdpApi.interceptors.response.use(resolveFunc, rejectFunc);
ccApi.interceptors.response.use(resolveFunc, rejectFunc);
oteApi.interceptors.response.use(resolveFunc, rejectFunc);
orginitApi.interceptors.response.use(resolveFunc, rejectFunc);
cerApi.interceptors.response.use(resolveFunc, rejectFunc);
teApi.interceptors.response.use(resolveFunc, rejectFunc);
coreApi.interceptors.response.use(resolveFunc, rejectFunc);

oteApi.interceptors.request.use(function(config) {
  config.headers.token = localStorage.getItem('token');
  if (config.method === 'get' && !config.data) {
    config.data = true;
  }
  return config;
});
orginitApi.interceptors.request.use(function(config) {
  config.headers.token = localStorage.getItem('token');
  if (config.method === 'get' && !config.data) {
    config.data = true;
  }
  return config;
});
cerApi.interceptors.request.use(function(config) {
  config.headers.token = localStorage.getItem('token');
  if (config.method === 'get' && !config.data) {
    config.data = true;
  }
  return config;
});
qidaApi.interceptors.request.use(function(config) {
  config.headers.token = localStorage.getItem('token');
  if (config.method === 'get' && !config.data) {
    config.data = true;
  }
  return config;
});
teApi.interceptors.request.use(function(config) {
  config.headers.token = localStorage.getItem('token');
  if (config.method === 'get' && !config.data) {
    config.data = true;
  }
  return config;
});
coreApi.interceptors.request.use(function(config) {
  config.headers.token = localStorage.getItem('token');
  if (config.method === 'get' && !config.data) {
    config.data = true;
  }
  return config;
});
const qidaApiConfig = qidaApi.defaults;
const udpApiConfig = udpApi.defaults;
const ccApiConfig = ccApi.defaults;
const commonApiConfig = commonApi.defaults;
const oteApiConfig = oteApi.defaults;
const orginitApiConfig = orginitApi.defaults;
const cerApiConfig = cerApi.defaults;
const teApiConfig = teApi.defaults;
const coreApiConfig = coreApi.defaults;

const setToken = token => {
  qidaApiConfig.headers.token = token;
  udpApiConfig.headers.token = token;
  ccApiConfig.headers.token = token;
  commonApiConfig.headers.token = token;
  oteApiConfig.headers.token = token;
  orginitApiConfig.headers.token = token;
  cerApiConfig.headers.token = token;
  teApiConfig.headers.token = token;
  coreApiConfig.headers.token = token;
};
export {
  setToken,
  udpApi,
  qidaApi,
  commonApi,
  oteApi,
  orginitApi,
  cerApi,
  ccApi,
  teApi,
  coreApi,
  visitorUdpApi
};
