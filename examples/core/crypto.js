const CryptoJS = require('crypto-js');

const key = CryptoJS.enc.Utf8.parse('L2wzTPcueOVkQr2IBVAoLFQxK1IL5uxm');
const iv = CryptoJS.enc.Utf8.parse('LQsG8QY5CXKVTzpV');

// 加密
export function Encrypt(word) {
  let srcs = CryptoJS.enc.Utf8.parse(word);
  let encrypted = CryptoJS.AES.encrypt(srcs, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
}

export function Decrypt(word) {
  var decrypted = CryptoJS.AES.decrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}
