require('babel-regenerator-runtime'); // add regenerator support for async await
import Vue from 'vue';
import entry from './app';
import VueRouter from 'vue-router';
import YxtUlcdSdk from 'main/index.js';
import hljs from 'highlight.js';
import routes from './route.config';
import demoBlock from './components/demo-block';
import MainFooter from './components/footer';
import MainHeader from './components/header';
import SideNav from './components/side-nav';
import FooterNav from './components/footer-nav';
import title from './i18n/title';
import { debugLoginEncrypt } from './service';
import { setToken } from './core/apis';
import Viser from 'viser-vue';
import store from './store';

import 'packages/theme-chalk/src/index.scss';
import 'packages/theme-chalk/src/global.scss';
import './assets/styles/common.css';
import './assets/styles/fonts/style.css';
import xss, { whiteList } from 'xss';

import Yxt, {
  Row,
  Link,
  Col,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Button,
  SvgIcon,
  Scrollbar,
  // Backtop,
  Tooltip,
  Icon,
  Form,
  FormItem,
  FrontButton,
  Drawer,
  Input,
  Loading,
  Message,
  Checkbox,
  Radio,
  CheckboxGroup,
  RadioGroup,
  Select,
  Option,
  MessageBox,
  Sticky,
  Layout,
  LayoutHeader,
  LayoutMain,
  LayoutSide
} from 'yxt-pc';
import YxtPD from 'yxt-project-designer';

// function importSkin() {
//   let style = document.createElement('link');
//   let frontStyle = document.createElement('link');
//   style.setAttribute('rel', 'stylesheet');
//   frontStyle.setAttribute('rel', 'stylesheet');
//   style.setAttribute('href', 'http://xuanxing-pc-ui.yunxuetang.com.cn//static/themes/ff6600/style.css?v=15');
//   frontStyle.setAttribute('href', 'http://xuanxing-pc-ui.yunxuetang.com.cn//static/themes/ff6600/front-style.css?v=15');
//   document.body.appendChild(style);
//   document.body.appendChild(frontStyle);
// }

// importSkin();
import { commonUtil } from 'yxt-biz-pc';

export const i18n = commonUtil.i18n;
const languageOptions = {
  modules: ['o2o', 'ulcdsdk'], // 模块
  appTypes: ['pc'] // 使用的端 pc 可以默认不传
};

export const SetLanguage = async() => {
  Yxt.i18n((key, value) => i18n.t(key, value));
  await commonUtil.setLanguage(languageOptions);
  return Promise.resolve();
};

const cmps = [
  Row,
  Link,
  Col,
  Sticky,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Button,
  SvgIcon,
  Scrollbar,
  // Backtop,
  Tooltip,
  Icon,
  Form,
  FormItem,
  FrontButton,
  Drawer,
  Input,
  Loading,
  Checkbox,
  Radio,
  CheckboxGroup,
  RadioGroup,
  Select,
  Option,
  Layout,
  LayoutHeader,
  LayoutMain,
  LayoutSide
];

cmps.forEach(comp => {
  Vue.use(comp);
});

try {
  for (const key in whiteList) Object.hasOwnProperty.call(whiteList, key) && whiteList[key].push('style');
} catch (error) {
}
// 采用过滤方式而不是转译
Vue.prototype.xss = (html, options) => xss(html, Object.assign({ stripIgnoreTag: true }, options || {}));

Vue.prototype.$message = Message;
Vue.prototype.$confirm = MessageBox.confirm;

Vue.use(VueRouter);
Vue.use(Viser);
Vue.use(YxtUlcdSdk, {
  env: process.env.API_ENV || 'dev',
  source: 501
});

Vue.use(YxtPD, {
  env: process.env.API_ENV || 'dev',
  source: 501,
  appCode: 'o2o'
});

Vue.component('DemoBlock', demoBlock);
Vue.component('MainFooter', MainFooter);
Vue.component('MainHeader', MainHeader);
Vue.component('SideNav', SideNav);
Vue.component('FooterNav', FooterNav);
Vue.prototype.checkActionPermission = YxtUlcdSdk.commonUtil.checkActionPermission;
Vue.prototype.commonUtil = YxtUlcdSdk.commonUtil;

window.YXTULCDSDK = YxtUlcdSdk;

const globalEle = new Vue({
  data: { $isEle: false } // 是否 ele 用户
});
// YxtUlcdSdk.commonUtil.setTheme(); // 换肤
Vue.mixin({
  computed: {
    $isEle: {
      get: () => globalEle.$data.$isEle,
      set: data => {
        globalEle.$data.$isEle = data;
      }
    }
  }
});

const router = new VueRouter({
  mode: 'hash',
  base: __dirname,
  routes
});

router.afterEach(route => {
  // https://github.com/highlightjs/highlight.js/issues/909#issuecomment-131686186
  Vue.nextTick(() => {
    const blocks = document.querySelectorAll('pre code:not(.hljs)');
    Array.prototype.forEach.call(blocks, hljs.highlightBlock);
  });
  const data = title[route.meta.lang];
  for (let val in data) {
    if (new RegExp('^' + val, 'g').test(route.name)) {
      document.title = data[val];
      return;
    }
  }
  document.title = 'Yxt-PC-biz';
  // ga('send', 'event', 'PageView', route.name);
});
const token = window.localStorage.getItem('token');

SetLanguage().then(() => {
  const launch = () =>
    new Vue({
      // eslint-disable-line
      ...entry,
      router,
      i18n,
      store
    }).$mount('#app');
  if (token) {
    launch();
  } else {
    debugLoginEncrypt()
      .then(data => {
        for (var key of Object.keys(data.userInfo)) {
          window.localStorage[key] = data.userInfo[key];
        }
        window.localStorage.token = data.userInfo.token;
        setToken(data.userInfo.token);
        launch();
      })
      .catch(() => {
        launch();
      });
  }
});
