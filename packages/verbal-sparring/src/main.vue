<template>
  <div class="yxtulcdsdk-verbal-sparring o2oplayframe-task-container yxtulcdsdk-ulcdsdk">
    <div class="verbal-sparring-content">
      <div class="font-size-18 font-weight-500 lh26">{{$t('pc_ulcdsdk_lbl_verbal-sparringe').d('话术训练任务仅支持通过手机端使用哦，请打开绚星APP、钉钉APP、企微APP、飞书APP扫描下方二维码参加')}}</div>
      <yxtbiz-qrcode
            v-if="qrCodeUrl"
            :url="qrCodeUrl"
            :size="200"
            hide-link
            hide-download
          />
    </div>
  </div>
</template>

<script>
import { commonUtil } from 'yxt-biz-pc';
import qs from 'qs';
export default {
  name: 'YxtUlcdSdkVerbalSparring',
  props: {
    projectId: String,
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      qrCodeUrl: ''
    }
  },
  created() {
    this.setQrCodeUrl();
  },
  methods: {
    async setQrCodeUrl() {
      const queryString = qs.stringify({
        trackId: this.params.trackId,
        type: 26
      });
      const url = `#/o2o/transpage?tid=${this.params.taskId}&pid=${this.projectId}&trId=${this.params.targetId}&${queryString}`;
      const res = await commonUtil.getShortUrl('', url, 0);
      this.qrCodeUrl = res;
    }
  }
};
</script>