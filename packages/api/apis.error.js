import { Message } from 'yxt-pc';

export const apiError = (result = {}) => {
  if (typeof result === 'string') {
    result = { message: result };
  }
  switch (result.key) {
    // 页面需要特殊处理的 key
    case 'apis.o2o.group.userIds.Size': // 小组人数超过限制
    case 'apis.o2o.grouphonor.Exist':
    case 'apis.o2o.honor.name.Exist':
    case 'apis.o2o.project.cycle.must_big_period':
    case 'apis.hw.has.finished':
      break;
    default:
      Message.error(result.message);
      break;
  }
};

/**
 * 通用的错误处理方法
 * @param {Object} error 错误信息
 */
const handleError = function(err) {
  const { error } = err;
  if (error) {
    if (error.key && error.key.indexOf('global.token') >= 0) {
      return; // 不弹出token错误
    }
    // let msg = error.key ? this.$t(error.key) : error.message
    // this.$toast.fail(error.message);
    Message.error(error.message);
  }
};

let addToVue = {
  install: function(Vue) {
    Vue.prototype.handleError = handleError;
  }
};

export default addToVue;
