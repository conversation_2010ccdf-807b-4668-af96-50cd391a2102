
import base, { domainBaseUrl } from './domain';
import { Source } from './const';
import { Message } from 'yxt-pc';
import { apiError } from './apis.error';
import { commonUtil, Api } from 'yxt-biz-pc';
import { FACTOR_FUCTION_ERRORKEY } from 'main/config/factor.js';
import { state as factorState } from 'main/store/factor';

const i18n = commonUtil.i18n;
const apiEnv = window.feConfig && window.feConfig.apiEnv;
const getErrorKeys = (key) => {
  // key值
  // login => 获取token失效等登录错误的key值  ['global.token.invalid','global.token.empty','global.user.kicked.out','global.user.kicked.app.out','global.user.kicked.pc.out','global.not.authorized']
  // kicked => 获取 踢出相关错误的key ['global.user.kicked.out','global.user.kicked.app.out', 'global.user.kicked.pc.out']
  // iplimit => 报名单的key 'global.ip.restriction'
  return commonUtil.getErrorKeys(key);
};
class ApiConfig {
  constructor() {
    this.baseURL = '';
    this.headers = {
      source: Source,
      'Content-Type': 'application/json;charset=UTF-8'
    };
    this.validateStatus = status => {
      return status >= 200 && status < 300;
    };
  }
  setBaseUrl(module) {
    this.baseURL = domainBaseUrl(module);
  }

  static checkLogin(data) {
    if (data && data.error) {
      if (getErrorKeys('login').includes(data.error.key)) {
        if (getErrorKeys('kicked').includes(data.error.key)) {
          // 检测到账号在其他地方登录，已退出登录，如非本人操作请立即修改密码。
          Message.error(i18n.t('common_msg_kicked_out'));
        }

        try {
          window.clearlocalStorageForKey();
        } catch (ex) {
          window.clearCookieForKey();
        }

        if (window.isApp) {
          window.AppProtocol.tokenExpired();
        } else {
          if (window.isCustomThirdScan) {
            window.location.hash = '/customerr';
            return;
          }
          // 如果存在returnurl，则不赋值
          if (!window.getLocalStorage('returnUrl')) {
            window.setLocalStorage('returnUrl', window.location.href);
          }
          window.setTimeout(function() {
            window.location.replace(
              window.getAuthUrlByOrgId('og', 'orgid').href // 非特殊平台 跳登录页
            );
          }, 1000);
        }
      }
    }
  }
}

const checkInvalidToken = (res) => {
  try {
    window.clearlocalStorageForKey();
  } catch (ex) {
    window.clearCookieForKey();
  }
  let _error = res.data.error || {};
  return (_error.isInvalidToken = !!res.config['invalidToken']);
};

// 403相关的具体跳转
const checkIpLimit = function(error) {
  if (error) {
    if (error.key === getErrorKeys('iplimit')) {
      window.location.replace(`${window.location.origin}/default-pages/iplimit.html`); // 不在白名单，跳转到白名单页面
    } else if (error.key === 'global.no.permission') {
      window.location.href = window.location.origin + '/error/403.html';
    }
  }
};

// 请求拦截
const request = {
  resolve: conf => {
    if (conf.method === 'get' || conf.method === 'delete') {
      // 给config.data赋值, 使Content-type起作用
      if (!conf.data) {
        conf.data = true;
      }
    }

    conf.headers['token'] = localStorage.token || '';
    return conf;
  },
  reject: error => {
    // eslint-disable-next-line
    return Promise.reject('req', error);
  }
};

// 响应拦截
const response = {
  resolve: async resp => {
    return resp;
  },
  reject: error => {
    try {
      let rejectRes = error.response || {};
      let _error = rejectRes.data.error || {};
      if (rejectRes.status === 401) {
        const isInvalidToken = checkInvalidToken(rejectRes);
        if (isInvalidToken) {
          return Promise.reject(_error);
        }
        ApiConfig.checkLogin(rejectRes.data); /** 检查token */
        return Promise.reject(rejectRes.data.error.key);
      }
      if (rejectRes.status === 403) {
        checkIpLimit(_error);
      }

      if (_error && FACTOR_FUCTION_ERRORKEY.includes(_error.key)) {
      // 刷新缓存信息
        factorState.resetFunctionStatus();
      }

      if (rejectRes.config && rejectRes.config.module === 'o2o') {
        apiError(_error);
      }

      return Promise.reject(_error);

    } catch (err) {
      const newError = { message: error.message };
      return Promise.reject(newError);
    }
  }
};

const obj = {};
Object.keys(base).forEach(key => {
  class C extends ApiConfig {
    constructor() {
      super();
      this.setBaseUrl(key);
    }
  }
  let ac = Api.create(new C());
  ac.interceptors.request.use(request.resolve, request.reject);
  ac.interceptors.response.use(response.resolve, response.reject);
  ac.originGet = ac.get;
  ac.get = (url, data = {}, config = {}) => {
    data = Object.hasOwnProperty.call(data, 'params')
      ? data
      : Object.assign({ params: data }, config || {});
    return ac.originGet(url, data);
  };
  obj[key + 'Api'] = ac;
});

const setToken = token => {
  Object.keys(obj).forEach(key => {
    let headers = obj[key].defaults.headers;
    headers.token = token;
  });
};

const envNow = apiEnv;// 只从配置中心取
export const coreApi = obj.coreApi;
export const udpApi = obj.udpApi;
export const o2oApi = obj.o2oApi;
export const o2obossApi = obj.o2obossApi;
export const utilityApi = obj.utilityApi;
export const kngApi = obj.kngApi;
export const enrollApi = obj.enrollApi;
export const surveyApi = obj.surveyApi;
export const oteApi = obj.oteApi;
export const decorateApi = obj.decorateApi;
export const teApi = obj.teApi;
export const tliveApi = obj.tliveApi;
export const sspApi = obj.sspApi;
export const imApi = obj.IMApi;
export const msgApi = obj.msgApi;
export const fileApi = obj.fileApi; // 文件中心
export const o2orptApi = obj.o2orptApi;
export const ulcdApi = obj.ulcdApi;
export const flipApi = obj.flipApi;
export const hwbaseApi = obj.hwbaseApi; // 作业引擎
export const o2ogameApi = obj.o2ogameApi;
export const engAttendApi = obj.engAttendApi; // 考勤引擎
export const evalApi = obj.spevalApi; // 人才发展
export const miscApi = obj.miscApi;
export const talentApi = obj.talentApi;
export const ccApi = obj.ccApi;
export const studylogApi = obj.studylogApi;
export const basebbsApi = obj.basebbsApi;
export const bbsApi = obj.bbsApi;
export const apiBaseUrlApi = obj.apiBaseUrlApi;
export { setToken, envNow };
