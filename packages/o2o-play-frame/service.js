import {o2oApi, miscApi} from 'packages/api';
import qs from 'qs';

// 学员项目详情
export const getStuDetail = (projectId) => {
  return o2oApi.get(`/study/project/${projectId}/detail?play=true`);
};

// 项目详情
export const getProjectBaseInfo = (projectId) => {
  if (!projectId) {
    return Promise.resolve();
  }
  return o2oApi.get(`/study/stu/project/baseinfo/${projectId}`);
};

// 根据key获取翻译
export const getGlobalLangTrans = (langKey) => {
  return o2oApi.get(`global/langTrans?langKey=${langKey}&iso2o=1`);
};

export const getTaskInfo = (projectId, taskId, userId = '') => {
  return o2oApi.get(`/task/basic/info/${projectId}/${taskId}?userId=${userId}`);
};

export const getStuTaskRepeatRecoreds = (taskId, query) => {
  return o2oApi.get(`sub/task/stu/${taskId}/repeat/study/list?${qs.stringify(query)}`);
};

// 项目权限校验
export const checkPermission = (taskId, config) => {
  return o2oApi.put(`study/task/${taskId}/check`, undefined, config);
};

// 是否可以进入补考
export const getResitArrId = (arrId, projectId, batchId = '') => {
  return o2oApi.get(`ote/resit/${arrId}/${projectId}?batchId=${batchId}`);
};

// 后端记录学员最近学习的任务
export const getRecentLea = params => {
  return o2oApi.get(
    `study/student/recent/learn/${params.projectId}/${params.taskId}`
  );
};

// 学员端-任务是否完成
export const postTaskCompleted = (projectId, taskIds) => {
  return o2oApi.post(`study/stu/task/completed/query?projectId=${projectId}`, taskIds);
};

// 查询用户在这个阶段下已经获得的学分积分
export const getPeriodScore = (projectId, periodId) => {
  return o2oApi.get(`score/query/period/score?projectId=${projectId}&periodId=${periodId}`);
};

// 项目发布时是否有更新
export const getProjectReleaseChange = (projectId) => {
  return o2oApi.post(`project/checkReleaseChange/${projectId}`);
};

// 学员端-学员学习进度任务数
export const getStuCompletedTaskInfo = (projectId) => {
  return o2oApi.get(`study/stu/completed/task/info?projectId=${projectId}`);
};

// 学员端-进度排行统计
export const getStatistics = (projectId, preview) => {
  return o2oApi.get(`/client/projects/${projectId}/statistics?preview=${preview}`);
};

export const getOrgParameter = (code, orgId) => {
  return miscApi.get(`orgparsetting/code?code=${code}&orgId=${orgId}`);
};

/**
 *  获取阶段编号是否展示
 * @param {String} projectId
 * @returns
 */
export const fetchPeriodNumShowStatus = projectId => {
  return o2oApi.get(`/settings/userStudy/${projectId}/peroidnum`);
};

export const getAllocationType = (projectId) => o2oApi.get(`position/allocation/getAllocationType/${projectId}`);
