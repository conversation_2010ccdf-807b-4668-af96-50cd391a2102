<template>
  <div class="yxtulcdsdk-o2o-play-frame yxtulcdsdk-ulcdsdk pr over-hidden">
    <transition name="slide-in" @after-leave="afterLeave" @before-enter="beforeEnter">
      <aside
        v-show="leftshow&&!isFullScreen"
        v-loading="leftloading"
        class="o2oPlayFrame-left"
        :class="[{'o2oPlayFrame-left__box-shadow':fixedTransEnd}]"
      >
        <div class="hline layout-flex layout-flex-vertical">
          <div class="yxtulcdsdk-flex-center-center left-title" :class="{'v-hidden':fixedTransEnd}">
            <yxt-ulcd-sdk-svg
              class="hand"
              width="20px"
              height="20px"
              icon-class="o2ogoleftnav"
              @click.native="setConfirmgoBack"
            />
            <span class="pl8 hand flex-1" @click="setConfirmgoBack">{{ $t('pc_o2o_lbl_back').d('返回') }}</span>
            <yxtf-tooltip
              placement="bottom"
              effect="dark"
              :content="$t('pc_ulcdsdk_lbl_putawaythetrainingoutline'/* 收起培训大纲 */)"
            >
              <yxt-ulcd-sdk-svg
                class="hand o2oPlayFrame-collapse-ico"
                width="24px"
                height="24px"
                icon-class="o2ocollapseoutline"
                @click.native="leftshow = !leftshow"
              />

            </yxtf-tooltip>
          </div>
          <div>
            <div class="ml16 mr16 font-size-18 mt20 mb24 weight-bold text-26"><span><yxtf-tooltip
              class="item"
              :open-filter="true"
              :open-delay="2000"
              :content="projectDetail.name"
              placement="top"
            >
              <span class="ulcdsdk-ellipsis-2 ulcdsdk-break hand" @click="goDetail">{{ projectDetail.name }}</span>
            </yxtf-tooltip></span></div>
            <o2o-progress v-if="!gwnlCourse" ref="o2oprogress" :project-id="projectId" />
          </div>
          <div class="pr mb12 flex-1 h0">
            <yxtf-scrollbar :fit-height="true">
              <div class="mt24 ml16 yxtulcdsdk-flex-center mr4 mb22">
                <yxt-ulcd-sdk-svg
                  class="mr8 textcolorop8"
                  width="16px"
                  height="16px"
                  icon-class="o2ooutline"
                />
                <span class="font-bolder textcolorop8 font-size-14 lh22">{{ $t('pc_o2o_lbl_trainingprogram'/* 培训大纲 */) }}</span>
              </div>

              <div v-for="(period,index) in periodList" v-show="!(isStageEnable && !period.studentTaskBeans.length)" :key="index">
                <!-- 遍历阶段 -->
                <div v-if="periodType === 1 && periodList.length > 0">
                  <div class="layout-flex hand ml16 flex-space-between" :class="period.actived&&period.studentTaskBeans.length?'':'mb32'" @click="collapSingPeriod(period)">
                    <div><yxt-ulcd-sdk-svg
                      module="o2o"
                      class="mr8 mt4 "
                      :class="[{'o2oPlayFrame-task-svg':!period.actived}]"
                      width="16px"
                      height="16px"
                      icon-class="tree-expand-icon"
                    />
                    </div>
                    <div class="flex-1 mr48">
                      <yxtf-tooltip
                        class="item"
                        :open-filter="true"
                        :content="period.name"
                        :open-delay="2000"
                        placement="top"
                      >
                        <span class="ulcdsdk-ellipsis-2  period-title ulcdsdk-break">
                          <span v-if="showPeriodNum === 1">
                            <template v-if="!gwnlCourse">
                              {{ getPeriodCodeName(period, index + 1) + getColon() }}
                            </template>
                          </span>{{ gwnlCourse ? (gwnlCourseType === 1 ? period.name : $t('pc_o2o_lbl_secion', [index + 1])) : period.name }}</span>
                      </yxtf-tooltip>
                      <span v-if="period.overdueTime" class="mt4 color-8c font-size-12 lh20">{{ $t('pc_ulcdsdk_lbl_over_time'/* 逾期时间： */)+ formateDate(period.overdueTime,'yyyy-MM-dd hh:mm') }}</span>
                    </div>

                    <yxt-ulcd-sdk-svg
                      v-if="period.periodStatus"
                      class="mr16 mt2 ml14"
                      width="16px"
                      height="16px"
                      icon-class="o2ocompleted"
                    />
                  </div>
                </div>
                <!-- 遍历任务 -->
                <div
                  v-for="(group, j) in period.studentTaskBeans"
                  :key="j"
                >
                  <div v-show="period.actived" class="ml8" :class="periodType === 1&&j === period.studentTaskBeans.length-1?'mb32':''">
                    <!-- 遍历任务组 -->
                    <div
                      v-if="group.type === 15"
                      class="mt24"
                    >
                      <div class="layout-flex mr48 hand flex-space-between" :class="periodType === 1?'ml32':'ml24'" @click="collapSingPeriod(group)">
                        <span><yxt-ulcd-sdk-svg
                          class="mr4 mt3 hand"
                          module="o2o"
                          :class="[{'o2oPlayFrame-task-svg':!group.actived}]"
                          width="16px"
                          height="16px"
                          icon-class="tree-expand-icon"
                        />
                        </span>
                        <div class="flex-1"> <yxtf-tooltip
                          class="item"
                          :open-filter="true"
                          :content="group.name"
                          :open-delay="2000"
                          placement="top"
                        >
                          <span class="ulcdsdk-ellipsis-2 group-title weight-bold ulcdsdk-break">{{ group.name }}</span>
                        </yxtf-tooltip>
                        </div>

                      </div>
                      <template v-if="group.actived">
                        <div
                          v-for="(task ,k) in group.groupCourse"
                          :key="k"
                        >
                          <task
                            :group-detail="task"
                            :chapter-list="chapterList"
                            :active-id="activeId"
                            :elective-text="electiveText"
                            :compulsory-text="compulsoryText"
                            :period-type="periodType"
                            :task-type="2"
                            :project-detail-config="projectDetail.webSettingConfig"
                            @onclickTask="onclickTask"
                            @onclickKng="clickKng"
                          />
                        </div>
                      </template>
                    </div>
                    <div v-else>
                      <task
                        :group-detail="group"
                        :chapter-list="chapterList"
                        :task-type="1"
                        :active-id="activeId"
                        :elective-text="electiveText"
                        :compulsory-text="compulsoryText"
                        :period-type="periodType"
                        :project-detail-config="projectDetail.webSettingConfig"
                        @onclickTask="onclickTask"
                        @onclickKng="clickKng"
                      />
                    </div>
                  </div>
                </div>
              </div>

            </yxtf-scrollbar>
          </div>
        </div>
      </aside>
    </transition>
    <header class="o2oPlayFrame-right" :class="{'o2oPlayFrame-rightwidth':leftshow&&!isFullScreen&&!leftpa, 'pr z-1000':fixedTransEnd}">
      <div v-show="!isFullScreen" class="title headerborder pr24">
        <div v-if="!leftshow||leftpa" class="ml16 yxtulcdsdk-flex-center-center">
          <yxt-ulcd-sdk-svg
            class="hand"
            width="20px"
            height="20px"
            icon-class="o2ogoleftnav"
            @click.native="setConfirmgoBack"
          />
          <span class="pl8 hand  flex-1" @click="setConfirmgoBack">{{ $t('pc_o2o_lbl_back').d('返回') }}</span>
          <span
            class="mr32 ml32 o2oPlayFrame-divider-vertical"
          ></span>
          <yxtf-tooltip
            placement="bottom"
            effect="dark"
            :content="$t('pc_ulcdsdk_lbl_fixedtrainingoutline'/* 固定培训大纲 */)"
          >
            <yxt-ulcd-sdk-svg
              class="hand o2oPlayFrame-collapse-ico"
              width="24px"
              height="24px"
              icon-class="o2ocollapseoutline1"
              @mouseenter.native="leftEnter"
              @click.native="fixedOutline"
            />
          </yxtf-tooltip>
        </div>
        <div class="ml24 flex-1 w0">
          <div class="yxtulcdsdk-flex-center">
            <yxtf-tooltip
              class="item"
              :open-filter="true"
              :open-delay="2000"
              :content="projectTaskDetail.name"
              placement="bottom"
            >
              <div class="ellipsis">{{ projectTaskDetail.name }}</div>
            </yxtf-tooltip>
          </div>
          <o2o-breadcrumb
            :is-multiple="isMultiple"
            :component-name="componentName"
            @setBackTask="setBackTask"
          />
        </div>
        <div class="ml80"> <yxtf-button
                             plain
                             icon="yxtf-icon-arrow-left"
                             @click="playPrevious()"
                           >{{ $t('pc_ulcdsdk_lbl_previous' /* 上一个 */) }}</yxtf-button>
          <yxtf-button
            class="ml12"
            plain
            @click="playNext()"
          >{{ $t('pc_ulcdsdk_lbl_next' /* 下一个 */) }}<i class="yxtf-icon-arrow-right yxtf-icon--right"></i></yxtf-button></div>
      </div>
    </header>
    <main class="o2oPlayFrame-right" :class="[{'h100p':isFullScreen},{'o2oPlayFrame-rightwidth':leftshow&&!isFullScreen&&!leftpa},{'o2oPlayFrame-right__wrap': !isFullScreen}]" @mouseenter="leftpa ? leftLeave() : null">
      <div class="main" :class="[{'p24':!['yxt-ulcd-sdk-examing', 'yxt-ulcd-sdk-course-page','yxt-ulcd-sdk-practicing','yxt-ulcd-sdk-o2o-offline-task'].includes(componentName)&&!isFullScreen}]">
        <!-- 锁定 -->
        <o2o-unlocked
          v-if="studyStatus===1||studyStatus===2"
          :remark="remark"
          :study-status="studyStatus"
          :study-start-time="studyStartTime"
        />
        <div v-else-if="!componentName" v-loading="true" class="wline hline"></div>
        <components
          :is="componentName"
          v-else-if="activeComponentId"
          :id="params.kngId"
          :key="activeComponentId"
          :ref="componentName"
          :params="params"
          :target="params"
          :task-id="activeId"
          :project-id="projectId"
          :inner="true"
          :is-course="projectTaskDetail.type===9"
          :radius="false"
          :auto-play="autoPlay"
          @fullScreen="fullScreen"
          @updateProgress="updateProgress"
          @multiClick="multiClick"
          @needCloseCatalog="needCloseCatalog"
        />
      </div>
    </main>
    <study-records
      v-if="studyRecordsVisible"
      :project-id="projectId"
      :chapter-point="chapterPoint"
      :task-detail="projectTaskDetail"
      :chapter-cer-num="chapterCerNum"
      :visible.sync="studyRecordsVisible"
    />
    <play-chapters-complete-dialog
      :visible.sync="chaptersCompleteVisible"
      :chapter-name="chapterName"
      :chapter-score="chapterScore"
      :chapter-point="chapterPoint"
      :chapter-cer-num="chapterCerNum"
      :is-chapter-for-next="isChapterForNext"
      app-code="o2o"
      @playNextChapter="playNextChapter"
    />

    <yxtf-dialog
      padding-size="small"
      :visible.sync="dialogCourseTip"
      width="400px"
    >
      <div class="mt16 ph12">
        <span>{{ dialogCourseTip===2?$t('pc_ulcdsdk_lbl_nexttaskoftraining',[playThisTaskInfo.name]):$t('pc_ulcdsdk_lbl_previoustaskoftraining',[playThisTaskInfo.name]) }}</span>
      </div>
      <div class="mt12 text-right">
        <yxtf-button plain @click="dialogCourseTip = 0">{{ $t('pc_ulcdsdk_cancel'/* 取消 */) }}</yxtf-button><yxtf-button type="primary" @click="playThisTask(false)">{{ $t('pc_ulcdsdk_done'/* 确定 */) }}</yxtf-button>
      </div>
    </yxtf-dialog>
  </div>
</template>

<script>
const LANGKEY = {
  O2O_COMPULSORY: 'keys.org.ssp.custom.global.compulsory', // 必修文案
  O2O_OPTIONAL: 'keys.org.ssp.custom.global.optional' // 选修文案
};
import { TitleUtil } from 'yxt-biz-pc';
import Svg from 'packages/_components/svg.vue';
import inject from 'packages/course-page/mixins/channel/inject';
import task from 'packages/o2o-play-frame/src/components/task.vue';
import { dateFormat } from 'packages/_utils/core/date-format';
import o2oUnlocked from 'yxt-ulcd-sdk/packages/o2o-play-frame/src/components/o2oUnlocked.vue';
import o2oBreadcrumb from 'yxt-ulcd-sdk/packages/o2o-play-frame/src/components/o2oBreadcrumb.vue';
import o2oProgress from 'yxt-ulcd-sdk/packages/o2o-play-frame/src/components/o2oProgress.vue';
import studyRecords from 'yxt-ulcd-sdk/packages/o2o-play-frame/src/study-records/index.vue';
import playChaptersCompleteDialog from 'yxt-ulcd-sdk/packages/playframe/src/components/playChaptersCompleteDialog.vue';
import outsideSchedule from 'yxt-ulcd-sdk/packages/course-page/src/components/outside-schedule.vue';
import { getLanguage, getTimeStamp, goO2OPage, getQueryString, getPeriodCodeName } from 'packages/_utils/core/utils.js';
import { getErrorMsg, sleep, delayTimeOut } from '../utils.js';
import { getAllocationType, fetchPeriodNumShowStatus, getStuDetail, getProjectBaseInfo, getGlobalLangTrans, checkPermission, getResitArrId, getRecentLea, postTaskCompleted, getPeriodScore, getProjectReleaseChange, getOrgParameter } from '../service';

export default {
  name: 'YxtUlcdSdkO2oPlayFrame',
  mixins: [inject],
  components: {
    studyRecords,
    playChaptersCompleteDialog,
    outsideSchedule,
    [Svg.name]: Svg,
    task,
    o2oProgress,
    o2oUnlocked,
    o2oBreadcrumb
  },
  data() {
    return {
      isStageEnable: false,
      leftshow: true,
      leftloading: true,
      leftpa: false,
      periodList: [],
      periodBakList: [], // 临时打平任务数据
      projectDetail: {}, // 项目详情
      projectTaskDetail: {name: '', index: ''}, // 当前任务信息
      electiveText: '',
      compulsoryText: '',
      studyRecordsVisible: false, // 重复学习
      studyStatus: '', // 学习状态，1锁定
      studyStartTime: '', // 开始学习时间
      chapterName: '', // 当前阶段名称
      chapterScore: 0, // 当前阶段总学分
      chapterPoint: 0, // 当前阶段总积分
      chapterCerNum: 0, // 当前阶段证书数量
      chapterForNext: 0, // 下一阶段的索引
      isChapterForNext: true, // 是否已是最后一阶段
      chaptersCompleteVisible: false,
      periodType: 0, // 是否有阶段
      isLockTask: false, // 是否存在锁定任务
      isFullScreen: false, // 是否全屏
      dialogCourseTip: false,
      playThisTaskInfo: {}, // 临时存储当前播放内容
      activeId: '', //  任务选中
      activeComponentId: '',
      params: {}, // 组件传值
      hadPopupAutoMessage: false,
      bakTask: {},
      timeNum: 5,
      remark: '',
      componentName: '', //  组件名称 yxt-ulcd-sdk-examine
      autoPlay: false,
      isMultiple: false, // 是否是多班次任务
      isMultipleShow: false, // 是否展示多班次任务弹框
      transEnd: false,
      fixedTransEnd: false,
      leftHeight: '220px',
      chapterList: null,
      studyStandard: 0, // 完成标准 默认0 完成所有必修 1完成所有任务
      showPeriodNum: 0, // 阶段编号展示： 0 隐藏 1 展示
      gwnlCourseType: 1
    };
  },
  created() {
    if (this.gwnlCourse) {
      this.getAllocationType();
    }

    this.getOrgParameter();
    this.getPeriodNumShowStatus();
    this.getGlobalLangTrans();
    this.getProjectDetail();
  },
  beforeDestroy() {
    this.clearTimer();
  },
  computed: {
    taskId() {
      return getQueryString('taskId') || '';
    },
    gwnlCourse() {
      return getQueryString('gwnlCourse') === '1';
    },
    projectId() {
      return getQueryString('projectId') || '';
    },
    appCode() {
      return [7, 8, 9].includes(this.projectDetail.sourceType) || getQueryString('gwnlCourse') === '1' ? 'gwnl' : 'o2o';
    }
  },
  methods: {
    getAllocationType() {
      getAllocationType(this.projectId).then(({data})=>{
        this.gwnlCourseType = data.type;
      });
    },
    getPeriodNumShowStatus() {
      fetchPeriodNumShowStatus(this.projectId)
        .then(res => {
          this.showPeriodNum = res.showPeroidNum;
        });
    },
    getOrgParameter() {
      getOrgParameter('Is_hidden_stage_enabled', window.localStorage.orgId).then(res => {
        this.isStageEnable = res.value === '1';
      });
    },
    // 知识大纲回调
    onChapterDataLoaded(data, courseId) {
      if (this.projectTaskDetail && this.projectTaskDetail.targetId === courseId) {
        let datas = data.map(d => {
          d.actived = false;
          return d;
        });
        this.chapterList = {
          courseId,
          isShow: true,
          data: datas
        };
      } else {
        this._changeChapter(false);
      }
    },
    // 知识大纲回调 课件变化
    onKngChange(kngId, courseId) {
      if (this.chapterList && this.chapterList.courseId === courseId) {
        this.$set(
          this.chapterList,
          'kngId',
          kngId
        );
      }
    },
    // 知识大纲回调 课程组件开始初始化
    onChapterDataStart(courseId) {
      this._changeChapter(true);
    },
    // 知识大纲回调 课程大纲数据加载失败
    onChapterDataError(courseId) {
      this._changeChapter(false);
    },
    _changeChapter(isShow) {
      this.chapterList = {
        isShow: isShow
      };
    },
    _isKngCourse() {
      return this.chapterList && this.chapterList.isShow;
    },
    formateDate: dateFormat,
    //  收起大纲
    collapSingPeriod(item) {
      item.actived = !item.actived;
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        this.$fmessage.closeAll();
      }
    },
    onclickTask(task) {
      task.resourceUpdated = 0; // 手动更新【新】标签
      if (this.$refs['yxt-ulcd-sdk-examing']) {
        this.bakTask = task;
        this.$refs['yxt-ulcd-sdk-examing'].confirmLeave(this.setBackTask);
      } else if (this.$refs['yxt-ulcd-sdk-o2o-homework-task']) {
        this.bakTask = task;
        this.$refs['yxt-ulcd-sdk-o2o-homework-task'].confirmLeave(this.setBackTask);
      } else if (this.$refs['yxt-ulcd-sdk-discuss-task']) {
        this.bakTask = task;
        this.$refs['yxt-ulcd-sdk-discuss-task'].confirmLeave(this.setBackTask);
      } else {
        this.setTask(task);
      }
    },
    setBackTask(leave) {
      if (leave) {
        this.setTask(this.bakTask, false, true);
      }
    },
    async setTask(task, isFirst = false, isBack = false) {
      this.dealAeriodActived(task.id);
      this.autoPlay = !isFirst;
      // if (!isFirst) {
      //   let res = await this.checkProject();
      //   if (res) {
      //     this.$alert(this.$t('pc_o2o_msg_pleaserefreshproject1'), {
      //       confirmButtonText: this.$t('pc_o2o_btn_well'),
      //       center: true,
      //       callback: () => {
      //         window.location.reload();
      //       }
      //     });
      //     return;
      //   }
      // }
      this.clearTimer();
      this.hadPopupAutoMessage = false;
      this.projectTaskDetail = task;
      // if (task.type === 9) {
      //   this.leftshow = false;
      // }
      this.clearTaskStudy();
      this.activeId = task.id;
      this.activeComponentId = task.id;
      this.checkTaskStudy(task);
      const {name, params, query} = this.$route;
      if (task.repeatFlag === 1 && isBack) {
        this.$router.replace({name, params, query: {projectId: this.projectId, taskId: task.id}}).catch(err => err);
      } else {
        this.$router.replace({name, params, query: {...query, taskId: task.id}}).catch(err => err);
      }
    },
    async checkProject() {
      try {
        let res = await getProjectReleaseChange(this.projectId);
        return res.data;
      } catch (err) {
        return false;
      }
    },
    // 重置学习变量
    clearTaskStudy() {
      this.chapterList = null;
      this.activeId = '';
      this.activeComponentId = '';
      this.remark = '';
      this.studyStatus = 0;
      this.studyStartTime = '';
      this.componentName = '';
      this.isMultiple = false;
    },
    // 多班次
    multiClick(taskInfo) {
      this.activeComponentId = '';
      this.componentName = '';
      const task = JSON.parse(JSON.stringify(this.projectTaskDetail));
      task.id = taskInfo.subTaskId;
      task.targetId = taskInfo.targetId;
      task.trackId = taskInfo.trackId;
      task.parentId = this.projectTaskDetail.id;
      task.repeatFlagType = true;
      this.isMultipleShow = false;
      this.goTaskComponent(task, this.appCode);
      this.activeComponentId = taskInfo.subTaskId;
    },
    checkTaskStudy(task) {
      if (this.checkMultiple(task, this.projectTaskDetail)) {
        return;
      }
      let studyModel = this.periodList[0].studyModel;
      // todo跟后端沟通是否已这个为准
      // allowStudy 是否允许学习 0不允许 1允许
      // 手动锁定；不允许学习、并且不是【签到、直播、考试、线下面授、活动、作业】（这些任务未到解锁时间也允许学员访问任务详情页）
      if (task.locked === 1 || (task.allowStudy !== 1 && !([0, 1, 2, 6, 7, 17].includes(task.type) && studyModel === 2))) { // 锁定
        this.studyStartTime = (task.locked || task.barrier || studyModel !== 2) ? '' : task.unlockTime;
        this.studyStatus = 1;
        return;
      }
      // 不是自由学习模式、允许学习
      if (studyModel > 0 && task.allowStudy && (task.startTime && getTimeStamp(task.startTime) > Date.now())) {
        // 【签到、直播、考试、线下面授、活动、作业】，其他的不允许进入任务详情页
        if (![0, 1, 2, 6, 7, 17].includes(task.type)) {
          this.studyStatus = 2;
          this.studyStartTime = task.startTime;
          return ;
        }
      }
      // 0未发布 1草稿 2进行中 3结束 4归档 5删除 6暂停，和后端确认已过时 暂停和撤回都大于2
      // if (this.projectStatus === 3 || this.projectStatus === 4) {
      //   // todo  业务逻辑
      // }
      // task.category === 1 导师任务（导师任务---作业/评价）
      // 如果是4评价任务，则出现弹框，您所要评价的导师为【xx】，点击确认跳转页面，否则直接跳转
      //  taskOwner ==1  0普通带教 1是学员评价导师 2是学员自评
      this.goTaskComponent(task, this.appCode);
    },
    goTaskComponent(task, appCode) {
      let { type, id: tid, subType, targetId, trackId } = task;
      let pid = this.projectDetail.projectId;
      // resultRepeatFlag 2 重复学习 重复学习支持 课程课件 考试 鉴定
      const batchId = task.resultRepeatFlag === 2 ? (task.batchId === '0' || !task.batchId) ? '' : task.batchId : '';
      // 如果是taskType=cooperationTask则走协同作业的验证接口，如果不是 走普通校验的验证
      // 如果是多班次，调此api用的taskid为壳子taskid，而不是班次的
      let taskFlag = task.repeatFlagType ? task.parentId : tid;

      checkPermission(taskFlag).then((res) => {
        let { code, message, isFinish } = res;
        // let middlePageParams = {};
        if (code === 0) {
          // 后端记录学员最近学习的任务
          getRecentLea({ projectId: pid, taskId: tid }).then(() => {
            if (type === 0) {
              this.params = {
                id: targetId,
                appCode
              };
              this.componentName = 'yxt-ulcd-sdk-attend';
            }
            // 任务详情
            if (type === 1) { // 面授
              // 新面授
              if (subType === '1') {
                this.params = {
                  appCode: task.shareFlag === 2 ? 'f2f' : appCode, // 共享独立任务要走独立任务的bizId和appCode
                  tid,
                  trackId,
                  targetId,
                  bizId: task.shareFlag === 2 ? task.targetId : pid // 共享独立任务要走独立任务的bizId和appCode
                };
                this.componentName = 'yxt-ulcd-sdk-o2o-offline-task';

              } else { // 老面授
                goO2OPage(`face/${tid}`, { trackId, btid: batchId}, true);
                this.studyStatus = 1;
                this.remark = '新页面打开';
              }
            }
            if (type === 2) { // 考试
              getResitArrId(targetId, pid, batchId).then(examResult => {
                const examTargetId = examResult.resitFlag ? examResult.resitArrangeId : examResult.arrangeId;
                this.params = {
                  arrangeId: examTargetId,
                  masterId: pid,
                  trackId,
                  masterType: appCode === 'gwnl' ? 4 : 1, // （1-项目 2-知识 3-练习 4-人才发展）
                  btid: batchId
                };
                this.componentName = 'yxt-ulcd-sdk-examing';
              });
            }
            if ([3, 4, 5, 25, 28].indexOf(type) > -1) { // 调查 评价 投票 自定义调查 报名登记
              this.params = {
                type,
                tid,
                taskId: tid,
                projectId: pid,
                trackId,
                btid: batchId
              };
              this.componentName = 'yxt-ulcd-sdk-o2o-evaluation-task';
            }
            if (type === 6) { // 作业
              this.params = {
                tid,
                taskId: tid,
                trackId,
                btid: batchId
              };
              this.componentName = 'yxt-ulcd-sdk-o2o-homework-task';
            }
            if (type === 7) { // 活动
              this.componentName = 'yxt-ulcd-sdk-activity';
            }
            if (type === 14) { // 其他
              this.componentName = 'yxt-ulcd-sdk-outlink';
            }
            if ([8, 9, 10, 11, 12, 13, 16, 22].indexOf(type) > -1) { // 文档 课程 微课 视频 音频 scorm html 外链课
              // 任务状态完成
              sessionStorage.newsTag = Number(isFinish);
              this.params = {
                kngId: targetId,
                targetId: pid,
                taskId: tid,
                targetCode: appCode,
                trackId,
                originOrgId: task.orgId,
                btid: batchId,
                batchId
              };
              this.componentName = 'yxt-ulcd-sdk-course-page';
            }
            if (type === 17) { // live
              this.params = {
                trackId: trackId,
                taskId: tid,
                btid: batchId,
                roomId: targetId
              };
              this.componentName = 'yxt-ulcd-sdk-live-task';
            }
            if (type === 19) {
              // 练习
              this.params = {
                trackId: trackId,
                praId: targetId,
                btid: batchId
              };
              this.componentName = 'yxt-ulcd-sdk-practicing';
            }
            if (type === 21) { // 鉴定
              this.params = {
                pid,
                taskId: tid,
                tid,
                trackId: trackId,
                btid: batchId
              };
              this.componentName = 'yxt-ulcd-sdk-o2o-ident-task';
            }

            if (type === 23) { // 考核
              this.params = { trackId: trackId, targetId, pid};
              this.componentName = 'yxt-ulcd-sdk-examine';
            }

            if (type === 26) { // 话术训练
              this.params = {
                targetId,
                taskId: this.taskId,
                trackId
              };
              this.componentName = 'yxt-ulcd-sdk-verbal-sparring';
            }
            if (type === 27) { // 讨论
              this.params = {
                pid,
                boardId: targetId,
                taskId: tid,
                trackId,
                targetCode: appCode
              };
              this.componentName = 'yxt-ulcd-sdk-discuss-task';
            }

            if (type === 29) { // 演讲训练
              this.params = {
                targetId,
                taskId: this.taskId,
                trackId
              };
              this.componentName = 'yxt-ulcd-sdk-speech';
            }

            if (type === 30) { // 实战演练
              this.params = {
                targetId,
                taskId: this.taskId,
                trackId
              };
              this.componentName = 'yxt-ulcd-sdk-drill';
            }

            this.setDocumentTitle(task.name);
          });
        } else {
          let msg = getErrorMsg(code, message, this.projectDetail.projectStatus);
          this.studyStatus = 1;
          this.remark = msg;
        }
      }).catch(e => {
        this.$message.error(e.message);
        this.studyStatus = 1;
        this.remark = e.message;
      });
    },
    setDocumentTitle(title, pageCode = '') {
      try {
        TitleUtil.setTitleByPageCode(pageCode, title);
      } catch (e) {}
    },
    // 多班次检查
    checkMultiple(task) {
      // 多班次任务，进入班次列表
      if (task.repeatFlag === 1) {
        // if (task.status === 5) return true;
        this.bakTask = task;
        this.componentName = 'yxt-ulcd-sdk-multi-task';
        this.isMultiple = true;
        return true;
      } else {
        return false;
      }
    },
    goBack(leave) {
      if (!leave) {
        return;
      }
      this.goDetail();
      // if (window.history.length > 1) {
      //   window.history.back(-1);
      // } else if (window.opener) {
      //   window.location.href = opener.location.href;
      // } else {
      //   this.goDetail();
      // }
    },
    goDetail() {
      if (this.gwnlCourse && window.history.length > 1) {
        return window.history.go(-1);
      }
      if (this.appCode === 'o2o') {
        window.location.href = `${window.location.origin}/o2o/#/project/detail/${this.projectDetail.projectId}`;
      } else {
        window.location.href = `${window.location.origin}/gwnl/#/web/detail/${this.projectDetail.projectId}/trans`;

      }
    },
    setConfirmgoBack() {
      if (this.$refs['yxt-ulcd-sdk-examing']) {
        this.$refs['yxt-ulcd-sdk-examing'].confirmLeave(this.goBack);
      } else if (this.$refs['yxt-ulcd-sdk-o2o-homework-task']) {
        this.$refs['yxt-ulcd-sdk-o2o-homework-task'].confirmLeave(this.goBack);
      } else {
        this.goBack(true);
      }
    },
    checkPreviousDisable() {
      return this.activeId === this.firstId();
    },
    checkNextDisable() {
      return this.activeId === this.lastId();
    },
    firstId() {
      return this.periodBakList && this.periodBakList.length > 0 ? this.periodBakList[0].id : '';
    },
    lastId() {
      return this.periodBakList && this.periodBakList.length > 0 ? this.periodBakList[this.periodBakList.length - 1].id : '';
    },
    // 下一节
    playNext() {
      function _playThisTask(that) {
        let thisIndex = that.periodBakList.findIndex((value)=>value.id === that.activeId);
        if (thisIndex >= that.periodBakList.length - 1) {
          that.$fmessage({
            message: that.$t('pc_ulcdsdk_lbl_theendone'/* 当前已是最后一个 */),
            type: 'warning'
          });
          return;
        }
        that.playThisTaskInfo = that.periodBakList[thisIndex + 1];
        that.playThisTask(true, 2);
      }
      if (this._isKngCourse()) {
        this.nextKng().then(res => {
        }).catch(() => {
          _playThisTask(this);
        });
      } else {
        _playThisTask(this);
      }
    },
    // 上一节
    playPrevious() {
      function _playThisTask(that) {
        let thisIndex = that.periodBakList.findIndex((value)=>value.id === that.activeId);
        if (thisIndex <= 0) {
          that.$fmessage({
            message: that.$t('pc_ulcdsdk_lbl_thefirstone'/* 当前已是第一个 */),
            type: 'warning'
          });
          return;
        }
        that.playThisTaskInfo = that.periodBakList[thisIndex - 1];
        that.playThisTask(true, 1);
      }
      if (this._isKngCourse()) {
        this.prevKng().then(res => {

        }).catch(() => {
          _playThisTask(this);
        });
      } else {
        _playThisTask(this);
      }
    },
    playThisTask(ischeck, num = 0) {
      if (this.projectTaskDetail.type === 9 && ischeck) {
        this.dialogCourseTip = num;
        return;
      } else {
        this.dialogCourseTip = 0;
      }
      this.onclickTask(this.playThisTaskInfo);
    },
    // 学习下一阶段
    playNextChapter() {
      this.chaptersCompleteVisible = false;
      this.onclickTask(this.periodBakList[this.chapterForNext + 1]);
    },
    // 是否全屏
    fullScreen(isFullScreen) {
      this.isFullScreen = isFullScreen;
    },
    afterLeave() {
      this.transEnd = true;
    },
    beforeEnter() {
      this.transEnd = false;
    },
    leftEnter() {
      this.fixedTransEnd = true;
      if (this.transEnd) {
        this.leftshow = true;
        this.leftpa = true;
      }
    },
    leftLeave() {
      if (!this.leftpa) return;
      this.leftshow = false;
      this.leftpa = false;
      if (this.transEnd) {
        this.fixedTransEnd = false;
      }
    },
    fixedOutline() {
      // if (this.projectTaskDetail.type === 9) {
      //   return;
      // }
      this.leftshow = true;
      this.leftpa = false;
      this.fixedTransEnd = false;
    },
    getGlobalLangTrans() {
      getGlobalLangTrans(LANGKEY.O2O_OPTIONAL).then(res => {
        this.electiveText = (res && res[getLanguage()]) || this.$t('pc_o2o_lbl_elective');
      }).catch(() => {
        this.electiveText = this.$t('pc_o2o_lbl_elective');
      });
      getGlobalLangTrans(LANGKEY.O2O_COMPULSORY).then(res => {
        this.compulsoryText = (res && res[getLanguage()]) || this.$t('pc_o2o_lbl_obligatory');
      }).catch(() => {
        this.compulsoryText = this.$t('pc_o2o_lbl_obligatory');
      });
    },
    showMsg(isAutoPlay, hadComplete = false) {
      if (isAutoPlay && !this.checkNextDisable()) {
        if (this.hadPopupAutoMessage) return;
        this.hadPopupAutoMessage = true;
        this.timeNum = 5;
        const h = this.$createElement;
        const messageContent = [
          h('span', {class: 'ulcd_o2ospantimewarning'}, this.timeNum),
          h('span', {class: 'ml4'}, this.$t('pc_ulcdsdk_btn_skiptothenexttask')),
          h('span', {
            class: 'ml32 color-primary-6 hand',
            on: {
              click: this.clearTimer
            }
          }, this.$t('pc_ulcdsdk_lbl_canceljump'/* 取消跳转 */))
        ];
        if (!hadComplete) {
          messageContent.unshift(h('span', {class: 'mr2'}, this.$t('pc_ulcdsdk_lbl_completedcurrenttasko2o')));
        }
        this.$fmessage({
          message:
          h('p', null, messageContent),
          showClose: false,
          duration: 5000,
          type: 'success'
        });

        this.timer = setInterval(() => {
          --this.timeNum;
          if (this.timeNum === 0) {
            this.playNext();
            this.clearTimer();
          } else {
            if (document.querySelector('.ulcd_o2ospantimewarning')) {
              document.querySelector('.ulcd_o2ospantimewarning').innerHTML = this.timeNum;
            }
          }
        }, 1000);
      } else {
        this.$fmessage({
          message: this.$t('pc_ulcdsdk_lbl_havecompletedthecurrenttask'),
          type: 'success'
        });
      }
    },
    // 	学习状态，1学习中，2已完成
    async updateProgress(status, isAutoPlay = false) {
      if (status === 2) {status = 3;} // 回调时候默认2完成，任务中3是完成
      if (this.projectTaskDetail.status > status || status < 2) {
        return;
      }

      const checkBackType = [27].includes(this.projectTaskDetail.type); // 支持回退状态的

      if ((this.projectTaskDetail.status !== status && status === 3) || checkBackType) {
        if ([6, 21, 0, 1, 3, 4, 5, 25, 28].indexOf(this.projectTaskDetail.type) > -1 || this.isMultiple) { // 检查是否完成
          await sleep(delayTimeOut);
          let res = await postTaskCompleted(this.projectId, [this.projectTaskDetail.id]);
          if (!(res && res.length && res[0].condition)) {
            this.getSurveyProgress();
            return;
          }
        }
        this.getUnlockTask();

        if (checkBackType && status === 4) {
          this.projectTaskDetail.status = 2;
        } else {
          this.showPeriodComplete();
          if (this.projectTaskDetail.id !== this.lastId && !this.chaptersCompleteVisible) {this.showMsg(isAutoPlay);}
          this.projectTaskDetail.status = status;
        }
        this.dealPeriodStatus();
        await sleep(delayTimeOut);
        this.$refs.o2oprogress.getFinishedProgress();
      } else {
        this.getSurveyProgress();
        this.dealPeriodStatus();
        return;
      }
    },
    // 合并评价进度回刷
    async getSurveyProgress() {
      if (this.projectDetail.allowSurveyMerging && [3, 4, 5, 25, 28].includes(this.projectTaskDetail.type)) { // 合并评价
        const linSurveyBak = this.periodBakList.filter(item => ([3, 4, 5, 25, 28].includes(item.type) && item.status < 3));
        if (linSurveyBak) {
          const listids = linSurveyBak.map(e => e.id);
          await sleep(delayTimeOut);
          let res = await postTaskCompleted(this.projectId, listids);
          if (res && res.length) {
            const taskids = res.filter(item =>item.condition === 1).map(e => e.id);
            if (taskids && taskids.length) {
              this.periodBakList.map(value => {
                if (taskids.includes(value.id)) {
                  value.status = 3;
                }
                return value;
              });
              this.$refs.o2oprogress.getFinishedProgress();
            }
          }
        }
      }
    },
    dealPeriodStatus() {
      if (this.periodType) {
        const linDetailBakList = this.periodBakList.filter(item => (item.periodId === this.projectTaskDetail.periodId));
        const linDetailBak = this.studyStandard !== 0 ? linDetailBakList.filter(item => (item.status !== 3)) : linDetailBakList.filter(item => (item.status !== 3 && item.userRequired === 1));
        let periodInfo = this.periodList.find(item => (item.id === this.projectTaskDetail.periodId));
        if (linDetailBak && linDetailBak.length === 0) {
          periodInfo.periodStatus = 1;
        } else {
          periodInfo.periodStatus = 0;
        }
      }
    },
    showPeriodComplete() {
      if (this.periodType) {
        const linDetailBakList = this.periodBakList.filter(item => (item.periodId === this.projectTaskDetail.periodId));
        console.log(linDetailBakList, ' linDetailBakList');
        const linDetailBak = linDetailBakList.filter(item => (item.status !== 3));
        if (linDetailBak && linDetailBak.length === 1) {
          let _lastID = '';
          linDetailBakList.forEach((item, index)=>{
            if (index === linDetailBakList.length - 1) {
              _lastID = item.id;
            }
          }) ;
          this.chapterForNext = this.periodBakList.findIndex((value)=>value.id === _lastID);
          this.isChapterForNext = !(this.chapterForNext === this.periodBakList.length - 1);
          this.chapterName = this.projectTaskDetail.periodName;
          this.getPeriodScore(this.projectTaskDetail.periodId);
          this.chaptersCompleteVisible = true;
        }
      } else {
      }
    },
    // 查询用户在这个阶段下已经获得的学分积分
    getPeriodScore(periodId) {
      this.chapterScore = 0;
      this.chapterPoint = 0;
      getPeriodScore(this.projectId, periodId).then(res => {
        this.chapterScore = parseFloat(res.credit.toFixed(2)); // 精度丢失问题
        this.chapterPoint = parseFloat(res.integral.toFixed(2));
      }).catch(e => {
      });
    },
    getProjectDetail() {
      getProjectBaseInfo(this.projectId).then(res => {
        this.projectDetail = res;
        this.getTreeDetaul();
      }).catch(e => {
        this.$message.error(e.message);
        this.$router.replace({
          name: 'catch',
          query: {
            type: 2,
            subtype: getQueryString('canAudit') || ''
          }
        });
      });
    },
    getTreeDetaul() {
      getStuDetail(this.projectId).then(res => {
        this.leftloading = false;
        this.periodType = res.periodType;
        this.studyStandard = res.studyStandard;
        this.periodList = res.datas.map((period, index) => {
          period.actived = false;
          period.studentTaskBeans.map(sub => {
            // 任务组
            if (sub.type === 15) {
              sub.actived = false;
              this.periodBakList.push(...sub.groupCourse.map(value => {
                this.getLockStatus(value);
                this.dealTaskName(value);
                value.locked = period.locked;
                value.periodId = period.id;
                value.periodName = period.name;
                return value;
              }));
            } else {
              this.getLockStatus(sub);
              this.dealTaskName(sub);
              sub.locked = period.locked;
              sub.periodName = period.name;
              sub.periodId = period.id;
              this.periodBakList.push(sub);
            }
            return sub;
          });
          return period;
        });
        const linDetailBak = this.periodBakList.find(item => (item.id === this.taskId));
        if (linDetailBak) {
          this.setTask(linDetailBak, true);
        } else {
          this.setTask(this.periodBakList[0], true);
        }
      }).catch(e => {
        this.leftloading = false;
        this.$message.error(e.message);
        this.$router.replace({
          name: 'catch',
          query: {
            type: ['apis.o2o.project.status.invalid', 'apis.o2o.project.notfound'].includes(e.key) ? 1 : 2,
            subtype: getQueryString('canAudit') || ''
          }
        });
      });
    },
    dealTaskName(task) {
      if (task.type === 4 && (task.taskOwner === 1 || task.taskOwner === 2)) {
        task.name = (task.taskOwner === 1 ? this.$t('pc_o2o_lbl_commentteacher') + '-' + task.ojtTeacherName : this.$t('pc_o2o_lbl_student_assess')) + '-' + task.name;
      }
    },
    // 处理默认选择状态
    dealAeriodActived(taskId) {
      this.periodList.map(period => {
        period.studentTaskBeans.map(sub => {
          // 任务组
          if (sub.type === 15) {
            sub.groupCourse.map(value => {
              if (taskId === value.id) {
                period.actived = true;
                sub.actived = true;
              }
              return value;
            });
          } else {
            if (taskId === sub.id) {
              period.actived = true;
            }
          }
          return sub;
        });
        return period;
      });
    },
    getLockStatus(task) {
      if (task.status === 1) {
        this.isLockTask = true;
      }
    },
    // 解除锁定状态
    getUnlockTask() {
      if (!this.isLockTask) {
        return;
      }
      this.getAllTaskProgress();
    },
    async getAllTaskProgress() {
      await sleep(delayTimeOut);
      let res = await getStuDetail(this.projectId);
      res.datas.map(period => {
        period.studentTaskBeans.map(sub => {
          // 任务组
          if (sub.type === 15) {
            sub.groupCourse.map(gc => {
              this.periodBakList.map(value => {
                if (value.id === gc.id && value.status < 3) {
                  value.status = gc.status;
                  value.allowStudy = gc.allowStudy;
                  value.startTime = gc.startTime;
                }
                return value;
              });
            });
          } else {
            this.periodBakList.map(value => {
              if (value.id === sub.id && value.status < 3) {
                value.status = sub.status;
                value.allowStudy = sub.allowStudy;
                value.startTime = sub.startTime;
              }
              return value;
            });
          }
        });
      });

    },
    needCloseCatalog() {
      this.leftshow = false;
    },
    getPeriodCodeName(...argu) {
      return getPeriodCodeName.call(this, ...argu);
    },
    getColon() {
      if (getLanguage() === 'zh') {
        return '：';
      } else if (getLanguage() === 'en') {
        return ' : ';
      }
      return ':';
    }
  }
};
</script>
