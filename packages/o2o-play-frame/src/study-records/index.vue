<template>
  <yxtf-dialog
    title="学习记录"
    width="800px"
    :visible.sync="visible"
  >
    <div v-loading="loading">
      <components
        :is="componentName"
        is-front
        :is-ojt="ojtRecord"
        :table-data="tableData"
        :info="info"
        @openIdentifyPage="openIdentifyPage"
        @openExamPage="openExamPage"
      />
      <yxtf-pagination
        class="text-right mt16"
        :current-page="query.current"
        :page-size="query.limit"
        layout="total, prev, pager, next"
        :total="count"
        simple-total
        @current-change="handleSearch"
      />
    </div>
  </yxtf-dialog>
</template>

<script>
import { getTaskType } from 'packages/_utils/core/utils.js';
import examTable from './exam-table.vue';
import identifyTable from './identify-table.vue';
import knowledgeTable from './knowledge-table.vue';
import { getTaskInfo, getStuTaskRepeatRecoreds } from '../../service';
export default {
  components: {
    examTable,
    knowledgeTable,
    identifyTable
  },
  watch: {
    visible: {
      handler: function(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  props: {
    taskDetail: {
      type: Array,
      default: []
    },
    visible: {
      type: Boolean,
      default: false
    },
    projectId: {
      type: String
    }
  },
  data() {
    return {
      tableData: [],
      loading: false,
      query: {
        limit: 10,
        current: 1,
        offset: 0,
        userId: null
      },
      count: 0,
      info: {}
    };
  },
  computed: {
    taskType() {
      return this.taskDetail.type;
    },
    taskId() {
      return this.taskDetail.id;
    },
    trackId() {
      return this.taskDetail.trackId;
    },
    componentName() {
      let name = '';
      console.log(this.taskType);
      const task = getTaskType(Number(this.taskType) || 0);
      if (task.code === 'exam') {
        name = 'examTable';
      } else if (task.code === 'evaluation') {
        name = 'identifyTable';
      } else if (['doc', 'course', 'weike', 'video', 'audio', 'scorm', 'html', 'link'].includes(task.code)) {
        name = 'knowledgeTable';
      } else {
        name = '';
      }
      return name;
    }
  },
  created() {
    this.fetchTaskInfo();
    this.fetchTableData();
  },
  methods: {
    fetchTableData() {
      this.loading = true;
      this.query.offset = (this.query.current - 1) * this.query.limit;
      this.query.userId = this.userId;
      getStuTaskRepeatRecoreds(this.taskId, this.query).then(res => {
        const { datas, paging } = res;
        this.tableData = datas;
        this.count = paging.count;
      }).finally(() => {
        this.loading = false;
      });
    },
    openIdentifyPage(data) {
      // const { projectId: pid, taskId: tid, trackId, taskType } = this
      // const name = this.ojtRecord ? 'ojtTaskEvaluationDetail' : 'identification'
      // const params = this.ojtRecord ? {} : { tid }
      // const btid = (data.batchId === '0' || !data.batchId) ? '' : data.batchId
      // let query = { origin: 'studyReocrd', btid }
      // query = this.ojtRecord ? { ...this.$route.query, targetId: this.info.targetId, userId: this.info.userId, projectStatus: this.info.projectStatus, ...query } : { pid, trackId, from: 'studyRecord', taskType, ...query }
      // this.$router.push({ name, params, query })
    },
    openExamPage(ueId) {
      // let userUxamUrl = ''
      // if (this.ojtRecord) {
      //   userUxamUrl = `/ote/#/examresultpaperp?ueId=${ueId}`
      // } else {
      //   userUxamUrl = `/ote/#/myexamresultpaper?ueId=${ueId}`
      // }
      // // 跳转到用户答卷页面（查看结果成绩等无限制）
      // window.open(userUxamUrl)
    },
    handleSearch(val) {
      this.query.current = val;
      this.fetchTableData();
    },
    fetchTaskInfo() {
      getTaskInfo(this.projectId, this.taskId, this.userId).then(res => {
        this.info = res;
      });
    }
  }
};
</script>
<style lang='scss'>
.o2oPlayFrame-record-table .cell {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

</style>

