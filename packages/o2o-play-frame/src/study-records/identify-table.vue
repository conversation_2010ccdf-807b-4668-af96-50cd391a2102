<template>
  <div class="o2oPlayFrame-repeat-study">
    <div
      v-for="(data ,k) in tableData"
      :key="k"
    >
      <div class="yxtulcdsdk-flex-center">
        <div class="flex-1">
          <div class="weight-bold font-size-16 lh24">{{ $t('pc_o2o_lbl_the_which_counts', [data.orderIndex] /* 第几次 */ ) }}</div>
          <div class="font-size-14 mt8 lh22 ">
            <span class="mr32">提交时间：{{ data.submitTime | date('yyyy-MM-dd HH:mm', '--') }}</span>
            <span class="mr32">鉴定状态：<span>{{ statusFormat(identifyStatus,data.appraisalStatus) }}</span></span>
            <span class="mr32">成绩：{{ (data.score !== null ? data.score : '--') + (data.score !== null ? (info.appraisalType === 1 ? $t('pc_o2o_lbl_score') : '%') : '') }}</span>
          </div>
        </div>
        <div>
          <span :class="['dot' + data.status, 'dot']"></span>
          <span :class="{ 'mr16': data.handCompleted !== 1 }">{{ statusFormat(orginStatusOptions,data.status) }}</span>
          <yxtf-tooltip
            v-if="data.handCompleted === 1"
            :content="$t('pc_o2o_handleCompleted_tip'/* 管理员手动标记完成 */)"
            placement="top"
          >
            <yxt-svg
              class="color-gray-6 v-mid hand hover-primary-6"
              width="16px"
              height="16px"
              icon-class="info-cirlce-o"
            />
          </yxtf-tooltip>
        </div>
        <div>
          <yxtf-button
            class="ml16"
            plain
            :disabled="![2,3].includes(data.appraisalStatus)"
            @click="viewComments(data)"
          >
            {{ $t("pc_o2o_lbl_view" /* 查看 */) }}
          </yxtf-button></div>
      </div>
      <yxtf-divider />
    </div>

  </div>
</template>
<script>
export default {
  props: {
    tableHeight: Number,
    tableData: Array,
    isFront: {
      type: Boolean,
      default: false
    },
    info: Object
  },
  data() {
    return {
    };
  },
  methods: {
    statusFormat(originList, id) {
      const list = id !== null ? originList.filter(item => item.id === id) : [];
      return list && list.length > 0 ? list[0].label : '--';
    },
    // 鉴定或查看
    viewComments(data) {
      if (this.isFront) {
        this.$emit('openIdentifyPage', data);
      } else {
        this.$refs['identify-review'].init({ ...data, ...{ userId: this.info.userId } }, { hideEditButton: true });
      }
    }
  },
  computed: {
    orginStatusOptions() {
      return [
        // 0未完成 1进行中 2已完成 3延期完成（我们这边不显示进行中，所以有俩未完成）
        { label: this.$t('pc_o2o_lbl_uncompleted' /* 未完成 */), id: 0 },
        { label: this.$t('pc_o2o_lbl_uncompleted' /* 未完成 */), id: 1 },
        { label: this.$t('pc_o2o_lbl_finished' /* 已完成 */), id: 2 },
        { label: this.$t('pc_o2o_lbl_delaycomplete' /* 延期完成 */), id: 3 }
      ];
    },
    identifyStatus() {
      return [
        // 0未申请 1待鉴定 2已合格 3 未合格
        { label: this.$t('pc_o2o_lbl_not_applied' /* 未申请 */), id: 0 },
        { label: this.$t('pc_o2o_lbl_to_be_identified' /* 待鉴定 */), id: 1 },
        { label: this.$t('pc_o2o_lbl_qualified' /* 已合格 */), id: 2 },
        { label: this.$t('pc_o2o_lbl_notquaified' /* 未合格 */), id: 3 }
      ];
    }
  }
};
</script>

