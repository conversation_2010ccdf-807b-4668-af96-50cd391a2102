<template>
  <div class="o2oPlayFrame-repeat-study">
    <div
      v-for="(data ,k) in tableData"
      :key="k"
    >
      <div class="yxtulcdsdk-flex-center">
        <div class="flex-1">
          <div class="weight-bold font-size-16 lh24">{{ $t('pc_o2o_lbl_the_which_counts', [data.orderIndex] /* 第几次 */ ) }}</div>
          <div class="font-size-14 mt8 lh22 ">
            完成时间： {{ data.completedTime | date('yyyy-MM-dd HH:mm', '--') }}
          </div>
        </div>
        <div>
          <span
            class="dot"
            :class="getStatus(data.status).dot"
          ></span> <span :class="{ 'mr16': data.handCompleted !== 1 }">{{ getStatus(data.status).str }}</span>
          <yxtf-tooltip
            v-if="data.handCompleted === 1"
            :content="$t('pc_o2o_handleCompleted_tip'/* 管理员手动标记完成 */)"
            placement="top"
          >
            <yxt-svg
              class="color-gray-6 v-mid hand hover-primary-6"
              width="16px"
              height="16px"
              icon-class="info-cirlce-o"
            />
          </yxtf-tooltip>
        </div>
      </div>
      <yxtf-divider />
    </div>

  </div>
</template>
<script>
export default {
  props: {
    tableHeight: Number,
    tableData: Array,
    isFront: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getStatus(status) {
      const options = {
        str: '',
        dot: ''
      };
      switch (status) {
        case 2:
          options.str = this.$t('pc_o2o_lbl_finished' /* 已完成 */);
          options.dot = 'dot-complete';
          break;
        case 3:
          options.str = this.$t('pc_o2o_lbl_delaycomplete' /* 延期完成 */);
          options.dot = 'dot-delay';
          break;
        default:
          options.str = this.$t('pc_o2o_lbl_uncompleted' /* 未完成 */);
          options.dot = ' dot-uncomplete';
          break;
      }
      return options;
    }
  }
};
</script>
