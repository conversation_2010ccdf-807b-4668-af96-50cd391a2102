<template>
  <div class="o2oPlayFrame-repeat-study">
    <div
      v-for="(data ,k) in tableData"
      :key="k"
    >
      <div class="yxtulcdsdk-flex-center">
        <div class="flex-1">
          <div class="weight-bold font-size-16 lh24">{{ $t('pc_o2o_lbl_the_which_counts', [data.orderIndex] /* 第几次 */ ) }}</div>
          <div class="font-size-14 mt8 lh22 ">
            <span class="mr32">开考时间：{{ data.ueStartTime | date('yyyy-MM-dd HH:mm', '--') }}</span>
            <span class="mr32">交卷时间：{{ data.submitTime | date('yyyy-MM-dd HH:mm', '--') }}</span>
            <span class="mr32">成绩： <template v-if="data.status === 2 || data.status === 3">
              <span>{{ data.bestScore }}</span>
            </template>
              <span v-else>--</span></span>
          </div>
        </div>
        <div>
          <yxtf-button
            class="ml16"
            plain
            :disabled="data.status !=2 || !data.submitTime"
            @click="viewPaper(data)"
          >
            {{ $t('pc_o2o_btn_viewpaper') }}
          </yxtf-button></div>
      </div>
      <yxtf-divider />
    </div>

  </div>
</template>
<script>
export default {
  props: {
    tableHeight: Number,
    tableData: Array,
    isFront: {
      type: Boolean,
      default: false
    },
    info: Object
  },
  data() {
    return {
    };
  },
  methods: {
    // 查看答卷
    viewPaper({ ueId }) {
      this.$emit('openExamPage', ueId);
    }
  }
};
</script>

