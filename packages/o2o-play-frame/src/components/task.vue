<template>
  <div
    class="mt8 hand mr8"
    :class="activeId===groupDetail.id?'task-content-active':'task-content'"
  >
    <div :class="[{'pl68':periodType === 1&&taskType===2},{'pl48':periodType === 1&&taskType!==2},,{'pl24':periodType !== 1&&taskType!==2},{'pl60':periodType !== 1&&taskType===2}]" class="hand pt8 pb8">
      <div class="layout-flex flex-space-between" @click="$emit('onclickTask',groupDetail)">
        <div class="flex-1"> <yxtf-tooltip
          class="item"
          :open-filter="true"
          :content="groupDetail.name"
          :open-delay="2000"
          placement="top"
        >
          <span :class="activeId===groupDetail.id&&!isShowChapter?'task-content-color-active':''" class="ulcdsdk-ellipsis-2 group-title font-w400 ulcdsdk-break">
            <span v-if="groupDetail.resourceUpdated" class="text-f52">{{ $t('pc_o2o_lbl_new_tag').d('【新】') }}</span>
            {{ groupDetail.name }}
          </span>
        </yxtf-tooltip>
        </div>
        <yxt-ulcd-sdk-svg
          class="mr8 mt2 ml14"
          width="16px"
          height="16px"
          :icon-class="getO2OStatus(groupDetail.status)"
        />
      </div>
      <div class="text-8c mt4 font-size-12 layout-flex lh20" @click="$emit('onclickTask',groupDetail)">
        <yxtf-tooltip
          class="item"
          :open-filter="true"
          :open-delay="2000"
          :content="getTaskTypeName(groupDetail.typeName, groupDetail.type)"
          placement="top"
        >
          <div class="mr8 task-content-tag ellipsis">
            {{ getTaskTypeName(groupDetail.typeName, groupDetail.type) }}
          </div></yxtf-tooltip>
        <template v-if="(groupDetail.userRequired !== 0 && compulsoryTextShown) || (groupDetail.userRequired === 0 && electiveTextShown)">
          |<yxtf-tooltip
            class="item"
            :open-filter="true"
            :open-delay="2000"
            :content="groupDetail.userRequired === 0 ? electiveText : compulsoryText "
            placement="top"
          ><div class="ml8 task-content-tag ellipsis mr8">{{ groupDetail.userRequired === 0 ? electiveText : compulsoryText }}</div></yxtf-tooltip>
        </template>
        <div v-if="groupDetail.category" class="layout-flex">|<div class="ml8 task-content-tag ellipsis">{{ $t('pc_o2o_lbl_tutor'/* 带教 */) }}</div></div>
        <div v-if="groupDetail.barrier" class="layout-flex">|<div class="ml8 task-content-tag ellipsis">{{ $t('pc_o2o_tmap_checkpoint'/* 关卡 */) }}</div></div>
      </div>
      <template v-if="activeId===groupDetail.id">
        <div v-if="(groupDetail.startTime||groupDetail.endTime)&&[8, 9, 10, 11, 12, 13, 16, 22].indexOf(groupDetail.type) > -1" class="text-8c mt4 font-size-12 lh20">
          {{ groupDetail.startTime | date('yyyy-MM-dd HH:mm', '--') }} {{ $t('pc_o2o_lbl_to'/* 至 */) }} {{ groupDetail.endTime | date('yyyy-MM-dd HH:mm', '--') }}
        </div>
        <div v-if="groupDetail.integral&&groupDetail.credit" class="text-8c mt4 font-size-12 lh20">
          {{ $t('pc_ulcdsdk_lbl_canearnpointscredits', [groupDetail.integral,groupDetail.credit]) }}
        </div>
        <div v-else-if="groupDetail.integral" class="text-8c mt4 font-size-12 lh20">
          {{ $t('pc_ulcdsdk_lbl_canearnpoints', [groupDetail.integral]) }}
        </div>
        <div v-else-if="groupDetail.credit" class="text-8c mt4 font-size-12 lh20">
          {{ $t('pc_ulcdsdk_lbl_canearncredits', [groupDetail.credit]) }}
        </div>
      </template>
      <courseChapter v-if="isShowChapter" :chapter-list="chapterList" @onclickKng="onclickKng" />
    </div>
  </div>
</template>

<script>
import Svg from 'packages/_components/svg.vue';
import courseChapter from 'yxt-ulcd-sdk/packages/o2o-play-frame/src/components/courseChapter.vue';
import { getTaskTypeName } from 'packages/_utils/core/utils.js';
export default {
  components: {
    [Svg.name]: Svg,
    courseChapter
  },
  props: {
    projectDetailConfig: {
      type: Object,
      default: () => ({})
    },
    chapterList: {
      type: Object,
      default: () => ({})
    },
    groupDetail: {
      type: Object,
      default: {}
    },
    activeId: {
      type: String
    },
    compulsoryText: {
      type: String
    },
    electiveText: {
      type: String
    },
    periodType: {
      type: Number
    },
    taskType: {
      type: Number
    }
  },
  computed: {
    isShowChapter() {
      return this.activeId === this.groupDetail.id && this.groupDetail.type === 9 && this.chapterList && this.chapterList.isShow;
    },
    compulsoryTextShown() {
      const {showTaskTagCompulsory} = this.projectDetailConfig || {};
      return showTaskTagCompulsory === 1;
    },
    electiveTextShown() {
      const {showTaskTagOptional} = this.projectDetailConfig || {};
      return showTaskTagOptional === 1;
    }
  },
  methods: {
    getO2OStatus(status) {
      let svgname = 'o2onotstarted';
      switch (status) {
        case 1:
          svgname = 'o2olock';
          break;
        case 2:
          svgname = 'o2oprogress';
          break;
        case 3:
          svgname = 'o2ocompleted';
          break;
      }
      return svgname;
    },
    getTaskTypeName(typeName, type) {
      return getTaskTypeName(typeName, type);
    },
    onclickKng(child) {
      this.$emit('onclickKng', child);
    }
  }
};
</script>

