
<template>
  <div v-if="info.show" class="yxtulcdsdk-o2obreadcrumb mt4">
    <yxtf-breadcrumb separator-class="yxtf-icon-arrow-right">
      <yxtf-breadcrumb-item
        v-for="(item,index) in info.list"
        :key="index"
        :is-link="!!item.eventName"
        @click.native="clickEventName(item.eventName)"
      >
        {{ item.name }}
      </yxtf-breadcrumb-item>
    </yxtf-breadcrumb>
  </div>
</template>

<script>
export default {
  name: 'O2oBreadcrumb',
  props: {
    isMultiple: {
      type: Boolean,
      default: false
    },
    componentName: {
      type: String,
      default: ''
    }
  },
  watch: {
    componentName(val) {
      this.info.show = false;
      this.info.list = [];
      if (this.isMultiple && this.componentName && this.componentName !== 'yxt-ulcd-sdk-multi-task') {
        this.info.show = true;
        this.info.list.push({name: this.$t('pc_o2o_lbl_multitasklist'), eventName: 'setBackTask'});
        let taskName = val === 'yxt-ulcd-sdk-examing' ? this.$t('pc_ulcdsdk_lbl_exam_task') : (val === 'yxt-ulcd-sdk-o2o-offline-task' ? this.$t('pc_ulcdsdk_lbl_offline_task') : this.$t('pc_ulcdsdk_lbl_live_task'));
        this.info.list.push({name: taskName});
      }
    }
  },
  data() {
    return {
      info: {
        show: false,
        list: []
      }
    };
  },
  created() {
    this.$root.$on('SETBREADCRUMBUPDATE', this.setBreadcrumbUpdate);
  },
  beforeDestroy() {
    this.$root.$off('SETBREADCRUMBUPDATE', this.setBreadcrumbUpdate);
  },
  methods: {
    setBreadcrumbUpdate(data) {
      if (data) {
        this.info = data;
        if (this.isMultiple && this.componentName && this.componentName !== 'yxt-ulcd-sdk-multi-task') {
          this.info.show = true;
          this.info.list.unshift({name: this.$t('pc_o2o_lbl_multitasklist'), eventName: 'setBackTask'});
        }
      }
    },
    clickEventName(eventName) {
      if (!eventName) {
        return;
      }
      if (eventName === 'setBackTask') {
        this.$emit('setBackTask', true);
      } else {
        this.$root.$emit(eventName);
      }
    }
  }
};
</script>
<style lang="scss">
.yxtulcdsdk-o2obreadcrumb {
  .yxtf-breadcrumb {
    height: 20px;
    font-size: 12px;
    line-height: 20px;
  }

  .is-link:hover {
    color: #262626 !important;
  }

  .yxtf-breadcrumb__inner {
    color: #8c8c8c !important;
  }

  .yxtf-breadcrumb__separator[class*=icon] {
    margin: 0 4px;
    font-weight: 400;
  }
}

</style>

