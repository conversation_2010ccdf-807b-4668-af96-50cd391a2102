<template>
  <div class="score-content">
    <div class="pb24 font-size-12 layout-flex layout-align-center lh20">
      <yxtf-progress :percentage="progressStatistics.completeProcessRate || 0" line-width="120px" :show-text="false" />
      <span class="text-8c ml12 font-size-12 lh20">{{ progressStatistics.completeProcessRate || 0 }}%（{{ finishedProgressStr }}）</span>
      <yxt-tooltip
        v-if="taskContent"
        :content="taskContent"
        placement="top"
        effect="light"
      >
        <yxt-svg
          class="color-gray-6 hand hover-primary-6 ml4 v-mid"
          width="16px"
          height="16px"
          icon-class="question-cirlce-o"
        />
      </yxt-tooltip>
    </div>
  </div>
</template>

<script>
import { getStatistics } from '../../service';
export default {
  name: 'YxtUlcdSdkO2oProgress',
  data() {
    return {
      progressStatistics: {},
      finishedProgressStr: ''
    };
  },
  props: {
    projectId: {
      type: String
    }
  },
  created() {
    this.getFinishedProgress();
  },
  computed: {
    taskContent() {
      return [
        this.$t('pc_ulcdsdk_lbl_statisticalscopetips'/* 如有课程，则课程中的课件不纳入统计范围 */),
        this.$t('pc_ulcdsdk_lbl_statisticalscopetips1'/* 统计必修任务完成情况，如有课程，则课程中的课件不纳入统计范围 */)
      ][this.progressStatistics.studyStandard];
    }
  },
  methods: {
    getFinishedProgress() {
      getStatistics(this.projectId, false).then(res => {
        if (res) {
          this.progressStatistics = res;
          const {
            studyStandard, completeCount, taskCount, allCompleteCount, allTaskCount, requiredSetting, electiveSetting,
            completeElectiveTaskCount, completePeriodCount, periodSetting, studyScoreSetting, completeStudyScore
          } = res;
          let finishedProgressStr = '';
          // 完成标准 默认0 完成所有必修 1完成所有任务 2完成项目内指定任务数量 3完成项目内指定阶段数量 4获得项目内指定学分
          switch (studyStandard) {
            case 0:
              finishedProgressStr = this.$t('pc_o2o_finish_result_task', [`${completeCount}/${taskCount}`]).d(`${`${completeCount}/${taskCount}`}个任务`);
              break;
            case 1:
              finishedProgressStr = this.$t('pc_o2o_finish_result_task', [`${allCompleteCount}/${allTaskCount}`]).d(`${`${allCompleteCount}/${allTaskCount}`}个任务`);
              break;
            case 2:
              finishedProgressStr = this.$t('pc_o2o_finish_result_task', [`${(completeCount > requiredSetting ? requiredSetting : completeCount) + (completeElectiveTaskCount > electiveSetting ? electiveSetting : completeElectiveTaskCount)}/${requiredSetting + electiveSetting}`]);
              break;
            case 3:
              finishedProgressStr = this.$t('pc_o2o_finish_result_phase', [`${completePeriodCount > periodSetting ? periodSetting : completePeriodCount}/${periodSetting}`]);
              break;
            case 4:
              finishedProgressStr = this.$t('pc_o2o_finish_result_studyScore', [`${completeStudyScore > studyScoreSetting ? studyScoreSetting : completeStudyScore}/${studyScoreSetting}`]);
              break;
          }
          this.finishedProgressStr = finishedProgressStr;
        }
      });
    }
  }
};
</script>

