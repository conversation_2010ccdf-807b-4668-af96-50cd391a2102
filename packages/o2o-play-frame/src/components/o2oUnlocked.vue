<template>
  <div class="yxtulcdsdk-flex-center-center h100p">
    <div>
      <div class="text-center">
        <yxt-ulcd-sdk-svg
          class=""
          width="150px"
          height="125px"
          icon-class="o2olockimage"
        />
      </div>
      <div class="lh28 mt24 text-8c weight-bold font-size-20 text-center ">
        {{ remark?remark:(studyStatus===2?$t('pc_ulcdsdk_lbl_taskNostart'/* 任务未开始 */):$t('pc_ulcdsdk_lbl_taskNo'/* 任务未解锁 */)) }}
      </div>
      <div v-if="studyStartTime" class="lh24 mt12 text-8c font-size-16 font-w400 text-center ">
        {{ studyStatus===2?$t('pc_ulcdsdk_lbl_willbeunlockedatstar',[ formateDate(studyStartTime,'yyyy-MM-dd hh:mm')]):$t('pc_ulcdsdk_lbl_willbeunlockedat',[ formateDate(studyStartTime,'yyyy-MM-dd hh:mm')]) }}
      </div>
      <div class="mt16 text-center ">
        <yxtf-button class="o2oPlayFrame-lockbutton " @click="reload()">
          <span class="yxtulcdsdk-flex-center">  <yxt-ulcd-sdk-svg
            class="mr8"
            width="16px"
            height="16px"
            icon-class="o2orefresh"
          />{{ $t('pc_o2o_lbl_refresh'/* 刷新 */) }}</span>
        </yxtf-button>
      </div>
    </div>
  </div>

</template>

<script>
import Svg from 'packages/_components/svg.vue';
import { dateFormat } from 'packages/_utils/core/date-format';
export default {
  name: 'YxtUlcdSdkUnlock',
  data() {
    return {
    };
  },
  props: {
    studyStatus: {
      type: Number,
      default: ''
    },
    studyStartTime: {
      type: String,
      default: ''
    },
    remark: {
      type: String,
      default: ''
    }
  },
  components: {
    [Svg.name]: Svg
  },
  methods: {
    formateDate: dateFormat,
    reload() {
      window.location.reload();
    }
  }
};
</script>
