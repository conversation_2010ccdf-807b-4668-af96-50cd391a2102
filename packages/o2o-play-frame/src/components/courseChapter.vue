<template>
  <div class="text-59  font-size-12 mb8 lh20">
    <yxt-divider class="mt12 mb12" />
    <template v-if="chapterList.data">
      <div
        v-for="(chapter, j) in chapterList.data"
        :key="j"
      >
        <template v-if="chapter.type===92">
          <div class="mt12 layout-flex hand flex-space-between" @click="collapChapter(chapter)">
            <div class="v-mid"><yxt-ulcd-sdk-svg
              class="mr4 hand"
              module="o2o"
              :class="[{'o2oPlayFrame-task-svg':!(activedChapterIds.includes(chapter.id)) }]"
              width="12px"
              height="12px"
              icon-class="tree-expand-icon"
            />
            </div><div class="ulcdsdk-ellipsis-2 ulcdsdk-coursetitle ulcdsdk-break"><yxtf-tooltip
              class="item"
              :open-filter="true"
              :content="chapter._name"
              :open-delay="2000"
              placement="top"
            >
              <span>
                {{
                  chapter._name
                }}</span></yxtf-tooltip></div></div>
          <template v-if="activedChapterIds.includes(chapter.id)">
            <div
              v-for="(child,i) in chapter.child"
              :key="i"
            >
              <div class="layout-flex mt12 flex-space-between" @click="$emit('onclickKng',child)">
                <div class="flex-1"> <yxtf-tooltip
                  class="item"
                  :open-filter="true"
                  :content="child._name"
                  :open-delay="2000"
                  placement="top"
                >
                  <span :class="[{'ulcdsdk-coursetitle__active':chapterList.kngId===child.id}]" class="ulcdsdk-ellipsis-2 ml32 ulcdsdk-coursetitle ulcdsdk-break"><span :class="[{'ulcdsdk-coursetitle__8c':chapterList.kngId!==child.id}]">{{ child._typeName+' | ' }}</span><span>{{ child.name }}</span></span>
                </yxtf-tooltip>
                </div>
                <yxt-ulcd-sdk-svg
                  class="mr8 ml14"
                  width="16px"
                  height="16px"
                  :icon-class="getkngStatus(child)"
                />
              </div>

            </div>
          </template>
        </template>
        <template v-else>
          <div class="layout-flex mt12 ml16 flex-space-between" @click="$emit('onclickKng',chapter)">
            <div class="flex-1"> <yxtf-tooltip
              class="item"
              :open-filter="true"
              :content="chapter._name"
              :open-delay="2000"
              placement="top"
            >
              <span :class="chapterList.kngId===chapter.id?'ulcdsdk-coursetitle__active':''" class="ulcdsdk-ellipsis-2 ulcdsdk-coursetitle ulcdsdk-break"><span :class="[{'ulcdsdk-coursetitle__8c':chapterList.kngId!==chapter.id}]">{{ chapter._typeName+' | ' }}</span><span>{{ chapter.name }}</span></span>
            </yxtf-tooltip>
            </div>
            <yxt-ulcd-sdk-svg
              class="mr8 ml14"
              width="16px"
              height="16px"
              :icon-class="getkngStatus(chapter)"
            />
          </div>
        </template>
      </div>
    </template>
    <template v-else>
      <div class="mt24 color-75 mb16">
        <yxtf-svg
          class="d-icon v-mid mr4"
          width="16px"
          height="16px"
          icon-class="loading"
        /><span>{{ $t('pc_ulcdsdk_lbl_outlineloading') }} </span>
      </div>
    </template>
  </div>
</template>

<script>
import Svg from 'packages/_components/svg.vue';
export default {
  components: {
    [Svg.name]: Svg
  },
  props: {
    chapterList: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    'chapterList.kngId'(v) {
      if (v && this.chapterList.data) {
        this.chapterList.data.forEach(t=> {
          if (t.type === 92) {
            if (t.child.some(item => item.id === v)) {
              this.activedChapterIds.push(t.id);
            }
          }
        });
      }
    }
  },
  data() {
    return {
      kngId: '',
      activedChapterIds: []
    };
  },
  computed: {
  },
  methods: {
    collapChapter(item) {
      this.activedChapterIds.includes(item.id) ? this.activedChapterIds = this.activedChapterIds.filter(v => v !== item.id) : this.activedChapterIds.push(item.id);
    },
    getkngStatus(chapter) {
      let svgname = '';
      if (chapter.lockStatus === 0 || chapter.bizAtt.needToBuy === 1) {
        return 'o2olock';
      }
      let studyStatus = chapter.studyStatus;
      switch (studyStatus) {
        case 0:
          svgname = 'o2oprogress';
          break;
        case 1:
          svgname = 'o2oprogress';
          break;
        case 2:
          svgname = 'o2ocompleted';
          break;
      }
      return svgname;
    }
  }
};
</script>

