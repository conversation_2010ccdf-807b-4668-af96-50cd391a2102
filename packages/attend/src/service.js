import {flipApi} from 'packages/api';

/**
 * 获取考勤配置
 * @param {Object} data
 * @returns {Promise}
 */
export const getAttendConfig = ({appCode, ...data}) => flipApi.post('attendancedetail/query', data, {headers: {'X-Attend-App-Code': appCode}});

/**
 * 获取用户考勤记录
 * @param {Object} data
 * @returns {Promise}
 */
export const getUserAttendInfo = ({appCode, ...data}) => flipApi.post('userAttendanceInfo/queryUserAttendInfoBase', data, {headers: {'X-Attend-App-Code': appCode}});

/**
 * 签到
 * @param {Object} data
 * @returns {Promise}
 */
export const clockIn = ({appCode, ...data}) => flipApi.post('userAttendanceInfo/signIn', data, {headers: {'X-Attend-App-Code': appCode}});

/**
 * 签到编排
 * @param {Object} data
 * @returns {Promise} 
 */
export const clockInOrch = ({...data}) => flipApi.post('orch/f2f/queryUserAttendInfoForSignIn', data);


/**
 * 签退
 * @param {Object} data
 * @returns {Promise}
 */
export const clockOut = ({appCode, ...data}) => flipApi.post('userAttendanceInfo/signOut', data, {headers: {'X-Attend-App-Code': appCode}});

/**
 * 请假
 * @param {Object} data
 * @returns {Promise}
 */
export const askOff = ({appCode, ...data}) => flipApi.post('userAttendanceInfo/leave', data, {headers: {'X-Attend-App-Code': appCode}});

/**
 * 打卡,或者请假前查询用户考勤信息
 * @param {Object} data
 * @returns
 */
export const checkUserAttendInfo = ({appCode, ...data}) => flipApi.post('userAttendanceInfo/queryUserAttendInfoForSignIn', data, {headers: {'X-Attend-App-Code': appCode}});

/**
 * 获取请假信息 
 * @param {String} attendId 
 * @returns 
 */
export const getLeaveInfo = attendId => flipApi.get(`/userAttendanceInfo/queryLeaveInfo/${attendId}`) 