<template>
  <div v-loading="loading" class="yxtulcdsdk-attend o2oplayframe-task-container ph200 pt40 pm40 yxtulcdsdk-ulcdsdk over-hidden">
    <template v-if="!firstLoaded">
      <yxtf-tooltip
        open-filter
        :content="attendConfig.attendanceName"
        placement="top"
      >
        <div class="yxtulcdsdk-attend__title ellipsis-2">{{ attendConfig.attendanceName }}</div>
      </yxtf-tooltip>
      <div v-if="attendConfig.attendanceLocationAddress || attendConfig.attendanceRealAddress" class="yxtulcdsdk-attend__address">
        <i class="yxt-icon-location-outline font-size-16 color-gray-7"></i>
        <yxtf-tooltip
          open-filter
          :content="attendAddress"
          placement="top"
        >
          <div class="ellipsis">{{ attendAddress }}</div>
        </yxtf-tooltip>
      </div>
      <!-- 打卡状态 -->
      <AttendDetail :attend-config="attendConfig" :user-attend-info="userAttendInfo" :attendance-over="attendanceOver" />
      <!-- 打卡区 -->
      <div class="yxtulcdsdk-attend__footer">
        <div v-if="attendanceOver" class="yxtulcdsdk-attend__footer-over text-center">{{ $t('pc_ulcdsdk_attendance_over' /* 考勤已结束 */) }}</div>
        <CLockInOut
          v-else
          :attend-config="attendConfig"
          :user-attend-info="userAttendInfo"
          :current-time="currentTime"
          @refresh-attend="handleAttendRefresh"
        />
      </div>
    </template>
  </div>

</template>

<script>
import AttendDetail from './components/attend-detail.vue';
import CLockInOut from './components/clock-in-out/index.vue';
import {getAttendConfig, getUserAttendInfo} from './service';
import {dateStringToDate} from 'yxt-ulcd-sdk/src/utils/date-util';
// 2. 定时器
// 数据分发
export default {
  name: 'YxtUlcdSdkAttend',
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  provide() {
    return {
      params: this.params
    };
  },
  components: {
    AttendDetail,
    CLockInOut
  },
  data() {
    this.timer = null; // 定时器
    return {
      loading: false,
      firstLoaded: true,
      attendConfig: {},
      userAttendInfo: {},
      currentTime: Date.now()
    };
  },
  created() {
    this.setTimer();
    this.fetchAttendData();
  },
  computed: {
    attendAddress() {
      const {attendConfig: {attendanceLocationAddress, attendanceRealAddress}} = this;
      const detailedAddress = attendanceLocationAddress ? '-' + attendanceRealAddress : attendanceRealAddress;
      return `${attendanceLocationAddress || ''}${detailedAddress}`;
    },
    attendanceOver() {
      const {signOutBeginTime, signOutEndTime, spaceTime, signInEndTime} = this.attendConfig;
      const { currentTime } = this;
      if (!signOutBeginTime) { // 仅签到
        const delayDuration = (spaceTime || 0) * 60 * 1000;
        const signInRealEndTime = dateStringToDate(signInEndTime).getTime() + delayDuration;
        if (currentTime > signInRealEndTime) return true; // 考勤结束
      }
      const signOutEnd = dateStringToDate(signOutEndTime).getTime();
      return currentTime > signOutEnd;
    }
  },
  methods: {
    fetchAttendData() {
      this.loading = true;
      const {id, appCode} = this.params;
      Promise.all([getAttendConfig({id, appCode}), getUserAttendInfo({attendanceId: id, appCode})])
        .then(([attendConfig, userAttendInfo]) => {
          this.attendConfig = attendConfig;
          this.userAttendInfo = userAttendInfo;
          this.firstLoaded = false;
        })
        .finally(() => (this.loading = false));
    },
    setTimer() {
      if (this.attendanceOver) return; // 考勤结束

      this.timer = setTimeout(() => {
        this.currentTime = Date.now();
        this.setTimer();
        // console.log('currentTime', new Date(this.currentTime).getSeconds());
      }, 1000);
    },
    clearTimer() {
      this.timer && clearTimeout(this.timer);
    },
    handleAttendRefresh() {
      this.$emit('updateProgress', 3); // 打卡 ｜｜ 请假成功
      this.fetchAttendData();
    }
  },
  destroyed() {
    this.clearTimer();
  }
};
</script>
