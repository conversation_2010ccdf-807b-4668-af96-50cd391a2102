import { checkUserAttendInfo as checkUserAttendInfoApi } from './service';
import { MessageBox } from 'yxt-pc';

/**
 * 检查用户打卡信息
 * @param {Object} params
 * @returns {Promise}
 */
export const checkUserAttendInfo = params => {
  const {id: attendanceId, appCode} = params;
  return checkUserAttendInfoApi({attendanceId, appCode})
    .then(data => data)
    .catch(err => {
      const msg = err && err.error && err.error.message;
      MessageBox.error(msg);
      return Promise.reject();
    });
};

/**
 * 打卡类型
 */
export const clockTypeEnum = {
  SIGN_IN: 'signIn',
  SIGN_OUT: 'signOut'
};
