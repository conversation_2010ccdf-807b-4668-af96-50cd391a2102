<!-- 早退原因 -->
<template>
  <yxtf-dialog
    :title="$t('pc_ulcdsdk_early_leave_reason' /* 早退原因 */)"
    width="480px"
    :visible.sync="dialogVisible"
    :cutline="false"
    append-to-body
    @closed="onClosed"
  >
    <yxtf-form ref="form" :model="form">
      <yxtf-form-item label-width="0" class="mb0">
        <yxtf-input
          v-model="form.reason"
          type="textarea"
          :placeholder="$t('pc_ulcdsk_entery_early_leave_reason' /* 请输入早退原因（非必填） */)"
          show-word-limit
          maxlength="30"
          rows="3"
        />
      </yxtf-form-item>
    </yxtf-form>
    <div slot="footer">
      <yxtf-button @click="dialogVisible = false">{{ $t('pc_ulcdsdk_cancel' /* 取消 */) }}</yxtf-button>
      <yxtf-button type="primary" :loading="confirmLoading" @click="handleConfirmClick">{{ $t('pc_ulcdsdk_done' /* 确定 */) }}</yxtf-button>
    </div>

  </yxtf-dialog>
</template>

<script>

export default {
  data() {
    return {
      dialogVisible: false,
      confirmLoading: false,
      form: {
        reason: ''
      }
    };
  },
  methods: {
    show() {
      this.dialogVisible = true;
    },
    handleConfirmClick() {
      // 早退
      this.dialogVisible = false;
      this.$emit('confirm', {...this.form});
    },
    resetData() {
      this.form.reason = '';
    },
    onClosed() {
      this.resetData();
    }
  }
};
</script>
