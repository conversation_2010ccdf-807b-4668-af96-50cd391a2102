<template>
  <yxtf-dialog
    :show-close="false"
    padding-size="small"
    append-to-body
    :visible.sync="visible"
    width="400px"
    class="yxtulcdsdk-attend-confirm"
    @closed="onClosed"
  >
    <div class="yxtulcdsdk-attend-confirm__title-wrap">
      <yxtf-svg
        class="d-icon"
        width="20px"
        height="20px"
        icon-class="icons/f_feedback-tip"
      />
      <span class="yxtulcdsdk-attend-confirm__title">{{ title }}</span>
    </div>
    <div class="yxtulcdsdk-attend-confirm__message">
      {{ message }}
    </div>
    <div class="text-right">
      <yxtf-button plain @click="visible = false">{{ $t('pc_ulcdsdk_cancel' /* 取消 */) }}</yxtf-button>
      <yxtf-button type="primary" @click="handleConfirmClick">{{ $t('pc_ulcdsdk_done' /* 确定 */) }}</yxtf-button>
    </div>
  </yxtf-dialog>

</template>

<script>
export default {
  data() {
    Object.assign(this, {
      resolve: null,
      reject: null
    });

    return {
      visible: false,
      message: '',
      title: ''
    };
  },
  methods: {
    handleConfirmClick() {
      setTimeout(() => (this.visible = false)); // 宏任务关闭弹层，解决弹层背景色闪烁问题
      this.resolve();
    },
    confirm({message, title }) { // 对外方法
      this.visible = true;
      return new Promise((resolve, reject) => {
        Object.assign(this, {
          resolve,
          reject,
          message,
          title
        });
      });
    },
    onClosed() {
      Object.assign(this, {
        resolve: null,
        reject: null,
        message: '',
        title: ''
      });
    }
  }
};
</script>
<style lang="scss" scoped>
$root: '.yxtulcdsdk-attend-confirm';

#{$root} {
  &__title {
    margin-left: 16px;
    color: #262626;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
  }

  &__title-wrap {
    display: flex;
    align-items: center;
  }

  &__message {
    margin-bottom: 24px;
    padding-top: 12px;
    padding-left: 36px;
    color: #757575;
    font-size: 14px;
    line-height: 22px;
  }
}
</style>
