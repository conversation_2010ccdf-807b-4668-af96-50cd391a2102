<!--  考勤 和 请假 -->
<template>
  <div>
    <div class="text-center">
      <yxtf-button
        :disabled="clockBtnDisabled"
        :loading="clockLoading"
        type="primary"
        @click="handleClockClick"
      >{{ clockBtnText }}</yxtf-button>
      <div class="mt16">
        <yxtf-row v-if="userAttendInfo.leaveFlag === 0" type="flex" justify="center" align="middle">
          <div class="font-size-14 lh20 color-gray-9">{{$t('pc_flip_leaved' /* 已请假 */)}}</div>
          <yxtf-button type="primary-text" @click="handleAskOffClick('view')">{{ $t('pc_ulcdsdk_view_reason' /*  查看原因 */) }}</yxtf-button>
        </yxtf-row>
        <yxtf-row class="font-size-14 lh20 color-gray-9" v-else-if="userAttendInfo.leaveAuditStatus === 0"  justify="center" type="flex" align="middle">
          <div>{{$t('pc_ulcdsdk_leave_auditing' /* 请假审核中 */)}}</div>
          <yxtf-button type="primary-text"  @click="toAudit">{{ $t('pc_ote_btn_viewdetail' /*  查看详情 */) }}</yxtf-button>
        </yxtf-row>
        <yxtf-button
          v-else
          type="text"
          :loading="askOffLoading"
          @click="handleAskOffClick('add')"
        >{{ $t('pc_ulcdsdk_ask_off' /* 请假 */) }}</yxtf-button>
      </div>
    </div>
    <AttendPassword ref="AttendPassword" :reference-password="referencePassword" @success="handleAttendPasswordSuccess" />
    <LeaveDialog ref="LeaveDialog"
     :title="$t(userAttendInfo.leaveFlag === 0 ? 'pc_ulcdsdk_ask_off_reason' /* 请假原因 */ : 'pc_ulcdsdk_ask_off' /* 请假 */)"
     :leaveFlag="userAttendInfo.leaveFlag" 
     :attend-config="attendConfig" @success="refreshAttendData" />
    <EarlyLeaveReason ref="EarlyLeaveReason" @confirm="handleEarlyLeaveConfirm" />
    <ConfirmBox ref="ConfirmBox" />
  </div>

</template>

<script>
import AttendPassword from './attend-password.vue';
import EarlyLeaveReason from './early-leave-reason.vue';
import ConfirmBox from './confirm-box.vue';
import LeaveDialog from './leave-dialog/index.vue'
import {dateStringToDate} from 'yxt-ulcd-sdk/src/utils/date-util';
import { clockIn, clockOut, clockInOrch} from '../../service';
import { checkUserAttendInfo, clockTypeEnum } from '../../utils';
import { commonUtil } from 'yxt-biz-pc';

export default {
  components: {
    AttendPassword,
    EarlyLeaveReason,
    ConfirmBox,
    LeaveDialog
  },
  props: {
    attendConfig: {
      type: Object,
      default: () => ({})
    },
    userAttendInfo: {
      type: Object,
      default: () => ({})
    },
    currentTime: {
      type: Number,
      default: () => Date.now()
    }
  },
  inject: ['params'],
  data() {
    return {
      referencePassword: null, // 打卡参考密码
      clockLoading: false,
      askOffLoading: false
    };
  },
  computed: {
    clockBtnDisabled() {
      const {scanCodeSignInFlag, electronicFenceFlag} = this.attendConfig;
      const {signInTime, signInStatus} = this.userAttendInfo;
      const onlySignIn = !this.attendConfig.signOutBeginTime;
      return scanCodeSignInFlag === 1 ||
      electronicFenceFlag === 1 ||
      this.outOfAttendDuration ||
      (onlySignIn && signInTime && typeof signInStatus === 'number'); //  仅签到 正常签到后，则不能再次签到
    },
    clockBtnText() {
      const {scanCodeSignInFlag, electronicFenceFlag} = this.attendConfig;
      if (scanCodeSignInFlag === 1) return this.$t('pc_ulcdsdk_clock_by_qr_code' /*  请在移动端“扫描二维码”签到 */); // 开启了仅扫码
      if (electronicFenceFlag === 1) return this.$t('pc_ulcdsdk_plz_clock_on_mobile' /* 请在移动端“打卡签到” */); // 开启了电子围栏
      if (this.outOfAttendDuration) return this.$t('pc_ulcdsdk_out_of_attend_duration' /* 不在考勤时间内 */);
      return this.$t(this.clockType === clockTypeEnum.SIGN_IN ? 'pc_ulcdsdk_clock_in' /* 签到 */ : 'pc_ulcdsdk_clock_out' /* 签退 */);
    },
    outOfAttendDuration() {
      const {signInBeginTime} = this.attendConfig;
      return signInBeginTime && this.currentTime < dateStringToDate(signInBeginTime).getTime();
    },
    clockType() { // 不是签到就是签退
      const {signInBeginTime, signInEndTime, spaceTime, signOutBeginTime} = this.attendConfig;
      const {signInTime, signInStatus} = this.userAttendInfo;
      if (!signInBeginTime) return clockTypeEnum.SIGN_OUT;
      if (!signOutBeginTime) return clockTypeEnum.SIGN_IN;
      const delayDuration = (spaceTime || 0) * 60 * 1000;
      const startTime = dateStringToDate(signInBeginTime).getTime();
      const endTime = dateStringToDate(signInEndTime).getTime() + delayDuration;
      const inSignInDuration = this.currentTime >= startTime && this.currentTime <= endTime;
      const unSignedIn = !(signInTime && typeof signInStatus === 'number');
      return inSignInDuration && unSignedIn ? clockTypeEnum.SIGN_IN : clockTypeEnum.SIGN_OUT;
    }
  },
  methods: {
    toAudit() {
      const {leaveAuditFormId: formId,leaveAuditFormType:formType} = this.userAttendInfo
      this.openAuditPage({
        formId,
        formType
      });
    },
    /**
     * 跳转审核详情
    */
    async openAuditPage({ formId, formType, orgId, userId, name = '_blank' }) {
      try {
        await commonUtil.goAuditDetail(formId, formType, orgId, userId, name);
      } catch (error) {
        this.$message.error(this.$t('pc_o2o_lbl_permisson_plan_audit').d('您暂无权限查看此审核单'));
      }
    },
    handleClockClick() {
      if (this.needPassword()) {
        const {referencePassword, clockType} = this;
        this.$refs['AttendPassword'].show({referencePassword, clockType});
      } else {
        this.clockType === clockTypeEnum.SIGN_IN ? this.submitClockInRequest() : this.clockOut();
      }
    },
    needPassword() {
      const {signInPassword, signOutPassword} = this.attendConfig;
      const {clockType} = this;
      if (clockType === clockTypeEnum.SIGN_IN && signInPassword) {
        this.referencePassword = signInPassword;
        return true;
      }

      if (clockType === clockTypeEnum.SIGN_OUT && signOutPassword) {
        this.referencePassword = signOutPassword;
        return true;
      }
      return false;
    },
    submitClockInRequest() {
      this.clockLoading = true;
      checkUserAttendInfo(this.params)
        .then(({id}) => {
          const {appCode, id: attendanceId, bizId} = this.params;
          const {scanCodeSignInFlag, signInPassword} = this.attendConfig;
          if(appCode === 'f2f'){
            const params = {
              id, attendanceId, scanCodeSignInFlag, appCode, bizId,
              signInDimension: 1,
              signInAccuracy: 1,
              signInPlaceName: '',
              signInPlaceStatus: 0,
              signInPassword
            };
            return clockInOrch(params);
          }
          else{
            const params = {
              id, attendanceId, scanCodeSignInFlag, appCode, bizId,
              signInDimension: '1',
              signInAccuracy: '1',
              signInPlaceName: '',
              signInPlaceStatus: 0,
              signInPassword
            };
            return clockIn(params);
          }
        })
        .then(() => {
          this.refreshAttendData();
          this.tipClockSuccess(clockTypeEnum.SIGN_IN);
        }).catch(e => {
            this.$message.error(e.message);
          })
        .finally(() => (this.clockLoading = false));

    },
    clockOut() {
      const {signOutBeginTime} = this.attendConfig;
      if (this.currentTime < dateStringToDate(signOutBeginTime).getTime()) { // 早退逻辑判断
        this.$refs['EarlyLeaveReason'].show();
      } else {
        this.submitClockOutRequest();
      }

    },
    getClockCommonParams() {
      return {
        id: this.userAttendInfo.id,
        attendanceId: this.params.id,
        appCode: this.params.appCode,
        bizId: this.params.bizId
      };
    },
    submitClockOutRequest(args = {}) {
      this.clockLoading = true;
      checkUserAttendInfo(this.params)
        .then(({id}) => {
          const {appCode, id: attendanceId, bizId} = this.params;
          const {scanCodeSignInFlag, signOutPassword} = this.attendConfig;
          const params = {
            id, attendanceId, scanCodeSignInFlag, appCode, bizId,
            signOutDimension: '1',
            signOutAccuracy: '1',
            signOutPlaceName: '',
            signOutPlaceStatus: 0,
            signOutPassword,
            ...args
          };
          return clockOut(params);
        })
        .then(() => {
          this.refreshAttendData();
          this.tipClockSuccess(clockTypeEnum.SIGN_OUT);
        })
        .finally(() => (this.clockLoading = false));
    },
    handleEarlyLeaveConfirm(form) {
      // 调用签退接口
      this.submitClockOutRequest({earlyLeaveReason: form.reason});
    },
    tipClockSuccess(clockType) {
      const { signOutTime } = this.userAttendInfo;
      let signInTip = this.$t('pc_ulcdsdk_singin_success' /* 签到成功 */);
      let singOutTip = this.$t('pc_ulcdsdk_signout_success' /* 签退成功 */);
      if (signOutTime) singOutTip += this.$t('pc_ulcdsdk_record_last_signout_time' /* 系统将记录最晚签退时间 */);
      this.$message.success(clockType === clockTypeEnum.SIGN_IN ? signInTip : singOutTip);
    },
    handleAskOffClick(operateType) {
      const revokeLeavePopupShow = () => {
        const {leaveFlag} = this.userAttendInfo
        const {id: attendanceId} = this.params
        this.$refs.LeaveDialog.show({leaveFlag, attendanceId})  
      }

      if (operateType === 'view') { // 查看原因
        revokeLeavePopupShow()
        return;
      }

      const { electronicFenceFlag, signOutBeginTime, signInBeginTime} = this.attendConfig;
      const { signInStatus, signOutStatus, signInPlaceStatus, signOutPlaceStatus } = this.userAttendInfo;

      const onlySignIn = !signOutBeginTime;
      const onlySingOut = !signInBeginTime;
      const fenceOpened = electronicFenceFlag === 1;
      let clockedNormally = true;
      // 正常打卡则提示， 否则直接请假
      // 签到， 无迟到和地点异常
      // 签退，无早退和地点异常
      if (onlySignIn) {
        clockedNormally = fenceOpened ? signInStatus === 0 && signInPlaceStatus === 0 : signInStatus === 0;
      } else if (onlySingOut) {
        clockedNormally = fenceOpened ? signOutStatus === 0 && signOutPlaceStatus === 0 : signOutStatus === 0;
      } else {
        const signedInNormally = fenceOpened ? signInStatus === 0 && signInPlaceStatus === 0 : signInStatus === 0;
        const signedOutNormally = fenceOpened ? signOutStatus === 0 && signOutPlaceStatus === 0 : signOutStatus === 0;
        clockedNormally = signedInNormally && signedOutNormally;
      }

      if (clockedNormally) {
        this.$refs['ConfirmBox'].confirm({
          title: this.$t('pc_ulcdsdk_confirm_ask_off' /* 确定请假吗？ */),
          message: this.$t('pc_ulcdsdk_confirm_ask_off_msg' /* 您的考勤目前正常，请确认是否需要请假 */)
        })
          .then(() => revokeLeavePopupShow());
      } else {
        revokeLeavePopupShow()
      }
    },
    refreshAttendData() {
      this.$emit('refresh-attend'); // 刷新考勤信息
    },
    handleAttendPasswordSuccess() {
      this.clockType === clockTypeEnum.SIGN_IN ? this.submitClockInRequest() : this.clockOut();
    }
  }

};
</script>
