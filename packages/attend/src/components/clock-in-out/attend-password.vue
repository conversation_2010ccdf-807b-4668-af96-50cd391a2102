<!-- 考勤口令 -->
<template>
  <yxtf-dialog
    :title="title"
    width="480px"
    append-to-body
    :visible.sync="dialogVisible"
    @closed="onClosed"
  >
    <yxtf-form ref="form" :model="form" :rules="rules">
      <yxtf-form-item label-width="0" prop="password">
        <yxtf-input
          v-model="form.password"
          :placeholder="$t('pc_ulcdsdk_plz_input_pwd_required' /* 请输入口令（必填） */)"
          show-word-limit
          maxlength="20"
          size="small"
        />
      </yxtf-form-item>
    </yxtf-form>
    <div slot="footer">
      <yxtf-button @click="dialogVisible = false">{{ $t('pc_ulcdsdk_cancel' /* 取消 */) }}</yxtf-button>
      <yxtf-button :disabled="!form.password" type="primary" @click="handleConfirmClick">{{ $t('pc_ulcdsdk_done' /* 确定 */) }}</yxtf-button>
    </div>
  </yxtf-dialog>
</template>

<script>
import { clockTypeEnum } from '../../utils';

const validatePassword = function(rule, value, callback) {
  const {referencePassword} = this;
  if (referencePassword !== value) {
    const isSignIn = this.clockType === clockTypeEnum.SIGN_IN;
    return callback(
      new Error(this.$t('pc_ulcdsdk_attend_password_error'/* {0}口令错误，再试试吧 */, [this.$t(isSignIn ? 'pc_ulcdsdk_clock_in' /* 签到 */ : 'pc_ulcdsdk_clock_out' /* 签退 */)]))
    );
  }
  return callback();
};

export default {
  data() {
    return {
      referencePassword: null,
      clockType: null,
      dialogVisible: false,
      form: {
        password: ''
      },
      rules: {
        password: [
          {
            validator: validatePassword.bind(this),
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    title() {
      const {clockType} = this;
      return clockType === clockTypeEnum.SIGN_IN
        ? this.$t('pc_ulcdsdk_sign_in_password' /* 签到口令 */)
        : this.$t('pc_ulcdsdk_sign_out_password' /* 签退口令 */);
    }
  },
  methods: {
    show({referencePassword, clockType}) {
      Object.assign(this, {
        referencePassword,
        dialogVisible: true,
        clockType
      });
    },
    handleConfirmClick() {
      this.$refs['form'].validate(flag => {
        if (flag) {
          this.dialogVisible = false;
          this.$emit('success');
        }
      });
    },
    resetData() {
      const { referencePassword, clockType} = this.$options.data.bind(this)();
      Object.assign(this, {
        referencePassword,
        clockType
      });
      this.$refs.form.resetFields();
    },
    onClosed() {
      this.resetData();
    }
  }
};
</script>
