<template>
  <yxtf-dialog
    :title="title"
    width="480px"
    destroy-on-close
    class="yxtulcdsdk-attend-leave-dialog yxtulcdsdk-attend-leave-dialog__body-overwrite"
    :visible.sync="visible"
    @closed="closed"
  >
    <div class="yxtulcdsdk-attend-leave-dialog__main">
      <ViewComp v-if="leaveFlag === 0" :attendance-id="params.id" />
      <LeaveComp v-else ref="LeaveComp" />
    </div>
    <span slot="footer" v-if="leaveFlag !== 0">
      <yxtf-button @click="visible = false">取消</yxtf-button>
      <yxtf-button :loading="confirmLoading" type="primary" @click="handleConfirm">确定</yxtf-button>
    </span>
  </yxtf-dialog>
</template>

<script>
import LeaveComp from './leave.vue';
import ViewComp from './view.vue';
import {askOff} from '../../../service';
import {checkUserAttendInfo} from '../../../utils';

export default {
  props: {
    title: String,
    leaveFlag: {
      type: Number, // 0 已请假 1 未请假
      required: true
    },
    attendConfig: {
      type: Object,
      default: () => ({})
    }
  },
  inject: ['params'],
  components: {
    LeaveComp,
    ViewComp
  },
  data() {
    return {
      confirmLoading: false,
      visible: false
    };
  },
  methods: {
    show() { // 是否请假 + attendId
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    handleConfirm() {
      this.$refs.LeaveComp.getParams()
        .then(({reason, attachments}) =>{
          this.confirmLoading = true;
          checkUserAttendInfo(this.params)
            .then(({id}) => {
              const {appCode, id: attendanceId, bizId} = this.params;
              const {scanCodeSignInFlag} = this.attendConfig;
              const params = { id, attendanceId, scanCodeSignInFlag, appCode, bizId, leaveReason: reason, attachments}; // TODO 通用参数
              return askOff(params);
            })
            .then(() => {
              this.$emit('success');
              this.hide();
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        });
    },
    closed() {
      Object.assign(this, this.$options.data.bind(this)());
    }
  }
};
</script>

<style lang="scss" scoped>
.yxtulcdsdk-attend-leave-dialog {
  &__body-overwrite {
    ::v-deep > .yxtf-dialog__body {
      padding-bottom: 0 !important;
    }
  }
}

</style>
