<template>
  <div class="yxtulcdsdk-attend-leave-view">
    <div v-floading="true" class="yxtulcdsdk-attend-leave-view__loading"  v-if="fetchLeaveInfoLoading"></div>
    <template>
      <div v-if="leaveReason" class="yxtulcdsdk-attend-leave-view__reason">{{ leaveReason }}</div>
      <yxtf-image
        v-if="leaveAttachments && leaveAttachments.length > 0"
        class="yxtulcdsdk-attend-leave-view__image"
        :src="leaveAttachments[0].url"
        :preview-src-list="[leaveAttachments[0].url]"
      />
      <div class="yxtbiz-leave-popup-view__nil" v-if="!leaveReason && (!leaveAttachments || leaveAttachments.length === 0)">{{$t('pc_ulcdsdk_none' /* 无 */)}}</div>
    </template>
  </div>
</template>

<script>
import {getLeaveInfo} from '../../../service'

export default {
  props: {
    attendanceId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      visible: false,
      leaveReason: '',
      fetchLeaveInfoLoading: false,
      leaveAttachments: [],
      previewZIndex: 2001
    };
  },
  mounted() {
    this.fetchLeaveInfo()
  },
  methods: {
    fetchLeaveInfo() {
      this.fetchLeaveInfoLoading = true
      getLeaveInfo(this.attendanceId)
      .then((data) => {
        const {leaveReason, attachments} = data || {}
        Object.assign(this, {
          leaveReason,
          leaveAttachments: attachments || []
        })
      })
      .finally(() => this.fetchLeaveInfoLoading = false)
    },
  }
}
</script>

<style lang="scss" scoped>
.yxtulcdsdk-attend-leave-view {
  position: relative;
  min-height: 164px;

  &__loading {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 45px;
    height: 45px;
    transform: translate(-50%, -50%);
  }

  &__reason {
    margin-bottom: 24px;
    font-size: #262626;
    font-size: 14px;
  }

  &__image {
    width: 120px;
    height: 120px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
}
</style>
