<template>
  <div>
    <yxtf-form ref="form" :model="form" label-position="top">
      <yxtf-form-item :label="$t('pc_o2o_lbl_leave_reason' /* 请假原因 */)">
        <yxtf-input maxlength="30" rows="2" :placeholder="$t('pc_ulcdsdk_enter_leave_reason' /* 请输入请假原因（非必填） */)" 
        type="textarea" v-model="form.reason" show-word-limit></yxtf-input>
      </yxtf-form-item>
      <yxtf-form-item class="mb0">
        <div slot="label"><span>{{$t('pc_o2o_lbl_uploadpic' /* 上传图片 */)}}</span><span class="color-gray-7 font-size-12 lh20">{{$t('pc_ulcdsdk_pic_max_size' /* （图片不超过10M） */, [10])}}</span></div>
        <yxtbiz-upload-image
          :limit="1"
          no-crop
          app-code="attend"
          module-name="attend"
          function-name="attend"
          @fileAdded="fileAdded"
          @fileRemoved="fileRemoved"
          :list="attachments"
          :size="10240"
        ></yxtbiz-upload-image>
      </yxtf-form-item>
    </yxtf-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        reason: '',
        attachments: [],
      }
    }
  },
  methods: {
    fileAdded({file,index}) {
      this.form.attachments[index] = this.convertFilePropsToBackend(file)
    },
    fileRemoved(index) {
      this.form.attachments.splice(index, 1)
    },
    getParams() {
      return new Promise(resolve => {
        const {reason, attachments} = this.form
        resolve({reason, attachments})
      })
    },
    convertFilePropsToBackend(file) {
      return {
          fileName: file.name,
          url: file.fullUrl,
          fileId: file.id,
          fileType: file.fileType,
          fileSubType: file.fileClass,
          fileSize: file.size,
      }
    },
  }
}
</script>