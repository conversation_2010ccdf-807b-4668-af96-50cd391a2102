
<!-- 签到结果展示 -->
<template>
  <ul class="yxtulcdsdk-attend-detail">
    <li v-if="attendConfig.signInBeginTime" class="yxtulcdsdk-attend-detail__item">
      <div class="yxtulcdsdk-attend-detail__datetime-wrap">
        <!-- yxtf-bg-warning -->
        <div class="yxtulcdsdk-attend-detail__clock-tag yxtulcdsdk-attend-detail__clock-tag--in">{{ $t('pc_ulcdsdk_clock_in' /* 签到 */) }}</div>
        <div class="yxtulcdsdk-attend-detail__datetime ">{{ dateFormat(attendConfig.signInBeginTime) }} ～ {{ dateFormat(attendConfig.signInEndTime) }}</div>
      </div>

      <div v-if="userAttendInfo.signInTime && typeof userAttendInfo.signInStatus === 'number'" class="yxtulcdsdk-attend-detail__clocked">
        <div>
          <div class="yxtulcdsdk-attend-detail__clocked-time">{{ $t('pc_ulcdsdk_clocked_in' /* 已打卡 {0} */, [dateFormat(userAttendInfo.signInTime, 'hh:mm')]) }}</div>
          <yxtf-tag v-if="userAttendInfo.signInStatus === 0 && userAttendInfo.signInPlaceStatus === 0" size="mini">{{ $t('pc_ulcdsdk_normal' /* 正常 */) }}</yxtf-tag>
          <template v-else>
            <yxtf-tag v-if="userAttendInfo.signInStatus === 1" type="danger" size="mini">{{ $t('pc_ulcdsdk_late' /* 迟到 */) }}</yxtf-tag>
            <yxtf-tag v-if="userAttendInfo.signInPlaceStatus === 1" type="danger" size="mini">{{ $t('pc_ulcdsdk_abnormal_location' /* 地点异常 */) }}</yxtf-tag>
          </template>
          <yxtf-tag v-if="userAttendInfo.leaveFlag === 0" size="mini">{{ $t('pc_ulcdsdk_ask_off' /* 请假 */) }}</yxtf-tag>
        </div>
        <div class="yxtulcdsdk-attend-detail__clocked-date">{{ dateFormat(userAttendInfo.signInTime, 'yyyy-MM-dd') }}</div>
      </div>
      <div v-else class="yxtulcdsdk-attend-detail__unclocked">
        <div class="yxtulcdsdk-attend-detail__unclocked-text">{{ $t('pc_ulcdsdk_not_clocked_in' /* 未打卡 */) }}</div>
        <yxtf-tag v-if="userAttendInfo.leaveFlag === 0" size="mini">{{ $t('pc_ulcdsdk_ask_off' /* 请假 */) }}</yxtf-tag>
        <yxtf-tag v-else-if="attendanceOver" type="danger" size="mini">{{ $t('pc_ulcdsdk_absence' /* 缺勤 */) }}</yxtf-tag>
      </div>
    </li>
    <li v-if="attendConfig.signOutBeginTime" class="yxtulcdsdk-attend-detail__item yxtulcdsdk-attend-detail__item--clock-out">
      <div class="yxtulcdsdk-attend-detail__datetime-wrap">
        <div class="yxtulcdsdk-attend-detail__clock-tag yxtulcdsdk-attend-detail__clock-tag--out">{{ $t('pc_ulcdsdk_clock_out' /* 签退 */) }}</div>
        <div class="yxtulcdsdk-attend-detail__datetime ">{{ dateFormat(attendConfig.signOutBeginTime) }} ～ {{ dateFormat(attendConfig.signOutEndTime) }}</div>
      </div>
      <div v-if="userAttendInfo.signOutTime && typeof userAttendInfo.signOutStatus === 'number'" class="yxtulcdsdk-attend-detail__clocked">
        <div>
          <div class="yxtulcdsdk-attend-detail__clocked-time">{{ $t('pc_ulcdsdk_clocked_in' /* 已打卡 {0} */, [dateFormat(userAttendInfo.signOutTime, 'hh:mm')]) }}</div>
          <yxtf-tag v-if="userAttendInfo.signOutStatus === 0 && userAttendInfo.signOutPlaceStatus === 0" size="mini">{{ $t('pc_ulcdsdk_normal' /* 正常 */) }}</yxtf-tag>

          <template v-else>
            <yxtf-tag v-if="userAttendInfo.signOutStatus === 1" type="danger" size="mini">{{ $t('pc_ulcdsdk_early_leave' /* 早退 */) }}</yxtf-tag>
            <yxtf-tag v-if="userAttendInfo.signOutPlaceStatus === 1" type="danger" size="mini">{{ $t('pc_ulcdsdk_abnormal_location' /* 地点异常 */) }}</yxtf-tag>
          </template>
          <yxtf-tag v-if="userAttendInfo.leaveFlag === 0" size="mini">{{ $t('pc_ulcdsdk_ask_off' /* 请假 */) }}</yxtf-tag>
        </div>
        <div class="yxtulcdsdk-attend-detail__clocked-date">{{ dateFormat(userAttendInfo.signOutTime, 'yyyy-MM-dd') }}</div>
        <template v-if="userAttendInfo.signOutStatus === 1">
          <yxtf-divider class="yxtulcdsdk-attend-detail__divider" />
          <div class="yxtulcdsdk-attend-detail__ask-off-reason">
            {{ $t('pc_ulcdsdk_reason_early_leave' /* 早退原因：{0} */ , [userAttendInfo.earlyLeaveReason || $t('pc_ulcdsdk_none' /* 无 */)] ) }}
          </div>
        </template>
      </div>
      <div v-else class="yxtulcdsdk-attend-detail__unclocked">
        <div class="yxtulcdsdk-attend-detail__unclocked-text">{{ $t('pc_ulcdsdk_not_clocked_in' /* 未打卡 */) }}</div>
        <yxtf-tag v-if="userAttendInfo.leaveFlag === 0" size="mini">{{ $t('pc_ulcdsdk_ask_off' /* 请假 */) }}</yxtf-tag>
        <yxtf-tag v-else-if="attendanceOver" type="danger" size="mini">{{ $t('pc_ulcdsdk_absence' /* 缺勤 */) }}</yxtf-tag>
      </div>
    </li>
  </ul>
</template>

<script>
import { dateFormat } from 'packages/_utils/core/date-format';

export default {
  props: {
    attendConfig: {
      type: Object,
      default: () => ({})
    },
    userAttendInfo: {
      type: Object,
      default: () => ({})
    },
    attendanceOver: Boolean
  },
  methods: {
    dateFormat: (date, format = 'yyyy-MM-dd hh:mm') => dateFormat(date, format)
  }
};
</script>
