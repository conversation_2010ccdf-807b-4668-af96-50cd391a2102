import { apiBaseUrlApi} from 'packages/api';

// 学员项目详情 projectId: 项目id
export const getStuDetail = (path, projectId, actvRegId) => {
  return apiBaseUrlApi.get(`${path}/aom/activities/${projectId}/arragetree4stu`, {
    regId: actvRegId
  });
};

// 项目详情
export const getProjectBaseInfo = (path, projectId, actvRegId) => {
  if (!projectId || !actvRegId) {
    return Promise.resolve();
  }
  return apiBaseUrlApi.get(`${path}/aom/activities/${projectId}/forstu`, {
    actvRegId
  });
};

// 获取设计器功能配置
export const getDesignerFuncConfig = (path, actvRegId) => {
  return apiBaseUrlApi.get(`${path}/aom/registries/${actvRegId}/designerfuncconfig`);
};

// 获取设计器模块配置
export const getDesignerModulesConfig = (path, actvRegId) => {
  return apiBaseUrlApi.get(`${path}/aom/registries/${actvRegId}/designermodules`);
};

// 项目权限校验
export const checkPermission = (path, projectId, taskId, actvRegId) => {
  return apiBaseUrlApi.post(`${path}/aom/activities/${projectId}/items/${taskId}/check`, {}, {
    params: {
      regId: actvRegId
    }
  });
};

// 后端记录学员最近学习的任务
export const setRecentLearn = (path, actvId, itemId, actvRegId) => {
  return apiBaseUrlApi.put(
    `${path}/aom/activities/${actvId}/items/${itemId}/recentlearn`,
    {},
    {
      params: {
        regId: actvRegId
      }
    }
  );
};

// 学员端-进度排行统计
export const getStatistics = (path, projectId, actvRegId) => {
  return apiBaseUrlApi.get(`${path}/aom/control/study/${projectId}/stu/statistics`, {
    actvRegId
  });
};

/**
 * 获取项目接口地址模块
 */
export const getProjectServiceUrl = (params) => {
  return apiBaseUrlApi.get('/uacd/stu/reg/serviceUrl', params);
};
