<template>
  <div class="score-content">
    <div class="pb24 font-size-12 layout-flex layout-align-center lh20">
      <yxtf-progress :percentage="(progressStatistics.taskCompletedRate || 0) * 100" line-width="120px" :show-text="false" />
      <span class="text-8c ml12 font-size-12 lh20">{{ (progressStatistics.taskCompletedRate || 0) * 100 }}%（{{ finishedProgressStr }}）</span>
    </div>
  </div>
</template>

<script>
import { getStatistics } from '../../service';
export default {
  name: 'YxtUlcdSdkO2oProgress',
  data() {
    return {
      progressStatistics: {},
      finishedProgressStr: ''
    };
  },
  props: {
    projectId: {
      type: String,
      default: ''
    },
    serverPath: {
      type: String,
      default: ''
    },
    actvRegId: {
      type: String,
      default: ''
    }
  },
  created() {
    this.getFinishedProgress();
  },
  computed: {
  },
  methods: {
    getFinishedProgress() {
      getStatistics(this.serverPath, this.projectId, this.actvRegId).then(res => {
        if (res) {
          this.progressStatistics = res;
          let { taskCompletedCount, taskCount } = res;
          taskCompletedCount = taskCompletedCount || 0;
          taskCount = taskCount || 0;
          // 目前完成标准只有完成所有任务
          this.finishedProgressStr = this.$t('pc_ulcdsdk_uacd_finish_result_task', [`${taskCompletedCount}/${taskCount}`]).d(`${`${taskCompletedCount}/${taskCount}`}个活动`);
        }
      });
    }
  }
};
</script>

