<template>
  <div class="yxtulcdsdk-flex-center-center h100p">
    <div>
      <div class="text-center">
        <yxt-ulcd-sdk-svg
          class=""
          width="150px"
          height="125px"
          icon-class="o2olockimage"
        />
      </div>
      <div class="lh28 mt24 text-8c weight-bold font-size-20 text-center ">
        {{ msg }}
      </div>
      <div v-if="studyStartTime" class="lh24 mt12 text-8c font-size-16 font-w400 text-center ">
        {{ studyStatus===2?$t('pc_ulcdsdk_lbl_willbeunlockedatstar',[ formatDate(studyStartTime,'yyyy-MM-dd hh:mm')]):$t('pc_ulcdsdk_lbl_willbeunlockedat',[ formatDate(studyStartTime,'yyyy-MM-dd hh:mm')]) }}
      </div>
      <div class="mt16 text-center ">
        <yxtf-button class="o2oPlayFrame-lockbutton " @click="reload()">
          <span class="yxtulcdsdk-flex-center">  <yxt-ulcd-sdk-svg
            class="mr8"
            width="16px"
            height="16px"
            icon-class="o2orefresh"
          />{{ $t('pc_o2o_lbl_refresh'/* 刷新 */) }}</span>
        </yxtf-button>
      </div>
    </div>
  </div>

</template>

<script>
import Svg from 'packages/_components/svg.vue';
import { dateFormat } from 'packages/_utils/core/date-format';
import {STUDY_STATUS} from '../../const';

export default {
  name: 'YxtUlcdSdkUnlock',
  components: {
    [Svg.name]: Svg
  },
  data() {
    return {
      STUDY_STATUS
    };
  },
  props: {
    studyStatus: {
      type: Number,
      default: ''
    },
    studyStartTime: {
      type: String,
      default: ''
    },
    remark: {
      type: String,
      default: ''
    }
  },
  computed: {
    msg() {
      if (this.remark) {
        return this.remark;
      }
      switch (this.studyStatus) {
        case STUDY_STATUS.NOT_START:
          return this.$t('pc_ulcdsdk_uacd_lbl_taskNostart'/* 任务未开始 */);
        case STUDY_STATUS.LOCK:
          return this.$t('pc_ulcdsdk_uacd_lbl_taskNo'/* 任务未解锁 */);
        case STUDY_STATUS.EMPTY:
          return this.$t('pc_ulcdsdk_uacd_errortip_empty'/* 没有可学习的活动 */);
        default:
          return '';
      }
    }
  },
  methods: {
    formatDate: dateFormat,
    reload() {
      window.location.reload();
    }
  }
};
</script>
