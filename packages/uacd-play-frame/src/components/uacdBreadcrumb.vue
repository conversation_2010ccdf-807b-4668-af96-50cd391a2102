
<template>
  <div v-if="info.show" class="yxtulcdsdk-o2obreadcrumb mt4">
    <yxtf-breadcrumb separator-class="yxtf-icon-arrow-right">
      <yxtf-breadcrumb-item
        v-for="(item,index) in info.list"
        :key="index"
        :is-link="!!item.eventName"
        @click.native="clickEventName(item.eventName)"
      >
        {{ item.name }}
      </yxtf-breadcrumb-item>
    </yxtf-breadcrumb>
  </div>
</template>

<script>
export default {
  name: 'O2oBreadcrumb',
  props: {
    componentName: {
      type: String,
      default: ''
    }
  },
  watch: {
    componentName(val) {
      this.info.show = false;
      this.info.list = [];
    }
  },
  data() {
    return {
      info: {
        show: false,
        list: []
      }
    };
  },
  created() {
    this.$root.$on('SETBREADCRUMBUPDATE', this.setBreadcrumbUpdate);
  },
  beforeDestroy() {
    this.$root.$off('SETBREADCRUMBUPDATE', this.setBreadcrumbUpdate);
  },
  methods: {
    setBreadcrumbUpdate(data) {
      if (data) {
        this.info = data;
      }
    },
    clickEventName(eventName) {
      if (!eventName) {
        return;
      }
      if (eventName === 'setBackTask') {
        this.$emit('setBackTask', true);
      } else {
        this.$root.$emit(eventName);
      }
    }
  }
};
</script>
<style lang="scss">
.yxtulcdsdk-o2obreadcrumb {
  .yxtf-breadcrumb {
    height: 20px;
    font-size: 12px;
    line-height: 20px;
  }

  .is-link:hover {
    color: #262626 !important;
  }

  .yxtf-breadcrumb__inner {
    color: #8c8c8c !important;
  }

  .yxtf-breadcrumb__separator[class*=icon] {
    margin: 0 4px;
    font-weight: 400;
  }
}

</style>

