<template>
  <div
    class="mt8 hand mr8"
    :class="activeId===groupDetail.id?'task-content-active':'task-content'"
  >
    <div :class="[{'pl68':hasFolder === 1&&taskType===2},{'pl48':hasFolder === 1&&taskType!==2},,{'pl24':hasFolder !== 1&&taskType!==2},{'pl60':hasFolder !== 1&&taskType===2}]" class="hand pt8 pb8">
      <div class="layout-flex flex-space-between" @click="$emit('onclickTask',groupDetail)">
        <div class="flex-1"> <yxtf-tooltip
          class="item"
          :open-filter="true"
          :content="groupDetail.computedName"
          :open-delay="2000"
          placement="top"
        >
          <span :class="activeId===groupDetail.id?'task-content-color-active':''" class="ulcdsdk-ellipsis-2 group-title font-w400 ulcdsdk-break">{{ groupDetail.computedName }}</span>
        </yxtf-tooltip>
        </div>
        <yxt-ulcd-sdk-svg
          class="mr8 mt2 ml14"
          width="16px"
          height="16px"
          :icon-class="getO2OStatus(groupDetail.resultStatus)"
        />
      </div>
      <div class="text-8c mt4 font-size-12 layout-flex lh20" @click="$emit('onclickTask',groupDetail)">
        <yxtf-tooltip
          class="item"
          :open-filter="true"
          :open-delay="2000"
          :content="groupDetail.computedTypeName"
          placement="top"
        >
          <div class="mr8 task-content-tag ellipsis">
            {{ groupDetail.computedTypeName }}
          </div></yxtf-tooltip>
        |<yxtf-tooltip
          class="item"
          :open-filter="true"
          :open-delay="2000"
          :content="groupDetail.required === 0 ? electiveText : compulsoryText "
          placement="top"
        ><div class="ml8 task-content-tag ellipsis mr8">{{ groupDetail.required === 0 ? electiveText : compulsoryText }}</div></yxtf-tooltip>
      </div>
      <template v-if="activeId===groupDetail.id">
        <div v-if="(groupDetail.startTime||groupDetail.endTime)&&[8, 9, 10, 11, 12, 13, 16, 22].indexOf(groupDetail.type) > -1" class="text-8c mt4 font-size-12 lh20">
          {{ groupDetail.startTime | date('yyyy-MM-dd HH:mm', '--') }} {{ $t('pc_o2o_lbl_to'/* 至 */) }} {{ groupDetail.endTime | date('yyyy-MM-dd HH:mm', '--') }}
        </div>
        <div v-if="groupDetail.integral&&groupDetail.credit" class="text-8c mt4 font-size-12 lh20">
          {{ $t('pc_ulcdsdk_lbl_canearnpointscredits', [groupDetail.integral,groupDetail.credit]) }}
        </div>
        <div v-else-if="groupDetail.integral" class="text-8c mt4 font-size-12 lh20">
          {{ $t('pc_ulcdsdk_lbl_canearnpoints', [groupDetail.integral]) }}
        </div>
        <div v-else-if="groupDetail.credit" class="text-8c mt4 font-size-12 lh20">
          {{ $t('pc_ulcdsdk_lbl_canearncredits', [groupDetail.credit]) }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import Svg from 'packages/_components/svg.vue';
export default {
  components: {
    [Svg.name]: Svg
  },
  props: {
    chapterList: {
      type: Object,
      default: () => ({})
    },
    groupDetail: {
      type: Object,
      default: {}
    },
    activeId: {
      type: String
    },
    compulsoryText: {
      type: String
    },
    electiveText: {
      type: String
    },
    hasFolder: {
      type: Number
    },
    taskType: {
      type: Number
    }
  },
  computed: {
  },
  methods: {
    getO2OStatus(resultStatus) {
      let svgname = 'o2onotstarted';
      switch (resultStatus) {
        // 暂时没有解锁逻辑
        // case 1:
        //   svgname = 'o2olock';
        //   break;
        case 1:
          svgname = 'o2oprogress';
          break;
        case 2:
          svgname = 'o2ocompleted';
          break;
      }
      return svgname;
    },
    onclickKng(child) {
      this.$emit('onclickKng', child);
    }
  }
};
</script>

