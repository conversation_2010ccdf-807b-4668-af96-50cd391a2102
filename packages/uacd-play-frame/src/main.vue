<template>
  <div class="yxtulcdsdk-o2o-play-frame yxtulcdsdk-ulcdsdk pr over-hidden">
    <transition name="slide-in" @after-leave="afterLeave" @before-enter="beforeEnter">
      <aside
        v-show="leftshow&&!isFullScreen"
        v-loading="leftloading"
        class="o2oPlayFrame-left"
        :class="[{'o2oPlayFrame-left__box-shadow':fixedTransEnd}]"
      >
        <div class="hline layout-flex layout-flex-vertical">
          <div class="yxtulcdsdk-flex-center-center left-title" :class="{'v-hidden':fixedTransEnd}">
            <yxt-ulcd-sdk-svg
              class="hand"
              width="20px"
              height="20px"
              icon-class="o2ogoleftnav"
              @click.native="setConfirmgoBack"
            />
            <span class="pl8 hand flex-1" @click="setConfirmgoBack">{{ $t('pc_o2o_lbl_back').d('返回') }}</span>
            <yxtf-tooltip
              placement="bottom"
              effect="dark"
              :content="$t('pc_ulcdsdk_uacd_lbl_putawaythetrainingoutline'/* 收起活动大纲 */)"
            >
              <yxt-ulcd-sdk-svg
                class="hand o2oPlayFrame-collapse-ico"
                width="24px"
                height="24px"
                icon-class="o2ocollapseoutline"
                @click.native="leftshow = !leftshow"
              />

            </yxtf-tooltip>
          </div>
          <div>
            <div class="ml16 mr16 font-size-18 mt20 mb24 weight-bold text-26"><span><yxtf-tooltip
              class="item"
              :open-filter="true"
              :open-delay="2000"
              :content="projectDetail.actvName"
              placement="top"
            >
              <span class="ulcdsdk-ellipsis-2 ulcdsdk-break hand" @click="goDetail">{{ projectDetail.actvName }}</span>
            </yxtf-tooltip></span></div>
            <uacd-progress
              v-if="serverPath"
              ref="o2oprogress"
              :project-id="projectId"
              :server-path="serverPath"
              :actv-reg-id="actvRegId"
            />
          </div>
          <div class="pr mb12 flex-1 h0">
            <yxtf-scrollbar :fit-height="true">
              <div class="mt24 ml16 yxtulcdsdk-flex-center mr4 mb22">
                <yxt-ulcd-sdk-svg
                  class="mr8 textcolorop8"
                  width="16px"
                  height="16px"
                  icon-class="o2ooutline"
                />
                <span class="font-bolder textcolorop8 font-size-14 lh22">{{ $t('pc_ulcdsdk_uacd_lbl_trainingprogram'/* 活动大纲 */) }}</span>
              </div>

              <div v-for="(period,index) in periodList" v-show="period.children.length" :key="index">
                <!-- 遍历阶段 -->
                <div v-if="hasFolder === 1 && periodList.length > 0">
                  <div class="layout-flex hand ml16 flex-space-between" :class="period.actived&&period.children.length?'':'mb32'" @click="collapSingPeriod(period)">
                    <div><yxt-ulcd-sdk-svg
                      module="o2o"
                      class="mr8 mt4 "
                      :class="[{'o2oPlayFrame-task-svg':!period.actived}]"
                      width="16px"
                      height="16px"
                      icon-class="tree-expand-icon"
                    />
                    </div>
                    <div class="flex-1 mr48"> <yxtf-tooltip
                                                class="item"
                                                :open-filter="true"
                                                :content="period.refName"
                                                :open-delay="2000"
                                                placement="top"
                                              >
                                                <span class="ulcdsdk-ellipsis-2  period-title ulcdsdk-break">{{
                                                  designerFuncConfig.computedPeriodName + (index+1)+":" + period.refName
                                                }}</span>
                                              </yxtf-tooltip>
                      <span v-if="period.overdueTime" class="mt4 color-8c font-size-12 lh20">{{ $t('pc_ulcdsdk_lbl_over_time'/* 逾期时间： */)+ formatDate(period.overdueTime,'yyyy-MM-dd hh:mm') }}</span>
                    </div>

                    <yxt-ulcd-sdk-svg
                      v-if="period.periodStatus"
                      class="mr16 mt2 ml14"
                      width="16px"
                      height="16px"
                      icon-class="o2ocompleted"
                    />
                  </div>
                </div>
                <!-- 遍历任务 -->
                <div
                  v-for="(group, j) in period.children"
                  :key="j"
                >
                  <div v-show="period.actived" class="ml8" :class="hasFolder === 1&&j === period.children.length-1?'mb32':''">
                    <!-- 遍历任务组 -->
                    <div
                      v-if="group.itemType === ITEM_TYPE.GROUP"
                      class="mt24"
                    >
                      <div class="layout-flex mr48 hand flex-space-between" :class="hasFolder === 1?'ml32':'ml24'" @click="collapSingPeriod(group)">
                        <span><yxt-ulcd-sdk-svg
                          class="mr4 mt3 hand"
                          module="o2o"
                          :class="[{'o2oPlayFrame-task-svg':!group.actived}]"
                          width="16px"
                          height="16px"
                          icon-class="tree-expand-icon"
                        />
                        </span>
                        <div class="flex-1"> <yxtf-tooltip
                          class="item"
                          :open-filter="true"
                          :content="group.refName"
                          :open-delay="2000"
                          placement="top"
                        >
                          <span class="ulcdsdk-ellipsis-2 group-title weight-bold ulcdsdk-break">{{ group.refName }}</span>
                        </yxtf-tooltip>
                        </div>

                      </div>
                      <template v-if="group.actived">
                        <div
                          v-for="(task ,k) in group.children"
                          :key="k"
                        >
                          <task
                            :group-detail="task"
                            :chapter-list="chapterList"
                            :active-id="activeId"
                            :elective-text="designerFuncConfig.electiveText"
                            :compulsory-text="designerFuncConfig.compulsoryText"
                            :has-folder="hasFolder"
                            :task-type="2"
                            @onclickTask="onclickTask"
                            @onclickKng="clickKng"
                          />
                        </div>
                      </template>
                    </div>
                    <div v-else>
                      <task
                        :group-detail="group"
                        :chapter-list="chapterList"
                        :task-type="1"
                        :active-id="activeId"
                        :elective-text="designerFuncConfig.electiveText"
                        :compulsory-text="designerFuncConfig.compulsoryText"
                        :has-folder="hasFolder"
                        @onclickTask="onclickTask"
                        @onclickKng="clickKng"
                      />
                    </div>
                  </div>
                </div>
              </div>

            </yxtf-scrollbar>
          </div>
        </div>
      </aside>
    </transition>
    <header class="o2oPlayFrame-right" :class="{'o2oPlayFrame-rightwidth':leftshow&&!isFullScreen&&!leftpa, 'pr z-1000':fixedTransEnd}">
      <div v-show="!isFullScreen" class="title headerborder pr24">
        <div v-if="!leftshow||leftpa" class="ml16 yxtulcdsdk-flex-center-center">
          <yxt-ulcd-sdk-svg
            class="hand"
            width="20px"
            height="20px"
            icon-class="o2ogoleftnav"
            @click.native="setConfirmgoBack"
          />
          <span class="pl8 hand  flex-1" @click="setConfirmgoBack">{{ $t('pc_o2o_lbl_back').d('返回') }}</span>
          <span
            class="mr32 ml32 o2oPlayFrame-divider-vertical"
          ></span>
          <yxtf-tooltip
            placement="bottom"
            effect="dark"
            :content="$t('pc_ulcdsdk_uacd_lbl_fixedtrainingoutline'/* 固定活动大纲 */)"
          >
            <yxt-ulcd-sdk-svg
              class="hand o2oPlayFrame-collapse-ico"
              width="24px"
              height="24px"
              icon-class="o2ocollapseoutline1"
              @mouseenter.native="leftEnter"
              @click.native="fixedOutline"
            />
          </yxtf-tooltip>
        </div>
        <div class="ml24 flex-1 w0">
          <div class="yxtulcdsdk-flex-center">
            <yxtf-tooltip
              class="item"
              :open-filter="true"
              :open-delay="2000"
              :content="projectTaskDetail.name"
              placement="bottom"
            >
              <div class="ellipsis">{{ projectTaskDetail.name }}</div>
            </yxtf-tooltip>
          </div>
          <uacd-breadcrumb
            :component-name="componentName"
            @setBackTask="setBackTask"
          />
        </div>
        <div class="ml80"> <yxtf-button
                             plain
                             icon="yxtf-icon-arrow-left"
                             @click="playPrevious()"
                           >{{ $t('pc_ulcdsdk_lbl_previous' /* 上一个 */) }}</yxtf-button>
          <yxtf-button
            class="ml12"
            plain
            @click="playNext()"
          >{{ $t('pc_ulcdsdk_lbl_next' /* 下一个 */) }}<i class="yxtf-icon-arrow-right yxtf-icon--right"></i></yxtf-button></div>
      </div>
    </header>
    <main class="o2oPlayFrame-right" :class="[{'h100p':isFullScreen},{'o2oPlayFrame-rightwidth':leftshow&&!isFullScreen&&!leftpa},{'o2oPlayFrame-right__wrap': !isFullScreen}]" @mouseenter="leftpa ? leftLeave() : null">
      <div class="main" :class="[{'p24':!['yxt-ulcd-sdk-examing', 'yxt-ulcd-sdk-course-page','yxt-ulcd-sdk-practicing','yxt-ulcd-sdk-o2o-offline-task'].includes(componentName)&&!isFullScreen}]">
        <!-- 锁定 -->
        <uacd-unlocked-or-error
          v-if="studyStatus !== STUDY_STATUS.NORMAL"
          :remark="remark"
          :study-status="studyStatus"
          :study-start-time="studyStartTime"
        />
        <div v-else-if="!componentName" v-loading="true" class="wline hline"></div>
        <components
          :is="componentName"
          v-else-if="activeComponentId"
          :id="params.kngId"
          :key="activeComponentId"
          :ref="componentName"
          :params="params"
          :target="params"
          :task-id="activeId"
          :project-id="projectId"
          :inner="true"
          :is-course="false"
          :radius="false"
          :auto-play="autoPlay"
          is-uacd
          @fullScreen="fullScreen"
          @updateProgress="updateProgress"
          @needCloseCatalog="needCloseCatalog"
        />
      </div>
    </main>
    <play-chapters-complete-dialog
      :visible.sync="chaptersCompleteVisible"
      :chapter-name="chapterName"
      :is-chapter-for-next="isChapterForNext"
      :designer-func-config="designerFuncConfig"
      @playNextChapter="playNextChapter"
    />
  </div>
</template>

<script>
import Svg from 'packages/_components/svg.vue';
import inject from 'packages/course-page/mixins/channel/inject';
import task from 'packages/uacd-play-frame/src/components/task.vue';
import { dateFormat } from 'packages/_utils/core/date-format';
import UacdUnlockedOrError from 'yxt-ulcd-sdk/packages/uacd-play-frame/src/components/uacdUnlockedOrError.vue';
import UacdBreadcrumb from 'yxt-ulcd-sdk/packages/uacd-play-frame/src/components/uacdBreadcrumb.vue';
import UacdProgress from 'yxt-ulcd-sdk/packages/uacd-play-frame/src/components/uacdProgress.vue';
import playChaptersCompleteDialog from 'yxt-ulcd-sdk/packages/uacd-play-frame/src/components/playChaptersCompleteDialog.vue';
import { getQueryString } from 'packages/_utils/core/utils.js';
import { getErrorMsg, sleep, delayTimeOut } from '../utils.js';
import {
  getStuDetail,
  getProjectBaseInfo,
  getDesignerFuncConfig,
  checkPermission,
  setRecentLearn, getProjectServiceUrl, getDesignerModulesConfig
} from '../service';
import {STUDY_STATUS, TASK_REG_IDS, TASK_REG_COMPONENTS} from '../const';

// 活动类型
const ITEM_TYPE = {
  TASK: 0, // 任务
  CATALOG: 1, // 章节、阶段
  GROUP: 2 // 任务组
};

export default {
  name: 'YxtUlcdSdkUacdPlayFrame',
  mixins: [inject],
  props: {
    projectId: {
      type: String,
      default: getQueryString('projectId') || ''
    },
    taskId: {
      type: String,
      default: getQueryString('taskId') || ''
    },
    actvRegId: {
      type: String,
      default: ''
    }
  },
  components: {
    playChaptersCompleteDialog,
    [Svg.name]: Svg,
    task,
    UacdProgress,
    UacdUnlockedOrError,
    UacdBreadcrumb
  },
  data() {
    return {
      leftshow: true,
      leftloading: true,
      leftpa: false,
      periodList: [],
      periodBakList: [], // 临时打平任务数据
      projectDetail: {}, // 项目详情
      projectTaskDetail: {name: '', index: ''}, // 当前任务信息
      designerFuncConfig: {
        electiveText: this.$t('pc_o2o_lbl_elective'),
        compulsoryText: this.$t('pc_o2o_lbl_obligatory')
      },
      designerModules: [], // 设计器模块配置
      studyStatus: STUDY_STATUS.NORMAL, // 学习状态，0正常 1锁定
      studyStartTime: '', // 开始学习时间
      chapterName: '', // 当前阶段名称
      chapterForNext: 0, // 下一阶段的索引
      isChapterForNext: true, // 是否已是最后一阶段
      chaptersCompleteVisible: false,
      hasFolder: 0, // 是否有阶段
      isLockTask: false, // 是否存在锁定任务
      isFullScreen: false, // 是否全屏
      activeId: '', //  任务选中
      activeComponentId: '',
      params: {}, // 组件传值
      hadPopupAutoMessage: false,
      bakTask: {},
      timeNum: 5,
      remark: '',
      componentName: '', //  组件名称 yxt-ulcd-sdk-examine
      autoPlay: false,
      transEnd: false,
      fixedTransEnd: false,
      leftHeight: '220px',
      chapterList: null,
      ITEM_TYPE,
      STUDY_STATUS,
      serverPath: '' // 服务端地址路径
    };
  },
  created() {
    if (this.actvRegId) {
      this.getProjectServiceUrl().then(res => {
        this.getDesignerFuncConfig();
        this.getProjectDetail();
      });
    }
  },
  beforeDestroy() {
    this.clearTimer();
  },
  methods: {
    // 处理有i18nKey和文本两个配置项合并取值显示
    getI18nValue(i18nKey, label, defaultLabel) {
      if (!i18nKey && !label) {
        return defaultLabel || '';
      }
      return i18nKey && this.$t(i18nKey) !== i18nKey ? this.$t(i18nKey) : label;
    },
    // 获取有自定义内容和默认内容的展示，比如类型、名称这种
    getCustomName(customName, defaultName) {
      return customName || defaultName;
    },
    // 获取服务地址
    async getProjectServiceUrl() {
      let uacdService;
      const token = localStorage.getItem('token');
      try {
        uacdService = JSON.parse(localStorage.getItem('uacdService'));
        if (!uacdService || !uacdService.token) {
          uacdService = {
            token
          };
        }
      } catch (e) {
        uacdService = {
          token
        };
      }
      const newTimestamp = new Date().getTime();
      if (token && uacdService.token === token && uacdService[this.actvRegId] && uacdService.timestamp && newTimestamp - uacdService.timestamp < 1000 * 60 * 60) {
        // 请求过
        this.serverPath = uacdService[this.actvRegId];
      } else {
        await getProjectServiceUrl({
          regId: this.actvRegId
        }).then(res => {
          this.serverPath = res.data;
          uacdService[this.actvRegId] = res.data;
          uacdService.token = token;
          uacdService.timestamp = new Date().getTime();
          localStorage.setItem('uacdService', JSON.stringify(uacdService));
        }).catch((err) => {
          this.leftloading = false;
          this.studyStatus = STUDY_STATUS.UKNOW;
          this.remark = err.message;
          this.$message.error(err.message);
        });
      }
    },
    // 知识大纲回调
    onChapterDataLoaded(data, courseId) {
      if (this.projectTaskDetail && this.projectTaskDetail.targetId === courseId) {
        let datas = data.map(d => {
          d.actived = false;
          return d;
        });
        this.chapterList = {
          courseId,
          isShow: true,
          data: datas
        };
      } else {
        this._changeChapter(false);
      }
    },
    // 知识大纲回调 课件变化
    onKngChange(kngId, courseId) {
      if (this.chapterList && this.chapterList.courseId === courseId) {
        this.$set(
          this.chapterList,
          'kngId',
          kngId
        );
      }
    },
    // 知识大纲回调 课程组件开始初始化
    onChapterDataStart(courseId) {
      this._changeChapter(true);
    },
    // 知识大纲回调 课程大纲数据加载失败
    onChapterDataError(courseId) {
      this._changeChapter(false);
    },
    _changeChapter(isShow) {
      this.chapterList = {
        isShow: isShow
      };
    },
    _isKngCourse() {
      return this.chapterList && this.chapterList.isShow;
    },
    formatDate: dateFormat,
    //  收起大纲
    collapSingPeriod(item) {
      item.actived = !item.actived;
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        this.$fmessage.closeAll();
      }
    },
    onclickTask(task) {
      if (this.$refs['yxt-ulcd-sdk-examing']) {
        this.bakTask = task;
        this.$refs['yxt-ulcd-sdk-examing'].confirmLeave(this.setBackTask);
      } else if (this.$refs['yxt-ulcd-sdk-o2o-homework-task']) {
        this.bakTask = task;
        this.$refs['yxt-ulcd-sdk-o2o-homework-task'].confirmLeave(this.setBackTask);
      } else if (this.$refs['yxt-ulcd-sdk-discuss-task']) {
        this.bakTask = task;
        this.$refs['yxt-ulcd-sdk-discuss-task'].confirmLeave(this.setBackTask);
      } else {
        this.setTask(task);
      }
    },
    setBackTask(leave) {
      if (leave) {
        this.setTask(this.bakTask, false, true);
      }
    },
    async setTask(task, isFirst = false, isBack = false) {
      this.dealAeriodActived(task.id);
      this.autoPlay = !isFirst;
      this.clearTimer();
      this.hadPopupAutoMessage = false;
      this.projectTaskDetail = task;
      this.clearTaskStudy();
      this.activeId = task.id;
      this.activeComponentId = task.id;
      this.checkTaskStudy(task);
      const {name, params, query} = this.$route;
      this.$router.replace({name, params, query: {...query, taskId: task.id}}).catch(err => err);
    },
    // 重置学习变量
    clearTaskStudy() {
      this.chapterList = null;
      this.activeId = '';
      this.activeComponentId = '';
      this.remark = '';
      this.studyStatus = STUDY_STATUS.NORMAL;
      this.studyStartTime = '';
      this.componentName = '';
    },
    checkTaskStudy(task) {
      // 目前没有解锁逻辑
      this.goTaskComponent(task);
    },
    goTaskComponent(task) {
      let { refRegId, id: tid, refId, trackId } = task;
      let pid = this.projectId;
      const batchId = ''; // 目前没有重学和多班次逻辑

      checkPermission(this.serverPath, this.projectId, tid, this.actvRegId).then((res) => {
        let { code, message } = res;
        if (code === 0) {
          // 后端记录学员最近学习的任务
          setRecentLearn(this.serverPath, pid, tid, this.actvRegId).then(() => {
            const findModule = this.designerModules.find(module => module.registryId === refRegId);
            let componentName = findModule && findModule.studyComponent;
            const params = {
              trackId
            };
            if (refRegId === TASK_REG_IDS.ACTV_EXAM) {
              // 考试有特殊参数
              params.arrangeId = refId;
              params.masterId = pid;
              params.masterType = 6; // （1-项目 2-知识 3-练习 4-人才发展 6 项目模型）
              params.btid = batchId;
            }
            if (!componentName) {
              // 兜底
              componentName = TASK_REG_COMPONENTS[refRegId];
            }
            this.params = params;
            this.componentName = componentName;
          });
        } else {
          let msg = getErrorMsg(code, message, this.projectDetail.actvStatus);
          this.studyStatus = STUDY_STATUS.UKNOW;
          this.remark = msg;
        }
      }).catch(e => {
        this.$message.error(e.message);
        this.studyStatus = STUDY_STATUS.UKNOW;
        this.remark = e.message;
      });
    },
    goBack(leave) {
      if (!leave) {
        return;
      }
      if (window.history.length > 1) {
        window.history.back(-1);
      } else if (window.opener) {
        window.location.href = opener.location.href;
      } else {
        this.goDetail();
      }
    },
    goDetail() {
      // todo 怎么回退
      // if (this.appCode === 'o2o') {
      //   window.location.href = `${window.location.origin}/o2o/#/project/detail/${this.projectDetail.projectId}`;
      // } else {
      //   window.location.href = `${window.location.origin}/gwnl/#/web/detail/${this.projectDetail.projectId}/trans`;
      // }
    },
    setConfirmgoBack() {
      if (this.$refs['yxt-ulcd-sdk-examing']) {
        this.$refs['yxt-ulcd-sdk-examing'].confirmLeave(this.goBack);
      } else if (this.$refs['yxt-ulcd-sdk-o2o-homework-task']) {
        this.$refs['yxt-ulcd-sdk-o2o-homework-task'].confirmLeave(this.goBack);
      } else {
        this.goBack(true);
      }
    },
    checkPreviousDisable() {
      return this.activeId === this.firstId();
    },
    checkNextDisable() {
      return this.activeId === this.lastId();
    },
    firstId() {
      return this.periodBakList && this.periodBakList.length > 0 ? this.periodBakList[0].id : '';
    },
    lastId() {
      return this.periodBakList && this.periodBakList.length > 0 ? this.periodBakList[this.periodBakList.length - 1].id : '';
    },
    // 下一节
    playNext() {
      function _playThisTask(that) {
        let thisIndex = that.periodBakList.findIndex((value)=>value.id === that.activeId);
        if (thisIndex >= that.periodBakList.length - 1) {
          that.$fmessage({
            message: that.$t('pc_ulcdsdk_lbl_theendone'/* 当前已是最后一个 */),
            type: 'warning'
          });
          return;
        }
        that.playThisTaskInfo = that.periodBakList[thisIndex + 1];
        that.playThisTask(true, 2);
      }
      if (this._isKngCourse()) {
        this.nextKng().then(res => {
        }).catch(() => {
          _playThisTask(this);
        });
      } else {
        _playThisTask(this);
      }
    },
    // 上一节
    playPrevious() {
      function _playThisTask(that) {
        let thisIndex = that.periodBakList.findIndex((value)=>value.id === that.activeId);
        if (thisIndex <= 0) {
          that.$fmessage({
            message: that.$t('pc_ulcdsdk_lbl_thefirstone'/* 当前已是第一个 */),
            type: 'warning'
          });
          return;
        }
        that.playThisTaskInfo = that.periodBakList[thisIndex - 1];
        that.playThisTask(true, 1);
      }
      if (this._isKngCourse()) {
        this.prevKng().then(res => {

        }).catch(() => {
          _playThisTask(this);
        });
      } else {
        _playThisTask(this);
      }
    },
    playThisTask(ischeck, num = 0) {
      this.onclickTask(this.playThisTaskInfo);
    },
    // 学习下一阶段
    playNextChapter() {
      this.chaptersCompleteVisible = false;
      this.onclickTask(this.periodBakList[this.chapterForNext + 1]);
    },
    // 是否全屏
    fullScreen(isFullScreen) {
      this.isFullScreen = isFullScreen;
    },
    afterLeave() {
      this.transEnd = true;
    },
    beforeEnter() {
      this.transEnd = false;
    },
    leftEnter() {
      this.fixedTransEnd = true;
      if (this.transEnd) {
        this.leftshow = true;
        this.leftpa = true;
      }
    },
    leftLeave() {
      if (!this.leftpa) return;
      this.leftshow = false;
      this.leftpa = false;
      if (this.transEnd) {
        this.fixedTransEnd = false;
      }
    },
    fixedOutline() {
      this.leftshow = true;
      this.leftpa = false;
      this.fixedTransEnd = false;
    },
    getDesignerFuncConfig() {
      return new Promise((resolve, reject) => {
        Promise.all([getDesignerFuncConfig(this.serverPath, this.actvRegId), getDesignerModulesConfig(this.serverPath, this.actvRegId)]).then(res => {
          if (res[0]) {
            const designerConfig = res[0];
            const requiredElectiveConfig = res[0].requiredElectiveConfig;
            const catalogConfig = res[0].catalogConfig;
            designerConfig.electiveText = this.getI18nValue(requiredElectiveConfig.electiveAliasI18n, requiredElectiveConfig.electiveAlias, this.$t('pc_o2o_lbl_elective' /* 选修 */));
            designerConfig.compulsoryText = this.getI18nValue(requiredElectiveConfig.requiredAliasI18n, requiredElectiveConfig.requiredAlias, this.$t('pc_o2o_lbl_obligatory' /* 必修 */));
            designerConfig.computedPeriodName = this.getI18nValue(catalogConfig[0].catalogNameI18n, catalogConfig[0].catalogName, this.$t('pc_o2o_lbl_period'/* 阶段 */));
            this.designerFuncConfig = designerConfig;
          }
          if (res[1]) {
            this.designerModules = res[1].datas;
          }
          resolve();
        }).catch((err) => {
          reject(err);
        });
      });
    },
    showMsg(isAutoPlay, hadComplete = false) {
      if (isAutoPlay && !this.checkNextDisable()) {
        if (this.hadPopupAutoMessage) return;
        this.hadPopupAutoMessage = true;
        this.timeNum = 5;
        const h = this.$createElement;
        const messageContent = [
          h('span', {class: 'ulcd_o2ospantimewarning'}, this.timeNum),
          h('span', {class: 'ml4'}, this.$t('pc_ulcdsdk_uacd_btn_skiptothenexttask', [this.$t('pc_ulcd_default_activity')]/* s 后跳转下个{0} */)),
          h('span', {
            class: 'ml32 color-primary-6 hand',
            on: {
              click: this.clearTimer
            }
          }, this.$t('pc_ulcdsdk_lbl_canceljump'/* 取消跳转 */))
        ];
        if (!hadComplete) {
          messageContent.unshift(h('span', {class: 'mr2'}, this.$t('pc_ulcdsdk_uacd_lbl_completedcurrenttasko2o', [this.$t('pc_ulcd_default_activity')]/* 您已完成当前{0}， */)));
        }
        this.$fmessage({
          message:
          h('p', null, messageContent),
          showClose: false,
          duration: 5000,
          type: 'success'
        });

        this.timer = setInterval(() => {
          --this.timeNum;
          if (this.timeNum === 0) {
            this.playNext();
            this.clearTimer();
          } else {
            if (document.querySelector('.ulcd_o2ospantimewarning')) {
              document.querySelector('.ulcd_o2ospantimewarning').innerHTML = this.timeNum;
            }
          }
        }, 1000);
      } else {
        this.$fmessage({
          message: this.$t('pc_ulcdsdk_uacd_lbl_havecompletedthecurrenttask', [this.$t('pc_ulcd_default_activity')]/* 太棒了，您已完成当前{0}！ */),
          type: 'success'
        });
      }
    },
    // 	完成状态(0未开始，1进行中，2已完成，3已逾期，4已逾期完成)
    async updateProgress(status, isAutoPlay = false) {
      if (status === 3) {
        status = 2; // 老版本2和3都会处理成已完成，此处兼容下组件
      }
      if (this.projectTaskDetail.resultStatus > status || status < 2) {
        return;
      }

      if ((this.projectTaskDetail.resultStatus !== status && status === 2)) {
        this.getUnlockTask();

        this.showPeriodComplete();
        if (this.projectTaskDetail.id !== this.lastId && !this.chaptersCompleteVisible) {this.showMsg(isAutoPlay);}
        this.projectTaskDetail.resultStatus = status;

        this.dealPeriodStatus();
        await sleep(delayTimeOut);
        this.$refs.o2oprogress.getFinishedProgress();
      } else {
        this.dealPeriodStatus();
        return;
      }
    },
    dealPeriodStatus() {
      if (this.hasFolder) {
        const linDetailBakList = this.periodBakList.filter(item => (item.periodId === this.projectTaskDetail.periodId));
        const linDetailBak = linDetailBakList.filter(item => (item.resultStatus !== 2));
        let periodInfo = this.periodList.find(item => (item.id === this.projectTaskDetail.periodId));
        if (linDetailBak && linDetailBak.length === 0) {
          periodInfo.periodStatus = 1;
        } else {
          periodInfo.periodStatus = 0;
        }
      }
    },
    showPeriodComplete() {
      if (this.hasFolder) {
        const linDetailBakList = this.periodBakList.filter(item => (item.periodId === this.projectTaskDetail.periodId));
        const linDetailBak = linDetailBakList.filter(item => (item.resultStatus !== 2));
        if (linDetailBak && linDetailBak.length === 1) {
          let _lastID = '';
          linDetailBakList.forEach((item, index)=>{
            if (index === linDetailBakList.length - 1) {
              _lastID = item.id;
            }
          }) ;
          this.chapterForNext = this.periodBakList.findIndex((value)=>value.id === _lastID);
          this.isChapterForNext = !(this.chapterForNext === this.periodBakList.length - 1);
          this.chapterName = this.projectTaskDetail.periodName;
          this.chaptersCompleteVisible = true;
        }
      } else {
      }
    },
    getProjectDetail() {
      getProjectBaseInfo(this.serverPath, this.projectId, this.actvRegId).then(res => {
        this.projectDetail = res;
        this.getTreeDetaul();
      }).catch(e => {
        this.leftloading = false;
        this.studyStatus = STUDY_STATUS.UKNOW;
        this.remark = e.message;
      });
    },
    getTreeDetaul() {
      getStuDetail(this.serverPath, this.projectId, this.actvRegId).then(res => {
        this.leftloading = false;
        this.hasFolder = res.hasFolder;
        this.periodList = res.datas.map((period, index) => {
          period.actived = false;
          period.periodStatus = period.passRate === 100; // 按进度计算阶段是否完成
          (period.children || []).map(sub => {
            // 任务组
            if (sub.itemType === ITEM_TYPE.GROUP) {
              sub.actived = false;
              this.periodBakList.push(...sub.children.map(value => {
                this.getLockStatus(value);
                value.locked = period.locked;
                value.periodId = period.id;
                value.periodName = period.refName;
                value.computedName = this.getCustomName(value.itemName, value.refName); // 优先取别名
                value.computedTypeName = this.getCustomName(value.actvAlias, this.$t(value.typeNameKey) || value.typeNameKey); // 优先取别名
                return value;
              }));
            } else {
              this.getLockStatus(sub);
              sub.locked = period.locked;
              sub.periodName = period.refName;
              sub.periodId = period.id;
              sub.computedName = this.getCustomName(sub.itemName, sub.refName); // 优先取别名
              sub.computedTypeName = this.getCustomName(sub.actvAlias, this.$t(sub.typeNameKey) || sub.typeNameKey); // 优先取别名
              this.periodBakList.push(sub);
            }
            return sub;
          });
          return period;
        });
        const linDetailBak = this.periodBakList.find(item => (item.id === this.taskId));
        if (linDetailBak) {
          this.setTask(linDetailBak, true);
        } else if (this.periodBakList.length) {
          this.setTask(this.periodBakList[0], true);
        } else {
          this.leftloading = false;
          this.studyStatus = STUDY_STATUS.EMPTY;
        }
      }).catch(e => {
        this.leftloading = false;
        this.$message.error(e.message);
        this.studyStatus = STUDY_STATUS.UKNOW;
        this.remark = e.message;
      });
    },
    // 处理默认选择状态
    dealAeriodActived(taskId) {
      this.periodList.forEach(period => {
        period.children.forEach(sub => {
          // 任务组
          if (sub.itemType === ITEM_TYPE.GROUP) {
            sub.children.forEach(value => {
              if (taskId === value.id) {
                period.actived = true;
                sub.actived = true;
              }
              return value;
            });
          } else {
            if (taskId === sub.id) {
              period.actived = true;
            }
          }
        });
      });
    },
    getLockStatus(task) {
      // todo 暂时没有解锁逻辑
      // if (task.status === 1) {
      //   this.isLockTask = true;
      // }
    },
    // 解除锁定状态
    getUnlockTask() {
      if (!this.isLockTask) {
        return;
      }
      this.getAllTaskProgress();
    },
    async getAllTaskProgress() {
      await sleep(delayTimeOut);
      let res = await getStuDetail(this.projectId);
      res.datas.map(period => {
        period.children.map(sub => {
          // 任务组
          if (sub.itemType === ITEM_TYPE.GROUP) {
            sub.children.map(gc => {
              this.periodBakList.map(value => {
                if (value.id === gc.id && value.resultStatus < 2) {
                  value.resultStatus = gc.resultStatus;
                  value.startTime = gc.startTime;
                }
                return value;
              });
            });
          } else {
            this.periodBakList.map(value => {
              if (value.id === sub.id && value.resultStatus < 2) {
                value.resultStatus = sub.resultStatus;
                value.startTime = sub.startTime;
              }
              return value;
            });
          }
        });
      });

    },
    needCloseCatalog() {
      this.leftshow = false;
    }
  }
};
</script>
