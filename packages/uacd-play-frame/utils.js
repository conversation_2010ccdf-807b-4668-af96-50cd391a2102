import { commonUtil } from 'yxt-biz-pc';

export const getErrorMsg = (code, message, status) =>{
  const i18n = commonUtil.i18n;
  /**
   "1：您不是这个项目的学员；"
   "2：阶段锁定；"
   "3：项目未到开始时间，开始时间：{message}" // 已废弃
   "4：任务未到开始时间，开始时间：{message}"
   "5：前面存在不合格的任务；"
   "6：前面存在未完成的任务；"
   "7：项目未发布；"
   "8：任务未找到；"
   "9：使用平台移动端/H5进行签到（签退）；"
   "10：签到已结束，未签到（签退）；"
   "11：{yyyy-MM-dd HH:mm:ss} 已签到（迟到签到 / 签退 / 早退）；"
   "12：阶段未到解锁时间（message有阶段解锁时间）；"
   "13：任务未到解锁时间，开始时间：{message}"
   "14：项目已结束；"
   "16:周期模式项目未到开始时间
   "20：管理员还没为您分配班次,请耐心等待-多班次"
   "21：项目归档"
   */
  switch (code) {
    case 1:
      return message;
    case 7:
      return i18n.t('pc_o2o_msg_no_release' /* 项目未发布 */);
    case 14:
    case 21:
      return i18n.t('pc_o2o_msg_projectend' /* 项目已结束 */);
    case 20:
      return i18n.t(message);
    default:
      window.location.href = window.location.origin + '/#/error/403.html';
  }
};

export const delayTimeOut = 1 * 1000;

export const sleep = (timeout) =>
  new Promise((resolve) => setTimeout(resolve, timeout));

export const getPeriodStatus = (periodStatus) => {
  let svgname = 'o2onotstarted';
  switch (periodStatus) {
    case 0:
      svgname = 'o2olock';
      break;
    case 2:
      svgname = 'o2oprogress';
      break;
    case 3:
      svgname = 'o2ocompleted';
      break;
  }
  return svgname;
};
