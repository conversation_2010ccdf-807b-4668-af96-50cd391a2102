import {o2oApi, apiBaseUrlApi} from 'packages/api';
// 完成其他任务
export const putFinishOther = data => {
  return o2oApi.put('/track/other', data);
};
// 获取uacd外链详情（测试接口）
export const getUacdLinkDetail = (actvId, itemId) => {
  return apiBaseUrlApi.post(`/aomproj/aom/activities/${actvId}/items/${itemId}/detail`);
};
// 完成外链（测试接口）
export const finishUacdLink = (actvId, itemId) => {
  return apiBaseUrlApi.post('aomproj/aom/activities/item/complete', {
    actvId,
    itemId
  });
};
