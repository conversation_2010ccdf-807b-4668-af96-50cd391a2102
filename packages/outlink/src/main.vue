<!--
 * 任务详情 - 外部链接
-->
<template>
  <div
    v-loading="loading"
    class="yxtulcdsdk-outlink o2oplayframe-task-container yxtulcdsdk-ulcdsdk"
  >
    <task-detail-container>
      <template slot="title">
        <yxtf-tooltip
          class="item"
          :open-filter="true"
          :content="task.name"
          placement="top"
        >
          <span class="ulcdsdk-ellipsis-2 ulcdsdk-break"> {{ task.name }}</span>
        </yxtf-tooltip>
      </template>
      <div v-if="isGetDetail">
        <div v-if="task.id">
          <div
            v-if="task.description"
            class="standard-size-16 break color-gray-7 mt8"
          >
            <wrap-line
              v-if="task.description"
              line-height="22px"
              :content="task.description"
              :hide-on-extend="hideOnExtend"
              :row="row"
              pre
              class="standard-size-16 color-gray-9"
            >
              <span
                slot="right-icon"
                class="v-top ml2 color-primary hand"
                @click="extendMore"
              >
                {{ hideOnExtend ? $t('pc_o2o_btn_expand') : $t('pc_o2o_btn_stow') }}<i :class="hideOnExtend?'yxt-icon-arrow-down':'yxt-icon-arrow-up'"></i>
              </span>
            </wrap-line>
          </div>
        </div>
        <div class="text-center mt48">
          <yxtf-button
            type="primary"
            size="larger"
            @click="goTask"
          >
            {{ $t('pc_ulcdsdk_btn_goimmediate'/**立即前往 */) }}
          </yxtf-button>
        </div>
      </div>
    </task-detail-container>
  </div>
</template>

<script>
import {finishUacdLink, getUacdLinkDetail, putFinishOther} from '../service.js';
import mixin from '../../_mixins/task.js';
import scrollTop from '../../_mixins/scroll-top';
import taskDetailContainer from '../../_components/task-detail-container.vue';
import wrapLine from '../../_components/wrap_line.vue';
export default {
  name: 'YxtUlcdSdkOutlink',
  mixins: [mixin, scrollTop],
  components: { taskDetailContainer, wrapLine },
  props: {
    isUacd: Boolean,
    projectId: String
  },
  data() {
    return {
      hideOnExtend: true,
      row: 5
    };
  },
  created() {
    if (this.isUacd) {
      if (this.preview) return;
      this.getUacdLinkDetail();

      this.loading = false;
      this.isGetDetail = true;
    } else {
      this.init();
    }
  },
  methods: {
    // 跳转其他任务
    async  goTask() {
      if (!this.task.taskResult || this.task.taskResult.length === 0 || this.task.taskResult[0].status === 0) {
        if (this.isUacd) {
          await finishUacdLink(this.projectId, this.taskId);
        } else {
          // 更改任务状态
          await putFinishOther({ id: this.taskId });
        }
        this.$emit('updateProgress', 3);
      }
      window.open(this.task.linkUrl);
    },
    extendMore() {
      this.row = this.row ? 0 : 5;
      // 展开更多
      this.hideOnExtend = !this.hideOnExtend;
    },
    getUacdLinkDetail() {
      this.workLoading = true;
      getUacdLinkDetail(this.projectId, this.taskId).then(res => {
        this.pid = this.projectId;
        this.task = {
          name: '外链测试数据',
          id: this.taskId,
          description: '测试数据的描述',
          linkUrl: 'https://' + 'pro-phx.yunxuetang.com.cn',
          taskResult: [{
            status: res.status
          }]
        };
      });

    }
  }
};
</script>
