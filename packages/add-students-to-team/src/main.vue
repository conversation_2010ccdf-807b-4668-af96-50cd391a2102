<template>
  <div class="yxtulcdsdk-add-students-to-team yxtulcdsdk-ulcdsdk">
    <div v-if="showTip" class="yxtulcdsdk-add-students-to-team__tip mb24">
      <i class="yxt-icon-warning-outline standard-size-16 color-gray-6"></i>
      <div class="ml4">
        <div>{{ $t('pc_kng_lbl_team_tip1'/** 1. 推荐给团队后，团队成员会新增一个学习计划，你可以在“我的团队”中查看团队成员的学习数据 */) }}</div>
        <div>{{ $t('pc_kng_lbl_team_tip2'/** 2. 推荐给团队后，团队成员会默认继承你的浏览权限 */) }}</div>
      </div>
    </div>
    <div>
      <span class="yxtulcdsdk-required">{{ $t('pc_kng_lbl_teamselectperson'/** 选择团队人员 */) }}</span>
      <yxt-button class="ml28" type="text" @click="showDialog">{{ $t('pc_kng_lbl_team_select'/** 去选择 */) }}</yxt-button>
    </div>
    <div class="yxtulcdsdk-add-students-to-team__people">
      <span class="yxtulcdsdk-add-students-to-team__selected">{{ $t('pc_kng_lbl_team_selectcount'/** 已选：{0}人 */,[list.length]) }}</span>
      <div class="yxtulcdsdk-add-students-to-team__selection">
        <yxt-scrollbar :fit-height="true">
          <check-list :data="list">
            <template slot-scope="scope">
              <yxt-tooltip
                v-for="item in scope.list"
                :key="item.id"
                placement="top"
              >
                <template slot="content">
                  <yxtbiz-user-name :name="item.fullname" />
                </template>
                <yxt-tag
                  class="mr5"
                  size="small"
                  type="info"
                  :disable-transitions="true"
                  closable
                  @close="deleteItem(item)"
                >
                  <yxtbiz-user-name :name="item.fullname" />
                </yxt-tag>
              </yxt-tooltip>
            </template>
          </check-list>
        </yxt-scrollbar>
      </div>
    </div>
    <div class="mt24">
      <span class="yxtulcdsdk-required">{{ $t('pc_kng_courseware_lbl_tab_end_time'/** 完成时间 */) }}</span>
    </div>
    <div class="yxtulcdsdk-add-students-to-team__time">
      <yxtf-radio-group v-model="radio" :direction="'row'" @change="radioChange">
        <yxtf-radio :label="3">
          <span>{{ $t('pc_kng_lbl_within3days'/** 3天内 */) }}</span>
          <span v-if="time && radio == 3" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="7">
          <span>{{ $t('pc_kng_lbl_within7days'/** 7天内 */) }}</span>
          <span v-if="time && radio == 7" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="14">
          <span>{{ $t('pc_kng_lbl_within14days'/** 14天内 */) }}</span>
          <span v-if="time && radio == 14" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="30">
          <span>{{ $t('pc_kng_lbl_within30days'/** 30天内 */) }}</span>
          <span v-if="time && radio == 30" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="0">
          <span>{{ $t('pc_kng_lbl_custom'/** 自定义 */) }}</span>
          <yxt-date-picker
            v-if="radio == 0"
            v-model="value"
            class="ml12"
            size="small"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            :placeholder="$t('pc_kng_lbl_selectdatetime'/** 选择日期时间 */)"
          />
        </yxtf-radio>
      </yxtf-radio-group>
    </div>

    <yxt-dialog
      :modal="false"
      width="960px"
      :title="$t('pc_kng_common_lbl_choosemen'/** 选择人员 */)"
      custom-class="yxtulcdsdk-add-students-to-team__dialog"
      :visible.sync="dialogVisible"
      show-back
      @back="closeDialog"
    >
      <YxtbizRangeSelector
        ref="rangeSelector"
        v-model="selectData"
        :tabs="tabs"
        model="array"
        :limit="1000"
      />
      <div slot="footer">
        <yxt-button plain @click="closeDialog">{{ $t('pc_ulcdsdk_cancel' /** 取消 */) }}</yxt-button>
        <yxt-button type="primary" @click="getUsers">{{ $t('pc_ulcdsdk_done'/** 确定 */) }}</yxt-button>
      </div>
    </yxt-dialog>
  </div>
</template>

<script>
import { planSave } from './../service.js';

import CheckList from './components/checkList';

import {addDay, formatDate, isBefore} from 'yxt-ulcd-sdk/packages/develop-view-plan/formateDate';

export default {
  name: 'YxtUlcdSdkAddStudentsToTeam',
  components: {
    CheckList
  },
  props: {
    kngId: {
      type: String,
      default: ''
    },
    teamId: {
      type: String,
      default: ''
    },
    showTip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      tabs: [{
        type: 'teamPersons',
        isSelectSelf: false, // 控制团队组件是否能选到自己
        teamId: this.teamId
        // teamId可以为以下特定字符, 表示特定的含义
        // teamId: deptManager', // 表示部门经理
        // teamId: lineManager', // 表示直属经理
      }],
      list: [],
      selectData: [],
      radio: 0,
      value: null,
      time: ''
    };
  },
  watch: {
    teamId: {
      immediate: true,
      handler(val) {
        this.tabs[0].teamId = val;
      }
    }
  },
  created() {
    this.radio = 3;
    this.finishTime = addDay(3, 'YYYY-MM-DD HH:mm:ss');
    this.time = addDay(3, 'YYYY-MM-DD HH:mm');
  },
  methods: {
    showDialog() {
      this.selectData = this.list;
      this.dialogVisible = true;
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    async getUsers() {
      const isValidate = await this.$refs.rangeSelector.validate();
      if (!isValidate) return;
      const data = await this.$refs.rangeSelector.getSelectDataArray();
      if (!data || data.length === 0) {
        this.$message.warning(this.$t('pc_kng_lbl_team_pleaseselect'/** 请选择人员！ */));
        return;
      }
      this.list = data || [];
      this.dialogVisible = false;
    },
    deleteItem(item) {
      this.list = this.list.filter(c => c.id !== item.id);
      this.selectData = this.list;
      console.log(this.selectData);
    },
    radioChange(val) {
      if (val === 0) {
        this.finishTime = '';
        this.time = '';
      } else {
        this.finishTime = addDay(val, 'YYYY-MM-DD HH:mm:ss');
        this.time = addDay(val, 'YYYY-MM-DD HH:mm');
      }
    },
    addStudents() {
      return new Promise(async(resolve, reject) => {
        if (this.list.length === 0) {
          this.$message.warning(this.$t('pc_kng_lbl_team_pleaseselect'/** 请选择人员！ */));
          return reject();
        }

        if (this.radio === 0 && this.value) {
          this.finishTime = formatDate(this.value, 'YYYY-MM-DD HH:mm:ss');
        }

        if (!this.finishTime) {
          this.$message.warning(this.$t('pc_kng_tip_settime'/** 请设置完成时间 */));
          return reject();
        }
        if (isBefore(this.finishTime)) {
          this.$message.warning(this.$t('pc_kng_team_timeerror'/** 完成时间不能小于当前时间 */));
          return reject();
        }

        try {
          const userIds = this.list.map(item => item.id);
          await planSave({
            kngId: this.kngId,
            leaderRecommend: 1, // 是否上级推荐 0：否 1：是
            planFinishTime: this.finishTime, // 计划完成时间，格式 yyyy-MM-dd HH:mm:ss
            userIds
          });
          this.list = [];
          return resolve();
        } catch (err) {
          if (err && err.key === 'apis.kng.self.study.plan.userid.error') {
            this.$message.error(this.$t('pc_kng_lbl_team_pleaseselect'/** 请选择人员！ */));
          } else {
            this.$message.error(err.message);
          }
          return reject();
        }
      });
    }
  }
};
</script>

