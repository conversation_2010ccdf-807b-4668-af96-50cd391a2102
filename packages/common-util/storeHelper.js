
/**
 * 检查导航编号下的权限点权限
 * @param {String} pageCode 页面编号
 * @param {String} version 操作编号
 */
let checkActionPermission = function(pageCode, actionCode, vm) {
  // debugger
  try {
    if (!vm) {
      vm = this;
    }
    if (!pageCode || !actionCode || !vm || !vm.$store || !vm.$store.state.navManageStore) {
      return false;
    }
    let per = false;
    let navsAll = vm.$store.state.navManageStore.navList;
    if (navsAll && navsAll.length > 0) {
      for (let index = 0; index < navsAll.length; index++) {
        const element = navsAll[index];
        if (element.code === pageCode &&
            element.pointActions &&
            element.pointActions.length > 0 &&
            element.pointActions.indexOf(actionCode) >= 0) {
          per = true;
          break;
        }
      }
    }
    return per;
  } catch (error) {
    // error
  }
  return false;
};

export default checkActionPermission;
