
const directives = {};

export const blurTrimDirective = {
  bind(el) { 
    const inputer = el.querySelector('input, textarea')
    
    inputer && inputer.addEventListener('blur', function(event) {
      if (!inputer) return
      const trimVal = inputer.value && inputer.value.trim();
      if (trimVal !== inputer.value) {
        inputer.value = trimVal;
        inputer.dispatchEvent(new Event('input'));
      }
    }, true) 
  }
}

directives.install = Vue => {
  // blur时执行trim处理，去除首尾空格
  Vue.directive('blur-trim', blurTrimDirective);
};
export default directives;
  