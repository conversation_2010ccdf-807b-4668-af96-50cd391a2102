import { enmuFunctions } from '../function-points/index';
import setTheme from './theme';
import checkActionPermission from './storeHelper';
import common from './utils';
import { setLanguage, getLanguage, i18n, getLangList } from './changeLangs';
import './devtools';
import YxtInit from './yxtInit';
import btnDirectives from './check-buttons';
import bindDirectives from './directives'
// import setCustomScript from './custom-script';
import {axiosReject, getErrorKeys} from './axios-reject';
// import Eco from './eco';

// 异步加载，因为资源是异步加载的加载完才能
const PCplayer = async() => {
  await common.loadPanguPlayer();
  return Promise.resolve(window.PanguPlayer && window.PanguPlayer.PCplayer);
} ;

export default {
  YxtInit,
  setTheme,
  checkActionPermission,
  common,
  setLanguage,
  getLanguage,
  i18n,
  getLangList,
  enmuFunctions,
  btnDirectives,
  bindDirectives,
  // setCustomScript,
  axiosReject,
  getErrorKeys,
  // Eco,
  AsyncPCplayer: PCplayer
};
