import { envNow } from 'yxt-ulcd-sdk/packages/api';

try {
  const DEV_ENV = ['dev', 'tcdev', 'stable', 'feature'];
  const TOOL_STORAGE_KEY = 'yxt_devtools_config';
  const MEDIA_BASE =
    'https://media-phx.yunxuetang.com.cn/common/basepc/yxt-devtools/lib/';
  const URL_TOOL_KW = 'yxtdevtool=';

  if (DEV_ENV.includes(envNow)) {
    const toolConfig = JSON.parse(
      window.localStorage[TOOL_STORAGE_KEY] || null
    );
    const yxtdevtool =
      window.location.search.indexOf(URL_TOOL_KW) > 0 ||
      window.location.hash.indexOf(URL_TOOL_KW) > 0;
    if (toolConfig || yxtdevtool) {
      window.Promise.all([
        new window.Promise((resolve, reject) => {
          let head = document.getElementsByTagName('head')[0];
          let link = document.createElement('script');
          link.type = 'text/javascript';
          link.src = MEDIA_BASE + 'index.js';
          link.onload = () => resolve();
          link.onerror = () => reject();
          head.appendChild(link);
        }),
        new window.Promise((resolve, reject) => {
          let body = document.getElementsByTagName('body')[0];
          let link = document.createElement('link');
          link.type = 'text/css';
          link.rel = 'stylesheet';
          link.href = MEDIA_BASE + 'theme-chalk/index.css';
          link.onload = () => resolve();
          link.onerror = () => reject();
          body.appendChild(link);
        })
      ]).then(() => {
        // if (toolConfig.pixelCompare) {
        //   window.YxtDevtools.initTools(['YxtDevtoolsPixelCompare']);
        // }
      });
    }
  }
} catch (error) {
  console.log('devtool load error:', error);
}
