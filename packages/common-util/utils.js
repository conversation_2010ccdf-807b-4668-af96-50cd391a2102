export default {
  loadScript(src) {
    return new window.Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.defer = true;
      script.src = src;
      script.onload = () => resolve();
      script.onerror = () => reject();
      document.head.appendChild(script);
    });
  },
  loadStyle(src) {
    return new window.Promise((resolve, reject) => {
      const head = document.getElementsByTagName('head')[0];
      const link = document.createElement('link');
      link.type = 'text/css';
      link.rel = 'stylesheet';
      link.href = src;
      link.onload = () => resolve();
      link.onerror = () => reject();
      head.appendChild(link);
    });
  },
  async loadPanguPlayer() {
    // eslint-disable-next-line yxtcom/only-cdn-domains, yxtcom/no-inner-domains
    if ((window.feConfig.apiEnv === 'dev' || 'di-hw-01') && !window.PAN_GU_PC_PATH) window.PAN_GU_PC_PATH = 'https://media-phx.yunxuetang.com.cn/pgp/';
    const PG_LOAD_SUCCESS = window.PanguPlayer && window.PanguPlayer.PCplayer;
    if (!PG_LOAD_SUCCESS && window.PAN_GU_PC_PATH) {
      // 加载静态资源
      await Promise.all([this.loadStyle(`${window.PAN_GU_PC_PATH}style.css`), this.loadScript(`${window.PAN_GU_PC_PATH}index.min.js`)]);
    }
    // 初始化
    window.PanguPlayer && window.PanguPlayer.Api.setConfig({
      domain: window.feConfig.common.apiBaseUrl + 'tcm/',
      source: 501
    });
    return Promise.resolve();
  }
};

