import {i18n, setLanguage as i18nSetLanguage} from 'yxt-i18n/es';
import { i18n as YxtPcI18n } from 'yxt-pc';

// 设置语言
const SetLanguage = () => {
  YxtPcI18n((key, value) => i18n.t(key, value));
  return i18nSetLanguage;
};
export const setLanguage = SetLanguage();

// export const getLanguage = yxtI18n.getLanguage; // 获取当前语言
// export const getLangList = yxtI18n.getLangList; // 获取语种列表
// export const saveLangKey = yxtI18n.saveLangKey; // 保存当前多语言翻译
// export const i18n = yxtI18n.i18n;

export { i18n, getLangList, getLanguage, saveLangKey } from 'yxt-i18n/es';

