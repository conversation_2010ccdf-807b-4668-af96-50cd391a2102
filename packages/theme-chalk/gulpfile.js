'use strict';

const { series, src, dest } = require('gulp');
const sass = require('gulp-sass')(require('sass'));
const autoprefixer = require('gulp-autoprefixer');
const cssmin = require('gulp-cssmin');
const postcss = require('gulp-postcss');
const rtlCss = require('postcss-rtlcss').postcssRTLCSS;

function compile() {
  return src('./src/*.scss')
    .pipe(sass.sync())
    .pipe(postcss([ rtlCss()]))
    .pipe(autoprefixer({
      overrideBrowserslist: ['ie > 9', 'last 2 versions'],
      cascade: false
    }))
    .pipe(cssmin())
    .pipe(dest('./lib'));
}

function copyfont() {
  return src('./src/fonts/**')
    .pipe(cssmin())
    .pipe(dest('./lib/fonts'));
}

function copyimg() {
  return src('./src/img/**')
    .pipe(dest('./lib/img'));
}

exports.build = series(compile, copyfont, copyimg);
