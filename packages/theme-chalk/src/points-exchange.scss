@import "mixins/mixins";
@import "common/var";

@include b(points-exchange) {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  background: #141414;

  &__inner {
    flex: 1;
    width: 100%;
    height: 0;
  }

  &__innerback {
    margin: 20px;
    overflow: hidden;
    background-color: #141414;
    border-radius: 8px;
  }

  &__cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &__back {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
  }
}

.exchange-credit {
  &__center {
    display: flex;
    margin-top: 16px;

    &--left {
      flex: 0 0 128px;
      width: 128px;
      height: 72px;
    }

    &--right {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }

  &__footer {
    margin-top: 55px;
  }
}
