@import "mixins/mixins";
@import "common/var";

@include b(add-students-to-team) {
  min-height: 290px;

  .yxtulcdsdk-required {
    position: relative;
  }

  .yxtulcdsdk-required::after {
    position: absolute;
    top: 8px;
    right: -8px;
    width: 4px;
    height: 4px;
    background-color: #f5222d;
    border-radius: 50%;
    content: " ";
  }

  @include e(tip) {
    display: flex;
    padding: 12px 8px;
    color: #595959;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    background: #fff9f1;
    border-radius: 4px;
  }

  @include e(people) {
    width: 100%;
    margin-top: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }

  @include e(selected) {
    margin: 8px 12px;
    color: #595959;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
  }

  @include e(selection) {
    height: 140px;
    padding: 0 4px;
  }

  .yxt-tag {
    width: 105px;
    margin-bottom: 4px;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
  }

  @include e(time) {
    display: flex;
    align-items: center;
    min-height: 30px;
  }

  @include e(dialog) {
    max-width: 100%;
    max-height: 100%;

    .yxt-dialog__body {
      height: calc(80vh - 56px - 64px);
    }
  }
}
