@import "./var.scss";

.yxtulcdsdk-ulcdsdk {
  // color相关
  .color-lightgrey,
  .color-75 {
    color: #757575;
  }

  .c-c0bfbe {
    color: #c0bfbe;
  }

  .c-fb {
    color: #bfbfbf;
  }

  .c-0-45 {
    color: rgba(0, 0, 0, 0.45);
  }

  .c-success {
    color: $--color-success;
  }

  .color-primary {
    color: var(--color-primary);
  }

  .c-f {
    color: #fff;
  }

  .c-f0 {
    color: $--color-f0;
  }

  .c-bf {
    color: $--color-bf;
  }

  .c-e9 {
    color: $--color-e9;
  }

  .color-59,
  .c-59 {
    color: $--color-59;
  }

  .text-43 {
    color: $--color-43;
  }

  .c-43-primary:hover {
    color: $--color-43;
  }

  .c-43-h:hover {
    color: var(--color-primary);  //修改#43f66的颜色根据主题色变换
  }

  .text-75 {
    color: $--color-75;
  }

  .c-a7 {
    color: #a7a7a7;
  }

  .c-ce {
    color: #cecece;
  }

  .c-b3 {
    color: #b3b3b3;
  }

  .c-a9 {
    color: #9a9a9a;
  }

  .text-fa5 {
    color: #fa5252;
  }

  .text-f52 {
    color: #f5222d;
  }

  .text-5c728 {
    color: #5cc728;
  }

  .color-d5 {
    color: #d5d5d5;
  }

  .color-26,
  .c-26 {
    color: $--color-main;
  }

  .c-8c {
    color: $--color-8c;
  }

  .text-warning {
    color: #fa8c16;
  }

  .color-8c {
    color: #8c8c8c;
  }

  // background-相关
  .bg-center {
    background-repeat: no-repeat;
    background-position: center;
  }

  .bg-cover {
    background-size: cover;
  }

  .bg-primary {
    background: var(--color-primary);
  }

  .bg-43 {
    background: $--color-43;
  }

  .bg-8c {
    background: #8c8c8c;
  }

  .bg-grey {
    background: #f6f7fa;
  }

  .bg-e9 {
    background-color: $--color-e9;
  }

  .bg-f52 {
    background-color: #f5222d;
  }

  .bg-f456 {
    background-color: $--color-f456;
  }

  .bg-fa {
    background-color: $--color-fa;
  }

  .bg-fb {
    background-color: $--color-fb;
  }

  .bg-f5 {
    background-color: #f5f5f5;
  }

  .bg-fff7e6 {
    background-color: #fff7e6;
  }

  .bg-edf1ff {
    background-color: #edf1ff;
  }

  .bg-ff4d4f {
    background-color: #ff4d4f;
  }

  .bg-fff1f0 {
    background-color: #fff1f0;
  }

  .bg-f5faf6 {
    background-color: #f5faf6;
  }

  .bg-fff9f1 {
    background-color: #fff9f1;
  }

  .bg-fff8f6 {
    background-color: #fff8f6;
  }

  .bg-d9 {
    background-color: #d9d9d9;
  }

  .bg-ff {
    background-color: $--color-ff;
  }

  .bg-transparent {
    background-color: transparent;
  }

  .hover-bg-f5 {
    &:hover {
      background-color: #f5f5f5;
    }
  }

  .hover-bg-fa {
    &:hover {
      background-color: #fafafa;
    }
  }

  .mask {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .bg-op5-f456 {
    background-color: rgba(#f4f5f6, .5);
  }

  // border-相关
  .border-default {
    border: 1px solid #e4e4e4;
  }

  .border-d9 {
    border: 1px solid #d9d9d9;
  }

  .border-f0 {
    border: solid 1px $--color-f0;
  }

  .border-b-f0 {
    border-bottom: solid 1px $--color-f0;
  }

  .border-error-f5 {
    border: 1px solid $--color-error;
  }

  .border-e9 {
    border: solid 1px #e9e9e9;
  }

  .border-b-e9 {
    border-bottom: solid 1px #e9e9e9;
  }

  .border-b-f5 {
    border-bottom: solid 1px #f5f5f5;
  }

  .border-b-d {
    border-bottom: 1px solid #ddd;
  }

  .border-l-f0 {
    border-left: solid 1px $--color-f0;
  }

  .border-l-e9 {
    border-left: solid 1px $--color-e9;
  }

  .border-r-f0 {
    border-right: solid 1px $--color-f0;
  }

  .border-b-8c {
    border-right: solid 1px $--color-8c;
  }

  .border-r-e9 {
    border-right: solid 1px $--color-e9;
  }

  .border-r-FA8C16 {
    border-right: 1px solid #fa8c16;
  }

  .border-color-f1 {
    border-color: #f1f1f1;
  }

  .hover-color-f5 {
    &:hover {
      background-color: #f5f5f5;
    }
  }
}
