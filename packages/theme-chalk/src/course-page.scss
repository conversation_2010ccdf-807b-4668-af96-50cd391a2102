@import "mixins/mixins";
@import "common/var";

@include b(course-page) {
  width: 100%;
  min-height: 100vh;

  .hover-opacity7 {
    cursor: pointer;
    opacity: 0.7;

    &:hover {
      opacity: 1;
    }
  }

  p {
    margin: 0;
  }

  .yxtulcdsdk-empty {
    min-height: calc(100vh - 64px);
  }

  @include m(short) {
    min-height: calc(100vh - 64px);

    @include e(top) {
      height: calc(100vh - 64px);
    }
  }

  @include e(top) {
    position: relative;
    height: 100vh;

    @include e(chapters) {
      position: absolute;
      top: 20px;
      bottom: 20px;
      left: 30px;
      z-index: 9;
      width: 360px;
      color: #fff;
      background-color: rgba($color: #000, $alpha: 0.6);
    }
  }

  @include e(info) {
    background-color: #f5f5f5;

    @include e(info-tab) {
      height: 64px;
      background-color: #fff;
      // box-shadow: 0 3px 6px 0 rgba($color: #000, $alpha: 0.1);

      .yxtf-tabs {
        // flex: 1;// ie下有问题
        width: 100%;
        max-width: 1384px;
      }

      @include e(info-tab-back) {
        margin-right: 12px;
        margin-left: 10px;
        padding: 4px;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          .color-gray-8 {
            color: var(--color-primary);
          }
        }
      }
    }

    @include e(info-left) {
      // flex: 1; // ie下有问题
      width: 100%;
      max-width: 984px;
      overflow: hidden;
      background-color: #fff;
    }

    @include e(info-right) {
      flex-shrink: 0;
      width: 312px;
    }

    @include e(intro-text) {
      @include m(close) {
        max-width: 800px;
        height: 800px;
        overflow: hidden;
      }
    }

    @include e(attachments) {
      @include e(attachments-cell) {
        width: 42.6%;
        height: 64px;
        margin-top: 4px;
        padding: 0 12px;
        background-color: #fafafa;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background-color: #f4f5f6;

          @include e(attachments-down) {
            display: flex;
          }
        }

        &:nth-child(2n+1) {
          margin-right: 24px;
        }

        @include e(attachments-down) {
          display: none;
          width: 24px;
          height: 24px;

          &:hover {
            background-color: #e9e9e9;
            border-radius: 2px;
          }
        }
      }
    }
  }

  @include e(chapter) {
    .yxtf-progress-bar__outer {
      min-width: 80px;
      background-color: rgb(238, 238, 238) !important;
    }

    @include e(chapter-item) {
      display: flex;
      align-items: center;
      height: 52px;
      padding: 0 20px;
      overflow: hidden;
      cursor: pointer;

      @include e(chapter-content) {
        display: none;
        align-items: center;
      }

      @include e(chapter-lock) {
        display: flex;
        align-items: center;
        padding: 4px;
      }

      @include m(hover) {
        @include e(chapter-content) {
          display: flex;
        }

        @include e(chapter-lock) {
          display: none;
        }
      }
    }
  }

  @include e(comment) {
    .yxt-rate {
      margin-top: 2px;

      .yxt-rate__text {
        margin-left: 12px;
        color: #fa8c16 !important;
      }
    }

    .yxtulcdsdk-mh-10 {
      margin-right: -10px;
      margin-left: -10px;
    }

    @include e(comment-cell) {
      align-items: start;
      padding: 32px 0;
      border-top: 1px solid #f0f0f0;

      @include m(model) {
        padding: 0;
        border-top: none;

        @include e(comment-reply-cell) {
          padding: 20px 0;
        }

        @include e(comment-reply-count) {
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid #f0f0f0;
        }
      }

      .min-width0 {
        min-width: 0;
      }

      @include e(comment-name) {
        max-width: calc(100% - 120px);
      }

      @include e(comment-hide) {
        opacity: 0;
      }

      &:hover {
        @include e(comment-hide) {
          opacity: 1;
        }
      }

      @include e(comment-reply-wrap) {
        position: relative;
        margin-top: 16px;
        overflow: hidden;
        background-color: #f5f5f5;
        border-radius: 4px;

        .yxtf-infinite-list {
          min-height: 96px;
          max-height: 450px;
        }

        &::before {
          position: absolute;
          top: -8px;
          left: 10px;
          border-right: 10px solid transparent;
          border-bottom: 10px solid #f5f5f5;
          border-left: 10px solid transparent;
          content: "";
        }

        @include e(comment-reply-more) {
          padding-left: 96px;
        }

        @include e(comment-reply-cell) {
          align-items: start;
          padding: 20px 32px;

          @include e(reply-hide) {
            opacity: 0;
          }

          &:hover {
            @include e(reply-hide) {
              opacity: 1;
            }
          }
        }
      }

      .replay-dialog {
        min-height: 640px;
      }
    }

    @include e(comment-input) {
      display: flex;
      flex-flow: column;
      height: 200px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      transition: border-color .2s cubic-bezier(.645, .045, .355, 1);

      &.input-focus {
        border-color: var(--color-primary);
        outline: 0;
        box-shadow: 0 0 0 2px var(--color-primary-2);
      }

      .yxtf-textarea {
        flex: 1;
      }

      .yxtf-textarea::before {
        top: 0;
        height: 12px;
      }

      textarea {
        height: 100%;
        padding-top: 12px;
        border: none;
      }

      .yxtf-textarea.is-ie {
        border: none;
        box-shadow: none !important;
      }

      textarea:focus {
        box-shadow: none;
      }

      .yxtf-textarea .yxtf-input__count {
        bottom: 0;
      }

      @include e(comment-dmn) {
        margin: 16px 0 -8px 16px;
      }

      @include e(comment-dmn-node) {
        height: 20px;
        margin-bottom: 8px;
        margin-left: 8px;
        padding: 0 8px;
        font-size: 12px;
        line-height: 20px;
        background: #f5f5f5;
        border-radius: 2px;
        cursor: pointer;
      }
    }
  }

  @include e(comment-share) {
    justify-content: center;
    max-width: 100vw !important;
    max-height: 100vh !important;
    margin: 0 !important;
    background-color: transparent !important;
    border-radius: 0 !important;
    box-shadow: none !important;

    .size-content-section {
      width: 21px;
      height: 16px;
    }

    .share-button-shadow {
      box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.1) !important;
    }

    .share-border-white {
      border-color: #fff !important;

      &:hover {
        border-color: #f0f0f0 !important;
      }
    }

    .share-lh28 {
      line-height: 28px;
    }

    @include e(share-wrap) {
      z-index: 999;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      width: 100%;
      min-height: 100vh;

      @include e(share-content) {
        position: relative;
        box-sizing: border-box;
        width: 344px;
        min-height: 480px;
        margin-top: 72px;
        padding: 12px;
        background-color: #e6eeff;

        @include e(share-top) {
          min-height: 356px;
          padding: 20px;
          background-color: #fff;
          border-radius: 8px;
        }

        @include e(share-bottom) {
          position: relative;
          z-index: 1;
        }

        @include e(share-bg) {
          position: absolute;
          right: 0;
          bottom: 0;
          left: 0;
          height: 112px;
        }

        @include e(share-info) {
          padding: 12px 8px;
          background-color: #f7faff;
          border-radius: 8px;

          .yxtf-image {
            width: 96px;
            height: 54px;
            border-radius: 4px;
          }
        }

        @include e(share-qrcode) {
          padding: 4px;
          background-color: #fff;
          border-radius: 4px;

          img {
            width: 56px;
            height: 56px;
          }
        }
      }
    }
  }

  @include e(note) {
    @include e(note-tab) {
      margin: -24px -32px 0;

      .yxtf-tabs__header {
        margin-bottom: 0;
      }
    }

    @include e(note-cell) {
      align-items: start;
      padding: 24px 0;

      &:not(:first-child) {
        border-top: 1px solid #f0f0f0;
      }

      @include e(note-hide) {
        opacity: 0;
      }

      &:hover {
        @include e(note-hide) {
          opacity: 1;
        }
      }
    }
  }

  @include e(other) {
    & > div {
      padding: 24px;
      background-color: white;
      border-radius: 8px;
    }

    @include e(other-operation) {
      padding: 12px !important;

      & > span {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      @include e(other-operation-hover) {
        height: 36px;
        margin: 0 -8px;
        padding: 0 16px 0 14px;
        cursor: pointer;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }

    @include e(other-img) {
      position: relative;
      width: 90px;
      height: 50px;
      overflow: hidden;
      border-radius: 5px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    @include e(other-type) {
      position: absolute;
      bottom: 4px;
      left: 4px;
      height: 20px;
      padding: 0 6px;
      color: #fff;
      font-size: 12px;
      line-height: 20px;
      background: rgba($color: #000, $alpha: 0.4);
      border-radius: 4px;
    }

    @include e(other-item) {
      display: flex;
      align-items: center;
      margin-top: 4px;
    }
  }

  @include e(users) {
    @include e(users-list) {
      display: flex;
      flex-wrap: wrap;
      gap: 12px 32px;
    }
  }

  .yxtulcdsdk-portrait {
    position: relative;
    display: flex;

    .portrait-tag {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 50%);
    }
  }

  .yxtulcdsdk-flex-align-start {
    align-items: start;
  }
}
