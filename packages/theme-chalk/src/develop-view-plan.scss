@import "mixins/mixins";
@import "common/var";

@include b(develop-view-plan) {
  @include e(develop) {
    min-height: 136px;

    @include e(develop-tip) {
      height: 40px;
      padding: 0 18px;
      color: #262626;
      font-size: 12px;
      line-height: 40px;
      background: url('./img/dplanbg1.jpg');
      border-radius: 4px;
    }
  }

  @include e(item) {
    padding: 20px 20px 24px;
    background: url('./img/vplanbg.jpg');

    &:not(:first-child) {
      margin-top: 20px;
    }
  }

  @include e(day) {
    margin: 0 10px;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
  }

  .yxt-button--default {
    background: transparent;
  }

  .yxtulcdsdk-required {
    position: relative;
  }

  .yxtulcdsdk-required::after {
    position: absolute;
    top: 8px;
    right: -8px;
    width: 4px;
    height: 4px;
    background-color: #f5222d;
    border-radius: 50%;
    content: " ";
  }
}
