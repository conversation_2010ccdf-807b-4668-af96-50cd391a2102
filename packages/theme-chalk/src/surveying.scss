@import "mixins/mixins";
@import "common/var";

$answer-width: 920px;
$answer-top: 16px;

@include b(survey-container) {
  flex-grow: 1;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  min-height: 100%;
  // overflow: auto;
  background: #f5f5f5;
  border-radius: 8px;

  // 保持和调查项目一样的处理
  p {
    margin: 0;
    padding: 0;
    outline: 0;
  }

  * {
    box-sizing: border-box;
  }

  @include m(ques) {
    height: auto;
    height: unset;
    min-height: 0;
    background: transparent;
    border-radius: 0;
  }

  >.yxt-scrollbar {
    height: 100%;
    border-radius: 8px;
  }

  @include m(no-radius) {
    border-radius: 0;

    >.yxt-scrollbar {
      border-radius: 0;
    }
  }

  @include m(no-scroll) {
    height: auto;
    min-height: 100%;
  }

  .yxt-scrollbar__view {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100%;

    > div {
      flex-grow: 1;
    }
  }

  @include b(surveying) {
    // min-width: fit-content;
  }

  @include b(survey-info) {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    min-width: 860px;
    margin: 0 auto;

    & > div {
      flex-grow: 0;
      flex-shrink: 0;
    }

    @include e(main) {
      position: relative;
      display: flex;
      flex-direction: column;
      flex-grow: 1 !important;
      flex-shrink: 1 !important;
      align-items: center;
      box-sizing: border-box;
      width: 100%;
      max-width: 1200px;
      padding: 44px 24px 12px;
      background-color: #fff;
      border-radius: 12px;

      > div:not(.yxt-loading-mask) {
        width: 100%;
        max-width: 1000px;
      }
    }

    @include e(panel) {
      border-radius: 10px;
    }

    @include m(tourist) {
      margin: 0 auto;
    }

    img {
      max-width: 100%;
    }
  }

  // 调查作答页面
  @include b(survey-answer) {
    position: relative;
    // min-width: 1280px;
    display: flex;
    flex-grow: 1;
    justify-content: center;
    box-sizing: border-box;
    // width: $answer-width !important;
    width: 100%;
    padding: 0 24px 24px;
    padding-top: $answer-top;

    @include e(max-width) {
      max-width: $answer-width;
    }

    @include e(main) {
      // position: relative;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      flex-shrink: 1;
      min-width: 500px;
      min-height: 100%;
      // padding-bottom: 108px;
      background-color: #fff;
      border-radius: 4px;
      // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);

      @include e(title-wrap) {
        flex-grow: 0;
        flex-shrink: 0;
        padding: 36px;
        white-space: pre-line;
        border-bottom: 1px solid #e4e7ed;
      }

      @include e(content-wrap) {
        flex-grow: 1;
        flex-shrink: 1;
        // overflow: auto;
      }

      @include e(submit) {
        flex-grow: 0;
        flex-shrink: 0;
        box-sizing: border-box;
        height: 96px;
        padding-top: 12px;
        padding-bottom: 36px;
        text-align: center;
        background-color: white;
        border-radius: 4px;
      }
    }

    @include e(main-detail) {
      box-shadow: none;
    }

    @include e(leftpanel) {
      flex-shrink: 0;
      // position: absolute;
      // top: 0;
      // right: 50%;
      // margin-right: calc(#{$answer-width} / 2 + 24px);
    }

    @include e(subpanel) {
      flex-shrink: 0;
      width: 144px;
      // position: absolute;
      // top: 0;
      // right: 30px;
      // left: 50%;
      margin-left: 24px;
      // margin-left: calc(#{$answer-width} / 2 + 24px);
    }

    @include e(subpanel-fix) {
      padding: 24px 16px 24px 16px;
      background: #fff;
      border-radius: 4px;
      // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);
    }

    @include e(subpanel-inner) {
      & > div {
        text-align: center;

        &:first-child {
          font-size: 14px;
          line-height: 14px;
        }

        &:last-child {
          margin-top: 10px;
          font-size: 20px;
          line-height: 16px;
        }
      }

      & + & {
        &::before {
          display: block;
          width: 100%;
          height: 1px;
          margin: 16px 0;
          background: #dcdfe6;
          content: ' ';
        }
      }
    }

    @include m(list) {
      display: flex;
      width: unset !important;
      min-width: fit-content;
      max-width: unset !important;

      @include e(main) {
        flex-grow: 1;
        flex-shrink: 1;
      }

      @include e(leftpanel) {
        position: relative;
        // right: 0;
        >div.yxtbiz-merge-evaluation {
          margin-right: 24px;
        }
      }

      @include e(subpanel) {
        position: relative;
        // left: 0;
        margin-left: 24px;
      }
    }

    @include m(full) {
      justify-content: unset;
      width: 100% !important;
      min-width: 100% !important;
      max-width: 100% !important;
      padding-right: 0 !important;
      padding-bottom: 0 !important;
      padding-left: 0 !important;

      &:first-child {
        padding-top: 0 !important;
      }

      .yxtulcdsdk-survey-answer__main {
        max-width: inherit;
      }

      .yxtulcdsdk-survey-answer__main:first-child {
        max-width: inherit !important;
        border: none;
        border-radius: 0;
        box-shadow: none;
      }
    }
  }

  // 作答单题
  @include b(survey-answer-ques) {
    position: relative;

    @include e(error) {
      position: absolute;
      bottom: -28px;
      left: 24px;
      color: #f5222d;
      font-size: 14px;
      line-height: 22px;
    }

    & + & {
      margin-top: 48px;
    }
  }

  @include b(survey-fake-input) {
    display: inline-block;
    box-sizing: border-box;
    width: 100%;
    min-height: 40px;
    padding: 8px 12px;
    color: #595959;
    line-height: 22px;
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    outline: 0;

    @include m(multi) {
      min-height: 118px;
      padding: 14px 12px;
    }
  }

  @include b(survey-result) {
    & > div {
      padding-bottom: 0;
    }
  }

  @include b(survey-ques-container) {
    position: relative;
    box-sizing: border-box;
    border: 1px solid transparent;

    .yxtulcdsdk-survey-ques {
      & > div {
        word-wrap: break-word;
      }

      .flex:not(.yxtulcdsdk-survey-slider-tick-num) {
        max-width: 100%;
      }
    }

    .pc_survey_show_more {
      max-height: 516px;
      margin-bottom: -12px;
      padding-bottom: 12px;
      overflow: hidden;
    }

    .yxtulcdsdk-survey-ques__box {
      position: relative;

      .pc_survey_show_open {
        position: static !important;
        height: 32px !important;
        margin: 10px 24px 0;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) -55.65%, #fff 26.3%, #fff 99.52%);
        background-color: #fff !important;
      }

      .yxtulcdsdk-survey-ques__more {
        position: absolute;
        bottom: -12px;
        left: 0;
        width: 100%;
        height: 77px;
        font-weight: 400;
        font-size: 14px;
        background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), color-stop(100%, #fff));
        background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0.6) 0%, #fff 30%);

        .yxt-button--text {
          color: #595959;
        }
      }
    }

    &.yxtulcdsdk-survey-ques-container--mode-0 { /**  编辑 */
      z-index: -1;
      padding: 24px;
      background-color: #fafafa;
      box-shadow: 0 2px 4px rgba(40, 41, 61, 0.04), 0 8px 11px rgba(96, 97, 112, 0.04);

      .yxtulcdsdk-survey-custom-radio__icon {
        cursor: default;
      }

      .yxtulcdsdk-survey-custom-type__cursor {
        cursor: default;
      }

      .yxtulcdsdk-survey-rate,
      .yxtulcdsdk-survey-slider {
        min-width: unset;
      }
    }

    &.yxtulcdsdk-survey-ques-container--mode-1 { /** 编辑预览 */
      padding: 24px;

      .yxt-divider--horizontal {
        margin: 0;
      }

      &:hover {
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(40, 41, 61, 0.1), 1px -1px 11px rgba(96, 97, 112, 0.04);

        @include e(menus) {
          display: flex !important;
        }
      }

      &.yxtulcdsdk-survey-ques-container--readonly {
        &:hover {
          box-shadow: none;
        }
      }

      .yxtulcdsdk-survey-ques--required::before {
        top: 12px !important;
      }

      /** focus和active去掉input样式 */
      .nofocusborder > input {
        cursor: default;
      }

      .nofocusborder > input:focus {
        border-color: #d9d9d9;
        box-shadow: none;
      }

      .nofocusborder > input:hover {
        border-color: #d9d9d9;
        box-shadow: none;
      }

      .noactiveshadow > input:active {
        box-shadow: none;
      }
    }

    /**  答题 */
    &.yxtulcdsdk-survey-ques-container--mode-2 {
      padding: 24px;

      .yxt-divider--horizontal {
        margin: 0;
      }
    }

    /**  管理员查看学员调查结果详情 */
    &.yxtulcdsdk-survey-ques-container--mode-3 {
      padding: 24px;
      border: 0;

      .yxt-divider--horizontal {
        margin: 0;
      }

      .yxtulcdsdk-survey-ques--required::before {
        display: none;
      }
    }

    /**  管理员批量录入试题结果详情 */
    &.yxtulcdsdk-survey-ques-container--mode-4 {
      padding: 12px 24px;
      border: 0;

      .yxt-divider--horizontal {
        margin: 0;
      }

      .yxtulcdsdk-survey-ques--required::before {
        display: none;
      }
    }

    &.yxtulcdsdk-survey-ques-container--mode-4.yxtulcdsdk-survey-ques-container-hover {
      background: #f7faff;
    }

    &.yxtulcdsdk-survey-ques-container--mode-4:hover {
      background: #f7faff;
    }

    @include e(error) {
      position: absolute;
      bottom: -4px;
      // left: 24px;
    }

    @include e(menus) {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
      display: none;
      height: 39px;
      padding: 0 8px;
      background: #fbfdff;

      .yxt-divider--vertical {
        height: 23px;
        margin: 0 19px;
      }
    }

    @include b(survey-ques) {
      img {
        max-width: 100%;
      }

      .multi-tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 52px;
        height: 20px;
        padding: 0 4px;
        color: #595959;
        font-size: 12px;
        background: #f0f0f0;
        border-radius: 2px;
      }

      .require-tag {
        position: absolute;
        top: 9px;
        left: -8px;
        width: 4px;
        height: 4px;
        background: #f5222d;
        border-radius: 4px;
      }

      .readonly-input {
        height: 40px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
      }

      .readonly-textarea {
        height: 83px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
      }

      @include e(sort-box) {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        margin-top: 1px;
        font-weight: bold;
        background-color: #e9e9e9;
        border-radius: 2px;
      }

      @include e(desc) {
        margin-left: 20px;
        white-space: pre-wrap;

        >:first-child {
          margin-top: 4px;
        }

        >:last-child {
          margin-bottom: -8px;
        }
      }

      @include e(score-icon) {
        // min-width: 26px;
        cursor: pointer;

        &::before,
        &::after {
          width: 9px;
          height: 1px;
          content: ' ';
        }

        // &:first-child::before {
        //   // width: 0;
        // }

        // &:last-child::after {
        //   // width: 0;
        // }

        svg {
          // aspect-ratio: 1/1;
          flex-shrink: 1;
        }
      }

      @include e(index) {
        line-height: 40px;
      }

      @include m(required) {
        position: relative;

        &::before {
          position: absolute;
          top: 20px;
          left: -7px;
          display: block;
          width: 4px;
          height: 4px;
          background-color: #f5222d;
          border-radius: 50%;
          transform: translateY(-50%);
          content: '';
        }
      }

      @include m(required-info) {
        &::before {
          top: 16px !important;
        }
      }

      .yxtulcdsdk-survey-ques__options_img {
        display: inline-block;
        width: 20px;
        height: 42px;
        background: url("./svg/drag.svg") left center no-repeat;
        visibility: hidden;
      }

      @include e(options) {
        .drag-bar {
          cursor: move;
        }

        .drag-bar,
        .del-bar {
          visibility: hidden;

          &:hover {
            color: #595959;
          }
        }

        &:hover {
          .drag-bar,
          .del-bar {
            visibility: visible;
          }

          .yxtulcdsdk-survey-ques__options_img {
            visibility: visible;

            &--hidden {
              visibility: hidden;
            }
          }
        }

        .yxtulcdsdk-survey-ques__input-rect {
          width: 145px;
          height: 28px;
          line-height: 28px;
          vertical-align: top;
          background: #fff;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
        }

        .yxtulcdsdk-survey-ques__options-user-input {
          margin-top: 8px;
        }

        &.sortable-ghost {
          height: 3px;
          margin-right: 24px;
          margin-left: 56px;
          overflow: hidden;
          background-color: var(--color-primary);

          * {
            display: none;
          }
        }
      }

      @include e(group) {
        .del-bar {
          display: none;
        }

        &:hover {
          .del-bar {
            display: block;
          }
        }
      }

      @include e(media-option) {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 3px 2px 5px rgba(40, 41, 61, 0.04), -6px -1px 10px rgba(96, 97, 112, 0.04);

        &.media-option--write {
          box-shadow: 0 0 1px 1px #e2e2e2;
        }

        &.media-option--manager-view {
          border: 1px solid #e2e2e2;
          box-shadow: none;
        }
      }

      @include e(classify) {
        @include m(wrap) {
          // padding: 12px 16px;
          padding: 12px 0;
          background-color: #fff;

          .yxt-scrollbar__view { /**  解决某些浏览器下，底部会因为缺少0.几的高度缺失，导致子元素边框显示不了 */
            padding-bottom: 1px;
          }
        }
      }

      @include b(survey-ques-scoring) {
        @include e(scale-item) {
          // width: 36px;
          width: 50px;
          height: 24px;
          overflow: visible;

          &:not(:first-child) {
            margin-left: 12px;
          }
        }
      }

      .yxtulcdsdk-survey-ques__scale-style-item {
        width: 72px;
        height: 32px;
        color: #d9d9d9;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;

        &:not(:first-child) {
          margin-left: 12px;
        }
      }

      .yxtulcdsdk-survey-ques__upload-cell {
        max-width: 100%;
        height: 32px;
        padding: 0 8px;

        .upload-delete {
          visibility: hidden;
        }

        &:hover {
          background-color: #f0f6ff;

          .upload-delete {
            visibility: visible;
          }
        }
      }

      .item-sep:not(:first-child) {
        margin-left: 12px;
      }

      .yxtulcdsdk-survey-ques-vote-wrap {
        margin: -15px;

        .write-option {
          margin: 15px;
        }
      }

      .mr-8 {
        margin-right: -8px;
      }

      .mr-20 {
        margin-right: -20px;
      }

      .radio-only {
        &.yxt-radio { margin-right: 0; }
        .yxt-radio__label { padding-left: 0; }
      }

      .yxtulcdsdk-survey-ques__add-bar {
        width: 192px;
        background-color: #fff;
        border: 1px dashed #d9d9d9;
        cursor: pointer;

        &.video-height {
          height: 167px;
        }

        &.pic-height {
          height: 240px;
        }
      }

      .classify-option-preview {
        // line-height: 40px;
        padding: 8px 12px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 2px;

        &:not(:first-child) {
          margin-top: 12px;
        }
      }

      .classify-group-preview {
        // height: 175px;
        overflow: hidden;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;

        &:not(:first-child) {
          margin-top: 16px;
        }

        .classify-group-title {
          // min-height: 40px;
          padding: 7.5px 12px;
          text-align: center;
          word-wrap: break-word;
          background-color: #f5f5f5;
          border-bottom: 1px solid #d9d9d9;
        }
      }

      .classify-option-write {
        min-height: 37px;
        margin-top: 12px;
        padding: 5px 0;
        color: #959595;
        background-color: #fff;
        border: 1px solid #e2e2e2;
        border-radius: 2px;
        cursor: move;

        &.drag-bar {
          color: #262626;
        }

        &.classify-option--disabled {
          color: #959595;
          cursor: not-allowed;
        }

        &.sortable-chosen {
          background-color: #f5f6f7;
        }

        &.sortable-chosen-custom {
          background-color: #f5f6f7;
        }
      }

      .classify-group-write {
        position: relative;
        min-height: 135px;
        margin-top: 12px;
        background-color: #fff;
        border: 1px solid #e2e2e2;

        .classify-group-title {
          max-width: 100%;
          // height: 37px;
          padding: 7px 10px;
          text-align: center;
          word-wrap: break-word;
          background-color: #f7f8fa;
          border-bottom: 1px solid #e2e2e2;
        }

        .classify-group-write__put {
          position: relative;
          min-height: 96px;
          padding: 6px;

          .classify-group-write__put-item {
            max-width: calc(100% - 12px);
            height: 32px;
            margin: 6px;
            padding: 0 8px 0 12px;
            background-color: #f5f5f5;
            border-radius: 4px;
          }

          .no-data {
            position: absolute;
            top: 50%;
            width: calc(100% - 12px);
            color: #959595;
            transform: translateY(-50%);
          }

          .sortable-ghost {
            width: 1px;
            height: 32px;
            margin: 6px;
            overflow: hidden;
            background-color: #262626;
          }
        }
      }

      .yxtbiz-upload,
      .yxtbiz-upload__dragger {
        max-width: 100%;
      }
    }

    // // 隐藏序号时 去除原先的序号缩进
    // @include m(nonum) {
    //   @include b(survey-ques) {
    //     > *.ml20 {
    //       margin-left: 0 !important;
    //     }
    //   }
    // }
  }

  @include b(survey-rate) {
    display: flex;
    justify-content: space-between;
    min-width: 100%;

    @include e(item) {
      width: 40px;
      height: 24px;
      color: #8c8c8c;
      background-color: #f0f0f0;
      border-radius: 3px;

      @include m(select) {
        // height: 30px;
        box-shadow: -1px 4px 7px var(--color-primary-2);
      }

      &.larger {
        width: 50px;
        height: 30px;
      }

      &.front {
        width: 50px;
        height: 36px;
        color: #262626;

        &.yxtulcdsdk-survey-rate__item--select {
          box-shadow: none;
        }
      }
    }

    @include m(lr) {
      min-width: unset;
    }
  }

  @include b(survey-slider) {
    position: relative;
    min-width: 100%;
    max-width: 100%;
    height: 8px;
    user-select: none;

    @include e(bar-wrap) {
      position: absolute;
      bottom: 0;
      z-index: 99;
    }

    @include e(bar) {
      position: relative;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      box-shadow: 0 3px 5px 0 var(--color-primary-3);
      transform: translateX(-50%) translateY(50%);

      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6px;
        height: 6px;
        background-color: #fff;
        border-radius: 50%;
        transform: translateX(-50%) translateY(-50%);
        content: '';
      }
    }

    @include e(line) {
      position: relative;
      top: -1px;
      height: 1px;
      background-color: #d9d9d9;
    }

    @include e(tick) {
      z-index: 20;
      width: 100%;
      height: 8px;

      @include e(tick-item-wrap) {
        position: relative;
        z-index: 20;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        // padding: 5px 5px 0;
        margin: -5px;
        padding: 5px;
        cursor: pointer;

        // &:last-child {
        //   padding-right: 0;
        // }

        // &:first-child {
        //   padding-left: 0;
        // }
      }

      @include e(tick-item) {
        position: relative;
        z-index: 10;
        width: 1px;
        height: 8px;
        background-color: #d9d9d9;

        &.not-show {
          visibility: hidden;
        }
      }
    }

    @include e(cover) {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 9;
      height: 3px;
      transform: translateY(50%);
    }

    @include e(label) {
      position: absolute;
      bottom: -20px;
      left: 50%;
      transform: translate(-50%, 100%);
    }

    &.is-large {
      height: 26px;
      padding: 10px 0 8px 0;

      @include e(bar) {
        width: 18px;
        height: 18px;
        transform: translateX(-50%);

        &::after {
          width: 12px;
          height: 12px;
        }
      }

      @include e(line) {
        top: -3px;
        height: 3px;
      }

      @include e(tick-item) {
        width: 2px;
      }

      @include e(cover) {
        bottom: 6px;
        height: 6px;
        transform: none;
      }
    }

    @include m(lr) {
      min-width: unset;
    }
  }

  @include b(survey-slider-tick-num) {
    > span {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 10px;
      word-break: keep-all;
    }
  }

  @include b(survey-custom-radio) {
    box-sizing: border-box;
    cursor: pointer;
    user-select: none;

    @include e(icon) {
      position: relative;
      width: 16px;
      height: 16px;

      &::after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        display: block;
        width: 100%;
        height: 100%;
        content: '';
      }

      .yxt-radio {
        font-size: 0;
        line-height: 0;
      }

      .yxt-radio__label {
        display: none;
      }
    }

    .no-border {
      border: none !important;
    }

    .select-cover {
      transform: translateX(-50%) translateY(-50%) scale(1);
    }
  }

  @include b(survey-custom-checkbox) {
    box-sizing: border-box;
    cursor: pointer;
    user-select: none;

    @include e(icon) {
      position: relative;
      width: 16px;
      height: 16px;
      overflow: hidden;

      &::after {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        display: block;
        width: 100%;
        height: 100%;
        content: '';
      }

      .yxt-checkbox {
        font-size: 0;
        line-height: 0;
      }
    }

    .no-border {
      border: none !important;
    }

    .select-cover {
      transform: translateX(-50%) translateY(-60%) rotate(45deg) scaleY(1);
    }

    @include m(disabled) {
      cursor: not-allowed;
    }
  }

  // global

  /** -------------- text-align -------------- */
  .tl {
    text-align: left;
  }

  .tc {
    text-align: center;
  }

  .tr {
    text-align: right;
  }

  /**  -------------- float -------------- */
  .fl {
    float: left;
  }

  .fr {
    float: right;
  }

  .clear-f::after {
    display: block;
    clear: both;
    content: "";
  }

  .no-style {
    font-style: normal;
  }

  .overflow-hide {
    overflow: hidden;
  }

  /**  --------------  ellipsis -------------- */
  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-wrap: break-word;
  }

  .ellipsis2 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .ellipsis3 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .tag-ellipsis {
    display: inline-block;
    max-width: 480px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-wrap: break-word;
  }

  .word-wrap {
    word-wrap: break-word !important;
  }

  /** ------------- 需要整理出去的样式 ------------- */
  .yxtulcdsdk-survey-drawer {
    &__title {
      position: relative;
      padding-left: 15px;

      &--tag {
        position: absolute;
        top: 50%;
        left: 0;
        display: block;
        width: 3px;
        height: 12px;
        transform: translateY(-50%);
        content: "";
      }
    }
  }

  /** ------------------- 公共覆盖coverage （考虑能否去除）------------------- */
  .yxt-dropdown-menu__item {
    white-space: nowrap;
  }

  .width-percent-100[class*="yxt-"] {
    width: 100% !important;
  }

  .yxt-input-number input.yxt-input__inner {
    text-align: left;
  }

  .yxtf-tabs__item {
    box-sizing: content-box !important;
  }
}

@include b(survey) {
  /** ------------------- flex ------------------------- */
  .flex {
    display: flex;
  }

  .flex-stretch {
    align-items: stretch;
  }

  .flex-grow {
    flex-grow: 1;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .flex-top {
    align-items: flex-start;
  }

  .flex-mid {
    align-items: center;
  }

  .flex-bottom {
    align-items: flex-end;
  }

  .flex-left {
    justify-content: flex-start;
  }

  .flex-center {
    justify-content: center;
  }

  .flex-right {
    justify-content: flex-end;
  }

  .flex-between {
    justify-content: space-between;
  }

  .flex-vertical {
    flex-direction: column;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-1-atuo {
    flex: 1 1 auto;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .flex-shrink-1 {
    flex-shrink: 1;
  }

  .flex-self-stretch {
    align-self: stretch;
  }

  .flex-self-top {
    align-self: flex-start;
  }

  .flex-self-bottom {
    align-self: flex-end;
  }

  @include m(deep) {
    .yxtulcdsdk-survey-ques-container--mode-2 {
      padding: 24px 32px !important;
    }
  }
}

/** ------------------- z-index --------------------- */
.zi98 {
  z-index: 98 !important;
}

.w-142 {
  width: 142px;
}

.w-144 {
  width: 144px;
}

.w-275 {
  width: 275px;
}

.w0 {
  width: 0;
}

.mw14 {
  min-width: 14px;
}

.w24 {
  width: 24px;
}

.w64 {
  width: 64px;
}

.w180 {
  width: 180px;
}

.w160 {
  width: 160px;
}

.w245 {
  width: 245px;
}

.w400 {
  width: 400px;
}

.mt-3 {
  margin-top: -3px;
}

.m-f8 {
  margin: -8px;
}

.mh-5 {
  margin-right: -5px;
  margin-left: -5px;
}

.pl70 {
  padding-left: 70px;
}

.pl80 {
  padding-left: 80px;
}

.pv3 {
  padding-top: 3px;
  padding-bottom: 3px;
}

.pv30 {
  padding-top: 30px;
  padding-bottom: 30px;
}

.pv32 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.mt44 {
  margin-top: 44px;
}

.ml2 {
  margin-left: 2px;
}

.ml3 {
  margin-left: 3px;
}

.ml6 {
  margin-left: 6px;
}

.ml8 {
  margin-left: 8px !important;
}

.ml9 {
  margin-left: 9px;
}

.ml10 {
  margin-left: 10px;
}

.ml20 {
  margin-left: 20px;
}

.ml73 {
  margin-left: 73px;
}

.mr6 {
  margin-right: 6px;
}

.mr21 {
  margin-right: 21px;
}

.mr52 {
  margin-right: 52px;
}

.mw500 {
  max-width: 500px;
}

.mxw800 {
  max-width: 800px;
}

.ws-prewrap {
  white-space: pre-wrap;
}

.color-white {
  color: white;
}

.color-white-i {
  color: white !important;
}

.color-danger {
  color: #fa5252;
}

.border-right {
  border-right: 1px solid #e9e9e9;
}

.color959595 {
  color: #959595;
}

.pointer {
  cursor: pointer;
}

@include b(survey-complete) {
  // min-width: 1340px;
  overflow: hidden;

  @include e(main) {
    box-sizing: border-box;
    min-height: 100%;
    padding: 32px 12px 16px;
    background-color: #fff;
    border-radius: 4px;
    // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);
  }
}

@include b(survey-header) {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  height: 44px;
  padding: 0 24px 0 16px;
  color: #595959;
  background: #fff;
  box-shadow: inset 0 -1px 0 0 #e9e9e9;
}

.yxtulcdsdk-survey-big-viewer {
  .yxtulcdsdk-survey-big-viewer__mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 3000;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.6);

    .yxtulcdsdk-survey-big-viewer__video {
      display: block;
      max-width: 100%;
      background-color: #000;
    }

    .yxtulcdsdk-survey-big-viewer__del {
      position: fixed;
      top: -40px;
      right: -40px;
      width: 80px;
      height: 80px;
      background-color: rgba(0, 0, 0, 0.9);
      border-radius: 50%;
      cursor: pointer;

      .del-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        color: white;
        transform: translateX(-100%);
      }
    }
  }
}

.yxtulcdsdk-survey-attachment-viewer {
  position: relative;
  overflow: hidden;
  background-color: #fcfcfc;
  border-radius: 4px;
  border-radius: 0\0;

  &__play {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 48px;
    height: 48px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }

  &__hover {
    position: absolute;
    top: 0;
    left: 0;
    display: none;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);

    .yxtulcdsdk-survey-attachment-viewer__preview {
      width: 80px;
      height: 32px;
    }

    .yxtulcdsdk-survey-attachment-viewer__change {
      width: 80px;
      height: 32px;
    }

    .yxtulcdsdk-survey-attachment-viewer__start {
      position: absolute;
      bottom: 8px;
      left: 8px;
      width: 24px;
      height: 24px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
    }

    .yxtulcdsdk-survey-attachment-viewer__delete {
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      border-bottom-left-radius: 4px;
    }
  }

  .yxtulcdsdk-survey-attachment-viewer__cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.5);

    .yxtulcdsdk-survey-attachment-viewer__delete {
      position: absolute;
      top: 0;
      right: 0;
      display: none;
      width: 20px;
      height: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      border-bottom-left-radius: 4px;
    }
  }

  &.yxtulcdsdk-survey-attachment-viewer--edit:hover {
    .yxtulcdsdk-survey-attachment-viewer__hover {
      display: flex;
    }

    .yxtulcdsdk-survey-attachment-viewer__delete {
      display: flex;
    }

    .yxtulcdsdk-survey-attachment-viewer__play {
      display: none;
    }
  }

  .yxtulcdsdk-survey-attachment-viewer__image {
    width: 100%;
    height: 100%;
    object-fit: cover;

    &.is-audio {
      background-color: rgba(247, 83, 113, 0.15);
    }

    &.is-video {
      background: rgba(122, 139, 247, 0.15);
    }

    &.is-transcoding {
      color: #fff;
      background-color: #000;
    }
  }

  .yxtulcdsdk-survey-attachment-viewer--front {
    position: relative;
    height: 100%;

    .play-cover {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
    }

    .yxtulcdsdk-survey-attachment-viewer--transcoding {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      color: #fff;
      background-color: #000;
    }
  }
}

.yxtulcdsdk-survey-attach-container {
  .yxtulcdsdk-survey-attach-container__attachment-wrap {
    position: relative;
    max-width: 100%;
    margin-top: 8px;
    margin-right: 7px;
    background-color: #f9f9f9;

    .yxtulcdsdk-survey-attachment-viewer {
      max-width: 100%;
    }
  }
}
// 下拉题样式
.yxtulcdsdk-survey-layouttype-select {
  max-width: 870px;

  .yxt-select-dropdown__item {
    height: auto;
    min-height: 36px;

    .preline {
      white-space: pre-line;
    }
  }
}

// 恢复富文本下的 标签默认样式
.reset-default-style-for-edit {
  // 恢复富文本下的 标签默认样式
  ul,
  ol {
    display: block;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0;
    margin-inline-end: 0;
    // padding-inline-start: 40px;
    margin: 10px 0 10px 20px;
  }

  ul {
    list-style-type: disc;

    li {
      list-style: disc;
    }
  }

  ol {
    list-style-type: decimal;

    li {
      list-style: decimal;
    }
  }

  // p,  p标签为默认行标签，如果加间距会导致一些单行的文本与左边元素错位
  pre,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  table {
    margin: 10px 0;
    line-height: 1.5;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    display: block;
    font-weight: bold;
    margin-inline-start: 0;
    margin-inline-end: 0;
  }

  h1 {
    font-size: 2em;
  }

  h2 {
    font-size: 1.5em;
  }

  h3 {
    font-size: 1.17em;
  }

  h4 {
    font-size: 1em;
  }

  h5 {
    font-size: 0.83em;
  }

  h6 {
    font-size: 0.67em;
  }

  table {
    border-top: 1px solid #d9d9d9;
    border-left: 1px solid #d9d9d9;

    th,
    td {
      padding: 3px 5px;
      border-right: 1px solid #d9d9d9;
      border-bottom: 1px solid #d9d9d9;
    }

    th {
      font-weight: bold;
      text-align: center;
      border-bottom: 2px solid #d9d9d9;
    }
  }

  sup {
    font-size: smaller;
    vertical-align: super;
  }

  sub {
    font-size: smaller;
    vertical-align: sub;
  }
}
