@import "mixins/mixins";
@import "common/var";

@include b(o2o-homework-task) {
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  min-width: 600px;
  max-width: 1200px;
  min-height: 100%;
  margin: 0 auto;

  $root: '.o2o-hwk';
  $reference: #{$root}-reference;
  $attachments: #{$root}-attachments;
  $centerblock: #{$root}-block;
  $centerblock2: #{$root}-block-ai;
  $centerblock3: #{$root}-block-ai-expand;

  #{$reference} {
    border-radius: 2px;

    &:hover {
      background-color: #f5f5f5;

      #{$reference}__download {
        visibility: visible;
      }
    }

    &__download {
      visibility: hidden;

      &:hover {
        background-color: #e9e9e9;
      }
    }

    &-loading {
      animation: loading-rotate 2s linear infinite;
    }

    &__popover {
      display: flex;
      flex-direction: column;

      .el-popover__reference-wrapper {
        display: flex;
        flex-direction: column;
      }
    }
  }

  #{$attachments} {
    &__mask-btn:hover {
      background-color: rgba($--color-black, .5);
    }
  }

  #{$centerblock} {
    min-width: 552px;
    max-width: 752px;
  }

  #{$centerblock2} {
    min-width: 492px;
    max-width: 712px;
  }

  #{$centerblock3} {
    min-width: 492px;
    max-width: 580px;
  }

  .o2o-drawer-control-homework {
    .o2o-task-file-wrap {
      margin-top: 16px;
      margin-right: 16px;

      &:nth-of-type(3n) {
        margin-right: 0;
      }
    }
  }

  .lhnormal {
    line-height: normal;
  }

  .o2o-voice-trans-text {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background: white;
    border-radius: 4px;
  }

  .o2o-voice-trans-text:hover {
    background: #f5f5f5;
  }

  .o2o-voice-trans-action {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
    height: 40px;
  }

  .o2o-voice-bar-item:hover {
    .o2o-voice-trans-action {
      display: block;
    }
  }

  .pa-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .opah0 {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .opah1:hover {
    opacity: 1;
  }

  p {
    margin: 0;
  }

  .c-white {
    color: white;
  }

  .c-success {
    color: $--color-success;
  }

  .c-warning {
    color: #fa8c16;
  }

  .pa-bottom-2 {
    bottom: -2px;
  }

  .pa-right-2 {
    right: -2px;
  }

  .o2o-myteaching-task-svg {
    transform: rotate(-90deg);
  }

  .o2o-homework-multiple-userline {
    border: 1px solid #e9e9e9;
    border-radius: 4px;
  }

  .review-return {
    color: #fa8c16;
    background-color: #fff9f1;
  }

  .review-success {
    color: #52c41a;
    background-color: #f5faf6;
  }

  .review-error {
    color: #f5222d;
    background-color: #fff8f6;
  }

  .review-success-color {
    color: #52c41a;
  }

  .review-error-color {
    color: #f5222d;
  }

  .review-success-bgcolor {
    background-color: #f5faf6;
  }

  .review-error-bgcolor {
    background-color: #fff8f6;
  }

  .o2o-hw-download {
    position: absolute;
    top: 34px;
    right: 32px;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #f0f0f0;
    border: 1px solid transparent;
    border-radius: 100%;
    cursor: pointer;
  }

  .flex-center {
    display: flex;
    align-items: center;
  }
}

.yxt-o2o-media-wrap {
  z-index: 2100;
}

.yxt-o2o-media-close {
  z-index: 9999;
  margin-top: -300px;
  margin-left: 416px;
}

.o2o-view-doc-container {
  height: 100% !important;
  max-height: 100% !important;
  border-radius: 0;

  .yxt-dialog__body {
    height: 100%;
    padding: 0;
  }

  .yxt-scrollbar__view {
    height: 100%;
  }
}

// 音频转码后图片
.yxt-o2o-audio-bg {
  background: url('./img/audio-bg.jpg') no-repeat center;
  background-size: 100% 100%;
}

.o2o-comment-ai-result {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  border-radius: 8px;
}

@include b(hw-ai) {
  @include e(comment) {
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    width: 100%;
    height: 160px;
    margin-top: 24px;
    background: linear-gradient(135deg, rgba(199, 120, 255, 1), rgba(118, 162, 255, 1), rgba(67, 219, 255, 1));
    border-radius: 8px;

    .comment-content {
      position: absolute;
      top: 1px;
      right: 1px;
      bottom: 1px;
      left: 1px;
      z-index: 2;
      background: #fff;
      border-radius: 7px;

      &__bg {
        width: 100%;
        background: linear-gradient(135deg, rgba(199, 120, 255, 0.1) 0%, rgba(118, 162, 255, 0.1) 50%, rgba(67, 219, 255, 0.1) 100%);

        .review-ai-bg {
          background-color: transparent !important;
        }
      }
    }

    .comment-ai-generate {
      height: 166px;
    }

    .comment-ai-generate-text {
      color: transparent; /* 使文本颜色透明 */
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      background: linear-gradient(230deg, #4cd2f4 0%, #7a1eff 100%);
      -webkit-background-clip: text; /* 用来裁剪背景到文本 */
      background-clip: text; /* 标准浏览器支持 */
    }

    .comment-ai-text {
      margin-left: 8px;
      color: transparent; /* 使文本颜色透明 */
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      background: linear-gradient(230deg, #4cd2f4 0%, #7a1eff 100%);
      -webkit-background-clip: text; /* 用来裁剪背景到文本 */
      background-clip: text; /* 标准浏览器支持 */
    }
  }

  @include e(upload) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 360px;
    height: 180px;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
  }
}

.o2o-newtask-ai {
  &-submit__btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 121px;
    height: 40px;
    margin-left: 20px;
    color: #fff;
    font-size: 14px;
    line-height: 40px;
    background: linear-gradient(135deg, #6d1bff 0%, #1d65ff 50%, #00bce6 100%);
    border-radius: 4px;
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
      opacity: 0.3;
    }
  }

  &-sticky {
    position: sticky;
    top: 0;
  }

  &-container {
    position: relative;
    z-index: 1;
    flex-shrink: 0;
    box-sizing: border-box;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #c778ff 0%, #76a2ff 50%, #43dbff 100%);
    border-radius: 8px;

    .container-bg {
      position: absolute;
      top: 1px;
      right: 1px;
      bottom: 1px;
      left: 1px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 7px;
      cursor: pointer;
    }
  }
}
