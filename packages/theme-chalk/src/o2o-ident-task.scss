@import "mixins/mixins";
@import "common/var";

@include b(o2o-ident-task) {
  &-wrap {
    box-sizing: border-box;
    min-width: 850px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .o2o-pc-identification {
    .o2o-pc-identification-content {
      width: 800px;
      margin-right: auto;
      margin-left: auto;
    }
  }

  .w50per {
    width: 50%;
  }

  .flex-center-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .center-wrap {
    padding-right: 200px !important;
    padding-left: 200px !important;
  }

  .required-dot {
    display: inline-block;
    width: 4px;
    height: 4px;
    background: #f5222d;
    border-radius: 50%;
  }

  .upload-list {
    position: relative;
    width: 188px;
    margin: 24px 16px 0 0;

    &-container {
      display: flex;
      flex-wrap: wrap;
    }

    &:hover .upload-attach-del {
      opacity: 1;
    }
  }

  .upload-attach {
    &-del {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      background-color: rgba(0, 0, 0, .5);
      border-radius: 0 4px;
      opacity: 0%;
      transition: opacity .3s;
    }

    &-bg {
      position: relative;

      &:hover .upload-attach-operate {
        opacity: 1;
      }
    }

    &-operate {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 106px;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      opacity: 0%;

      &-item {
        display: flex;
      }
    }

    &-default-bg {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 106px;
      background-color: #f5f5f5;
      border-radius: 4px;

      &.error {
        box-sizing: border-box;
        border: 1px solid #f5222d;
      }
    }

    &-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 4px;
      background-color: #436bff;
    }

    &-name {
      height: 20px;
      line-height: 20px;
      text-align: center;
    }

    &-tooltip {
      display: inline-block;
      width: 100%;
    }
  }

  .ml38 {
    margin-left: 38px;
  }

  .mt-8px {
    margin-top: -8px;
  }
}
