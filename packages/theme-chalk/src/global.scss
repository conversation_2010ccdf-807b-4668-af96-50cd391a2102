@import "./mixins/space.scss";

ul,
li,
ol,
dl,
dd,
dt {
  margin: 0;
  padding: 0;
  list-style: none;
}

.clearfix {
  &::before {
    display: table;
    content: '';
  }

  &::after {
    display: table;
    clear: both;
    content: '';
  }
}

// padding
@include padding(24);

//margin
@include margin(5);
@include margin(10);
@include margin(15);
@include margin(8);

img {
  vertical-align: middle;
}

.circle-img {
  border-radius: 50%;
}

.over-hidden {
  overflow: hidden;
}

.over-auto {
  overflow: auto;
}

.hand {
  cursor: pointer;
}

.notallowed {
  cursor: not-allowed;
}

a {
  text-decoration: none;

  &:hover {
    text-decoration: none;
  }
}

a.no-hover {
  &:hover {
    color: inherit;
    text-decoration: none;
  }
}

label {
  display: inline-block;
  max-width: 100%;
}

.seperate-l {
  border-left: solid 1px #bcbcbc;
}

.pull-left {
  float: left !important;
}

.pull-right {
  float: right !important;
}

.d-in-block {
  display: inline-block;
}

.d-block {
  display: block;
}

.d-none {
  display: none;
}

.dis-tabcell {
  display: table-cell;
}

.v-hidden {
  visibility: hidden;
}

.center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}

//文字换行
.nowrap {
  white-space: nowrap;
  word-wrap: normal;
  word-break: normal;
}

.nowrap-left {
  overflow: hidden;
  direction: rtl;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-wrap: normal;
  word-break: normal;
}

.nowrap.lh-standard {
  overflow-y: visible;
}

.ellipsis {
  // @include nowrap();
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

//文字粗细
.font-bold {
  font-weight: bold;
}

.font-bolder,
.weight-bold {
  font-weight: 500;
}

.font-normal {
  font-weight: normal;
}

.font-lighter {
  font-weight: 100;
}

//背景色
.bg-white {
  background-color: #fff;
}

.bg-lightgrey {
  background-color: #f5f5f5;
}

// 边线
.border-l2b {
  border-bottom: 1px solid #e4e4e4;
}

.border-r {
  border-right: 1px solid #e4e4e4;
}

.v-mid {
  vertical-align: middle;
}

.v-text-bottom {
  vertical-align: text-bottom;
}

// z-index
.z-1 {
  z-index: 1;
}

.z-19 {
  z-index: 19;
}

.z-99999 {
  z-index: 99999 !important;
}

.z-999 {
  z-index: 999;
}

.z-1000 {
  z-index: 1000;
}

.pr {
  position: relative;
}

.pa {
  position: absolute;
}

.right-top {
  position: absolute;
  top: 0;
  right: 0;
}

.pos_b10 {
  bottom: 10px;
}
//文字排版
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

//垂直排版
.v-top {
  vertical-align: top !important;
}

.v-middle {
  vertical-align: middle !important;
}

.v-bottom {
  vertical-align: bottom !important;
}

// 行高
.lh19 {
  line-height: 19px;
}

.lh32 {
  line-height: 32px;
}

.lh40 {
  line-height: 40px;
}

.lh60 {
  line-height: 60px;
}

// 基础三列
.yxt-tree-node__content .yxt-icon-setting {
  visibility: hidden;
}

.yxt-tree-node__content:hover .yxt-icon-setting {
  visibility: visible;
}

.template3 {
  width: 100%;

  .template3-body {
    background-color: #fff;
    border-radius: 4px;
  }

  .yxt-breadcrumb::after {
    display: inline-block;
  }

  .template3-tree {
    float: left;
    box-sizing: border-box;
    width: 348px;
    padding: 24px;

    .yxt-icon-setting::before {
      position: absolute;
      top: -10px;
    }

    .yxt-tree-node > .yxt-tree-node__children {
      overflow: visible;
    }

    .yxt-dropdown-menu {
      margin: 0;
      padding: 0;
    }
  }
}

.template3-table {
  margin-left: 348px;
  padding: 24px;

  .yxt-table .cell {
    white-space: nowrap;
    word-wrap: normal;
    word-break: normal;
  }
}

// //面包屑
// .yxt-breadcrumb__inner,
// .yxt-breadcrumb__separator {
//   color: #333333 !important;
// }
// 表单
.yxt-card-body {
  padding: 24px;
  background: #fff;
  border-radius: 4px;

  .yxt-form-item {
    margin-bottom: 24px !important;

    // 文本框高度
    .yxt-textarea > textarea {
      height: 72px;
    }
    //左侧文本color&右边距
    .yxt-form-item__label {
      padding-right: 17px;
      color: rgba(0, 0, 0, 0.847);
    }

    .yxt-form-item__content {
      div:first-child {
        width: 60%;
      }

      .yxt-select {
        .yxt-input {
          width: 100%;
        }
      }
    }
  }
}

.mb0 {
  margin-bottom: 0 !important;
}

.mr2 {
  margin-right: 2px;
}

.mr8 {
  margin-right: 8px;
}

.mr16 {
  margin-right: 16px;
}

.mr14 {
  margin-right: 14px;
}

.ml3 {
  margin-left: 3px;
}

.ml8 {
  margin-left: 8px;
}

.ml12 {
  margin-left: 12px;
}

.ml18 {
  margin-left: 18px;
}

.ml14 {
  margin-left: 14px;
}

.ml52 {
  margin-left: 52px;
}

.mh8 {
  margin-right: 8px;
  margin-left: 8px;
}

.mh10 {
  margin-right: 10px;
  margin-left: 10px;
}

.mh20 {
  margin-right: 20px;
  margin-left: 20px;
}

.mt4 {
  margin-top: 4px;
}

.mt12 {
  margin-top: 12px;
}

.mb12 {
  margin-bottom: 12px;
}

.mb8 {
  margin-bottom: 8px;
}

.mb20 {
  margin-bottom: 20px;
}

.mh15 {
  margin-right: 15px;
  margin-left: 15px;
}

.mt16 {
  margin-top: 16px;
}

.ml16 {
  margin-left: 16px;
}

.mt20 {
  margin-top: 20px;
}

.mt100 {
  margin-top: 100px;
}

.ml20 {
  margin-left: 20px;
}

.ml24 {
  margin-left: 24px;
}

.ml28 {
  margin-left: 24px;
}

.ml40 {
  margin-left: 40px;
}

.mt24 {
  margin-top: 24px;
}

.mr24 {
  margin-right: 24px;
}

.mb24 {
  margin-bottom: 24px;
}

.mb32 {
  margin-bottom: 32px;
}

.mb40 {
  margin-bottom: 40px;
}

.mv24 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.pr20 {
  padding-right: 20px;
}

.pr24 {
  padding-right: 24px;
}

.pt15 {
  padding-top: 15px;
}

.pt20 {
  padding-top: 20px;
}

.pt31 {
  padding-top: 31px;
}

.pt32 {
  padding-top: 32px;
}

.pl5 {
  padding-left: 5px;
}

.pl8 {
  padding-left: 8px;
}

.pt4 {
  padding-top: 4px;
}

.pt44 {
  padding-top: 44px;
}

.pt8 {
  padding-top: 8px;
}

.pb8 {
  padding-bottom: 8px;
}

.pb32 {
  padding-bottom: 32px;
}

.ph8 {
  padding-right: 8px;
  padding-left: 8px;
}

.ph32 {
  padding-right: 32px;
  padding-left: 32px;
}

.pv8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.ph10 {
  padding-right: 10px;
  padding-left: 10px;
}

.pv12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.pt34 {
  padding-top: 34px;
}

.pt36 {
  padding-top: 36px;
}

.pl40 {
  padding-left: 40px;
}

.pl44 {
  padding-left: 44px;
}

.pl52 {
  padding-left: 52px;
}

.pl68 {
  padding-left: 68px;
}

.pl60 {
  padding-left: 60px;
}

.pr40 {
  padding-right: 40px;
}

.ph40 {
  padding-right: 40px;
  padding-left: 40px;
}

.pt48 {
  padding-top: 48px;
}

.ph48 {
  padding-right: 48px;
  padding-left: 48px;
}

.d-in-flex {
  display: inline-flex;
}

.col-flex-1 {
  flex: 1;
}

.white-space {
  white-space: nowrap;
}

.space-pre-line {
  white-space: pre-line;
}

// 图标颜色
.color0 {
  color: #000;
}

// 字体大小
.font-size-14 {
  font-size: 14px !important;
}

.font-size-12 {
  font-size: 12px !important;
}

.font-size-16 {
  font-size: 16px !important;
}

.font-size-20 {
  font-size: 20px;
}

.routineColor {
  margin: 0 5px;
  color: #606266;
}

.addFace .yxt-tabs__nav-scroll {
  padding: 0 !important;
}

// 底部按钮样式
.fixedFooterStyle {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 101;
  height: 56px;
  line-height: 56px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  box-shadow: rgba(0, 0, 0, 0.2) 2px 0 4px;
}

.fixedFooter-width {
  width: calc(100% - 200px);
}
// 左侧折叠面板出现后按钮样式
.collapse-fixedFooter-width {
  width: calc(100% - 72px);
}

.width30 {
  width: 30%;
}

.width640 {
  width: 640px;
}

.width960 {
  width: 960px;
}

.width480 {
  width: 480px;
}

.width959 {
  width: 959px;
}

.ph80 {
  padding: 0 80px;
}

.pt40 {
  padding-top: 40px;
}

.pb40 {
  padding-bottom: 40px;
}
// 颜色
.text-26 {
  color: #262626;
}

.text-59 {
  color: #595959;
}

.text-8c {
  color: #8c8c8c;
}

.text-1890FF {
  color: #1890ff;
}

// 字体加粗
.font-w700 {
  font-weight: 700;
}

.font-w400,
.font-weight-400 {
  font-weight: 400;
}

.yxt-drawer__body {
  overflow: auto;
}

.width-percent-100 {
  width: 100%;
}

.max-width-100 {
  max-width: 100%;
}

// 边框
.border-t {
  border-top: 1px solid #e4e4e4;
}

.border-dashed {
  border: 1px dashed #e4e4e4 !important;
}

.check-style {
  .yxt-checkbox {
    margin-right: 24px !important;
  }
}

.auditor {
  .yxt-button--text {
    padding: 0 !important;
    color: #1890ff;
  }
}

// 基础两列
.template2 {
  padding: 20px 24px;
  border-radius: 4px;
}

.template2-table {
  .title-color {
    background-color: #fafafa;
    border-radius: 4px;
  }
}
// 颜色
.txt-blue {
  color: #409eff;
}

.txt-red {
  color: #f56c6c;
}

.txt-grey {
  color: #979797;
}
//筛选器
.template5 {
  font-size: 14px;

  .yxt-button--primary {
    width: 116px;
    background-color: #436bff;
  }

  .submit {
    width: 96px;
  }

  .p-style {
    height: 32px;
    margin: 0;
    color: #262626;
    font-weight: 500;
    font-size: 16px;
    line-height: 32px;
  }

  .span-style {
    padding-left: 10px;
    color: #757575;
    font-weight: 400;
    font-size: 14px;
  }

  .mutiple-select {
    position: relative;
    display: inline-block;
    width: 190px;
    height: 38px;
    line-height: 38px;
    border: 1px solid rgb(220, 223, 230);
    border-radius: 5px;
    cursor: pointer;

    .mutiple-select-span {
      display: inline-block;
    }

    .mutiple-select-span:first-child {
      width: 50px;
      padding: 0 10px 0 15px;
      color: #606266;
    }

    .mutiple-select-span:nth-child(2) {
      padding: 0 10px 0 15px;
      color: #c0c4cc;
      font-size: 14px;
    }

    .mutiple-select-span:nth-child(3) {
      width: 25px;
      text-align: center;
    }

    .mutiple-select-dropdown {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 500;
      width: 100%;
      margin-top: 45px;
      background-color: #fff;
      border: 1px solid #f0f0f0;
    }

    .yxt-checkbox {
      width: 95%;
      margin-right: 0;
      padding-left: 5%;
    }
  }

  .template5-filter1 {
    position: relative;
    padding: 24px;
    border-radius: 4px;

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  .template4-duration {
    width: 60px;
  }

  .inlineBlock {
    display: inline-block;
  }

  .template5-filter2 {
    position: relative;
    padding: 24px;
    border-radius: 4px;
    // height: 320px;

    //   @media screen and (max-width:1490px){
    //     .mutiple-filter div:nth-of-type(9){
    //       margin-bottom: 0;
    //     }
    //    }
    //    @media screen and (min-width:1490px) and (max-width:1700px){
    //     .mutiple-filter div:nth-of-type(6), .mutiple-filter div:nth-of-type(7),.mutiple-filter div:nth-of-type(8), .mutiple-filter div:nth-of-type(9){
    //       margin-bottom: 0;
    //     }
    //   }
    // //   @media screen and (min-width:1730px) and (max-width:1880px){
    // //     .mutiple-filter div:nth-of-type(7),.mutiple-filter div:nth-of-type(8), .mutiple-filter div:nth-of-type(9){
    // //       margin-bottom: 0;
    // //   }
    // // }
    //     @media screen and (min-width:1700px) and (max-width:1920px){
    //       .mutiple-filter div:nth-of-type(7), .mutiple-filter div:nth-of-type(8), .mutiple-filter div:nth-of-type(9){
    //         margin-bottom: 0;
    //     }
    //   }
    .yxt-button {
      position: relative;
    }

    .input-left .yxt-input__inner {
      padding-left: 0;
    }

    .filter-btn {
      width: 128px;
      padding: 8px 12px;
    }

    .filter-icon {
      position: absolute;
      left: 12px;
      width: 16px;
      height: 16px;
    }

    p {
      font-size: 16px;
    }

    .yxt-icon-error {
      color: #bfbfbf;
      cursor: pointer;
    }

    .clear {
      line-height: 40px;
      cursor: pointer;
    }

    .colorStyle {
      color: #595959;
    }

    .yxtfbiz-filter-box {
      box-sizing: border-box;
      width: 936px !important;
      margin: 0 24px;
      border: none !important;
      box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .template5-filter-box {
      position: absolute;
      left: 0%;
      z-index: 200;
      display: flex;
      width: 100%;
      min-height: 90px;
      background-color: #fff;
      border: 1px solid #eee;

      .template5-filter-left {
        position: relative;
        flex-basis: 160px;
        flex-shrink: 0;
        text-align: center;
        background-color: #fafafa;

        .yxt-button {
          position: absolute;
          top: 50%;
          left: 50%;
          margin-top: -20px;
          margin-left: -47px;
        }
      }

      .template5-filter-right {
        padding: 24px 32px;
      }

      .template5-filter-right div:nth-of-type(7) {
        padding-bottom: 0;
      }

      @media screen and (max-width: 1620px) {
        .template5-filter-right div:nth-of-type(5),
        .template5-filter-right div:nth-of-type(6),
        .template5-filter-right div:nth-of-type(7) {
          padding-bottom: 0;
        }
      }
    }

    .filter-box-item {
      width: auto;
      margin-right: 8px;
      padding: 0 8px;
      background: #f0f0f0;
      border-radius: 3px;

      .yxt-input input {
        color: #606266;
        background: #f0f0f0;
        border: none;

        &:focus {
          box-shadow: none;
        }
      }

      .yxtf-input input {
        padding-left: 0;
        color: #595959;
        background: #f0f0f0;
        border: none;

        &:focus {
          box-shadow: none;
        }
      }
    }
  }
}

.mr12 {
  margin-right: 12px;
}

.mt28 {
  margin-top: 28px;
}

.mb16 {
  margin-bottom: 16px;
}

.mt8 {
  margin-top: 8px;
}

.pb20 {
  padding-bottom: 20px;
}

.pb16 {
  padding-bottom: 16px;
}

.width60 {
  width: 60px;
}

.width120 {
  width: 120px;
}

.width49 {
  width: 49px;
}

.width305 {
  width: 305px;
}

.fileStyle {
  padding: 15px 0;
  border-bottom: 1px solid #ddd;
}
// .addFace .yxt-drawer__header, .choosePeople .yxt-drawer__header{
//   border-bottom:none !important;
//   padding-left:40px !important;
// }
// .addFace .pl40, .choosePeople .pl40{
//   padding-left:40px !important;
// }
// .articleDetail .yxt-drawer__header, .chooseTree .yxt-drawer__header{
//   padding-bottom:16px !important;
// }
// .choosePeople .yxt-tabs__nav-scroll, .choosePeople .yxt-tabs__content,.choosePeople .list{
//   padding:0 31px 0 40px  !important;
// }
.pr32 {
  padding-right: 32px !important;
}

.width970 {
  width: 970px;
}

.yxtbiz-drawer-depet-tree {
  .yxtbiz-dept-tree__input {
    width: 100% !important;
  }

  .tree-scroll-wrap {
    width: 100% !important;
  }
}

// 前台相关样式
.yxtbizf-ft-32 { font-size: 32px; }
.yxtbizf-ft-24 { font-size: 24px; }
.yxtbizf-ft-20 { font-size: 20px; }
.yxtbizf-ft-18 { font-size: 18px; }
.yxtbizf-ft-16 { font-size: 16px; }
.yxtbizf-ft-14 { font-size: 14px; }
.yxtbizf-ft-12 { font-size: 12px; }

.yxtbizf-br-2 { border-radius: 2px; }
.yxtbizf-br-4 { border-radius: 4px; }
.yxtbizf-br-8 { border-radius: 8px; }
.yxtbizf-br-12 { border-radius: 12px; }

.yxtbizf-layout-container {
  width: 1320px;
  min-width: 1320px;
  margin: 0 auto;
}

.yxtbizf-width-200 { width: 200px; }
.yxtbizf-width-312 { width: 312px; }
.yxtbizf-width-648 { width: 648px; }
.yxtbizf-width-760 { width: 760px; }
.yxtbizf-width-984 { width: 984px; }

.yxtbizf-width-422 { width: 422px; }

.yxtbizf-card-width-312 { width: 312px; }
.yxtbizf-card-width-422 { width: 422px; }

@media (max-width: 1365px) {
  .yxtbizf-layout-container {
    width: 1200px;
    min-width: 1200px;
  }

  .yxtbizf-width-200 { width: 184px; }
  .yxtbizf-width-312 { width: 286px; }
  .yxtbizf-width-648 { width: 580px; }
  .yxtbizf-width-760 { width: 682px; }
  .yxtbizf-width-984 { width: 890px; }
}

.yxtbiz-check-buttons__div {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 4000;
  display: block;
}

.yxtbiz-check-buttons__tip-default {
  // position: absolute;
  top: -10px;
  // left: 50%;
  // transform: translateX(-50%) translateY(-100%);
  z-index: 3000;
  padding: 6px 8px;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  background: #262626;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  opacity: 0.9;

  &::before {
    position: absolute;
    bottom: -4px;
    left: 50%;
    border-top: 5px solid #262626;
    border-right: 5px solid transparent;
    border-left: 5px solid transparent;
    transform: translateX(-50%);
    content: "";
  }
}

.yxtbiz-check-buttons__none {
  display: none;
}

.yxtbiz-check-buttons__events {
  pointer-events: 'hover';
}

.yxtulcdsdk-flex {
  display: flex;
}

.yxtulcdsdk-flex-wrap {
  flex-wrap: wrap;
}

.yxtulcdsdk-flex-1 {
  flex: 1;
}

.yxtulcdsdk-flex-center {
  display: flex;
  align-items: center;
}

.yxtulcdsdk-flex-space-between {
  display: flex;
  justify-content: space-between;
}

.yxtulcdsdk-flex-auto {
  flex: auto;
}

.yxtulcdsdk-flex-mid {
  display: flex;
  justify-content: center;
}

.yxtulcdsdk-flex-right {
  display: flex;
  justify-content: end;
}

.yxtulcdsdk-flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.yxtulcdsdk-flex-vertical {
  flex-direction: column;
}

.yxtulcdsdk-flex-shrink-0 {
  flex-shrink: 0;
}

.yxtulcdsdk-flex-shrink-1 {
  flex-shrink: 1;
}

.yxtulcdsdk-inline-flex {
  display: inline-flex;
}

.h100p {
  height: 100% !important;
}

.opacity8 {
  opacity: 0.8;
}

.opacity9 {
  opacity: 0.9;
}

.opacity6 {
  opacity: 0.6;
}

.opacity5 {
  opacity: 0.5;
}

.opacity4 {
  opacity: 0.4;
}

.p4 {
  padding: 4px;
}

.pre-break {
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-word;
}

.mw84 {
  max-width: 84px;
}

.mw140 {
  max-width: 140px;
}

.wp50 {
  width: 50%;
}

.minw246 {
  min-width: 246px;
}

.minw360 {
  min-width: 360px;
}

.minh500 {
  min-height: 500px;
}

.h586 {
  height: 586px;
}

.mw700 {
  max-width: 700px;
}

.h585 {
  height: 585px;
}

.mw280 {
  max-width: 280px;
}

.w162 {
  width: 162px;
}

.w168 {
  width: 168px;
}

.w344 {
  width: 344px;
}

.w140 {
  width: 140px;
}

.box-border {
  box-sizing: border-box;
}

.ml7 {
  margin-left: 7px;
}

.mr40 {
  margin-right: 40px;
}

.p8 {
  padding: 8px;
}

.mt30 {
  margin-top: 30px;
}

.yxtulcdsdkmp0 {
  margin: 0;
  padding: 0;
}

.w12 {
  width: 12px;
}

.w0 {
  width: 0;
}

.ulcdsdk-break-word {
  word-wrap: break-word;
  word-break: break-word;
}

.ulcdsdk-ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.ulcdsdk-break {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
}

.demo-o2o-play-frame {
  background-color: darkgray;
}

.word-break-all .break {
  word-break: break-all;
}

.slide-in-enter-active,
.slide-in-leave-active {
  transition: transform .5s ease-in-out;
}

.slide-in-enter {
  transform: translateX(-100%);
}

.slide-in-leave-to {
  transform: translateX(-100%);
}

.width-in-enter-active {
  transition-timing-function: ease-in-out, ease-out;
  transition-duration: 0.5s, 0.5s;
  transition-property: width, opacity;
}

.width-in-leave-active {
  transition-timing-function: ease-in-out, ease-in;
  transition-duration: 0.5s, 0.5s;
  transition-property: width, opacity;
}

.width-in-enter,
.width-in-leave-to {
  width: 0 !important;
  opacity: 0;
}

.lhnormal {
  line-height: normal;
}

.opa-tran {
  transition: opacity 0.5s linear;
}

.opah0 {
  opacity: 0;
  transition: opacity 0.3s;
}

.opah1:hover {
  opacity: 1;
}


/* rtl:begin:ignore */
[dir="rtl"] .transformrtl {
  transform: scaleX(-1);
}

/* rtl:end:ignore */
.mt0-i {
  margin-top: 0 !important;
}
