@import "mixins/mixins";
@import "common/var";

@include b(user-wrong-exam) {
  flex: 1 0 auto;
  flex-direction: column;

  > div {
    flex-grow: 0 !important;
  }

  @include b(user-header) {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    height: 44px;
    padding: 0 24px 0 16px;
    color: #595959;
    background: #fff;
    box-shadow: inset 0 -1px 0 0 #e9e9e9;
  }

  @include b(marking-top-wrapper) {
    display: flex;
    justify-content: center;
    // min-width: 1200px;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 24px;

    .yxt-divider {
      background-color: #f0f0f0;
    }

    @include b(left) {
      flex-grow: 0;
      flex-shrink: 0;
      width: 144px;
      height: auto;
    }

    @include b(marking-main) {
      position: relative;
      flex-grow: 1;
      flex-shrink: 1;
      margin: 20px auto;

      .yxt-divider--horizontal {
        margin: 16px 0;
      }
    }

    @include b(marking-left) {
      .yxtf-divider--horizontal {
        margin: 12px 0;
      }

      .yxt-divider--horizontal {
        margin: 0;
      }
    }

    @include b(marking) {
      &__title-infos {
        display: flex;
      }

      &__title-block {
        flex: 1;
        flex-shrink: 0;
        max-width: 200px;

        &:last-child {
          flex: 1.3;
        }
      }
    }

    @include b(marking-signimg) {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      box-sizing: border-box;
      margin: 0 24px;
      margin-top: 1px;
      padding: 12px 24px;

      .yxt-image {
        width: 140px;
        height: 80px;
        background: #fff;
        border: 1px solid #e9e9e9;
        border-radius: 3px;
      }
    }

    &--noleft {
      @include b(marking-main) {
        margin-right: 24px;
        overflow: hidden;
      }

      @include b(review-fixed-content) {
        width: 100% !important;
        margin-right: 24px;
        margin-left: 0;
      }
    }

    @include b(wrong-alllist) {
      margin-bottom: 22px;
      padding-top: 1px;
      font-size: 14px;
      background-color: #fff;
      border-radius: 4px;
    }
  }

  @include b(ques-checkbox) {
    line-height: 22px !important;

    .yxt-checkbox__input,
    .yxtf-checkbox__input {
      padding-top: 1px !important;
      line-height: 22px !important;
      vertical-align: top !important;
    }

    .yxt-checkbox__label,
    .yxtf-checkbox__label {
      line-height: 22px !important;
      white-space: normal !important;
    }
  }

  @include b(ques-checkbox-group) {
    box-sizing: border-box;
    width: 100%;
    line-height: 22px !important;

    @include b(ques-checkbox) {
      .yxt-checkbox__input,
      .yxtf-checkbox__input {
        padding-top: 10px !important;

        .yxtf-checkbox__inner::after {
          top: 0;
        }
      }

      .yxt-checkbox__label,
      .yxtf-checkbox__label {
        padding: 8px 0 8px 8px !important;
      }
    }
  }

  @include b(ques-radio) {
    line-height: 22px;

    .yxt-radio__input,
    .yxtf-radio__input {
      padding-top: 1px;
      line-height: 22px !important;
      vertical-align: top !important;
    }

    .yxt-radio__label,
    .yxtf-radio__label {
      display: inline-block;
      line-height: 22px !important;
      white-space: normal !important;
    }
  }

  @include b(ques-radio-group) {
    box-sizing: border-box;
    width: 100%;
    line-height: 22px !important;

    @include b(ques-radio) {
      .yxt-radio__input,
      .yxtf-radio__input {
        padding-top: 10px !important;
      }

      .yxt-radio__label,
      .yxtf-radio__label {
        padding: 8px 0 8px 8px !important;
      }
    }
  }

  @include b(marking-right) {
    display: flex;
    flex-direction: column;
    overflow: hidden;

    @include e(card) {
      display: flex;
      flex-direction: column;
      padding-top: 24px;
      overflow: auto;
      background-color: white;
      border-radius: 4px;
      // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, .05);
    }

    @include b(marking-answer__box) {
      display: flex;
      flex-shrink: 1;
      min-height: 100px;
      overflow: auto;

      .yxt-scrollbar {
        flex-grow: 1;
        flex-shrink: 1;
        height: auto !important;
      }

      .yxt-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  }

  @include b(answer-check-item) {
    display: inline-block;
    white-space: normal;
  }

  @include b(card-list) {
    margin-left: -16px;

    @include b(answer-card) {
      margin-bottom: 16px;
      margin-left: 24px;
    }

    li {
      position: relative;
      float: left;
      width: 22px;
      height: 22px;
      margin-bottom: 8px;
      margin-left: 16px;
      color: #757575;
      line-height: 22px;
      text-align: center;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 2px;

      &.success {
        color: #52c41b;
        background-color: #d9f7be;
        border-color: #d9f7be;
      }

      &.error {
        color: #fa5252;
        background-color: #fff2f0;
        border-color: #fff2f0;
      }

      &.subjective-ques {
        background-color: #f5f5f5;
        border-color: #f5f5f5;
      }

      &.answered {
        color: #fff;
        background-color: #436bff;
        border-color: #436bff;
      }

      &.answering {
        color: #436bff;
        border-color: #436bff;
      }

      &.answer-tag {
        color: #f56c6c;
        background-color: #ffece5;
        border-color: #f56c6c;
      }

      .svgico-answer-flag {
        position: absolute;
        right: -1px;
        bottom: -1px;
      }
    }
  }

  // 试题公共标签
  @include b(marking-tag) {
    height: 22px !important;
    margin-right: 24px;
    line-height: 22px;
    border: 0;
    border-radius: 2px;
  }

  @include b(review-tag) {
    @include b(marking-tag) {
      height: 22px !important;
      margin-right: 24px;
      line-height: 22px !important;
      border: 0;
      border-radius: 2px;
    }
  }

  .zindex1999 {
    z-index: 1999;
  }

  .width32 {
    width: 32px;
  }

  .vertical-align-top {
    vertical-align: top;
  }

  .clazz_width_80 {
    width: 80%;
  }

  .min-w534 {
    min-width: 534px;
  }
}
