$mianWidth: 872px; // 中间主体宽度
$rightWidth: 256px; // 右侧答题卡宽度

@media screen and (max-width: 1366px) {
  .yxtulcdsdk-pc-marking-top-wrapper {
    // width: 1200px;
  }

  $mianWidth: auto;

  .yxtulcdsdk-review-fixed-head {
    width: $mianWidth;

    &--no-right {
      width: $mianWidth + $rightWidth;
    }

    .yxt-divider--horizontal {
      margin: 20px 0;
    }
  }

  .yxtulcdsdk-review-fixed-content {
    width: $mianWidth;

    &--no-right {
      width: $mianWidth + $rightWidth;
    }

    &--practice {
      width: 1000px;
    }
  }
}
