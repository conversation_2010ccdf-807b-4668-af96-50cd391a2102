@import "mixins/mixins";
@import "common/var";

@include b(exam-container) {
  flex-grow: 1;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  min-height: 100%;
  word-break: break-word;
  // overflow: auto;
  background: #f5f5f5;
  border-radius: 8px;

  >.yxt-scrollbar {
    height: 100%;
    border-radius: 8px;
  }

  .yxt-scrollbar__view {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100%;

    > div {
      flex-grow: 1;
    }
  }

  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-wrap: normal;
    word-break: normal;
  }

  @include b(uexam-preview) {
    margin: 24px auto;
  }

  @include m(page) {
    height: auto;
    min-height: auto;

    > div {
      min-width: 1200px;
      min-height: 100%;
    }

    .yxtulcdsdk-pc-marking-top-wrapper {
      max-width: 1320px;
    }
  }

  @include m(fs) {
    border-radius: 0;

    >.yxt-scrollbar {
      border-radius: 0;
    }
  }

  @include m(exporting) {
    &.yxtulcdsdk-exam-container--page > div {
      min-width: auto;
    }
  }

  @include m(no-radius) {
    border-radius: 0;

    >.yxt-scrollbar {
      border-radius: 0;
    }
  }
}

@include b(full-screen) {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 999;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 12px;
  color: #fff;
  background: #000;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0.6;
}

@include b(answer-card) {
  display: flex;
  flex-direction: column;
  width: 100%;
  // filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.05));

  @include e(top) {
    position: relative;
    flex-grow: 0;
    flex-shrink: 0;
    padding: 16px 0;
    background: white;
    background: radial-gradient(circle at bottom left, transparent 4px, white 0) bottom left, radial-gradient(circle at bottom right, transparent 4px, white 0) bottom right;
    background-repeat: no-repeat;
    background-size: 50% 100%;
    border-radius: 4px 4px 0 0;

    &::after {
      position: absolute;
      bottom: 0;
      left: -12px;
      width: calc(100% - 16px);
      height: 1px;
      margin: 0 20px;
      background-image: linear-gradient(to right, #e9e9e9 75%, #fff 0%);
      background-repeat: repeat-x;
      background-position: bottom;
      background-size: 16px 1px;
      content: '';
    }

    & + & {
      background: radial-gradient(circle at top left, transparent 4px, white 0) top left, radial-gradient(circle at top right, transparent 4px, white 0) top right, radial-gradient(circle at bottom right, transparent 4px, white 0) bottom right, radial-gradient(circle at bottom left, transparent 4px, white 0) bottom left;
      background-repeat: no-repeat;
      background-size: 50% 50%;
      border-radius: 0;
    }
  }

  @include e(bottom) {
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    flex-shrink: 1;
    align-items: center;
    justify-content: stretch;
    min-height: 172px;
    padding: 16px 0;
    overflow: auto;
    background: white;
    background: radial-gradient(circle at top left, transparent 4px, white 0) top left, radial-gradient(circle at top right, transparent 4px, white 0) top right;
    background-repeat: no-repeat;
    background-size: 50% 100%;
    border-radius: 0 0 4px 4px;
  }

  @include e(btn) {
    flex-grow: 0;
    flex-shrink: 0;
    margin-top: 16px;
  }

  @include m(result) {
    filter: none;

    @include e(top) {
      background: white;
      border-radius: 4px;
      // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);

      &::after {
        display: none;
      }
    }

    @include e(bottom) {
      margin-top: 8px;
      background: white;
      border-radius: 4px;
      // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);
    }
  }
}

@include b(ote-ai-content) {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  min-height: 122px;
  padding: 8px 12px;
  background: var(--color-primary-6);
  background: linear-gradient(135deg, rgba(248, 148, 160, 1), rgba(149, 85, 254, 1), rgba(82, 200, 245, 1));
  border-radius: 4px;

  > div {
    position: relative;
    z-index: 1;
  }

  &::before {
    position: absolute;
    top: 1px;
    right: 1px;
    bottom: 1px;
    left: 1px;
    background-color: white;
    border-radius: 3px;
    content: ' ';
  }

  &::after {
    position: absolute;
    top: 1px;
    right: 1px;
    bottom: 1px;
    left: 1px;
    background: linear-gradient(135deg, rgba(199, 120, 255, .03) 0, rgba(118, 162, 255, .03) 50%, rgba(67, 219, 255, .03) 100%);
    border-radius: 3px;
    content: ' ';
  }
}

@include b(exam-header) {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  height: 44px;
  padding: 0 24px 0 16px;
  color: #595959;
  background: #fff;
  box-shadow: inset 0 -1px 0 0 #e9e9e9;

  @include m(nav) {
    height: 56px;
    color: #262626;
    font-weight: 500;
  }
}

@include b(user-exam-end) {
  padding-top: 40px;
  text-align: center;

  // 防止加载慢出现抖动
  img {
    width: 296px;
    height: 223px;
  }
}

/********* userexam.css ************/
@include b(uexam-preview-container) {
  width: 100%;
  margin: 0 auto;
}

@include b(uexam-preview) {
  position: relative;
  min-width: 812px;
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 12px;
  font-size: 14px;

  &.show-info {
    width: 100%;
    background-color: #fff;
    border-radius: 12px;
  }

  @include e(task) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    height: 32px;
    margin: -5px auto;
  }

  @include b(uexam-preview-code) {
    position: relative;
    float: right;
    height: 40px;
    padding: 12px 12px 0 0;
    font-size: 40px;
    line-height: 0;
    cursor: pointer;

    @include e(mark) {
      position: absolute;
      bottom: 0;
      width: 0;
      height: 0;
      border-right: 26px solid transparent;
      border-bottom: 26px solid #fff;
    }
  }

  @include b(uexam-preview-content) {
    padding: 44px 0 36px 0;

    .main-top-wrap {
      width: calc(100% - 48px);
      min-width: 764px;
      max-width: 1000px;
      margin: 0 auto;
    }

    @include e(title) {
      color: #262626;
      font-weight: 500;
      font-size: 24px;
      line-height: 26px;
      text-align: center;
    }

    @include e(time) {
      margin-top: 16px;
      color: #595959;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      text-align: center;
    }

    @include e(base) {
      width: 100%;
      margin-top: 28px;
      background: #fafafa;
      border-radius: 10px;

      @include e(base-row-flex) {
        display: flex;
        align-items: center;
        height: 250px;
        min-height: 250px;

        .info-left,
        .info-right {
          width: 50%;

          & > div {
            display: flex;
            align-items: center;

            & + div {
              margin-top: 32px;
            }
          }

          .info-text {
            margin-left: 8px;
            color: #757575;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;

            & > span {
              color: #262626;
            }
          }
        }

        .info-line,
        .info-line-course {
          margin: 0 16px;

          & > span {
            display: block;
            width: 1px;
            height: 195px;
            background-color: #f0f0f0;
          }
        }

        .info-line-course {
          margin: 0 !important;
        }

        .info-left {
          padding-left: 70px;
        }

        .info-right {
          padding-left: 70px;
        }

        @include m(practice) {
          height: 140px;
          min-height: 140px;

          .info-line,
          .info-line-course {
            margin: 20px 16px;

            & > span {
              height: 100px;
            }
          }
        }
      }
    }

    @include e(code) {
      position: relative;
      width: 100%;
      min-height: 256px;
      margin-top: 4px;
      padding-bottom: 18px;
      text-align: center;
      background: #fff;
      border-radius: 10px;

      & > p {
        margin: 0;
      }

      @include m(text) {
        position: absolute;
        bottom: 8px;
        width: calc(100% - 10px);
      }
    }

    @include e(explain) {
      width: calc(100% - 48px);
      min-width: 764px;
      max-width: 1000px;
      margin: 24px auto 0 auto;
      overflow-y: auto;

      // 滚动条样式重写
      .new-scroll {
        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
          background-color: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #e9e9e9;
          border-radius: 10px;
        }

        &::-webkit-scrollbar-track {
          /* 滚动条里面轨道 */
          background: transparent;
          border: 1px solid transparent;
        }
      }

      .exam-rule {
        & + .exam-rule {
          margin-top: 24px;

          .rule-info {
            line-height: 22px;
          }
        }

        .rule-title {
          display: flex;
          align-items: center;
          font-weight: 500;
          font-size: 16px;
          line-height: 18px;

          & > span {
            margin-left: 8px;
            color: #262626;
          }
        }

        .rule-info {
          margin-top: 8px;
          color: #595959;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;

          & > p {
            text-align: center;
          }
        }
      }

      @include m(practice) {
        height: auto;
      }
    }

    @include e(viewmore) {
      width: calc(100% - 48px);
      min-width: 764px;
      max-width: 800px;
      margin: 4px auto 0 auto;
      overflow-y: auto;
      text-align: center;
    }

    @include e(result) {
      width: calc(100% - 48px);
      min-width: 764px;
      max-width: 1000px;
      margin: 0 auto;

      .result-title {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 16px;
        line-height: 18px;

        & > span {
          margin-left: 8px;
          color: #262626;
        }
      }

      .result-box {
        height: 88px;
        border-radius: 4px;

        .passed {
          font-size: 18px;
          line-height: 26px;
        }

        .history {
          font-size: 14px;
          line-height: 24px;
        }
      }
    }
  }

  @include b(uexam-preview-bottom) {
    width: 100%;
    height: 64px;
    line-height: 64px;
    text-align: center;
    background-color: #fff;

    .yxtf-button--primary {
      min-width: 112px;
    }

    .date-time {
      display: inline-block;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;

      i {
        display: inline-block;
        width: 30px;
        height: 30px;
        font-weight: 500;
        font-size: 16px;
        font-style: normal;
        line-height: 32px;
        text-align: center;
        border-style: solid;
        border-width: 1px;
        border-radius: 4px;
      }

      .unit {
        margin: 0 8px;
      }
    }

    @include m(practice) {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 36px;
      margin: 4px 0 40px;
      line-height: 36px;
    }
  }

  @include m(practice) {
    box-sizing: border-box;
    // min-height: 100%;
    padding-bottom: 12px;
  }
}

@include b(info-list) {
  z-index: 2;
  display: flex;
  flex-wrap: wrap;

  > * {
    display: inline-flex;
    margin-top: 8px;
    margin-right: 24px;

    &:last-child {
      margin-right: 0;
    }

    > :first-child {
      flex-shrink: 0;
      margin-right: 12px;
      color: #8c8c8c;
    }

    > :last-child {
      color: #262626;
    }
  }
}

/*
考试结果统计
*/
@include b(uexam-res) {
  max-width: 1200px;
  margin: 0 auto;

  @include e(tourist) {
    margin: 0 auto 32px;
  }

  @include b(uexam-res-top) {
    display: flex;
    margin-top: 16px;
    padding: 24px 24px 24px 32px;
    background-color: #fff;
    border-radius: 4px;

    @include e(base) {
      position: relative;
      flex-grow: 1;
      flex-shrink: 1;
      width: 100%;

      .yxtulcdsdk-custom-icon {
        position: absolute;
        top: 0;
        right: 0;
        left: -98px;

        &:last-child {
          left: -74px;
        }
      }
    }
  }

  @include b(uexam-res-main) {
    display: flex;
    margin-top: 16px;

    @include e(bar) {
      flex-grow: 0;
      flex-shrink: 0;
      min-width: 178px;
      margin-right: 16px;
      border-radius: 4px;

      @include m(box) {
        overflow: hidden;
        background-color: #fff;
        border-radius: 4px;
        // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);

        .yxtf-divider--horizontal {
          margin: 0;
          color: #f0f0f0;
        }
      }

      @include m(title) {
        min-height: 56px;

        .yxtf-divider--horizontal {
          margin: 0;
          color: #f0f0f0;
        }
      }

      @include m(ul) {
        display: flex;
        max-height: 572px;

        li {
          position: relative;
          box-sizing: border-box;
          width: 100%;
          padding: 0 8px;

          .yxtf-divider--horizontal {
            position: absolute;
            top: 0;
            width: calc(100% - 20px);
            margin: 0;
          }

          &.actived {
            >div {
              background: #f5f5f5;
              border-radius: 8px;
            }

            .yxtf-divider--horizontal {
              display: none;
            }
          }
        }

        li.actived + li {
          .yxtf-divider--horizontal {
            display: none;
          }
        }
      }
    }

    @include e(content) {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      flex-shrink: 1;
      width: 0;
      min-height: 320px;

      @include e(content__base) {
        // height: 196px;
        background-color: #fff;
        border-radius: 4px;

        @include m(shape) {
          display: flex;
          padding-top: 32px;

          .yxtulcdsdk-ueresult-ev {
            display: flex;
            flex: 1;
            align-items: flex-start;
            justify-content: flex-start;
            padding-right: 4px;
            padding-left: 32px;

            &:not(:last-child) {
              position: relative;

              &::after {
                position: absolute;
                // top: 50%;
                top: 36px;
                right: 0;
                display: block;
                width: 1px;
                height: 55px;
                background-color: #f0f0f0;
                transform: translateY(-50%);
                content: '';
              }
            }
          }

          .yxtulcdsdk-ueresult-ev-1 {
            float: left;
            padding-top: 16px;
          }

          .yxtulcdsdk-ueresult-ev-2 {
            padding-left: 16px;
          }
        }

        @include m(time) {
          padding: 24px;
          padding-left: 32px;
        }
      }

      @include e(content__sta) {
        min-height: 516px;
        margin-top: 16px;
        padding-bottom: 24px;
        background-color: #fff;
        border-radius: 4px;

        @include m(left) {
          display: flex;
          flex-direction: column;
          flex-shrink: 0;
          align-items: center;
          width: 268px;

          .yxtf-divider--horizontal {
            width: 220px;
            margin: 12px 60px;
          }
        }

        @include m(right) {
          flex-grow: 1;
          flex-shrink: 1;
          margin: -8px;
          margin-right: 16px;

          ul {
            li {
              display: inline-block;
              width: calc(50% - 16px);
              min-height: 126px;
              margin: 8px;
              border-radius: 4px;

              &:first-child {
                display: none;
                width: calc(100% - 16px);
              }
            }

            .yxtf-progress {
              flex-grow: 1;
              flex-shrink: 1;

              .yxtf-progress-bar__outer {
                min-width: auto;
              }
            }
          }
        }
      }

      @include e(content__pending) {
        width: 100%;
        height: 514px;
        margin-top: 16px;
        background-color: #fff;
        border-radius: 4px;

        @include m(img) {
          padding-top: 36px;
          text-align: center;
        }
      }

      @include e(content__rank) {
        width: 100%;
        margin-top: 16px;
        background-color: #fff;
        border-radius: 4px;
      }
    }

    @include e(pending) {
      width: 100%;
      height: 736px;
      background-color: #fff;
      border-radius: 4px;

      @include m(img) {
        padding-top: 155px;
        padding-left: 330px;
      }
    }
  }

  @include m(small) {
    .yxtulcdsdk-ueresult-ev {
      padding-left: 16px;

      &:first-child {
        padding-left: 24px;
      }
    }

    .yxtulcdsdk-ueresult-ev-1 {
      display: none;
    }

    .yxtulcdsdk-ueresult-ev-2 {
      padding-left: 0;
    }

    .yxtulcdsdk-uexam-res-main__content__base--time {
      padding-left: 24px;
    }

    .yxtulcdsdk-uexam-res-main__content__sta--left {
      display: none;
    }

    .yxtulcdsdk-uexam-res-main__content__sta--right {
      margin-left: 16px;

      ul li:first-child {
        display: inline-block;
      }
    }

    .font-size-32,
    .standard-size-32,
    .font-size-40,
    .standard-size-40 {
      font-size: 20px !important;
      line-height: 28px !important;
    }
  }
}

// 处理*的pop居中问题
.yxtulcdsdk-ueresult-ev-starpop {
  margin-top: -10px;
  margin-left: 24px !important;
}

@include b(examing) {
  word-break: break-word;

  .yxtulcdsdk-pc-marking-top-wrapper {
    min-height: 100%;
  }

  .yxtulcdsdk-review-fixed-content {
    display: flex;
    flex-direction: column;
    min-height: 100%;
  }

  @include e(left) {
    flex-grow: 0;
    flex-shrink: 0;
    width: 144px;
    margin-right: 24px;
  }

  @include e(right) {
    flex-grow: 0;
    flex-shrink: 0;
    width: 256px;
    margin-left: 24px;
  }

  @include e(progress) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 8px 24px;
    color: #262626;
    background: #fff;
    border-radius: 4px;
    // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);

    .yxtf-progress-bar__outer {
      min-width: 100px;
    }
  }

  @include e(answer-card) {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 40px);
    background: #fff;
    border-radius: 4px;

    .card-top {
      flex-grow: 0;
      flex-shrink: 0;
      padding: 20px 20px 0;

      .titles {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 12px;

        & > :first-child {
          flex-grow: 1;
          flex-shrink: 0;
          margin-right: 4px;
          margin-bottom: 4px;
          color: #262626;
          font-weight: 500;
          font-size: 18px;
          line-height: 26px;
        }

        & > :last-child {
          display: flex;
          flex-grow: 0;
          flex-shrink: 0;
          align-items: flex-start;
          max-width: 100%;
          margin-bottom: 4px;
          color: #595959;
        }
      }

      .types {
        > span {
          position: relative;
          display: inline-block;
          padding-left: 20px;
          font-size: 12px;
          line-height: 20px;

          &::after {
            position: absolute;
            top: 4px;
            left: 0;
            box-sizing: border-box;
            width: 12px;
            height: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            content: ' ';
          }

          &:not(:last-child) {
            margin-right: 16px;
          }
        }

        .type-correct::after {
          background: #d7f7c1;
          border: none;
        }

        .type-wrong::after {
          background: #fef2f0;
          border: none;
        }
      }
    }

    .card-body {
      display: flex;
      flex-grow: 0;
      flex-shrink: 1;
      justify-content: stretch;
      margin-top: -8px;
      padding: 0 2px 12px 8px;
      overflow: hidden;

      .yxt-scrollbar {
        // height: auto !important;
        // flex-grow: 1;
        // flex-shrink: 1;
      }

      .ques-no {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        width: 24px;
        height: 24px;
        margin: 8px 12px;
        color: #757575;
        font-size: 12px;
        word-break: normal;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
        cursor: pointer;

        &.current {
          color: var(--color-primary-6);
          border-color: var(--color-primary-6);
        }

        &.correct {
          color: #52c41b;
          background: #d7f7c1;
          border: none;

          &.current {
            border: 1px solid #8bdf9a;
          }
        }

        &.wrong {
          color: #fa5252;
          background: #fef2f0;
          border: none;

          &.current {
            border: 1px solid #ffa1a4;
          }
        }
      }
    }
  }

  @include e(watermark) {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    text-decoration: none;
    cursor: default;
    pointer-events: none;

    canvas {
      text-decoration: none;
      cursor: default;
      pointer-events: none;
    }
  }

  @include e(bottom) {
    // position: absolute;
    // bottom: 0;
    width: 100%;
  }

  @include e(btns) {
    padding-top: 24px;
    padding-bottom: 24px;
    text-align: center;
    background-color: white;
  }

  @include e(result) {
    box-sizing: border-box;
    // width: 824px;
    width: calc(100% - 48px);
    margin-bottom: 24px;
    margin-left: 24px;
    padding: 16px 24px 24px;
    background-color: #fafafa;

    @include e(result-line) {
      display: flex;
      flex-direction: row;

      & + & {
        margin-top: 16px;
      }

      @include e(result-line-left) {
        flex-grow: 0;
        flex-shrink: 0;
      }

      @include e(result-line-right) {
        flex-grow: 1;
        flex-shrink: 1;
        line-height: 20px;
      }
    }
  }
}

// 考前签名
@include b(sign-pop) {
  @include e(title) {
    display: flex;
    align-items: center;
    height: 24px;
    color: #262626;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;

    span {
      flex-grow: 1;
      flex-shrink: 1;
      text-align: left;
    }

    svg {
      flex-grow: 0;
      flex-shrink: 0;
      width: 20px !important;
      height: 20px !important;
      margin-left: 12px;
    }
  }

  @include e(desc) {
    margin-top: -8px;
    color: #8c8c8c;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
  }

  @include e(panel) {
    width: 472px;
    height: 266px;
    margin-top: 16px;
    overflow: hidden;
    background: #f4f5f6;
    border-radius: 4px;

    canvas {
      position: relative !important;
    }
  }

  @include e(bottom) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 24px;

    @include e(icons) {
      display: flex;
      flex-grow: 1;
      flex-shrink: 1;
      align-items: center;

      svg {
        margin-left: -2px;
      }
    }

    @include e(btn) {
      flex-grow: 0;
      flex-shrink: 0;

      .yxt-button + .yxt-button {
        margin-left: 16px;
      }
    }
  }
}

@include b(user-exam) {
  flex-grow: 1;
  align-self: stretch;

  @include e(answercard) {
    position: absolute;
    top: 24px;
    right: 24px;

    >svg {
      visibility: hidden;
    }
  }
}

@include b(confirm-dialog) {
  padding-top: 24px;
  text-align: center;

  @include e(tip-icon) {
    height: 80px;
  }

  @include e(tip-msg) {
    margin-top: 22px;
    color: #262626;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
  }

  @include e(btns) {
    margin-top: 32px;
    margin-bottom: 22px;
  }

  .yxtulcdsdk-button-group__btns {
    .yxtf-button--others {
      max-width: 130px;
    }
  }
}

/********* marking.css ************/
$rightWidth: 256px; // 右侧答题卡宽度

.yxtulcdsdk-pc-marking-main {
  position: relative;
  // 不要调整位置
  margin: 0 auto;
  overflow: auto;

  &--ov {
    overflow: visible;
  }

  &.img-no-point-event {
    img {
      pointer-events: none;
    }
  }

  &.text-no-select {
    user-select: none;
  }

  .yxtulcdsdk-ques-radio-group,
  .yxtulcdsdk-ques-checkbox-group {
    &.active {
      width: 100%;
      margin-left: -4px;

      .yxtf-radio__label,
      .yxtf-checkbox__label {
        position: relative;
        width: calc(100% - 24px);
      }
    }
  }

  .yxtulcdsdk-pc-marking-title__margin {
    margin-bottom: -10px;
  }

  .yxtulcdsdk-ques-radio,
  .yxtulcdsdk-ques-checkbox {
    &.active {
      width: calc(100% - 32px);
      padding-right: 0;
      padding-left: 12px;

      &:hover {
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }

    &.is-disabled:hover {
      background-color: transparent;
      border-radius: unset;
    }
  }

  .yxt-divider--horizontal {
    margin: 16px 0;
  }

  .marking-shadow {
    word-wrap: break-word;
    background: rgba(255, 255, 255, 1);
    border-radius: 4px;
    // box-shadow: 0 3px 6px 0 rgba(2, 9, 84, 0.05);
  }

  .marking-top {
    height: 147px;
  }

  .otec-point-input {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    width: 90px;
    height: 32px;
    border: 1px solid rgba(217, 217, 217, 1);
    border-radius: 4px;

    input {
      float: left;
      box-sizing: border-box;
      width: 72px;
      height: 30px;
      padding: 0 5px;
      line-height: 20px;
      background: #fff;
      border: 0;
      outline: none;

      &[disabled],
      &.disabled {
        background: #fafafa;
        cursor: not-allowed;
      }
    }

    .unit {
      position: absolute;
      top: -1px;
      right: -1px;
      box-sizing: border-box;
      height: 32px;
      padding: 0 4px;
      line-height: 30px;
      text-align: center;
      background: rgba(245, 245, 245, 1);
      border: 1px solid rgba(217, 217, 217, 1);
      border-radius: 0 4px 4px 0;
    }
  }

  .check-btn {
    display: inline-block;
    box-sizing: border-box;
    width: 32px;
    height: 32px;
    padding-top: 2px;
    color: #bfbfbf;
    line-height: 30px;
    text-align: center;
    border: 1px solid rgba(217, 217, 217, 1);
    border-radius: 4px;

    &.success {
      color: #52c41b;
      border-color: #52c41b;
    }

    &.error {
      color: #fa5252;
      border-color: #ff525b;
    }

    &.none {
      background: transparent;
      border-color: transparent;
    }
  }

  .title-has-img {
    max-width: 85%;

    p {
      margin: 0;
    }

    img {
      max-width: 100%;
    }
  }
}

.yxtulcdsdk-marking-name {
  width: calc(100% - 48px);
}

.yxtulcdsdk-review-fixed-content {
  width: auto;
  // margin-top: 36px;
  // margin-right: 24px;
  // margin-left: 24px;
}

.yxtulcdsdk-pc-marking-top-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;

  img {
    max-width: 100%;
  }

  .yxt-divider {
    background-color: #f0f0f0;
  }

  .yxtulcdsdk-pc-marking-main {
    flex-grow: 1;
    flex-shrink: 1;
  }

  .yxtulcdsdk-pc-right {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    align-items: stretch;
    width: 256px;
    height: 100%;
  }

  .yxtulcdsdk-marking {
    &__title-infos {
      display: flex;
    }

    &__title-block {
      flex: 1;
      flex-shrink: 0;
      max-width: 200px;

      &:last-child {
        flex: 1.3;
      }
    }
  }

  .yxtulcdsdk-pc-marking-signimg {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    margin-top: 1px;
    padding: 12px 24px;

    .yxt-image {
      width: 140px;
      height: 80px;
      background: #fff;
      border: 1px solid #e9e9e9;
      border-radius: 3px;
    }
  }
}

.yxtulcdsdk-review-exam_title {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  &--center {
    justify-content: center;
  }

  & > div {
    flex-shrink: 0;
    max-width: 100%;
    margin-right: 32px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 试题公共标签
.yxtulcdsdk-pc-marking-tag {
  width: auto !important;
  height: 22px !important;
  margin-right: 24px;
  line-height: 22px;
  border: 0;
  border-radius: 2px;
}

.yxtulcdsdk-review-tag {
  .yxtulcdsdk-pc-marking-tag {
    height: 22px !important;
    margin-right: 24px;
    line-height: 22px !important;
    border: 0;
    border-radius: 2px;
  }
}

// 答题卡
.yxtulcdsdk-pc-card-list {
  margin-left: -18px;

  .uexam-answer-card {
    margin-bottom: 16px;
    margin-left: 24px;

    li:nth-child(5n+1) {
      margin-left: 20px;
    }
  }

  li {
    position: relative;
    float: left;
    box-sizing: content-box;
    width: 22px;
    height: 22px;
    margin-bottom: 16px;
    margin-left: 24px;
    color: #757575;
    line-height: 22px;
    text-align: center;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 2px;

    &.success {
      color: #52c41b;
      background-color: #d7f7c1;
      border-color: #d7f7c1;
    }

    &.error {
      color: #fa5252;
      background-color: #fef2f0;
      border-color: #fef2f0;
    }

    &.done {
      color: #fff;
      background-color: var(--color-primary-6);
      border-color: var(--color-primary-6);
    }

    &.current {
      color: var(--color-primary-6);
      background: #fff;
      border-color: var(--color-primary-6);

      &.success {
        color: #52c41b;
        background-color: #d9f7be;
        border-color: #52c41b;
      }

      &.error {
        color: #fa5252;
        background-color: #fff2f0;
        border-color: #fa5252;
      }
    }

    &.subjective-ques {
      background-color: #f5f5f5;
      border-color: #f5f5f5;
    }

    &.answered {
      color: #fff;
      background-color: var(--color-primary-6);
      border-color: var(--color-primary-6);
    }

    &.answering {
      color: var(--color-primary-6);
      background-color: #ffff;
      border-color: var(--color-primary-6);

      &.answered {
        color: #fff;
        background-color: var(--color-primary-6);
        border-color: var(--color-primary-6);
      }
    }

    &.answer-tag {
      color: #f56c6c;
      background-color: #ffece5;
      border-color: #f56c6c;
    }

    .svgico-answer-flag {
      position: absolute;
      right: -1px;
      bottom: -1px;
    }
  }
}

.decorate-answer {
  position: relative;
  top: 2px;
  display: inline-block;
  box-sizing: content-box;
  width: 10px;
  height: 10px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 3px;

  &.answered {
    background-color: var(--color-primary-6);
    border-color: var(--color-primary-6);
  }

  &.answering {
    border-color: var(--color-primary-6);
  }

  &.answer-right {
    background-color: #d7f7c1;
    border: 1px solid #d7f7c1;
  }

  &.answer-wrong {
    background-color: #fef2f0;
    border: 1px solid #fef2f0;
  }

  &.subjective-ques {
    background-color: #f5f5f5;
    border: 1px solid transparent;
  }

  &.answertag {
    background-color: #ffece5;
    border-color: #f56c6c;
  }

  .svgico-answer-flag {
    position: absolute;
    right: -1px;
    bottom: -1px;
  }
}

/********* userexam.css *********/

// mediaPlayer
.yxtulcdsdk-media-player-container {
  .yxtulcdsdk-media-player {
    position: relative;
    display: inline-block;
    overflow: hidden;
    overflow: visible\0;
    border-radius: 4px;
    border-radius: 0\0;

    .transcoding-tips {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 1;
      display: none;
      height: 40px;
      margin-top: -20px;
      color: #fff;
      line-height: 40px;
      transform: translateX(-50%);

      .yxt-svg-icon {
        vertical-align: -2px;
      }
    }

    &.yxtulcdsdk-file-transcodeing {
      .transcoding-tips {
        display: block;
        width: 100%;
        text-align: center;
      }

      .yxt-biz-video-container {
        background-color: #000;

        .jw-title,
        .jw-icon-container {
          display: none;
        }
      }
    }

    .player-action-button {
      position: absolute;
      top: 6px;
      right: 8px;
      z-index: 1;
      color: #fff;
      cursor: pointer;
    }
  }
}

// quesAccuracy
.pl48 {
  padding-left: 50px;
}

.yxtulcdsdk-big-viewer {
  .yxtulcdsdk-big-viewer__mask {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 3000;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background-color: rgba(0, 0, 0, 0.6);

    .yxtulcdsdk-big-viewer__video {
      display: block;
      max-width: 100%;
      background-color: #000;
    }

    .yxtulcdsdk-big-viewer__del {
      position: fixed;
      top: -40px;
      right: -40px;
      width: 80px;
      height: 80px;
      background-color: rgba(0, 0, 0, 0.9);
      border-radius: 50%;
      cursor: pointer;

      .del-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        color: white;
        transform: translateX(-100%);
      }
    }
  }
}

/******* exam.css *********/

// 复选框支持右侧多行
.yxtulcdsdk-ques-checkbox {
  line-height: 22px !important;

  .yxt-checkbox__input,
  .yxtf-checkbox__input {
    padding-top: 1px !important;
    line-height: 22px !important;
    vertical-align: top !important;

    .yxtf-checkbox__inner::after {
      //top: 4px;
    }
  }

  .yxt-checkbox__label,
  .yxtf-checkbox__label {
    line-height: 22px !important;
    white-space: normal !important;
  }
}

.yxtulcdsdk-ques-checkbox-group {
  line-height: 22px !important;

  .yxtulcdsdk-ques-checkbox {
    .yxt-checkbox__input,
    .yxtf-checkbox__input {
      padding-top: 11px !important;
    }

    .yxt-checkbox__label,
    .yxtf-checkbox__label {
      padding: 8px 0 8px 8px !important;
    }
  }
}

// 单选框支持右侧多行
.yxtulcdsdk-ques-radio {
  line-height: 22px;

  .yxt-radio__input,
  .yxtf-radio__input {
    padding-top: 1px;
    line-height: 22px !important;
    vertical-align: top !important;
  }

  .yxt-radio__label,
  .yxtf-radio__label {
    display: inline-block;
    line-height: 22px !important;
    white-space: normal !important;
  }
}

.yxtulcdsdk-ques-radio-group {
  line-height: 22px !important;

  .yxtulcdsdk-ques-radio {
    .yxt-radio__input,
    .yxtf-radio__input {
      padding-top: 10px !important;
    }

    .yxt-radio__label,
    .yxtf-radio__label {
      padding: 8px 0 8px 8px !important;
    }
  }
}

/******* common.css *********/
// 自定义icon
@include b(custom-icon) {
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 100% 100%;

  @include e (content) {
    max-width: 90%;
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
    text-align: center;
    transform: rotate(-20deg);
  }

  svg {
    top: 0;
    left: 0;
  }
}

@include b(form) {
  display: flex;
  flex-direction: row;

  @include e(sub) {
    flex-grow: 0;
    flex-shrink: 0;
  }

  @include e(content) {
    flex-grow: 1;
    flex-shrink: 1;
  }
}

/* 假播放器 */
@include b(player-fake) {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: black;

  @include e(play) {
    box-sizing: border-box;
    height: 30px;
    padding: 0;
    background-color: black;
    border-color: transparent transparent transparent white;
    border-style: solid;
    border-width: 15px 0 15px 30px;

    &::after {
      position: absolute;
      width: 54px;
      height: 54px;
      margin-top: -28px;
      margin-left: -46px;
      border: 1px solid #333;
      border-radius: 50%;
      content: ' ';
    }
  }
}

/*********   ques.css   **********/

.yxtulcdsdk-ques-answer_card__type {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: left;
  min-height: 20px;
  padding: 0 16px;
  color: #262626;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  vertical-align: middle;

  span {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    margin-right: 16px;
    overflow: hidden;
  }

  span:last-child {
    margin-right: 0;
  }

  i {
    display: inline-block;
    flex-shrink: 0;
    box-sizing: content-box;
    width: 12px;
    height: 12px;
    margin-right: 4px;
    vertical-align: middle;
    border-radius: 2px;
  }

  small {
    vertical-align: middle;
  }

  &--small i {
    width: 10px;
    height: 10px;
  }
}

.yxtulcdsdk-ques-answer_type__1 {
  background-color: #fff;
  border: 1px solid #d9d9d9;
}

.yxtulcdsdk-ques-answer_type__2 {
  background-color: #d7f7c1;
  border: 1px solid #d7f7c1;
}

.yxtulcdsdk-ques-answer_type__3 {
  background-color: #fef2f0;
  border: 1px solid #fef2f0;
}

.yxtulcdsdk-ques-answer_type__4 {
  background-color: #fff;
  border: 1px solid var(--color-primary-6);
}

.yxtulcdsdk-ques-answer_type__5 {
  background-color: var(--color-primary-6);
  border: 1px solid var(--color-primary-6);
}

.yxtulcdsdk-marking-exam-top {
  padding: 16px 16px 16px 24px !important;
  color: #262626;

  &-title {
    display: flex;
    align-items: center;
    justify-content: center;

    &__title {
      flex-grow: 1;
      flex-shrink: 1;
    }

    &__tag {
      position: relative;
      z-index: 1;
      flex-grow: 0;
      flex-shrink: 0;
      align-self: flex-start;
      width: 66px;
      margin-left: 16px;

      & > div {
        position: absolute;
      }
    }

    & + .yxt-divider--horizontal {
      margin-top: 10px !important;
      margin-bottom: 18px !important;
    }
  }
}

.yxtulcdsdk-review-fixed-content__margin {
  margin-top: 20px !important;
}

.yxtulcdsdk-ques-score_type {
  margin-top: 29px;
  line-height: 32px;

  &::after {
    display: block;
    clear: both;
    height: 0;
    content: '';
  }
}

.yxtulcdsdk-ques-score_radio {
  margin-right: 16px;
}

.yxtulcdsdk-ques-score_input {
  display: inline-block;

  .yxtf-input__inner {
    padding: 4px 10px;
  }

  & > span {
    display: flex;
    flex-direction: row;
    justify-items: center;
    margin-bottom: 16px;

    small {
      flex-shrink: 0;
      width: 30px;
      height: 32px;
      //margin-right: 5px;
      font-size: 14px;
      line-height: 32px;
      text-align: left;
    }

    .yxtf-input {
      width: 81px;
    }
  }
}

.yxtulcdsdk-ques-score_btn {
  float: right;
  text-align: right;
}

.yxtulcdsdk-ques-score_input__item {
  box-sizing: border-box;
  width: 81px;
  height: 32px;
  margin-right: 12px;
  padding: 0 8px;
  color: #262626;
  border: 1px solid #d9d9d9;
  border-radius: 4px;

  &::placeholder {
    color: #bfbfbf;
  }
}

.yxtulcdsdk-ques-score_checed {
  box-sizing: border-box;
  height: 32px;

  & > span {
    box-sizing: border-box;
    padding-top: 2px;
  }
}

.yxtulcdsdk-ques-score_radio,
.yxtulcdsdk-ques-score_checed,
.yxtulcdsdk-ques-score_input {
  float: left;
}

// 批阅时特殊处理输入框颜色
.yxtulcdsdk-review-input {
  &--disabled {
    &.yxtf-input.is-disabled .yxtf-input__inner {
      color: #595959;
    }

    &.yxtf-textarea.is-disabled .yxtf-textarea__inner {
      color: #595959;
    }
  }
}

.yxtulcdsdk-ques-score__item {
  box-sizing: border-box;
  height: 40px;
  padding: 5px 20px;
  line-height: 40px;

  span {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    vertical-align: top;
  }

  .yxtulcdsdk-ques-score__i {
    width: 81px;
    margin-right: 12px;
    padding-left: 12px;
    color: #262626;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
}

.yxtulcd-practice-sta {
  &__info {
    display: flex;
    justify-content: space-between;
    max-width: 80%;
    margin: 0 auto;
    margin-top: 28px;
    font-size: 16px;
    line-height: 24px;

    &:first-of-type {
      margin-top: 0;
    }
  }

  &__data {
    min-width: 40px;
    color: #262626;
    text-align: right;
  }
}

/* 错误页面container样式 */
.exam-error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fff;
}

@include b(ques-answer) {
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  font-size: 14px;
  line-height: 22px;
  white-space: pre-wrap;

  @include e(style) {
    max-height: 260px;
  }
}

@include b(ques-scrollbar) {
  .yxt-scrollbar__bar {
    opacity: 1;
  }
}

/* 随堂练习的样式 */
.yxtulcdsdk-testdialog .yxtf-dialog__body {
  height: 572px;
  padding: 0 !important;
}

@include b(practice-container) {
  @include m(course) {
    background-color: #fff;

    .yxtulcdsdk-uexam-preview-content {
      padding: 0 0 36px 0;
    }

    .yxtulcd-practice-course {
      min-width: calc(100% - 48px);
      padding: 32px 24px 0;

      .yxtulcdsdk-uexam-preview {
        margin: 0 auto !important;
      }

      .info-text-course {
        display: flex;
        align-items: center;
        height: 52px;
      }

      .info-left,
      .info-right {
        & > div {
          & + div {
            margin-top: 16px !important;
          }
        }
      }
    }

    .yxtulcdsdk-user-exam {
      padding: 24px 24px 0;
    }

    .yxtulcdsdk-uexam-preview {
      min-width: 592px !important;

      .main-top-wrap {
        width: 100% !important;
        min-width: 544px !important;
        margin: auto !important;
      }

      .yxtulcdsdk-uexam-preview-content__explain {
        width: 100% !important;
        min-width: 544px !important;
      }
    }

    .yxtulcdsdk-uexam-preview--practice {
      padding-bottom: 0 !important;
    }

    .yxtulcdsdk-pc-marking-main {
      padding: 0 !important;
    }

    .yxtulcdsdk-examing__btns {
      padding: 12px 0 !important;
    }

    .yxtulcdsdk-pc-marking-top-wrapper {
      height: 518px !important;
    }

    .yxtulcdsdk-examing__result {
      width: 100%;
      margin: 0 !important;
    }

    .pt60 {
      padding-top: 60px;
    }
  }
}

@include b(declare-dialog) {
  min-height: 254px;

  @include e(title) {
    box-sizing: border-box;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    text-align: center;
  }

  @include e(content) {
    min-height: 66px;
  }
}

/******* common/global ********/

/* 行flex */
@include b(row-flex) {
  display: flex;
  align-items: center;

  .yxt-col {
    flex-grow: 0;
    flex-shrink: 0;
    float: none;
  }

  @include e(sub) {
    flex-grow: 0;
    flex-shrink: 0;
  }

  @include e(main) {
    flex-grow: 1;
    flex-shrink: 1;
  }
}

/* 列flex */
@include b(col-flex) {
  display: flex;
  flex-direction: column;

  @include e(sub) {
    flex-grow: 0;
    flex-shrink: 0;
  }

  @include e(main) {
    flex-grow: 1;
    flex-shrink: 1;
  }
}

@include b(ques-reference) {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  @include b(reference-list) {
    width: 50%;
  }
}

.b-radius4-bottom {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}

.pos_t0 {
  top: 0 !important;
}

.pos_t2 {
  top: 2px !important;
}

.pos_t3 {
  top: 3px !important;
}

.pos_t4 {
  top: 4px !important;
}

.pos_t5 {
  top: 5px !important;
}

.pos_t6 {
  top: 6px !important;
}

.pos_t8 {
  top: 8px !important;
}

.pos_t16 {
  top: 16px !important;
}

.pos_b5 {
  bottom: 5px;
}

.pos_l0 {
  left: 0;
}

.pos_b0 {
  bottom: 0;
}

.pos_r4 {
  right: 4px;
}

.pos_r8 {
  right: 8px;
}

.pos_r16 {
  right: 16px;
}

.pos_fl1 {
  left: -1px;
}

.pos_fl5 {
  left: -5px;
}

.pos_fl12 {
  left: -12px;
}

.pos_ft1 {
  left: -1px;
}

.pos_ft2 {
  top: -2px;
}

.pos_ft7 {
  top: -7px;
}

.pos_fr0 {
  right: 0;
}

.pos_fb56 {
  bottom: -56px;
}

.box-size-borderbox {
  box-sizing: border-box !important;
}

.box-size-contentbox {
  box-sizing: content-box !important;
}

.ml0 {
  margin-left: 0 !important;
}

.ml10 {
  margin-left: 10px !important;
}

.ml72 {
  margin-left: 72px;
}

.pt1 {
  padding-top: 1px;
}

.pr68 {
  padding-right: 68px;
}

.pr150 {
  padding-right: 150px;
}

.pb76 {
  padding-bottom: 76px;
}

.pb100 {
  padding-bottom: 100px;
}

.ph24 {
  padding-right: 24px;
  padding-left: 24px;
}

.ph60 {
  padding-right: 60px;
  padding-left: 60px;
}

.mfl12 {
  margin-left: -12px !important;
}

.w-auto {
  width: auto;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.ph15 {
  padding-right: 15px;
  padding-left: 15px;
}

.height60 {
  height: 60px;
}

.height72 {
  height: 72px;
}

.width220 {
  width: 220px;
}

.width200 {
  width: 200px;
}

.minwidth32 {
  min-width: 32px;
}

.mwidth1248 {
  min-width: 1248px;
}

.minwidth850 {
  min-width: 850px;
}

.mheight48 {
  min-height: 48px;
}

.mheightp100 {
  min-height: 100%;
}

.overhidden {
  overflow: hidden;
}

.flex-g-1 {
  flex-grow: 1;
}

.flex-j-stretch {
  justify-content: stretch;
}

.border-radius-4 {
  border-radius: 4px;
}

.max-w600 {
  max-width: 600px;
}

.maxwidth38 {
  max-width: 38px;
}

@import './media.scss';
@import './wrong.scss';
