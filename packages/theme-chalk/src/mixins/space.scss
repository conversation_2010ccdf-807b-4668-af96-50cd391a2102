
@mixin padding($value) {

  .p#{$value} {
    padding: #{$value}px;
  }

  .pl#{$value} {
    padding-left: #{$value}px;
  }

  .pr#{$value} {
    padding-right: #{$value}px;
  }

  .ph#{$value} {
    padding-right: #{$value}px;
    padding-left: #{$value}px;
  }

  .pv#{$value} {
    padding-top: #{$value}px;
    padding-bottom: #{$value}px;
  }
}

@mixin margin($value) {

  .m#{$value} {
    margin: #{$value}px;
  }

  .ml#{$value} {
    margin-left: #{$value}px;
  }

  .mr#{$value} {
    margin-right: #{$value}px;
  }

  .mt#{$value} {
    margin-top: #{$value}px;
  }

  .mb#{$value} {
    margin-bottom: #{$value}px;
  }

  .mh#{$value} {
    margin-right: #{$value}px;
    margin-left: #{$value}px;
  }

  .mv#{$value} {
    margin-top: #{$value}px;
    margin-bottom: #{$value}px;
  }
}

@mixin size($value) {
  .w#{$value} {
    width: #{$value}px;
  }

  .h#{$value} {
    height: #{$value}px;
  }
}

@mixin customSize($value,$suffix) {
  .wp#{$value} {
    width: #{$value}#{$suffix};
  }

  .hp#{$value} {
    height: #{$value}#{$suffix};
  }
}
