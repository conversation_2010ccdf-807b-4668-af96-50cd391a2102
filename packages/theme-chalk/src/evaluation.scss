@import "mixins/mixins";
@import "common/var";

@include b(evaluation) {
  background: #f5f5f5;

  &--radius {
    border-radius: 8px;
  }

  &-wrap {
    box-sizing: border-box;
    min-width: 850px;
    max-width: 1200px;
    min-height: 100%;
    margin: 0 auto;
  }

  .flex-row-between {
    display: flex;
    justify-content: space-between;
  }

  .flex-center,
  .flex-row-center,
  .flex-align-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .gwnl-flex,
  .layout-flex {
    display: flex;
  }

  .justify-content-between {
    justify-content: space-between;
  }

  .pos-relative {
    position: relative;
  }

  .pos-absolute {
    position: absolute;
  }

  .w280 {
    width: 280px;
  }

  .w282 {
    width: 282px;
  }

  .w570 {
    width: 570px;
  }

  .pwidth50 {
    width: 50%;
  }

  .gwnl-eval__container,
  .pwidth100,
  .width100 {
    width: 100%;
  }

  .bg-color-d7f7c1 {
    background-color: #d7f7c1;
  }

  .bg-color-fef2f0 {
    background-color: #fef2f0;
  }

  .bg-color-c7d9ff {
    background-color: #c7d9ff;
  }

  .bg-light {
    background-color: rgb(251 251 253);
  }

  .color-red-fa {
    color: #f5222d;
  }

  .top4 {
    top: 4px;
  }

  .visibility-show {
    visibility: visible;
  }

  .visibility-hide {
    visibility: hidden;
  }
}
