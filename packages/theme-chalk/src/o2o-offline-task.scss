@import "mixins/mixins";
@import "common/var";

@include b(o2o-offline-task) {
    position: relative;
    min-width: 850px;
    max-width: 1200px;
    margin: 0 auto;
    box-sizing: border-box;
    
    .min-width-0 {
      min-width: 0;
    }

    .offline-task-eval {
      width: 100%;
      box-sizing: border-box;
      border-radius: 4px;
      border: 1px solid #E9E9E9;
    }

    .width864 {
        width: 864px;
        box-sizing: border-box;
    }

    .width312 {
        width: 312px;
        box-sizing: border-box;
    }

    .mt-4 {
        margin-top: -4px;
    }

    .border-radius4 {
        border-radius: 4px;
    }


.flip-stu-score {
    width: 100%;
    height: 48px;
    box-sizing: border-box;
  
    &.score-1 {
      color: #ad4e00;
    }
  
    &.score-2 {
      color: #389e0d;
    }
  
    &.score-3 {
      color: #52C41A;
      background-color: #F5FAF6;
    }
  
    &.score-4 {
      color: #F5222D;
      background-color: #FFF8F6;
    }
}
}
