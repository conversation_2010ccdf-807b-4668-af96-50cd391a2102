.yxtulcdsdk-ulcdsdk {
  // flex
  .flex,
  .layout-flex {
    display: flex;
  }

  .flex-align-center,
  .flex-center,
  .flex-mid {
    display: flex;
    align-items: center;
  }

  .flex-justify-center {
    display: flex;
    justify-content: center;
  }

  .flex-wrap,
  .layout-flex-wrap {
    flex-wrap: wrap;
  }

  .flex-dir-ver,
  .layout-flex-vertical {
    flex-direction: column;
  }

  .layout-inline-flex {
    display: inline-flex;
  }

  // 子元素在交叉轴方向对齐方式
  .layout-align-center,
  .align-center, .align-items-center {
    align-items: center;
  }

  .layout-flex-wrap {
    flex-wrap: wrap;
  }

  .layout-flex-nowrap {
    flex-wrap: nowrap;
  }

  .layout-align-start {
    align-items: flex-start;
  }

  .layout-align-end {
    align-items: flex-end;
  }

  // 元素自身在交叉轴方向对齐方式
  .layout-self-center {
    align-self: center;
  }

  .layout-self-start {
    align-self: flex-start;
  }

  .layout-self-end {
    align-self: flex-end;
  }

  .layout-justify-start {
    justify-content: flex-start;
  }

  .layout-justify-center,.justify-center {
    justify-content: center;
  }

  .layout-justify-end {
    justify-content: flex-end;
  }

  // 首尾子元素两边没有空白
  .layout-justify-between, .justify-between {
    justify-content: space-between;
  }

  // 每个项目两侧的间隔相等
  .layout-justify-around {
    justify-content: space-around;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  // flex相关
  .flex-1,
  .col-flex-1 {
    flex: 1;
  }

  .flex {
    display: flex;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .col-flex-2 {
    flex: 2;
  }

  .col-flex-3 {
    flex: 3;
  }

  .col-flex-4 {
    flex: 4;
  }

  .col-flex-5 {
    flex: 5;
  }

  .col-flex-6 {
    flex: 6;
  }

  .flex-morethan-1 {
    flex: 1.38;
  }

  // felx-水平对齐-垂直对齐
  .flex-between-center {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex-start-center {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .o2o-flex-center-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .reactive-flex-1 {
    flex: 1;
    width: 0;
  }

  .flex-width-0 {
    flex: 1;
    width: 0;
  }

  .flex-none {
    flex: none;
  }

  .flex-item-auto-width {
    width: 0%;
    overflow: hidden;
  }
}

.yxtpd-flex {
  display: flex;
}

.yxtpd-flex-align-center {
  display: flex;
  align-items: center;
}

.yxtpd-flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.yxtpd-flex-1 {
  flex: 1;
}

.yxtpd-flex-stretch {
  align-items: stretch;
}

.yxtpd-flex-justify-end {
  display: flex;
  justify-content: flex-end;
}

.yxtpd-flex-dir-ver {
  flex-direction: column;
}
