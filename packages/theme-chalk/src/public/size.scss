@import "./mixins/space.scss";
@import "./impt.scss";

@for $i from 0 through 10 {
  @include padding(1 * $i);
  @include margin(1 * $i);
}
// padding
@include padding(16);
@include padding(20);
@include padding(22);
@include padding(24);
@include padding(48);
@include padding(72);

//margin
@include margin(0);
@include margin(2);
@include margin(5);
@include margin(6);
@include margin(10);
@include margin(15);
@include margin(22);
@include margin(24);
@include margin(26);
@include margin(8);
@include margin(32);
@include margin(168);
@include margin(50);

//size
@include size(8);
@include size(38);
@include size(120);
@include size(144);
@include size(200);
@include size(240);
@include customSize(100,'%');
@include customSize(50,'%');

.yxtulcdsdk-ulcdsdk {
  // font-size相关
  .font-size-0 {
    font-size: 0;
  }

  .font-size-12 {
    font-size: 12px;
  }

  .font-size-14 {
    font-size: 14px;
  }

  .font-size-16 {
    font-size: 16px;
  }

  .font-size-18 {
    font-size: 18px;
  }

  .font-size-20 {
    font-size: 20px;
  }

  .font-size-24 {
    font-size: 24px;
  }

  .font-size-26 {
    font-size: 26px;
  }

  .font-size-28 {
    font-size: 28px;
  }

  .font-size-30 {
    font-size: 30px;
  }

  .font-size-32 {
    font-size: 32px;
  }

  // 宽度-相关
  .lt24 {
    left: 24px;
  }

  .w-2\/4 {
    width: 50%;
  }

  .maxwp100 {
    max-width: 100%;
  }

  .maxwp50 {
    max-width: 50%;
  }

  .maxwp49 {
    max-width: 49%;
  }

  .maxmp60 {
    max-width: 60%;
  }

  .maxw60 {
    max-width: 60px;
  }

  .min68 {
    min-width: 68px;
  }

  .maxw84 {
    max-width: 84px;
  }

  .maxw82 {
    max-width: 82px;
  }

  .maxwp33 {
    max-width: 33%;
  }

  .maxw94 {
    max-width: 94px;
  }

  .maxw160 {
    max-width: 160px;
  }

  .max-w-100 {
    max-width: 100px;
  }

  .max-w-220 {
    max-width: 220px;
  }

  .max-w-256 {
    max-width: 256px;
  }

  .max-w-560 {
    max-width: 560px;
  }

  .max-232 {
    max-width: 232px;
  }

  .max-240 {
    max-width: 240px;
  }

  .max-280 {
    max-width: 280px;
  }

  .minw300 {
    min-width: 300px;
  }

  .max-400 {
    max-width: 400px;
  }

  .max-536 {
    max-width: 536px;
  }

  .minw600 {
    min-width: 600px;
  }

  .minw1200 {
    min-width: 1200px;
  }

  .minW120 {
    min-width: 120px;
  }

  .minW70 {
    min-width: 70px;
  }

  .wlimit {
    min-width: 1280px;
  }

  .wlimit_card {
    min-width: 1096px;
  }

  .maxw120 {
    max-width: 120px;
  }

  .maxw260 {
    max-width: 260px;
  }

  .maxw350 {
    // 一般用于 popover
    max-width: 350px;
  }

  .maxw100-40 {
    // 一般用于 popover
    max-width: calc(100% - 40px);
  }

  .maxw1320 {
    max-width: 1320px;
  }

  .max-h-150 {
    max-height: 150px;
  }

  .max-h-168 {
    max-height: 168px;
  }

  .max-h-224 {
    max-height: 224px;
  }

  .maxhnone {
    max-height: none;
  }

  .maxh445 {
    max-height: 445px;
  }

  .maxh622 {
    max-height: 622px;
  }

  .maxh720 {
    max-height: 720px;
  }

  .maxh200 {
    max-height: 200px;
  }

  .w0 {
    width: 0;
  }

  .w1 {
    width: 1px;
  }

  .w2 {
    width: 2px;
  }

  .w3 {
    width: 3px;
  }

  .w4 {
    width: 4px;
  }

  .w6 {
    width: 6px;
  }

  .w8 {
    width: 8px;
  }

  .w12 {
    width: 12px;
  }

  .w14 {
    width: 14px;
  }

  .w16 {
    width: 16px;
  }

  .w18 {
    width: 18px;
  }

  .w20 {
    width: 20px;
  }

  .w24 {
    width: 24px;
  }

  .w32 {
    width: 32px;
  }

  .w36 {
    width: 36px;
  }

  .w40 {
    width: 40px;
  }

  .w46 {
    width: 46px;
  }

  .w48 {
    width: 48px;
  }

  .w56 {
    width: 56px;
  }

  .w58 {
    width: 58px;
  }

  .w60 {
    width: 60px;
  }

  .w62 {
    width: 62px;
  }

  .w72 {
    width: 72px;
  }

  .w80 {
    width: 80px;
  }

  .w82 {
    width: 82px;
  }

  .w92 {
    width: 92px;
  }

  .w100 {
    width: 100px;
  }

  .w104 {
    width: 104px;
  }

  .w106 {
    width: 106px;
  }

  .w110 {
    width: 110px;
  }

  .w120 {
    width: 120px;
  }

  .w126 {
    width: 126px;
  }

  .w136 {
    width: 136px;
  }

  .w144 {
    width: 144px;
  }

  .w150 {
    width: 150px;
  }

  .w152 {
    width: 152px;
  }

  .w160 {
    width: 160px;
  }

  .w168 {
    width: 168px;
  }

  .w178 {
    width: 178px;
  }

  .w180 {
    width: 180px;
  }

  .w185 {
    width: 185px;
  }

  .w188 {
    width: 188px;
  }

  .w170 {
    width: 170px;
  }

  .w196 {
    width: 196px;
  }

  .w200 {
    width: 200px;
  }

  .w206 {
    width: 206px;
  }

  .w220 {
    width: 220px;
  }

  .w240 {
    width: 240px;
  }

  .w260 {
    width: 260px;
  }

  .w270 {
    width: 270px;
  }

  .w278 {
    width: 278px;
  }

  .w250 {
    width: 250px;
  }

  .w256 {
    width: 256px;
  }

  .w264 {
    width: 264px;
  }

  .w284 {
    width: 284px;
  }

  .w286 {
    width: 286px;
  }

  .w300 {
    width: 300px;
  }

  .w308 {
    width: 308px;
  }

  .w314 {
    width: 314px;
  }

  .w320 {
    width: 320px;
  }

  .w328 {
    width: 328px;
  }

  .w340 {
    width: 340px;
  }

  .w346 {
    width: 346px;
  }

  .w352 {
    width: 352px;
  }

  .w355 {
    width: 355px;
  }

  .w362 {
    width: 362px;
  }

  .w375 {
    width: 375px;
  }

  .w380 {
    width: 380px;
  }

  .w396 {
    width: 396px;
  }

  .w415 {
    width: 415px;
  }

  .w428 {
    width: 428px;
  }

  .w488 {
    width: 488px;
  }

  .w500 {
    width: 500px;
  }

  .w544 {
    width: 544px;
  }

  .w600 {
    width: 600px;
  }

  .w628 {
    width: 628px;
  }

  .w692 {
    width: 692px;
  }

  .w760 {
    width: 760px;
  }

  .w800 {
    width: 800px;
  }

  .w880 {
    width: 880px;
  }

  .w736 {
    width: 736px;
  }

  .w1000 {
    width: 1000px;
  }

  .w1200 {
    width: 1200px;
  }

  .w33\% {
    width: 33.333%;
  }

  .wauto {
    width: auto;
  }

  .wp-25 {
    width: 25%;
  }

  .wp-70 {
    width: 70%;
  }

  .wp-90 {
    width: 90%;
  }

  .wp-95 {
    width: 95%;
  }

  .wp-100,
  .wline {
    width: 100%;
  }

  .wp-460 {
    width: 460px;
  }

  .wp-96 {
    max-width: 96%;
  }

  .max-width-300 {
    max-width: 300px;
  }

  // height-相关
  .h-50vh {
    height: 50vh;
  }

  .h-80vh {
    height: 80vh;
  }

  .h0 {
    height: 0;
  }

  .h4 {
    height: 4px;
  }

  .h6 {
    height: 6px;
  }

  .h8 {
    height: 8px;
  }

  .h12 {
    height: 12px;
  }

  .h14 {
    height: 14px;
  }

  .h18 {
    height: 18px;
  }

  .h20 {
    height: 20px;
  }

  .h22 {
    height: 22px;
  }

  .h24 {
    height: 24px;
  }

  .h26 {
    height: 26px;
  }

  .h28 {
    height: 28px;
  }

  .h32 {
    height: 32px;
  }

  .h36 {
    height: 36px;
  }

  .h38 {
    height: 38px;
  }

  .h40 {
    height: 40px;
  }

  .h42 {
    height: 42px;
  }

  .h44 {
    height: 44px;
  }

  .h45 {
    height: 45px;
  }

  .h48 {
    height: 48px;
  }

  .h50 {
    height: 50px;
  }

  .h56 {
    height: 56px;
  }

  .h58 {
    height: 58px;
  }

  .h64 {
    height: 64px;
  }

  .h65 {
    height: 65px;
  }

  .h72 {
    height: 72px;
  }

  .h74 {
    height: 74px;
  }

  .h75 {
    height: 75px;
  }

  .h76 {
    height: 76px;
  }

  .hline {
    height: 100%;
  }

  .h90 {
    height: 90px;
  }

  .h94 {
    height: 94px;
  }

  .h96 {
    height: 96px;
  }

  .h100 {
    height: 100px;
  }

  .h106 {
    height: 106px;
  }

  .h116 {
    height: 116px;
  }

  .h120 {
    height: 120px;
  }

  .h150 {
    height: 150px;
  }

  .h164 {
    height: 164px;
  }

  .h168 {
    height: 168px;
  }

  .h180 {
    height: 180px;
  }

  .h198 {
    height: 198px;
  }

  .h200 {
    height: 200px;
  }

  .h214 {
    height: 214px;
  }

  .h204 {
    height: 204px;
  }

  .h260 {
    height: 260px;
  }

  .h300 {
    height: 300px;
  }

  .h340 {
    height: 340px;
  }

  .h348 {
    height: 348px;
  }

  .h388 {
    height: 388px;
  }

  .h400 {
    height: 400px;
  }

  .h450 {
    height: 450px;
  }

  .h720 {
    height: 720px;
  }

  .hauto {
    height: auto;
  }

  .minh-72 {
    min-height: 72px;
  }

  .minh100 {
    min-height: 100%;
  }

  .min-h-104 {
    min-height: 104px;
  }

  .min-h-200 {
    min-height: 200px;
  }

  .min-h-400 {
    min-height: 400px;
  }

  .min-h-480 {
    min-height: 480px;
  }

  // line-height相关
  .lh0 {
    line-height: 0;
  }

  .lh-1 {
    line-height: 1;
  }

  .lh14 {
    line-height: 14px;
  }

  .lh18 {
    line-height: 18px;
  }

  .lh19 {
    line-height: 19px;
  }

  .lh20 {
    line-height: 20px;
  }

  .lh22 {
    line-height: 22px;
  }

  .lh24 {
    line-height: 24px;
  }

  .lh26 {
    line-height: 26px;
  }

  .lh28 {
    line-height: 28px;
  }

  .lh29 {
    line-height: 29px;
  }

  .lh30 {
    line-height: 30px;
  }

  .lh32 {
    line-height: 32px;
  }

  .lh34 {
    line-height: 34px;
  }

  .lh36 {
    line-height: 36px;
  }

  .lh38 {
    line-height: 38px;
  }

  .lh40 {
    line-height: 40px;
  }

  .lh44 {
    line-height: 44px;
  }

  .lh45 {
    line-height: 45px;
  }

  .lh46 {
    line-height: 46px;
  }

  .lh48 {
    line-height: 48px;
  }

  .lh50 {
    line-height: 50px;
  }

  .lh55 {
    line-height: 55px;
  }

  .lh56 {
    line-height: 56px;
  }

  .lh75 {
    line-height: 75px;
  }

  .lh80 {
    line-height: 80px;
  }

  .lh98 {
    line-height: 98px;
  }

  // margin-相关
  .m-c {
    margin: 0 auto;
  }

  .m2 {
    margin: 2px;
  }

  .m24 {
    margin: 24px;
  }

  .m32 {
    margin: 32px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .mb14 {
    margin-bottom: 14px;
  }

  // margin-top
  .mt-4 {
    margin-top: -4px;
  }

  .mt-6 {
    margin-top: -6px;
  }

  .mt-8 {
    margin-top: -8px;
  }

  .mt-12 {
    margin-top: -12px;
  }

  .mt-16 {
    margin-top: -16px;
  }

  .mt-24 {
    margin-top: -24px;
  }

  .mt0 {
    margin-top: 0;
  }

  .mt-28 {
    margin-top: -28px;
  }

  .mt-56 {
    margin-top: -56px;
  }

  .mt1 { // 图标和文字偏移 1px
    margin-top: 1px;
  }

  .mt2 {
    margin-top: 2px;
  }

  .mt3 {
    margin-top: 3px;
  }

  .mt4 {
    margin-top: 4px;
  }

  .mt6 {
    margin-top: 6px;
  }

  .mt7 {
    margin-top: 7px;
  }

  .mt8 {
    margin-top: 8px;
  }

  .mt9 {
    margin-top: 9px;
  }

  .mt14 {
    margin-top: 14px;
  }

  .mv12 {
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .mt16,
  .mv16 {
    margin-top: 16px;
  }

  .mt17 {
    margin-top: 17px;
  }

  .mt18 {
    margin-top: 18px;
  }

  .mt20 {
    margin-top: 20px;
  }

  .mt22 {
    margin-top: 22px;
  }

  .mt24 {
    margin-top: 24px;
  }

  .mt26 {
    margin-top: 26px;
  }

  .mt28 {
    margin-top: 28px;
  }

  .mt30 {
    margin-top: 30px;
  }

  .mt32 {
    margin-top: 32px;
  }

  .mt34 {
    margin-top: 34px;
  }

  .mt35 {
    margin-top: 35px;
  }

  .mt36 {
    margin-top: 36px;
  }

  .mt40 {
    margin-top: 40px;
  }

  .mt42 {
    margin-top: 42px;
  }

  .mt46 {
    margin-top: 46px;
  }

  .mt48 {
    margin-top: 48px;
  }

  .mt52 {
    margin-top: 52px;
  }

  .mt54 {
    margin-top: 54px;
  }

  .mt56 {
    margin-top: 56px;
  }

  .mt64 {
    margin-top: 64px;
  }

  .mt80 {
    margin-top: 80px;
  }

  .mt90 {
    margin-top: 90px;
  }

  .mt94 {
    margin-top: 94px;
  }

  .mt96 {
    margin-top: 96px;
  }

  .mt100 {
    margin-top: 100px;
  }

  .mt120 {
    margin-top: 120px;
  }

  .mt130 {
    margin-top: 130px;
  }

  .mv130 {
    margin: 130px 0;
  }

  .mt15-vh {
    margin-top: 15vh;
  }

  .mv94 {
    margin: 94px 0;
  }

  .mt230 {
    margin-top: 230px;
  }

  .mv64 {
    margin-top: 64px;
    margin-bottom: 64px;
  }

  .mv96 {
    margin-top: 96px;
    margin-bottom: 96px;
  }

  .mv132 {
    margin-top: 132px;
    margin-bottom: 132px;
  }
  // margin-right相关
  .mr-8 {
    margin-right: -8px;
  }

  .mr-40 {
    margin-right: -40px;
  }

  .mr6 {
    margin-right: 6px;
  }

  .mr8 {
    margin-right: 8px;
  }

  .mr12,
  .mh12 {
    margin-right: 12px;
  }

  .mr14 {
    margin-right: 14px;
  }

  .mh16,
  .mr16 {
    margin-right: 16px;
  }

  .mh18,
  .mr18 {
    margin-right: 18px;
  }

  .mr20 {
    margin-right: 20px;
  }

  .mh18 {
    margin-right: 18px;
    margin-left: 18px;
  }

  .mr24,
  .mh24 {
    margin-right: 24px;
  }

  .mh48 {
    margin: 0 48px;
  }

  .mr28 {
    margin-right: 28px;
  }

  .mr30 {
    margin-right: 30px;
  }

  .mr32 {
    margin-right: 32px;
  }

  .mr36 {
    margin-right: 36px;
  }

  .mr40 {
    margin-right: 40px;
  }

  .mr44 {
    margin-right: 44px;
  }

  .mr48 {
    margin-right: 48px;
  }

  .mr52 {
    margin-right: 52px;
  }

  .mr56 {
    margin-right: 56px;
  }

  .mr60 {
    margin-right: 60px;
  }

  .mr64 {
    margin-right: 64px;
  }

  .mr70 {
    margin-right: 70px;
  }

  .mr80 {
    margin-right: 80px;
  }

  .mr84 {
    margin-right: 84px;
  }

  .mr100 {
    margin-right: 100px;
  }

  .mr112 {
    margin-right: 112px;
  }

  .mr120 {
    margin-right: 120px;
  }

  .mr128 {
    margin-right: 128px;
  }

  .mr160 {
    margin-right: 160px;
  }

  .mr320 {
    margin-right: 320px;
  }

  .mr-16 {
    margin-right: -16px;
  }

  .mr-24 {
    margin-right: -24px;
  }

  .mr-32 {
    margin-right: -32px;
  }

  // margin-left
  .ml-2 {
    margin-left: -2px;
  }

  .ml-4 {
    margin-left: -4px;
  }

  .ml-6 {
    margin-left: -6px;
  }

  .ml-8 {
    margin-left: -8px;
  }

  .ml-12 {
    margin-left: -12px;
  }

  .ml-16 {
    margin-left: -16px;
  }

  .ml-20 {
    margin-left: -20px;
  }

  .ml-22 {
    margin-left: -22px;
  }

  .ml-24 {
    margin-left: -24px;
  }

  .ml-32 {
    margin-left: -32px;
  }

  .ml-75 {
    margin-left: -75px;
  }

  .ml2 {
    margin-left: 2px;
  }

  .ml4 {
    margin-left: 4px;
  }

  .ml5 {
    margin-left: 5px;
  }

  .ml6,
  .mh6 {
    margin-left: 6px;
  }

  .ml8 {
    margin-left: 8px;
  }

  .ml12,
  .mh12 {
    margin-left: 12px;
  }

  .ml100 {
    margin-left: 100px;
  }

  .ml14 {
    margin-left: 14px;
  }

  .mh16,
  .ml16 {
    margin-left: 16px;
  }

  .ml16-impt {
    margin-left: 16px;
  }

  .ml18 {
    margin-left: 18px;
  }

  .ml20 {
    margin-left: 20px;
  }

  .ml22 {
    margin-left: 22px;
  }

  .ml24,
  .mh24 {
    margin-left: 24px;
  }

  .ml26 {
    margin-left: 26px;
  }

  .ml28 {
    margin-left: 28px;
  }

  .ml30 {
    margin-left: 30px;
  }

  .ml32 {
    margin-left: 32px;
  }

  .ml42 {
    margin-left: 42px;
  }

  .ml44 {
    margin-left: 44px;
  }

  .ml48 {
    margin-left: 48px;
  }

  .ml52 {
    margin-left: 52px;
  }

  .ml56 {
    margin-left: 56px;
  }

  .ml60 {
    margin-left: 60px;
  }

  .ml64 {
    margin-left: 64px;
  }

  .ml70 {
    margin-left: 70px;
  }

  .ml76 {
    margin-left: 76px;
  }

  .ml80 {
    margin-left: 80px;
  }

  .ml82 {
    margin-left: 82px;
  }

  .ml84 {
    margin-left: 84px;
  }

  .ml86 {
    margin-left: 86px;
  }

  .ml106 {
    margin-left: 106px;
  }

  .mr106 {
    margin-right: 106px;
  }

  .ml132 {
    margin-left: 132px;
  }

  .ml145 {
    margin-left: 145px;
  }

  .ml156 {
    margin-left: 156px;
  }

  .ml160 {
    margin-left: 160px;
  }

  .ml175 {
    margin-left: 175px;
  }

  // margin-bottom
  .mb-8 {
    margin-bottom: -8px;
  }

  .mb-24 {
    margin-bottom: -24px;
  }

  .mb2 {
    margin-bottom: 2px;
  }

  .mb4 {
    margin-bottom: 4px;
  }

  .mb6 {
    margin-bottom: 6px;
  }

  .mb8 {
    margin-bottom: 8px;
  }

  .mb16,
  .mv16 {
    margin-bottom: 16px;
  }

  .mb18 {
    margin-bottom: 18px;
  }

  .mb20 {
    margin-bottom: 20px;
  }

  .mb22 {
    margin-bottom: 22px;
  }

  .mb24 {
    margin-bottom: 24px;
  }

  .mb26 {
    margin-bottom: 26px;
  }

  .mb28 {
    margin-bottom: 28px;
  }

  .mb30 {
    margin-bottom: 30px;
  }

  .mb32 {
    margin-bottom: 32px;
  }

  .mb36 {
    margin-bottom: 36px;
  }

  .mb44 {
    margin-bottom: 44px;
  }

  .mb56 {
    margin-bottom: 56px;
  }

  .mb72 {
    margin-bottom: 72px;
  }

  .mb70 {
    margin-bottom: 70px;
  }

  .mb80 {
    margin-bottom: 80px;
  }

  .mb100 {
    margin-bottom: 80px;
  }

  .mb96 {
    margin-bottom: 96px;
  }

  .mb106 {
    margin-bottom: 106px;
  }

  .mb120 {
    margin-bottom: 120px;
  }

  .mb240 {
    margin-bottom: 240px;
  }

  // padding-相关
  .p16 {
    padding: 16px;
  }

  .pv13 {
    padding-top: 13px;
    padding-bottom: 13px;
  }

  .p0 {
    padding: 0;
  }

  .p2 {
    padding: 2px;
  }

  .p4 {
    padding: 4px;
  }

  .p8 {
    padding: 8px;
  }

  .p10 {
    padding: 10px;
  }

  .p24 {
    padding: 24px;
  }

  .p32 {
    padding: 32px;
  }

  .pl2 {
    padding-left: 2px;
  }

  .pl14 {
    padding-left: 14px;
  }

  .pl18 {
    padding-left: 18px;
  }

  .ph8,
  .pl8 {
    padding-left: 8px;
  }

  .pl22 {
    padding-left: 22px;
  }

  .pl24 {
    padding-left: 24px;
  }

  .pl26 {
    padding-left: 26px;
  }

  .pl28 {
    padding-left: 28px;
  }

  .pl36 {
    padding-left: 36px;
  }

  .pl70 {
    padding-left: 70px;
  }

  .pl80 {
    padding-left: 80px;
  }

  .pl100 {
    padding-left: 100px;
  }

  .ph8,
  .pr8 {
    padding-right: 8px;
  }

  .pr56 {
    padding-right: 56px;
  }

  .pr22 {
    padding-right: 22px;
  }

  .pr88 {
    padding-right: 88px;
  }

  .pr200 {
    padding-right: 200px;
  }

  .pr100 {
    padding-right: 100px;
  }

  .pr140 {
    padding-right: 140px;
  }

  .pt0 {
    padding-top: 0;
  }

  .pt2 {
    padding-top: 2px;
  }

  .pt4 {
    padding-top: 4px;
  }

  .pt6 {
    padding-top: 6px;
  }

  .pt8 {
    padding-top: 8px;
  }

  .pt17 {
    padding-top: 17px;
  }

  .pv1 {
    padding-top: 1px;
    padding-bottom: 1px;
  }

  .pv18 {
    padding-top: 18px;
  }

  .pr36 {
    padding-right: 36px;
  }

  .pt10,
  .pv10 {
    padding-top: 10px;
  }

  .pv12,
  .pt12 {
    padding-top: 12px;
  }

  .pt16 {
    padding-top: 16px;
  }

  .pt18 {
    padding-top: 18px;
  }

  .pt20 {
    padding-top: 20px;
  }

  .pt22 {
    padding-top: 22px;
  }

  .pt30 {
    padding-top: 30px;
  }

  .pt40 {
    padding-top: 40px;
  }

  .pt42 {
    padding-top: 42px;
  }

  .pt48 {
    padding-top: 48px;
  }

  .pt50 {
    padding-top: 50px;
  }

  .pt52 {
    padding-top: 52px;
  }

  .pt56 {
    padding-top: 56px;
  }

  .pt90 {
    padding-top: 90px;
  }

  .pt100 {
    padding-top: 100px;
  }

  .pt120 {
    padding-top: 120px;
  }

  .pt130 {
    padding-top: 130px;
  }

  .pt220 {
    padding-top: 220px;
  }

  .pl0 {
    padding-left: 0;
  }

  .pl4 {
    padding-left: 4px;
  }

  .ph6,
  .pl6 {
    padding-left: 6px;
  }

  .pl10 {
    padding-left: 10px;
  }

  .ph12,
  .pl12 {
    padding-left: 12px;
  }

  .pl15 {
    padding-left: 15px;
  }

  .ph16,
  .pl16 {
    padding-left: 16px;
  }

  .pl20 {
    padding-left: 20px;
  }

  .pl32 {
    padding-left: 32px;
  }

  .pl35 {
    padding-left: 35px;
  }

  .pl44 {
    padding-left: 44px;
  }

  .pl76 {
    padding-left: 76px;
  }

  .pl94 {
    padding-left: 94px;
  }

  .pv128 {
    padding: 128px 0;
  }

  .pl200 {
    padding-left: 200px;
  }

  .pr0 {
    padding-right: 0;
  }

  .pr4 {
    padding-right: 4px;
  }

  .ph6,
  .pr6 {
    padding-right: 6px;
  }

  .pr10 {
    padding-right: 10px;
  }

  .ph12,
  .pr12 {
    padding-right: 12px;
  }

  .ph16,
  .pr16 {
    padding-right: 16px;
  }

  .pr20 {
    padding-right: 20px;
  }

  .pr24 {
    padding-right: 24px;
  }

  .pr26 {
    padding-right: 26px;
  }

  .pr28 {
    padding-right: 26px;
  }

  .pr32 {
    padding-right: 32px;
  }

  .pr35 {
    padding-right: 35px;
  }

  .pr40 {
    padding-right: 40px;
  }

  .pr48 {
    padding-right: 48px;
  }

  .pr142 {
    padding-right: 142px;
  }

  .pr176 {
    padding-right: 176px;
  }

  .pb4 {
    padding-bottom: 4px;
  }

  .pb6 {
    padding-bottom: 6px;
  }

  .pb8 {
    padding-bottom: 8px;
  }

  .pb10,
  .pv10 {
    padding-bottom: 10px;
  }

  .pv12,
  .pb12 {
    padding-bottom: 12px;
  }

  .pt14 {
    padding-top: 14px;
  }

  .pb14 {
    padding-bottom: 14px;
  }

  .pv18,
  .pb18 {
    padding-bottom: 18px;
  }

  .pb20 {
    padding-bottom: 20px;
  }

  .pb22 {
    padding-bottom: 22px;
  }

  .pb24 {
    padding-bottom: 24px;
  }

  .pb26 {
    padding-bottom: 26px;
  }

  .pb28 {
    padding-bottom: 28px;
  }

  .pb30 {
    padding-bottom: 30px;
  }

  .pb32 {
    padding-bottom: 32px;
  }

  .pb36 {
    padding-bottom: 36px;
  }

  .pb40 {
    padding-bottom: 40px;
  }

  .pb50 {
    padding-bottom: 50px;
  }

  .pb52 {
    padding-bottom: 52px;
  }

  .pb56 {
    padding-bottom: 56px;
  }

  .pb58 {
    padding-bottom: 58px;
  }

  .pb72 {
    padding-bottom: 72px;
  }

  .pb84 {
    padding-bottom: 84px;
  }

  .pb94 {
    padding-bottom: 94px;
  }

  // dialog 空数据底部 padding
  .pb180 {
    padding-bottom: 180px;
  }

  .pv2 {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .pv4 {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .pv6 {
    padding-top: 6px;
    padding-bottom: 6px;
  }

  .pv14 {
    padding-top: 14px;
    padding-bottom: 14px;
  }

  .pv16 {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .pv20 {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .pv32 {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .ph10 {
    padding-right: 10px;
    padding-left: 10px;
  }

  .ph20 {
    padding-right: 20px;
    padding-left: 20px;
  }

  .ph26 {
    padding-right: 26px;
    padding-left: 26px;
  }

  .ph30 {
    padding-right: 30px;
    padding-left: 30px;
  }

  .ph32 {
    padding-right: 32px;
    padding-left: 32px;
  }

  .ph40 {
    padding-right: 40px;
    padding-left: 40px;
  }

  .ph44 {
    padding-right: 44px;
    padding-left: 44px;
  }

  .ph68 {
    padding-right: 68px;
    padding-left: 68px;
  }

  .ph94 {
    padding-right: 94px;
    padding-left: 94px;
  }

  .ph200 {
    padding-right: 200px;
    padding-left: 200px;
  }

  .mv20 {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .mh32 {
    margin-right: 32px;
    margin-left: 32px;
  }

  // z-index相关
  .z-index-20 {
    z-index: 20;
  }

  .z-index-30 {
    z-index: 30;
  }

  .z-999 {
    z-index: 999;
  }

  .right-20 {
    right: -20px;
  }

  .top0 {
    top: 0;
  }

  .bottom0 {
    bottom: 0;
  }

  .left0 {
    left: 0;
  }

  .right0 {
    right: 0;
  }

  .pw15-pr {
    padding-right: 15%;
    padding-left: 15%;
  }
}
