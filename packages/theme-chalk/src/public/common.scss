.yxtulcdsdk-ulcdsdk {
  .pa-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .pa-center-v,
  .pa-center-l-v,
  .pa-center-r-v {
    top: 50%;
    transform: translate(0, -50%);
  }

  .pa-center-l-v,
  .left {
    left: 0;
  }

  .pa-center-r-v {
    right: 0;
  }

  .o2oplayframe-task-container {
    box-sizing: border-box;
    min-width: 850px;
    max-width: 1200px;
    min-height: 100%;
    margin: auto;
    background: #fff;
    border-radius: 4px;
  }

  .min-width0 {
    min-width: 0;
  }

  .font-weight-500 {
    font-weight: 500;
  }
}
