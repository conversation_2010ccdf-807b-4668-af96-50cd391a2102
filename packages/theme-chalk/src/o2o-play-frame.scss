@import "mixins/mixins";
@import "common/var";

@include b(o2o-play-frame) {
  // display: flex;
  min-width: 1200px;
  height: 100vh;
  min-height: 700px;
  background-color: #fafafa;

  .yxtf-dialog {
    max-width: 800px;
  }

  .o2oPlayFrame-left {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1000;
    width: 319px;
    min-width: 319px;
    height: 100%;
    background-color: #fafafa;
    border-right: 1px solid #e9e9e9;

    &__box-shadow {
      box-shadow: 3px 2px 8px 0 rgba(0, 0, 0, 0.1);
    }

    .left-title {
      padding: 21px 16px 20px 16px;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }

    .score-content {
      margin: 0 16px;
      border-bottom: 1px solid #e9e9e9;
    }

    .period-title {
      color: #262626;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
    }

    .group-title {
      color: #595959;
      font-size: 14px;
      line-height: 20px;
    }

    .task-content {
      :hover {
        background: #f5f5f5;
        border-radius: 4px;
      }
    }

    .task-content-active {
      background: #f0f0f0;
      border-radius: 4px;
    }

    .task-content-color-active {
      color: var(--color-primary);
    }

    .yxtf-progress-bar__outer {
      background-color: #d9d9d9;
    }

    .task-content-tag {
      max-width: 72px;
    }
  }

  .o2oPlayFrame-rightwidth {
    padding-left: 320px;
  }

  .o2oPlayFrame-right {
    flex-grow: 1;
    background-color: #fafafa;
    transition: .5s ease-in-out;

    &__wrap {
      height: calc(100% - 64px);
    }

    .title {
      display: flex;
      align-items: center;
      height: 63px;
      // padding: 0 16px;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
    }

    .main {
      display: block;
      -ms-flex: 1;
      flex: 1;
      flex-basis: auto;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: #f5f5f5;
      -ms-flex-preferred-size: auto;
    }

    .h100p {
      height: 100%;
    }

    .p24 {
      padding: 24px;
    }

    .headerborder {
      border-bottom: 1px solid #e9e9e9;
    }
  }

  .o2oPlayFrame-collapse-ico {
    &:hover {
      background: #f5f5f5;
      border-radius: 4px;
    }
  }

  .o2oPlayFrame-divider-vertical {
    width: 1px;
    height: 64px;
    background: #e9e9e9;
  }

  .o2oPlayFrame-collapse-rotate180 {
    transform: rotate(180deg);
  }

  .o2oPlayFrame-lockbutton {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 32px;
    padding: 0 20px 0 12px;
    color: #595959;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    outline: 0;
    cursor: pointer;
    -webkit-transition: .1s;
    transition: .1s;

    &:hover {
      background: #f0f0f0;
    }
  }

  .o2oPlayFrame-repeat-study {
    min-height: 380px;

    .yxtf-divider--horizontal {
      margin: 16px 0;
    }

    .dot {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 7px;
      vertical-align: middle;
      border-radius: 50%;
    }

    .dot0,
    .dot1 {
      background-color: #d9d9d9;
    }

    .dot2,
    .dot3 {
      background-color: #52c41a;
    }

    .reviewdot0 {
      background-color: #d9d9d9;
    }

    .reviewdot1 {
      background-color: #fa8c16;
    }

    .reviewdot2 {
      background-color: #52c41a;
    }

    .reviewdot3 {
      background-color: #f5222d;
    }

    .dot-complete {
      background: #52c41a;
    }

    .dot-uncomplete {
      background: #d9d9d9;
    }

    .dot-delay {
      background: #fa8c16;
    }
  }

  .ulcdsdk-coursetitle {
    &__active {
      color: var(--color-primary);
    }

    &__8c {
      color: #8c8c8c;
    }

    &:hover > span {
      color: var(--color-primary) !important;
    }
  }

  .o2oPlayFrame-task-svg {
    transform: rotate(-90deg);
  }
}

.ulcd_o2ospantimewarning {
  color: #fa8c16;
}
