@import "mixins/mixins";
@import "common/var";

@mixin info-icon {
  display: inline-block;
  width: 53px;
  height: 53px;
  margin: 19px 12px 18px 0;
  line-height: 60px;
  text-align: center;
  background-color: #fff;
  border-radius: 50%;
}

@include b(live-task) {
  @include b(live-task-detail) {
    width: 100%;
    height: 100%;

    @include e(info) {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      padding: 32px 90px 44px 32px;
      overflow: hidden;
      background-color: white;

      @include e(info-left) {
        position: absolute;
        top: 32px;
        left: 32px;
        height: 255px;

        @include e(info-image) {
          width: 452px;
          height: 255px;
        }
      }

      @include e(info-right) {
        position: relative;
        float: right;
        width: calc(100% - 484px);
        min-height: 254px;

        @include e(info-title) {
          display: -webkit-box;
          max-width: 714px;
          max-height: 64px;
          overflow: hidden;
          color: #262626;
          font-size: 24px;
          line-height: 32px;
          word-break: break-all;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        @include e(info-date) {
          margin: 21px 0 16px 0;
          color: #262626;
          font-size: 18px;
          line-height: 22px;

          @include e(info-duration) {
            margin-left: 42px;
            color: #595959;
            font-size: 14px;
          }
        }

        @include e(info-location) {
          max-width: 400px;
          height: 20px;
          overflow: hidden;
          color: #888;
          font-weight: 400;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 20px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        @include e(info-runtime) {
          box-sizing: border-box;
          height: 90px;
          padding: 0 24px;
          background-color: #fafafa;
          border-radius: 2px;

          &::after {
            display: table;
            clear: both;
            content: "";
          }

          @include e(noStart) {
            height: 90px;

            @include e(count-down) {
              color: #262626;
              font-size: 16px;
              line-height: 90px;

              span {
                display: inline-block;
              }

              @include e(count-txt) {
                &:last-child {
                  margin-left: 18px;
                }

                &:first-child {
                  margin-right: 14px;
                }
              }

              @include e(count-number) {
                min-width: 60px;
                height: 64px;
                margin-right: 8px;
                padding: 0 14px;
                font-weight: 500;
                font-size: 30px;
                line-height: 64px;
                text-align: center;
                vertical-align: middle;
                background: #fff;
                border-radius: 12px;
              }

              @include e(count-unit) {
                margin: 0 14px;
                color: #8c8c8c;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
              }
            }
          }

          @include e(living) {
            height: 90px;

            @include e(living-icon) {
              @include info-icon;

              vertical-align: middle;
            }

            @include e(living-msg) {
              display: inline-block;
              box-sizing: border-box;
              padding: 18px 0;
              vertical-align: middle;

              @include e(living-desc) {
                margin-bottom: 8px;
                color: #ff730d;
              }

              @include e(living-data) {
                color: #8c8c8c;
              }
            }
          }

          @include e(ended) {
            height: 90px;

            @include e(ended-icon) {
              @include info-icon;
            }

            @include e(ended-msg) {
              display: inline-block;
              padding: 35px 0;

              @include m(status) {
                color: #8c8c8c;
              }
            }
          }
        }

        @include e(audience) {
          position: absolute;
          right: 0;
          bottom: 0;
        }

        @include e(button-group) {
          position: relative;

          .yxtf-button {
            margin-right: 24px;
          }

          @include e(btn) {
            width: auto;
            min-width: 160px;
            height: 44px;
            margin-top: 32px;
            font-size: 18px;

            &.fix-bottom {
              position: absolute;
              bottom: 0;
              left: 0;
            }
          }

          @include e(qrcodeBtn) {
            position: absolute;
            bottom: 0;
            width: 44px;
            height: 44px;
            padding-left: 10px;

            @include e(qrcodeBtn-qrcode) {
              width: 24px;
              height: 24px;
              background-image: url("img/live/qrcode.png");
              background-size: 100%;
            }

            &:hover {
              @include e(qrcodeBtn-qrcode) {
                width: 24px;
                height: 24px;
                background-image: url("img/live/qrcode_clicked.png");
                background-size: 100%;
              }
            }
          }

          @include e(qrcode) {
            text-align: center;
          }
        }
      }

      .cover-error img {
        width: 452px;
        height: 253px;
      }
    }

    @include e(cont) {
      margin-top: 24px;
      padding: 32px 32px 60px 32px;
      background-color: white;

      @include e(teach-cont) {
        position: relative;
        display: flex;
        padding-bottom: 16px;
      }

      @include e(cont-title) {
        margin: 32px 0 16px;
        font-size: 24px;
        line-height: 32px;

        &:first-child {
          margin-top: 0;
        }

        @include e(evalution-link) {
          display: inline-block;
          box-sizing: border-box;
          padding: 4px 19px;
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          white-space: nowrap;
          text-align: center;
          vertical-align: text-bottom;
          background: rgba(225, 122, 26, 0.6);
          border-radius: 4px;
          outline: 0;
          cursor: pointer;
        }
      }

      @include e(avatar-group) {
        flex: 1;
        overflow: hidden;

        @include m(expand) {
          height: 74px;
        }
      }

      @include e(avatar) {
        display: inline-flex;
        align-items: center;
        margin: 0 24px 32px 0;
      }

      @include e(teacher) {
        margin-left: 14px;
        color: #262626;
        font-size: 16px;
      }

      @include e(teacher-name) {
        display: block;
        width: 70px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      @include e(name-tag) {
        display: inline-block;
        height: 20px;
        padding: 0 8px;
        color: #fff;
        font-size: 12px;
        font-style: normal;
        line-height: 20px;
        background: rgba(67, 107, 255, 0.6);
        border-radius: 10px;

        @include m(guest) {
          background: rgba(225, 122, 26, 0.6);
        }
      }

      @include e(toggle-exhibit) {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-items: center;
        align-self: baseline;
        padding: 16px 0;
        color: #426cff;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;

        @include when(expand) {
          position: absolute;
          right: 0;
          bottom: 40px;

          @include e(toggle-icon) {
            transform: rotate(180deg);
          }
        }

        @include e(toggle-icon) {
          width: 14px;
          height: 14px;
          background: url("img/live/open.png") 100%;
          background-size: 100% 100%;
        }
      }

      @include e(intro) {
        color: #595959;
        font-size: 16px;
        line-height: 28px;
        white-space: pre-wrap;
        word-wrap: break-word;
        word-break: normal;
      }

      @include e(delete) {
        height: calc(100% - 46px);
        margin: 24px 0 0;
        background-color: #fff;
      }
    }

    @include e(evalution) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80px;
      margin-top: 24px;
      padding: 0 32px;
      font-weight: 700;
      font-size: 24px;
      background: #fff;

      @include e(evalution-tips) {
        margin-left: 10px;
        color: #595959;
        font-weight: 400;
        font-size: 14px;
      }

      .yxt-link {
        margin-left: 30px;
        padding: 8px 20px;
        color: #fff;
        font-size: 18px;
        line-height: 24px;
        background: rgba(225, 122, 26, 0.6);
        border-radius: 4px;
      }
    }
  }

  @include b(live-task-corner-mark) {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    box-sizing: border-box;
    height: 24px;
    padding: 4px 8px;
    color: #fff;
    font-weight: 600;
    font-size: 12px;
    line-height: 17px;
    border-radius: 4px 0 4px 0;
    border-top-left-radius: 0 !important;

    @include m(living) {
      background: linear-gradient(90deg, #fa8943 0%, #f9622b 100%);
    }

    @include m(notStart) {
      background: linear-gradient(90deg, #40c2f9 0%, #2ba2f5 100%);
    }

    @include m(replay) {
      background: linear-gradient(90deg, #4cc3bb 0%, #3bbeb5 100%);
    }

    @include m(ended) {
      background: linear-gradient(90deg, #668290 0%, #94afbb 100%);
    }
  }

  @include b(live-task-wave) {
    @keyframes is-playing {
      0% {
        height: 8px;
      }

      100% {
        height: 4px;
      }
    }

    @keyframes is-bigger-playing {
      0% {
        height: 20px;
      }

      100% {
        height: 10px;
      }
    }

    @include e(dot) {
      display: inline-block;
      height: 8px;
      background: #fff;
      border-radius: 6px;
      transform-origin: center;
      animation: is-playing 0.5s ease infinite alternate;

      &:nth-child(1) {
        animation-delay: 0.15s;
      }

      &:nth-child(2) {
        animation-delay: 0.65s;
      }

      &:nth-child(3) {
        animation-delay: 0.15s;
      }

      @include m(corner) {
        width: 2px;
        margin-right: 1px;

        &:last-child {
          margin-right: 3px;
        }
      }
    }

    @include m(bigger) {
      @include e(dot) {
        width: 5px;
        margin-right: 3px;
        background-color: #ff730d;
        transform-origin: center;
        animation: is-bigger-playing 0.5s ease infinite alternate;

        &:nth-child(1) {
          animation-delay: 0.15s;
        }

        &:nth-child(2) {
          animation-delay: 0.65s;
        }

        &:nth-child(3) {
          margin-right: 0;
          animation-delay: 0.15s;
        }
      }
    }

    @include b(live-task-wave-pause) {
      @include e(dot) {
        display: inline-block;
        width: 5px;
        margin-right: 3px;
        background-color: #8c8c8c;
        border-radius: 6px;
        animation: pause;

        &:nth-child(1) {
          height: 10px;
        }

        &:nth-child(2) {
          height: 20px;
        }

        &:nth-child(3) {
          height: 15px;
        }
      }
    }
  }

  .loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(100% - 57px);
    color: #fff;
    text-align: center;
    background-color: #fff;

    .loading-cont {
      position: absolute;
      top: 225px;
      left: 50%;
      box-sizing: border-box;
      width: 130px;
      height: 110px;
      padding-top: 15px;
      background-color: rgba(0, 0, 0, 0.86);
      border-radius: 6px;
      transform: translateX(-50%);
    }
  }
}
