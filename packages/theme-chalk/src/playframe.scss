@import "mixins/mixins";
@import "common/var";

@include b(playframe) {
  height: 100%;

  @include e(right) {
    display: flex;
    flex-direction: column;
    align-items: end;
    justify-content: center;
    height: 100%;
  }

  @include e(wrap) {
    box-sizing: border-box;
    height: 100%;
    padding: 20px;
    background-color: #141414;
  }

  .ulcdsdk-ellipsis-2 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .ulcdsdk-break {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-word;
  }

  .playFullScreen {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1999;
    display: -ms-flexbox;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    overflow: auto;
    -ms-flex-align: center;
  }

  .gray {
    .bgheader {
      background: #272a33;
    }

    .bg {
      background: #141414;
    }

    .textcolor {
      color: #d2d3d4;
      // opacity: .8;
    }

    .textcolorop8 {
      color: #fff;
      opacity: .8;
    }

    .textcolorop7 {
      color: #fff;
      opacity: .7;
    }

    .textcolorop6 {
      color: #fff;
      opacity: .6;
    }

    .dividerbg {
      background: #323942;
    }

    .liactive {
      background: #404652;
      opacity: 1;
    }
  }

  .tag-type {
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
    height: 20px;
    padding: 0 8px;
    font-size: 12px;
    line-height: 20px;
    border: 1px solid #fff;
    border-radius: 2px;
    -ms-flex-align: center;
  }

  .leftico {
    position: absolute;
    top: 40px;
    z-index: 99;
    transition: left 0.5s;

    &-show {
      left: -8px;
    }

    &-hide {
      left: 0;

      svg {
        transform: rotate(180deg);
      }
    }
  }

  .left-expand-icon {
    width: 16px;
    height: 60px;
    background: #333742;
    border-radius: 8px;
    box-shadow: 3px 3px 4px 0 rgba(0, 0, 0, 0.5);

    svg {
      color: #fff;
      opacity: .4;
    }

    &:hover {
      background: #464c5c;

      svg {
        color: #fff;
        opacity: .6;
      }
    }
  }

  .divider {
    width: 100%;
    height: 1px;
  }

  .dividervertical {
    height: 100%;

    &-w1 {
      width: 1px;
    }

    &-w0 {
      width: 0;
    }
  }

  .flex-center {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
  }

  .flex-space-between {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-between;
  }

  .flex-1 {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
  }

  .yxtulcdsdk-header {
    flex-shrink: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    -ms-flex-negative: 0;

    .title {
      height: 64px;
    }
  }

  .yxtulcdsdk-container {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1;
    flex: 1;
    flex-basis: auto;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    min-width: 0;
    height: calc(100% - 64px);
  }

  .yxtulcdsdk-container.is-vertical {
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .yxtulcdsdk-aside__wrap {
    flex-shrink: 0;
    width: 320px;
    background-color: #1f2329;
  }

  .yxtulcdsdk-aside {
    flex-shrink: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 320px;
    overflow: auto;
    -ms-flex-negative: 0;
    background-color: #1f2329;

    .linozj {
      padding: 8px 0;

      svg {
        opacity: .5;
      }

      &:hover {
        // opacity: .6;
        background: #323841;
        border-radius: 4px;
      }
    }

    .liactive {
      color: #fff;
      border-radius: 4px;

      svg {
        opacity: 1;
      }
    }
  }

  .yxtulcdsdk-main {
    display: block;
    -ms-flex: 1;
    flex: 1;
    flex-basis: auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    -ms-flex-preferred-size: auto;

    &__cantsee {
      box-sizing: border-box;
      height: 100%;
      padding: 20px;
      background-color: #141414;

      & > div {
        width: 100%;
        height: 100%;
        background-color: #000;
        border-radius: 8px;
      }
    }
  }

  .ulcdsdk-button {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 90px;
    height: 32px;
    color: #fff;
    font-weight: 400;
    line-height: 22px;
    white-space: nowrap;
    text-align: center;
    background: #3e4352;
    border: 1px solid #3e4352;
    border-color: #3e4352;
    border-radius: 4px;
    outline: 0;
    cursor: pointer;
    -webkit-transition: .1s;
    transition: .1s;

    span {
      opacity: .8;
    }

    i {
      opacity: .72;
    }

    &:hover {
      background: #626980;
    }

    &.is-disabled,
    &.is-disabled:hover {
      color: #fff;
      background-color: #3e4352;
      background-image: none;
      border-color: #3e4352;
      cursor: not-allowed;
      opacity: .7;

      span {
        opacity: .3;
      }

      i {
        opacity: .24;
      }
    }
  }

  .yxtulcdsdk-unlock {
    position: absolute;
    top: 50%;
    left: 50%;
    box-sizing: border-box;
    text-align: center;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);

    .textcolor {
      color: #fff;
      opacity: .8;
    }
  }

  .yxtulcdsdk-unlock__wrap {
    box-sizing: border-box;
    height: 100%;
    padding: 20px;
    background-color: #141414;
  }

  .yxtulcdsdk-unlock-bg {
    box-sizing: border-box;
    height: 100%;
    background-color: #000;
  }
}

.yxtulcdsdk-chapterscompletedialog {
  .score {
    width: 100%;
    height: 86px;
    background: linear-gradient(180deg, #fef8f5 0%, #fcefe7 100%);
    border-radius: 8px;
  }

  .ulcdsdk-nextchapterbutton {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    height: 48px;
    color: #662a09;
    font-weight: 400;
    line-height: 22px;
    white-space: nowrap;
    text-align: center;
    background: linear-gradient(117deg, #f9d9be 0%, #efb291 100%);
    border: none;
    border-radius: 4px;
    outline: 0;
    cursor: pointer;
    -webkit-transition: .1s;
    transition: .1s;

    &.is-disabled {
      background: linear-gradient(117deg, #f9d9be 0%, #efb291 100%);
      opacity: 0.4;
    }
  }
}

.ulcd_spantimewarning {
  color: #fa8c16;
}

.yxtulcdsdk-play-goback {
  padding: 3px 4px;
  border-radius: 4px;

  &:hover {
    background: #3e4352;
  }
}
