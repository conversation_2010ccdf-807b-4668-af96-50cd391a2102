@import "mixins/mixins";
@import "common/var";

@include b(course-player) {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  background-color: #141414;

  & > .yxt-loading-mask {
    pointer-events: none; // 防止挡住大纲的收起按钮
  }

  @include e(inner) {
    flex: 1;
    width: 100%;
    height: 0;

    @include e(inner-left) {
      margin: 20px;
      overflow: hidden;
      background-color: #000;
      border-radius: 8px;

      iframe {
        border-radius: 8px;
      }

      @include e(exercise-model) {
        .yxtf-dialog__body {
          height: 586px;
        }
      }
    }

    @include e(inner-mid) {
      display: flex;
      flex-direction: column;
      align-items: end;
      justify-content: center;
      height: 100%;

      @include e(kng-operate) {
        padding: 8px 4px;
        color: #fff;
        background-color: #272c33;
        border-radius: 4px 0 0 4px;
      }
    }

    @include e(inner-right) {
      position: relative;
      box-sizing: border-box;
      width: 360px;
      background-color: #1f2329;

      @include e(functional) {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;

        @include e(handouts-drag) {
          position: absolute;
          top: 50%;
          left: -13px;
          display: flex;
          justify-content: space-between;
          width: 6px;
          cursor: ew-resize;

          & > div {
            width: 1px;
            height: 44px;
            background-color: #84878d;
          }
        }

        // 笔记
        @include e(note-item) {
          margin: 0 12px;
          padding: 24px 0 12px;
          border-top: 1px solid #393d42;

          &:not(:first-child) {
            margin-top: 12px;
          }

          @include e(note-item-line) {
            box-sizing: border-box;
            height: 40px;
            margin: 0 -8px;
            padding: 16px;
            cursor: pointer;
          }

          @include e(note-item-action) {
            transform: rotate(90deg);
          }

          @include e(note-item-edit) {
            height: 300px;
          }
        }

        @include e(right-close) {
          position: absolute;
          top: 24px;
          right: 18px;
          color: #757575;
          cursor: pointer;

          &:hover {
            color: #fff;
          }
        }

        .yxtf-tabs__item {
          color: rgba(255, 255, 255, 0.6);
        }

        .yxtf-tabs__item.is-active,
        .yxtf-tabs__item:hover {
          color: #fff;
        }

        .yxtf-tabs__active-bar {
          background-color: #fff;
        }

        .yxtulcdsdk-mt-12 {
          margin-top: -12px;
        }

        @include e(exercise) {
          padding: 13px 20px 13px 16px;
          background-color: #363d47;
          border-radius: 4px;

          &:not(:first-child) {
            margin-top: 24px;
          }
        }

        @include e(point) {
          position: relative;
          padding-bottom: 4px;
          cursor: pointer;
          opacity: 0.9;

          &:hover {
            opacity: 1;
          }

          @include e(point-text) {
            margin-left: 26px;
            padding: 12px 10px;
            border-radius: 4px;

            &:hover {
              background-color: #404652;
            }
          }

          @include e(point-top-line) {
            position: absolute;
            top: 0;
            left: 5px;
            z-index: 1;
            width: 1px;
            height: 23px;
            background-color: rgba($color: #fff, $alpha: 0.2);
          }

          @include e(point-bottom-line) {
            position: absolute;
            top: 23px;
            bottom: 0;
            left: 5px;
            z-index: 1;
            width: 1px;
            background-color: rgba($color: #fff, $alpha: 0.2);
          }

          @include e(point-circle) {
            position: absolute;
            top: 17px;
            left: 0;
            z-index: 2;
            width: 11px;
            height: 11px;
            background-color: rgba($color: #fff, $alpha: 0.2);
            border-radius: 50%;

            &::after {
              position: absolute;
              top: 2px;
              left: 2px;
              width: 7px;
              height: 7px;
              background-color: rgba($color: #fff, $alpha: 0.8);
              border-radius: 50%;
              content: '';
            }
          }

          &:first-child {
            @include e(point-top-line) {
              display: none;
            }
          }

          &:last-child {
            @include e(point-bottom-line) {
              display: none;
            }
          }
        }

        @include e(text) {
          color: rgba($color: #fff, $alpha: 0.8);

          &.text-highlight {
            background: #363d47;
            border-radius: 4px;
          }
        }
      }
    }

    @include e(back) {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
    }

    @include e(cover) {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    @include e(countdown) {
      position: absolute;
      top: 4px;
      right: 4px;
      z-index: 999;
      padding: 0 12px;
      line-height: 36px;
      background-color: rgba($color: #000, $alpha: 0.5);
      border-radius: 6px;
      cursor: move;
      user-select: none;
    }

    @include e(countdown-simple) {
      position: absolute;
      top: 4px !important;
      right: 4px !important;
      z-index: 999;
    }
  }

  @include e(tools) {
    display: flex;
    align-items: center;
    height: 60px;

    @include e(divider) {
      background-color: #8c8c8c !important;
    }

    @include e(like) {
      cursor: pointer;

      &:hover {
        color: #fff;

        .yxtf-action-icon__praise-light svg > g > g:first-child path:first-child {
          fill: #fff;
          stroke: #fff;
        }

        .yxtf-action-icon__collection-light svg > g > g:nth-child(4) path:first-child {
          fill: #fff;
          stroke: #fff;
        }

        @include e(notallowed) {
          color: #595959;
        }
      }
    }
  }

  @include e(small-show) {
    display: none;
  }

  @include e(notallowed) {
    cursor: not-allowed;
  }

  @include e(iframe) {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border: none;
  }

  // 笔记-编辑
  @include e(note-editor) {
    display: flex;
    flex-direction: column;

    @include e(note-editor-scroll) {
      overflow: hidden;
      background-color: #242933;
      border: 1px solid #30373f;
      border-radius: 4px;

      @include e(note-input) {
        padding: 12px;
        word-break: break-all;

        & > div:not(:first-child) {
          margin-top: 12px;
        }

        @include e(note-edit-mark) {
          display: inline-flex;
          align-items: center;
          box-sizing: border-box;
          max-width: 100%;
          padding: 0 8px;
          color: #fff;
          line-height: 24px;
          white-space: nowrap;
          background-color: #3e4652;
          border-radius: 12px;
          user-select: none;
        }
      }

      @include e(note-placeholder) {
        position: absolute;
        top: 12px;
        left: 12px;
        color: rgba($color: #fff, $alpha: 0.5);
        pointer-events: none;
      }

      @include e(note-action) {
        padding: 17px 0;
      }
    }
  }

  // 笔记查看
  @include e(note-item-view) {
    word-wrap: break-word;
    word-break: break-word;

    & > div {
      margin-top: 12px;
    }

    @include e(note-mark) {
      display: inline-flex;
      align-items: center;
      box-sizing: border-box;
      max-width: 100%;
      padding: 0 8px;
      line-height: 24px;
      white-space: nowrap;
      background-color: #58595a;
      border-radius: 12px;
      cursor: pointer;
      user-select: none;

      &:hover {
        color: #fff !important;

        .color-gray-6 {
          color: #fff !important;
        }
      }
    }

    @include m(light) {
      color: #595959;

      @include e(note-mark) {
        color: #595959;
        background-color: #f5f5f5;
      }
    }
  }

  @include e(other) {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    @include e(other-cover) {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba($color: #000, $alpha: 0.4);
    }
  }

  .ml27 {
    margin-left: 27px;
  }

  .ml22 {
    margin-left: 22px;
  }

  .ml7 {
    margin-left: 7px;
  }

  .size40 {
    width: 40px;
    height: 40px;
  }

  .p-rlt {
    position: relative;
  }

  .yxtulcdsdk-fullsize {
    position: relative;
    width: 100%;
    height: 100%;
  }

  &__playeare {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .yxtulcdsdk-web-full {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    background: #282a33;
  }

  .yxtulcdsdk-web-full &__playeare {
    height: calc(100% - 64px);
  }

  @include e(web-header) {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: 100%;
    height: 64px;
    padding: 0 24px 0 16px;
  }

  .h0 {
    height: 0;
  }

  .yxt-loading-mask {
    background-color: transparent;
  }
}

@include b(action-icon) {
  box-sizing: border-box;
  width: 50px;
  padding: 8px 2px;
  border-radius: 2px;
  cursor: pointer;
  opacity: 0.7;

  @include m(disabled) {
    cursor: not-allowed !important;
    opacity: 0.3 !important;
  }

  &:hover {
    background-color: rgba($color: #fff, $alpha: 0.1);
    opacity: 1;
  }

  &:not(:first-child) {
    margin-top: 4px;
  }

  @include m(disabled) {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

@include b (course-summary) {
  color: #fff;
  background-color: #272a33;

  .yxtf-progress-bar__outer {
    min-width: 80px;
    background-color: #4e5366 !important;
  }

  .yxtf-divider--vertical {
    margin: 0 20px;
  }

  @include e(hover) {
    cursor: pointer;
    opacity: 0.8;

    &:hover {
      opacity: 1 !important;
    }
  }

  .opacity8 {
    opacity: 0.8;
  }

  .opacity5 {
    opacity: 0.5;
  }
}

.yxtulcdsdk-course-summary__tooltip {
  margin-right: 8px;
  padding: 12px 14px;

  &.yxt-tooltip__popper.is-dark,
  &.yxt-tooltip__popper.is-dark .popper__arrow::after {
    background-color: #3d3f47;
  }
}

@include b(course-operation) {
  color: #fff;
  background-color: #272a33;

  @include e(hover) {
    cursor: pointer;
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }

  @include e(split) {
    position: relative;
    display: inline-block;
    margin-right: 6px;

    &:not(:first-child) {
      padding-left: 7px;
    }

    &:not(:first-child)::before {
      position: absolute;
      top: 4px;
      left: -7px;
      width: 1px;
      height: 12px;
      margin-left: 7px;
      background: #8c8c8c;
      content: "";
    }
  }

  .opacity6 .yxtulcdsdk-course-operation__split::before {
    background: #fff;
  }

  .opacity6 {
    opacity: .6;
  }
}

@include b(player-message) {
  position: absolute;
  bottom: 50px;
  left: 6px;
  z-index: 99;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 24px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  transform: translate(0, 0);
  transition: all 0.3s;

  @include e(content) {
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    opacity: 1;
  }

  &.yxtulcdsdk-player-message-fade-enter,
  &.yxtulcdsdk-player-message-fade-leave-active {
    transform: translate(0, 50%);
    opacity: 0;
  }
}
// 课程更多操作，样式复写
.yxtulcdsdk-course-operation__more {
  margin-top: 4px !important;
  padding: 0 !important;
  color: #fff;
  background-color: #333742 !important;
  border: none !important;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.2);

  & > .yxtulcdsdk-course-operation__hover {
    height: 36px;
    padding: 0 16px 0 14px;
  }
}

@include b(study-plan) {
  .plan-button {
    color: #fff;
    background: transparent;
    border: 1px solid #fff;
    opacity: 0.8;
  }

  .plan-button:hover {
    opacity: 1;
  }

  @include e(list) {
    min-height: 530px;
  }

  .yxtf-table__empty-block,
  .yxtf-table__empty-mask {
    padding: 80px 0;
  }
}

.course-source-list {
  box-sizing: border-box;
  padding: 0;
  padding: 7px 4px;
  background: #272a33;
  border: none;

  & > div {
    box-sizing: border-box;
    padding: 7px 17px;
    color: #bebfc2;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background: #3d4048;
    }
  }
}

.div-face-recognition {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1500;
  width: 100%;
  height: 100%;
  overflow: auto;
  background: white;
}

.yxtulcdsdk-course-player {
  .yxtbiz-watermark-v2 {
    z-index: 999 !important;
  }
}
