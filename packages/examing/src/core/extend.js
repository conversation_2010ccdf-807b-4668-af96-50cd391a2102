// 设置cookie
window.setCookie = window.setCookie || function(cname, cvalue, expiration) {
  let myCookie = cname + '=' + cvalue + ';';
  if (expiration) {
    // 设置有效期
    myCookie += 'expires=' + expiration + ';';
  }
  document.cookie = myCookie;
};
// 获取cookie
window.getCookie = window.getCookie || function(cname) {
  let result = null;
  const myCookie = '' + document.cookie + ';';
  const searchName = '' + cname + '=';
  let startOfCookie = myCookie.indexOf(searchName);
  let endOfCookie;
  if (startOfCookie !== -1) {
    startOfCookie += searchName.length;
    endOfCookie = myCookie.indexOf(';', startOfCookie);
    result = myCookie.substring(startOfCookie, endOfCookie);
  }
  return result;
};
// 清除cookie
window.clearCookie = window.clearCookie || function(name) {
  window.setCookie(name, '');
};
/**
 * ie 兼容
 */
if (!window.location.origin) {
  window.location.origin = window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '');
}
