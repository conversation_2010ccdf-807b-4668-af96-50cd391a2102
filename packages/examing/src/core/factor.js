import { Message } from 'yxt-pc';
import { i18n } from '../configs/language';

import { commonUtil } from 'yxt-biz-pc';
// 要素
export const FACTORS = {
  WRONG_QUESTION_BOOK: 'wrong_question_book'
};
// 功能点
export const FACTOR_FUCTIONS = {
  MY_MISTAKES: 'my_mistakes' // 我的错题 入口
};

export const FACTOR_FUCTIONS_ERRORS = {
};
// 功能权限异常的key
export const FACTOR_FUCTION_ERRORKEY = [
  'global.factor.invalid',
  'apis.ote.org.factor.check.senior.question.type',
  'apis.ote.org.factor.check.face.recognition',
  'apis.ote.org.factor.check.prevent.switch.screen',
  'apis.ote.org.factor.check.e.signature',
  'apis.ote.org.factor.skill.kng.view',
  'apis.ote.org.e.signature.invalid',
  'apis.ote.org.prevent.cheat.invalid',
  'apis.ote.org.factor.senior.ques.disabled'
];

// 项目的所有依赖功能点及要素
export const FACTORS_AND_FUCTIONS = {
  factors: Object.values(FACTORS),
  functions: Object.values(FACTOR_FUCTIONS)
};

// 旧的12种状态
// const OBJ_DISABLED_VALUES = [2, 8, 11] // 功能禁用的所有值
// const OBJ_HIDDEN_VALUES = [1, 7, 10, 5] // 功能隐藏的所有值
// const OBJ_CUSTOM_VALUES = [3, 9, 12] // 功能自定义的所有值
// const OBJ_ENABLED_VALUES = [4, 5, 6] // 功能启用状态

// 新的8种状态
export const OBJ_DISABLED_VALUES = [6]; // 功能置灰的所有值
export const OBJ_HIDDEN_VALUES = [0, 1, 3, 5]; // 功能隐藏的所有值
export const OBJ_CUSTOM_VALUES = [4, 7]; // 功能自定义的所有值

export const OBJ_ENABLED_VALUES = [2, 3, 4, 7]; // 功能可用状态
export const OBJ_ENABLED_VALUES_NO_CUSTOM = [2, 3, 4]; // 功能可用状态, 不包含自定义

/**
 * 获取按钮类的状态控制
 * @param {String} funCode 功能点
 * @returns 状态控制策略
 */
export const getFactorFunctionStatus = (funCode) => {
  try {
    /*
     0.要素下架-隐藏
     1.要素上架-未购买-隐藏

     2.要素上架-已购买未过期（启用）-显示
     3.要素上架-已购买未过期（启用）-隐藏
     4.要素上架-已购买未过期（启用）-自定义

     5.要素上架-已购买已过期（禁用）-隐藏
     6.要素上架-已购买已过期（禁用）-置灰
     7.要素上架-已购买已过期（禁用）-自定义
    */
    const objStatus = commonUtil.checkTimeOutFnc(funCode);
    // console.log(funCode, objStatus)
    // return {
    //   disabled: false,
    //   hidden: false
    // }
    return {
      disabled: OBJ_DISABLED_VALUES.indexOf(objStatus) >= 0,
      hidden: OBJ_HIDDEN_VALUES.indexOf(objStatus) >= 0,
      custom: OBJ_CUSTOM_VALUES.indexOf(objStatus) >= 0,
      enabled: OBJ_ENABLED_VALUES.indexOf(objStatus) >= 0,
      enabledNoCustom: OBJ_ENABLED_VALUES_NO_CUSTOM.indexOf(objStatus) >= 0,
      value: objStatus
    };
  } catch (error) {
    return {};
  }
};

let warningTS = 0;
/**
 * 根据功能过期禁用重设属性的值
 * @param {*} funCode 功能编号
 * @param {*} resetFun 重设值方法
 */
export const resetValueBystatus = (funCode, resetFun) => {
  const status = getFactorFunctionStatus(funCode);
  if (!status.enabled) {
    const errMsg = i18n.t(FACTOR_FUCTIONS_ERRORS[funCode]);
    let ts = Date.now();
    let to = 0;
    const tDiff = ts - warningTS;
    // 弹出提示信息
    if (errMsg) {
      // 弹窗连续触发有点问题，需要延迟下
      if (tDiff < 1000) {
        to = 1000 + tDiff;
        ts += to;
      }
      warningTS = ts;
      setTimeout(() => {
        Message.warning({
          showClose: true,
          duration: 0,
          message: errMsg
        });
      }, to);
    }
    if (resetFun && typeof (resetFun) === 'function') {
      resetFun(status);
    }
  }
};

