/** 循环练习中的某个练习的状态
 */
export const LoopPracticeStatus = {
  // userBatchStatus 0:开始练习,1-再练一次,2-已完成,3-已过期
  start: 0,
  repeat: 1,
  done: 2,
  expired: 3
};

/**
 * 循环练习和循环考试的结束类型
 * */
export const LoopEndType = {
  // endType 0：无限重复-不结束；1：终止于某一天；2：循环次数；
  infinitely: 0,
  endByDay: 1,
  endByTimes: 2
};

/**
 * 循环练习和循环考试中子项目激活时间类型
 * */
export const LoopActivateTimeType = {
  // effectType 0：当天；1：下个周期结束前；2：项目结束前；
  veryDay: 0,
  nextCycle: 1,
  loopEnd: 2
};

/** 循环练习类型
 * */
export const LoopFrequencyType = {
  // frequencyType 0：天；1：周；2：月；
  day: 0,
  week: 1,
  month: 2
};

/**
 * 非循环练习的状态
 * */
export const PracticeStatusEnum = {
  // praStatus 0：未开始；1：发布中；2：进行中(已发布)；3：已结束；4：归档中；5：已归档
  notStart: 0,
  publishing: 1,
  published: 2,
  ended: 3,
  archiving: 4,
  archived: 5
};

/**
 * 用户练习状态
 * */
export const PracticeUserStatusEnum = {
  // upmStatus 0：未开始；1：练习中；2：已提交；3：已完成；4：已归档；
  notStart: 0,
  doing: 1,
  submitted: 2,
  finished: 3,
  archived: 4
};

/**
 * 1，考试和练习题型的枚举
 * 2，考试给题型重命名后题型的枚举。只有考试可以重命名题型名称，练习不行。字段：quesTypeTitles[index].type */
export const QuesTypeEnum = {
  // 考试叫 type 0：单选、1：多选题、2：判断题、3：填空题、4：问答题、5：组合题
  // 练习叫 quesType
  // 错题本叫 quesType
  singleChoice: 0,
  multiChoice: 1,
  judge: 2,
  fillBlank: 3,
  shortAnswer: 4,
  composite: 5
};

/** 考试和练习多媒体型的枚举 */
export const QuesMediaTypeEnum = {
  // 考试叫 quesType 0：文字、1：图片、2：音频、3：视频
  // 练习叫 questionType
  // 错题本叫 mediaType
  text: 0,
  pic: 1,
  audio: 2,
  video: 3
};

export const ChartDataType = {
  number: 1, // 数量
  percent: 2 // 百分比
};

// 统计报表的图形类型
export const ChartType = {
  circle: 1, // 环形图
  pie: 2, // 饼图
  bar: 3, // 柱状图
  line: 4, // 折线图
  stack: 5, // 堆叠
  strip: 6, // 条形图
  gauge: 7, // 仪表盘
  radar: 8 // 雷达图
};

export const ExamArrangeStatusEnum = {
  // status 0：未开始；1：发布中；2：进行中(已发布)；3：已结束；4：归档中；5：已归档
  notStart: 0,
  publishing: 1,
  published: 2,
  ended: 3,
  archiving: 4,
  archived: 5
};

export const ExamUserStatusEnum = {
  // userStatus 0：未开始；1：考试中；2：已提交；3：批阅中；4：已完成
  notStart: 0,
  doing: 1,
  submitted: 2,
  marking: 3,
  finished: 4
};

export const ExamSubmitTypeEnum = {
  //  -1:未提交 0：主动提交；1：后台点击结束考试时的内存提交；2：自动提交（(用时已尽)；3：自动提交(统一交卷)；4：自动交卷（APP多次退出 或 PC端多次切屏）；5：自动提交，超过指定时间未操作考试页面；
  unsubmit: -1,
  self: 0,
  admin: 1,
  timeout: 2,
  together: 3,
  cutout: 4,
  leave: 5
};

// 进入考试 继续考试 已提交 批阅中 已完成
export const ExamUserStatusI18nEnum = ['pc_ote_btn_enterexam', 'pc_ote_btn_continueexam', 'pc_ote_lbl_submitted', 'pc_ote_lbl_marking', 'pc_ote_lbl_done'];

// 用户考试的几个页面步骤
export const UserExamStep = {
  preview: 0, // 考试预览
  examing: 1, // 考试答题
  result: 2, // 考试结果
  answers: 3, // 答卷详情
  wrong: 4 // 重做错题
};
// 用户考试的几个页面步骤
export const UserPracticeStep = {
  preview: 0, // 练习预览
  practicing: 1, // 练习答题
  wrong: 2 // 重做错题
};

/* 考试空页面显示 */
export const ErrorPageEnum = {
  1: { btnStr: 'pc_ote_btn_exam_error_page' },
  2: { btnStr: 'pc_ote_btn_practice_error_page' }
};
