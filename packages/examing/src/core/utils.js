import { commonUtil } from 'yxt-biz-pc';
import { i18n } from '../configs/language';
import { QuesTypeEnum } from './enums';
import xss from 'xss';

export const htmlEncode = function(str) {
  if (str === undefined || str === null || str.length === 0) return '';
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/ /g, '&nbsp;')
    .replace(/'/g, '&#39;')
    .replace(/"/g, '&quot;')
    .replace(/·/g, '&#183;');
  // .replace(/\\/g, `\\\\`)
  // .replace(/%/g, `%25`)
};
export const htmlDecode = function(str) {
  if (str === undefined || str === null || str.length === 0) return '';
  return str
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&nbsp;/g, ' ')
    .replace(/&#39;/g, '\'')
    .replace(/&quot;/g, '"')
    .replace(/&#183;/g, '·');
  // .replace(/\\\\/g, `\\`)
  // .replace(/%25/g, `%`)
};
export const isNullOrEmpty = s => {
  return s === null || s === '';
};

export const isString = (str) => {
  return typeof str === 'string';
};

export const isNullOrUndefined = s => {
  return [undefined, null, 'undefined', 'null'].includes(s);
};

// 格式化时间所用的数字
export const formatNumber = function(n) {
  return n >= 10 ? n : '0' + n;
};

export const getDateType = (text, notReplace) => {
  if (isNullOrEmpty(text)) {
    return '';
  }
  if (!notReplace && isString(text)) {
    text = text.replace(/\.\d+/, '').replace(/-/g, '/');
  }
  const date = new Date(text);
  if (isNullOrEmpty(date) || isNaN(date.getDate())) {
    return '';
  }

  return date;
};

export const dateToString = (text, format, notReplace) => {
  const date = getDateType(text, notReplace);
  if (!date) return '';

  let dateStr = format;
  dateStr = dateStr
    .replace('yyyy', date.getFullYear())
    .replace('MM', (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1))
    .replace('dd', (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()))
    .replace('HH', (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()))
    .replace('mm', (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()))
    .replace('ss', (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()));
  dateStr = dateStr
    .replace('M', date.getMonth() + 1)
    .replace('d', date.getDate())
    .replace('H', date.getHours())
    .replace('m', date.getMinutes())
    .replace('s', date.getSeconds());
  return dateStr;
};

/**
 * 获取时间范围 modify by wxl 2020-1-4
 * @param {String} startTime 开始时间
 * @param {String} endTime 结束时间
 * @param {String} formatDay 年月日格式  例：'yyyy-MM-dd'
 * @param {String} formatTime 时分秒格式 例：'HH:mm'
 */
export const getTimePeriod = (startTime, endTime, formatDay, formatTime) => {
  if (startTime && endTime) {
    const st = new Date(startTime.replace(/-/g, '/'));
    const et = new Date(endTime.replace(/-/g, '/'));
    const nt = new Date();
    const stDay = st.setHours(0, 0, 0, 0);
    const etDay = et.setHours(0, 0, 0, 0);
    const ntDay = nt.setHours(0, 0, 0, 0);
    if (stDay === etDay) {
      if (stDay !== ntDay) {
        return dateToString(startTime, `${formatDay} ${formatTime}`) + ' ~ ' + dateToString(endTime, formatTime);
      } else {
        return dateToString(startTime, formatTime) + ' ~ ' + dateToString(endTime, formatTime);
      }
    } else {
      return dateToString(startTime, `${formatDay} ${formatTime}`) + ' ~ ' + dateToString(endTime, `${formatDay} ${formatTime}`);
    }
  } else {
    return '';
  }
};
// 拼接URL参数
export const linkSubString = (url, data) => {
  if (url === null || url === '') {
    return url;
  }
  let queryString = '';
  if (typeof data === 'object') {
    for (const i in data) {
      queryString += i + '=' + data[i] + '&';
    }
  }
  if (url.indexOf('?') > url.indexOf('/')) {
    url += '&';
  } else {
    url += '?';
  }
  if (queryString !== '') {
    queryString = queryString.substr(0, queryString.length - 1);
  }
  url += queryString;
  return url;
};

// 获取纯文本内容
export const getSafeHtmlContent = (s) => {
  s = xss(s);
  const div = document.createElement('div');
  div.innerHTML = s;
  return div.innerText || div.textContent;
};

// 去除html标记
export const replaceHtmlTags = (s) => {
  s = filterXss(s);
  const div = document.createElement('div');
  div.innerHTML = s;
  return div.innerText || div.textContent;
};

/**
 * 过滤xss攻击代码
 * @param {String} text 文本
 */
export const filterXss = (text) => {
  if (!text) {
    return '';
  }

  // 去掉script标签
  text = text.replace(/<script[\s\S]*?(<\/script>|\/>)/ig, '');
  // 去除on事件
  const onTagReg = /<[^>]*?( |\/)(on[\W\w]*?=([""'])?([^'""]+)([""'])?)[\S\s]*?>/ig;
  const onReg = /( |\/)on[\W\w]*?=([""'])?([^'""]+)([""'])?/ig;
  let onMatches = onTagReg.exec(text);
  while (onMatches) {
    text = text.replace(onMatches[0], onMatches[0].replace(onReg, ''));
    onMatches = onTagReg.exec(text);
  }
  // 去除非链接href
  const hrefReg = /<a[^>]*(href=([""']?([^'""]+)([""'])?))[\S\s]*?>/ig;
  let hrefMatches = hrefReg.exec(text);
  while (hrefMatches) {
    hrefMatches[3].indexOf('http') !== 0 && (text = text.replace(hrefMatches[0], hrefMatches[0].replace(hrefMatches[1], '')));
    hrefMatches = hrefReg.exec(text);
  }
  // 去除eval(...)和expression(...)
  text = text.replace(/(eval|expression)\(.*?\)/ig, '');

  return text;
};

// 将时间格式化成YYYY/MM/dd HH:mm:ss
export const DateFormat = (time) => {
  return (time || '').replace(/\.\d+/, '').replace(/-/g, '/');
};

// 整数
export const toNumber = (s) => {
  return s.replace(/[^\d]/g, '');
};

// boolean类型转换成0和1
export const booleanToInt = (json) => {
  for (const key in json) {
    if (typeof json[key] === 'boolean') {
      json[key] = json[key] ? 1 : 0;
    }
  }
  return json;
};

/**
 * 数字转字母
 * @param {number} num 数字
 */
export const convertASCIIForNum = (num) => {
  let itemCode = '';
  num = num + 65;
  if (num > 64 && num < 91) {
    itemCode = String.fromCharCode(num);
  }
  return itemCode;
};

/**
 * 获取题号的名称。中文：一二三...  英文：Ⅰ Ⅱ Ⅲ Ⅳ Ⅴ...
 * 支持至百位
 * @param {number} num 题号
 */
export const getQuesTypeIndexName = (num) => {
  let newNum = '';
  const lan = commonUtil.getLanguage();
  switch (lan) {
    case 'zh':// 简体
    case 'ha':// 繁体
      const KEYS_LAN_CH_NUM = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']; //  lan === 'zh' ? ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'] : ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const KEYS_LAN_CH_UNIT = lan === 'zh' ? ['', '十', '百', '千', '万'] : ['', '十', '百', '千', '萬'];
      const strArr = num.toString().split('').reverse();
      for (let i = 0; i < strArr.length; i++) {
        newNum = (
          (i === 0 && strArr[i] === '0') || (i > 0 && strArr[i] === '0' && strArr[i - 1] === '0')
            ? ''
            : (
              strArr[i] === '0'
                ? KEYS_LAN_CH_NUM[0]
                : (
                  KEYS_LAN_CH_NUM[strArr[i]] + KEYS_LAN_CH_UNIT[i]
                )
            )) + newNum;
      }
      return newNum;
    default:
      // 英文
      const k = Math.floor(num / 1000);
      const h = Math.floor((num % 1000) / 100);
      const t = Math.floor((num % 100) / 10);
      const o = num % 10;
      const KEYS_LAN_LM_ONE = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX'];
      const KEYS_LAN_LM_TEN = ['X', 'XX', 'XXX', 'XL', 'L', 'LX', 'LXX', 'LXXX', 'XC'];
      const KEYS_LAN_LM_HUN = ['C', 'CC', 'CCC', 'CD', 'D', 'DC', 'DCC', 'DCCC', 'CM'];
      const KEYS_LAN_LM_THO = 'M'; // 千位以上有点问题
      for (let i = 0; i < k; i++) {
        newNum += KEYS_LAN_LM_THO;
      }
      if (h) {
        newNum += KEYS_LAN_LM_HUN[h - 1];
      }
      if (t) {
        newNum += KEYS_LAN_LM_TEN[t - 1];
      }
      if (o) {
        newNum += KEYS_LAN_LM_ONE[o - 1];
      }
      return newNum;
  }
};

// 显示--（空或字符串）
export const getStringValue = (val) => {
  if (!val) {
    return '--';
  }
  return val;
};

// 显示-- (传入是数字、空或字符串0)
export const getNumberValue = (val) => {
  if (!val || val === 0) {
    return '--';
  }
  return val;
};

// 描述类信息替换\r\n
export const getDescription = (val) => {
  if (!val) {
    return '--';
  }
  val = dealLineFeed(val, true);
  return val;
};

// 判断是否是IE浏览器
export const isIE = () => {
  if (window.navigator.userAgent.indexOf('MSIE') >= 1) {
    return true;
  }
  return !!window.ActiveXObject || 'ActiveXObject' in window;
};

// 移除元素方法
export const remove = (_element) => {
  if (isIE()) {
    _element.removeNode(true);
  } else {
    _element.remove();
  }
};

/**
 * 处理换行符
 * @param {String} txt 文本
 * @param {Boolean} [isNotHtml] 是否非Html内容
 */
export const dealLineFeed = (txt, isNotHtml) => {
  if (txt) {
    if (isNotHtml) {
      txt = htmlEncode(txt); // 对多行文本就行编码预防xss
    }
    // 去掉所有的换行符
    txt = txt.replace(/\r\n/g, '<br />');
    txt = txt.replace(/\r/g, '<br />');
    txt = txt.replace(/\n/g, '<br />');
  }
  return txt;
};

/**
 * 时分秒转换
 * @param {Number} s 秒
 * @param {Object} vm this
 */
export const convertSecToHMS = (s) => {
  let timeStr = '';
  if (s > -1) {
    const hour = Math.floor(s / 3600);
    const min = Math.floor(s / 60) % 60;
    const sec = s % 60;
    if (hour > 0) { timeStr += hour + i18n.t('pc_ote_lbl_hour'); }
    if (min > 0) { timeStr += min + i18n.t('pc_ote_lbl_minute'); }
    if (sec > 0) { timeStr += sec + i18n.t('pc_ote_lbl_second'); }
  }
  return timeStr === '' ? '--' : timeStr;
};

/**
 * 分秒转换
 * @param {Number} s 秒
 */
export const convertSecToMS = (s) => {
  let timeStr = '';
  if (s > -1) {
    const min = Math.floor(s / 60);
    const sec = s % 60;
    if (min > 0) { timeStr += min + i18n.t('pc_ote_lbl_minute'); }
    if (sec > 0) { timeStr += sec + i18n.t('pc_ote_lbl_second'); }
  }
  return timeStr === '' ? '--' : timeStr;
};

/**
 * 是否中文字符
 * @param {string} letter
 */
const isChinese = letter => {
  return /^[\u4e00-\u9fa5]$/.test(letter);
};

/**
 * 截取字符串
 * @param {string} name 字符串
 * @param [maxLen=4]
 */
export const getShortName = (name, maxLen = 4) => {
  if (name.length <= maxLen) return name;
  maxLen = maxLen * 2; // 中文字算长度，英文2个算1个
  const arr = name.split('');
  const nameArr = [];
  let leng = 0;
  for (let index = 0; index < arr.length; index++) {
    const letter = arr[index];
    const weight = isChinese(letter) ? 2 : 1;
    if (leng + weight <= maxLen) {
      nameArr.push(letter);
      leng += weight;
    } else {
      nameArr.push('...');
      break;
    }
  }
  return nameArr.join('');
};

/**
 * 闭包节流函数(防止频繁操作请求API)
 * @param {object} fn 执行方法
 * @param {number} delay 延迟时间
 */
export const throttle = function(fn, delay) {
  let timer = null;
  return () => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
};

/**
 * 将数组转化为2级
 * @param {Array} datas 执行方法
 * @param {Number} len 一维的长度
 */
export const dealListTo2Deep = function(datas, len = 3) {
  const arrNew = [];
  let arrSub = [];
  for (let i = 0; i < datas.length; i++) {
    if (i % len === 0) {
      arrNew.push(arrSub);
    }
    arrSub.push(datas[i]);
    if (i % len === len - 1) {
      arrSub = [];
    }
  }
  return arrNew;
};

const replaceAllName = String.prototype.ReplaceAll ? 'ReplaceAll' : 'replaceAll';
/**
 * 处理文本、富文本内容
 * @param {String} str 富文本内容
 * @param {Boolean} [isInLine] 是否需要在行内显示，此时需要处理第一个p
 */
export const cleanStyle = function(str, isInLine) {
  if (!str) {
    return '';
  }
  if (str) {
    if (str.indexOf('<p>') === 0 && str.indexOf('<p>') === str.lastIndexOf('<p>')) {
      // 单个纯粹的P, 直接去除。当成纯文本
      str = str.replace('<p>', '').replace('</p>', '');
    } else if (isInLine) {
      // 需要在行内展示的情况： A. ******
      // 处理原P标签，无样式的默认行替换掉第一行实现ABCD插入, 同时处理间距和富文本保持一致
      try {
        str = str.replace('<p>', '').replace('</p>', '')[replaceAllName]('<p>', '<p class="mv10">');
      } catch (error) {
        str = str.replace('<p>', '').replace('</p>', '');
        str = str.replace(/<p>/g, '<p class="mv10">');
      }
    } else {
      // 其余情况 去除顶部margin-top来对其，同时处理间距和富文本保持一致
      try {
        str = str.replace('<p>', '<p class="mt0 mb10" style="margin-top: 0px">')[replaceAllName]('<p>', '<p class="mv10">');
      } catch (error) {
        str = str.replace('<p>', '<p class="mt0 mb10" style="margin-top: 0px">');
        str = str.replace(/<p>/g, '<p class="mv10">');
      }
    }
  }
  // // 去除图片中style样式
  // let reg = /(<img.+?>)/g
  // if (str && reg.test(str)) {
  //   str = str.replace(reg, (pattern) => {
  //     return pattern.replace(/\s+style="[^"]*"/g, ' ')
  //   })
  // }
  // 替换&nbsp; 单个空格留空格 多个每2个替换为 空格+&nbsp;
  const reg1 = /&nbsp;/g;
  const reg2 = / {2}/g;
  if (str) {
    str = str.replace(reg1, ' ');
    str = str.replace(reg2, ' &nbsp;');
  }
  // \r\n 改成<br>自动换行
  return dealLineFeed(str);
};

export const loadScript = src => {
  return new window.Promise((resolve, reject) => {
    const script = document.createElement('script');
    const head = document.getElementsByTagName('head')[0];
    script.type = 'text/javascript';
    script.async = true;
    script.defer = true;
    script.src = src;
    script.onload = () => resolve();
    script.onerror = () => reject();
    head.appendChild(script);
  });
};

export const loadStyle = src => {
  return new window.Promise((resolve, reject) => {
    const links = document.head.getElementsByTagName('link');
    const head = document.getElementsByTagName('head')[0];
    const link = document.createElement('link');
    link.type = 'text/css';
    link.rel = 'stylesheet';
    link.href = src;
    link.onload = () => resolve();
    link.onerror = () => reject();
    if (links.length === 0) {
      head.appendChild(link);
    } else {
      head.insertBefore(link, links[0]);
    }
  });
};

/**
 * 获取对象对应的语言的名称
 * @param {Object} data 数据
 * @param {String} key 名称key
 */
export const Globalize = (data, key) => {
  if (!data) {
    return '';
  }
  const oriKey = key || 'name';
  const lang = localStorage.lang || 'zh';
  if (lang === 'tw') {
    key = 'haName';
  } else if (lang === 'en') {
    key = 'enName';
  }
  return data[key] || data[oriKey];
};

/**
 * @param {string} val 时间字符串
 */
export const shortDate = (val) => {
  return getStringValue(dateToString(val, 'yyyy-MM-dd'));
};

/**
 * @param {string} val 时间字符串
 */
export const shortMonTime = (val) => {
  return getStringValue(dateToString(val, 'MM-dd HH:mm'));
};

/**
 * @param {string} val 时间字符串
 */
export const shortDateTime = (val) => {
  return getStringValue(dateToString(val, 'yyyy-MM-dd HH:mm'));
};

/**
 * @param {string} val 时间字符串
 * yyyy-MM-dd HH:mm:ss
 */
export const shortSecondTime = (val) => {
  return getStringValue(dateToString(val, 'yyyy-MM-dd HH:mm:ss'));
};

/**
 * table formatter
 * @param {string} val 时间字符串
 */
export const shortDateTimeFormatter = (row, col, val, index) => {
  return shortDateTime(val);
};

/**
 * table formatter
 * @param {string} val 时间字符串
 */
export const shortSecondTimeFormatter = (row, col, val, index) => {
  return shortSecondTime(val);
};

/**
 * 优先判断是否是同一年，然后进行不同结果返回
 * @param {*} val
 * @returns
 */
export const shortMonTimeYear = (val) => {
  const date = getDateType(val);
  if (!date) return '';

  const currentYear = new Date().getFullYear();

  if (date.getFullYear() === currentYear) {
    return shortMonTime(date);
  }

  return shortDateTime(date);
};

/**
 * 使用JSON.parse 和 JSON.stringify进行深拷贝
 * @param {Object} obj 对象
 */
export const jsonDeepClone = (obj) => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * 重置页面滚动条，触发一些滚动的事件效果
 */
export const resetScroll = () => {
  window.scrollTop = 1;
  window.scrollTop = 0;
  const mainApp = document.getElementById('app');
  if (mainApp) {
    mainApp.scrollTop = 1;
    mainApp.scrollTop = 0;
    const navMain = mainApp.children[0];
    if (navMain) {
      navMain.scrollTop = 1;
      navMain.scrollTop = 0;
    }
  }
};

/**
 * 根据 object对象的path路径获取值。 如果解析 value 是 undefined 会以 defaultValue 取代。
 * @param {Object} object  : 要检索的对象。
   @param {Array|string} path : 要获取属性的路径。
   @param {*} [defaultValue] : 如果解析值是 undefined ，这值会被返回。
 */
export const getPathVal = (object, path, defaultValue) => {
  const pathArr = path.replace(/\[/g, '.').replace(/\]/g, '').split('.');
  return pathArr.reduce((pre, old) => {
    return (pre || {})[old];
  }, object) || defaultValue;
};

/**
 * 防抖
 * @param {*} fn
 * @param {*} delay
 * @param {*} immediate
 * @returns
 */
export const debounce = (fn, delay, immediate) => {
  let timer;
  let result;
  return function(...args) {
    if (timer) clearTimeout(timer);

    if (immediate) {
      // 如果timer存在，说明第二次调用的时候还没到delay时间，因为如果超过delay时间
      // timer会被赋值为null，所以这个时候我们不应该执行fn，应该重新设置一个定时器
      // 但如果是一次的时候，因为还没有设过定时器，所以这里timer会是undefined
      if (timer) {
        timer = setTimeout(() => { timer = null; }, delay);
      } else {
        result = fn.apply(this, args);
        return result;
      }
    } else {
      timer = setTimeout(() => fn.apply(this, args), delay);
    }
  };
};

/**
 *  关闭页面 处理isv关闭后残留空白页的问题
 * @param {*} routeMethod 页面跳转
 * @returns
 */
export const windowClose = (routeMethod) => {
  try {
    const appBrowser = new window.Browser();
    if (appBrowser.browser === 'DingTalk') {
      if (window.opener && window.opener.location.href) {
        window.location.href = window.opener.location.pathname + '' + window.opener.location.hash;
      } else if (typeof routeMethod === 'function') {
        routeMethod();
      } else {
        window.close();
      }
    } else {
      if (window.WeixinJSBridge) {
        document.addEventListener(
          'WeixinJSBridgeReady', () => {
            window.WeixinJSBridge.call('closeWindow');
          });
        window.WeixinJSBridge.call('closeWindow');
      } else {
        window.close();
      }
    }
  } catch (error) {
    window.close();
  }
};

export const isQW = () => {
  return localStorage.sourceCode === '100';
};

/**
 * 节流
 * @param {*} fn
 * @param {*} wait
 * @param {*} immediate
 * @returns
 */
export const throttling = (fn, wait, immediate) => {
  let timer;
  let context, args;

  const run = () => {
    timer = setTimeout(() => {
      if (!immediate) {
        fn.apply(context, args);
      }
      clearTimeout(timer);
      timer = null;
    }, wait);
  };

  return function() {
    context = this;
    args = arguments;
    if (!timer) {
      // throttle, set
      if (immediate) {
        fn.apply(context, args);
      }
      run();
    } else {
      // throttle, ignore
    }
  };
};

// 获取可滚动的父亲
export const getScrollParent = (element, passNoScroll) => {
  let node = element;
  while (node &&
    node.nodeType === document.ELEMENT_NODE &&
    node.tagName !== 'HTML' &&
    node.tagName !== 'BODY' &&
    node !== window
  ) {
    const { overflowY } = window.getComputedStyle(node);

    if (/scroll|auto/i.test(overflowY) && !(passNoScroll && node.scrollHeight <= node.clientHeight)) {
      return node;
    }

    node = node.parentNode;
  }

  return window;
};

/**
 * 日期格式化
 * @param {Object} value 日期值
 * @param {String} part 格式
 */
export const dateFormatUtil = (value, part) => {
  let date;
  if (value instanceof Date) {
    date = value;
  } else if (typeof value === 'number') { // 时间戳
    date = new Date(value);
  } else {
    if (typeof value === 'undefined' || value === null || value === '' || value === '-' || value.indexOf('0001') >= 0 || value.indexOf('1900') >= 0) {
      return '-';
    }
    if (value.length > 10) {
      value = value.replace(/T/, ' ');
    }
    date = new Date(value.replace(/-/g, '/').split('.')[0]);
  }
  if (date === new Date(1970, 0, 1)) {
    return '-';
  }
  let redate = '';
  part = (part == null) ? 'yyyy-MM-dd HH:mm' : part;
  const y = date.getFullYear();
  const M = date.getMonth() + 1;
  const d = date.getDate();
  const H = date.getHours();
  const m = date.getMinutes();
  const s = date.getSeconds();
  const MM = (M > 9) ? M : '0' + M;
  const dd = (d > 9) ? d : '0' + d;
  const HH = (H > 9) ? H : '0' + H;
  const mm = (m > 9) ? m : '0' + m;
  const ss = (s > 9) ? s : '0' + s;
  redate = part.replace('yyyy', y).replace('MM', MM).replace('dd', dd).replace('HH', HH).replace('mm', mm).replace('ss', ss).replace('M', M).replace('d', d).replace('H', H).replace('m', m).replace('s', s);
  return redate;
};

export const downFileUrl = (name, url) => {
  if (url) {
    commonUtil.common.downloadFileIE(name, url);
  }
};

/**
 * 写日志 https://alidocs.dingtalk.com/i/nodes/QG53mjyd80RjZYvGUavYPZpKV6zbX04v
  category: 111, //日志分类
  msg: '用户提交考试',// 日志描述类容
  infos: {
    w_succ: 1, // 用于计算率的标识,可选为1和0  1：成功   0：失败
    c1: JSON.stringify({
      xxx: ''
    }) //自定义字段1，用于辅助问题排查，将展示在日志详情中，可选
  }
 */
export const reportYxtLog = (category, msg, infos) =>{
  try {
    if (!window.yxtRPT || !window.yxtRPT.report) {
      return;
    }

    const logContent = {
      category: category, // 日志分类
      msg: msg // 日志描述类容
    };
    infos && Object.assign(logContent, infos);

    window.yxtRPT.report(logContent);
  } catch (error) {
  }
};

/**
 * 合并填空题所有的答案和备选答案
 * @param {*} item
 * @returns
 */
export const mergeAnswerFun = (item) => {
  const { itemAnswer, itemAnswer1, itemAnswer2, itemAnswer3, itemAnswer4, itemAnswer5 } = item;
  return [itemAnswer, itemAnswer1, itemAnswer2, itemAnswer3, itemAnswer4, itemAnswer5].filter(item => item && item.trim() !== '').join(';');
};

/** 从服务器数据渲染题目的用户答案和正确答案。
 * 新增字段： userAnswer: 用户答案
 *          correctAnswer：正确答案
 *          isAnswered：是否回答
 * 注意字段名称：服务器返回的用户回答的字段：userAnswers
 *             填空题子项：fillItem
 *             组合题子题名称：subQuesItem
 * */
export const parseQuesAnswer = (ques) => {
  ques.userAnswers = ques.userAnswers || [];
  switch (ques.quesType) {
    case QuesTypeEnum.singleChoice:
      let correctAnswer = '';
      if (ques && ques.choiceItems) {
        ques.choiceItems.forEach(function(e, i) {
          if (e.answer === 1) { correctAnswer = convertASCIIForNum(i); }
        });
      }
      ques.userAnswer = ques.userAnswers[0] || '';
      ques.correctAnswer = correctAnswer;
      ques.isAnswered = ques.userAnswer ? 1 : 0;
      break;
    case QuesTypeEnum.multiChoice:
      const correctAnswers = [];
      if (ques && ques.choiceItems) {
        ques.choiceItems.forEach(function(e, i) {
          if (e.answer === 1) { correctAnswers.push(convertASCIIForNum(i)); }
        });
      }
      ques.correctAnswer = correctAnswers.join('、');
      ques.userAnswer = ques.userAnswers || [];
      ques.isAnswered = (ques.answers && ques.answers.length > 0) ? 1 : 0;
      break;
    case QuesTypeEnum.judge:
      let userAnswer = '-1';
      // 正确错误文本
      const labels = [ques.judgeWrongOptionContent || i18n.t('pc_ote_lbl_wrong'), ques.judgeCorrectOptionContent || i18n.t('pc_ote_lbl_correct')];
      userAnswer = ques.userAnswers[0];

      ques.isAnswered = ['0', '1'].includes(userAnswer);
      ques.correctAnswer = labels[ques.judgeAnswer];
      ques.userAnswer = userAnswer;
      break;
    case QuesTypeEnum.fillBlank:
      const correctAnswerArr = [];
      ques.userAnswer = [];
      const hasAnswers = ques.userAnswers && ques.userAnswers.length > 0;
      if (ques && ques.fillItems) {
        ques.fillItems.forEach(function(e, i) {
          if (e.itemAnswer) {
            ques.userAnswer.push(hasAnswers ? ques.userAnswers[i] : '');
            correctAnswerArr.push(mergeAnswerFun(e));
          }
        });
      }
      ques.correctAnswer = correctAnswerArr.join('、');
      ques.isAnswered = ques.userAnswers && ques.userAnswers.length && ques.userAnswers.filter(item => item).length > 0 ? 1 : 0;
      break;
    case QuesTypeEnum.shortAnswer:
      ques.userAnswer = ques.userAnswers[0] || '';
      ques.isAnswered = (ques.userAnswer || (ques.attach && ques.attach.length > 0)) ? 1 : 0;
      ques.correctAnswer = cleanStyle(htmlEncode(ques.answerContent)) || htmlEncode((JSON.parse(ques.answerKeyword || null) || []).map(item => item.name).join(';'));
      break;
    case QuesTypeEnum.composite:
      // 处理子题
      ques.subQuesItem.forEach(q => parseQuesAnswer(q));
      break;
    default:
      break;
  }
  ques.correctAnswer = ques.correctAnswer || i18n.t('pc_ote_lbl_noCorrectAnswer');
};

/** 组装题目提交内容 */
export const assembleQuesAnswer = ques => {
  let answer = [];
  switch (ques.quesType) {
    case QuesTypeEnum.singleChoice:
    case QuesTypeEnum.judge:
      answer.push(ques.userAnswer);
      ques.isAnswered = 1;
      break;
    case QuesTypeEnum.multiChoice:
      answer = ques.userAnswer;
      ques.isAnswered = ques.userAnswer && ques.userAnswer.length > 0 ? 1 : 0;
      break;
    case QuesTypeEnum.fillBlank:
      answer = ques.userAnswer.map(item => item);
      ques.isAnswered = +(answer.filter(item => item && item.trim()).length > 0);
      break;
    case QuesTypeEnum.shortAnswer:
      // 问答题答案
      answer.push(ques.userAnswer);
      ques.isAnswered = ques.userAnswer ? 1 : 0;
      break;
    default:
      break;
  }
  return answer;
};

/**
 * 获取试题的分值描述
 */
export const getQuesScoreDesc = (ques) => {
  if (!ques) return '';
  const { type, missScoreType, missScore, totalScore } = ques;
  const score = totalScore || ques.score;
  // 其他题型分数
  if (type !== 1) return i18n.t('pc_ote_lbl_many_score', [score]);
  // 多选分数
  switch (missScoreType) {
    case 1:
    case 2:
      return i18n.t('pc_ote_lbl_many_score', [score]);
    case 0:
    default:
      return missScore ? i18n.t('pc_ote_lbl_total_miss_score', [score, missScore]) : i18n.t('pc_ote_lbl_total_miss_no_score', [score]);
  }
};
