import { oteApi } from '../../../api';

// 记录接口的 trackId
const trackIds = {};
const setApiTrackId = (apiKey, trackId) => {
  trackIds[apiKey] = trackId;
};

oteApi.interceptors.request.use(function(configOfApi) {
  // 给接口加上 trackId
  const trackId = trackIds['oteApi'];
  if (configOfApi.url && trackId) {
    const trackIdApiKey = 'trackId';
    if (configOfApi.params && typeof (configOfApi.params) === 'object') {
      configOfApi.params[trackIdApiKey] = trackId;
    } else {
      configOfApi.url += `${configOfApi.url.includes('?') ? '&' : '?'}${trackIdApiKey}=${trackId}`;
    }
  }
  return configOfApi;
}, function(error) {
  // 对请求错误做些什么
  return Promise.reject(error);
});

export {oteApi, setApiTrackId};
