import { commonUtil } from 'yxt-biz-pc';

export const getMobileDomain = commonUtil.getMobileDomain;
export const getShortUrl = commonUtil.getShortUrl;

/**
 * 获取当前机构的域名
 */
export const getDomain = commonUtil.getDomain;

/**
 * 获取地址信息
 * @param {string} h5Url h5地址
 * @param {string} pcUrl pc地址
 * @param {number} isv 是否匿名
 */
export const generateShortUrl = async(h5Url, pcUrl, isv = 0, isGroupProject, orgIdParam) => {
  try {
    const isAny = window.localStorage.isVisitor === '1';
    // 地址
    if (isAny) {
      // 游客特殊处理
      let { h5Domain } = await getDomain();
      if (h5Domain && h5Domain[h5Domain.length - 1] !== '/') h5Domain += '/';
      return {
        url: `${h5Domain}#/ote/${h5Url}`
      };
    } else {
      if (isGroupProject) {
        h5Url = addGroupTagOnUrl(h5Url);
        pcUrl = addGroupTagOnUrl(pcUrl);
      }
      const _h5Url = h5Url ? `/#/ote/${h5Url}` : '';
      const _pcUrl = pcUrl ? `/ote/#/${pcUrl}` : '';
      const shortUrl = await getShortUrl(_pcUrl, _h5Url, isv, orgIdParam);
      return { url: shortUrl };
    }
  } catch (error) {
    return { url: '' };
  }
};

/**
 * 地址上加上集团标记
 */
const addGroupTagOnUrl = (url) => {
  if (url) {
    url += `${url.includes('?') ? '&' : '?'}grouplink=1`;
  }
  return url;
};

/**
 * 企微对接的名称转换
 */
export const getOpenData = name => {
  return commonUtil.common.getOpenData(name);
};
