<template>
  <div
    id="watermark"
    v-loading="loading"
    class="yxtulcdsdk-user-exam box-size-borderbox"
  >
    <!-- 页头 -->
    <CommonSticky
      ref="stickyLeft"
      position="top"
      class="z-999"
      :by-class="deepStudy"
    >
      <div class="yxtulcdsdk-exam-header">
        <div
          class="flex flex-mid hand hover-primary-6"
          @click="backToPreview()"
        >
          <yxt-svg icon-class="quit" width="16px" height="16px" />
          <span class="ml8">{{ $t('pc_ote_btn_exit'/** 退出 */) }}</span>
        </div>
        <div class="flex flex-mid color-gray-8">
          <template v-if="forbidExit === 1">
            <!-- 允许切屏 -->
            <span>{{ $t('pc_ote_lbl_allowscreencut') }} <span class="yxtf-color-danger">{{ userExitCount }}</span>/{{ exitTimes }}</span>
            <yxtf-popover
              class="flex flex-mid"
              :content="$t('pc_ote_tip_anticheatmsg3')"
              placement="bottom-end"
              trigger="hover"
              :padding-small="true"
            >
              <yxt-svg
                slot="reference"
                class="ml4"
                icon-class="question-cirlce-o"
                width="16px"
                height="16px"
              />
            </yxtf-popover>
          </template>
        </div>
      </div>
    </CommonSticky>
    <div
      v-if="!loading"
      class="yxtulcdsdk-pc-marking-top-wrapper"
      :style="{
        minHeight: (getHeight() - 61) + 'px',
        maxWidth: '1248px'
      }"
    >
      <!-- 中间主体 -->
      <div
        class="yxtulcdsdk-pc-marking-main yxtulcdsdk-pc-special pt16 pb24 ph24"
        :class="{
          'img-no-point-event': allowPictureCopy === 0,
          'text-no-select': allowCharacterCopy === 0
        }"
      >
        <div
          class="yxtulcdsdk-review-fixed-content"
          :style="submitDisabled ? 'pointer-events:none;':''"
        >
          <div class="bg-white yxtbizf-br-4 ph20 pr pv16 yxtulcdsdk-common-shadow">
            <!-- 考试安排名称 -->
            <yxtf-tooltip
              :content="examName"
              placement="top"
              open-filter
              :max-width="780"
            >
              <div class="standard-size-20 yxt-weight-5 color-gray-10 text-center ph32 ellipsis">
                {{ examName }}
              </div>
            </yxtf-tooltip>
            <div class="yxtulcdsdk-info-list yxtulcdsdk-flex-mid standard-size-12 ph32">
              <div>
                <span>{{ $t('pc_ote_lbl_examinee' /** 考生 */) }}</span>
                <yxtbiz-user-name :name="baseInfo.fullname" /><template v-if="baseInfo.userName || baseInfo.username">（{{ baseInfo.userName || baseInfo.username }}）</template>
              </div>
              <div>
                <span>{{ $t('pc_ote_lbl_job_no' /** 工号 */) }}</span>
                <span>{{ baseInfo.userNo || '--' }}</span>
              </div>
              <div>
                <span>{{ $t('pc_ote_lbl_department' /** 部门 */) }}</span>
                <yxtbiz-dept-name :name="baseInfo.deptName || '--'" />
              </div>
            </div>
            <div class="yxtulcdsdk-info-list yxtulcdsdk-flex-mid standard-size-12 ph32">
              <div>
                <span>{{ $t('pc_ote_lbl_totalscore') }}</span>
                <span>{{ $t('pc_ote_lbl_many_score',[baseInfo.totalScore]/*{0}分*/) }}</span>
              </div>
              <div>
                <span>{{ $t('pc_ote_lbl_passscore') }}</span>
                <span>{{ $t('pc_ote_lbl_many_score',[baseInfo.allowViewResultPassed === 0 ? '*' : baseInfo.passScore]/*{0}分*/) }}</span>
              </div>
            </div>
          </div>
          <!-- 所有试题 -->
          <template v-for="(typeObj, indexType) in quesTypesList">
            <div v-if="typeObj.quesList && typeObj.quesList.length > 0" :key="indexType" class="bg-white yxtbizf-br-4 mt16 font-size-14">
              <div class="color-gray-10 pt20 pb4 ph24 yxt-weight-5 standard-size-14 font-size-14">
                {{ typeObj.title }}
                <span class="color-gray-7 font-size-12 font-normal">{{ typeObj.titleDesc }}</span>
              </div>
              <yxt-divider class="mt20 mb24" />
              <!-- 组合题题干 -->
              <user-ques-detail
                v-if="typeObj.ques && typeObj.ques.type === 5"
                class="mt16 yxtulcdsdk-pc-marking-title__margin ph24"
                :value="typeObj.ques"
              />
              <!-- 试题 -->
              <div class="pb24" :class="deepStudy ? 'ph32':'ph24'">
                <template v-for="(item, index) in typeObj.quesList">
                  <div :key="item.id + '_position'" :ref="item.id">
                  </div>
                  <!-- 单题展示 -->
                  <user-ques-detail
                    :key="item.id + '_ques'"
                    v-model="typeObj.quesList[index]"
                    :ue-id="ueId"
                    :class="typeObj.quesList && index !== typeObj.quesList.length - 1 ? 'mv24' : 'mt24'"
                    :index="index"
                    :allow-upload="allowUpload"
                    @submitSingle="submitSingle"
                    @recCurrentQues="recCurrentQues"
                    @markQues="markQues"
                    @file-click="fileClick"
                  />
                  <div v-show="typeObj.quesList && index !== typeObj.quesList.length - 1" :key="item.id + '_line'">
                    <yxt-divider />
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
      <!-- 答题卡 -->
      <CommonSticky
        ref="stickyRight"
        position="top"
        class="mr24 yxtulcdsdk-flex-shrink-0 mt16"
        :offset="60"
        :by-class="deepStudy"
        :style="getAnswerCardStyle()"
      >
        <div
          class="yxtulcdsdk-pc-right"
          :style="getAnswerCardStyle()"
        >
          <div
            class="yxtulcdsdk-answer-card"
            :style="getAnswerCardStyle()"
          >
            <div v-if="((usedTimesCtrl === 1 && usedTimesLen > 0) || (showRemaining && forceSubmitCtrl === 1)) && countdownFormatDate" class="yxtulcdsdk-answer-card__top ph16 flex flex-mid">
              <!-- 剩余时间 -->
              <span class="color-gray-8">{{ $t('pc_ote_lbl_timeremaining') }}</span>
              <span class="ml8 yxt-weight-5 font-size-18 lh20" :class="{'text-f52': true /*submitStatus === 1*/}">{{ countdownFormatDate }}</span>
            </div>
            <div class="yxtulcdsdk-answer-card__top ph16">
              <div v-if="allQuestions" class="flex flex-mid">
                <span class="color-gray-7 yxtulcdsdk-flex-shrink-0">{{ $t('pc_ote_lbl_progressnum') }}</span>
                <yxtf-progress
                  class="yxtulcdsdk-flex-shrink-1 ph8"
                  :percentage="(allQuestions.length - unSubmittedCount)/allQuestions.length*100"
                  :show-text="false"
                  :fit-width="true"
                />
                <span class="color-gray-8 yxtulcdsdk-flex-shrink-0"><span class="yxt-weight-5">{{ allQuestions.length - unSubmittedCount }}</span>/{{ allQuestions.length }}</span>
              </div>
            </div>
            <div class="yxtulcdsdk-answer-card__bottom color-gray-10">
              <div class="wp-100 text-left pl16 box-size-borderbox font-size-18 mb12 yxt-weight-5 lh26">{{ $t('pc_ote_lbl_answercard') }}</div>
              <div class="wp-100">
                <!-- 未做 -->
                <span class="d-in-block">
                  <span class="decorate-answer ml16 mr8"></span>
                  <span class="standard-size-12">{{ $t('pc_ote_tip_answerundo') }}</span>
                </span>
                <!-- 已做 -->
                <span class="d-in-block">
                  <span class="decorate-answer ml16 mr8 answered bg-primary-6-i border-primary-6-i"></span>
                  <span class="standard-size-12">{{ $t('pc_ote_tip_answerdone') }}</span>
                </span>
                <!-- 当前 -->
                <span class="d-in-block">
                  <span class="decorate-answer ml16 mr8 answering border-primary-6-i"></span>
                  <span class="standard-size-12">{{ $t('pc_ote_tip_answercurrent') }}</span>
                </span>
                <!-- 标记 -->
                <span class="d-in-block">
                  <span class="decorate-answer ml16 mr8">
                    <yxt-svg-icon
                      icon-class="answer-flag"
                      class-name="svgico-answer-flag color-red-6"
                      width="9px"
                      height="9px"
                    /></span>
                  <span class="standard-size-12">{{ $t('pc_ote_tip_markflag') }}</span>
                </span>
              </div>
              <yxt-divider class="mv16" />
              <div class="wp100 mheight48 flex flex-j-stretch over-auto yxtulcdsdk-flex-shrink-1">
                <yxt-scrollbar :fit-height="true" class="wp100" :style="{height: 'unset'}">
                  <!-- 题型列表 -->
                  <template v-for="(type, indexType) in quesTypesList">
                    <div
                      v-if="type.quesList && type.quesList.length > 0"
                      :key="indexType"
                      class="ph16"
                      :class="{
                        'mb12': indexType !== quesTypesList.length - 1
                      }"
                    >
                      <div class="ellipsis standard-size-14 mb16">
                        {{ type.title }}
                      </div>
                      <ul class="yxtulcdsdk-pc-card-list font-size-12 clearfix">
                        <!-- 题号列表 -->
                        <li
                          v-for="(item, index) in type.quesList"
                          :key="item.id"
                          class="hand uexam-answer-card"
                          :class=" {
                            'answering': currentQues.id === item.id,
                            'answered': item.isAnswered === 1
                          }"
                          @click="scrollView(item)"
                        >
                          {{ index + 1 }}
                          <yxt-svg-icon
                            v-if="item.isMarked === 1"
                            icon-class="answer-flag"
                            class-name="svgico-answer-flag color-red-6"
                            width="12px"
                            height="12px"
                          />
                        </li>
                      </ul>
                    </div>
                  </template>
                </yxt-scrollbar>
              </div>
            </div>
            <!-- 提交考试 -->
            <div class="yxtulcdsdk-answer-card__btn width-percent-100 lh40 font-size-14">
              <yxtf-button
                type="primary"
                size="larger"
                class="width-percent-100"
                :disabled="submitDisabled || submited"
                :loading="isInSubmiting"
                @click="submit(true)"
              >
                {{ submitText }}
              </yxtf-button>
            </div>
          </div>
        </div>
      </CommonSticky>
    </div>
    <!-- 切屏提示框 -->
    <yxtf-dialog
      :visible.sync="antiCheatDialog"
      width="400px"
      :cutline="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="flex flex-start color-gray-10 yxt-weight-5">
        <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
        <span class="ml16">
          {{ $t( 'pc_ote_tip_anticheat') /** 本场考试已经开启防作弊： */ }}
        </span>
      </div>
      <div class="ml36 mt12 ">
        <div class="color-gray-8">
          <LanguageSlot
            v-if="exitTimes > userExitCount"
            inline
            :lang-key="'pc_ote_msg_exitnumstips'"
          >
            <span slot="0" class="yxtf-color-danger"> {{ userExitCount }} </span>
            <span slot="1" class="yxtf-color-danger"> {{ exitTimes }} </span>
          </LanguageSlot>
          <span v-else>
            {{ $t(submitEndedFun ? 'pc_ote_tip_anticheatmsgsubmited' : 'pc_ote_tip_anticheatmsg5') /** 切屏次数达到上限，试卷已自动提交 / 切屏次数达到上限，试卷自动提交中 */ }}
          </span>
        </div>
        <div
          v-if="exitTimes > userExitCount"
          class="color-gray-8 standard-size-12 bg-gray-2 pv8 ph8 mt8"
        >
          {{ $t('pc_ote_tip_anticheatmsg3') /** 请勿点击考试页面最小化、退出按钮，切换应用软件、点击考试区域以外的任意操作 */ }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- 我知道了 -->
        <yxtf-button type="primary" @click="closeAntiCheatDialog()">{{ $t('pc_ote_btn_iknow') }}</yxtf-button>
      </span>
    </yxtf-dialog>
    <!-- 长时间不操作提示框 -->
    <yxtf-dialog
      :visible.sync="noOperateDialog"
      width="400px"
      :cutline="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="flex flex-start color-gray-10 yxt-weight-5">
        <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
        <span class="ml16">
          {{ $t( 'pc_ote_tip_anticheat') /** 本场考试已经开启防作弊： */ }}
        </span>
      </div>
      <div class="ml36 mt12 ">
        <div class="color-gray-8">
          <!-- 超过{{maxLeaveTime}}分钟没有滑动或点击屏幕，试卷自动提交中 -->
          <span v-if="noOperateSecs <= 0" v-html="$t('pc_ote_tip_anticheatmsg6', [maxLeaveTime])"></span>
          <LanguageSlot v-else-if="noOperateSecs <= 30" inline :lang-key="'pc_ote_tip_anticheatmsg7'">
            <span slot="0" class="yxtf-color-danger"> {{ maxLeaveTime }} </span>
            <span slot="1" class="yxtf-color-danger"> {{ noOperateSecs }} </span>
          </LanguageSlot>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <yxtf-button type="primary" @click="closeAutoHandConfirmTip()">{{ $t('pc_ote_btn_iknow') /** 我知道了 */ }}</yxtf-button>
      </span>
    </yxtf-dialog>
    <!-- 空题提示框 -->
    <yxtf-dialog
      :visible.sync="confirmUndoDialog"
      width="400px"
      :cutline="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="flex flex-start color-gray-10 yxt-weight-5">
        <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
        <span class="ml16">
          <!-- 该考试不允许空题，请全部作答完成后再提交 -->
          {{ $t('pc_ote_msg_examneedanswerall') }}
        </span>
      </div>
      <div class="ml36 mt12 color-gray-10 d-in-block">
        <!-- 未作答题目：{0} -->
        <span class="color-gray-8">{{ $t('pc_ote_lbl_examundonum', [unSubmittedCount]) }}</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <yxtf-button type="primary" @click="continueAnswer()">{{ $t('pc_ote_btn_continueanswer') /** 继续做答 */ }}</yxtf-button>
      </span>
    </yxtf-dialog>
    <!-- 离开提示框 -->
    <yxtf-dialog
      :visible.sync="confirmLeaveDialog"
      width="400px"
      :cutline="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <!-- 优先提示防切屏的 -->
      <div v-if="forbidExit === 1">
        <div class="flex flex-start color-gray-10 yxt-weight-5">
          <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
          <span class="ml16">
            <!-- 本场考试已开启防作弊，是否确定切换屏幕？   -->
            {{ $t(!isLastRemainingTime ? 'pc_ote_lbl_leaveconfirmexit' :'pc_ote_lbl_leaveconfirmexitnotime') }}
          </span>
        </div>
        <div class="ml36 mt12">
          <div v-if="!isLastRemainingTime" class="color-gray-8">
            <LanguageSlot inline :lang-key="'pc_ote_msg_exitnumstips'">
              <span slot="0" class="yxtf-color-danger"> {{ userExitCount }} </span>
              <span slot="1" class="yxtf-color-danger"> {{ exitTimes }} </span>
            </LanguageSlot>
          </div>
          <div
            class="color-gray-8"
            :class="{
              'standard-size-12 bg-gray-2 pv8 ph8 mt8': !isLastRemainingTime,
              'mt12': isLastRemainingTime
            }"
          >{{ $t('pc_ote_tip_anticheatmsg3') }}</div>
        </div>
      </div>
      <div v-else>
        <div class="flex flex-start color-gray-10 yxt-weight-5">
          <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
          <span class="ml16">
            <!-- 你还有 15 题未作答，确定要退出吗？   当前考试未交卷，是否确定退出？ -->
            <LanguageSlot
              inline
              :lang-key="unSubmittedCount > 0 ? 'pc_ote_lbl_leaveconfirmquesnummsg' :'pc_ote_lbl_leaveconfirmnotsubmit'"
              class="d-in-block"
            >
              <span slot="0" class="color-primary-6"> {{ unSubmittedCount }} </span>
            </LanguageSlot>
          </span>
        </div>
        <div v-if="usedTimesCtrl === 1 && usedTimesLen > 0 && baseInfo.isContainQuitTime" class="ml36 mt12 color-gray-10 d-in-block">
          <span class="color-gray-8">{{ $t('pc_ote_lbl_examtimetotal', [usedTimesLen/60]) }}</span>
          <span class="yxt-weight-5">{{ $t('pc_ote_lbl_leavetimeinclude') }}</span>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <yxtf-button v-if="forbidExit === 1 && isLastRemainingTime" type="primary" @click="confirmLeaved(false)">{{ $t('pc_ote_btn_iknow') /** 我知道了 */ }}</yxtf-button>
        <template v-else>
          <!-- 取消 -->
          <yxtf-button @click="confirmLeaved(false)">{{ $t('pc_ote_btn_cancel') }}</yxtf-button>
          <!-- 确定 -->
          <yxtf-button type="primary" @click="confirmLeaved(true)">{{ $t('pc_ote_btn_confirm') }}</yxtf-button>
        </template>
      </span>
    </yxtf-dialog>
    <!-- 提交提示框 -->
    <yxtf-dialog
      :visible.sync="confirmFinishDialog"
      width="400px"
      :cutline="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="flex flex-start color-gray-10 yxt-weight-5">
        <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
        <span class="ml16">
          <!-- 还有*道题未做，是否确认交卷？ -->
          <LanguageSlot inline lang-key="pc_ote_msg_existundoques" class="d-in-block">
            <span slot="0" class="color-primary-6"> {{ unSubmittedCount }} </span>
          </LanguageSlot>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- 取消 -->
        <yxtf-button @click="confirmFinish(false)">{{ $t('pc_ote_btn_cancel') }}</yxtf-button>
        <!-- 确定 -->
        <yxtf-button type="primary" @click="confirmFinish(true)">{{ $t('pc_ote_btn_confirm') }}</yxtf-button>
      </span>
    </yxtf-dialog>
    <!-- 提交完成 -->
    <submit-result :ue-id="ueId" :visible="submitedDialog" @end="examingEnd" />
    <!-- 水印 -->
    <yxtbiz-watermark
      v-if="allowAddWatermark && ~~watermarkConfig.enabled"
      class="z-999"
      :option="watermarkConfig"
      :show="true"
      app-code="ote"
      version="v2"
    />
  </div>
</template>
<script>
import * as examService from '../service/user.service';
import { mapActions } from 'vuex';
import { QUES_TYPE_NAMES, USER_EXAM_USED_TIME, USER_EXAM_ANSWER_INFO, USER_EXAM_QUES_MARK } from '../configs/const';
import { getQuesTypeIndexName, getScrollParent } from '../core/utils';
import UserQuesDetail from './question/UserQuesDetail';
import { getOrgWatermarkConfig } from '../service/config.service';
import deepStudyPage from '../mixins/deepStudyPage';
import YxtSvgIcon from './components/svgIcon.vue';
import CommonSticky from './components/CommonSticky.vue';
import submitResult from './examing/submitResult.vue';
import { LanguageSlot } from 'yxt-biz-pc';
import { MessageBox } from 'yxt-pc';
export default {
  components: {
    CommonSticky,
    UserQuesDetail,
    YxtSvgIcon,
    submitResult,
    LanguageSlot
  },
  mixins: [deepStudyPage],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      loading: true,
      antiCheatDialog: false,
      noOperateDialog: false,
      confirmLeaveDialog: false,
      submitedDialog: false,
      confirmUndoDialog: false,
      confirmFinishDialog: false,
      arrId: routeParams.arrId || routeParams.arrangeId, // 考试安排ID
      uemId: routeParams.uemId, // uemId
      masterId: routeParams.masterId, // 第三方考试来源ID
      masterType: routeParams.masterType, // 第三方考试来源类型 1-项目 2-课程包
      packageId: routeParams.packageId, // 课程包ID
      ueId: '', // 用户试卷ID
      uniqueId: '', // 唯一码（每次打开页面获取一个新的）
      baseInfo: {},
      avatar: '',
      arrName: '', // 考试安排名称
      examName: '', // 试卷名称
      fullname: '',
      submitText: this.$t('pc_ote_btn_submitexam'),
      submitDisabled: false,
      submitType: 0,
      submitStatus: 0, // 0-正常状态 1-即将提交
      allowUpload: 0, // 问答题是否允许上传附件
      allQuestions: [], // 所有的单题
      singleChoiceQues: [], // 单选题
      multiChoiceQues: [], // 多选题
      judgeQues: [], // 判断题
      fillQues: [], // 填空题
      ansQues: [], // 问答题
      combQuesDic: {}, // 组合题 字典
      combQuesSubListsDic: {}, // 组合题子题 字典
      quesTypesList: [], // 处理过的所有题型及内部的题目
      titleSettings: [], // 标题的设置
      allowAddWatermark: false, // 是否添加水印
      allowCharacterCopy: 0, // 是否允许字符复制
      allowPictureCopy: 0, // 是否允许图片复制
      watermarkConfig: {
        enabled: 1,
        type: 1,
        opacity: 80,
        text: ' ',
        fontSize: 30,
        color: '#000'
      },
      // 当前操作的试题
      currentQues: {
        id: '',
        type: -1,
        index: -1
      },
      // 单题提交请求体
      submitAnswerBody: {
        answers: [],
        isH5Exam: 0,
        submitType: 0 // 提交类型。0：主动提交；1：后台点击结束考试时的内存提交；2：自动提交（(用时已尽)；3：自动提交(统一交卷)；4：自动交卷（APP多次退出 或 PC端多次切屏）；5：自动提交，超过指定时间未操作考试页面
      },
      // 试题答案集合（用于提交试卷时与服务器缓存比对）
      // 单题提交请求体
      quesAnswerList: {
        answers: [],
        isH5Exam: 0,
        usedTime: 30
      },
      submitEndedFun: null,
      tempPendingList: [], // 等待提交的试题
      timer: '', // 定时器
      startResidueTimeStr: '', // 记录打开页面的倒计时
      startResidueTime: 0, // 刚进页面时,考试剩余时间
      residueTime: 0, // 用时倒计时  时间到自动提交
      usedTimesCtrl: 0, // 是否控制考试时长
      usedTimesLen: 0, // 考试时长（单位：秒）
      forceSubmitCtrl: 0,
      forceSubmitTime: '',
      elapsedTimeCounter: null, // 考试总有效时长（TODO: 几分钟不操作鼠标）
      perSubmitTime: 0, // 等待提交时长
      perSubmitTimeCounter: null, // 两分钟缓存时间提交计时器
      localTimeCounter: null, // 考试用时未提交时长计时器
      forbidExit: 0, // 是否允许切屏
      needAnswerAll: 0, // 是否需要做答全部题目
      exitTimes: 0, // 允许切屏次数
      userExitCount: 0, // 用户已退出次数
      autoHandSeconds: 0, // 页面不操作时限（单位：秒）
      noOperateSecs: 0, // 页面不操作时长（单位：秒）
      maxLeaveTime: 0, // 页面允许不操作时长（单位：分钟）
      quesTypeTitles: [], // 试卷题型标题集合
      typeTitles: {}, // 试卷题型标题
      quesMarkIdList: [], // 试题标记集合
      allowResubmitTimes: 3,
      resubmitTimes: 0,
      localAnswerInfo: {}, // 本地localStorage试题信息
      localAnswerQues: [], // 本地localStorage试题
      serverUserQues: [], // 服务器用户试卷试题信息（已作答）
      localExtraQues: [], // // 本地未存在于服务器的试题
      isInSubmiting: false, // 正在提交中
      submited: false, // 已提交
      fileClickStatus: false,
      isShowShortDialog: false // 是否最短答题时长展示了
    };
  },
  inject: ['getHeight', 'getWidth', 'getFSWidth'],
  created() {
    this.getUemInfo();
    getOrgWatermarkConfig()
      .then(config => {
        this.watermarkConfig = config;
      })
      .catch(err => this.handleError(err));
  },
  computed: {
    quesOfType() {
      return [
        this.singleChoiceQues,
        this.multiChoiceQues,
        this.judgeQues,
        this.fillQues,
        this.ansQues,
        this.combQuesSubListsDic
      ];
    },
    // 显示倒计时（针对统一交卷）
    showRemaining() {
      // 统一交卷时间是否在24小时内
      return this.forceSubmitCtrl === 1 && this.usedTimesCtrl === 0 && this.residueTime <= 24 * 60 * 60 * 1000;
    },
    // 毫秒级转hh:mm:ss倒计时
    countdownFormatDate() {
      let result = '';
      if (this.residueTime) {
        let hours = Math.floor(this.residueTime / (3600 * 1000));
        // 计算相差分钟数
        const leave2 = this.residueTime % (3600 * 1000); // 计算小时数后剩余的毫秒数
        let minutes = Math.floor(leave2 / (60 * 1000));
        // 计算相差秒数
        const leave3 = leave2 % (60 * 1000); // 计算分钟数后剩余的毫秒数
        let seconds = Math.round(leave3 / 1000);
        hours = parseInt(hours) > 9 ? parseInt(hours) : '0' + parseInt(hours);
        minutes = parseInt(minutes) > 9 ? parseInt(minutes) : '0' + parseInt(minutes);
        seconds = parseInt(seconds) > 9 ? parseInt(seconds) : '0' + parseInt(seconds);
        result = hours + ':' + minutes + ':' + seconds;
      }
      return result;
    },
    storageKey() {
      return USER_EXAM_QUES_MARK + this.ueId;
    },
    orgId() {
      return window.localStorage.getItem('orgId') || '';
    },
    // 未作答答题数
    unSubmittedCount() {
      let unSubmittedCount = 0;
      const tempAnswerList = this.allQuestions;
      // 获取当前页面所有已作答试题的试题答案
      if (tempAnswerList && tempAnswerList.length > 0) {
        let formerType = 0; // 上一题的题型
        let currType = 0; // 当前试题题型
        for (let i = 0; i < tempAnswerList.length; i++) {
          const e = tempAnswerList[i];
          if (e) {
            currType = e.type;
            // 每更换一次题型，index都从0开始计数
            if (formerType !== currType) {
              formerType = currType;
            }
            if (e.isAnswered !== 1) {
              unSubmittedCount++;
            }
          }
        }
      }
      return unSubmittedCount;
    },
    // 剩余切屏次数
    remainingTimes() {
      return this.exitTimes - this.userExitCount;
    },
    // 没有切屏机会了
    isLastRemainingTime() {
      return false ; // 暂时先不提示 this.remainingTimes <= 1;
    }
  },
  methods: {
    ...mapActions(['setTicket']),
    // 暂停所有音视频，依赖于mediaplayer里记录的当前播放中的媒体实例
    pauseAllMediaPlayer() {
      if (window.__MEDIA_PLAYER_ONPLAY__) {
        window.__MEDIA_PLAYER_ONPLAY__.pause && window.__MEDIA_PLAYER_ONPLAY__.pause();
      }
    },
    // 获取防作弊数字样式
    getTextHtml(textNum) {
      return '<span class="color-primary-6 yxt-weight-5">' + textNum + '</span>';
    },
    // 鼠标离开当前页面（切屏）
    addExitListener() {
      window.addEventListener('blur', this.addExistFunc);
      window.addEventListener('focus', this.backExistFunc);
    },
    backExistFunc() {
      this.cutScreenFixSto && clearTimeout(this.cutScreenFixSto);
    },
    addExistFunc() {
      if (this.fileClickStatus) {
        this.fileClickStatus = false;
        return;
      }

      this.cutScreenFixSto && clearTimeout(this.cutScreenFixSto);
      this.cutScreenFixSto = setTimeout(() => {
        if (this.submitDisabled) return;
        if (this.forbidExit === 0) return;
        // 记录切屏次数
        this.userExitCount++;
        this.showAntiCheatDialog();
        if (this.userExitCount >= this.exitTimes) {
          // 切屏次数以完成，提交答卷
          this.autoSubmit(4);
        } else {
          // 提交切屏操作
          this.saveExit();
        }
        this.pauseAllMediaPlayer();
      }, 500);
    },
    // 切屏提示框
    showAntiCheatDialog() {
      this.antiCheatDialog = true;
      // 去掉退出切屏确认，这两个提示比较相近会有困惑
      this.confirmLeaveDialog && this.confirmLeaved(false);
    },
    // 关闭切屏提示框
    closeAntiCheatDialog() {
      this.antiCheatDialog = false;
      // 同时关闭长时间未操作提示框
      this.closeAutoHandConfirmTip();

      if (this.submitEndedFun) {
        this.submitEndedFun();
      }
    },
    // 不操作页面时限
    addOperationListener() {
      this.operateSeconds();
      window.addEventListener('mousemove', this.resetSeconds);
      window.addEventListener('click', this.resetSeconds);
      window.addEventListener('mousewheel', this.resetSeconds);
      window.addEventListener('keyup', this.resetSeconds);
    },
    // 重置未操作读秒
    resetSeconds(flag) {
      if ((flag && flag === 1) || this.noOperateSecs >= 30) {
        this.noOperateSecs = this.autoHandSeconds;
      }
    },
    // 长时间未操作页面处理
    operateSeconds() {
      if (this.noOperateSecs <= 0) {
        // 长时间不操作页面，自动提交试卷
        this.autoSubmit(5);
      } else if (this.noOperateSecs === 30) {
        // 剩余时间小于30s，弹出提示框
        this.showAutoHandConfirmTip();
      }
    },
    // 长时间未操作提示框
    showAutoHandConfirmTip() {
      this.noOperateDialog = true;
      this.pauseAllMediaPlayer();
    },
    // 关闭长时间未操作提示框
    closeAutoHandConfirmTip() {
      this.noOperateDialog = false;
      this.resetSeconds(1);
    },
    // 自动提交试卷
    async autoSubmit(type) {
      this.submitDisabled = true;
      this.submitText = this.$t('pc_ote_msg_submitting');
      this.submitType = type; // 4-多次切屏提交 5-长时间未操作提交
      if (this.currentQues.type === 3 || this.currentQues.type === 4) {
        // 如果最后一题是填空题或问答题，针对最后一题做单题提交
        await this.submitSingle(this.currentQues);
      }
      this.submitUserExam();
    },
    // 提交切屏信息
    saveExit() {
      const reqBody = {
        remark: 'PC考试切屏',
        time: Date.parse(new Date()),
        type: 0,
        uniqueId: this.uniqueId,
        orgId: this.orgId
      };
      examService.saveExitInfo(this.ueId, reqBody).then(res => {
        // console.log('切屏次数提交成功')
      }).catch(err => {
        this.handleErrorMsg(err);
      });
    },
    // 获取考试信息
    getUemInfo() {
      const lclTime = this.checkLocalUsedTime();
      examService.getUemStartInfo(this.uemId, {
        arrangeId: this.arrId,
        usedTimes: lclTime,
        uemSignId: this.routeParams.signId || '',
        batchId: this.routeParams.batchId, // 循环考试批次ID
        thirdBatchId: this.routeParams.btid, // 在线课堂批次ID
        orgId: this.orgId
      }).then(res => {
        this.clearLocalUsedTime();
        this.setTicket(res.ticket);
        this.fullname = res.fullname;
        this.avatar = res.avatar;
        this.ueId = res.ueId; // 用户试卷ID
        this.uniqueId = res.uniqueId; // 唯一码
        this.arrName = res.arrName; // 考试安排名称
        this.examName = res.examName; // 试卷名称
        this.allowUpload = res.allowUpload; // 问答题是否允许上传附件
        this.loadQuesList(res.userQues); // 加载页面试题
        this.forbidExit = res.forbidExit;
        this.exitTimes = res.exitTimes;
        this.userExitCount = res.userExitCount;
        this.maxLeaveTime = res.maxLeaveTime;
        this.allowAddWatermark = !!res.allowAddWatermark;
        this.allowCharacterCopy = res.allowCharacterCopy;
        this.allowPictureCopy = res.allowPictureCopy;
        this.baseInfo = res;

        // 页面初始化倒计时处理
        this.getInitAndLatestTime(res);
        // 页面计时
        this.timing();
        // 是否开启防作弊
        if (res.forbidExit === 1 && res.exitTimes > 0) {
          if (res.userExitCount < res.exitTimes) {
            // 1.切屏检查开启
            this.addExitListener();
          } else {
            // 2.切屏次数已达上限
            this.autoSubmit(4);
          }
        }
        if (res.maxLeaveTime > 0) {
          // 2.页面不操作时限
          this.autoHandSeconds = res.maxLeaveTime * 60;
          this.noOperateSecs = res.maxLeaveTime * 60;
          this.addOperationListener();
        }
        // 生成试卷题型标题
        this.titleSettings = res.quesTypeTitles;
        this.generateQuesTitle();
        this.loading = false;

        this.$emit('updateProgress', 1); // 学习中
        this.reportYxtLog(110, '用户进入考试', {w_succ: 1});
      }).catch((err) => {
        this.handleErrorMsg(err, true);
      });
    },
    // 检查本地有没有未提交的用时
    checkLocalUsedTime() {
      let lclTime = 0;
      const lclTimeStr = window.localStorage.getItem(USER_EXAM_USED_TIME);
      if (lclTimeStr) {
        const lclTimeArr = lclTimeStr.split('|');
        if (lclTimeArr.length === 2 && lclTimeArr[0] === this.uemId) {
          lclTime = lclTimeArr[1];
        }
      }
      return lclTime;
    },
    // 清空本地未提交用时
    clearLocalUsedTime() {
      window.localStorage.setItem(USER_EXAM_USED_TIME, '');
    },
    // 处理错误Key
    handleErrorMsg(err, init) {
      if (err && err.key) {
        if (err.key === 'apis.ote.userexam.NotSingleTest' ||
        err.key === 'apis.ote.userexam.validation.TimeLimited' ||
        err.key === 'apis.ote.examinee.NotChangeDevice' ||
        err.key === 'apis.ote.userexam.validation.TimesLimited') {
          // 页面多开，回到考试预览页面
          // 此次考试有时间限制
          // 不允许切换设备，回到考试预览页面
          // 考试次数已用完，回到考试预览页面
          this.backToPreview(true);
        } else if (err.key === 'apis.ote.userexam.Submitted') {
          // 试卷已提交，跳转到提交成功页面
          this.linkToSubmitResult();
          // this.submitedDialog = true;
        } else if (err.key === 'apis.ote.user.exam.NotFound' ||
          err.key === 'apis.ote.arrangement.NotFound' ||
          err.key === 'apis.ote.userexam.removed') {
          // 当前用户试卷已被删除
          // 考试安排不存在
          // 跳转错误页面
          // 考生被移除
          this.handlerPublicError(err);
        } else if (err.key === 'apis.ote.arrangement.Ended') {
          // 考试安排已结束
          this.backToPreview(true);
        } else if (err.key === 'apis.ote.ue.exam.sync.reset') {
          // 清空作答，提示重新打开
          !this.syncResetShowed && this.$confirm('', this.$t('pc_ote_msg_examupdated'), {
            confirmButtonText: this.$t('pc_ote_btn_confirm'),
            type: 'warning',
            showCancelButton: false
          }).finally(() => {
            // windowClose()
            this.backToPreview(true, true);
          });
          this.syncResetShowed = true;
        } else if (err.key === 'apis.ote.arrangement.ip.changed') {
          this.$confirm(this.$t('pc_ote_lbl_change_ip_messege' /* 您所参加的考试更换了IP地址，请您切换IP后继续考试，点击确定后将关闭考试页面！ */), this.$t('pc_ote_msg_tip' /* 提示 */), {
            confirmButtonText: this.$t('pc_ote_btn_confirm' /* 确定 */),
            cancelButtonText: this.$t('pc_ote_btn_cancel' /* 取消 */),
            type: 'info'
          }).then(() => {
            this.backToPreview(true);
          }).catch(() => {});
        } else if (err.key === 'apis.ote.userexam.all.unfinished') {
          this.confirmUndoDialog = true;
        } else {
          this.handleError(err);
        }
      } else if (err && err.message) {
        this.handleError(err);
      } else {
        if (!err || err.message === undefined || !navigator.onLine) {
          this.$message({
            type: 'error',
            message: this.$t('pc_ote_msg_nonet') // 网络已断开或已超时
          });
        } else {
          this.$message({
            type: 'error',
            message: this.$t('pc_ote_msg_submitfail') // 提交失败！
          });
        }
      }
    },
    // 回到考试预览
    backToPreview(isNotConfirm, isRedo) {
      if (!isNotConfirm) {
        this.confirmLeave();
      } else {
        this.closeShortTimeDialog();
        this.$emit('changeStep', this.UserExamStep.preview,
          {
            trackId: this.routeParams.trackId,
            arrangeId: this.arrId,
            uemId: isRedo ? undefined : this.uemId,
            batchId: this.routeParams.batchId, // 循环考试批次ID
            btid: this.routeParams.btid, // 在线课堂批次ID
            gwnlUrl: this.routeParams.gwnlUrl, // 人才发展url进行面包屑点击回退
            redo: 1
          }, true);
      }
    },
    // 单题提交
    submitSingle(ques) {
      if (this.submited) return;
      this.currentQues = ques;
      const answerList = [];
      const answer = this.getSubmitAnswer(ques);
      if (answer) {
        answerList.push(answer);
      }
      this.submitAnswerBody.uniqueId = this.uniqueId;
      this.submitAnswerBody.submitType = 0;
      this.submitAnswerBody.answers = answerList;
      this.submitAnswer(ques);
    },
    // 提交
    submitAnswer(ques) {
      this.submitAnswerBody.uniqueId = this.uniqueId;
      this.submitAnswerBody.orgId = this.orgId;
      this.submitAnswerBody.arrangeId = this.arrId;
      this.submitAnswerBody.ueId = this.ueId;
      this.submitAnswerBody.uemId = this.uemId;
      examService.postSingleAnswer(this.submitAnswerBody).then(res => {
        // 是否开启防作弊 && 原来没开启放作弊
        if (res.forbidExit === 1 && res.exitTimes > 0 && this.forbidExit === 0) {
          if (this.userExitCount < res.exitTimes) {
            // 1.切屏检查开启
            this.addExitListener();
            // 显示放切屏信息
            this.showAntiCheatDialog();
          } else {
            // 2.切屏次数已达上限
            // 显示放切屏信息
            this.showAntiCheatDialog();
            setTimeout(() => {
              this.autoSubmit(4);
            }, 500);
          }
        } else if (res.forbidExit === 1 && res.exitTimes > 0 && this.forbidExit === 1) {
          // 是否开启防作弊 && 原来开启放作弊
          if (this.userExitCount >= res.exitTimes) {
            // 2.切屏次数已达上限
            // 显示放切屏信息
            this.showAntiCheatDialog();
            setTimeout(() => {
              this.autoSubmit(4);
            }, 500);
          }
        }
        // 实时获取是否开启切屏和允许切屏次数
        this.forbidExit = res.forbidExit;
        this.exitTimes = res.exitTimes;
      }).catch(err => {
        this.handleErrorMsg(err);

        // 考生被移除不执行下列代码
        if (err && err.key && err.key === 'apis.ote.userexam.removed') return;
        // 提交失败，将试题答案存入临时用户试题答案
        if (this.submitAnswerBody && this.submitAnswerBody.answers && this.submitAnswerBody.answers.length > 0) {
          let isExistLocal = false;
          for (let i = 0; i < this.localAnswerQues.length; i++) {
            if (this.localAnswerQues[i].quesId === ques.id) {
              // 更新临时试题答案库
              this.localAnswerQues[i] = this.submitAnswerBody.answers[0];
              isExistLocal = true;
              break;
            }
          }
          // 如果临时答案库不存在此试题，则新增
          if (!isExistLocal) {
            this.localAnswerQues.push(this.submitAnswerBody.answers[0]);
          }
          this.localAnswerInfo = {
            ueId: this.ueId,
            ques: this.localAnswerQues,
            date: new Date().formatDate('yyyy-MM-dd HH:mm:ss')
          };
          window.localStorage.setItem(USER_EXAM_ANSWER_INFO, JSON.stringify(this.localAnswerInfo));
        }
      });
    },
    // 获取单题提交请求体
    getSubmitAnswer(ques) {
      const answerBody = {};
      answerBody.quesId = ques.id;
      answerBody.quesType = ques.type;
      answerBody.lastSubmitTime = new Date().getTime();
      let answer = [];
      const attach = [];
      switch (ques.type) {
        case 0:
          answer.push(ques.answer);
          ques.isAnswered = 1;
          break;
        case 1:
          answer = ques.answers;
          ques.isAnswered = answer && answer.length > 0 ? 1 : 0;
          break;
        case 2:
          answer.push(ques.submitContent);
          ques.isAnswered = 1;
          break;
        case 3:
          let isAns = 0;
          ques.fillInItems.forEach(function(e) {
            if (e) {
              // 默认存下填空题的所有空（填空题批阅会考虑填空项的顺序）
              answer.push(e.submitAnswer ? e.submitAnswer : '');
              if (e.submitAnswer && e.submitAnswer.length > 0) {
                isAns = 1;
              }
            }
          });
          // 如果一个空都没有填，把answer置空
          if (isAns !== 1) {
            answer = [];
          }
          ques.isAnswered = isAns;
          break;
        case 4:
          ques.attach.forEach(function(e) {
            attach.push({
              fileId: e.fileId,
              fileName: e.fileName,
              status: e.status ? e.status : 0
            });
          });
          // 问答题附件
          answerBody.attach = attach;
          // 问答题答案
          answer.push(ques.submitContent);
          ques.isAnswered = (ques.submitContent || (attach && attach.length > 0)) ? 1 : 0;
          break;
        default:
          break;
      }
      answerBody.answer = answer;
      return answerBody;
    },
    // 提交用户试卷信息
    submitUserExam() {
      if (this.submited) return;
      this.reportYxtLog(111, '用户提交考试', {
        c1: JSON.stringify({
          arrId: this.arrId,
          submitType: this.submitType
        })
      });
      if (this.resubmitTimes > this.allowResubmitTimes) {
        this.submitDisabled = true;
        this.submitText = this.$t('pc_ote_msg_submitfail');
        return;
      }
      this.resubmitTimes++;
      // 与本地quesAnswerList比对，如果比对成功提交试卷，如果不成功，提交本地答案
      examService.getUserAnswerCache(this.ueId, this.orgId).then(res => {
        try {
          if (res && res.length > 0) {
            const serverList = res;
            this.tempPendingList = [];
            if (this.compareAnswerList(serverList)) {
              // 没有需要提交的试题，可以直接提交试卷
              this.submitPaper();
            } else {
              // 需要提交不一样的
              this.submitAnswerBody.answers = this.tempPendingList;
              this.submitAnswerBody.uniqueId = this.uniqueId;
              this.submitAnswerBody.orgId = this.orgId;
              examService.submitSingleAnswer(this.ueId, this.submitAnswerBody).then(() => {
                this.submitUserExam();
              }).catch(error => {
                this.isInSubmiting = false;
                this.handleError(error);
              });
            }
          } else {
            this.submitPaper();
          }
        } catch (e) {
          this.$message({
            type: 'error',
            message: this.$t('pc_ote_msg_papersubmitfail')
          });
        }
      }).catch(err => {
        this.isInSubmiting = false;
        this.handleErrorMsg(err);
      });
    },
    // 提交异常试题
    submitFailAnswer() {
      this.submitAnswerBody.uniqueId = this.uniqueId;
      this.submitAnswerBody.orgId = this.orgId;
      examService.submitSingleAnswer(this.ueId, this.submitAnswerBody).then(res => {
        if (this.submitTimes < 3) {
          this.submitPaper();
          this.submitTimes = 3;
        }
      }).catch(this.handleError);
    },
    // 跳转到提交成功页面
    linkToSubmitResult() {
      const queryUrl = {
        trackId: this.routeParams.trackId,
        arrangeId: this.arrId,
        uemId: this.uemId,
        ueId: this.ueId,
        batchId: this.routeParams.batchId, // 循环考试批次ID
        btid: this.routeParams.btid, // 在线课堂批次ID
        gwnlUrl: this.routeParams.gwnlUrl // 人才发展url进行面包屑点击回退
      };
      // 第三方考试来源ID
      if (this.masterId) {
        queryUrl.masterId = this.masterId;
      }
      // 第三方考试来源类型
      if (this.masterType) {
        queryUrl.masterType = this.masterType;
      }
      // 课程包ID
      if (this.packageId) {
        queryUrl.packageId = this.packageId;
      }

      this.closeShortTimeDialog();
      this.$emit('changeStep', this.UserExamStep.result, queryUrl, true);
    },
    examingEnd(isToResult, isFinished) {
      // 通知使用方已经完成了
      isFinished && this.$emit('updateProgress', 2);
      this.submitEndedFun = () => {
        if (isToResult) {
          this.linkToSubmitResult();
        } else {
          this.backToPreview(true);
        }
      };

      // this.antiCheatDialog = true;  提示切屏的时候，需要点我知道了再跳走
      if (this.antiCheatDialog && isToResult) {
        return;
      }
      this.submitEndedFun();
    },
    // 离开前的确认
    confirmLeave() {
      this.confirmLeaveDialog = true;
    },
    // 离开前的确认后
    confirmLeaved(leave) {
      this.confirmLeaveDialog = false;
      if (this.leaveCB) {
        // 沉浸式的时候告知播放器是否离开
        this.leaveCB(leave);
        this.leaveCB = undefined;
      } else if (leave) {
        this.backToPreview(true);
      }
    },
    // 离开前的确认
    confirmFinish(finish) {
      this.confirmFinishDialog = false;
      if (finish) {
        this.submitUserExam();
      } else {
        this.isInSubmiting = false;
      }
    },
    // 沉浸式外部主动离开的处理
    confirmLeaveStudy(cb) {
      cb = cb || (() =>{}); // 标记外部离开的
      this.leaveCB = cb;
      this.confirmLeave();
    },
    continueAnswer() {
      // 需要定位到第一道没做的题目
      // 打乱题序时allQuestions会出现题型的乱序，不能直接用
      let tempAnswerList = [];
      this.quesTypesList.forEach((e) => {
        tempAnswerList = [...tempAnswerList, ...e.quesList];
      });
      // 获取当前页面所有已作答试题的试题答案
      if (tempAnswerList && tempAnswerList.length > 0) {
        let formerType = 0; // 上一题的题型
        let currType = 0; // 当前试题题型
        for (let i = 0; i < tempAnswerList.length; i++) {
          const e = tempAnswerList[i];
          if (e) {
            currType = e.type;
            // 每更换一次题型，index都从0开始计数
            if (formerType !== currType) {
              formerType = currType;
            }
            if (e.isAnswered !== 1) {
              this.scrollView(e);
              this.confirmUndoDialog = false;
              return;
            }
          }
        }
      }
    },
    // 提交试卷
    submitPaper() {
      const reqBody = {};
      reqBody.isH5Exam = 0;
      reqBody.submitType = this.submitType;
      reqBody.uniqueId = this.uniqueId;
      reqBody.usedTime = this.perSubmitTime;
      reqBody.orgId = this.orgId;
      examService.submitPaper(this.ueId, reqBody).then(res => {
        this.isInSubmiting = false;
        this.$message({
          type: 'success',
          message: this.$t('pc_ote_msg_papersubmitsuccess')
        });
        this.clearMarkStore();
        this.clearLocalUsedTime();
        // this.linkToSubmitResult();
        setTimeout(() => {
          this.submitedDialog = true;
        }, 500);
        this.clearPage();
        this.submited = true;
        this.submitText = this.$t('pc_ote_lbl_submitted');
        this.reportYxtLog(111, '用户提交考试', {
          w_succ: 1,
          c1: JSON.stringify({
            arrId: this.arrId,
            submitType: this.submitType
          })
        });
      }).catch(err => {
        this.isInSubmiting = false;
        // if (err.key) {
        this.handleErrorMsg(err);
        // } else {
        //   this.$message({
        //     type: 'error',
        //     message: this.$t('pc_ote_msg_papersubmitfail')
        //   });
        // }
      });
    },

    // 显示最短答题时长dialog
    showShortTimeDialog(leftShortTime) {
      const shortMinTime = Math.ceil(this.baseInfo.shortUsedTimesLen / 60);
      const minTime = Math.ceil(Math.abs(leftShortTime) / 60);
      const shortMinTimeStr = `<span class="color-primary-6 weight-bold"> ${shortMinTime} </span>`;
      const minTimeStr = `<span class="color-primary-6 weight-bold"> ${minTime} </span>`;
      this.isShowShortDialog = true;
      // 该考试设置了“最短答题时长”，需要答题满{0}分钟才能交卷，请{1}分钟后再进行交卷。
      MessageBox.confirm(this.$t('pc_ulcd_ote_tips_submit_short_time', [shortMinTimeStr, minTimeStr]), this.$t('pc_ote_msg_tip' /* 提示 */), {
        type: 'info',
        showCancelButton: false,
        dangerouslyUseHTMLString: true,
        confirmButtonText: this.$t('pc_ote_btn_iknow')
      }).then(() => {
        this.isShowShortDialog = false;
      }).catch(() => {
        this.isShowShortDialog = false;
      });
    },

    // 关闭最短答题时长，如果未关闭的话，返回或者去其他页面
    closeShortTimeDialog() {
      this.isShowShortDialog && MessageBox.close();
    },

    // 获取最新的考试设置
    getExamNewSetting(cb) {
      examService.getExamNewInfo(this.arrId).then(res => {
        this.baseInfo.allowSubmitAllFinish = res.allowSubmitAllFinish;
        this.baseInfo.shortUsedTimesCtrl = res.shortUsedTimesCtrl;
        this.baseInfo.shortUsedTimesLen = res.shortUsedTimesLen;
      }).finally(() => {
        cb && cb();
      });
    },
    async submit(isUser, newSetting) {
      if (isUser && !newSetting) {
        this.getExamNewSetting(() => {
          this.submit(isUser, true);
        });
        return;
      }

      if (isUser && this.baseInfo.shortUsedTimesCtrl) {
        const leftShortTime = ~~this.baseInfo.usedTimes + this.perSubmitTime - this.baseInfo.shortUsedTimesLen;
        if (leftShortTime < 0) {
          this.showShortTimeDialog(leftShortTime);
          return;
        }
      }

      // 空题判断
      if (isUser && this.baseInfo.allowSubmitAllFinish && this.unSubmittedCount > 0) {
        this.confirmUndoDialog = true;
        return;
      }
      if (this.isInSubmiting) return;
      this.isInSubmiting = true;
      try {
        if (this.currentQues.type === 3 || this.currentQues.type === 4) {
          // 如果最后一题是填空题或问答题，针对最后一题做单题提交
          await this.submitSingle(this.currentQues);
        }
        this.submitExam();
      } catch (err) {
        this.isInSubmiting = false;
        this.handleCatchError(err);
      }
    },
    // 提交试卷
    submitExam() {
      const unSubmittedCount = this.getAnswersAndUnSubmittedCount();
      if (unSubmittedCount > 0 && this.submitType === 0) {
        this.confirmFinishDialog = true;
      } else {
        this.submitUserExam();
      }
    },
    // 获取未作答的试题数量
    getAnswersAndUnSubmittedCount() {
      const answers = [];
      let unSubmittedCount = 0;
      const tempAnswerList = this.allQuestions;
      // 获取当前页面所有已作答试题的试题答案
      if (tempAnswerList && tempAnswerList.length > 0) {
        let formerType = 0; // 上一题的题型
        let currType = 0; // 当前试题题型
        for (let i = 0; i < tempAnswerList.length; i++) {
          const e = tempAnswerList[i];
          if (e) {
            currType = e.type;
            // 每更换一次题型，index都从0开始计数
            if (formerType !== currType) {
              formerType = currType;
            }
            // 如果当前试题已作答，纳入请求体
            if (e.isAnswered === 1) {
              answers.push(this.getSubmitAnswer(e));
            } else {
              unSubmittedCount++;
            }
          }
        }
      }
      this.quesAnswerList.answers = answers;
      return unSubmittedCount;
    },
    // 获取本地存储的试题信息
    getLocalAnswerInfo() {
      const storeStr = window.localStorage.getItem(USER_EXAM_ANSWER_INFO);
      if (storeStr) {
        try {
          const storeInfo = JSON.parse(storeStr);
          // 获得当前用户试卷试题答案
          if (storeInfo && storeInfo.ueId === this.ueId) {
            this.localAnswerInfo = storeInfo;
            this.localAnswerQues = storeInfo.ques;
            // 比对答案
            this.mergeLocalAnswerList();
          }
        } catch (error) {}
      }
    },
    // 合并本地与服务器用户答案
    mergeLocalAnswerList() {
      const lclQues = this.localAnswerQues;
      const serQues = this.serverUserQues;
      for (let i = 0; i < lclQues.length; i++) {
        let isExistLcl = false;
        for (let j = 0; j < serQues.length; j++) {
          if (lclQues[i].quesId === serQues[j].id) {
            isExistLcl = true;
            break;
          }
        }
        // 在本地答案中未找到该服务器答案
        if (!isExistLcl) {
          // 加入队列，后续提交至服务器；并将答案同步到该试题，用于页面呈现
          this.localExtraQues.push(lclQues[i]);
        }
      }
      if (this.localExtraQues.length > 0) {
        this.syncUserQuesAnswer();
        // 重新提交试题
        this.submitAnswerBody = {
          answers: this.localExtraQues,
          isH5Exam: 0,
          submitType: 0,
          uniqueId: this.uniqueId
        };
        this.submitFailAnswer();
      }
    },
    // 将本地答案同步到试题中
    syncUserQuesAnswer() {
      for (let i = 0; i < this.localExtraQues.length; i++) {
        const e = this.localExtraQues[i];
        for (let index = 0; index < this.allQuestions.length; index++) {
          try {
            if (this.allQuestions[index].id === e.quesId) {
              const ques = this.allQuestions[index];
              switch (ques.type) {
                case 0:
                // 单选题
                  ques.answer = e.answer[0];
                  // 选中单选题选项
                  for (let k = 0; k < ques.choiceItems.length; k++) {
                    if (e.answer.includes(ques.choiceItems[k].id)) {
                      ques.choiceItems[k].selected = 1;
                    }
                  }
                  // 已作答标记
                  ques.isAnswered = 1;
                  break;
                case 1:
                // 多选题
                  ques.answers = e.answer;
                  // 选中多选题选项
                  for (let k = 0; k < ques.choiceItems.length; k++) {
                    if (e.answer.includes(ques.choiceItems[k].id)) {
                      ques.choiceItems[k].selected = 1;
                    }
                  }
                  // 已作答标记
                  if (e.answer.length > 0) {
                    ques.isAnswered = 1;
                  }
                  break;
                case 2:
                // 判断题
                  ques.submitContent = e.answer[0];
                  ques.isAnswered = 1;
                  break;
                case 3:
                // 填空题
                  for (let k = 0; k < e.answer.length; k++) {
                    for (let m = 0; m < ques.fillInItems.length; m++) {
                      if (m === k) {
                        ques.fillInItems[m].submitAnswer = e.answer[k];
                      }
                    }
                  }
                  // 已作答标记
                  ques.isAnswered = e.answer.length > 0 ? 1 : 0;
                  break;
                case 4:
                // 问答题
                  if (e.answer && e.answer.length > 0) {
                    ques.submitContent = e.answer[0];
                    ques.isAnswered = 1;
                  } else {
                    ques.isAnswered = 0;
                  }
                  break;
                default:
                  break;
              }
              break;
            }
          } catch (error) {
            // TODO 可以给用户提示，检查历史作答
          }
        }
      }
      this.$forceUpdate();
    },
    // 加载各题型试题（并判断试题是否已作答）
    loadQuesList(quesList) {
      this.initMarkStore();
      for (let i = 0; i < quesList.length; i++) {
        const e = quesList[i];
        if (!e) {
          continue;
        }
        switch (e.type) {
          case 0:
            this.dealQuestionInfo(e);
            this.singleChoiceQues.push(e);
            break;
          case 1:
            this.dealQuestionInfo(e);
            this.multiChoiceQues.push(e);
            break;
          case 2:
            this.dealQuestionInfo(e);
            this.judgeQues.push(e);
            break;
          case 3:
            this.dealQuestionInfo(e);
            this.fillQues.push(e);
            break;
          case 4:
            this.dealQuestionInfo(e);
            this.ansQues.push(e);
            break;
          case 5:
            // 处理子题
            e.subQuesList.forEach((ques) => {
              this.dealQuestionInfo(ques);
            });
            this.combQuesSubListsDic[e.examQuesId] = e.subQuesList;
            this.combQuesDic[e.examQuesId] = e;
            break;
          default:
            break;
        }
      }
      this.getLocalAnswerInfo();
    },
    // 处理服务器给的题目信息
    dealQuestionInfo(ques) {
      // 标记
      ques.isMarked = this.quesMarkIdList.includes(ques.id) ? 1 : 0;
      switch (ques.type) {
        case 0:
          ques.answer = this.getSingleAnswer(ques.choiceItems);
          ques.isAnswered = ques.answer ? 1 : 0;
          break;
        case 1:
          ques.answers = this.getMultiAnswer(ques.choiceItems);
          ques.isAnswered = (ques.answers && ques.answers.length > 0) ? 1 : 0;
          break;
        case 2:
          ques.submitContent = this.getJudgeAnswer(ques.submitContent);
          ques.isAnswered = (ques.submitContent === '0' || ques.submitContent === '1') ? 1 : 0;
          break;
        case 3:
          ques.isAnswered = this.getFillAnswer(ques.fillInItems);
          break;
        case 4:
          const files = [];
          ques.uploadDisabled = 1;
          if (ques.attachments && ques.attachments.length > 0) {
            ques.attachments.forEach(function(f) {
              files.push({
                fileId: f.fileId,
                fileName: f.fileName,
                name: f.fileName,
                status: f.status
              });
            });
          }
          if (!ques.attachments || ques.attachments.length < 5) {
            ques.uploadDisabled = 0;
          }
          ques.attach = files;
          ques.isAnswered = (ques.submitContent || (ques.attach && ques.attach.length > 0)) ? 1 : 0;
          break;
        default:
          break;
      }
      if (ques.isAnswered === 1) {
        this.serverUserQues.push(ques);
      }
      this.allQuestions.push(ques);
    },
    // 获取单选题已选答案
    getSingleAnswer(choiceItems) {
      let answer = '';
      if (choiceItems && choiceItems.length > 0) {
        choiceItems.forEach(e => {
          if (e.selected === 1) {
            answer = e.id;
          }
        });
      }
      return answer;
    },
    // 获取多选题已选答案
    getMultiAnswer(choiceItems) {
      const answers = [];
      if (choiceItems && choiceItems.length > 0) {
        choiceItems.forEach(e => {
          if (e.selected === 1) {
            answers.push(e.id);
          }
        });
      }
      return answers;
    },
    // 获取判断题已选答案
    getJudgeAnswer(submitContent) {
      let answer = '-1';
      if (submitContent && submitContent === '0') {
        answer = '0';
      } else if (submitContent && submitContent === '1') {
        answer = '1';
      }
      return answer;
    },
    // 获取填空题已填项，以显示答题卡选中效果
    getFillAnswer(fillInItems) {
      let flag = 0;
      fillInItems.forEach(function(e) {
        if (e.submitAnswer && e.submitAnswer.length > 0) {
          flag = 1;
        }
      });
      return flag;
    },
    // 定位试题
    scrollView(ques) {
      const quesEl = this.$refs[ques.id] && this.$refs[ques.id][0];
      if (quesEl) {
        const scroller = getScrollParent(quesEl, true);
        scroller.scrollTop = quesEl.offsetTop;
      }

      this.currentQues = ques;
    },
    // 定位当前试题
    recCurrentQues(ques) {
      this.currentQues = ques;
    },
    // 标记
    markQues(ques) {
      const isMarked = (!ques.isMarked || ques.isMarked === 0) ? 1 : 0;
      ques.isMarked = isMarked;
      this.updateMarkStore(ques.id, isMarked);
    },
    // 比对服务器缓存答案List与本地quesAnswerList
    compareAnswerList(serverList) {
      // let tempPendingList = [] // 与服务器缓存比对不通过的试题
      const localAnswerList = this.quesAnswerList.answers;
      for (let i = 0; i < localAnswerList.length; i++) {
        let isMatch = false;
        for (let j = 0; j < serverList.length; j++) {
          if (localAnswerList[i].quesId === serverList[j].quesId) {
            // 匹配到试题，比对答案项
            isMatch = this.compareAnswerItem(localAnswerList[i], serverList[j]);
            // 跳出子循环
            break;
          }
        }
        // 如果本地答案在缓存中不存在，则进行更新到服务器操作
        if (!isMatch) {
          this.tempPendingList.push(localAnswerList[i]);
        }
      }
      return !(this.tempPendingList && this.tempPendingList.length > 0);
    },
    // 对比选项和答案项/附件
    compareAnswerItem(locQues, sevQues) {
      let flag = true;
      if ((locQues.quesType === 0 && sevQues.quesType === 0) || (locQues.quesType === 2 && sevQues.quesType === 2)) {
        // 单选题、判断题
        if (!(locQues.answer[0] && sevQues.answer[0] && locQues.answer[0] === sevQues.answer[0])) {
          flag = false;
        }
      } else if ((locQues.quesType === 1 && sevQues.quesType === 1) || (locQues.quesType === 3 && sevQues.quesType === 3)) {
        // 多选题、填空题
        if (locQues.answer && sevQues.answer && locQues.answer.length === sevQues.answer.length) {
          for (let i = 0; i < locQues.answer.length; i++) {
            if (locQues.answer[i] !== sevQues.answer[i]) {
              flag = false;
            }
          }
        } else {
          flag = false;
        }
      } else if (locQues.quesType === 4 && sevQues.quesType === 4) {
        // 问答题
        if (locQues.answer[0] && sevQues.answer[0] && locQues.answer[0] !== sevQues.answer[0]) {
          flag = false;
        } else if (locQues.attach && sevQues.attach && locQues.attach.length === sevQues.attach.length) {
          for (let i = 0; i < locQues.attach.length; i++) {
            if (locQues.attach[i].fileId !== sevQues.attach[i].fileId) {
              flag = false;
            }
          }
        } else {
          flag = false;
        }
      }
      return flag;
    },
    // 答题倒计时
    timing() {
      // 考试用时，进入该页面的时间
      const timer1Interval = function() {
        // 设置offsetSecondes
        const vm = this;
        vm.elapsedTimeCounter = setInterval(function() {
          vm.perSubmitTime++;
          window.localStorage.setItem(USER_EXAM_USED_TIME, vm.uemId + '|' + vm.perSubmitTime);
          if (vm.maxLeaveTime > 0) {
            vm.noOperateSecs--;
            vm.operateSeconds();
          }
        }, 1000);
      };
      // 每30秒进行缓存提交并校时
      const timer2Interval = function() {
        const vm = this;
        vm.perSubmitTimeCounter = setInterval(function() {
          // 提交成功，重新计时
          const reqBody = {};
          reqBody.uniqueId = vm.uniqueId;
          reqBody.usedTime = vm.perSubmitTime;
          reqBody.orgId = vm.orgId;
          reqBody.arrangeId = vm.arrId;
          reqBody.ueStartTime = vm.baseInfo.ueStartTime;
          vm.perSubmitTime = vm.perSubmitTime - reqBody.usedTime;
          examService.submitUsedTime(vm.ueId, reqBody).then(res => {
            try {
              vm.userExitCount = res.exitCount;
              vm.getInitAndLatestTime(res);
            } catch (error) {
            }
          }).catch(err => {
            vm.perSubmitTime = vm.perSubmitTime + reqBody.usedTime;
            vm.handleErrorMsg(err);
          });
          // 心跳打点
          window.YxtFeLog && window.YxtFeLog.track('e_heartbeat', { lib: 'js', properties: {} });
        }, 30 * 1000);
      };

      timer1Interval.bind(this)(); // 设置perSubmitTime
      timer2Interval.bind(this)(); // 每两分钟提交一次
    },

    // hh:mm:ss倒计时转毫秒级
    getNormalMillisecond(e) {
      let result = '';
      if (e) {
        const tempTimeArr = e.split(':');
        const tempSec = parseInt(tempTimeArr[0]) * 60 * 60 + parseInt(tempTimeArr[1]) * 60 + parseInt(tempTimeArr[2]);
        result = tempSec * 1000;
      }
      return result;
    },
    // yyyy-mm-dd hh:mm:ss标准时间转时间的毫秒级
    getMillisecond(e) {
      let result = '';
      if (e) {
        result = Date.parse(new Date(e.replace(/-/g, '/')));
      }
      return result;
    },
    // 用时倒计时  时间到自动提交
    timingUsedTime() {
      if (this.usedTimesCtrl === 1 || this.forceSubmitCtrl === 1) {
        this.timer = setInterval(() => {
          this.residueTime = this.residueTime - 1000;
          if (this.residueTime <= 60 * 1000) {
            this.submitText = this.$t('pc_ote_msg_tobesubmitted');
            this.submitStatus = 1;
          }
          if (this.residueTime <= 0) {
            // 小于0时赋值为0
            this.residueTime = 0;
            // 考试用时用尽，自动提交
            window.clearInterval(this.timer);
            // 提交类型。0：主动提交；1：后台点击结束考试时的内存提交；2：自动提交（(用时已尽)；3：自动提交(统一交卷)；4：自动交卷（多次退出）
            if (this.baseInfo.residueTimeType === '0') {
              this.submitType = 2;
            } else if (this.baseInfo.residueTimeType === '1') {
              this.submitType = 3;
            }
            this.submit();
          }
        }, 1000);
      }
    },
    // 生成试卷题型标题
    generateQuesTitle() {
      let quesTypeCount = 0; // 题型序号
      for (let i = 0; i < this.titleSettings.length; i++) {
        const setting = this.titleSettings[i]; // 题型设置
        let title = '';
        let isHasSubs = false; // 题型下是否有题目
        let quesList = null; // 题型下所以的小题
        switch (setting.type) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
            if (this.quesOfType[setting.type] && this.quesOfType[setting.type].length > 0) {
              quesList = this.quesOfType[setting.type];
              isHasSubs = true;
            }
            break;
          case 5:
            // 组合题
            if (
              this.quesOfType[setting.type] &&
              this.quesOfType[setting.type][setting.composeQuesId] &&
              this.quesOfType[setting.type][setting.composeQuesId].length > 0
            ) {
              quesList = this.quesOfType[setting.type][setting.composeQuesId];
              isHasSubs = true;
            }
            break;
          default:
            break;
        }
        if (isHasSubs) {
          // 序号
          title = getQuesTypeIndexName(++quesTypeCount) + '、';
          // 标题
          title += setting.alias ? this.titleSettings[i].alias : this.$t(QUES_TYPE_NAMES[setting.type]);
          const titleDesc = setting.description ? this.titleSettings[i].description : this.getQuesTypeDefaultDescription(setting.type, quesList);
          this.quesTypesList.push({
            ques: setting.type === 5 ? this.combQuesDic[setting.composeQuesId] : null, // 组合题时的组合题基本信息
            quesList,
            title,
            titleDesc
          });
        }
      }
    },
    // 获取试卷题型默认描述
    getQuesTypeDefaultDescription(type, quesList) {
      let title = '';
      let score = 0; // 计算总分
      quesList.forEach(function(e) {
        score += e.totalScore;
      });
      title = this.$t('pc_ote_lbl_paper_question_title', [quesList.length, parseFloat(score.toFixed(2))]);
      return title;
    },
    // 初始化用户试卷试题标记
    initMarkStore() {
      const markInfo = window.getCookie(this.storageKey) || window.localStorage.getItem(this.storageKey);
      if (markInfo) {
        try {
          this.quesMarkIdList = JSON.parse(markInfo);
        } catch (error) {}
      }
    },
    // 清除当前用户考试标记
    clearMarkStore() {
      window.clearCookie(this.storageKey);
      window.localStorage.removeItem(this.storageKey);
    },
    // 同步缓存中的标记
    updateMarkStore(quesId, isMarked) {
      const markInfo = window.getCookie(this.storageKey) || window.localStorage.getItem(this.storageKey);
      let storeList = [];
      try {
        if (markInfo) {
          storeList = JSON.parse(markInfo);
          if (storeList.includes(quesId)) {
            if (isMarked === 0) {
              // 取消标记的试题在store中存在，则删除该试题Id
              const splIndex = storeList.indexOf(quesId);
              storeList.splice(splIndex, 1);
            }
          } else if (isMarked === 1) {
            // 进行标记的试题在store中不存在，则追加该试题Id
            storeList.push(quesId);
          }
        } else {
          storeList = [quesId];
        }
      } catch (error) {}
      window.clearCookie(this.storageKey);
      window.localStorage.setItem(this.storageKey, JSON.stringify(storeList));
    },
    getAnswerCardStyle() {
      return {
        maxHeight: (this.getHeight() - 60 - 24 - ((this.getWidth() < (1252 + this.getFSWidth() * 2)) && this.deepStudy ? 60 : 0)) + 'px',
        minHeight: '320px',
        width: this.getWidth() > 1000 ? '282px' : '256px'
      };
    },
    clearPage() {
      // 清空定时器
      window.clearInterval(this.timer);
      window.clearInterval(this.elapsedTimeCounter);
      window.clearInterval(this.perSubmitTimeCounter);
      window.clearInterval(this.localTimeCounter);
      // 清除页面绑定事件
      window.removeEventListener('blur', this.addExistFunc);
      window.removeEventListener('focus', this.backExistFunc);
      window.removeEventListener('mousemove', this.resetSeconds);
      window.removeEventListener('click', this.resetSeconds);
      window.removeEventListener('mousewheel', this.resetSeconds);
      window.removeEventListener('keyup', this.resetSeconds);

      // this.antiCheatDialog = false;
      this.noOperateDialog = false;
      this.confirmLeaveDialog = false;
      this.submitedDialog = false;
      this.confirmUndoDialog = false;
      this.confirmFinishDialog = false;
    },

    // 点击文件选择的时候逻辑
    fileClick() {
      this.fileClickStatus = true;
    },

    // 每30秒提交的time接口中进行获取新的时间进行倒计时
    getInitAndLatestTime(res) {
      // 在获取新的配置的时候先清除定时器，防止为0立刻提交
      window.clearInterval(this.timer);

      this.usedTimesCtrl = res.usedTimesCtrl; // 是否允许控制考试总时长
      this.usedTimesLen = res.usedTimesLen; // 考试允许总时长
      this.forceSubmitCtrl = res.forceSubmitCtrl; // 考试统一交卷
      this.forceSubmitTime = res.forceSubmitTime;
      this.startResidueTimeStr = res.residueTimeStr; // 考试剩余用时
      this.startResidueTime = this.getNormalMillisecond(this.startResidueTimeStr);
      this.residueTime = this.startResidueTime; // 倒计时用
      this.baseInfo.residueTimeType = res.residueTimeType;
      this.baseInfo.usedTimes = res.usedTimes;

      this.$nextTick(() => {
        if ((res.usedTimesCtrl === 1 && res.usedTimesLen > 0) || res.forceSubmitCtrl === 1) {
          if (res.usedTimesCtrl === 1 && res.usedTimes >= res.usedTimesLen) {
            // 考试时长已用完，自动提交
            this.submitType = 2;
            this.submit();
            return;
          }
          if (res.forceSubmitCtrl === 1 && res.forceSubmitTime && res.currentSystemTime) {
            if (Date.parse(res.forceSubmitTime.replace(/-/g, '/')) <= Date.parse(res.currentSystemTime.replace(/-/g, '/'))) {
              // 统一交卷时间已到，自动提交
              this.submitType = 3;
              this.submit();
              return;
            }
          }

          this.timer && window.clearInterval(this.timer);
          // 统一考试时长，开启倒计时
          this.timingUsedTime();
        }
      });
    }
  },

  beforeDestroy() {
    this.clearPage();
  }
};
</script>
