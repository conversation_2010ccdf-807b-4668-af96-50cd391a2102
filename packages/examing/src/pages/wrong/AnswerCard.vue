<template>
  <CommonSticky
    position="top"
    :offset="64"
    class="yxtulcdsdk-flex-shrink-0 mt20"
    by-class
    :style="getAnswerCardStyle()"
  >
    <div class="yxtulcdsdk-marking-right" :style="getAnswerCardStyle()">
      <div class="yxtulcdsdk-marking-right__card">
        <div class="color-gray-10 text-center standard-size-18 mb12 yxt-weight-5">{{ $t('pc_ote_lbl_answercard') }}</div>
        <template v-if="mode === 'answer'">
          <div class="yxtulcdsdk-answer-check-group">
            <!-- 未做 -->
            <span class="yxtulcdsdk-answer-check-item">
              <span class="decorate-answer ml16 mr8"></span>
              <span class="standard-size-12">{{ $t('pc_ote_tip_answerundo') }}</span>
            </span>
            <!-- 已做 -->
            <span class="yxtulcdsdk-answer-check-item">
              <span class="decorate-answer ml16 mr8 answered bg-primary-6-i border-primary-6-i"></span>
              <span class="standard-size-12">{{ $t('pc_ote_tip_answerdone') }}</span>
            </span>
            <!-- 当前 -->
            <span class="yxtulcdsdk-answer-check-item">
              <span class="decorate-answer ml16 mr8 answering border-primary-6-i"></span>
              <span class="standard-size-12">{{ $t('pc_ote_tip_answercurrent') }}</span>
            </span>
            <!-- 标记 -->
            <span class="yxtulcdsdk-answer-check-item">
              <span class="decorate-answer ml16 mr8">
                <yxt-svg-icon
                  icon-class="answer-flag"
                  class-name="svgico-answer-flag color-red-6"
                  width="9px"
                  height="9px"
                />
              </span>
              <span class="standard-size-12">{{ $t('pc_ote_tip_markflag') }}</span>
            </span>
          </div>
          <yxt-divider class="mv16" />
          <div class="yxtulcdsdk-marking-answer__box">
            <yxt-scrollbar fit-height>
              <!-- 题型列表 -->
              <template v-for="(typedQuesList, indexType) in quesTypesList">
                <div v-if="typedQuesList.quesList && typedQuesList.quesList.length > 0" :key="indexType" class="ph15 mb12">
                  <div class="ellipsis standard-size-14 mt0 mb16">{{ typedQuesList.title }}</div>
                  <ul class="yxtulcdsdk-card-list font-size-12 clearfix">
                    <!-- 题号列表 -->
                    <li
                      v-for="(item, index) in isComposite(typedQuesList) ? typedQuesList.quesList[0].subQuesItem : typedQuesList.quesList"
                      :key="item.id"
                      class="hand yxtulcdsdk-answer-card"
                      :class="quesItemClass(item)"
                      @click="scrollView(item, isComposite(typedQuesList))"
                    >
                      {{ index + 1 }}
                      <yxt-svg-icon
                        v-if="item.isMarked === 1"
                        icon-class="answer-flag"
                        class-name="svgico-answer-flag color-red-6"
                        width="12px"
                        height="12px"
                      />
                    </li>
                  </ul>
                </div>
              </template>
            </yxt-scrollbar>
          </div>
        </template>
        <template v-if="mode === 'review'">
          <div class="yxtulcdsdk-answer-check-group">
            <!-- 正确 -->
            <span class="yxtulcdsdk-answer-check-item mb10">
              <span class="decorate-answer ml16 mr8 answer-right"></span>
              <small class="ellipsis">{{ $t('pc_ote_lbl_correct') }}{{ $t('pc_ote_lbl_colon') }}{{ statistic.correct }} <span class="color-gray-7">{{ $t('pc_ote_lbl_autoRemoveWrongQues'/*自动移除*/) }}</span></small>
            </span>
            <!-- 错误 -->
            <span class="yxtulcdsdk-answer-check-item">
              <span class="decorate-answer ml16 mr8 answer-wrong"></span>
              <small class="ellipsis">{{ $t('pc_ote_lbl_wrong') }}{{ $t('pc_ote_lbl_colon') }}{{ statistic.wrong }}</small>
            </span>
            <!-- 主观题 -->
            <span class="yxtulcdsdk-answer-check-item">
              <span class="decorate-answer subjective-ques ml16 mr8"></span>
              <span class="standard-size-12">{{ $t('pc_ote_lbl_subjectiveQuestion'/*主观题*/) }}{{ $t('pc_ote_lbl_colon') }}{{ statistic.subjective }}</span>
            </span>
          </div>
          <yxt-divider class="mv16" />
          <div class="yxtulcdsdk-marking-answer__box">
            <yxt-scrollbar fit-height>
              <!-- 题型列表 -->
              <template v-for="(typedQuesList, indexType) in quesTypesList">
                <div v-if="typedQuesList.quesList && typedQuesList.quesList.length > 0" :key="indexType" class="ph16 mb10 mt14">
                  <div class="ellipsis font-size-14 mb14">{{ typedQuesList.title }}</div>
                  <ul class="yxtulcdsdk-card-list font-size-12 clearfix">
                    <li
                      v-for="(item, index) in isComposite(typedQuesList) ? typedQuesList.quesList[0].subQuesItem : typedQuesList.quesList"
                      :key="item.id"
                      class="hand"
                      :class="quesItemClass(item)"
                      @click="scrollView(item, isComposite(typedQuesList))"
                    >
                      {{ index + 1 }}
                    </li>
                  </ul>
                </div>
              </template>
            </yxt-scrollbar>
          </div>
        </template>
        <template v-if="mode === 'preview'">
          <div class="yxtulcdsdk-marking-answer__box">
            <yxt-scrollbar fit-height>
              <!-- 题型列表 -->
              <template v-for="(typedQuesList, indexType) in quesTypesList">
                <div v-if="typedQuesList.quesList && typedQuesList.quesList.length > 0" :key="indexType" class="ph16 mb10 mt14">
                  <div class="ellipsis font-size-14 mb14">{{ typedQuesList.title }}</div>
                  <ul class="yxtulcdsdk-card-list font-size-12 clearfix">
                    <li
                      v-for="(item, index) in isComposite(typedQuesList) ? typedQuesList.quesList[0].subQuesItem : typedQuesList.quesList"
                      :key="item.id"
                      class="hand error"
                      @click="scrollView(item, isComposite(typedQuesList))"
                    >
                      {{ index + 1 }}
                    </li>
                  </ul>
                </div>
              </template>
            </yxt-scrollbar>
          </div>
        </template>
        <div v-if="showRemoveAllButton" class="text-center mv16">
          <yxt-button size="small" plain @click="removeAll">{{ $t('pc_ote_lbl_removeAllWrongQues'/*清空所有错题*/) }}</yxt-button>
        </div>
      </div>
      <!-- 提交考试 -->
      <yxt-button
        v-if="showMainButton"
        type="primary"
        size="large"
        class="width-percent-100 submit-btn mt16"
        :loading="isSubmitting"
        @click="submit()"
      >
        {{ submitText }}
      </yxt-button>
    </div>
  </CommonSticky>
</template>

<script>
import { QuesTypeEnum } from '../../core/enums';
import CommonSticky from '../components/CommonSticky.vue';
import YxtSvgIcon from '../components/svgIcon.vue';

export default {
  name: 'AnswerCard',
  components: {
    CommonSticky,
    YxtSvgIcon
  },
  inject: ['getHeight', 'getWidth', 'getFSWidth'],
  props: {
    mode: {
      type: String,
      validator: (value) => ['itemList', 'answer', 'review', 'preview'].includes(value)
    },
    isSubmitting: {
      type: Boolean
    },
    statistic: {
      type: Object,
      default: () => {}
    },
    quesTypesList: {
      type: Array,
      default: () => []
    },
    currentQues: {
      type: Object,
      default: () => {}
    },
    deepStudy: Boolean
  },
  data() {
    return {
      QuesTypeEnum
    };
  },
  computed: {
    submitText() {
      const index = ['itemList', 'answer', 'review', 'preview'].indexOf(this.mode);
      return ['', this.$t('pc_ote_lbl_submitWrongQuesExam'/* 提交作答 */), this.$t('pc_ote_lbl_practiceWrongQues'/* 练习错题 */), ''][index];
    },
    showRemoveAllButton() {
      if (this.mode === 'preview') return true;
      if (this.mode === 'review') {
        if (this.statistic.subjective > 0 || this.statistic.wrong > 0) return true;
      }
      return false;
    },
    showMainButton() {
      if (this.mode === 'answer') return true;
      if (this.mode === 'review') {
        if (this.statistic.subjective > 0 || this.statistic.wrong > 0) return true;
      }
      return false;
    }
  },
  created() {
    this.dealStickyEvent(1);
  },
  methods: {
    dealStickyEvent(type) {
      switch (type) {
        case 0:
          this.resizeHander();
          break;
        case 1:
          window.addEventListener('resize', this.resizeHander);
          break;
        case 2:
          window.removeEventListener('resize', this.resizeHander);
          break;
        default:
          break;
      }
    },
    resizeHander() {
      this.$refs.stickyRight && this.$refs.stickyRight.scrollHandler();
    },
    submit() {
      this.$emit('submit');
    },
    scrollView(item, val) {
      this.$emit('scrollTo', item, val);
    },
    // 状态样式
    quesItemClass(item) {
      if (this.mode === 'answer') {
        // 三种状态
        if (item.isAnswered === 1) {
          return 'answered bg-primary-6-i border-primary-6-i';
        } else if (this.currentQues.id === item.id) {
          return 'answering border-primary-6-i color-primary-6-i';
        } else {
          return '';
        }
      } else if (this.mode === 'review') {
        // 0 判断失败或未判定；1 错误；2 正确
        if ([QuesTypeEnum.fillBlank, QuesTypeEnum.shortAnswer].includes(item.quesType)) return 'subjective-ques';
        if (item.correct === 1) return 'error';
        if (item.correct === 2) return 'success';
        return '';
      } else if (this.mode === 'preview') {
        // 都是 error
        return '';
      } else {
        return '';
      }
    },
    removeAll() {
      this.$emit('removeAll');
    },

    isComposite(list) {
      return list.quesType === QuesTypeEnum.composite;
    },

    getAnswerCardStyle() {
      return {
        maxHeight: (this.getHeight() - 44 - 20 - 22 - (this.getWidth() < (1360 + this.getFSWidth() * 2) && this.deepStudy ? 60 : 0)) + 'px',
        minHeight: '220px',
        width: '256px'
      };
    }
  },
  beforeDestroy() {
    this.dealStickyEvent(2);
  }
};
</script>
