<template>
  <div class="yxtulcdsdk-wrong-item">
    <div v-if="mode === 'itemList' && !isSubItem" class="single-ques-mode-header yxtulcdsdk-flex-center-center color-gray-7 font-size-12 lh22">
      <yxtf-checkbox v-model="value.quesUISelected" @change="selectItem" />
      <span class="ellipsis flex-1 ph8 ml8">
        {{ titleSettings[value.quesType].title }}
      </span>
      <span>{{ $t('pc_ote_lbl_difficulty') }}{{ $t('pc_ote_lbl_colon') }}{{ $t(QUES_LEVEL_NAMES[value.levelType]) }}</span>
      <span class="mh16 split-bar-1px"></span>
      <span>{{ $t('pc_ote_lbl_wrongTimes', [value.wrongNum]/*答错次数*/) }}</span>
    </div>
    <!-- 单选题 -->
    <div v-if="value.quesType === QuesTypeEnum.singleChoice" class="yxtulcdsdk-quesCard">
      <div class="flex color-gray-10 layout-align-start mt20 mb5">
        <div class="width32 flex-shrink-0">{{ index + 1 }}.</div>
        <div class="flex-1"><span v-html="cleanStyle(value.content)"></span></div>
        <div v-if="mode!=='itemList'" class="flex-shrink-0">
          <span class="ml20 lh20 color-gray-7 font-size-12 ph8 bg-gray-2">{{ $t('pc_ote_lbl_wrongTimes', [value.wrongNum]/*答错次数*/) }}</span>
        </div>
        <div class="ml12 hand h22 flex-mid">
          <span v-if="mode === 'answer'" class="flex" @click="markQues()">
            <yxtf-svg
              :remote-url="mediaUrl"
              :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
              :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
              width="16px"
              height="16px"
            />
          </span>

          <yxt-tooltip
            v-if="showDelete"
            placement="top"
            effect="light"
            width="16px"
            height="16px"
            :content="$t('pc_ote_lbl_removeWrongQues'/*移除错题*/)"
          >
            <span class="visibility-hidden icon-delete flex-mid" @click="delItem">
              <yxtf-svg
                class="color-gray-8"
                width="16px"
                height="16px"
                icon-class="delete"
              />
            </span>
          </yxt-tooltip>
        </div>
      </div>
      <media-player
        v-if="value.fileId && value.playDetails && value.mediaType !== QuesMediaTypeEnum.text"
        ref="mediaPlayer"
        width="610"
        height="460"
        class="mt16 mb10"
        :player-key="index"
        :file-id="value.fileId"
        :options="playerOptions"
        :type="value.mediaType"
        :tran-status="value.tranStatus"
      />
      <yxtf-radio-group
        v-model="value.userAnswer"
        direction="row"
        :disabled="formItemDisabled ? 'lock' : false"
        class="color-primary-6 pl32 pr24 yxtulcdsdk-ques-radio-group width-percent-100"
        @change="submitSingle()"
      >
        <template v-for="(i, j) in value.choiceItems">
          <yxtf-radio
            :key="i.id"
            :label="i.id"
            class="yxtulcdsdk-ques-radio"
          >
            <div class="flex"><span class="vertical-align-top pr4 yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span></div>
          </yxtf-radio>
          <media-player
            v-if="i.itemPlay && i.itemPlay.fileId"
            :key="i.id"
            ref="mediaPlayer"
            width="610"
            height="460"
            class="mt4 ml23"
            :file-id="i.itemPlay.fileId"
            :options="playerMediaOptions(i.itemPlay)"
            :type="i.itemPlay.type"
            :tran-status="i.itemPlay.tranStatus"
          />
        </template>
      </yxtf-radio-group>
      <div v-if="formItemDisabled" class="bg-gray-1 yxtulcdsdk-review-tag b-radius4 ph24 pv20 mt20">
        <!-- 答案 -->
        <div class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_itemanswer') }}</yxtf-tag>
          <div class="flex-1 color-gray-9">{{ value.correctAnswer }}</div>
        </div>
        <div v-if="!value.parentId" class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_assesspoint'/*考点*/) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            <span>{{ value.pointNames.length ? value.pointNames.join('、') : $t('pc_ote_lbl_none') }}</span>
          </div>
        </div>
        <div class="flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_quesanalyze'/*解析*/) }}</yxtf-tag>
          <div class="color-gray-9 flex-1 over-hidden">
            <div v-if="value.explainText || (value.explainPlay && value.explainPlay.fileId)" class="flex">
              <div :ref="'nowrapEle_' + value.id" :class="[value.isShowMoreText && !value.isShowMore ? 'no-ellipsis max-height30' : 'max-height-unset', 'flex-1']">
                <span v-html="cleanStyle(value.explainText)"></span>
                <div v-if="value.explainPlay && value.explainPlay.fileId">
                  <media-player
                    v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                    ref="mediaPlayer"
                    width="610"
                    height="460"
                    class="flex"
                    :class="value.explainText ? 'mt12' : ''"
                    :player-key="value.explainPlay.fileId"
                    :type="value.explainPlay.type"
                    :file-id="value.explainPlay.fileId"
                    :options="playerMediaOptions(value.explainPlay)"
                    :tran-status="value.explainPlay.tranStatus"
                  />
                </div>
              </div>
              <div v-if="value.isShowMoreText" class="font-size-14 hand ml20" @click="changeMoreStatus(value.isShowMore)">
                <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
                <span :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'" class="ml4"></span>
              </div>
            </div>
            <div v-show="!value.explainText && !value.explainPlay">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 多选题 -->
    <div v-else-if="value.quesType === QuesTypeEnum.multiChoice" class="yxtulcdsdk-quesCard">
      <div class="flex color-gray-10 layout-align-start mt20 mb5">
        <div class="width32 flex-shrink-0">{{ index + 1 }}.</div>
        <div class="flex-1"><span v-html="cleanStyle(value.content)"></span></div>
        <div v-if="mode!=='itemList'" class="flex-shrink-0">
          <span class="ml20 lh20 color-gray-7 font-size-12 ph8 lh20 bg-gray-2">{{ $t('pc_ote_lbl_wrongTimes', [value.wrongNum]/*答错次数*/) }}</span>
        </div>
        <div class="ml12 hand h22 flex-mid">
          <span v-if="mode === 'answer'" class="flex" @click="markQues()">
            <yxtf-svg
              :remote-url="mediaUrl"
              :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
              :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
              width="16px"
              height="16px"
            />
          </span>

          <yxt-tooltip
            v-if="showDelete"
            placement="top"
            effect="light"
            width="16px"
            height="16px"
            :content="$t('pc_ote_lbl_removeWrongQues'/*移除错题*/)"
          >
            <span class="visibility-hidden icon-delete flex-mid" @click="delItem">
              <yxtf-svg
                class="color-gray-8"
                width="16px"
                height="16px"
                icon-class="delete"
              />
            </span>
          </yxt-tooltip>
        </div>
      </div>
      <media-player
        v-if="value.fileId && value.playDetails && value.mediaType !== QuesMediaTypeEnum.text"
        ref="mediaPlayer"
        width="610"
        height="460"
        class="mt16 mb10"
        :player-key="index"
        :file-id="value.fileId"
        :options="playerOptions"
        :type="value.mediaType"
        :tran-status="value.tranStatus"
      />
      <yxtf-checkbox-group v-model="value.userAnswer" :direction="'row'" class="color-primary-6 pr24 pl32 yxtulcdsdk-ques-checkbox-group width-percent-100">
        <template v-for="(i, j) in value.choiceItems">
          <yxtf-checkbox
            :key="i.id"
            :label="i.id"
            class="yxtulcdsdk-ques-checkbox"
            :disabled="formItemDisabled"
            @change="submitSingle()"
          >
            <div class="flex"><span class="vertical-align-top pr4 yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span></div>
          </yxtf-checkbox>
          <media-player
            v-if="i.itemPlay && i.itemPlay.fileId"
            :key="i.id"
            ref="mediaPlayer"
            width="610"
            height="460"
            class="mt4 ml23"
            :file-id="i.itemPlay.fileId"
            :options="playerMediaOptions(i.itemPlay)"
            :type="i.itemPlay.type"
            :tran-status="i.itemPlay.tranStatus"
          />
        </template>
      </yxtf-checkbox-group>
      <div v-if="formItemDisabled" class="bg-gray-1 yxtulcdsdk-review-tag b-radius4 ph24 pv20 mt20">
        <!-- 答案 -->
        <div class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_itemanswer') }}</yxtf-tag>
          <div class="flex-1 color-gray-9">{{ value.correctAnswer }}</div>
        </div>
        <div v-if="!value.parentId" class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_assesspoint'/*考点*/) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            <span>{{ value.pointNames.length ? value.pointNames.join('、') : $t('pc_ote_lbl_none') }}</span>
          </div>
        </div>
        <div class="flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_quesanalyze'/*解析*/) }}</yxtf-tag>
          <div class="color-gray-9 flex-1 over-hidden">
            <div v-if="value.explainText || (value.explainPlay && value.explainPlay.fileId)" class="flex">
              <div :ref="'nowrapEle_' + value.id" :class="[value.isShowMoreText && !value.isShowMore ? 'no-ellipsis max-height30' : 'max-height-unset', 'flex-1']">
                <span v-html="cleanStyle(value.explainText)"></span>
                <div v-if="value.explainPlay && value.explainPlay.fileId">
                  <media-player
                    v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                    ref="mediaPlayer"
                    width="610"
                    height="460"
                    class="flex"
                    :class="value.explainText ? 'mt12' : ''"
                    :player-key="value.explainPlay.fileId"
                    :type="value.explainPlay.type"
                    :file-id="value.explainPlay.fileId"
                    :options="playerMediaOptions(value.explainPlay)"
                    :tran-status="value.explainPlay.tranStatus"
                  />
                </div>
              </div>
              <div v-if="value.isShowMoreText" class="font-size-14 hand ml20" @click="changeMoreStatus(value.isShowMore)">
                <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
                <span :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'" class="ml4"></span>
              </div>
            </div>
            <div v-show="!value.explainText && !value.explainPlay">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 判断题 -->
    <div v-else-if="value.quesType === QuesTypeEnum.judge" class="yxtulcdsdk-quesCard">
      <div class="flex color-gray-10 layout-align-start mt20 mb5">
        <div class="width32 flex-shrink-0">{{ index + 1 }}.</div>
        <div class="flex-1"><span v-html="cleanStyle(value.content, true)"></span></div>
        <div v-if="mode!=='itemList'" class="flex-shrink-0">
          <span class="ml20 lh20 color-gray-7 font-size-12 ph8 lh20 bg-gray-2">{{ $t('pc_ote_lbl_wrongTimes', [value.wrongNum]/*答错次数*/) }}</span>
        </div>
        <div class="ml12 hand h22 flex-mid">
          <span v-if="mode === 'answer'" class="flex" @click="markQues()">
            <yxtf-svg
              :remote-url="mediaUrl"
              :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
              :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
              width="16px"
              height="16px"
            />
          </span>

          <yxt-tooltip
            v-if="showDelete"
            placement="top"
            effect="light"
            width="16px"
            height="16px"
            :content="$t('pc_ote_lbl_removeWrongQues'/*移除错题*/)"
          >
            <span class="visibility-hidden icon-delete flex-mid" @click="delItem">
              <yxtf-svg
                class="color-gray-8"
                width="16px"
                height="16px"
                icon-class="delete"
              />
            </span>
          </yxt-tooltip>
        </div>
      </div>
      <media-player
        v-if="value.fileId && value.playDetails && value.mediaType !== QuesMediaTypeEnum.text"
        ref="mediaPlayer"
        width="610"
        height="460"
        class="mt16 mb10"
        :player-key="index"
        :file-id="value.fileId"
        :options="playerOptions"
        :type="value.mediaType"
        :tran-status="value.tranStatus"
      />
      <yxtf-radio-group
        v-model="value.userAnswer"
        :direction="'row'"
        class="color-primary-6 pr24 pl32"
        :disabled="formItemDisabled ? 'lock' : false"
        @change="submitSingle()"
      >
        <yxtf-radio label="1">{{ value.judgeCorrectOptionContent || $t('pc_ote_lbl_correct') }}</yxtf-radio>
        <yxtf-radio label="0">{{ value.judgeWrongOptionContent || $t('pc_ote_lbl_wrong') }}</yxtf-radio>
      </yxtf-radio-group>
      <div v-if="formItemDisabled" class="bg-gray-1 yxtulcdsdk-review-tag b-radius4 ph24 pv20 mt20">
        <!-- 答案 -->
        <div class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_itemanswer') }}</yxtf-tag>
          <div class="flex-1 color-gray-9">{{ value.correctAnswer }}</div>
        </div>
        <div v-if="!value.parentId" class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_assesspoint'/*考点*/) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            <span>{{ value.pointNames.length ? value.pointNames.join('、') : $t('pc_ote_lbl_none') }}</span>
          </div>
        </div>
        <div class="flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_quesanalyze'/*解析*/) }}</yxtf-tag>
          <div class="color-gray-9 flex-1 over-hidden">
            <div v-if="value.explainText || (value.explainPlay && value.explainPlay.fileId)" class="flex">
              <div :ref="'nowrapEle_' + value.id" :class="[value.isShowMoreText && !value.isShowMore ? 'no-ellipsis max-height30' : 'max-height-unset', 'flex-1']">
                <span v-html="cleanStyle(value.explainText)"></span>
                <div v-if="value.explainPlay && value.explainPlay.fileId">
                  <media-player
                    v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                    ref="mediaPlayer"
                    width="610"
                    height="460"
                    class="flex"
                    :class="value.explainText ? 'mt12' : ''"
                    :player-key="value.explainPlay.fileId"
                    :type="value.explainPlay.type"
                    :file-id="value.explainPlay.fileId"
                    :options="playerMediaOptions(value.explainPlay)"
                    :tran-status="value.explainPlay.tranStatus"
                  />
                </div>
              </div>
              <div v-if="value.isShowMoreText" class="font-size-14 hand ml20" @click="changeMoreStatus(value.isShowMore)">
                <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
                <span :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'" class="ml4"></span>
              </div>
            </div>
            <div v-show="!value.explainText && !value.explainPlay">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 填空题 -->
    <div v-else-if="value.quesType === QuesTypeEnum.fillBlank" class="yxtulcdsdk-quesCard">
      <div class="flex color-gray-10 layout-align-start mt20 mb5">
        <div class="width32 flex-shrink-0">{{ index + 1 }}.</div>
        <div class="flex-1"><span v-html="cleanStyle(value.content)"></span></div>
        <div v-if="mode!=='itemList'" class="flex-shrink-0">
          <span class="ml20 lh20 color-gray-7 font-size-12 ph8 lh20 bg-gray-2">{{ $t('pc_ote_lbl_wrongTimes', [value.wrongNum]/*答错次数*/) }}</span>
        </div>
        <div class="ml12 hand h22 flex-mid">
          <span v-if="mode === 'answer'" class="flex" @click="markQues()">
            <yxtf-svg
              :remote-url="mediaUrl"
              :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
              :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
              width="16px"
              height="16px"
            />
          </span>

          <yxt-tooltip
            v-if="showDelete"
            placement="top"
            effect="light"
            width="16px"
            height="16px"
            :content="$t('pc_ote_lbl_removeWrongQues'/*移除错题*/)"
          >
            <span class="visibility-hidden icon-delete flex-mid" @click="delItem">
              <yxtf-svg
                class="color-gray-8"
                width="16px"
                height="16px"
                icon-class="delete"
              />
            </span>
          </yxt-tooltip>
        </div>
      </div>
      <media-player
        v-if="value.fileId && value.playDetails && value.mediaType !== QuesMediaTypeEnum.text"
        ref="mediaPlayer"
        width="610"
        height="460"
        class="mt16 mb10"
        :player-key="index"
        :file-id="value.fileId"
        :options="playerOptions"
        :type="value.mediaType"
        :tran-status="value.tranStatus"
      />
      <yxt-row v-for="(item, j) in value.fillItems" :key="item.id" class="lh40 pr24 pl32 mt20">
        <yxt-col :span="1">{{ j + 1 }}.</yxt-col>
        <yxt-col :span="17">
          <yxtf-input
            v-model="value.userAnswer[j]"
            :disabled="formItemDisabled"
            class="clazz_width_80"
            maxlength="500"
            @paste.native.capture.prevent="handlePaste"
            @focus="recCurrentQues()"
            @change="submitSingle()"
          />
        </yxt-col>
      </yxt-row>
      <div v-if="formItemDisabled" class="bg-gray-1 yxtulcdsdk-review-tag b-radius4 ph24 pv20 mt20">
        <!-- 答案 -->
        <div class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_itemanswer') }}</yxtf-tag>
          <div class="flex-1 color-gray-9">{{ value.correctAnswer }}</div>
        </div>
        <div v-if="!value.parentId" class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_assesspoint'/*考点*/) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            <span>{{ value.pointNames.length ? value.pointNames.join('、') : $t('pc_ote_lbl_none') }}</span>
          </div>
        </div>
        <div class="flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_quesanalyze'/*解析*/) }}</yxtf-tag>
          <div class="color-gray-9 flex-1 over-hidden">
            <div v-if="value.explainText || (value.explainPlay && value.explainPlay.fileId)" class="flex">
              <div :ref="'nowrapEle_' + value.id" :class="[value.isShowMoreText && !value.isShowMore ? 'no-ellipsis max-height30' : 'max-height-unset', 'flex-1']">
                <span v-html="cleanStyle(value.explainText)"></span>
                <div v-if="value.explainPlay && value.explainPlay.fileId">
                  <media-player
                    v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                    ref="mediaPlayer"
                    width="610"
                    height="460"
                    class="flex"
                    :class="value.explainText ? 'mt12' : ''"
                    :player-key="value.explainPlay.fileId"
                    :type="value.explainPlay.type"
                    :file-id="value.explainPlay.fileId"
                    :options="playerMediaOptions(value.explainPlay)"
                    :tran-status="value.explainPlay.tranStatus"
                  />
                </div>
              </div>
              <div v-if="value.isShowMoreText" class="font-size-14 hand ml20" @click="changeMoreStatus(value.isShowMore)">
                <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
                <span :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'" class="ml4"></span>
              </div>
            </div>
            <div v-show="!value.explainText && !value.explainPlay">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 问答题 -->
    <div v-else-if="value.quesType === QuesTypeEnum.shortAnswer" class="yxtulcdsdk-quesCard">
      <div class="flex color-gray-10 layout-align-start mt20 mb5">
        <div class="width32 flex-shrink-0">{{ index + 1 }}.</div>
        <div class="flex-1"><span v-html="cleanStyle(value.content)"></span></div>
        <div v-if="mode!=='itemList'" class="flex-shrink-0">
          <span class="ml20 lh20 color-gray-7 font-size-12 ph8 lh20 bg-gray-2">{{ $t('pc_ote_lbl_wrongTimes', [value.wrongNum]/*答错次数*/) }}</span>
        </div>
        <div class="ml12 hand h22 flex-mid">
          <span v-if="mode === 'answer'" class="flex" @click="markQues()">
            <yxtf-svg
              :remote-url="mediaUrl"
              :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
              :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
              width="16px"
              height="16px"
            />
          </span>

          <yxt-tooltip
            v-if="showDelete"
            placement="top"
            effect="light"
            width="16px"
            height="16px"
            :content="$t('pc_ote_lbl_removeWrongQues'/*移除错题*/)"
          >
            <span class="visibility-hidden icon-delete flex-mid" @click="delItem">
              <yxtf-svg
                class="color-gray-8"
                width="16px"
                height="16px"
                icon-class="delete"
              />
            </span>
          </yxt-tooltip>
        </div>
      </div>
      <media-player
        v-if="value.fileId && value.playDetails && value.mediaType !== QuesMediaTypeEnum.text"
        ref="mediaPlayer"
        width="610"
        height="460"
        class="mt16 mb10"
        :player-key="index"
        :file-id="value.fileId"
        :options="playerOptions"
        :type="value.mediaType"
        :tran-status="value.tranStatus"
      />
      <yxt-row v-else class="lh40 pr24 pl32 mt16">
        <yxt-col :span="18">
          <!-- 问答题答案最多输入2000字 -->
          <yxtf-input
            v-model="value.userAnswer"
            :disabled="formItemDisabled"
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            maxlength="2000"
            show-word-limit
            @change="submitSingle()"
            @paste.native.capture.prevent="handlePaste"
            @focus="recCurrentQues()"
          />
        </yxt-col>
      </yxt-row>
      <div v-if="formItemDisabled" class="bg-gray-1 yxtulcdsdk-review-tag b-radius4 ph24 pv20 mt20">
        <!-- 关键词 -->
        <div v-if="isNotNullKeyword(value.answerKeyword)" class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_keyword' /* 关键词 */) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            {{ (JSON.parse(value.answerKeyword || null) || []).map(item => item.name).join(';') }}
          </div>
        </div>
        <!-- 答案 -->
        <div class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_itemanswer') }}</yxtf-tag>
          <div class="flex-1 color-gray-9">
            <div v-if="value.answerContent">
              <span v-html="cleanStyle(value.answerContent, true)"></span>
            </div>
            <media-player
              v-if="value.answerPlay && value.answerPlay.fileId"
              ref="mediaPlayer"
              width="610"
              height="460"
              :class="value.answerContent ? 'mt12' : ''"
              :player-key="index"
              :file-id="value.answerPlay.fileId"
              :options="playerMediaOptions(value.answerPlay)"
              :type="value.answerPlay"
              :tran-status="value.answerPlay.tranStatus"
            />
            <span v-if="!value.answerContent && !value.answerPlay">{{ $t('pc_ote_lbl_noCorrectAnswer') }}</span>
          </div>
        </div>
        <div v-if="!value.parentId" class="mb20 flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_assesspoint'/*考点*/) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            <span>{{ value.pointNames.length ? value.pointNames.join('、') : $t('pc_ote_lbl_none') }}</span>
          </div>
        </div>
        <div class="flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_quesanalyze'/*解析*/) }}</yxtf-tag>
          <div class="color-gray-9 flex-1 over-hidden">
            <div v-if="value.explainText || (value.explainPlay && value.explainPlay.fileId)" class="flex">
              <div :ref="'nowrapEle_' + value.id" :class="[value.isShowMoreText && !value.isShowMore ? 'no-ellipsis max-height30' : 'max-height-unset', 'flex-1']">
                <span v-html="cleanStyle(value.explainText)"></span>
                <div v-if="value.explainPlay && value.explainPlay.fileId">
                  <media-player
                    v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                    ref="mediaPlayer"
                    width="610"
                    height="460"
                    class="flex"
                    :class="value.explainText ? 'mt12' : ''"
                    :player-key="value.explainPlay.fileId"
                    :type="value.explainPlay.type"
                    :file-id="value.explainPlay.fileId"
                    :options="playerMediaOptions(value.explainPlay)"
                    :tran-status="value.explainPlay.tranStatus"
                  />
                </div>
              </div>
              <div v-if="value.isShowMoreText" class="font-size-14 hand ml20" @click="changeMoreStatus(value.isShowMore)">
                <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
                <span :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'" class="ml4"></span>
              </div>
            </div>
            <div v-show="!value.explainText && !value.explainPlay">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 组合题,只包含题干部分 -->
    <div v-else-if="value.quesType === QuesTypeEnum.composite">
      <div class="flex color-gray-10 layout-align-start mt20 mb5">
        <div class="flex-1"><span v-html="cleanStyle(value.content)"></span></div>
      </div>
      <media-player
        v-if="value.fileId && value.playDetails && value.mediaType !== QuesMediaTypeEnum.text"
        ref="mediaPlayer"
        width="610"
        height="460"
        class="mt16 mb10"
        :player-key="index"
        :file-id="value.fileId"
        :options="playerOptions"
        :type="value.mediaType"
        :tran-status="value.tranStatus"
      />

      <div v-if="formItemDisabled" class="bg-gray-1 yxtulcdsdk-review-tag b-radius4 ph24 pv20 mt20">
        <div class="flex">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag mr16-impt">{{ $t('pc_ote_lbl_assesspoint'/*考点*/) }}</yxtf-tag>
          <div class="pos_fl5 flex-1 color-gray-9">
            <span>{{ value.pointNames.length ? value.pointNames.join('、') : $t('pc_ote_lbl_none') }}</span>
          </div>
        </div>
      </div>

      <template v-for="(subItem, subItemIndex) in value.subQuesItem">
        <div :key="subItem.id + '_position'" :ref="subItem.id"></div>

        <yxt-ulcd-sdk-wrong-item
          :key="subItem.id + '_ques'"
          :value="subItem"
          :title-settings="titleSettings"
          is-sub-item
          :mode="mode"
          :index="mode === 'itemList' ? index : subItemIndex"
          v-on="$listeners"
        />
      </template>
    </div>
  </div>
</template>
<script>
import { QUES_LEVEL_NAMES, SvgMediaUrl } from '../../configs/const';
import { cleanStyle, convertASCIIForNum, getScrollParent } from '../../core/utils';
import MediaPlayer from '../components/mediaPlayer/index.vue';
import { QuesTypeEnum, QuesMediaTypeEnum } from '../../core/enums';

const NowrapHeight = 38;

export default {
  name: 'YxtUlcdSdkWrongItem',
  components: {
    MediaPlayer
  },
  props: {
    // 试题信息
    value: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 是否回答和只读
    mode: {
      type: String,
      validator(value) {
        return ['itemList', 'answer', 'preview', 'review'].includes(value);
      }
    },
    titleSettings: {
      type: Object,
      default: () => {}
    },
    // 题序
    index: {
      type: Number,
      default: 0
    },
    isSubItem: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      mediaUrl: SvgMediaUrl,
      QuesTypeEnum,
      QuesMediaTypeEnum,
      QUES_LEVEL_NAMES
    };
  },
  computed: {
    playerOptions() {
      if (this.value.tranStatus === 2) {
        const options = this.value.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: this.value.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    },
    showDelete() {
      return ['review', 'preview'].includes(this.mode) && this.value.currentStatus === 0;
    },
    formItemDisabled() {
      return ['review', 'preview', 'itemList'].includes(this.mode) ? 'lock' : false;
    }
  },
  mounted() {
    this.nowrapEleHeight();
  },
  methods: {
    cleanStyle,
    submitSingle() {
      this.$emit('submit', this.value);
    },
    markQues() {
      this.$emit('markQues', this.value);
    },
    selectItem(checked) {
      this.$emit('select', this.value, checked);
    },
    recCurrentQues() {
      this.$emit('recCurrentQues', this.value);
    },
    // 删除错题
    delItem() {
      this.$confirm('', this.$t('pc_ote_msg_confirmRemoveOneWrongQues'/* 确认将此题从错题本中移出吗？移出后，将不可恢复 */), {
        confirmButtonText: this.$t('pc_ote_btn_confirm'),
        cancelButtonText: this.$t('pc_ote_btn_cancel'),
        type: 'warning'
      }).then(() => {
        this.$emit('delete-ques', this.value);
      }).catch(() => {});
    },
    // 获取试题解析是否支持展开收起效果
    nowrapEleHeight() {
      if (this.value.explainText && this.$refs['nowrapEle_' + this.value.id] && this.$refs['nowrapEle_' + this.value.id].offsetHeight > NowrapHeight) {
        this.value.isShowMoreText = true;
      }
      this.$forceUpdate();
    },
    // 展开/收起切换
    changeMoreStatus(isShowMore) {
      this.value.isShowMore = !isShowMore;
      this.$forceUpdate();
    },
    playerMediaOptions(mediaPlay) {
      if (mediaPlay.tranStatus === 2) {
        const options = mediaPlay.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: mediaPlay.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    },
    convertASCIIForNum,
    isNotNullKeyword(keyword) {
      if (keyword && JSON.parse(keyword) && JSON.parse(keyword).length) {
        return true;
      }
      return false;
    },

    scrollView(selector) {
      const quesEl = this.$refs[selector] && this.$refs[selector][0];
      if (quesEl) {
        const scroller = getScrollParent(quesEl, true);
        // 20：是因为标题样式做了margin-top:20px;
        scroller.scrollTop = quesEl.offsetTop + 20;
      }
    },

    // 和 userQuesDetail文件一致添加空方法避免监控报错
    handlePaste() {}
  }
};
</script>

<style scoped lang="scss">

.max-height30 {
  max-height: 30px;
}

.split-bar-1px {
  height: 9px;
  border-right: 1px solid #d9d9d9;
}

.yxtulcdsdk-quesCard {
  &:hover {
    .icon-delete {
      visibility: visible;
    }
  }

  .nowrap-2 {
    max-height: 48px;
  }

  .rotated {
    transform: rotate(180deg);
  }
}

.no-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  word-wrap: normal;
  word-break: normal;
}

</style>
