<template>
  <yxtf-dialog
    v-if="ueId"
    :visible="!!info"
    width="520px"
    :cutline="false"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="() => back()"
    append-to-body
  >
    <div class="yxtulcdsdk-user-exam-end pb8">
      <div><img :src="remoteCdn + '/pc/img/exam-empty.png'" alt="" class="exam-empty"></div>
      <div class="mt20 standard-size-18 color-gray-10 yxt-weight-5 d-in-block">
        {{ hintMsg }}
      </div>
      <div class="mt16 color-gray-8">
        {{ info && shortDateTime(info.submitTime) || '--' }} {{ $t('pc_ote_lbl_sumitpaper') }}
      </div>
      <div v-if="isShowProgress" class="text-center mt24 color-gray-8">
        <yxtf-progress
          :percentage="numPer"
          :width="200"
          class="width200"
          :stroke-width="12"
          :show-text="false"
        />
        <div class="mt6 mb12">{{ $t('pc_ote_lbl_examresultgeting') }}</div>
      </div>
      <div v-else class="btn-group mt32">
        <!-- 退出考试 -->
        <yxtf-button
          v-if="isShowBackBtn"
          class="width220"
          type="primary"
          size="others"
          @click="back"
        >
          {{ $t('pc_ote_btn_exitexam') }}
        </yxtf-button>
      </div>
    </div>
  </yxtf-dialog>
</template>
<script>
import { getSubmitInfo } from '../../service/user.service';
import { shortDateTime } from '../../core/utils';
import { ExamUserStatusEnum } from '../../core/enums';
import { remoteCdn } from '../../configs/const';
export default {
  props: {
    // 用户试卷ID
    ueId: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      remoteCdn,
      arrId: '', // 考试安排ID
      uemId: '',
      info: null,
      numPer: 0,
      ticker: 1, // 100毫秒的percentage
      timer: null,
      isShowProgress: true, // 是否显示滚动条
      isShowBackBtn: false // 是否显示退出考试的按钮
    };
  },
  watch: {
    numPer(val) {
      if (val && (val % 25 === 0)) {
        getSubmitInfo(this.ueId, this.orgId).then(res => {
          this.batchId = res.batchId;
          this.info = res;
          if (res.userStatus === ExamUserStatusEnum.finished) {
            this.examEnd(true);
          } else if (val === 100) {
            this.isShowProgress = false;
            this.isShowBackBtn = true;
          }
        });
      }
      if (val >= 100) {
        window.clearInterval(this.timer);
      }
    },
    visible(val) {
      val && this.getDetail();
    }
  },
  computed: {
    hintMsg() {
      // 考试完成标准提示调整
      let msg = '';

      if (this.info) {
        switch (this.info.finishStandard) {
          case 0:
            msg = this.$t(this.info.needCheck === 1 ? 'pc_ote_msg_successandpending' : 'pc_ote_msg_successsubmit');
            break;
          case 1:
            msg = this.$t(this.info.needCheck === 1 ? 'pc_ote_msg_successandwaitformark' : 'pc_ote_msg_examsuccessandpassed');
            break;
          case 2:
            msg = this.$t('pc_ote_msg_successandwaitformarkpassed');
            break;
          default:
            break;
        }
      }

      return msg;
    },
    orgId() {
      return window.localStorage.getItem('orgId') || '';
    }
  },
  created() {
  },
  methods: {
    setTimer() {
      const vm = this;
      this.timer = setInterval(function() {
        vm.numPer += vm.ticker;
      }, 100);
    },
    getDetail() {
      getSubmitInfo(this.ueId, this.orgId).then(res => {
        this.arrId = res.arrId;
        this.uemId = res.uemId;
        this.batchId = res.batchId;
        if (res.userStatus === ExamUserStatusEnum.finished) {
          this.examEnd(true);
          return;
        } else if (res.needCheck === 0) {
          this.info = res;
          this.setTimer();
        } else {
          this.info = res;
          this.isShowProgress = false;
          this.isShowBackBtn = true;
        }
      }).catch(this.handleError);
    },
    examEnd(isToResult) {
      this.$emit('end', isToResult, this.info && this.info.userExamFinished);
    },
    back() {
      this.examEnd(false);
    },
    shortDateTime
  }
};
</script>
