<template>
  <user-exam-result
    :arrange-id="arrangeId"
    :user-exam-id="ueId"
    :batch-id="batchId"
    :deep-study="deepStudy"
    :query-datas="queryDatas"
    @changeStep="changeStep"
    @updateProgress="updateProgress"
  />
</template>
<script>
import UserExamResult from './statistic/UserExamResult.vue';
import deepStudyPage from '../mixins/deepStudyPage';
export default {
  components: {
    UserExamResult
  },
  mixins: [deepStudyPage],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      arrangeId: routeParams.arrangeId,
      batchId: routeParams.batchId, // 循环考试批次ID
      ueId: routeParams.ueId
    };
  }
};
</script>
