<template>
  <div class="yxtulcdsdk-form font-size-16">
    <template v-if="value.type === 5">
      <div class="wline">
        <div class="font-size-14 lh24" v-html="cleanStyle(value.content)"></div>
        <media-player
          v-if="value.fileId && value.questionType > 1"
          ref="mediaPlayer"
          :player-key="index"
          class="mt16"
          :type="value.questionType"
          :file-id="value.fileId"
          :options="playerOptions"
          :tran-status="value.tranStatus"
        />
      </div>
    </template>
    <template v-else>
      <div class="yxtulcdsdk-form__sub">
        {{ index + 1 }}.
      </div>
      <div class="yxtulcdsdk-form__content pl6">
        <div v-html="cleanStyle(value.content)"></div>
        <media-player
          v-if="value.fileId && value.questionType > 1"
          ref="mediaPlayer"
          :player-key="index"
          class="mt16"
          :type="value.questionType"
          :file-id="value.fileId"
          :options="playerOptions"
          :tran-status="value.tranStatus"
        />
        <div class="mt16">
          <template v-if="value.type === 0">
            <!-- 单选题 -->
            <yxtf-radio-group
              v-model="value.answer"
              class="mfl12 color-primary-6 yxtulcdsdk-ques-radio-group active mv-f8"
              :direction="'row'"
              :disabled="!!value.isAnswered ? 'lock' : false"
              @change="submitSingle()"
            >
              <template v-for="(i, j) in value.choiceItems">
                <yxtf-radio
                  :key="i.id"
                  :label="i.id"
                  class="yxtulcdsdk-ques-radio active hover-primary-6-i"
                >
                  <span>{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span>
                </yxtf-radio>
                <media-player
                  v-if="i.itemPlay && i.itemPlay.fileId"
                  :key="i.id"
                  ref="mediaPlayer"
                  :player-key="index"
                  class="mt4 ml35"
                  :type="i.itemPlay.type"
                  :file-id="i.itemPlay.fileId"
                  :options="playerMediaOptions(i.itemPlay)"
                  :tran-status="i.itemPlay.tranStatus"
                />
              </template>
            </yxtf-radio-group>
          </template>
          <template v-else-if="value.type === 1">
            <!-- 多选题 -->
            <yxtf-checkbox-group
              v-model="value.answers"
              :disabled="!!value.isAnswered"
              :direction="'row'"
              class="mfl12 mv-f8 color-primary-6 yxtulcdsdk-ques-checkbox-group active"
              @change="submitSingle()"
            >
              <template v-for="(i, j) in value.choiceItems">
                <yxtf-checkbox
                  :key="i.id"
                  :label="i.id"
                  class="yxtulcdsdk-ques-checkbox active hover-primary-6-i"
                >
                  <span>{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span>
                </yxtf-checkbox>
                <media-player
                  v-if="i.itemPlay && i.itemPlay.fileId"
                  :key="i.id"
                  ref="mediaPlayer"
                  :player-key="index"
                  class="mt4 ml35"
                  :type="i.itemPlay.type"
                  :file-id="i.itemPlay.fileId"
                  :options="playerMediaOptions(i.itemPlay)"
                  :tran-status="i.itemPlay.tranStatus"
                />
              </template>
            </yxtf-checkbox-group>
          </template>
          <template v-else-if="value.type === 2">
            <!-- 判断题 -->
            <yxtf-radio-group
              v-model="value.submitContent"
              :disabled="!!value.isAnswered ? 'lock' : false"
              :direction="'row'"
              class="mfl12 mv-f8 color-primary-6 yxtulcdsdk-ques-radio-group active"
              @change="submitSingle()"
            >
              <yxtf-radio :key="value.id + '1'" class="yxtulcdsdk-ques-radio active hover-primary-6-i" label="1">{{ value.judgeCorrectOptionContent ? value.judgeCorrectOptionContent : $t('pc_ote_lbl_correct') }}</yxtf-radio>
              <yxtf-radio :key="value.id + '0'" class="yxtulcdsdk-ques-radio active hover-primary-6-i" label="0">{{ value.judgeWrongOptionContent ? value.judgeWrongOptionContent : $t('pc_ote_lbl_wrong') }}</yxtf-radio>
            </yxtf-radio-group>
          </template>
          <template v-else-if="value.type === 3">
            <!-- 填空题 -->
            <div
              v-for="(i, j) in value.fillInItems"
              :key="i.id"
              :class="{'mt20': j > 0,'mt16': j === 0}"
              class="lh40 font-size-14 yxtulcdsdk-form"
            >
              <div class="yxtulcdsdk-form__sub">
                {{ j + 1 }}.
              </div>
              <div class="yxtulcdsdk-form__content pl10">
                <yxtf-input
                  v-model="i.submitAnswer"
                  class="max-w600"
                  :disabled="!!(value.isAnswered || value.needMark)"
                  maxlength="500"
                  @change="submitSingle()"
                  @blur="blurEvent(i, 'submitAnswer')"
                />
              </div>
            </div>
          </template>
          <template v-else-if="value.type === 4">
            <!-- 问答题 -->
            <!-- 问答题答案最多输入2000字 火狐不支持:autosize="{ minRows: 5, maxRows: 8}"-->
            <yxtf-input
              v-model="value.submitContent"
              :disabled="!!(value.isAnswered || value.needMark)"
              class="w-620"
              type="textarea"
              :rows="5"
              maxlength="2000"
              show-word-limit
              @change="submitSingle()"
              @blur="blurEvent(value, 'submitContent')"
            />
          </template>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import { SvgMediaUrl } from '../../configs/const';
import { convertASCIIForNum, cleanStyle } from '../../core/utils';
import MediaPlayer from '../components/mediaPlayer';
export default {
  name: 'UserPraQuesDetail',
  components: {
    MediaPlayer
  },
  props: {
    // 试题信息
    value: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 题序
    index: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      mediaUrl: SvgMediaUrl,
      currentComponent: this.value
    };
  },
  computed: {
    playerOptions() {
      if (this.value.tranStatus === 2) {
        const options = this.value.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: this.value.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    }
  },
  watch: {
    'value.fillInItems': {
      handler(val) {
        this.$emit('submitSingle', this.value);
      },
      immediate: true,
      deep: true
    },
    'value.submitAnswer': {
      handler(val) {
        this.$emit('submitSingle', this.value);
      }
    },
    'value.submitContent': {
      handler(val) {
        this.$emit('submitSingle', this.value);
      }
    }
  },
  created() {
  },
  methods: {
    cleanStyle,
    convertASCIIForNum,
    submitSingle() {
      this.$emit('submitSingle', this.value);
    },
    playerMediaOptions(mediaPlay) {
      if (mediaPlay.tranStatus === 2 && mediaPlay.playDetails) {
        const options = mediaPlay.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: mediaPlay.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    },

    blurEvent(answer, name) {
      this.$nextTick(() => {
        if (answer[name] && typeof answer[name] === 'string') {
          this.$set(answer, name, answer[name].trim());
        }
      });
    }
  }
};
</script>
