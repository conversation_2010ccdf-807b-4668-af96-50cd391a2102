<template>
  <!-- 单选题 -->
  <div v-if="value.type === 0">
    <yxt-row class="color-gray-10 mt20 mb5 clearfix flex lh32">
      <yxt-col :span="1" class="minwidth32 maxwidth38 yxtulcdsdk-flex-shrink-0">
        {{ index + 1 }}.
      </yxt-col>
      <yxt-col :span="23" class="pr pos_fl12">
        <span v-html="cleanStyle(value.content)"></span>
        <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :player-key="index"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxtf-radio-group v-model="value.answer" :direction="'row'" class="color-primary-6 ph24 yxtulcdsdk-ques-radio-group active">
      <yxtf-radio
        v-for="(i, j) in value.choiceItems"
        :key="i.id"
        :label="i.id"
        class="yxtulcdsdk-ques-radio"
        disabled="lock"
      >
        <div class="flex"><span class="vertical-align-top pr4 yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span></div>
        <media-player
          v-if="i.itemPlay && i.itemPlay.fileId"
          ref="mediaPlayer"
          width="640"
          height="480"
          class="mt12"
          :file-id="i.itemPlay.fileId"
          :options="playerMediaOptions(i.itemPlay)"
          :type="i.itemPlay.type"
          :tran-status="i.itemPlay.tranStatus"
        />
      </yxtf-radio>
    </yxtf-radio-group>
    <div v-show="showAnswer || showExplain" class="bg-gray-1 yxtulcdsdk-review-tag yxtbizf-br-4 yxtbizf-br-4 ph24 pv8 mt10">
      <!-- 答案 -->
      <yxt-row v-show="showAnswer" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <span>{{ $t('pc_ote_lbl_itemanswer') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1 ">
          <span class="color-gray-9">{{ value.correctAnswer }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain && showPoint" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 考点 -->
            <span>{{ $t('pc_ote_lbl_assesspoint') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span v-if="value.pointNames" class="color-gray-9">{{ value.pointNames }}</span>
          <span v-else class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 解析 -->
            <span>{{ $t('pc_ote_lbl_quesanalyze') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <div v-show="value.explainText || value.explainPlay">
            <div :ref="'nowrapEle_' + value.id" :class="value.isShowMoreText && !value.isShowMore ? 'color-gray-9 nowrap-2' : 'color-gray-9'">
              <span v-html="cleanStyle(value.explainText)"></span>
              <div v-if="value.explainPlay && value.explainPlay.fileId">
                <media-player
                  v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                  ref="mediaPlayer"
                  width="640"
                  height="480"
                  class="flex"
                  :class="value.explainText ? 'mt12' : ''"
                  :player-key="value.explainPlay.fileId"
                  :type="value.explainPlay.type"
                  :file-id="value.explainPlay.fileId"
                  :options="playerMediaOptions(value.explainPlay)"
                  :tran-status="value.explainPlay.tranStatus"
                />
              </div>
            </div>
            <p v-if="value.isShowMoreText" class="text-center color-primary-6 font-size-14 hand mt5 mb0" @click="changeMoreStatus(value.isShowMore, 0, index)">
              <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
              <span class="" :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'"></span>
            </p>
          </div>
          <div v-show="!value.explainText && !value.explainPlay" class="color-gray-9">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
        </yxt-col>
      </yxt-row>
    </div>
    <div v-if="!isMarking" class="yxtulcdsdk-ques-score_type">
      <span class="yxtulcdsdk-ques-score_input color-gray-9">
        {{ $t('pc_ote_lbl_markscore') }}：
      </span>
      <span class="yxtulcdsdk-ques-score_checed">
        <span v-if="value.faulted === 0" class="check-btn success mr12">
          <yxt-svg-icon
            icon-class="success"
            class-name="success"
            width="16px"
            height="16px"
          />
        </span>
        <span v-else class="check-btn error mr12">
          <yxt-svg-icon
            icon-class="error"
            class-name="error"
            width="16px"
            height="16px"
          />
        </span>
      </span>
      <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="yxtulcdsdk-ques-score_input">
        <input
          v-model="value.score"
          type="text"
          class="yxtulcdsdk-ques-score_input__item"
          disabled
        >
      </span>
    </div>
  </div>
  <!-- 多选题 -->
  <div v-else-if="value.type === 1">
    <yxt-row class="color-gray-10 mt20 mb5 clearfix flex lh32">
      <yxt-col :span="1" class="minwidth32 maxwidth38 yxtulcdsdk-flex-shrink-0">
        {{ index + 1 }}.
      </yxt-col>
      <yxt-col :span="23" class="pr pos_fl12">
        <span v-html="cleanStyle(value.content)"></span>
        <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="color-gray-8">
          （ {{ getQuesScoreDesc(value) }} ）
        </span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :player-key="index"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxtf-checkbox-group v-model="value.answers" :direction="'row'" class="color-primary-6 ph24 yxtulcdsdk-ques-checkbox-group active">
      <yxtf-checkbox
        v-for="(i, j) in value.choiceItems"
        :key="i.id"
        :label="i.id"
        class="yxtulcdsdk-ques-checkbox"
        disabled
      >
        <div class="flex"><span class="vertical-align-top pr4 yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span></div>
        <media-player
          v-if="i.itemPlay && i.itemPlay.fileId"
          ref="mediaPlayer"
          width="640"
          height="480"
          class="mt12"
          :file-id="i.itemPlay.fileId"
          :options="playerMediaOptions(i.itemPlay)"
          :type="i.itemPlay.type"
          :tran-status="i.itemPlay.tranStatus"
        />
      </yxtf-checkbox>
    </yxtf-checkbox-group>
    <div v-show="showAnswer || showExplain" class="bg-gray-1 yxtulcdsdk-review-tag yxtbizf-br-4 ph24 pv8 mt10">
      <!-- 答案 -->
      <yxt-row v-show="showAnswer" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <span>{{ $t('pc_ote_lbl_itemanswer') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span class="color-gray-9">{{ value.correctAnswer }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain && showPoint" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 考点 -->
            <span>{{ $t('pc_ote_lbl_assesspoint') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span v-if="value.pointNames" class="color-gray-9">{{ value.pointNames }}</span>
          <span v-else class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 解析 -->
            <span>{{ $t('pc_ote_lbl_quesanalyze') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <div v-show="value.explainText || value.explainPlay">
            <div :ref="'nowrapEle_' + value.id" :class="value.isShowMoreText && !value.isShowMore ? 'color-gray-9 nowrap-2' : 'color-gray-9'">
              <span v-html="cleanStyle(value.explainText)"></span>
              <div v-if="value.explainPlay && value.explainPlay.fileId">
                <media-player
                  v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                  ref="mediaPlayer"
                  width="640"
                  height="480"
                  class="flex"
                  :class="value.explainText ? 'mt12' : ''"
                  :player-key="value.explainPlay.fileId"
                  :type="value.explainPlay.type"
                  :file-id="value.explainPlay.fileId"
                  :options="playerMediaOptions(value.explainPlay)"
                  :tran-status="value.explainPlay.tranStatus"
                />
              </div>
            </div>
            <p v-if="value.isShowMoreText" class="text-center color-primary-6 font-size-14 hand mt5 mb0" @click="changeMoreStatus(value.isShowMore, 1, index)">
              <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
              <span class="" :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'"></span>
            </p>
          </div>
          <div v-show="!value.explainText && !value.explainPlay" class="color-gray-9">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
        </yxt-col>
      </yxt-row>
    </div>
    <div v-if="!isMarking" class="yxtulcdsdk-ques-score_type">
      <span class="yxtulcdsdk-ques-score_input color-gray-9">
        {{ $t('pc_ote_lbl_markscore') }}：
      </span>
      <span class="yxtulcdsdk-ques-score_checed">
        <span v-if="value.faulted === 0" class="check-btn success mr12">
          <yxt-svg-icon
            icon-class="success"
            class-name="success"
            width="16px"
            height="16px"
          />
        </span>
        <span v-else class="check-btn error mr12">
          <yxt-svg-icon
            icon-class="error"
            class-name="error"
            width="16px"
            height="16px"
          />
        </span>
      </span>
      <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="yxtulcdsdk-ques-score_input">
        <input
          v-model="value.score"
          type="text"
          disabled
          class="yxtulcdsdk-ques-score_input__item"
        >
      </span>
    </div>
  </div>
  <!-- 判断题 -->
  <div v-else-if="value.type === 2">
    <yxt-row class="color-gray-10 mt20 mb5 clearfix yxtulcdsdk-flex-center lh32">
      <yxt-col :span="1" class="minwidth32 maxwidth38 yxtulcdsdk-flex-shrink-0">
        {{ index + 1 }}.
      </yxt-col>
      <yxt-col :span="23" class="pr pos_fl12">
        <span v-html="cleanStyle(value.content)"></span>
        <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :player-key="index"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxtf-radio-group v-model="value.submitContent" :direction="'row'" class="color-primary-6 ph24 yxtulcdsdk-ques-radio-group">
      <yxtf-radio class="yxtulcdsdk-ques-radio" label="1" disabled="lock">{{ value.judgeCorrectOptionContent ? value.judgeCorrectOptionContent : $t('pc_ote_lbl_correct') }}</yxtf-radio>
      <yxtf-radio class="yxtulcdsdk-ques-radio" label="0" disabled="lock">{{ value.judgeWrongOptionContent ? value.judgeWrongOptionContent : $t('pc_ote_lbl_wrong') }}</yxtf-radio>
    </yxtf-radio-group>
    <div v-show="showAnswer || showExplain" class="bg-gray-1 yxtulcdsdk-review-tag yxtbizf-br-4 ph24 pv8 mt10">
      <!-- 答案 -->
      <yxt-row v-show="showAnswer" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <span>{{ $t('pc_ote_lbl_itemanswer') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span class="color-gray-9">{{ value.judgeAnswer === 0 ?(value.judgeWrongOptionContent ? value.judgeWrongOptionContent : $t('pc_ote_lbl_wrong')) : (value.judgeCorrectOptionContent ? value.judgeCorrectOptionContent : $t('pc_ote_lbl_correct')) }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain && showPoint" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 考点 -->
            <span>{{ $t('pc_ote_lbl_assesspoint') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span v-if="value.pointNames" class="color-gray-9">{{ value.pointNames }}</span>
          <span v-else class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 解析 -->
            <span>{{ $t('pc_ote_lbl_quesanalyze') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <div v-show="value.explainText || value.explainPlay">
            <div :ref="'nowrapEle_' + value.id" :class="value.isShowMoreText && !value.isShowMore ? 'color-gray-9 nowrap-2' : 'color-gray-9'">
              <span v-html="cleanStyle(value.explainText)"></span>
              <div v-if="value.explainPlay && value.explainPlay.fileId">
                <media-player
                  v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                  ref="mediaPlayer"
                  width="640"
                  height="480"
                  class="flex"
                  :class="value.explainText ? 'mt12' : ''"
                  :player-key="value.explainPlay.fileId"
                  :type="value.explainPlay.type"
                  :file-id="value.explainPlay.fileId"
                  :options="playerMediaOptions(value.explainPlay)"
                  :tran-status="value.explainPlay.tranStatus"
                />
              </div>
            </div>
            <p v-if="value.isShowMoreText" class="text-center color-primary-6 font-size-14 hand mt5 mb0" @click="changeMoreStatus(value.isShowMore, 2, index)">
              <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
              <span class="" :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'"></span>
            </p>
          </div>
          <div v-show="!value.explainText && !value.explainPlay" class="color-gray-9">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
        </yxt-col>
      </yxt-row>
    </div>
    <div v-if="!isMarking" class="yxtulcdsdk-ques-score_type">
      <span class="yxtulcdsdk-ques-score_input color-gray-9">
        {{ $t('pc_ote_lbl_markscore') }}：
      </span>
      <span class="yxtulcdsdk-ques-score_checed">
        <span v-if="value.faulted === 0" class="check-btn success mr12">
          <yxt-svg-icon
            icon-class="success"
            class-name="success"
            width="16px"
            height="16px"
          />
        </span>
        <span v-else class="check-btn error mr12">
          <yxt-svg-icon
            icon-class="error"
            class-name="error"
            width="16px"
            height="16px"
          />
        </span>
      </span>
      <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="yxtulcdsdk-ques-score_input">
        <input
          v-model="value.score"
          type="text"
          disabled
          class="yxtulcdsdk-ques-score_input__item"
        >
      </span>
    </div>
  </div>
  <!-- 填空题 -->
  <div v-else-if="value.type === 3">
    <yxt-row class="color-gray-10 mt20 mb5 clearfix yxtulcdsdk-flex-center lh32">
      <yxt-col :span="1" class="minwidth32 maxwidth38 yxtulcdsdk-flex-shrink-0">
        {{ index + 1 }}.
      </yxt-col>
      <yxt-col :span="23" class="pr pos_fl12">
        <span v-html="cleanStyle(value.content)"></span>
        <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :player-key="index"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxt-row v-for="(i, j) in value.fillInItems" :key="i.id" class="lh40 ph24 mt20">
      <yxt-col :span="1">
        {{ j + 1 }}.
      </yxt-col>
      <yxt-col :span="17">
        <yxtf-input v-model="i.submitAnswer" :disabled="true" />
      </yxt-col>
      <yxt-col v-show="value.fillMarkByItem ===1 && !isMarking" :span="6">
        <div class="yxtulcdsdk-ques-score__item"><span class="yxtulcdsdk-ques-score__i">{{ i.score }}</span><span>{{ $t('pc_ote_lbl_score') }}</span></div>
      </yxt-col>
      <!-- 暂时用这个隐藏 最快上线-->
      <!--      <yxt-col :span="6" class="visibility-hidden">-->
      <!--        <span v-if="i.faulted === 0" class="check-btn success">-->
      <!--          <yxt-svg-icon icon-class="success"-->
      <!--                        class-name="success"-->
      <!--                        width="16px"-->
      <!--                        height="16px"></yxt-svg-icon>-->
      <!--        </span>-->
      <!--        <span v-else class="check-btn error">-->
      <!--          <yxt-svg-icon icon-class="error"-->
      <!--                        class-name="error"-->
      <!--                        width="17px"-->
      <!--                        height="15px"></yxt-svg-icon>-->
      <!--        </span>-->
      <!--      </yxt-col>-->
    </yxt-row>
    <div v-show="showAnswer || showExplain" class="bg-gray-1 yxtulcdsdk-review-tag yxtbizf-br-4 ph24 pv8 mt10">
      <!-- 答案 -->
      <yxt-row v-if="showAnswer" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <span>{{ $t('pc_ote_lbl_itemanswer') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span class="color-gray-9">{{ value.correctAnswer ? value.correctAnswer : $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain && showPoint" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 考点 -->
            <span>{{ $t('pc_ote_lbl_assesspoint') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span v-if="value.pointNames" class="color-gray-9">{{ value.pointNames }}</span>
          <span v-else class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 解析 -->
            <span>{{ $t('pc_ote_lbl_quesanalyze') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <div v-show="value.explainText || value.explainPlay">
            <div :ref="'nowrapEle_' + value.id" :class="value.isShowMoreText && !value.isShowMore ? 'color-gray-9 nowrap-2' : 'color-gray-9'">
              <span v-html="cleanStyle(value.explainText)"></span>
              <div v-if="value.explainPlay && value.explainPlay.fileId">
                <media-player
                  v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                  ref="mediaPlayer"
                  width="640"
                  height="480"
                  class="flex"
                  :class="value.explainText ? 'mt12' : ''"
                  :player-key="value.explainPlay.fileId"
                  :type="value.explainPlay.type"
                  :file-id="value.explainPlay.fileId"
                  :options="playerMediaOptions(value.explainPlay)"
                  :tran-status="value.explainPlay.tranStatus"
                />
              </div>
            </div>
            <p v-if="value.isShowMoreText" class="text-center color-primary-6 font-size-14 hand mt5 mb0" @click="changeMoreStatus(value.isShowMore, 3, index)">
              <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
              <span class="" :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'"></span>
            </p>
          </div>
          <div v-show="!value.explainText && !value.explainPlay" class="color-gray-9">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
        </yxt-col>
      </yxt-row>
    </div>

    <div v-show="value.fillMarkByItem ===0 && !isMarking" class="yxtulcdsdk-ques-score_type">
      <span class="yxtulcdsdk-ques-score_input color-gray-9">{{ $t('pc_ote_lbl_markscore') }}：</span>
      <span class="yxtulcdsdk-ques-score_checed">
        <span v-if="value.faulted === 1" class="check-btn error mr12">
          <yxt-svg-icon
            icon-class="error"
            class-name="error"
            width="16px"
            height="16px"
          />
        </span>
        <span v-else class="check-btn success mr12">
          <yxt-svg-icon
            icon-class="success"
            class-name="success"
            width="17px"
            height="15px"
          />
        </span>
      </span>
      <span v-if="value.fillMarkByItem === 0 &&!(routeSrc === 2 && info.viewResultScore === 0)" class="yxtulcdsdk-ques-score_input">

        <input
          v-model="value.score"
          type="text"
          disabled
          class="yxtulcdsdk-ques-score_input__item"
        >
      </span>
    </div>
  </div>
  <!-- 问答题 -->
  <div v-else-if="value.type === 4">
    <yxt-row class="color-gray-10 mt20 mb5 clearfix yxtulcdsdk-flex-center lh32">
      <yxt-col :span="1" class="minwidth32 maxwidth38 yxtulcdsdk-flex-shrink-0">
        {{ index + 1 }}.
      </yxt-col>
      <yxt-col :span="23" class="pr pos_fl12">
        <span v-html="cleanStyle(value.content)"></span>
        <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :player-key="index"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxt-row class="lh40 ph24 mt20 mb12">
      <!-- 用户答案 -->
      <yxt-col v-if="value.submitContent" :span="24">
        <yxt-scrollbar class="yxtulcdsdk-ques-scrollbar">
          <div :class="exporting ? '' : 'yxtulcdsdk-ques-answer__style'" class="yxtulcdsdk-ques-answer" v-html="dealLineFeed(value.submitContent || $t('pc_ote_lbl_noanswer'), true)"></div>
        </yxt-scrollbar>
      </yxt-col>
      <!-- 附件 -->
      <yxt-col v-if="value.attachments && value.attachments.length > 0" :span="24">
        <div
          v-if="value.attachments.length > 1"
          class="color-primary-6 lh22 hand"
          :class="{'mt24': value.submitContent}"
          @click="downloadAllFile"
        >{{ $t('pc_ote_lbl_download_attach' /* 下载所有附件 */) }}</div>
        <div class="yxtulcdsdk-ques-reference mb12 mt8">
          <template v-for="(fileInfo) in value.attachments">
            <Reference
              :key="fileInfo.fileId"
              :reference="fileInfo"
              @detail="viewDetail(fileInfo)"
              @download="downloadFile(fileInfo)"
            />
          </template>
        </div>

        <file-big-viewer
          v-if="showFile"
          ref="bigViewer"
          :file-id="fileInfo.fileId"
          :file-type="fileInfo.fileType"
          :image-url="fileInfo.imageUrl"
          :file-info="fileInfo.lists"
          :video-options="fileInfo.videoOptions"
        />
      </yxt-col>
    </yxt-row>
    <yxt-row v-if="!isMarking" class="lh40 ph24 mb20">
      <yxt-col :span="24" class="color-gray-9">
        {{ $t('pc_ote_lbl_markcontent' /* 批阅评语 */) }}
      </yxt-col>
      <yxt-col :span="18">
        <div v-if="value.markType === 1" class="yxtulcdsdk-ote-ai-content">
          <div class="color-gray-10 lh22">{{ value.remark }}</div>
          <div class="color-gray-7 font-size-12 lh20 mt12">{{ $t('pc_biz_common_generate_by_ai')/** 内容由AI生成，仅供参考 */ }}</div>
        </div>
        <yxtf-input
          v-else
          v-model="value.remark"
          type="textarea"
          :disabled="true"
          :autosize="{ minRows: 4, maxRows: 8}"
        />
      </yxt-col>
    </yxt-row>
    <div v-show="showAnswer || showExplain" class="bg-gray-1 yxtulcdsdk-review-tag yxtbizf-br-4 ph24 pv8 mt10">
      <!-- 关键词 -->
      <yxt-row v-if="isNotNullKeyword(value.answerKeyword) && showAnswer" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <span>{{ $t('pc_ote_lbl_keyword') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1 color-gray-9">
          {{ JSON.parse(value.answerKeyword).map(item => item.name).join(';') }}
        </yxt-col>
      </yxt-row>
      <!-- 答案 -->
      <yxt-row v-show="showAnswer" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <span>{{ $t('pc_ote_lbl_itemanswer') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <div v-if="value.answerContent || value.answerPlay">
            <span v-if="value.answerContent" class="color-gray-9" v-html="cleanStyle(value.answerContent, true)"></span>
            <media-player
              v-if="value.answerPlay && value.answerPlay.fileId && value.answerPlay.type > 1"
              ref="mediaPlayer"
              width="640"
              height="480"
              :class="value.answerContent ? 'mt12' : ''"
              :player-key="value.answerPlay.fileId"
              :type="value.answerPlay.type"
              :file-id="value.answerPlay.fileId"
              :options="playerMediaOptions(value.answerPlay)"
              :tran-status="value.answerPlay.tranStatus"
            />
          </div>
          <div v-if="!value.answerContent && !value.answerPlay" class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</div>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain && showPoint" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 考点 -->
            <span>{{ $t('pc_ote_lbl_assesspoint') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span v-if="value.pointNames" class="color-gray-9">{{ value.pointNames }}</span>
          <span v-else class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
      <yxt-row v-show="showExplain" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 解析 -->
            <span>{{ $t('pc_ote_lbl_quesanalyze') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <div v-show="value.explainText || value.explainPlay">
            <div :ref="'nowrapEle_' + value.id" :class="value.isShowMoreText && !value.isShowMore ? 'color-gray-9 nowrap-2' : 'color-gray-9'">
              <span v-html="cleanStyle(value.explainText)"></span>
              <div v-if="value.explainPlay && value.explainPlay.fileId">
                <media-player
                  v-if="value.explainPlay && value.explainPlay.fileId && value.explainPlay.type > 1"
                  ref="mediaPlayer"
                  width="640"
                  height="480"
                  class="flex"
                  :class="value.explainText ? 'mt12' : ''"
                  :player-key="value.explainPlay.fileId"
                  :type="value.explainPlay.type"
                  :file-id="value.explainPlay.fileId"
                  :options="playerMediaOptions(value.explainPlay)"
                  :tran-status="value.explainPlay.tranStatus"
                />
              </div>
            </div>
            <p v-if="value.isShowMoreText" class="text-center color-primary-6 font-size-14 hand mt5 mb0" @click="changeMoreStatus(value.isShowMore, 4, index)">
              <span>{{ value.isShowMore ? $t('pc_ote_btn_putaway') : $t('pc_ote_btn_expand') }}</span>
              <span class="" :class="value.isShowMore ? 'yxt-icon-arrow-up' : 'yxt-icon-arrow-down'"></span>
            </p>
          </div>
          <div v-show="!value.explainText && !value.explainPlay" class="color-gray-9">{{ $t('pc_ote_lbl_noquestionanalysis') }}</div>
        </yxt-col>
      </yxt-row>
    </div>
    <div v-if="!isMarking" class="yxtulcdsdk-ques-score_type">
      <span class="yxtulcdsdk-ques-score_input color-gray-9">{{ $t('pc_ote_lbl_markscore') }}：</span>
      <span class="yxtulcdsdk-ques-score_checed">
        <span v-if="value.faulted === 1" class="check-btn error mr12">
          <yxt-svg-icon
            icon-class="error"
            class-name="error"
            width="16px"
            height="16px"
          />
        </span>
        <span v-else class="check-btn success mr12">
          <yxt-svg-icon
            icon-class="success"
            class-name="success"
            width="17px"
            height="15px"
          />
        </span>
      </span>
      <span v-if="!(routeSrc === 2 && info.viewResultScore === 0)" class="yxtulcdsdk-ques-score_input">
        <input
          v-model="value.score"
          type="text"
          disabled
          class="yxtulcdsdk-ques-score_input__item"
        >
      </span>
    </div>
  </div>
  <!-- 组合题,只包含题干部分 -->
  <div v-else-if="value.type === 5">
    <yxt-row class="color-gray-10 mb4 clearfix lh32">
      <yxt-col :span="24">
        <span v-html="cleanStyle(value.content)"></span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :player-key="index"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <div v-show="showAnswer || showExplain" class="bg-gray-1 yxtulcdsdk-review-tag yxtbizf-br-4 ph24 pv8 mt10">
      <yxt-row v-show="showExplain" class="mv16 yxtulcdsdk-flex-center">
        <yxt-col :class="'w-auto'">
          <yxtf-tag class="yxtulcdsdk-pc-marking-tag">
            <!-- 考点 -->
            <span>{{ $t('pc_ote_lbl_assesspoint') }}</span>
          </yxtf-tag>
        </yxt-col>
        <yxt-col :class="'w-auto'" class="pr pos_fl5 flex-g-1 yxtulcdsdk-flex-shrink-1">
          <span v-if="value.pointNames" class="color-gray-9">{{ value.pointNames }}</span>
          <span v-else class="color-gray-9">{{ $t('pc_ote_lbl_none') }}</span>
        </yxt-col>
      </yxt-row>
    </div>
  </div>
</template>
<script>
import { getDownloadUrl, postFileBatchDown } from '../../service/user.service';
import { SvgMediaUrl } from '../../configs/const';
import { cleanStyle, convertASCIIForNum, downFileUrl, dealLineFeed, getQuesScoreDesc } from '../../core/utils';
import MediaPlayer from '../components/mediaPlayer/index.vue';
import filePlay from '../../mixins/filePlay';
import FileBigViewer from '../components/FileBigViewer';
import YxtSvgIcon from '../components/svgIcon.vue';
import Reference from '../components/attachment/Reference';

export default {
  name: 'UserQuesDetail',
  components: {
    MediaPlayer,
    FileBigViewer,
    YxtSvgIcon,
    Reference
  },
  mixins: [filePlay],
  props: {
    // 试题信息
    value: {
      type: Object,
      default: () => {
        return {};
      }
    },
    ueId: {
      type: String,
      default: ''
    },
    // 考试信息
    info: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 题序
    index: {
      type: Number,
      default: 0
    },
    // 是否允许上传
    allowUpload: {
      type: Number,
      default: 0
    },
    routeSrc: {
      type: Number,
      default: 0
    },
    showAnswer: {
      type: Boolean,
      default: false
    },
    onlyWrong: {
      type: Boolean,
      default: false
    },
    showExplain: {
      type: Boolean,
      default: false
    },
    showPoint: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      nowrapHeight: 38,
      mediaUrl: SvgMediaUrl
    };
  },
  computed: {
    playerOptions() {
      if (this.value.tranStatus === 2) {
        const options = this.value.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: this.value.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    },

    exporting() {
      return ~~this.$route.query.exporting === 1;
    },
    // 答卷是否处于批阅中
    isMarking() {
      return this.info.status <= 3;
    }
  },
  mounted() {
    if (!this.$route.query.exporting) {
      this.nowrapEleHeight();
    }
  },
  methods: {
    cleanStyle,
    dealLineFeed,
    // 下载附件
    downloadFile({ fileId, name }) {
      getDownloadUrl(this.ueId, fileId).then(res => {
        if (res && res.downloadUrl) {
          downFileUrl(name, res.downloadUrl);
        }
      }).catch(this.handleError);
    },
    // 获取试题解析是否支持展开收起效果
    nowrapEleHeight() {
      if (this.value.explainText && this.$refs['nowrapEle_' + this.value.id] && this.$refs['nowrapEle_' + this.value.id].offsetHeight > this.nowrapHeight) {
        this.value.isShowMoreText = true;
      }
      this.$forceUpdate();
    },
    // 展开/收起切换
    changeMoreStatus(isShowMore, type, index) {
      this.value.isShowMore = !isShowMore;
      this.$forceUpdate();
    },
    playerMediaOptions(mediaPlay) {
      if (mediaPlay.tranStatus === 2 && mediaPlay.playDetails) {
        const options = mediaPlay.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: mediaPlay.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    },
    getQuesScoreDesc,
    convertASCIIForNum,
    isNotNullKeyword(keyword) {
      if (keyword && JSON.parse(keyword) && JSON.parse(keyword).length) {
        return true;
      }
      return false;
    },

    // 生成key的对象
    getNewFileKeyInfo(attachments) {
      let attachInfo = {};
      let fileIds = [];

      attachments.forEach(item => {
        fileIds.push(item.fileId);
        attachInfo[item.fileId] = item;
      });

      return { attachInfo, fileIds };
    },

    // 下载所有附件
    downloadAllFile() {
      const { attachInfo, fileIds } = this.getNewFileKeyInfo(this.value.attachments);

      postFileBatchDown({ fileIds }).then(fileInfo => {
        if (fileInfo && fileInfo.downFiles && fileInfo.downFiles.length) {
          fileInfo.downFiles.forEach(info => {
            downFileUrl(attachInfo[info.fileId].name, info.downloadUrl);
          });

        }
      }).catch(this.handleError);
    }
  }
};
</script>
