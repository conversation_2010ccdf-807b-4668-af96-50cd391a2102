<template>
  <!-- 单选题 -->
  <div v-if="value.type === 0">
    <div class="color-gray-10 mb4 clearfix standard-size-14 flex">
      <div class="w32 yxtulcdsdk-flex-shrink-0">{{ index + 1 }}.</div>
      <div class="pr flex-1">
        <span v-html="cleanStyle(value.content)"></span>
        <span v-if="!practice" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </div>
      <div v-if="!practice" class="width100">
        <span class="pull-right hand" @click="markQues()">
          <yxtf-svg
            :remote-url="mediaUrl"
            :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
            :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
            width="16px"
            height="16px"
          />
        </span>
      </div>
    </div>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxtf-radio-group
      v-model="value.answer"
      :disabled="(!!value.isAnswered && practice) ? 'lock' : false"
      :direction="'row'"
      class="color-primary-6 ph24 yxtulcdsdk-ques-radio-group active"
      @change="submitSingle()"
    >
      <template v-for="(i, j) in value.choiceItems">
        <yxtf-radio
          :key="i.id"
          :label="i.id"
          class="yxtulcdsdk-ques-radio active hover-primary-6-i"
        >
          <div class="flex"><span class="vertical-align-top pr4 yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span></div>
        </yxtf-radio>
        <media-player
          v-if="i.itemPlay && i.itemPlay.fileId"
          :key="i.id"
          ref="mediaPlayer"
          width="640"
          height="480"
          class="mt4 ml35"
          :file-id="i.itemPlay.fileId"
          :options="playerMediaOptions(i.itemPlay)"
          :type="i.itemPlay.type"
          :tran-status="i.itemPlay.tranStatus"
        />
      </template>
    </yxtf-radio-group>
  </div>
  <!-- 多选题 -->
  <div v-else-if="value.type === 1">
    <div class="color-gray-10 mb4 clearfix standard-size-14 flex">
      <div class="w32 yxtulcdsdk-flex-shrink-0">{{ index + 1 }}.</div>
      <div class="pr flex-1">
        <span v-html="cleanStyle(value.content)"></span> <span v-if="!practice" class="color-gray-8">
          （ {{ getQuesScoreDesc(value) }} ）
        </span>
      </div>
      <div v-if="!practice" class="width100">
        <span class="pull-right hand" @click="markQues()">
          <yxtf-svg
            :remote-url="mediaUrl"
            :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
            :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
            width="16px"
            height="16px"
          />
        </span>
      </div>
    </div>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxtf-checkbox-group
      v-model="value.answers"
      :disabled="!!value.isAnswered && practice "
      :direction="'row'"
      class="color-primary-6 ph24 yxtulcdsdk-ques-checkbox-group active"
      @change="submitSingle()"
    >
      <template v-for="(i, j) in value.choiceItems">
        <yxtf-checkbox
          :key="i.id"
          :label="i.id"
          class="yxtulcdsdk-ques-checkbox active hover-primary-6-i"
        >
          <div class="flex"><span class="vertical-align-top pr4 yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(j) }}. </span><span v-html="cleanStyle(i.itemContent, true)"></span></div>
        </yxtf-checkbox>
        <media-player
          v-if="i.itemPlay && i.itemPlay.fileId"
          :key="i.id"
          ref="mediaPlayer"
          width="640"
          height="480"
          class="mt4 ml35"
          :file-id="i.itemPlay.fileId"
          :options="playerMediaOptions(i.itemPlay)"
          :type="i.itemPlay.type"
          :tran-status="i.itemPlay.tranStatus"
        />
      </template>
    </yxtf-checkbox-group>
  </div>
  <!-- 判断题 -->
  <div v-else-if="value.type === 2">
    <div class="color-gray-10 mb4 clearfix flex lh32">
      <div class="w32 yxtulcdsdk-flex-shrink-0">{{ index + 1 }}.</div>
      <div class="pr flex-1">
        <span v-html="cleanStyle(value.content)"></span> <span v-if="!practice" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </div>
      <div v-if="!practice" class="width100">
        <span class="pull-right hand" @click="markQues()">
          <yxtf-svg
            :remote-url="mediaUrl"
            :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
            :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
            width="16px"
            height="16px"
          />
        </span>
      </div>
    </div>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxtf-radio-group
      v-model="value.submitContent"
      :disabled="(!!value.isAnswered && practice) ? 'lock' : false"
      :direction="'row'"
      class="color-primary-6 ph24 yxtulcdsdk-ques-radio-group active"
      @change="submitSingle()"
    >
      <yxtf-radio :key="value.id + '1'" class="yxtulcdsdk-ques-radio active hover-primary-6-i" label="1">{{ value.judgeCorrectOptionContent ? value.judgeCorrectOptionContent : $t('pc_ote_lbl_correct') }}</yxtf-radio>
      <yxtf-radio :key="value.id + '0'" class="yxtulcdsdk-ques-radio active hover-primary-6-i" label="0">{{ value.judgeWrongOptionContent ? value.judgeWrongOptionContent : $t('pc_ote_lbl_wrong') }}</yxtf-radio>
    </yxtf-radio-group>
  </div>
  <!-- 填空题 -->
  <div v-else-if="value.type === 3">
    <div class="color-gray-10 mb4 clearfix flex lh32">
      <div class="w32 yxtulcdsdk-flex-shrink-0">{{ index + 1 }}.</div>
      <div class="pr flex-1"><span v-html="cleanStyle(value.content)"></span> <span v-if="!practice" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span></div>
      <div v-if="!practice" class="width100">
        <span class="pull-right hand" @click="markQues()">
          <yxtf-svg
            :remote-url="mediaUrl"
            :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
            :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
            width="16px"
            height="16px"
          />
        </span>
      </div>
    </div>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxt-row v-for="(i, j) in value.fillInItems" :key="i.id" class="lh40 ph32 mt16">
      <yxt-col :span="1" class="width24">{{ j + 1 }}.</yxt-col>
      <yxt-col :span="17">
        <yxtf-input
          v-model="i.submitAnswer"
          :disabled="!!value.isAnswered && practice "
          class="clazz_width_80"
          maxlength="500"
          @change="submitSingle()"
          @paste.native.capture.prevent="handlePaste"
          @focus="recCurrentQues()"
        />
      </yxt-col>
    </yxt-row>
  </div>
  <!-- 问答题 -->
  <div v-else-if="value.type === 4">
    <div class="color-gray-10 mb4 clearfix lh32 flex">
      <div class="w32 yxtulcdsdk-flex-shrink-0">{{ index + 1 }}.</div>
      <div class="pr flex-1">
        <span v-html="cleanStyle(value.content)"></span> <span v-if="!practice" class="color-gray-8">（ {{ getQuesScoreDesc(value) }} ）</span>
      </div>
      <div v-if="!practice" class="width100">
        <span class="pull-right hand" @click="markQues()">
          <yxtf-svg
            :remote-url="mediaUrl"
            :icon-class="value.isMarked === 1 ? 'flag' : 'flag-o'"
            :class="value.isMarked === 1 ? 'color-red-6' : 'color-gray-7'"
            width="16px"
            height="16px"
          />
        </span>
      </div>
    </div>
    <media-player
      v-if="value.fileId"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
    <yxt-row class="lh40 ph32 mt16">
      <yxt-col :span="18">
        <!-- 问答题答案最多输入2000字 -->
        <yxtf-input
          v-model="value.submitContent"
          :disabled="!!value.isAnswered && practice"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 8}"
          maxlength="2000"
          show-word-limit
          @change="submitSingle()"
          @paste.native.capture.prevent="handlePaste"
          @focus="recCurrentQues()"
        />
      </yxt-col>
      <!-- 问答题附件最多上传5个 -->
      <yxt-col :span="24">
        <yxtbiz-upload
          v-if="allowUpload === 1"
          ref="bizUpload"
          app-code="ote"
          config-key="AttachConfigKey"
          module-name="ote"
          function-name="userexam"
          :on-ready="onReady"
          :error-tip="true"
          :ticket="ticket"
          :before-upload="beforeUpload"
          :on-progress="onProgress"
          :files-filter="filesFilter"
          :on-uploaded="onUploaded"
          :on-error="onError"
          filters=".w4v,.m4a,.wma,.wav,.mp3,.amr,.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.mts,.m2ts,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pps,.pdf,.jpg,.jpeg,.gif,.png,.bmp,.ico,.zip,.rar"
          :files-added="filesAdded"
        >
          <!-- 上传附件 -->
          <yxtf-button
            plain
            class="mt12"
            icon="yxtf-icon-upload2"
            @click.stop.native="clickIcon($event)"
          >{{ $t('pc_ote_btn_uploadattach') }}</yxtf-button>
        </yxtbiz-upload>

        <div class="yxtulcdsdk-ques-reference mt20">
          <template v-for="(fileInfo, j) in value.attach">
            <Reference
              :key="fileInfo.fileId"
              :reference="fileInfo"
              :percent="fileInfo.percent"
              is-edit
              @del="delFile($event, j)"
              @download="downloadFile(fileInfo)"
            />
          </template>
        </div>
      </yxt-col>
    </yxt-row>
  </div>
  <!-- 组合题,只包含题干部分 -->
  <div v-else-if="value.type === 5">
    <yxt-row class="color-gray-10 mb4 clearfix lh32">
      <yxt-col :span="24">
        <span v-html="cleanStyle(value.content)"></span>
      </yxt-col>
    </yxt-row>
    <media-player
      v-if="value.fileId && value.quesType > 1"
      ref="mediaPlayer"
      width="640"
      height="480"
      class="mt16 mb10"
      :file-id="value.fileId"
      :options="playerOptions"
      :type="value.quesType"
      :tran-status="value.tranStatus"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { getDownloadUrl } from '../../service/user.service';
import { SvgMediaUrl } from '../../configs/const';
import { convertASCIIForNum, cleanStyle, downFileUrl, getQuesScoreDesc } from '../../core/utils';
import MediaPlayer from '../components/mediaPlayer';
import Reference from '../components/attachment/Reference';

export default {
  name: 'UserQuesDetail',
  components: {
    MediaPlayer,
    Reference
  },
  props: {
    // 试题信息
    value: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 题序
    index: {
      type: Number,
      default: 0
    },
    // 是否允许上传
    allowUpload: {
      type: Number,
      default: 0
    },
    ueId: {
      type: String,
      default: ''
    },
    // 是否练习
    practice: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      mediaUrl: SvgMediaUrl,
      currentFile: null
    };
  },
  computed: {
    ...mapState({
      ticket: state => state.ticket
    }),
    playerOptions() {
      if (this.value.tranStatus === 2) {
        const options = this.value.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: this.value.fileId,
            resolution: option.desc
          };
        });
      }
      return [{
        fileFullUrl: '',
        label: ''
      }];
    }
  },
  watch: {
    'value.fillInItems': {
      handler(val) {
        if (this.practice) {
          this.$emit('submitSingle', this.value);
        }
      },
      immediate: true,
      deep: true
    },
    'value.submitAnswer': {
      handler(val) {
        if (this.practice) {
          this.$emit('submitSingle', this.value);
        }
      }
    }
  },
  created() {
  },
  methods: {
    cleanStyle,
    markQues() {
      this.$emit('markQues', this.value);
    },
    trimInput() {
      try {
        // 去除收尾空格
        if (this.value.type === 3) {
          this.value.fillInItems.forEach((item) => {
            item.submitAnswer = (item.submitAnswer && item.submitAnswer.trim()) || '';
          });
        } else if (this.value.type === 4) {
          this.value.submitContent = (this.value.submitContent && this.value.submitContent.trim()) || '';
        }
      } catch (error) {
      }
    },
    submitSingle() {
      this.trimInput();
      this.$emit('submitSingle', this.value);
    },
    recCurrentQues() {
      this.$emit('recCurrentQues', this.value);
    },
    delFile(reference, j) {
      this.$confirm('', this.$t('pc_ote_msg_deleteattach'), {
        confirmButtonText: this.$t('pc_ote_btn_confirm'),
        cancelButtonText: this.$t('pc_ote_btn_cancel'),
        type: 'warning'
      }).then(() => {
        this.value.attach = this.value.attach.filter(item => {
          if (item.status === 3 && item.fileId === reference.fileId) {
            this.$refs.bizUpload && this.$refs.bizUpload.remove(this.currentFile);
          }

          return item.fileId !== reference.fileId;
        });
        // 文件上传中被移除了之后把icon变为可以上传的
        this.value.uploadDisabled = 0;
        this.submitSingle(this.value);
      }).catch(() => {});
    },
    onReady() {},
    beforeUpload(file) {
      this.value.uploadDisabled = 1;

      this.value.attach.push({
        fileId: file.uuid,
        fileType: file.fileType,
        fileSize: file.size,
        name: file.name,
        status: 3, // 代表上传中的状态
        percent: 0
      });
    },
    filesFilter(files) {
      this.currentFile = files[0];
      return files;
    },
    onProgress(file, progress, event) {
      const per = (progress * 100).toFixed(0);
      this.value.attach.forEach((item, index) => {
        if (item.fileId === file.uuid) {
          this.$set(this.value.attach[index], 'percent', per);
        }
      });
    },
    getQuesScoreDesc,
    onUploaded(file) {
      try {
        if (this.value.attach && this.value.attach.length >= 5) {
          this.value.uploadDisabled = 1;
        } else {
          this.value.uploadDisabled = 0;
        }

        // 上传完成后进行赋值
        this.updateDataInfo(this.handleFile(file));

        // 上传附件提交
        this.$nextTick(() => {
          this.submitSingle(this.value);
        });
      } catch (error) {
        console.log(error);
      }
    },
    onError() {
      this.value.uploadDisabled = 0;

      this.value.attach = this.value.attach.filter(item => {
        return item.fileId !== this.currentFile.uuid;
      });
      // 上传失败后把当前file对象置为空
      this.currentFile = null;
      this.$message.error(this.$t('pc_ote_lbl_upload_failed' /* 上传失败 */));
    },

    updateDataInfo(obj) {
      this.value.attach.forEach(async(item, index) => {
        if (item.fileId === obj.uuid) {
          if (obj.fileType === 'image') {
            obj.viewUrl = obj.url;
          }
          this.$set(this.value.attach, index, obj);
        }
      });

      this.currentFile = null;
    },

    handleFile(file) {
      const fileType = file.fileType;
      return {
        fileId: file.id,
        uuid: file.uuid,
        fileType,
        fileSubType: file.fileClass, // 子类型
        url: file.fullUrl,
        name: file.name,
        fileName: file.name,
        fileSize: file.size,
        status: 1 // 0-成功 1-待处理 2-处理中 3-处理失败
      };
    },
    // 下载附件
    downloadFile({ fileId, name }) {
      getDownloadUrl(this.ueId, fileId).then(res => {
        if (res && res.downloadUrl) {
          downFileUrl(name, res.downloadUrl);
        }
      }).catch(this.handleError);
    },
    playerMediaOptions(mediaPlay) {
      if (mediaPlay.tranStatus === 2 && mediaPlay.playDetails) {
        const options = mediaPlay.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: mediaPlay.fileId,
            resolution: option.desc
          };
        });
      }
      return [{ fileFullUrl: '', label: '' }];
    },
    convertASCIIForNum,
    clickIcon(e) {
      if (this.value.attach && this.value.attach.length >= 5) {
        // 最多可上传{0}个文件
        return this.$message.warning(this.$t('pc_ote_msg_maxfilesize', [5]));
      }

      if (this.value.uploadDisabled === 1) {
        // 文件上传中，请稍后再试
        return this.$message.warning(this.$t('pc_ote_msg_file_uploading'));
      }

      // 触发文件选择
      const fileElement = e.currentTarget.parentElement.parentElement.querySelector('input[type]');
      fileElement && fileElement.click();
      this.$emit('file-click');
    },
    handlePaste() {}
  }
};
</script>
