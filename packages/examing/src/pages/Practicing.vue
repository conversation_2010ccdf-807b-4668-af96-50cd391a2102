<template>
  <div v-if="doneConfirm && coursePractice" class="pt60 text-center box-size-borderbox">
    <practice-statistics
      :show-old-dialog="showOldDialog"
      :is-wrong-question-mode="isWrongQuestionMode"
      :repeated="info.repeated"
      :submit-sta-info="submitStaInfo"
      :btn-info="btnInfo"
      @go-third="goThirdPage"
      @go-cancel="goCancelPage"
      @go-main="completePractice"
      @quit-practice="completePractice"
      @do-one-more="doOneMore"
    />
  </div>

  <div
    v-else
    v-loading="loading"
    class="yxtulcdsdk-user-exam box-size-borderbox yxtulcdsdk-flex-vertical"
  >
    <!-- 页头 -->
    <CommonSticky
      v-if="!coursePractice"
      ref="stickyLeft"
      position="top"
      class="z-999"
      :by-class="deepStudy"
    >
      <div class="yxtulcdsdk-exam-header">
        <div
          class="flex flex-mid flex-shrink-0 hand hover-primary-6"
          @click="backToPreview()"
        >
          <yxt-svg icon-class="quit" width="16px" height="16px" />
          <span class="ml8 flex-shrink-0">{{ $t('pc_ote_btn_exit'/** 退出 */) }}</span>
        </div>
        <yxtf-divider direction="vertical" />
        <yxtf-tooltip
          v-if="info && info.praName"
          :content="info.praName"
          placement="bottom"
          open-filter
          :max-width="780"
        >
          <span class="d-in-block flex-g-1 ellipsis">{{ info.praName }}</span>
        </yxtf-tooltip>
      </div>
    </CommonSticky>
    <div v-if="allQuestions && coursePractice && !loading" class="flex flex-mid">
      <yxtf-progress
        class="yxtulcdsdk-flex-shrink-1 pr8"
        :stroke-width="4"
        :percentage="answeredNum/allQuestions.length*100"
        :show-text="false"
        :fit-width="true"
      />
      <span class="color-gray-8 yxtulcdsdk-flex-shrink-0"><span class="yxt-weight-5">{{ answeredNum }}</span>/{{ allQuestions.length }}</span>
    </div>
    <div
      v-if="!loading && currentQues"
      class="yxtulcdsdk-pc-marking-top-wrapper"
      :style="{
        minHeight: (getHeight() - 46) + 'px'
      }"
    >
      <!-- 作答区 -->
      <div
        class="yxtulcdsdk-pc-marking-main yxtulcdsdk-pc-marking-main--ov yxtulcdsdk-pc-special pt24 ph24 pb24"
        :class="{'img-no-point-event': info.allowPictureCopy === 0, 'text-no-select': info.allowCharacterCopy === 0 }"
      >
        <!-- 中间主体 -->
        <div class="yxtulcdsdk-review-fixed-content bg-white yxtbizf-br-4 font-size-14 mheightp100 flex layout-flex-vertical">
          <div class="color-gray-10 pt20 pb4 yxt-weight-5 standard-size-14 font-size-16" :class="{'ph24': !coursePractice }">
            {{ generateQuesTitle(currentQues.parent?currentQues.parent.quesType:currentQues.quesType) }}
          </div>
          <yxt-divider v-if="!coursePractice" class="mt20 mb24" />
          <!-- 组合题题干 -->
          <user-ques-detail
            v-if="currentQues.parent"
            practice
            class="mv24"
            :class="{'ph24': !coursePractice }"
            :value="currentQues.parent"
          />
          <div class="flex-g-1">
            <!-- 试题 -->
            <div
              class="pb24"
              :class="{
                'ph24': !coursePractice && !deepStudy,
                'ph32': !coursePractice && deepStudy
              }"
            >
              <!-- 单题展示 -->
              <user-ques-detail
                ref="quesMain"
                v-model="currentQues"
                practice
                :class="'mt24'"
                :index="index"
                :allow-upload="0"
                @submitSingle="doSingle"
              />
            </div>
            <!-- 答案展示 -->
            <div v-if="currentQues.isSubmited && info.allowViewSingleQuesResult" class="yxtulcdsdk-examing__result font-size-14 color-gray-9">
              <!-- 关键词 -->
              <div v-if="isNotNullKeyword(currentQues.resultInfo.keywords) && info.allowViewSingleQuesAnswer" class="yxtulcdsdk-examing__result-line">
                <yxtf-tag size="mini" class="yxtulcdsdk-examing__result-line-left">{{ $t('pc_ote_lbl_keyword') }}</yxtf-tag>
                <div class="yxtulcdsdk-examing__result-line-right">
                  {{ currentQues.resultInfo.keywords.length && currentQues.resultInfo.keywords.map(item => item.name).join(';') }}
                </div>
              </div>
              <!-- 用户作答的 -->
              <div class="yxtulcdsdk-examing__result-line">
                <yxtf-tag size="mini" class="yxtulcdsdk-examing__result-line-left">{{ $t('pc_ote_lbl_submitanswer') }}</yxtf-tag>
                <!-- 填空题答案 -->
                <div v-if="currentQues.quesType === 3" class="yxtulcdsdk-examing__result-line-right">
                  <template v-for="(ans, fillIndex) in currentQues.ansUser">
                    <div :key="'asw' + fillIndex" class="yxtulcdsdk-row-flex">
                      <span class="mr16">{{ `(${fillIndex + 1}) ${ans.answer}` }}</span>
                      <yxt-svg-icon
                        width="16px"
                        height="16px"
                        class="yxtulcdsdk-row-flex__sub"
                        :icon-class="ans.corrected ? 'success' : 'error'"
                        :class="[ans.corrected ? 'yxtf-color-success' : 'yxtf-color-danger']"
                      />
                    </div>
                  </template>
                </div>
                <!-- 其他题型答案 -->
                <div v-else class="yxtulcdsdk-examing__result-line-right yxtulcdsdk-row-flex">
                  <span v-if="currentQues.quesType === 4" class="mr16" v-html="currentQues.ansUser"></span>
                  <span v-else-if="currentQues.quesType === 2" class="mr16">{{ currentQues.ansUser === $t('pc_ote_lbl_correct')?(currentQues.judgeCorrectOptionContent?currentQues.judgeCorrectOptionContent:$t('pc_ote_lbl_correct')):(currentQues.judgeWrongOptionContent?currentQues.judgeWrongOptionContent:$t('pc_ote_lbl_wrong') ) }}</span>
                  <span v-else class="mr16">{{ currentQues.ansUser }}</span>
                  <yxt-svg-icon
                    width="16px"
                    height="16px"
                    class="yxtulcdsdk-row-flex__sub"
                    :icon-class="currentQues.resultInfo.corrected ? 'success' : 'error'"
                    :class="[currentQues.resultInfo.corrected ? 'yxtf-color-success' : 'yxtf-color-danger']"
                  />
                </div>
              </div>
              <!-- 正确答案 -->
              <div v-if="info.allowViewSingleQuesAnswer" class="yxtulcdsdk-examing__result-line">
                <yxtf-tag type="warning" size="mini" class="yxtulcdsdk-examing__result-line-left">{{ $t('pc_ote_lbl_correctanswer') }}</yxtf-tag>
                <div v-if="currentQues.quesType === 3">
                  <template v-if="currentQues.ansCorrect && currentQues.ansCorrect.length">
                    <template v-for="(ans, fillIndex) in currentQues.ansCorrect">
                      <div :key="'asw' + fillIndex">
                        ({{ fillIndex + 1 }}) {{ ans }}
                      </div>
                    </template>
                  </template>
                  <template v-else>
                    <div>
                      {{ $t('pc_ote_lbl_none') }}
                    </div>
                  </template>
                </div>
                <div v-else-if="currentQues.quesType === 2" class="yxtulcdsdk-examing__result-line-right">
                  {{ currentQues.ansCorrect === $t('pc_ote_lbl_correct')?(currentQues.judgeCorrectOptionContent?currentQues.judgeCorrectOptionContent:$t('pc_ote_lbl_correct')):(currentQues.judgeWrongOptionContent?currentQues.judgeWrongOptionContent:$t('pc_ote_lbl_wrong') ) }}
                </div>
                <div v-else-if="currentQues.quesType === 4" class="width-percent-100">
                  <span v-html="cleanStyle(currentQues.ansCorrect) || (currentQues.resultInfo.answerPlay ? '' : $t('pc_ote_lbl_none'))"></span>
                  <media-player
                    v-if="currentQues.resultInfo.answerPlay && currentQues.resultInfo.answerPlay.fileId"
                    ref="mediaPlayer"
                    :player-key="currentQues.resultInfo.answerPlay.fileId"
                    :class="cleanStyle(currentQues.ansCorrect) ? 'mt16' : ''"
                    :type="currentQues.resultInfo.answerPlay.type"
                    :file-id="currentQues.resultInfo.answerPlay.fileId"
                    :options="playerMediaOptions(currentQues.resultInfo.answerPlay)"
                    :tran-status="currentQues.resultInfo.answerPlay.tranStatus"
                  />
                </div>
                <template v-else>
                  <div v-if="currentQues.ansCorrect && currentQues.ansCorrect.length" class="yxtulcdsdk-examing__result-line-right" v-html="cleanStyle(currentQues.ansCorrect)"></div>
                  <div v-else class="yxtulcdsdk-examing__result-line-right">{{ $t('pc_ote_lbl_none') }}</div>
                </template>
              </div>
              <!-- 解析 -->
              <div v-if="info.allowViewSingleQuesExplain" class="yxtulcdsdk-examing__result-line">
                <yxtf-tag size="mini" class="yxtulcdsdk-examing__result-line-left">{{ $t('pc_ote_lbl_quesanalysis') }}</yxtf-tag>
                <div class="yxtulcdsdk-examing__result-line-right">
                  <span v-html="cleanStyle(currentQues.resultInfo.explainText) || (currentQues.resultInfo.explainPlay ? '' : $t('pc_ote_lbl_none'))"></span>
                  <media-player
                    v-if="currentQues.resultInfo.explainPlay && currentQues.resultInfo.explainPlay.fileId"
                    ref="mediaPlayer"
                    :player-key="currentQues.resultInfo.explainPlay.fileId"
                    class="flex"
                    :class="cleanStyle(currentQues.resultInfo.explainText) ? 'mt12' : ''"
                    :type="currentQues.resultInfo.explainPlay.type"
                    :file-id="currentQues.resultInfo.explainPlay.fileId"
                    :options="playerMediaOptions(currentQues.resultInfo.explainPlay)"
                    :tran-status="currentQues.resultInfo.explainPlay.tranStatus"
                  />
                </div>
              </div>
            </div>
            <!-- 确认按钮 -->
            <div v-if="!currentQues.isSubmited && currentQues.quesType !== 0 && currentQues.quesType !== 2" class="mb24">
              <yxtf-button
                class="ml44"
                :disabled="!isAnswered || isSubmiting"
                type="primary"
                @click="doSingle(currentQues, true)"
              >
                {{ ' ' + $t('pc_ote_btn_confirm') }}
              </yxtf-button>
            </div>
          </div>
          <!-- 上一题下一题 -->
          <common-sticky
            ref="btnSticky"
            :by-class="deepStudy"
            class="z-19"
          >
            <div class="yxtulcdsdk-examing__bottom">
              <!-- <div><yxtf-divider class="mv0" /></div> -->
              <div class="yxtulcdsdk-examing__btns b-radius4-bottom">
                <yxtf-button
                  plain
                  size="larger"
                  :disabled="index <= 0 || isSubmiting"
                  @click="goIndex(-1)"
                >
                  {{ $t('pc_ote_btn_prevques') }}
                </yxtf-button>
                <yxtf-button
                  :plain="!isLastQues"
                  size="larger"
                  :type="isLastQues?'primary':''"
                  :disabled="disabledNextBtn"
                  @click="isLastQues ? submit() : goIndex(1)"
                >
                  {{ $t(isLastQues ? 'pc_ote_btn_submit':'pc_ote_btn_nextques') }}
                </yxtf-button>
              </div>
            </div>
          </common-sticky>
        </div>
      </div>
      <!-- 答题卡 -->
      <CommonSticky
        v-if="!coursePractice"
        ref="stickyRight"
        position="top"
        class="mr24 yxtulcdsdk-flex-shrink-0 mt16"
        :offset="68"
        :by-class="deepStudy"
        :style="getAnswerCardStyle()"
      >
        <div
          class="yxtulcdsdk-pc-right"
          :style="getAnswerCardStyle()"
        >
          <div
            class="yxtulcdsdk-answer-card"
            :style="getAnswerCardStyle()"
          >
            <div class="yxtulcdsdk-answer-card__top ph16">
              <div v-if="allQuestions" class="flex flex-mid">
                <span class="color-gray-7 yxtulcdsdk-flex-shrink-0">{{ $t('pc_ote_lbl_progressnum') }}</span>
                <yxtf-progress
                  class="yxtulcdsdk-flex-shrink-1 ph8"
                  :percentage="answeredNum/allQuestions.length*100"
                  :show-text="false"
                  :fit-width="true"
                />
                <span class="color-gray-8 yxtulcdsdk-flex-shrink-0"><span class="yxt-weight-5">{{ answeredNum }}</span>/{{ allQuestions.length }}</span>
              </div>
            </div>
            <div class="yxtulcdsdk-answer-card__bottom color-gray-10">
              <div class="wp-100 text-left ph16 box-size-borderbox font-size-18 mb12 yxt-weight-5 flex layout-justify-between">
                <span class="lh26">{{ $t('pc_ote_lbl_answercard') }}</span>
                <!-- 只看错题暂时拿掉，看产品后面要不要加 -->
                <!-- <yxtf-checkbox v-model="onlyWrong">{{ $t('pc_ote_btn_onlywronganswer') }}</yxtf-checkbox> -->
              </div>
              <div class="wp-100">
                <div class="yxtulcdsdk-ques-answer_card__type yxtulcdsdk-ques-answer_card__type--small">
                  <span>
                    <i class="yxtulcdsdk-ques-answer_type__1"></i>
                    <small class="ellipsis">{{ $t('pc_ote_lbl_noanswer') }}</small>
                  </span>
                  <template v-if="info.allowViewSingleQuesResult">
                    <span>
                      <i class="yxtulcdsdk-ques-answer_type__4"></i>
                      <small class="ellipsis">{{ $t('pc_ote_tip_answercurrent') }}</small>
                    </span>
                    <span>
                      <i class="yxtulcdsdk-ques-answer_type__2"></i>
                      <small class="ellipsis">{{ $t('pc_ote_lbl_correct') }}</small></span>
                    <span>
                      <i class="yxtulcdsdk-ques-answer_type__3"></i>
                      <small class="ellipsis">{{ $t('pc_ote_lbl_wrong') }}</small>
                    </span>
                  </template>
                  <template v-else>
                    <span>
                      <i class="yxtulcdsdk-ques-answer_type__5"></i>
                      <small class="ellipsis">{{ $t('pc_ote_tip_answerdone') }}</small>
                    </span>
                    <span>
                      <i class="yxtulcdsdk-ques-answer_type__4"></i>
                      <small class="ellipsis">{{ $t('pc_ote_tip_answercurrent') }}</small>
                    </span>
                  </template>
                </div>
              </div>
              <yxt-divider class="mv16" />
              <div class="wp100 mheight48 flex flex-j-stretch over-auto yxtulcdsdk-flex-shrink-1">
                <yxt-scrollbar :fit-height="true" class="wp100" :style="{height: 'unset'}">
                  <ul class="yxtulcdsdk-pc-card-list ml10 font-size-12 clearfix">
                    <template v-for="(item, ci) in allQuestions">
                      <li
                        v-if="!onlyWrong || (onlyWrong && item.faulted === 1)"
                        :key="item.id"
                        class="hand"
                        :class="{
                          'done': item.isAnswered && !info.allowViewSingleQuesResult,
                          'success': item.isAnswered && info.allowViewSingleQuesResult && (item.resultInfo && item.resultInfo.corrected),
                          'error' : item.isAnswered && info.allowViewSingleQuesResult && (item.resultInfo && !item.resultInfo.corrected),
                          'current': ci === index
                        }"
                        @click="() => goIndex(ci, true)"
                      >
                        {{ ci + 1 }}
                      </li>
                    </template>
                  </ul>
                </yxt-scrollbar>
              </div>
            </div>
          </div>
        </div>
      </CommonSticky>
      <!-- 作答完成提示框 -->
      <yxtf-dialog
        padding-size="medium"
        :visible.sync="doneConfirm"
        width="520px"
        append-to-body
        custom-class="yxtulcdsdk-ulcdsdk"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :modal-append-to-body="false"
        @close="backToPreview(true)"
      >
        <div class="yxtulcdsdk-confirm-dialog">
          <practice-statistics
            :show-old-dialog="showOldDialog"
            :is-wrong-question-mode="isWrongQuestionMode"
            :repeated="info.repeated"
            :submit-sta-info="submitStaInfo"
            :btn-info="btnInfo"
            @go-third="goThirdPage"
            @go-cancel="goCancelPage"
            @go-main="backToPreview(true)"
            @quit-practice="quitPractice"
            @do-one-more="doOneMore"
          />
        </div>
      </yxtf-dialog>
      <!-- 水印 -->
      <yxtbiz-watermark
        v-if="info.allowAddWatermark && ~~watermarkConfig.enabled"
        class="z-999"
        :option="watermarkConfig"
        :show="true"
        app-code="ote"
        version="v2"
      />
    </div>

    <!-- 离开提示框 -->
    <yxtf-dialog
      :visible.sync="confirmLeaveDialog"
      width="400px"
      :cutline="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <div class="flex flex-start color-gray-10 yxt-weight-5">
        <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
        <span class="ml16">
          <!-- 你还有 {0} 题未作答，确定要退出吗？ -->
          <LanguageSlot
            v-if="isNeedLeaveConfirm"
            inline
            lang-key="pc_ote_lbl_leaveconfirmquesnummsg"
            class="d-in-block"
          >
            <span slot="0" class="color-primary-6"> {{ isNeedLeaveConfirm }} </span>
          </LanguageSlot>
          <!-- 当前练习未提交，是否确定退出？ -->
          <span v-else>{{ $t('pc_ote_msg_practice_not_submit') }}</span>
        </span>
      </div>
      <span slot="footer" class="dialog-footer">
        <!-- 取消 -->
        <yxtf-button @click="confirmLeaved(false)">{{ $t('pc_ote_btn_cancel') }}</yxtf-button>
        <!-- 确定 -->
        <yxtf-button type="primary" @click="confirmLeaved(true)">{{ $t('pc_ote_btn_confirm') }}</yxtf-button>
      </span>
    </yxtf-dialog>
  </div>
</template>

<script>
import { getPraStart, getErrorPraStart, submitSingleAnswerOfpra, submitSingleErrorAnswerOfpra, submitPractice, postPracticeTime, putSubmitPractice, deleteUserPracticeInfo } from '../service/user.service';
import { QUES_TYPE_NAMES, QUES_JUDGE_CHOICE, USER_PRACTICE_USED_TIME } from '../configs/const';
import { convertASCIIForNum, cleanStyle, dealLineFeed, isNullOrUndefined, mergeAnswerFun } from '../core/utils';
import UserQuesDetail from './question/UserPraQuesDetail';
import CommonSticky from './components/CommonSticky.vue';
import { getOrgWatermarkConfig } from '../service/config.service';
import MediaPlayer from './components/mediaPlayer';
import deepStudyPage from '../mixins/deepStudyPage';
import YxtSvgIcon from './components/svgIcon.vue';
import practice from './mixins/practice';
import PracticeStatistics from './components/PracticeStatistics.vue';
import { LanguageSlot } from 'yxt-biz-pc';

export default {
  components: {
    UserQuesDetail,
    CommonSticky,
    MediaPlayer,
    YxtSvgIcon,
    PracticeStatistics,
    LanguageSlot
  },
  mixins: [deepStudyPage, practice],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      praId: routeParams.praId, // 练习ID
      pumId: routeParams.pumId, // 用户练习ID
      praBatchId: routeParams.praBatchId,
      index: 0, // 当前操作的试题编号
      onlyWrong: false, // 只显示错题
      info: {
        allowAddWatermark: false,
        allowCharacterCopy: 0,
        allowPictureCopy: 0
      }, // 练习信息
      allQuestions: [], // 所有的单题
      currentQues: null, // 当前操作的试题
      // 单题提交请求体
      submitAnswerBody: {
        answers: [],
        isH5Exam: 0
      },
      watermarkConfig: {
        enabled: 1,
        type: 1,
        opacity: 80,
        text: ' ',
        fontSize: 30,
        color: '#000'
      },
      doneConfirm: false, // 答完确认框
      isConfirmed: false, // 是否确认了
      isSubmiting: false, // 单题提交中
      isSubmited: false, // 是否最终提交了
      isFinished: false, // 是否全部答完了
      isAnswered: 0, // 当前题目是否作答了，控制确定按钮
      unsubmittedTime: 0, // 未提交时长
      timer: null, // 练习时长定时器
      postTimer: null, // 推送时长定时器
      forceSubmitCount: 0 // 默认强制提交次数
    };
  },
  inject: ['getHeight', 'getWidth', 'getFSWidth'],
  computed: {
    isLastQues() {
      return this.index >= this.allQuestions.length - 1;
    },

    showSubmitBtn() {
      return this.answeredNum >= this.allQuestions.length - 1;
    },

    storageCacheKey() {
      const userId = window.localStorage.userId;
      return `${USER_PRACTICE_USED_TIME}_${this.praId}_${userId}`;
    },

    disabledNextBtn() {
      const disabled = this.isSubmiting || (this.isConfirmed && this.isLastQues);
      if (this.coursePractice) {
        return disabled || !this.currentQues.isSubmited;
      }

      return disabled;
    },

    isWrongQuestionMode() {
      const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
      return ['1', 1].includes(routeParams.wq);
    },

    currentParams() {
      return {
        trackId: this.routeParams.trackId,
        btid: this.routeParams.btid,
        praId: this.praId,
        praBatchId: this.praBatchId,
        gwnlUrl: this.routeParams.gwnlUrl // 人才发展url进行面包屑点击回退
      };
    }
  },
  created() {
    this.unsubmittedTime = window.localStorage.getItem(this.storageCacheKey) || 0;
  },
  async mounted() {
    try {
      const result = await getOrgWatermarkConfig('practice');
      this.watermarkConfig = result;
    } catch (err) {
      this.handlerPublicError(err);
    }
    this.getUpmInfo();
    // 中途退出处理
    window.addEventListener('beforeunload', this.halfwaySubmit);

    this.$emit('updateProgress', 1); // 学习中
  },
  beforeDestroy() {
    this.clearTimer();
    this.halfwaySubmit();
    window.removeEventListener('beforeunload', this.halfwaySubmit);
  },
  methods: {
    cleanStyle,
    setTimer() {
      if (!this.timer) {
        this.timer = setInterval(() => {
          this.unsubmittedTime++;
          window.localStorage.setItem(this.storageCacheKey, this.unsubmittedTime);
        }, 1000);
      }
      if (!this.postTimer) {
        this.postTimer = setInterval(() => {
          this.postTime();
          // 心跳打点
          window.YxtFeLog && window.YxtFeLog.track('e_heartbeat', { lib: 'js', properties: {} });
        }, 30 * 1000);
      }
    },
    clearTimer() {
      clearInterval(this.timer);
      clearInterval(this.postTimer);
      this.timer = null;
      this.postTimer = null;
    },

    async postTime() {
      const usedTime = this.unsubmittedTime;
      this.unsubmittedTime = this.unsubmittedTime - usedTime;
      try {
        await postPracticeTime({ praId: this.praId, puId: this.info.puId, uniqueId: this.info.uniqueId, usedTime, praBatchId: this.praBatchId });
      } catch (error) {
        this.unsubmittedTime = this.unsubmittedTime + usedTime;
      }
      window.localStorage.setItem(this.storageCacheKey, this.unsubmittedTime);
    },
    // 获取练习信息
    getUpmInfo() {
      const getFun = this.isWrongQuestionMode ? getErrorPraStart : getPraStart;
      const usedTime = this.unsubmittedTime;
      getFun(this.praId, usedTime, this.praBatchId).then(res => {
        this.unsubmittedTime = this.unsubmittedTime - usedTime;
        this.isFinished = false;
        this.isConfirmed = false;
        this.index = 0;
        this.info = res;
        this.info.allowAddWatermark = !!this.info.allowAddWatermark;
        this.loadQuesList(res.listQues); // 加载页面试题
        this.currentQues = this.allQuestions[this.index];
        // 去第一次未作答的题目
        this.goUndo();
        this.setTimer();
        this.resetSticky();
        this.loading = false;

        // 如果题目做完再次进入出现未提交的情况，此时在调用一次提交接口做提交
        if (this.answeredNum === this.allQuestions.length) {
          // 控制提交的次数，防止一直调用接口
          this.forceSubmitCount++;
          this.forceSubmitCount <= 3 && res.puId && putSubmitPractice(res.puId).then(() => {
            this.doOneMore();
          }).catch((err) => {
            console.log(err);
          });
        }

        // 练习信息加载完成调用success事件
        this.coursePractice && this.handlerCoursePracticeEvent('success');
      })
        .catch((err) => {
          const newError = (err && err.error) || err;
          if (['apis.ote.pra.validation.TimesLimited', 'apis.ote.pra.Ended'].includes(newError.key)) {
            // 练习过期/结束处理
            this.backToPreview(true);
          } else if (['apis.ote.exam.question.change'].includes(newError.key)) {
            this.$confirm('', this.$t('pc_ote_msg_practicequeschanged') /** 当前练习下的试题已被管理员更新，请重新参与作答 */, {
              showCancelButton: false,
              beforeClose: (action, instance, done) => { (action === 'confirm') && done(); },
              type: 'info'
            }).then(() => {
              // 剩余试题被删除，手动结束
              deleteUserPracticeInfo(this.praId).then(() => {
                this.getUpmInfo();
              }).catch(err => {
                this.handlerPublicError(err);
              });
            }).catch(() => {
            });
          } else {
            this.handlerPublicError(err);
          }
        });
    },

    // 离开前的确认
    confirmLeave() {
      this.confirmLeaveDialog = true;
    },

    // 离开前的确认后
    confirmLeaved(leave) {
      this.confirmLeaveDialog = false;
      if (leave) {
        this.backToPreview(true);
      }
    },

    // 回到考试预览
    backToPreview(isNotConfirm) {
      if (!isNotConfirm) {
        this.confirmLeave();
      } else {
        this.$emit('changeStep', this.UserPracticeStep.preview, {
          trackId: this.routeParams.trackId,
          btid: this.routeParams.btid,
          praId: this.praId,
          praBatchId: this.praBatchId,
          gwnlUrl: this.routeParams.gwnlUrl // 人才发展url进行面包屑点击回退
        }, true);
      }
    },
    // 上一题、下一题、提交
    goIndex(i, trueIndex) {
      const newIndex = trueIndex ? i : (this.index + i);
      const ques = this.allQuestions[newIndex];

      if (ques) {
        this.index = newIndex;
        // 正常上一题下一题
        this.currentQues = ques;
        this.isAnswered = this.getSubmitAnswer(this.currentQues, true);
        this.isSubmited = this.currentQues.isSubmited;
      }

      this.resetSticky();
    },

    async submit() {
      if (this.isNeedLeaveConfirm) {
        this.confirmSubmitLeave();
      } else {
        if (this.isFinished && this.showSubmitBtn) {
          this.isConfirmed = true;

          // 初始化重做错题
          !this.coursePractice && await this.initRedoWrong(this.praId);
          this.isWrongQuestionMode ? this.endSubmit() : this.endSubmit(this.getEndStaData);
        }
      }

      this.resetSticky();
    },

    async endSubmit(func) {
      // 最后题做完了提交
      await this.endPractice(() => {
        !this.isWrongQuestionMode && this.$emit('updateProgress', 2); // 通知播放器完成
      });

      if (func) {
        typeof func === 'function' && await func(() => {
          this.doneConfirm = true;
          this.clearTimer();
        });
      } else {
        this.doneConfirm = true;
        this.clearTimer();
      }
    },

    confirmSubmitLeave() {
      this.$confirm(this.$t('pc_ote_lbl_practiceunfinishtip'/** 当前尚有未练习完毕的题目，请确认是否继续练习 */),
        this.$t('pc_ote_lbl_practiceendtiptitle'/** 确定完成练习吗？ */), {
          confirmButtonText: this.$t('pc_ote_lbl_practicecontinue'/** '继续练习' */),
          cancelButtonText: this.$t('pc_ote_btn_quitpractice'/** '退出练习' */),
          showCancelButton: true,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          type: 'warning'
        })
        .then(() => {
          // 前往未作答的题目
          this.goUndo();
        }).catch(() => {
          this.backToPreview(true);
        });
    },

    // 做题
    doSingle(ques, isConfirm) {
      // 做过了不在处理，也不可修改
      if (ques.isAnswered) {
        return ques.isAnswered;
      }
      // 单选多选不需要确认
      if (ques.quesType === 0 || ques.quesType === 2 || isConfirm) {
        if (this.isSubmiting) {
          return;
        }
        // 做了，确认提交
        const answer = this.getSubmitAnswer(ques);
        if (ques.isAnswered) {
          const answerList = [];
          if (answer) {
            answerList.push(answer);
          }
          this.submitAnswerBody.uniqueId = this.info.uniqueId;
          this.submitAnswerBody.pumId = this.info.pumId;
          this.submitAnswerBody.answers = answerList;
          this.submitAnswerBody.isLastQues = this.showSubmitBtn ? 1 : 0;
          this.submitAnswerBody.praBatchId = this.praBatchId;
          this.submitSingle();
          this.$forceUpdate();
          this.$refs.quesMain && this.$refs.quesMain.$forceUpdate();
        }
      } else {
        // 还在做，不提交
        this.isAnswered = this.getSubmitAnswer(ques, true);
        this.$forceUpdate();
        this.$refs.quesMain && this.$refs.quesMain.$forceUpdate();
      }
    },
    // 单题提交
    submitSingle() {
      this.isSubmiting = true;
      const submitFun = this.isWrongQuestionMode ? submitSingleErrorAnswerOfpra : submitSingleAnswerOfpra;
      submitFun(this.info.praId, this.isWrongQuestionMode ? this.info.pumId : this.info.puId, this.submitAnswerBody).then((res) => {
        // 添加try - catch避免走到接口的 catch导致 currentQues.isAnswered = false;最后接口提交完成，但是页面最后一次提交的isLastQues为 0
        try {
          if (res && res.length > 0) {
            this.$set(this.currentQues, 'resultInfo', res[0]);
            this.$set(this.currentQues, 'corrected', res[0].corrected);

            // 重新更新练习控制信息
            this.info.allowViewSingleQuesAnswer = this.currentQues.resultInfo.allowViewSingleQuesAnswer;
            this.info.allowViewSingleQuesExplain = this.currentQues.resultInfo.allowViewSingleQuesExplain;
            this.info.allowViewSingleQuesResult = this.currentQues.resultInfo.allowViewSingleQuesResult;
            // 获取答案、对错信息
            this.getansUser();
          }
          if (this.submitAnswerBody.isLastQues) {
            // 做完了
            this.isFinished = true;
            this.coursePractice && this.handlerCoursePracticeEvent('finish');
          }
          this.currentQues.isSubmited = true;
          this.resetSticky();
        } catch (e) {
          console.log(e);
        }
      }).catch(err => {
        this.currentQues.isSubmited = false;
        this.currentQues.isAnswered = false;
        this.handlerPublicError(err);
      }).finally(() => {
        this.isSubmiting = false;
        this.$forceUpdate();
        this.$refs.quesMain && this.$refs.quesMain.$forceUpdate();
      });
    },
    // 处理显示在页面上的答案
    getansUser(ques) {
      let ansCorrect = ''; // 正确答案
      let ansUser = ''; // 用户作答
      const currentQues = ques || this.currentQues;

      switch (currentQues.quesType) {
        case 0:
        case 1:
          // 选择类型的
          currentQues.choiceItems.forEach((item, index) => {
            if (this.info.allowViewSingleQuesAnswer && currentQues.resultInfo.answers) {
              // 正确答案
              currentQues.resultInfo.answers.forEach(correct => {
                if (item.id === correct) {
                  ansCorrect += (ansCorrect && '、') + convertASCIIForNum(index);
                }
              });
            }
            // 当前用户答案
            if (currentQues.quesType === 1) {
              // 多选
              currentQues.answers.forEach(ua => {
                if (item.id === ua) {
                  ansUser += (ansUser && '、') + convertASCIIForNum(index);
                }
              });
            } else {
              // 单选
              if (item.id === currentQues.answer) {
                ansUser += (ansUser && '、') + convertASCIIForNum(index);
              }
            }
          });
          break;
        case 2:
          // 判断
          if (this.info.allowViewSingleQuesAnswer) {
            ansCorrect = this.$t(QUES_JUDGE_CHOICE[currentQues.resultInfo.judgeAnswer ? 1 : 0]);
          }
          ansUser = this.$t(QUES_JUDGE_CHOICE[currentQues.submitContent === '1' ? 1 : 0]);
          break;
        case 3:
          // 填空
          ansUser = [];
          ansCorrect = [];
          // 当前用户答案
          currentQues.answers.forEach((ua, index) => {
            ansUser.push({
              answer: ua,
              corrected: currentQues.resultInfo.fillinCorrectedInfo ? currentQues.resultInfo.fillinCorrectedInfo[index] : this.info.allowViewSingleQuesAnswer
            });
          });
          // 正确答案
          if (this.info.allowViewSingleQuesAnswer && currentQues.resultInfo.fillinAnswers) {
            currentQues.resultInfo.fillinAnswers.forEach((as, i) => {
              ansCorrect.push(mergeAnswerFun(as));
            });
          }
          break;
        case 4:
          // 问答
          if (this.info.allowViewSingleQuesAnswer) {
            if (currentQues.resultInfo.answers && currentQues.resultInfo.answers.length > 0 && currentQues.resultInfo.answers[0]) {
              ansCorrect += cleanStyle(currentQues.resultInfo.answers[0], true);
            }
          }
          ansUser = dealLineFeed(currentQues.submitContent, true);
          break;
        case 5:
          // 组合题不会提交单题
          break;
        default:
      }
      currentQues.ansCorrect = ansCorrect;
      currentQues.ansUser = ansUser;
      return currentQues;
    },

    // 退出练习
    quitPractice() {
      this.backToPreview(true);
    },
    // 再练一次
    doOneMore() {
      this.loading = true;
      this.isSubmiting = false;
      this.isAnswered = 0;
      this.doneConfirm = false;
      this.allQuestions = [];
      this.staTimer = null;
      this.time = 1000;
      this.showOldDialog = false;
      this.quesIndex = 0;
      this.submitStaInfo = {};
      this.haveFaultQues = false;
      this.getUpmInfo();
    },
    // 获取用户作答及单题提交请求体
    getSubmitAnswer(ques, isDoing) {
      let answer = [];
      let answered = 0;
      switch (ques.quesType) {
        case 0:
          answer.push(ques.answer);
          answered = !!ques.answer;
          break;
        case 1:
          answer = ques.answers;
          answered = answer && answer.length > 0 ? 1 : 0;
          break;
        case 2:
          answer.push(ques.submitContent);
          answered = ques.submitContent && ques.submitContent !== '-1';
          break;
        case 3:
          let isAns = 0;
          ques.fillInItems.forEach(function(e) {
            if (e) {
              // 默认存下填空题的所有空（填空题批阅会考虑填空项的顺序）
              answer.push(e.submitAnswer ? e.submitAnswer : '');
              if (e.submitAnswer && e.submitAnswer.length > 0) {
                isAns = 1;
              }
            }
          });
          // 如果一个空都没有填，把answer置空
          if (isAns !== 1) {
            answer = [];
          }
          answered = isAns;
          ques.answers = answer;
          break;
        case 4:
          // 问答题答案
          answer.push(ques.submitContent);
          answered = ques.submitContent ? 1 : 0;
          break;
        default:
          break;
      }
      if (isDoing) {
        // 作答中只返回是否已做答
        return answered;
      } else {
        ques.isAnswered = answered;
      }
      // 组装单体提交请求体
      const answerBody = {};
      answerBody.quesId = ques.id;
      answerBody.lastSubmitTime = new Date().getTime();
      answerBody.answer = answer;
      answerBody.examQuesId = ques.examQuesId;
      answerBody.quesType = ques.quesType;
      return answerBody;
    },
    // 加载各题型试题（并判断试题是否已作答）
    loadQuesList(quesList) {
      for (let i = 0; i < quesList.length; i++) {
        const e = quesList[i];
        e.type = e.quesType;

        if ([0, 1, 2, 3, 4].includes(e.quesType)) {
          this.dealQuestionInfo(e);
        } else if (e.quesType === 5) {
          // 处理子题
          if (e.subQuesList && e.subQuesList.length > 0) {
            e.subQuesList.forEach((ques) => {
              ques.type = ques.quesType;
              this.dealQuestionInfo(ques, e);
            });
          }
        }
      }
    },
    // 处理服务器给的题目信息
    dealQuestionInfo(ques, pQues) {
      if ([0, 1, 2, 3, 4].includes(ques.quesType)) {
        ques = this.handlerQuesResultEcho(ques);
      }

      if (pQues) {
        ques.parent = pQues;
      }

      ques.index = this.quesIndex;
      this.allQuestions.push(ques);
      // 每次push完之后，下次进来的题目的index + 1
      this.quesIndex++;
    },

    // 初始化接口返回的值，用作页面显示
    handlerInitInfo(ques) {
      ques.answered = ques.isAnswered = this.isSubmited = ques.isSubmited = true;
      const currentQues = {
        answer: ques.answer || '',
        correct: ques.corrected,
        submitContent: ques.submitContent,
        resultInfo: {
          answerPlay: ques.answerPlay,
          answers: ques.correctAnswers || [],
          corrected: ques.corrected,
          explainPlay: ques.explainPlay,
          explainText: ques.explainText,
          fillinAnswers: ques.fillinAnswers,
          fillinCorrectedInfo: ques.fillinCorrectedInfo,
          judgeAnswer: ques.judgeAnswer,
          keywords: ques.keywords
        }
      };

      // 填空题答案取的是用户提交的答案
      if (ques.quesType === 3) {
        currentQues.answers = ques.submitQuesAnswers;
      }

      ques = this.getansUser({ ...ques, ...currentQues });
      return ques;
    },

    // 处理练习回显示答案
    handlerQuesResultEcho(quesResult) {
      const type = quesResult.quesType;
      if (quesResult.submitQuesAnswers && quesResult.submitQuesAnswers.length) {
        const cotterEsum = {
          0: { key: 'answer', val: quesResult.submitQuesAnswers[0] || '' },
          1: { key: 'answers', val: quesResult.submitQuesAnswers || [] },
          2: { key: 'submitContent', val: quesResult.submitQuesAnswers[0] || '' },
          3: { key: 'submitAnswer', val: quesResult.submitQuesAnswers },
          4: { key: 'submitContent', val: quesResult.submitQuesAnswers[0] || '' }
        };

        if ([0, 1, 2, 4].includes(type)) {
          quesResult[cotterEsum[type].key] = cotterEsum[type].val;
        }
        if (type === 3) {
          quesResult.fillInItems.forEach((item, index) => {
            item[cotterEsum[type].key] = cotterEsum[type].val[index];
          });
        }
        quesResult = this.handlerInitInfo(quesResult);
      } else {
        const cotterEsum = {
          0: { key: 'answer', val: '' },
          1: { key: 'answers', val: [] },
          2: { key: 'submitContent', val: '' }
        };

        if ([0, 1, 2].includes(type)) {
          quesResult[cotterEsum[type].key] = cotterEsum[type].val;
        }
        quesResult.isAnswered = 0;
      }

      return quesResult;
    },

    // 生成试卷题型标题
    generateQuesTitle(type) {
      return this.$t(QUES_TYPE_NAMES[type]);
    },
    resetSticky() {
      this.$nextTick(() => {
        this.$refs.btnSticky && this.$refs.btnSticky.scrollHandler();
      });
    },
    playerMediaOptions(mediaPlay) {
      if (mediaPlay.tranStatus === 2) {
        const options = mediaPlay.playDetails;
        return options.map(option => {
          return {
            fileFullUrl: option.url,
            fileId: mediaPlay.fileId,
            resolution: option.desc
          };
        });
      }
      return [{ fileFullUrl: '', label: '' }];
    },
    isNotNullKeyword(keyword) {
      return !!(keyword && keyword.length);
    },
    getAnswerCardStyle() {
      return {
        maxHeight: (this.getHeight() - 68 - 24 - ((this.getWidth() < (1252 + this.getFSWidth() * 2)) && this.deepStudy ? 68 : 0)) + 'px',
        minHeight: '320px',
        width: '282px'
      };
    },

    goUndo() {
      this.goIndex((this.firstUndo && this.firstUndo.index) || this.allQuestions[0].index, true);
    },

    // 单纯的中途退出调用的接口
    async halfwaySubmit() {
      try {
        if (this.info) {
          if (isNullOrUndefined(this.info.praId)) return;
          const usedTime = this.unsubmittedTime;
          await submitPractice(this.info.praId, this.info.puId, this.info.uniqueId, usedTime, this.praBatchId, this.routeParams.trackId);

          this.unsubmittedTime = this.unsubmittedTime - usedTime;
          window.localStorage.setItem(this.storageCacheKey, this.unsubmittedTime);
        }
      } catch (error) {
        this.handlerPublicError.bind(this)(error);
      }
    },

    // 练习做完了 | 中途退出
    async endPractice(endedFun) {
      try {
        if (this.info) {
          await this.halfwaySubmit();

          if (this.isWrongQuestionMode) {
            this.showOldDialog = true;
          }

          endedFun && typeof endedFun === 'function' && endedFun();
        }
      } catch (error) {
        this.handlerPublicError.bind(this)(error);
      }
    },

    // 随课练习做完了之后退出练习
    completePractice() {
      this.handlerCoursePracticeEvent('complete');
    }
  }
};
</script>
