<template>
  <div id="watermark" class="yxtulcdsdk-user-wrong-exam width-percent-100">
    <!-- 页头 -->
    <CommonSticky
      ref="stickyLeft"
      position="top"
      class="zindex1999"
      by-class
    >
      <div class="yxtulcdsdk-user-header">
        <div class="flex flex-mid wp100">
          <div class="d-in-block flex flex-mid hover-primary-6" @click="exitCurrentPage">
            <yxt-svg icon-class="quit" width="16px" height="16px" />
            <span class="ml8 hand">{{ $t('pc_ote_btn_exit'/** 退出 */) }}</span>
          </div>

          <template v-if="wrongName">
            <yxtf-divider direction="vertical" />
            <yxtf-tooltip
              :content="wrongName"
              :max-width="780"
              placement="bottom"
              open-filter
            >
              <span class="d-in-block col-flex-1 ellipsis">{{ wrongName }}</span>
            </yxtf-tooltip>
          </template>
        </div>
      </div>
    </CommonSticky>
    <div v-if="quesTypesList.length>0" class="yxtulcdsdk-marking-top-wrapper yxtulcdsdk-marking-top-wrapper--noleft">
      <!-- 中间主体 -->
      <div class="yxtulcdsdk-marking-main mb0 over-hidden min-w534">
        <div class="yxtulcdsdk-review-fixed-content">
          <!-- 所有试题 -->
          <template v-for="(typeObj, indexType) in quesTypesList">
            <div v-if="typeObj.quesList && typeObj.quesList.length > 0" :key="indexType" class="yxtulcdsdk-wrong-alllist">
              <div class="color-gray-10 mt18 mb16 standard-size-14 ph48 font-w700">{{ typeObj.title }}</div>
              <yxt-divider />
              <!-- 试题 -->
              <div class="pl48 pb24 pr24">
                <template v-for="(item, index) in typeObj.quesList">
                  <!-- 定位锚点-->
                  <div v-if="item.quesType !== 5" :key="item.id + '_position'" :ref="item.id"></div>
                  <ques-item
                    :key="item.id + '_ques'"
                    :ref="item.quesId"
                    v-model="typeObj.quesList[index]"
                    :class="typeObj.quesList && index !== typeObj.quesList.length - 1 ? 'mv24' : 'mt24'"
                    :index="index"
                    :mode="mode"
                    @recCurrentQues="recCurrentQues"
                    @markQues="markQues"
                    @delete-ques="removeOneQues"
                    @submit="submitSingle"
                    @select="selectSingle"
                  />
                  <yxt-divider v-if="typeObj.quesList && index !== typeObj.quesList.length - 1" :key="item.id + '_line'" />
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
      <!-- 答题卡 -->
      <answer-card
        :mode="mode"
        :is-submitting="isInSubmitting"
        :ques-types-list="quesTypesList"
        :current-ques="currentQues"
        :statistic="reviewQuesStatistic"
        :deep-study="deepStudy"
        @scrollTo="scrollView"
        @submit="submit"
        @removeAll="removeAllQues"
      />
    </div>
  </div>
</template>

<script>
import { QUES_TYPE_NAMES } from '../configs/const';
import { assembleQuesAnswer, cleanStyle, getQuesTypeIndexName, parseQuesAnswer, getScrollParent } from '../core/utils';
import QuesItem from './wrong/QuesItem';
import AnswerCard from './wrong/AnswerCard';
import { QuesTypeEnum } from '../core/enums';
import {
  getWrongExamAnswerReview,
  getWrongExamInfo,
  getWrongProjectQues,
  removeAllWrongQues,
  removeOneWrongQues,
  submitWrongExamAnswer,
  wrongQuesDoAgain
} from '../service/wrongQuesBook.service';
import deepStudyPage from '../mixins/deepStudyPage';
import CommonSticky from './components/CommonSticky.vue';
import { setApiTrackId } from '../core/api';

const ModeMap = {
  StuWrongQuesBookReview: 'review',
  StuWrongQuesBookPreview: 'preview',
  StuWrongQuesBookAnswer: 'answer'
};

export default {
  name: 'Wrong',
  components: {
    QuesItem,
    AnswerCard,
    CommonSticky
  },
  mixins: [deepStudyPage],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    const modeName = this.deepStudy ? routeParams.name : this.$route.name;

    return {
      QuesTypeEnum,
      // itemList：单题列表模式，只读，没有默认答案、手动展开收起解析，头部多了一行信息
      // answer：答题、
      // review：答完之后展示答案、
      // preview：显示试题及其解析。后俩的区别是preview没有回答，没有对错，试题和试题答案，解析，考核点
      mode: ModeMap[modeName],
      uaId: routeParams.uaId, // 用户试卷ID
      wupId: routeParams.wupId,
      submitText: this.$t('pc_ote_btn_submitexam'),
      allQuestions: [], // 所有的单题
      singleChoiceQues: [], // 单选题
      multiChoiceQues: [], // 多选题
      judgeQues: [], // 判断题
      fillQues: [], // 填空题
      ansQues: [], // 问答题
      combQuesSubListsDic: {}, // 组合题子题 字典
      quesTypesList: [], // 处理过的所有题型及内部的题目
      // 当前操作的试题
      currentQues: {
        id: '',
        type: -1,
        index: -1
      },
      // 用户回答缓存
      submitAnswerBody: {},
      quesMarkIdList: [], // 试题标记集合
      isInSubmitting: false, // 正在提交中

      isIndeterminate: false, // 全选
      checkAll: false,
      reviewQuesStatistic: {
        correct: 0,
        wrong: 0,
        subjective: 0
      },
      wrongName: ''
    };
  },

  created() {
    this.dealStickyEvent(1);
    this.getUemInfo();

    // 错题本这块不需要接口trackId
    setApiTrackId('oteApi', '');
  },
  watch: {
    '$route'() {
      this.mode = ModeMap[this.$route.name];
      this.uaId = this.$route.query.uaId || this.uaId;
      this.getUemInfo();
    }
  },
  methods: {
    /** 清除所有错题 */
    removeAllQues() {
      this.$confirm('', this.$t('pc_ote_msg_confirmClearAllWrongQues'/* 确认将此错题本中的错题全部清空吗？清除后，此错题本将不可恢复。 */), {
        confirmButtonText: this.$t('pc_ote_btn_confirm'),
        cancelButtonText: this.$t('pc_ote_btn_cancel'),
        type: 'warning'
      })
        .then(() => {
          const id = this.uaId || this.$route.query.projectId;
          const type = this.$route.query.projectId ? 'project' : 'exam';
          removeAllWrongQues(id, type).then(() => {
            this.$message({
              type: 'success',
              message: this.$t('pc_ote_msg_success')
            });

            if (this.deepStudy) {
              this.backToPreview();
            } else {
              this.$router.replace({ name: 'StuWrongQuesBookIndex' });
            }
          }).catch(this.$handleError);
        }).catch(() => {});
    },
    /** 删除一个试题 */
    removeOneQues(ques) {
      removeOneWrongQues([ques.id])
        .then(() => {
          ques.currentStatus = 1;
          this.$message({ type: 'success', message: this.$t('pc_ote_msg_success') });
        })
        .catch(this.handleError.bind(this));
    },
    checkAllQues(checked) {
      this.isIndeterminate = false;
      this.quesTypesList.forEach(item => item.quesList.forEach(ques => { ques.quesUISelected = checked; }));
    },
    selectSingle(item, checked) {
      item.quesUISelected = checked;
      const quesList = this.quesTypesList.map(ques => ques.quesList).flat();
      const count = quesList.filter(ques => ques.quesUISelected).length;
      this.isIndeterminate = count > 0 && count < quesList.length;
    },
    cleanStyle,
    // 暂停所有音视频，依赖于mediaplayer里记录的当前播放中的媒体实例
    pauseAllMediaPlayer() {
      if (window.__MEDIA_PLAYER_ONPLAY__) {
        window.__MEDIA_PLAYER_ONPLAY__.pause && window.__MEDIA_PLAYER_ONPLAY__.pause();
      }
    },
    // 处理左右两个sticky的监听
    dealStickyEvent(type) {
      switch (type) {
        case 0:
          this.resizeHandler();
          break;
        case 1:
          window.addEventListener('resize', this.resizeHandler);
          break;
        case 2:
          window.removeEventListener('resize', this.resizeHandler);
          break;
        default:
          break;
      }
    },
    resizeHandler() {
      this.$refs.stickyLeft && this.$refs.stickyLeft.scrollHandler();
      this.$refs.stickyRight && this.$refs.stickyRight.scrollHandler();
    },

    // 获取考试信息
    getUemInfo() {
      this.allQuestions = [];
      this.quesTypesList = [];
      this.singleChoiceQues = [];
      this.multiChoiceQues = [];
      this.judgeQues = [];
      this.fillQues = [];
      this.ansQues = [];
      this.combQuesSubListsDic = {};
      this.reviewQuesStatistic.wrong = 0;
      this.reviewQuesStatistic.subjective = 0;
      this.reviewQuesStatistic.correct = 0;
      let promise;
      switch (this.mode) {
        case 'answer':
          promise = getWrongExamInfo(this.uaId);
          break;
        case 'preview':
          promise = getWrongProjectQues(this.$route.query.projectId);
          break;
        case 'review':
          promise = getWrongExamAnswerReview(this.wupId, this.uaId);
          break;
        default:
          break;
      }
      promise
        .then(data => {
          this.wrongName = data.name || '';
          return data.wrongQuesIdList || data.wuaqList || [];
        })
        .then(res => {
          res.length && this.loadQuesList(res); // 加载页面试题
          this.$nextTick(() => {
            this.dealStickyEvent(0);
          });
          this.generateQuesTitle();
        })
        .catch(this.handleError);
    },
    submitSingle(ques) {
      this.currentQues = ques;
      this.submitAnswerBody[ques.id] = this.assembleAnswerForPut(ques);
    },
    // 获取单题提交请求体
    assembleAnswerForPut(ques) {
      return {
        quesId: ques.quesId,
        quesType: ques.quesType,
        answer: assembleQuesAnswer(ques),
        wuqId: ques.id
      };
    },
    // 提交试卷
    submitUserExam() {
      this.isInSubmitting = true;
      return submitWrongExamAnswer(this.uaId, Object.values(this.submitAnswerBody))
        .then(() => {
          this.isInSubmitting = false;
          this.$message({
            type: 'success',
            message: this.$t('pc_ote_msg_submitsuccess')
          });

          const queryUrl = { uaId: this.uaId, wupId: this.wupId };

          this.changeRouterFunc('StuWrongQuesBookReview', queryUrl);
        })
        .catch((error) => {
          this.isInSubmitting = false;
          this.handleError(error);
        });
    },

    // 改变路由地址方法
    changeRouterFunc(name, queryUrl) {
      if (this.deepStudy) {
        const { currentParams } = this.routeParams;
        const step = currentParams.type === 'exam' ? this.UserExamStep.wrong : this.UserPracticeStep.wrong;

        currentParams.trackId && setApiTrackId('oteApi', currentParams.trackId);
        this.$emit('changeStep', step, { ...queryUrl, name, currentParams });
      } else {
        this.$router.replace({ name, query: queryUrl });
      }
    },

    submit() {
      if (this.isInSubmitting) return;
      this.isInSubmitting = true;

      if (this.mode === 'answer') {
        const unSubmittedCount = this.allQuestions.filter(item => {
          if (item.quesType === QuesTypeEnum.composite) return item.subQuesItem.every(subItem => subItem.isAnswered !== 1);
          return item.isAnswered !== 1;
        }).length;
        let promise;
        if (unSubmittedCount > 0) {
        // 还有*道题未做，是否确认交卷？
          const msg = this.$t('pc_ote_msg_existundoques', [unSubmittedCount]);
          promise = this.$confirm('', msg, {
            confirmButtonText: this.$t('pc_ote_btn_confirm'),
            cancelButtonText: this.$t('pc_ote_btn_cancel'),
            type: 'warning'
          })
            .then(this.submitUserExam)
            .catch(() => {
              this.isInSubmitting = false;
            });
        } else {
          promise = this.submitUserExam();
        }
        promise.catch(this.handleError);
      } else if (this.mode === 'review') {
        wrongQuesDoAgain(this.uaId)
          .then((res) => {
            const queryUrl = { uaId: res.uaId, wupId: this.wupId };
            this.changeRouterFunc('StuWrongQuesBookAnswer', queryUrl);
          })
          .catch(this.handleError.bind(this))
          .finally(() => {
            this.isInSubmitting = false;
          });
      }
    },
    loadQuesList(quesList) {
      for (const quesItem of quesList) {
        if (!quesItem) continue;
        quesItem.isShowMoreText = false;
        quesItem.isMarked = false;
        quesItem.isShowMore = false;
        quesItem.userAnswer = [];
        quesItem.isAnswered = false;
        quesItem.correctAnswer = '';
        const isComposite = !!quesItem.parentId;
        const correctStatus = ['', 'wrong', 'correct'];
        switch (quesItem.quesType) {
          case QuesTypeEnum.singleChoice:
            if (!isComposite) this.singleChoiceQues.push(quesItem);
            this.reviewQuesStatistic[correctStatus[quesItem.correct]]++;
            break;
          case QuesTypeEnum.multiChoice:
            this.reviewQuesStatistic[correctStatus[quesItem.correct]]++;
            if (!isComposite) this.multiChoiceQues.push(quesItem);
            break;
          case QuesTypeEnum.judge:
            this.reviewQuesStatistic[correctStatus[quesItem.correct]]++;
            if (!isComposite) this.judgeQues.push(quesItem);
            break;
          case QuesTypeEnum.fillBlank:
            this.reviewQuesStatistic.subjective++;
            if (!isComposite) this.fillQues.push(quesItem);
            break;
          case QuesTypeEnum.shortAnswer:
            if (!isComposite) this.ansQues.push(quesItem);
            this.reviewQuesStatistic.subjective++;
            break;
          case QuesTypeEnum.composite:
            this.combQuesSubListsDic[quesItem.quesId] = quesItem;
            this.loadQuesList(quesItem.subQuesItem);
            break;
          default:
            break;
        }
        parseQuesAnswer(quesItem);
        if (!quesItem.subQuesItem) this.allQuestions.push(quesItem);
      }
    },
    // 定位试题
    scrollView(selector, isComposite) {
      const elementId = isComposite ? selector.parentId : selector.id;
      const quesEl = this.$refs[elementId] && this.$refs[elementId][0];

      if (quesEl) {
        if (isComposite) {
          quesEl.scrollView(selector.id);
        } else {
          const scroller = getScrollParent(quesEl, true);
          scroller.scrollTop = quesEl.offsetTop;
        }
      }
    },
    // 定位当前试题
    recCurrentQues(ques) {
      this.currentQues = ques;
    },
    // 标记
    markQues(ques) {
      this.$set(ques, 'isMarked', (!ques.isMarked || ques.isMarked === 0) ? 1 : 0);
    },
    // 生成试卷题型标题
    generateQuesTitle() {
      let quesTypeCount = 0; // 题型序号
      [this.singleChoiceQues, this.multiChoiceQues, this.judgeQues, this.fillQues, this.ansQues].forEach((arr, index) => {
        if (arr.length) {
          let title = getQuesTypeIndexName(++quesTypeCount) + '、';
          // 标题
          title += this.$t(QUES_TYPE_NAMES[index]);
          this.quesTypesList.push({
            quesList: arr,
            quesType: index,
            title
          });
        }
      });
      for (const composeQuesId in this.combQuesSubListsDic) {
        const compositeQues = this.combQuesSubListsDic[composeQuesId];
        let title = getQuesTypeIndexName(++quesTypeCount) + '、';
        // 标题
        title += this.$t(QUES_TYPE_NAMES[QuesTypeEnum.composite]);
        this.quesTypesList.push({
          quesList: [compositeQues],
          quesType: QuesTypeEnum.composite,
          title
        });
      }
    },

    // 退出当前页面，因为页面是由window.open打开，所以close正常可以关闭
    exitCurrentPage() {
      if (this.deepStudy) {
        this.backToPreview();
        return;
      }
      if (window.history.length === 1) {
        window.close();
        return;
      }

      window.history.go(-1);
    },

    backToPreview() {
      const { currentParams } = this.routeParams;
      const step = currentParams.type === 'exam' ? currentParams.typeValue : this.UserPracticeStep.preview;

      currentParams.trackId && setApiTrackId('oteApi', currentParams.trackId);
      this.$emit('changeStep', step, currentParams);
    }
  },
  beforeDestroy() {
    // 清除页面绑定事件
    this.dealStickyEvent(2);
  }
};
</script>
