import ErrorPage from '../components/ErrorPage.vue';
import { USER_PACTICE_NONE_KEY, USER_EXAM_NONE_KEY } from 'packages/examing/src/configs/const';
import { Message } from 'yxt-pc';

export default {
  components: {
    ErrorPage
  },

  data() {
    return {
      isShowErrorPage: false,
      errorMessage: '',
      isPractice: false
    };
  },

  methods: {
    backToList() {
      this.prevent = false;
      window.location.replace(`/ote/#/stu/myexam${this.isPractice ? '?pra=1' : ''}`);
    },

    handleError(error) {
      if (error && error.error) {
        // 兼容处理
        error = error.error;
      }
      if (error) {
        if (error.key && error.key.indexOf('global.token') >= 0) {
          return; // 不弹出token错误
        }
        const msg = error.message;
        if (typeof (msg) !== 'string') return;
        Message({
          message: msg,
          type: 'error'
        });
      }
    },

    errorPublic(err, fun, customIf = false) {
      const newError = (err && err.error) || err;
      const errList = ['apis.ote.arrangement.NoPublished', 'apis.ote.userexam.removed', ...USER_EXAM_NONE_KEY, ...USER_PACTICE_NONE_KEY];
      const newIf = customIf || errList.includes(newError.key);

      if (newError.key) {
        if (newIf) {
          this.isShowErrorPage = true;
          this.errorMessage = newError.message;
          this.isPractice = USER_PACTICE_NONE_KEY.includes(newError.key);

          fun && typeof fun === 'function' && fun();
        } else {
          this.handleError(err);
        }
      }
    }
  }
};
