import { FACTOR_FUCTIONS, getFactorFunctionStatus } from '../../core/factor';
import { genWrongExam, getIsCanRedoWrongs } from '../../service/wrongQuesBook.service';

export default {
  data() {
    return {
      allowRedoWrongs: false,
      wuId: '',
      isOldWrongs: false
    };
  },

  computed: {
    oteWrongQues() {
      return getFactorFunctionStatus(FACTOR_FUCTIONS.MY_MISTAKES);
    },

    // 显示重做错题按钮
    showWrongQuesBtn() {
      return !!(this.allowRedoWrongs && !this.oteWrongQues.hidden);
    }
  },

  methods: {
    async initRedoWrong(arrId) {
      if (window.localStorage.isVisitor !== '1') {
        const { errorSetFlag, showEntryFlag, wupId } = await getIsCanRedoWrongs(arrId || this.arrangeId);
        this.allowRedoWrongs = !!showEntryFlag;
        this.isOldWrongs = !!errorSetFlag || !this.oteWrongQues.enabledNoCustom;
        this.wupId = wupId;
      }
    },

    // 重做错题
    linkToWrongs(type) {
      const data = { buildType: 1, wupId: this.wupId };
      genWrongExam(data)
        .then(({ uaId }) => {
          const queryUrl = {
            uaId,
            wupId: this.wupId
          };

          if (this.deepStudy) {
            const params = this.currentParams || this.routeParams;
            queryUrl.name = 'StuWrongQuesBookAnswer';
            queryUrl.currentParams = type === 'exam' ? { ...params, ...this.currentStep } : params;
          }

          const step = type === 'exam' ? this.UserExamStep.wrong : this.UserPracticeStep.wrong;
          this.$emit('changeStep', step, queryUrl, false, true);
        })
        .catch((err) => {
          this.handleError(err);
          if (err && err.key === 'apis.ote.wrong.exam.ques.none.answer') {
            if (type === 'exam') {
              this.getArrangeInfo(true);
            } else {
              setTimeout(() => window.location.reload(), 2000);
            }
          }
        });
    }
  }
};
