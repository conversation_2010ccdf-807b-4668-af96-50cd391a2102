import wrong from './wrong';
import { getSimpleSta } from '../../service/user.service';

export default {
  data() {
    return {
      loading: true,
      submitStaInfo: {},
      haveFaultQues: false,
      quesIndex: 0, // 整个题目的Index

      staTimer: null,
      time: 1000,
      showOldDialog: false,
      confirmLeaveDialog: false
    };
  },
  mixins: [wrong],
  computed: {
    // 未作答答题数
    answeredNum() {
      return this.allQuestions.filter(q => q.isAnswered).length;
    },

    // 第一个未作答的题目
    firstUndo() {
      return this.allQuestions.find(q => q.corrected === -1);
    },

    isNeedLeaveConfirm() {
      return this.allQuestions.length - this.answeredNum;
    },

    // 是否展示重做错题
    isShowRedoWrong() {
      return !this.isOldWrongs && this.allowRedoWrongs && !this.oteWrongQues.hidden;
    },

    // 有错题 & (错题集 || 错题练习)
    showWrongBtn() {
      return this.haveFaultQues && (this.isShowRedoWrong || this.isOldWrongs);
    },

    showThirdBtnText() {
      if (!this.haveFaultQues) return '';

      if (this.isShowRedoWrong) {
        return this.$t('pc_ote_btn_wrongQuesRedo' /* 重做错题 */);
      }

      if (this.isOldWrongs) {
        return this.$t('pc_ote_btn_practicefault' /* 错题练习 */);
      }
    },

    showCancelBtnText() {
      if (this.info.repeated) {
        return this.$t('pc_ote_btn_practiceagain' /* 再练一次 */);
      }

      return this.showThirdBtnText;
    },

    /**
     * 是否显示第三个按钮
     * if 是错题练习 | 随堂练习，则永远不显示第三个按钮
     * 如果允许重复练习，则第二个按钮一定显示，此时判断是否有错题，来看是否显示第三个按钮
     * 如果不允许重复练习，则直接显示false,因为错题练习按钮如果有的话也会显示在第二个按钮上
     */
    showThirdBtn() {
      if (this.isWrongQuestionMode || this.coursePractice) return false;
      return this.info.repeated && this.showWrongBtn;
    },

    /**
     * 是否显示第二个按钮
     * if 是错题练习，则看是否允许重复练习，允许则显示再练一次，否则不显示第二个按钮
     * else 练习是否允许重复练习，如果允许则显示再练一次
     * 如果不允许重复练习，则看是否存在错题，如果有错题，显示错题练习 | 重做练习
     * 如果没有错题，则不显示第二个按钮
     */
    showCancelBtn() {
      if (this.isWrongQuestionMode) return this.info.repeated;
      return this.info.repeated || this.showWrongBtn;
    },

    btnInfo() {
      return {
        third: { show: this.showThirdBtn, text: this.showThirdBtnText },
        cancel: { show: this.showCancelBtn, text: this.showCancelBtnText }
      };
    },

    courseInfo() {
      return {
        praId: this.info.praId,
        praName: this.info.praName,
        repeated: this.info.repeated,
        type: 'practing'
      };
    }
  },

  methods: {
    // 练习答题
    linkToPractice() {
      const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;

      if (!this.deepStudy) {
        this.$router.replace({
          name: 'StuUserPractice',
          query: {
            ...routeParams,
            wq: 1
          }
        });
      } else {
        this.$emit('changeStep', this.UserPracticeStep.practicing, routeParams, true);
      }

      this.doOneMore();
    },

    goCancelPage() {
      if (this.info.repeated) {
        return this.doOneMore();
      }

      this.goThirdPage();
    },

    // 点击第三个按钮触发的事件
    goThirdPage() {
      if (this.isShowRedoWrong) {
        return this.linkToWrongs();
      }

      if (this.isOldWrongs) {
        return this.linkToPractice();
      }
    },

    // 根据结束返回的统计信息展示不同结束界面
    async getEndStaData(func) {
      try {
        const result = await getSimpleSta(this.info.puId);

        if (result.finished) {
          this.submitStaInfo = result || {};
          // 是否本次练习有错题
          this.haveFaultQues = result && result.errorQty > 0;
          func && typeof func === 'function' && func();
        } else {
          this.staTimer && window.clearTimeout(this.staTimer);
          if (this.time > 2000) {
            this.showOldDialog = true;
            func && typeof func === 'function' && func();
            return;
          }
          this.staTimer = setTimeout(() => {
            this.time += 1000;
            this.getEndStaData(func);
          }, 1000);
        }
      } catch (error) {
        this.submitDisable = false;
        this.handleError.bind(this)(error);
      }
    }
  }
};
