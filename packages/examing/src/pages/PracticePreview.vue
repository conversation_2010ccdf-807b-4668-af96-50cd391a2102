<template>
  <div class="box-size-borderbox flex flex-dir-ver" :class="coursePractice ? 'yxtulcd-practice-course' : 'ph24 minwidth850'">
    <div v-if="!deepStudy" class="yxtulcdsdk-uexam-preview__task width-percent-100">
      <stu-breadcrumb
        v-if="$route.name === 'StuPracticePreview' && isLoaded"
        :breadcrumb="breadcrumb"
        :current-name="$t('pc_ote_lbl_practicepreview')"
      />
      <yxtbiz-skip-task v-if="info.masterType === 1 && info.masterId" :biz-data="{taskId: praId, projectId: info.masterId, trackId, btid, gwnlUrl}" />
    </div>
    <div class="yxtulcdsdk-col-flex__main yxtulcdsdk-col-flex">
      <div v-if="isLoaded" class="yxtulcdsdk-uexam-preview yxtulcdsdk-uexam-preview--practice show-info yxtulcdsdk-col-flex yxtulcdsdk-col-flex__main" :class="deepStudy ? '' : 'mb0'">
        <div class="main-wrap">
          <div v-if="tabIndex === 0 && !coursePractice" class="yxtulcdsdk-uexam-preview-code box-size-contentbox" @click="togTab()">
            <yxt-svg-icon
              icon-class="code"
              class-name="code color-primary-6"
              width="40px"
              height="40px"
            />
            <div class="yxtulcdsdk-uexam-preview-code__mark"></div>
          </div>
          <div v-else-if="tabIndex === 1 && !coursePractice" class="yxtulcdsdk-uexam-preview-code box-size-contentbox" @click="togTab()">
            <yxt-svg-icon
              icon-class="computer"
              class-name="computer color-primary-6"
              width="40px"
              height="40px"
            />
            <div class="yxtulcdsdk-uexam-preview-code__mark"></div>
          </div>
          <div class="yxtulcdsdk-uexam-preview-content">
            <div class="main-top-wrap">
              <div class="yxtulcdsdk-uexam-preview-content__title">
                <span class="lh26 yxt-weight-5">{{ info.praName }}</span>
              </div>
              <!-- 练习时间 -->
              <div v-if="info.entryEndTime" class="yxtulcdsdk-uexam-preview-content__time">
                {{ $t('pc_ote_lbl_exercisetime') }}：<span class="ltr-text">{{ shortDateTime(info.entryStartTime) }}</span> {{ $t('pc_ote_lbl_to') }} <span class="ltr-text">{{ shortDateTime(info.entryEndTime) }}</span>
              </div>
              <div v-else class="yxtulcdsdk-uexam-preview-content__time">{{ $t('pc_ote_lbl_exercisetime') }}：{{ $t('pc_ote_lbl_timenotlimit') }}</div>
              <!-- 基本信息 -->
              <div v-if="tabIndex === 0" class="yxtulcdsdk-uexam-preview-content__base">
                <div class="yxtulcdsdk-uexam-preview-content__base-row-flex yxtulcdsdk-uexam-preview-content__base-row-flex--practice" :class="{'pv10': coursePractice}">
                  <div class="info-left" :style="deepStudy && getInfoLeftStyle('left')">
                    <div>
                      <!-- 试题总数 -->
                      <yxt-svg-icon
                        icon-class="pre-total"
                        class-name="total"
                        width="20px"
                        height="20px"
                      />
                      <div class="info-text" :class="{'info-text-course': coursePractice}">
                        <div class="color-gray-10">
                          <span>{{ $t('pc_ote_lbl_questotal') }}：</span>
                          <span class="yxt-weight-5">{{ $t('pc_ote_lbl_many_question',[info.totalQuesQty]/*{0}题*/) }}</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <!-- 错题数量 -->
                      <yxt-svg-icon
                        icon-class="wrong-preview"
                        class-name="exam-wrong"
                        width="20px"
                        height="20px"
                        is-new-svg-url
                      />
                      <div class="info-text" :class="{'info-text-course': coursePractice}">
                        <div class="color-gray-10">
                          <span>{{ $t('pc_ote_lbl_wrongquestionnum') }}：</span>
                          <span class="yxt-weight-5">{{ $t('pc_ote_lbl_many_question', [info.totalAnswerErrorQty]/*{0}题*/) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div :class="coursePractice ? 'info-line-course' : 'info-line'"><span></span></div>
                  <div class="info-right" :style="deepStudy && getInfoLeftStyle('right')">
                    <div>
                      <!-- 累计答题 -->
                      <yxt-svg-icon
                        icon-class="pre-excellence-score"
                        class-name="excellence-score"
                        width="20px"
                        height="20px"
                      />
                      <div class="info-text" :class="{'info-text-course': coursePractice}">
                        <div class="color-gray-10">
                          <span>{{ $t('pc_ote_lbl_cumulativeanswer') }}：</span>
                          <span class="yxt-weight-5">{{ $t('pc_ote_lbl_many_question', [info.totalAnswerQty]/*{0}题*/) }}</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <!-- 累计练习 -->
                      <yxt-svg-icon
                        icon-class="pre-exam-time"
                        class-name="exam-time"
                        width="20px"
                        height="20px"
                      />
                      <div class="info-text" :class="{'info-text-course': coursePractice}">
                        <div class="color-gray-10">
                          <span>{{ $t('pc_ote_lbl_cumulativeexercise') }}：</span>
                          <span class="yxt-weight-5">{{ secToMinSec(info.totalUsedTimes) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 二维码模式 -->
              <div v-if="tabIndex === 1" class="yxtulcdsdk-uexam-preview-content__code">
                <p>
                  <yxtbiz-qrcode
                    v-if="url"
                    :url="url"
                    :hide-link="true"
                    :hide-download="true"
                    :size="256"
                  />
                </p>
                <!-- 请在手机端扫码参加练习 -->
                <div class="yxtulcdsdk-uexam-preview-content__code--text font-size-16">{{ $t('pc_ote_msg_practicescantip') }}</div>
              </div>
            </div>
            <div class="yxtulcdsdk-uexam-preview-content__explain yxtulcdsdk-uexam-preview-content__explain--practice ">
              <!-- 练习说明 -->
              <div v-if="info.description" class="exam-rule">
                <div class="rule-title">
                  <!-- <yxt-svg-icon icon-class="pre-exam-notes"
                                class-name="exam-notes"
                                width="18px"
                                height="18px"/> -->
                  <span class="yxt-weight-5 ml0">{{ $t('pc_ote_lbl_exercisedesc') }}</span>
                </div>
                <div v-if="info.description" class="rule-info" v-html="dealLineFeed(info.description, true)">
                </div>
                <div v-else class="rule-info">{{ $t('pc_ote_lbl_none') }}</div>
              </div>

              <div v-if="showFieldList.length" class="exam-rule">
                <div class="rule-title">
                  <span class="yxt-weight-5 ml0">{{ $t('pc_ote_lbl_otherinfo' /* 其他信息 */) }}</span>
                </div>

                <div class="rule-info">
                  <div v-for="item in showFieldList" :key="item.id">
                    <!-- 单行文本 -->
                    <div v-if="item.fieldType === 0" class="mt8">
                      <yxt-tooltip
                        :content="item.detail"
                        placement="top"
                        open-filter
                      >
                        <div class="ellipsis">
                          {{ item.fieldName }}：{{ item.detail || '--' }}
                        </div>
                      </yxt-tooltip>
                    </div>
                    <!-- 多行文本 -->
                    <VueClamp v-else-if="item.fieldType === 1" :max-lines="3" class="mt8">
                      {{ item.fieldName }}：{{ item.detail || '--' }}
                      <template #after="{ clamped, toggle }">
                        <div v-if="clamped || expanded" class="color-primary-6 d-in-block" @click="toggle">
                          {{ clamped ? $t('pc_ote_btn_expand'/** 展开 */) : $t('pc_ote_btn_putaway'/** 收起 */) }}
                        </div>
                      </template>
                    </VueClamp>
                    <!-- 选项 -->
                    <div v-else-if="item.fieldType === 2" class="mt8">
                      {{ item.fieldName }}：{{ getOptionDetail(item) }}
                    </div>
                    <!-- 时间 -->
                    <div v-else-if="item.fieldType === 3" class="mt8">
                      {{ item.fieldName }}：{{ dateFormatUtil(item.detail,'yyyy-MM-dd HH:mm') }}
                    </div>
                    <!-- 时间范围 -->
                    <div v-else-if="item.fieldType === 4" class="mt8">
                      {{ item.fieldName }}：{{ formatTimeRange(item) }}
                    </div>
                    <!-- 数值 -->
                    <div v-else-if="item.fieldType === 6" class="mt8">
                      {{ item.fieldName }}：{{ item.detail || '--' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 练习都对了 -->
        <div v-if="(info.upmStatus >= 3 || info.userBatchStatus === LoopPracticeStatus.done) && !info.repeated && info.totalAnswerErrorQty === 0 && info.totalAnswerCorrectQty !== 0" class="yxtulcdsdk-uexam-preview-bottom yxtulcdsdk-uexam-preview-bottom--practice">
          <yxt-svg-icon icon-class="tip-good" width="36px" height="36px" />
          <span class="color-gray-10 font-size-18 ml12">{{ $t('pc_ote_msg_practicedoneall' /* 真棒，已答对全部练习题 */) }}</span>
        </div>
        <!-- 练习结束、归档 -->
        <div v-else-if="info.praStatus >= 3 || info.userBatchStatus === LoopPracticeStatus.expired" class="yxtulcdsdk-uexam-preview-bottom yxtulcdsdk-uexam-preview-bottom--practice">
          <yxt-svg-icon icon-class="tip-warning" width="36px" height="36px" />
          <span class="color-gray-10 font-size-18 ml12">{{ $t('pc_ote_msg_practiceended' /* 练习时间已结束 */) }}</span>
        </div>
        <!-- 不能再做了（不能重复练习，错题集做完了） -->
        <div v-else-if="!getBtnText().isShowBtn && !isOldWrongs && (!allowRedoWrongs || !oteWrongQues.enabled)" class="yxtulcdsdk-uexam-preview-bottom yxtulcdsdk-uexam-preview-bottom--practice">
          <yxt-svg-icon icon-class="tip-good" width="36px" height="36px" />
          <span class="color-gray-10 font-size-18 ml12">{{ $t('pc_ote_msg_practicefinished'/** 恭喜您已完成当前练习 */) }}</span>
        </div>
        <common-sticky
          v-else
          ref="btnSticky"
          :by-class="deepStudy"
        >
          <div v-if="showTimeCounter" class="yxtulcdsdk-uexam-preview-bottom">
            <div class="date-time">
              <!-- 天时分秒 后开始 -->
              <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.dd }}</span></i><span class="unit">{{ $t('pc_ote_lbl_day') }}</span></span>
              <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.hh }}</span></i><span class="unit">{{ $t('pc_ote_lbl_hour') }}</span></span>
              <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.mm }}</span></i><span class="unit">{{ $t('pc_ote_lbl_minute') }}</span></span>
              <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.ss }}</span></i><span class="unit">{{ $t('pc_ote_lbl_second') }}&nbsp;&nbsp;{{ $t('pc_ote_lbl_thenstart') }}</span></span>
            </div>
          </div>
          <div v-else class="yxtulcdsdk-uexam-preview-bottom">
            <yxtf-button
              v-if="getBtnText().isShowBtn"
              :disabled="checking"
              :type="showWrongBtn ? '' : 'primary'"
              :plain="showWrongBtn"
              size="larger"
              @click="goPracticeCheck()"
            >
              {{ getBtnText().startText }}
            </yxtf-button>
            <template v-if="showWrongBtn">
              <yxtf-button
                v-if="!isOldWrongs && showWrongQuesBtn"
                v-checkButtons="oteWrongQues.value"
                :disabled="oteWrongQues.disable || checking"
                type="primary"
                size="larger"
                @click=" linkToWrongs()"
              >
                {{ $t( 'pc_ote_lbl_redoWrongQuestion' ) }}
              </yxtf-button>
              <yxtf-button
                v-if="isOldWrongs"
                :disabled="checking"
                type="primary"
                size="larger"
                @click=" goPracticeCheck(1) "
              >
                {{ $t( 'pc_ote_lbl_startwrongpractice' ) }}
              </yxtf-button>
            </template>
          </div>
        </common-sticky>
      </div>
    </div>
  </div>
</template>
<script>
import { generateShortUrl } from '../core/bizUtil';
import { getPraPreview, checkPraStart } from '../service/user.service';
import CommonSticky from './components/CommonSticky.vue';
import { shortDateTime, dealLineFeed, dateFormatUtil } from '../core/utils';
import StuBreadcrumb from './components/StuBreadcrumb.vue';
import VueClamp from 'vue-clamp';
import { LoopPracticeStatus } from '../core/enums';
import deepStudyPage from '../mixins/deepStudyPage';
import YxtSvgIcon from './components/svgIcon.vue';
import wrong from './mixins/wrong';

export default {
  components: {
    CommonSticky,
    StuBreadcrumb,
    VueClamp,
    YxtSvgIcon
  },
  mixins: [deepStudyPage, wrong],
  inject: ['getWidth'],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      trackId: routeParams.trackId,
      btid: routeParams.btid,
      LoopPracticeStatus,
      checking: false, // 是否检查状态中
      loadedQrcode: false, // 是否加载过二维码
      praId: routeParams.praId,
      info: {},
      tabIndex: 0,
      url: '',
      isLoaded: false,
      showTimeCounter: false, // 未开始倒计时
      countDownInfo: {
        dd: '0',
        hh: '0',
        mm: '0',
        ss: '0'
      },
      timer: '',
      times: 0,
      breadcrumb: [],
      oteExamPraId: 'ote_pra_' + routeParams.praId,
      gwnlUrl: routeParams.gwnlUrl,
      praBatchId: routeParams.praBatchId
    };
  },
  computed: {
    courseInfo() {
      return {
        praId: this.info.praId,
        praName: this.info.praName,
        repeated: this.info.repeated,
        type: 'preview'
      };
    },

    showFieldList() {
      return this.info.customFieldProject
        ? this.info.customFieldProject.filter((item) => {
          return (item.visible && !!item.detail && item.detail !== 'undefined') || item.detail === 0;
        })
        : [];
    },
    // 是否展示错题练习 ｜ 重做错题按钮
    showWrongBtn() {
      return this.info.totalAnswerErrorQty > 0 && !this.coursePractice && (this.isOldWrongs || (!this.isOldWrongs && this.showWrongQuesBtn));
    },

    currentParams() {
      return {
        trackId: this.trackId,
        praId: this.praId,
        praBatchId: this.praBatchId,
        btid: this.btid,
        gwnlUrl: this.gwnlUrl // 人才发展url进行面包屑点击回退
      };
    }
  },

  watch: {
    '$route.query.praId'(v) {
      !this.deepStudy && this.$router.go(0);
    }
  },

  created() {
    this.getDetail();
  },
  methods: {
    /** 练习状态
     * 1，进到这个页面都是已发布的
     * 2，练习分为
     *    1，单个练习：使用 upmStatus 和 praStatus 判断
     *    2，循环练习的子练习 使用 userBatchStatus 判断
     * 3，单个和循环练习都有是否重复练习的标识
     *
     * 需要得到的状态：
     *    1，开始练习（按钮
     *    2，再练一次（按钮
     *    3，有错题：（按钮） 错题练习
     *    4，没有错题：（文字）真棒，已答对全部练习题
     *    5，练习结束（文字）练习时间已结束
     *
     * 本页面实际逻辑：
     * 1，判断状态 4
     * 2，判断状态 5
     * 3，练习还没到开始时间，显示倒计时
     * 4，判断状态 1
     * 5，判断状态 2
     * */
    // 按钮文字
    getBtnText() {
      // userBatchStatus 0:开始练习,1-再练一次,2-已完成,3-已过期
      const userBatchStatusMap = [
        {
          startText: this.$t('pc_ote_btn_startpractice'), // 开始练习
          isShowBtn: true // true-展示按钮 false-展示文本
        },
        {
          startText: this.$t('pc_ote_btn_practiceagain'), // 再练一次
          isShowBtn: true // true-展示按钮 false-展示文本
        }
      ];
      if (this.praBatchId) {
        if ([LoopPracticeStatus.start, LoopPracticeStatus.repeat].includes(this.info.userBatchStatus)) {
          return userBatchStatusMap[this.info.userBatchStatus];
        } else {
          return {
            isShowBtn: false
          };
        }
      } else {
        if ((this.info.repeated || this.info.upmStatus < 3) && this.info.praStatus === 2) {
          if (this.info.upmStatus >= 3 && this.info.repeated) {
            return {
              startText: this.info.lastAttendStatus === 1 ? this.$t('pc_ote_lbl_practicecontinue') : this.$t('pc_ote_btn_practiceagain'), // 再练一次
              isShowBtn: true // true-展示按钮 false-展示文本
            };
          } else {
            return {
              startText: this.info.lastAttendStatus === 1 ? this.$t('pc_ote_lbl_practicecontinue') : this.$t('pc_ote_btn_startpractice'), // 开始练习
              isShowBtn: true // true-展示按钮 false-展示文本
            };
          }
        }
      }

      return {};
    },
    // 保存项目来源处理课程详情面包屑导航
    setSessionStorage(masterId) {
      const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
      const session = {
        taskId: routeParams.taskId,
        targetId: routeParams.targetId,
        targetCode: routeParams.targetCode
      };
      if (session.taskId && session.targetId && session.targetCode === 'o2o') {
        sessionStorage[this.oteExamPraId] = JSON.stringify(session);
      }
    },
    dealLineFeed,
    // 切换右上角
    togTab() {
      this.tabIndex = this.tabIndex === 0 ? 1 : 0;
      if (!this.loadedQrcode) {
        this.loadedQrcode = true;
        this.getUrl();
      }
    },
    async getUrl() {
      let h5Url = `stu/practicepreview?praId=${this.praId}`;
      if (this.praBatchId) h5Url += `&praBatchId=${this.praBatchId}`;
      generateShortUrl(h5Url, '', 0, this.info.groupDataType === 1, this.info.projectOrgId).then(res => {
        if (res && res.url) {
          this.url = res.url;
        }
      }).catch(err => { this.handlerPublicError(err, false); });
    },
    getDetail() {
      this.isLoaded = false;
      this.breadcrumb = [];

      const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
      const { masterId, masterType, packageId, praBatchId } = routeParams;
      getPraPreview(this.praId, masterId, masterType, packageId, praBatchId).then(async res => {
        this.info = res;
        /**
         * M5V2对接新的课程包考试、练习方案，课程包内考试、练习会归为项目类型。ID会重新生成
         */
        if (this.info.masterBizCode === 1) {
          this.info.masterType = 2; // 这里按课程包处理页面
          this.praId = this.info.praId; // 换为真正的考试安排ID
          this.info.masterId = this.info.masterSecondId || this.info.masterId; // 换为课程包的ID
          this.oteExamPraId = 'ote_pra_' + this.praId;
        }

        await this.initRedoWrong(this.praId);
        this.isLoaded = true;
        // 倒计时处理
        if (this.info.entryStartTime) {
          const currentTime = Date.parse(this.info.currentSystemDate.replace(/-/g, '/'));
          const startTime = Date.parse(this.info.entryStartTime.replace(/-/g, '/'));
          if (currentTime < startTime) {
            this.showTimeCounter = true;
            this.times = startTime - currentTime;
            this.handleTimeStr();
            this.timer = setInterval(() => {
              this.handleTimeStr();
            }, 1000);
          } else {
            this.showTimeCounter = false;
          }
        }
        // 面包屑
        const myEnt = {
          url: '/study/#/userhome',
          name: this.$t('pc_ote_lbl_mylearning')
        };
        this.breadcrumb.push(myEnt);
        if (this.info.masterId) {
          this.setSessionStorage(this.info.masterId);
          // 0-单独安排 1-项目安排 2-课程安排
          switch (this.info.masterType) {
            case 1:
              this.breadcrumb.push({
                url: `/o2o/#/project/detail/${this.info.masterId}`,
                name: this.$t('pc_ote_lbl_traindetail')
              });
              break;
            case 2:
              let url = '';
              if (sessionStorage[this.oteExamPraId]) {
                const session = JSON.parse(sessionStorage[this.oteExamPraId]);
                url = `&targetId=${session.targetId}&taskId=${session.taskId}&targetCode=${session.targetCode}&trackId=${this.trackId}&btid=${this.btid}`;
              }
              this.breadcrumb.push({
                url: `/kng/#/course/detail?courseId=${this.info.masterId}${url}`,
                name: this.$t('pc_ote_lbl_coursedetail')
              });
              break;
            case 4:
              this.breadcrumb.push({
                url: this.gwnlUrl || '/gwnl/#/web/map',
                name: this.$t('pc_ote_lbl_talentdevelopment')
              });
              break;
            default:
              this.breadcrumb.push({
                path: '/stu/myexam?pra=1',
                name: this.$t('pc_ote_lbl_exampracticecenter')
              });
              break;
          }
        } else {
          this.breadcrumb.push({
            path: '/stu/myexam?pra=1',
            name: this.$t('pc_ote_lbl_exampracticecenter')
          });
        }

        if (this.coursePractice) {
          this.handlerCoursePracticeEvent('success');
        }
      }).catch((err) => {
        this.handlerPublicError(err);
      });
    },
    shortDateTime,
    dateFormatUtil,
    formatTimeRange(item) {
      if (!item.detail) return '--';
      try {
        const data = JSON.parse(item.detail);
        return dateFormatUtil(data[0], 'yyyy-MM-dd HH:mm') + ' ' + this.$t('pc_ote_lbl_to' /* 至 */) + ' ' + dateFormatUtil(data[1], 'yyyy-MM-dd HH:mm');
      } catch (error) {
        return '--';
      }
    },
    getOptionDetail(item) {
      if (!item.detail || !item.fieldOptions || item.fieldOptions.length === 0) return '--';
      const data = item.fieldOptions.find(d => d.lanKey === item.detail);
      return data ? data.content : '--';
    },
    // 跳转至答题页面
    goPracticeCheck(isWrong) {
      if (!isWrong) {
        this.checking = true;
        checkPraStart(this.praId).then(() => {
          this.practicing();
        }).catch(() => {
          // 检查有异常做刷新处理
          this.getDetail();
        }).finally(() => {
          this.checking = false;
        });
      } else {
        this.practicing(isWrong);
      }
    },

    // 跳转至答题页面
    practicing(isWrong) {
      this.$emit('changeStep', this.UserPracticeStep.practicing, {
        trackId: this.trackId,
        praId: this.praId,
        wq: isWrong ? '1' : '0',
        praBatchId: this.praBatchId,
        btid: this.btid,
        gwnlUrl: this.gwnlUrl
      });
    },
    // 处理倒计时文本
    handleTimeStr() {
      if (this.times >= 0) {
        this.countDownInfo.dd = Math.floor(this.times / 1000 / 60 / 60 / 24);
        this.countDownInfo.hh = Math.floor((this.times / 1000 / 60 / 60) % 24);
        this.countDownInfo.mm = Math.floor((this.times / 1000 / 60) % 60);
        this.countDownInfo.ss = Math.floor((this.times / 1000) % 60);
        if (this.times === 0) {
          window.clearInterval(this.timer);
          this.getDetail();
        } else {
          this.times = this.times - 1000;
        }
      }
    },
    secToMinSec(sec) {
      let str = '';
      const min = Math.floor(sec / 60);
      sec = sec % 60;
      if (min > 0) {
        str += min + this.$t('pc_ote_lbl_minutes');
      }
      if (sec > 0 || !str) {
        str += sec + this.$t('pc_ote_lbl_second');
      }
      return str;
    },
    // this.getWidth()=> 860-1248之间，paddingLeft => 24-64之间
    getInfoLeftStyle(type) {
      // 获取left百分比
      let left = (this.getWidth() - 860) / (1248 - 860) * (64 - 24) + 24;
      if (left < 24) {
        left = 24;
      } else if (left > 64) {
        left = 64;
      }

      // 因为随课练习下中间的分隔宽度置为 0，所以不需要再次减去 16px
      if (!this.coursePractice) {
        if (type === 'right') {
          left -= 16;
        }
      }

      return {
        paddingLeft: left + 'px'
      };
    }
  },
  beforeRouteUpdate(to, from, next) {
    if (from.query.praId && to.query.praId !== from.query.praId) {
      next();
      window.location.reload();
    } else {
      next();
    }
  },
  beforeDestroy() {
    if (this.timer) {
      window.clearInterval(this.timer);
    }
  }
};
</script>
