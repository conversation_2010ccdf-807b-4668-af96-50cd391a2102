<template>
  <div
    v-loading="loading"
    class="box-size-borderbox "
  >
    <CommonSticky
      v-if="!exporting"
      ref="stickyTop"
      position="top"
      class="z-999"
      :by-class="deepStudy"
    >
      <yxt-page-header
        icon-hot
        :title="$t( 'pc_ote_tit_viewpaper' /** 查看答卷 */)"
        size="small"
        @back="back()"
      >
        <span slot="title-right">
          <!-- 导出PDF-->
          <yxtf-button
            v-if="showPDFExport"
            :loading="downloading"
            plain
            @click="startExportPdf"
          >
            {{ $t('pc_ote_btn_exportpdf') }}
          </yxtf-button>
          <!-- 重新批阅-->
          <yxtf-button
            v-if="(routeSrc !== 2 && info.reApprove)"
            plain
            @click="remarkPaper()"
          >
            {{ $t('pc_ote_lbl_markoncemore') }}
          </yxtf-button>

        </span>
      </yxt-page-header>
    </CommonSticky>
    <div
      class="yxtulcdsdk-pc-marking-top-wrapper"
    >
      <!-- 中间主体 -->
      <div
        v-show="!loading"
        class="yxtulcdsdk-pc-marking-main pt16 pb24 ph24 yxtulcdsdk-pc-special"
        :class="{'img-no-point-event': info.allowPictureCopy === 0 && isLearner, 'text-no-select': info.allowCharacterCopy === 0 && isLearner}"
      >
        <div class="yxtulcdsdk-review-fixed-content">
          <div class="marking-shadow yxtulcdsdk-marking-exam-top pr">
            <div class="yxtulcdsdk-marking-exam-top-title">
              <yxtf-tooltip
                :content="info.examName"
                placement="top"
                open-filter
                :max-width="780"
              >
                <span
                  class="standard-size-20 color-gray-10  yxtulcdsdk-marking-name yxt-weight-5"
                  :class="{
                    ellipsis: !exporting
                  }"
                >
                  {{ info.examName }}
                </span>
              </yxtf-tooltip>
              <div class="yxtulcdsdk-marking-exam-top-title__tag">
                <!-- 通过、不通过 -->
                <custom-icon
                  v-if="((routeSrc === 2 && info.allowViewResultPassed === 1) || routeSrc === 0 || routeSrc === 1) && !isMarking"
                  width="66px"
                  height="66px"
                  :type="info.score >= info.passScore ? 'sucess':'error'"
                >
                  {{ $t(info.score >= info.passScore ? 'pc_ote_lbl_passed':'pc_ote_lbl_unpass') }}
                </custom-icon>
              </div>
            </div>
            <div class="yxtulcdsdk-info-list mt4">
              <!-- 考试成绩 -->
              <div v-if="exporting && !isMarking">
                <span>{{ $t('pc_ote_lbl_examscore') }}</span>
                <span v-if="routeSrc === 2 && info.viewResultScore === 0">*</span>
                <span v-else :class="routeSrc === 2 && info.allowViewResultPassed === 0 ? '': (info.score < info.passScore ? 'color-red-6' : 'color-green-6')">{{ info.score }}</span>
              </div>
              <!-- 试卷总分 -->
              <div>
                <span>{{ $t('pc_ote_lbl_exampapertotalscore') }}</span>
                <span>{{ info.totalScore }}</span>
              </div>
              <!-- 通过分数 -->
              <div v-if="exporting">
                <span>{{ $t('pc_ote_lbl_passscore') }}</span>
                <span>{{ routeSrc === 2 && info.allowViewResultPassed === 0 ? '*' : info.passScore }}</span>
              </div>
              <!-- 提交时间 -->
              <div>
                <span>{{ $t('pc_ote_lbl_submittime') }}</span>
                <span class="ltr-text">{{ info.submitTime || '--' }}</span>
              </div>
              <!-- 批阅人 -->
              <div v-if="routeSrc !== 2 && info.status === 3">
                <span>{{ $t('pc_ote_lbl_reviewer') }}</span>
                <span><yxtbiz-user-name :name="(info.markerUsers && info.markerUsers.join('、')) || '--'" /></span>
              </div>
            </div>
            <div class="yxtulcdsdk-info-list">
              <div>
                <span>{{ $t('pc_ote_lbl_examinee' /* 考生 */) }}</span>
                <span><yxtbiz-user-name :name="info.fullname" /><template v-if="info.userName || info.username">（{{ info.userName || info.username }}）</template></span>
              </div>
              <div>
                <span>{{ $t('pc_ote_lbl_job_no' /* 工号 */) }}</span>
                <span>{{ info.userNo || '--' }}</span>
              </div>
              <div>
                <span>{{ $t('pc_ote_lbl_department' /* 部门 */) }}</span>
                <yxtbiz-dept-name :name="info.deptName || '--'" />
              </div>
              <div>
                <span>{{ $t('pc_ote_lbl_position' /* 岗位 */) }}</span>
                <span>{{ info.positionName || '--' }}</span>
              </div>
            </div>
          </div>
          <!-- 暂无错误题目 -->
          <div v-show="onlyWrong && totalFaultCount === 0" class="marking-shadow pt4 mt16 font-size-14">
            <yxtf-empty class="pt100 pb100" :empty-text="$t('pc_ote_msg_nowrongques')" />
          </div>
          <template v-for="(typeObj, indexType) in quesTypesList">
            <div v-if="(onlyWrong && typeObj.faultCount > 0) || (!onlyWrong && typeObj.quesList.length > 0)" :key="indexType" class="marking-shadow pt4 mt12 font-size-14">
              <div class="color-gray-10 lh20 ph24 mv16 yxt-weight-5">
                {{ typeObj.title }}
                <span class="color-gray-7 font-size-12 font-normal">{{ typeObj.titleDesc }}</span>
              </div>
              <yxt-divider class="mt20 mb18" />
              <!-- 组合题题干 -->
              <user-ques-view
                v-if="typeObj.ques && typeObj.ques.type === 5"
                class="mt24 mb32 ph24"
                :value="typeObj.ques"
                :show-answer="false"
                :show-explain="showExplain"
              />
              <div class="ph24 pb24">
                <template v-for="(item, index) in typeObj.quesList">
                  <div v-if="!onlyWrong || (onlyWrong && item.faulted === 1)" :key="item.id">
                    <div :ref="item.id">
                    </div>
                    <!-- 单题展示 -->
                    <user-ques-view
                      v-model="typeObj.quesList[index]"
                      :info="info"
                      :class="typeObj.quesList && index !== typeObj.quesList.length - 1 ? 'mv24' : 'mt24'"
                      :ue-id="ueId"
                      :index="index"
                      :route-src="routeSrc"
                      :show-answer="showAnswer"
                      :show-explain="showExplain"
                      :only-wrong="onlyWrong"
                      :show-point="!(typeObj.ques && typeObj.ques.type === 5)"
                    />
                    <div v-show="typeObj.quesList && index !== typeObj.quesList.length - 1">
                      <yxt-divider />
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
        <div v-if="info && info.signShowAnswerDetail && info.nameSignUrl" class="marking-shadow yxtulcdsdk-pc-marking-signimg">
          <span class="color-gray-7 font-size-14 mr12">{{ $t('pc_ote_lbl_graph') }}：</span>
          <yxt-image
            :src="info.nameSignUrl"
            :preview-src-list="[info.nameSignUrl]"
          />
        </div>
      </div>
      <!-- 答题卡 -->
      <CommonSticky
        v-if="!loading && !exporting && !isMarking"
        ref="stickyRight"
        position="top"
        class="mr24 yxtulcdsdk-flex-shrink-0 mt16"
        :offset="topHeight"
        :by-class="deepStudy"
        :style="getAnswerCardStyle()"
      >
        <div
          class="yxtulcdsdk-pc-right"
          :style="getAnswerCardStyle()"
        >
          <div
            :style="getAnswerCardStyle()"
            class="yxtulcdsdk-answer-card yxtulcdsdk-answer-card--result"
          >
            <div class="yxtulcdsdk-answer-card__top ph16 font-size-14">
              <!-- 考试成绩 -->
              <div class="text-left color-gray-10">
                <span>{{ $t('pc_ote_lbl_examscore') }}</span>
                <span v-if="routeSrc === 2 && info.viewResultScore === 0" class="standard-size-24 ml8">**</span>
                <span v-else :class="[routeSrc === 2 && info.allowViewResultPassed === 0 ? '': (info.score < info.passScore ? 'color-red-6' : 'color-green-6'), 'standard-size-24 ml8']">{{ info.score }}</span>
              </div>
              <!-- 通过分数 -->
              <div class="text-left color-gray-7 mt8">
                <span>{{ $t('pc_ote_lbl_passscore') }}</span>
                <span class="ml8">{{ routeSrc === 2 && info.allowViewResultPassed === 0 ? '**' : info.passScore }}</span>
              </div>
              <div v-if="routeSrc === 0 || routeSrc === 1 || routeSrc === 2"><yxtf-divider class="mv16" /></div>
              <div v-if="routeSrc === 0 || routeSrc === 1" class="d-in-block">
                <!-- 只看错题 -->
                <div class="mb16"><yxtf-checkbox v-model="onlyWrong">{{ $t('pc_ote_btn_onlywronganswer') }}</yxtf-checkbox></div>
                <!-- 显示正确答案 -->
                <div class="mb16"><yxtf-checkbox v-model="showAnswer">{{ $t('pc_ote_btn_viewcorrectanswer') }}</yxtf-checkbox></div>
                <!-- 展开所有解析 -->
                <div><yxtf-checkbox v-model="showExplain">{{ $t('pc_ote_btn_expandallexplain') }}</yxtf-checkbox></div>
              </div>
              <!-- 学员查看操作控制 -->
              <div v-else-if="routeSrc === 2" class="d-in-block">
                <div class="mb16"><yxtf-checkbox v-model="onlyWrong" :disabled="onlyWrongDisabled">{{ $t('pc_ote_btn_onlywronganswer') }}</yxtf-checkbox></div>
                <div class="mb16"><yxtf-checkbox v-model="showAnswer" :disabled="info.allowViewResultDetailAnswer === 0">{{ $t('pc_ote_btn_viewcorrectanswer') }}</yxtf-checkbox></div>
                <div><yxtf-checkbox v-model="showExplain" :disabled="info.allowViewResultDetailExplain === 0">{{ $t('pc_ote_btn_expandallexplain') }}</yxtf-checkbox></div>
              </div>
            </div>
            <div class="yxtulcdsdk-answer-card__bottom color-gray-10">
              <div class="wp-100 mt8 text-left pl16 box-size-borderbox font-size-18 mb12 yxt-weight-5 lh26">{{ $t('pc_ote_lbl_answercard') }}</div>
              <div class="wp-100">
                <div class="yxtulcdsdk-ques-answer_card__type">
                  <span>
                    <i class="yxtulcdsdk-ques-answer_type__1"></i>
                    <small class="ellipsis">{{ $t('pc_ote_lbl_noanswer') }}</small>
                  </span>
                  <span>
                    <i class="yxtulcdsdk-ques-answer_type__2"></i>
                    <small class="ellipsis">{{ $t('pc_ote_lbl_correct') }}</small></span>
                  <span>
                    <i class="yxtulcdsdk-ques-answer_type__3"></i>
                    <small class="ellipsis">{{ $t('pc_ote_lbl_wrong') }}</small>
                  </span>
                </div>
              </div>
              <yxt-divider class="mv16" />
              <div class="wp-100 mheight48 flex flex-j-stretch over-auto yxtulcdsdk-flex-shrink-1">
                <yxt-scrollbar :fit-height="true" class="wp100" :style="{height: 'unset'}">
                  <template v-for="(type, indexType) in quesTypesList">
                    <div
                      v-if="(onlyWrong && type.faultCount > 0) || (!onlyWrong && type.quesList && type.quesList.length > 0)"
                      :key="indexType"
                      :class="{
                        'mb12': indexType !== quesTypesList.length - 1
                      }"
                      class="ph16"
                    >
                      <div class="ellipsis font-size-14 mb16">{{ type.title }}</div>
                      <ul class="yxtulcdsdk-pc-card-list font-size-12 clearfix">
                        <template v-for="(item, index) in type.quesList">
                          <li
                            v-if="!onlyWrong || (onlyWrong && item.faulted === 1)"
                            :key="item.id"
                            :class="item.faulted === 0 ? 'hand success' : (item.faulted === 1 || (item.type === 3 || item.type === 4) ? 'hand error' : 'hand')"
                            @click="scrollView(item)"
                          >
                            {{ index + 1 }}
                          </li>
                        </template>
                      </ul>
                    </div>
                  </template>
                </yxt-scrollbar>
              </div>
            </div>

          </div>
        </div>
      </CommonSticky>
    </div>

    <yxtbiz-watermark
      v-if="info.allowAddWatermark && ~~watermarkConfig.enabled"
      class="z-999 ote-review-watermark"
      :option="watermarkConfig"
      :show="true"
      app-code="ote"
      version="v2"
    />
    <!-- pdf导出标记 -->
    <img
      v-if="exporting && dataRenderOver"
      class="pdf-render-flag"
      src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg=="
      alt=""
    >
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import { QUES_TYPE_NAMES } from '../configs/const';
import { getExamPaperAdmin, getMarkingDetailAdmin, getMarkingDetail } from '../service/review.service';
import { getUserPaperDetail } from '../service/user.service';
import { getQuesTypeIndexName, convertASCIIForNum, isQW, getScrollParent, mergeAnswerFun } from '../core/utils';
import UserQuesView from './question/UserQuesView.vue';
import CustomIcon from './components/customIcon.vue';
import pdfExport from '../mixins/pdfExport';
import { getOrgWatermarkConfig } from '../service/config.service';
import deepStudyPage from '../mixins/deepStudyPage';
import CommonSticky from './components/CommonSticky.vue';
export default {
  components: {
    UserQuesView,
    CustomIcon,
    CommonSticky
  },
  inject: ['getHeight', 'getWidth', 'getFSWidth'],
  mixins: [pdfExport, deepStudyPage],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      topHeight: 56 + 16,
      mediaUrl: '',
      ueId: routeParams.ueId, // 用户试卷ID（如果有值传入，则定位到指定用户试卷）
      arrName: '',
      routeSrc: 0,
      isShowMore: false,
      isShowMoreText: false,
      allQuestions: [], // 所有的单题
      questionsList: [],
      singleChoiceQues: [], // 单选题
      multiChoiceQues: [], // 多选题
      judgeQues: [], // 判断题
      fillQues: [], // 填空题
      ansQues: [], // 问答题
      combQuesDic: {}, // 组合题 字典
      combQuesFaultDic: {}, // 组合题 错误数字典
      combQuesSubListsDic: {}, // 组合题子题 字典
      quesTypesList: [], // 处理过的所有题型及内部的题目
      titleSettings: [], // 标题的设置
      info: {
        notAllowDownloadPdf: true
      },
      params: {
        limit: 5,
        current: 1,
        orderby: 'submitTime',
        direction: 'DESC'
      },
      // 错误数量统计
      singleFaultCount: 0,
      multiFaultCount: 0,
      judgeFaultCount: 0,
      fillFaultCount: 0,
      ansFaultCount: 0,
      totalFaultCount: 0,
      onlyWrongDisabled: false,
      onlyWrong: false, // 只显示错题
      showAnswer: false, // 显示正确答案
      showExplain: false, // 展开试题解析
      typeTitles: {}, // 试卷题型标题
      loading: true,
      watermarkConfig: {
        enabled: 1,
        type: 1,
        opacity: 80,
        text: ' ',
        fontSize: 30,
        color: '#000'
      },
      dataRenderOver: false
    };
  },
  created() {
    const frmName = this.$route.name;
    if (frmName === 'examresultdetail' || frmName === 'groupexamresultdetail') {
      this.routeSrc = 0; // 考试跟踪入口
    } else if (frmName === 'examresultpaper' || frmName === 'groupexamresultpaper') {
      this.routeSrc = 1; // 后台批阅（仅控制是否匿名，请求/mark/view接口）
    } else if (frmName === 'StuMyExamResultPaperP' || frmName === 'StuExamResultPaperManager') {
      this.routeSrc = 3; // 前台批阅、我的团队查看
    } else if (frmName === 'StuMyExamResultPaper') {
      this.routeSrc = 2; // 学员查看答卷（控制是否允许查看成绩等）
    } else {
      this.routeSrc = 2; // 默认 学员查看答卷（控制是否允许查看成绩等）
    }
    this.getDetail();
    this.dealStickyEvent(1);
    if (this.isLearner) {
      getOrgWatermarkConfig()
        .then(config => {
          this.watermarkConfig = config;
        })
        .catch(err => this.handleError(err));
    }
  },
  computed: {
    // 是否是学员端
    isLearner() {
      return this.deepStudy || ['examresultpaperp', 'myexamresultpaper', 'StuMyExamResultPaper', 'StuMyExamResultPaperP', 'StuExamResultPaperManager'].includes(this.$route.name);
    },
    showPDFExport() {
      return !this.exporting && !isQW() && !(this.routeSrc === 2 && this.info.notAllowDownloadPdf);
    },
    quesOfType() {
      return [
        this.singleChoiceQues,
        this.multiChoiceQues,
        this.judgeQues,
        this.fillQues,
        this.ansQues,
        this.combQuesSubListsDic
      ];
    },
    faultOfType() {
      return [
        this.singleFaultCount,
        this.multiFaultCount,
        this.judgeFaultCount,
        this.fillFaultCount,
        this.ansFaultCount,
        this.combQuesFaultDic
      ];
    },
    orgId() {
      return window.localStorage.getItem('orgId') || '';
    },
    // 答卷是否处于批阅中
    isMarking() {
      return this.info.status <= 3;
    }
  },
  methods: {
    ...mapActions(['setTicket']),
    isQW,
    startExportPdf() {
      this.pdfName = `${this.info.arrName.length > 50 ? (this.info.arrName.substring(0, 50) + '...') : this.info.arrName}-${this.info.fullname}`;
      let urlNow = window.location.href;

      // 沉浸式导出ote项目的页面
      if (this.deepStudy) {
        urlNow = `${window.location.origin}/ote/#/stu/myexamresultpaper`;
        for (const key in this.queryDatas) {
          if (Object.hasOwnProperty.call(this.queryDatas, key)) {
            urlNow += `${urlNow.indexOf('?') >= 0 ? '&' : '?'}${key}=${this.queryDatas[key]}`;
          }
        }
      }

      urlNow += `${urlNow.indexOf('?') >= 0 ? '&' : '?'}ow=${this.onlyWrong ? '1' : '0'}`;
      urlNow += `${urlNow.indexOf('?') >= 0 ? '&' : '?'}sw=${this.showAnswer ? '1' : '0'}`;
      urlNow += `${urlNow.indexOf('?') >= 0 ? '&' : '?'}se=${this.showExplain ? '1' : '0'}`;

      this.exportPdf(urlNow, '.yxtulcdsdk-pc-marking-top-wrapper');
    },
    // 处理左右两个sticky的监听
    dealStickyEvent(type) {
      switch (type) {
        case 0:
          this.resizeHander();
          break;
        case 1:
          window.addEventListener('resize', this.resizeHander);
          break;
        case 2:
          window.removeEventListener('resize', this.resizeHander);
          break;
        default:
          break;
      }
    },
    resizeHander() {
      if (!this.exporting) {
        this.$refs.stickyTop && this.$refs.stickyTop.scrollHandler();
        this.$refs.stickyRight && this.$refs.stickyRight.scrollHandler();
      }
    },
    getDetail() {
      let apiMethod = getExamPaperAdmin;
      if (this.routeSrc === 1) {
        apiMethod = getMarkingDetailAdmin;
      } else if (this.routeSrc === 2) {
        apiMethod = getUserPaperDetail;
      } else if (this.routeSrc === 3) {
        apiMethod = getMarkingDetail;
      }
      apiMethod(this.ueId, this.orgId, this.$route.name === 'StuExamResultPaperManager' ? 1 : 0).then(res => {
        this.setTicket(res.ticket);
        this.info = res;
        this.questionsList = res.userQues;
        this.loadQues();
        if (this.routeSrc === 2) {
          // 只允许查看错题
          if (res.allowViewResultDetailErrorQues === 1) {
            this.onlyWrong = true;
            this.onlyWrongDisabled = true;
          }
          // 允许查看答案
          if (res.allowViewResultDetailAnswer === 1) {
            this.showAnswer = true;
          }
          // 允许查看解析
          if (res.allowViewResultDetailExplain === 1) {
            this.showExplain = true;
          }
        } else {
          this.showAnswer = true;
          this.showExplain = true;
        }

        // 生成试卷题型标题
        this.titleSettings = res.quesTypeTitles;
        this.generateQuesTitle();

        if (this.exporting) {
          this.onlyWrong = (this.onlyWrong && this.onlyWrongDisabled) || this.$route.query.ow === '1';
          this.showAnswer = this.showAnswer && this.$route.query.sw === '1';
          this.showExplain = this.showExplain && this.$route.query.se === '1';
        }
        this.loading = false;

        this.$nextTick(() => {
          this.$refs.stickyTop && this.$refs.stickyTop.resetHeight();
          this.$nextTick(() => {
            this.dataRenderOver = true;
          });
        });
      }).catch(this.handleError);
    },
    scrollView(item) {
      if (this.onlyWrong && !item.faulted) {
        return;
      }
      const quesEl = this.$refs[item.id][0];
      if (quesEl) {
        const scroller = getScrollParent(quesEl, true);
        scroller.scrollTop = quesEl.offsetTop;
      }
    },
    // 加载各题型试题
    loadQues() {
      for (let i = 0; i < this.questionsList.length; i++) {
        const e = this.questionsList[i];
        e.isShowMore = false;
        e.isShowMoreText = false;
        if (!e) {
          continue;
        }
        switch (e.type) {
          case 0:
            this.dealQuestionInfo(e);
            this.singleChoiceQues.push(e);
            // 统计错误题数
            if (e.faulted === 1) {
              this.singleFaultCount++;
            }
            break;
          case 1:
            this.dealQuestionInfo(e);
            this.multiChoiceQues.push(e);
            // 统计错误题数
            if (e.faulted === 1) {
              this.multiFaultCount++;
            }
            break;
          case 2:
            this.dealQuestionInfo(e);
            this.judgeQues.push(e);
            // 统计错误题数
            if (e.faulted === 1) {
              this.judgeFaultCount++;
            }
            break;
          case 3:
            this.dealQuestionInfo(e);
            this.fillQues.push(e);
            // 统计错误题数
            if (e.faulted === 1) {
              this.fillFaultCount++;
            }
            break;
          case 4:
            this.dealQuestionInfo(e);
            this.ansQues.push(e);
            // 统计错误题数
            if (e.faulted === 1) {
              this.ansFaultCount++;
            }
            break;
          case 5:
            let fc = 0;
            // 处理子题
            e.subQuesList.forEach((ques) => {
              this.dealQuestionInfo(ques);
              if (ques.faulted === 1) {
                fc++;
              }
            });
            this.combQuesSubListsDic[e.examQuesId] = e.subQuesList;
            this.combQuesFaultDic[e.examQuesId] = fc;
            this.combQuesDic[e.examQuesId] = e;
            break;
          default:
            break;
        }
        if (e.faulted === 1) {
          this.totalFaultCount++;
        }
      }
      this.$nextTick(() => {
        this.dealStickyEvent(0);
      });
    },
    dealQuestionInfo(e) {
      switch (e.type) {
        case 0:
          e.answer = this.getSingleChoiceAnswer(e);
          break;
        case 1:
          // 获取多选题正确答案以及用户所选答案
          e.answers = this.getMultiChoiceAnswer(e);
          break;
        case 2:
          break;
        case 3:
          // 获取填空题正确答案
          this.getFillAnswer(e);
          break;
        case 4:
          // 获取问答题正确答案
          // 附件fileName和name赋值（附件查看组件用）
          if (e.attachments && e.attachments.length > 0) {
            e.attachments.forEach(function(att) {
              att.name = att.fileName;
            });
          }
          break;
        case 5:
          break;
        default:
          break;
      }
    },
    // 获取单选题用户答案
    getSingleChoiceAnswer(ques) {
      let answer = '';
      let correctAnswer = '';
      if (ques && ques.choiceItems) {
        ques.choiceItems.forEach(function(e, i) {
          if (e.selected === 1) { answer = e.id; }
          if (e.answer === 1) { correctAnswer = convertASCIIForNum(i); }
        });
      }
      ques.correctAnswer = correctAnswer;
      return answer;
    },
    // 获取多选题用户答案
    getMultiChoiceAnswer(ques) {
      const answers = [];
      let correctAnswer = '';
      if (ques && ques.choiceItems) {
        ques.choiceItems.forEach(function(e, i) {
          if (e.selected === 1) { answers.push(e.id); }
          if (e.answer === 1) { correctAnswer += convertASCIIForNum(i) + '、'; }
        });
      }
      correctAnswer = correctAnswer.substring(0, correctAnswer.length - 1);
      ques.correctAnswer = correctAnswer;
      return answers;
    },
    // 获取填空题正确答案和批阅分数
    getFillAnswer(ques) {
      let correctAnswer = '';
      if (ques && ques.fillInItems) {
        ques.fillInItems.forEach(function(e, i) {
          if (e.itemAnswer) {
            correctAnswer += mergeAnswerFun(e) + '、';
          }
        });
      }
      correctAnswer = correctAnswer.substring(0, correctAnswer.length - 1);
      ques.correctAnswer = correctAnswer;
    },
    // 生成试卷题型标题
    generateQuesTitle() {
      let quesTypeCount = 0; // 题型序号
      for (let i = 0; i < this.titleSettings.length; i++) {
        const setting = this.titleSettings[i]; // 题型设置
        let title = '';
        let isHasSubs = false; // 题型下是否有题目
        let quesList = null; // 题型下所以的小题
        let faultCount = 0;
        switch (setting.type) {
          case 0:
          case 1:
          case 2:
          case 3:
          case 4:
            if (this.quesOfType[setting.type] && this.quesOfType[setting.type].length > 0) {
              quesList = this.quesOfType[setting.type];
              isHasSubs = true;
              faultCount = this.faultOfType[setting.type];
            }
            break;
          case 5:
            // 组合题
            if (
              this.quesOfType[setting.type] &&
              this.quesOfType[setting.type][setting.composeQuesId] &&
              this.quesOfType[setting.type][setting.composeQuesId].length > 0
            ) {
              quesList = this.quesOfType[setting.type][setting.composeQuesId];
              isHasSubs = true;
              faultCount = this.faultOfType[setting.type][setting.composeQuesId];
            }
            break;
          default:
            break;
        }
        if (isHasSubs) {
          // 序号
          title = getQuesTypeIndexName(++quesTypeCount) + '、';
          // 标题
          title += setting.alias ? this.titleSettings[i].alias : this.$t(QUES_TYPE_NAMES[setting.type]);
          const titleDesc = setting.description ? this.titleSettings[i].description : this.getQuesTypeDefaultDescription(setting.type, quesList);
          this.quesTypesList.push({
            ques: setting.type === 5 ? this.combQuesDic[setting.composeQuesId] : null, // 组合题时的组合题基本信息
            quesList,
            faultCount,
            title,
            titleDesc
          });
        }
      }
    },
    // 获取试卷题型默认描述
    getQuesTypeDefaultDescription(type, quesList) {
      let title = '';
      let score = 0; // 计算总分
      quesList.forEach(function(e) {
        score += e.totalScore;
      });
      title = this.$t('pc_ote_lbl_paper_question_title', [quesList.length, parseFloat(score.toFixed(2))]);
      return title;
    },
    getAnswerCardStyle() {
      return {
        maxHeight: (this.getHeight() - this.topHeight - 24 - ((this.getWidth() < (1252 + this.getFSWidth() * 2)) && this.deepStudy ? this.topHeight : 0)) + 'px',
        minHeight: '320px',
        width: this.getWidth() > 1000 ? '282px' : '256px'
      };
    },
    // 重新批阅此答卷
    remarkPaper() {
      // 集团版，需要控制走不同批阅页面
      let isGroupData = this.info.groupDataType !== 0;
      if (window.opener && window.opener.location && window.opener.location.href && window.opener.location.href.indexOf('/#/group/') > 0) {
        isGroupData = true;
      }
      let pathName = '';
      switch (this.routeSrc) {
        case 0:
        case 1:
          // 管理端链接
          pathName = `${isGroupData ? 'group/' : ''}reviewdetail`;
          break;
        case 3:
        default:
          // 学员端链接
          pathName = 'stu/examMark';
          break;
      }

      window.refMarkList = () => { window.location.reload();};
      window.open(`${window.location.origin}/ote/#/${pathName}?remarkAll=1&arrangeId=${this.info.arrId}&ueId=${this.ueId}`);
    },
    back() {
      if (this.deepStudy) {
        this.$emit('changeStep', this.UserExamStep.result,
          this.routeParams.refparams
        );
      } else if (window.history.length > 1) {
        window.history.go(-1);
      } else {
        window.close();
      }
    }
  },
  beforeDestroy() {
    this.dealStickyEvent(2);
  }
};
</script>
