<template>
  <yxtf-dialog
    :close-on-click-modal="false"
    :visible="value"
    :title="$t('pc_ote_lbl_tosign')"
    class="yxtulcdsdk-sign-pop"
    width="520px"
    append-to-body
    @close="close()"
  >
    <div>
      <div class="yxtulcdsdk-sign-pop__desc">
        {{ $t('pc_ote_lbl_signtip') }}
      </div>
      <div class="yxtulcdsdk-sign-pop__panel">
        <sign-canvas
          v-if="value"
          ref="SignCanvas"
          v-model="signData"
          class="sign-canvas"
          :options="signOptions"
        />
      </div>
      <div class="yxtulcdsdk-sign-pop__bottom">
        <div class="yxtulcdsdk-sign-pop__icons">
          <yxt-button plain @click="$refs.SignCanvas.canvasClear()">
            <yxt-svg-icon
              icon-class="clean"
              class="v-mid mr6 mb4"
              width="14px"
              height="14px"
            />{{ $t('pc_ote_btn_cleartosign') }}
          </yxt-button>
        </div>
        <div class="yxtulcdsdk-sign-pop__btn">
          <yxt-button plain @click="close">{{ $t('pc_ote_btn_cancel') }}</yxt-button>
          <yxt-button type="primary" :loading="saving" @click="save">{{ $t('pc_ote_btn_confirm1') }}</yxt-button>
        </div>
      </div>
      <yxtbiz-upload
        v-show="false"
        ref="uploadImage"
        config-key="ImageConfigKey"
        app-code="ote"
        module-name="userexam"
        function-name="sign"
        md5
        :on-ready="onReady"
        :before-upload="beforeUpload"
        :on-progress="onProgress"
        :on-uploaded="onUploaded"
        :on-error="onError"
        :ticket="ticket"
      />
    </div>
  </yxtf-dialog>
</template>
<script type="text/ecmascript-6">
import { mapState } from 'vuex';
import SignCanvas from './SignCanvas.vue';
import YxtSvgIcon from '../components/svgIcon.vue';

export default {
  components: {
    SignCanvas,
    YxtSvgIcon
  },
  props: {
    value: {
      default: false,
      type: Boolean
    },
    saving: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      files: [],
      signData: '',
      signData90: '',
      signOptions: {
        isFullScreen: false, // 是否全屏手写 [Boolean] 可选
        isFullCover: true, // 是否全屏模式下覆盖所有的元素 [Boolean] 可选
        isDpr: false, // 是否使用dpr兼容高倍屏 [Boolean] 可选
        lastWriteSpeed: 1, // 书写速度 [Number] 可选
        lastWriteWidth: 2, // 下笔的宽度 [Number] 可选
        lineCap: 'round', // 线条的边缘类型 [butt]平直的边缘 [round]圆形线帽 [square] 正方形线帽
        lineJoin: 'bevel', // 线条交汇时边角的类型  [bevel]创建斜角 [round]创建圆角 [miter]创建尖角。
        canvasWidth: '100%', // canvas宽高 [Number] 可选
        canvasHeight: '100%', // 高度  [Number] 可选
        isShowBorder: false, // 是否显示边框 [可选]
        bgColor: '#F2F2F2', // 背景色 [String] 可选
        borderWidth: 0, // 网格线宽度  [Number] 可选
        borderColor: '#fff', // 网格颜色  [String] 可选
        writeWidth: 4, // 基础轨迹宽度  [Number] 可选
        maxWriteWidth: 4, // 写字模式最大线宽  [Number] 可选
        minWriteWidth: 2, // 写字模式最小线宽  [Number] 可选
        writeColor: '#00000', // 轨迹颜色  [String] 可选
        isSign: true, // 签名模式 [Boolean] 默认为非签名模式,有线框, 当设置为true的时候没有任何线框
        imgType: 'png' // 下载的图片格式  [String] 可选为 jpeg  canvas本是透明背景的
      }
    };
  },
  computed: {
    ...mapState({
      ticket: state => state.ticket
    })
  },
  created() {
  },
  mounted() {
  },
  methods: {
    close() {
      this.signData90 = '';
      this.signData = '';
      this.$emit('input', false);
      this.$emit('update:saving', false);
    },
    onReady() {
    },
    beforeUpload(file) {
      this.files.push({
        uuid: file.uuid,
        name: file.name,
        progress: 0
      });
    },
    onProgress(file, progress) {
    },
    onUploaded(file) {
      // this.close()
      if (!this.value) {
        return;
      }
      this.$emit('saved', file.fullUrl);
    },
    onError(type, file) {
      this.$emit('update:saving', false);
    },
    async rotateBase64Img(src, edgNum) {
      await new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        let imgW;// 图片宽度
        let imgH;// 图片高度
        let size;// canvas初始大小

        const edg = 90 * edgNum;
        const quadrant = edgNum % 4; // 旋转象限
        const cutCoor = { sx: 0, sy: 0, ex: 0, ey: 0 }; // 裁剪坐标

        const image = new Image();
        image.crossOrigin = 'anonymous';
        image.src = src;

        image.onload = () => {
          imgW = image.width;
          imgH = image.height;
          size = imgW > imgH ? imgW : imgH;

          canvas.width = size * 2;
          canvas.height = size * 2;
          switch (quadrant) {
            case 0:
              cutCoor.sx = size;
              cutCoor.sy = size;
              cutCoor.ex = size + imgW;
              cutCoor.ey = size + imgH;
              break;
            case 1:
              cutCoor.sx = size - imgH;
              cutCoor.sy = size;
              cutCoor.ex = size;
              cutCoor.ey = size + imgW;
              break;
            case -1:
              cutCoor.sx = size;
              cutCoor.sy = size - imgW;
              cutCoor.ex = size + imgH;
              cutCoor.ey = size;
              break;
            case 2:
              cutCoor.sx = size - imgW;
              cutCoor.sy = size - imgH;
              cutCoor.ex = size;
              cutCoor.ey = size;
              break;
            case 3:
              cutCoor.sx = size;
              cutCoor.sy = size - imgW;
              cutCoor.ex = size + imgH;
              cutCoor.ey = size + imgW;
              break;
          }

          ctx.translate(size, size);
          ctx.rotate(edg * Math.PI / 180);
          ctx.drawImage(image, 0, 0);

          const imgData = ctx.getImageData(cutCoor.sx, cutCoor.sy, cutCoor.ex, cutCoor.ey);
          if (quadrant % 2 === 0) {
            canvas.width = imgW;
            canvas.height = imgH;
          } else {
            canvas.width = imgH;
            canvas.height = imgW;
          }
          ctx.putImageData(imgData, 0, 0);
          this.signData90 = canvas.toDataURL();
          resolve();
        };
      });
    },
    async save() {
      try {
        if (!this.$refs.SignCanvas.signed) {
          this.$message({
            message: this.$t('pc_ote_msg_signnotempty'),
            type: 'error'
          });
          return;
        }
        this.$emit('update:saving', true);
        this.$refs.SignCanvas.saveAsImg();
        let base64Data = this.signData;
        if (this.signOptions.isFullScreen) {
          await this.rotateBase64Img(base64Data, -1);
          base64Data = this.signData90;
        }
        const base64DataArr = base64Data.split(',');
        const mime = base64DataArr[0].match(/:(.*?);/)[1];
        const bstr = atob(base64DataArr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        const file = new File([u8arr], 'sign.png', { type: mime });
        this.$refs.uploadImage.addFile(file);
        this.$refs.uploadImage.start();
      } catch (error) {
        this.$emit('update:saving', false);
      }
    }
  }
};
</script>
