<template>
  <yxtf-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="value"
    width="520px"
    append-to-body
    :cutline="false"
    padding-size="medium"
    custom-class="yxtulcdsdk-declare-dialog"
    @close="close"
  >

    <div class="color-gray-10">
      <div>
        <yxtf-tooltip :content="info.declareTitle" placement="top" open-filter>
          <span class="ulcdsdk-ellipsis-2 ulcdsdk-break yxtulcdsdk-declare-dialog__title">{{ info.declareTitle }}</span>
        </yxtf-tooltip>
      </div>

      <div class="mt24">
        <yxt-scrollbar>
          <div class="font-size-14 lh22 yxtulcdsdk-declare-dialog__content" v-html="dealLineFeed(info.declareContent, true)">
          </div>
        </yxt-scrollbar>
      </div>

      <div class="text-center mt24">
        <yxtf-button
          size="other"
          type="primary"
          :disabled="confirmDisabled"
          @click="$emit('saved')"
        >{{ confirmButtonText }}</yxtf-button>
      </div>
    </div>
  </yxtf-dialog>
</template>

<script>
import { dealLineFeed } from '../../core/utils';
export default {
  name: 'Declare',
  props: {
    value: {
      defualt: false,
      type: Boolean
    },
    info: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      interval: null,
      confirmDisabled: false,
      confirmButtonText: this.$t('pc_biz_ote_btn_determine' /* 确认 */) + '（5s）'
    };
  },

  watch: {
    value(v) {
      if (v) {
        this.$nextTick(() => {
          const pageHeight = document.documentElement.clientHeight || document.body.clientHeight;
          const pageTitleHeight = document.querySelector('.yxtulcdsdk-declare-dialog__title').clientHeight;
          document.querySelector('.yxtulcdsdk-declare-dialog__content').style.maxHeight = `${pageHeight * 0.8 - 156 - pageTitleHeight}px`;

          window.clearInterval(this.interval);

          let count = 5;
          this.confirmDisabled = true;
          this.confirmButtonText = `${this.$t('pc_biz_ote_btn_determine')}（${count}s）`;

          this.interval = setInterval(() => {
            if (count <= 1) {
              this.confirmButtonText = this.$t('pc_biz_ote_btn_determine');
              clearInterval(this.interval);
              this.confirmDisabled = false;
            } else {
              count--;
              this.confirmButtonText = `${this.$t('pc_biz_ote_btn_determine')}（${count}s）`;
            }
          }, 1000);
        });
      } else {
        window.clearInterval(this.interval);
      }
    }
  },
  methods: {
    dealLineFeed,
    close() {
      this.$emit('input', false);
    }
  },

  beforeDestroy() {
    window.clearInterval(this.interval);
  }
};
</script>
