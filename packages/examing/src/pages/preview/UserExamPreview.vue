<template>
  <div class="yxtulcdsdk-uexam-preview-container flex flex-g-1">
    <div v-show="showBody" class="yxtulcdsdk-uexam-preview show-info flex flex-dir-ver flex-g-1" :class="[deepStudy ? '' : 'mb0', $route.name === 'StuExamEntryTr' ? 'mt0' : '']">
      <div class="main-wrap">
        <div v-if="tabIndex === 0 && info.onlyPc === 0 && info.onlyMobile === 0" class="yxtulcdsdk-uexam-preview-code box-size-contentbox" @click="togTab()">
          <yxt-svg-icon
            icon-class="code"
            class-name="code color-primary-6"
            width="40px"
            height="40px"
          />
          <div class="yxtulcdsdk-uexam-preview-code__mark"></div>
        </div>
        <div v-else-if="tabIndex === 1 && info.onlyPc === 0 && info.onlyMobile === 0" class="yxtulcdsdk-uexam-preview-code box-size-contentbox" @click="togTab()">
          <yxt-svg-icon
            icon-class="computer"
            class-name="computer color-primary-6"
            width="40px"
            height="40px"
          />
          <div class="yxtulcdsdk-uexam-preview-code__mark"></div>
        </div>
        <div class="yxtulcdsdk-uexam-preview-content">
          <div class="main-top-wrap">
            <div class="yxtulcdsdk-uexam-preview-content__title">
              <span class="lh26 yxt-weight-5">{{ info.arrName }}</span>
            </div>
            <!-- 入场时间 -->
            <div v-if="info.entryTimeCtrl === 1" class="yxtulcdsdk-uexam-preview-content__time">
              {{ $t('pc_ote_lbl_entryperiod') }}：<span class="ltr-text">{{ dateFormatUtil(info.entryStartTime,'yyyy-MM-dd HH:mm') }}</span> {{ $t('pc_ote_lbl_to') }} <span class="ltr-text">{{ dateFormatUtil(info.entryEndTime,'yyyy-MM-dd HH:mm') || '--' }}</span>
            </div>
            <div v-else class="yxtulcdsdk-uexam-preview-content__time">{{ $t('pc_ote_lbl_entryperiod') }}：{{ $t('pc_ote_lbl_timenotlimit') }}</div>
            <!-- 基本信息 -->
            <div v-if="tabIndex === 0" class="yxtulcdsdk-uexam-preview-content__base">
              <div class="yxtulcdsdk-uexam-preview-content__base-row-flex">
                <div class="info-left" :style="deepStudy && getInfoLeftStyle('left')">
                  <div>
                    <!-- 试卷总分 -->
                    <yxt-svg-icon
                      icon-class="pre-total-score"
                      class-name="total-score"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">{{ $t('pc_ote_lbl_exampapertotalscore') }}：<span class="yxt-weight-5">{{ $t('pc_ote_lbl_many_score',[info.totalScore]/*{0}分*/) }}</span></div>
                  </div>
                  <div>
                    <!-- 试题总数 -->
                    <yxt-svg-icon
                      icon-class="pre-total"
                      class-name="total"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">{{ $t('pc_ote_lbl_questotal') }}：<span class="yxt-weight-5">{{ $t('pc_ote_lbl_many_question',[info.totalQuesQty]/*{0}题*/) }}</span></div>
                  </div>
                  <div>
                    <!-- 考试限时 -->
                    <yxt-svg-icon
                      icon-class="pre-exam-time"
                      class-name="exam-time"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">
                      {{ $t('pc_ote_lbl_examtimelimit') }}：<span v-if="info.usedTimesCtrl === 1" class="yxt-weight-5">{{ info.usedTimesLen / 60 }}{{ $t('pc_ote_lbl_minutes') }}</span>
                      <span v-else class="yxt-weight-5">{{ $t('pc_ote_lbl_unlimited') }}</span>
                    </div>
                  </div>
                  <div>
                    <!-- 交卷时间 -->
                    <yxt-svg-icon
                      icon-class="pre-submit-time"
                      class-name="submit-time"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">
                      {{ $t('pc_ote_lbl_examsubmittime') }}：<span v-if="info.forceSubmitCtrl === 1" class="yxt-weight-5">{{ shortDateTime(info.forceSubmitTime) }}</span>
                      <span v-else class="yxt-weight-5">--</span>
                    </div>
                  </div>
                </div>
                <div class="info-line"><span></span></div>
                <div class="info-right over-hidden" :style="deepStudy && getInfoLeftStyle('right')">
                  <div>
                    <!-- 通过分数 -->
                    <yxt-svg-icon
                      icon-class="pre-pass-score"
                      class-name="pass-score"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">{{ $t('pc_ote_lbl_passscore') }}：<span class="yxt-weight-5">{{ info.allowViewResultPassed === 1 ? $t('pc_ote_lbl_many_score', [info.passScore]/*{0}分*/) : $t('pc_ote_lbl_many_score', ['*']/*{0}分*/) }}</span></div>
                  </div>
                  <div>
                    <!-- 优秀分数 -->
                    <yxt-svg-icon
                      icon-class="pre-excellence-score"
                      class-name="excellence-score"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">
                      {{ $t('pc_ote_lbl_excellentscore') }}：<span v-if="info.resited === 0 && info.excellenceScore > 0" class="yxt-weight-5">{{ $t('pc_ote_lbl_many_score',[info.excellenceScore]/*{0}分*/) }}</span>
                      <span v-else class="yxt-weight-5">--</span>
                    </div>
                  </div>
                  <div>
                    <!-- 考试次数 -->
                    <yxt-svg-icon
                      icon-class="pre-submit-times"
                      class-name="submit-times"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text">
                      {{ $t('pc_ote_lbl_examtimes') }}：<span v-if="info.repeated === 0" class="yxt-weight-5">{{ $t('pc_ote_lbl_many_time', [`${info.usedExamTimes}/1`]/*{0}次*/) }}</span>
                      <span v-else-if="info.repeated === 1 && info.repeatTimes === 0" class="yxt-weight-5">{{ info.usedExamTimes }}/{{ $t('pc_ote_lbl_unlimited') }}</span>
                      <span v-else class="yxt-weight-5">{{ $t('pc_ote_lbl_many_time', [`${info.usedExamTimes}/${info.repeatTimes}`]/*{0}次*/) }}</span>
                    </div>
                  </div>
                  <div>
                    <!-- 考生姓名 -->
                    <yxt-svg-icon
                      icon-class="pre-examinee-name"
                      class-name="examinee-name"
                      width="20px"
                      height="20px"
                    />
                    <div class="info-text flex-align-center white-space over-hidden">
                      {{ $t('pc_ote_lbl_examineename') }}：
                      <yxt-tooltip v-if="info.fullName && info.fullName.length > 4" placement="top">
                        <div slot="content"><yxtbiz-user-name :name="info.fullName" /></div>
                        <span class="hand yxt-weight-5 ellipsis maxmp60 d-in-block">
                          <yxtbiz-user-name :name="info.fullName" />
                        </span>
                      </yxt-tooltip>
                      <span v-else class="yxt-weight-5">
                        <yxtbiz-user-name :name="info.fullName" />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 二维码模式 -->
            <div v-if="tabIndex === 1" class="yxtulcdsdk-uexam-preview-content__code">
              <p v-if="url">
                <yxtbiz-qrcode
                  :url="url"
                  :hide-link="true"
                  :hide-download="true"
                  :size="256"
                />
              </p>
              <!-- 请在手机端扫码参加考试 -->
              <div class="yxtulcdsdk-uexam-preview-content__code--text">{{ $t('pc_ote_msg_scancodetomobile') }}</div>
            </div>
          </div>
          <!--考试结果-->
          <div v-if="isShowExamResultBox" class="yxtulcdsdk-uexam-preview-content__result mt24">
            <div class="result-title">
              <span class="yxt-weight-5 ml0">{{ $t('pc_ote_lbl_examresult') }}</span>
            </div>
            <!--通过状态 + 分数-->
            <div
              v-if="isShowExamResultPassed"
              :style="{background: getResultStyle().boxBg}"
              class="result-box mt12 flex flex-center"
            >
              <div class="result-left flex-g-1 ml16 flex flex-dir-ver layout-align-start">
                <!--批阅中 || 是否通过 显示文字-->
                <div v-if="getPassedText" class="flex align-center">
                  <yxt-svg-icon
                    is-new-svg-url
                    :icon-class="getResultStyle().passedIcon"
                    width="24px"
                    height="24px"
                  />
                  <span class="color-gray-10 yxt-weight-5 passed ml8">{{ getPassedText }}</span>
                </div>
                <!--历史最高分: 允许查分 && 已完成考试-->
                <div
                  v-if="info.viewResultScore === 1 && info.userStatus === ExamUserStatusEnum.finished"
                  class="flex flex-start-center"
                  :class="getPassedText ? 'ml32 mt4' : ''"
                >
                  <div class="history" :class="getPassedText ? 'color-gray-8' : 'color-gray-10'">{{ $t('pc_ote_lbl_historyhighestscore' /* 历史最高分 */) }}</div>
                  <div class="history ml8" :class="getPassedText ? 'color-gray-8' : 'color-gray-10'">{{ info.userScore }}</div>
                </div>
              </div>
              <yxtf-button
                size="middle"
                plain
                class="mr24"
                @click="viewExamResult"
              >{{ $t('pc_ote_btn_viewdetail'/* 查看详情 */) }}</yxtf-button>
            </div>
            <div v-else class="mt12">
              <yxtf-button
                size="middle"
                plain
                class="mr24"
                @click="viewExamResult"
              >{{ $t('pc_ote_btn_viewdetail'/* 查看详情 */) }}</yxtf-button>
            </div>
          </div>
          <div class="yxtulcdsdk-uexam-preview-content__explain">
            <!-- 考试说明 -->
            <div v-if="info.description" class="exam-rule">
              <div class="rule-title">
                <!-- <yxt-svg-icon icon-class="pre-exam-notes"
                              class-name="exam-notes"
                              width="18px"
                              height="18px"></yxt-svg-icon> -->
                <span class="yxt-weight-5 ml0">{{ $t('pc_ote_lbl_examexplain') }}</span>
              </div>
              <div class="rule-info">
                <span v-html="dealLineFeed(info.description, true)"></span>
              </div>
            </div>
            <!-- 答卷须知 -->
            <div class="exam-rule">
              <div class="rule-title">
                <!-- <yxt-svg-icon icon-class="pre-answer-notice"
                              class-name="answer-notice"
                              width="18px"
                              height="18px"></yxt-svg-icon> -->
                <span class="yxt-weight-5 ml0">{{ $t('pc_ote_tit_examnotes') }}</span>
              </div>
              <div class="rule-info">
                <span class="d-in-block lh22">
                  <!-- 可以使用试题后的“{0}”标记按钮，标记不确定的试题，标记后能快速定位。如果考试限定时间，请在倒计时结束前完成答卷，倒计时结束时会自动交卷。如果你已经准备好了，请点击“开始考试”按钮即开始考试，开始计时。 -->
                  <LanguageSlot lang-key="pc_ote_msg_examnotes">
                    <yxt-svg-icon
                      slot="0"
                      icon-class="flag-o"
                      class-name="color-gray-7"
                      width="16px"
                      height="16px"
                    />
                  </LanguageSlot>
                </span>
              </div>
            </div>
            <div v-if="info.customFieldProject && info.customFieldProject.length && info.customFieldProject.filter(d => d.visible && d.detail).length" class="exam-rule">
              <div class="rule-title">
                <span class="yxt-weight-5 ml0">{{ $t('pc_ote_lbl_otherinfo' /* 其他信息 */) }}</span>
              </div>

              <div class="rule-info">
                <div v-for="item in info.customFieldProject.filter(d => d.visible && d.detail)" :key="item.id">
                  <!-- 单行文本 -->
                  <div v-if="item.fieldType === 0" class="mt8">
                    <yxt-tooltip
                      :content="item.detail"
                      placement="top"
                      open-filter
                    >
                      <div class="ellipsis">
                        {{ item.fieldName }}：{{ item.detail || '--' }}
                      </div>
                    </yxt-tooltip>
                  </div>
                  <!-- 多行文本 -->
                  <VueClamp v-else-if="item.fieldType === 1" :max-lines="3" class="mt8">
                    {{ item.fieldName }}：{{ item.detail || '--' }}
                    <template #after="{ clamped, toggle }">
                      <div v-if="clamped || expanded" class="color-primary-6 d-in-block" @click="toggle">
                        {{ clamped ? $t('pc_ote_btn_expand'/** 展开 */) : $t('pc_ote_btn_putaway'/** 收起 */) }}
                      </div>
                    </template>
                  </VueClamp>
                  <!-- 选项 -->
                  <div v-else-if="item.fieldType === 2" class="mt8">
                    {{ item.fieldName }}：{{ getOptionDetail(item) }}
                  </div>
                  <!-- 时间 -->
                  <div v-else-if="item.fieldType === 3" class="mt8">
                    {{ item.fieldName }}：{{ dateFormatUtil(item.detail,'yyyy-MM-dd HH:mm') }}
                  </div>
                  <!-- 时间范围 -->
                  <div v-else-if="item.fieldType === 4" class="mt8">
                    {{ item.fieldName }}：{{ formatTimeRange(item) }}
                  </div>
                  <!-- 数值 -->
                  <div v-else-if="item.fieldType === 6" class="mt8">
                    {{ item.fieldName }}：{{ item.detail || '--' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <common-sticky
        ref="commonStickyRef"
        :by-class="deepStudy"
      >
        <div v-if="showTimeCounter" class="yxtulcdsdk-uexam-preview-bottom">
          <div class="date-time">
            <!-- 天时分秒 后开始 -->
            <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.dd }}</span></i><span class="unit">{{ $t('pc_ote_lbl_day') }}</span></span>
            <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.hh }}</span></i><span class="unit">{{ $t('pc_ote_lbl_hour') }}</span></span>
            <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.mm }}</span></i><span class="unit">{{ $t('pc_ote_lbl_minute') }}</span></span>
            <span class="date"><i class="border-primary-6 color-primary-6"><span class="yxt-weight-5">{{ countDownInfo.ss }}</span></i><span class="unit">{{ $t('pc_ote_lbl_second') }}&nbsp;&nbsp;{{ $t('pc_ote_lbl_thenstart') }}</span></span>
          </div>
        </div>
        <div v-else class="yxtulcdsdk-uexam-preview-bottom">
          <yxtf-button
            v-if="btnDisabled && btnDisabledTips"
            :type="showWrongQuesBtn ? '' : 'primary'"
            size="larger"
            class="mt12 mb24"
            :plain="showWrongQuesBtn"
            @click="showBtnDisabledTips"
          >
            {{ $t( info.lastStatus === 1 ? 'pc_ote_btn_continueexam' : 'pc_ote_btn_examagain') }}
          </yxtf-button>
          <yxtf-button
            v-else-if="btnDisabled"
            :type="showWrongQuesBtn ? '' : 'primary'"
            size="larger"
            class="mt12 mb24"
            :plain="showWrongQuesBtn"
            :disabled="true"
          >
            {{ btnText }}
          </yxtf-button>
          <yxtf-button
            v-else
            :type="showWrongQuesBtn ? '' : 'primary'"
            size="larger"
            class="mt12 mb24"
            :plain="showWrongQuesBtn"
            @click="takeExam()"
          >
            {{ btnText }}
          </yxtf-button>

          <!-- 重做错题 -->
          <yxtf-button
            v-if="showWrongQuesBtn"
            v-checkButtons="oteWrongQues.value"
            :disabled="oteWrongQues.disable"
            type="primary"
            size="larger"
            class="mt12 mb24"
            @click="linkToWrongs('exam')"
          >
            {{ $t('pc_ote_lbl_redoWrongQuestion' /* 重做错题 */) }}
          </yxtf-button>
        </div>
      </common-sticky>
      <!-- 防作弊提示框 -->
      <yxtf-dialog
        :visible.sync="antiCheatDialog"
        width="400px"
        :cutline="false"
        :show-close="false"
        append-to-body
      >
        <div class="flex flex-start color-gray-10 yxt-weight-5">
          <yxt-svg-icon icon-class="icon-warning" width="20px" height="20px" />
          <span class="ml16">
            <!-- 本场考试已经开启防作弊：   -->
            {{ $t( 'pc_ote_tip_anticheat') }}
          </span>
        </div>
        <div v-if="info.exitTimes > 0" class="ml36 mt12 color-gray-8">
          <span v-if="info.exitTimes > 0 && info.maxLeaveTime > 0">1. </span>
          <!--切换屏幕 {{info.exitTimes}} 次将自动交卷，当前剩余次数 {{info.exitTimes - info.userExitCount}} 次-->
          <LanguageSlot
            inline
            :lang-key="'pc_ote_tip_anticheatmsg1'"
          >
            <span slot="0" class="yxtf-color-danger"> {{ info.exitTimes }} </span>
            <span slot="1" class="yxtf-color-danger"> {{ Math.max(info.exitTimes - info.userExitCount, 0) }} </span>
          </LanguageSlot>
        </div>
        <div v-if="info.maxLeaveTime > 0" class="ml36 color-gray-8" :class="info.exitTimes > 0 ? 'mt8' : 'mt12'">
          <span v-if="info.exitTimes > 0 && info.maxLeaveTime > 0">2. </span>
          <!-- 考试答题过程中超过 {{info.maxLeaveTime}} 分钟没有滑动或点击屏幕，将自动交卷！ -->
          <LanguageSlot
            inline
            :lang-key="'pc_ote_tip_anticheatmsg2'"
          >
            <span slot="0" class="yxtf-color-danger"> {{ info.maxLeaveTime }} </span>
          </LanguageSlot>
        </div>
        <!-- 请勿点击考试页面最小化、退出按钮，切换应用软件、点击考试区域以外的任意操作 -->
        <div class="color-gray-8 standard-size-12 bg-gray-2 border-radius-4 pv8 ph8 mt8 ml36">
          {{ $t('pc_ote_tip_anticheatmsg3') }}
        </div>
        <span slot="footer" class="dialog-footer">
          <!-- 我知道了 -->
          <yxtf-button type="primary" @click="closeAntiCheatTip()">{{ $t('pc_ote_btn_iknow') }}</yxtf-button>
        </span>
      </yxtf-dialog>
      <!-- 浏览器检查提示框 -->
      <yxtf-dialog
        :visible.sync="browserDialog"
        width="400px"
        :cutline="false"
        :show-close="false"
        append-to-body
      >
        <p>
          <yxt-svg-icon icon-class="icon-warning" width="17px" height="17px" />
          <!-- 本场考试已经开启防作弊： -->
          <span class="font-size-16 yxt-weight-5 ml16 lh22">{{ $t('pc_ote_tip_anticheat') }}</span>
        </p>
        <!-- <p>您当前的浏览器版本不支持防作弊考试，请切换到谷歌，火狐，搜狗，Edge，Safari，QQ浏览器的最新版本。</p> -->
        <p>{{ $t('pc_ote_tip_anticheatmsg4') }}</p>
        <span slot="footer" class="dialog-footer">
          <yxtf-button type="primary" @click="browserDialog = false">{{ $t('pc_ote_btn_iknow') }}</yxtf-button>
        </span>
      </yxtf-dialog>
    </div>
    <exam-sign v-model="isShowSign" :saving.sync="signing" @saved="saveSignImg" />
    <!-- 考试考前说明 -->
    <declare
      v-model="isShowDeclare"
      :info="declareInfo"
      @saved="confirmDeclare"
    />
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import { generateShortUrl } from '../../core/bizUtil';
import { shortDateTime, dealLineFeed, dateFormatUtil } from '../../core/utils';
import { LanguageSlot } from 'yxt-biz-pc';
import { saveSign, getDeclareInfo } from '../../service/user.service';
import CommonSticky from '../components/CommonSticky.vue';
import ExamSign from './ExamSign.vue';
import touristTest from '../../mixins/touristTest';
import deepStudyPage from '../../mixins/deepStudyPage';
import YxtSvgIcon from '../components/svgIcon.vue';
import { ExamUserStatusEnum } from '../../core/enums';
import wrong from '../mixins/wrong';
import Declare from './Declare.vue';

export default {
  name: 'UserExamPreview',
  components: {
    CommonSticky,
    ExamSign,
    LanguageSlot,
    YxtSvgIcon,
    Declare
  },
  mixins: [touristTest, deepStudyPage, wrong],
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      }
    },
    arrangeId: {
      type: String,
      default: ''
    },
    uemId: {
      type: String,
      default: ''
    },
    masterId: {
      type: String,
      default: ''
    },
    masterType: {
      type: String,
      default: ''
    },
    packageId: {
      type: String,
      default: ''
    },
    batchId: {
      type: String,
      default: ''
    },
    btid: {
      type: String,
      default: ''
    },
    gwnlUrl: {
      type: String,
      default: ''
    },
    trackId: {
      type: String,
      default: ''
    },
    redo: {
      type: Number,
      default: 0
    }
  },
  inject: ['getWidth'],
  data() {
    return {
      isShowSign: false,
      isSigned: false,
      signing: false,
      signId: '', // 签名后的唯一ID
      antiCheatDialog: false,
      browserDialog: false,
      url: '',
      params: {
        uemId: this.uemId, // 用户考试关联ID
        masterId: this.masterId, // 第三方考试来源ID
        masterType: this.masterType, // 第三方考试来源类型
        packageId: this.packageId, // 课程包ID
        batchId: this.batchId, // 循环考试批次ID
        thirdBatchId: this.btid, // 在线课堂批次ID
        gwnlUrl: this.gwnlUrl
      },
      tabIndex: 0,
      btnText: '',
      btnDisabled: true,
      btnDisabledTips: '',
      showTimeCounter: false,
      showBody: false, // 展示
      currentSystemTime: '',
      countDownInfo: {
        dd: '0',
        hh: '0',
        mm: '0',
        ss: '0'
      },
      timer: '',
      times: 0,
      nowrapHeight: 110,
      isShowMore: false,
      isShowMoreText: Boolean,
      ExamUserStatusEnum,
      isShowDeclare: false, // 是否显示考试考前说明
      declareInfo: {}
    };
  },
  computed: {
    // 是否显示考试结果内容
    isShowExamResultBox() {
      //  2：已提交；3：批阅中；4：已完成；显示考试结果列表
      return [this.ExamUserStatusEnum.submitted, this.ExamUserStatusEnum.marking, this.ExamUserStatusEnum.finished].includes(this.info.userStatus);
    },
    // 是否显示考试结果通过状态
    isShowExamResultPassed() {
      // 允许查通过 || 允许查分数
      return this.info.allowViewResultPassed === 1 || this.info.viewResultScore === 1;
    },
    // 获取 通过、未通过、批阅中文字
    getPassedText() {
      if (this.info.userStatus === this.ExamUserStatusEnum.finished) {
        if (this.info.allowViewResultPassed === 1) {
          return this.info.userPassed === 1 ? this.$t('pc_ote_lbl_pass') : this.$t('pc_ote_lbl_unpass');
        } else {
          return '';
        }
      }
      return this.$t('pc_ote_lbl_marking');
    },

    currentParams() {
      return {
        arrangeId: this.arrangeId,
        trackId: this.trackId,
        batchId: this.batchId, // 循环考试批次ID
        btid: this.btid, // 在线课堂批次ID
        ...this.getOtherId(),
        redo: this.redo
      };
    },

    currentStep() {
      return { type: 'exam', typeValue: this.UserExamStep.preview };
    }
  },
  created() {
    this.getDetail();
  },
  methods: {
    ...mapActions(['setTicket']),
    // 第三方的其他ID
    getOtherId() {
      const {uemId, masterId, masterType, packageId, gwnlUrl} = this.params;
      return {
        uemId: uemId ? uemId : undefined, // 用户考试关联ID
        masterId: masterId ? masterId : undefined, // 第三方考试来源ID
        masterType: masterType ? masterType : undefined, // 第三方考试来源类型
        packageId: packageId ? packageId : undefined, // 课程包ID
        gwnlUrl: gwnlUrl ? gwnlUrl : undefined // 人才发展url
      };
    },
    showBtnDisabledTips() {
      this.$message({
        type: 'warning',
        message: this.btnDisabledTips
      });
    },
    saveSignImg(url) {
      this.signing = true;
      saveSign(this.params.uemId, url).then((res) => {
        // 签名后的唯一ID，提供给考试页面使用
        this.signId = res;
        this.isSigned = true;
        this.takeExam();
      }).catch(this.handleError).finally(() => {
        this.signing = false;
      });
    },
    dealLineFeed,
    // 获取防作弊数字样式
    getTextHtml(textNum) {
      return '<span class="color-primary-6 yxt-weight-5">' + textNum + '</span>';
    },
    async getDetail() {
      if (this.info) {
        this.setTicket(this.info.ticket);
        await this.initRedoWrong(this.info.arrId);
        let btnText = '';
        let btnDisabled = true;
        // 兼容游客考试  added by wuym 2020-04-23
        if (this.info.canAnswered === 0) {
          // 游客授权失败，跳转至信息收集页面
          this.checkTestSetting(this.info);
          if (this.info.errorKey === 'apis.ote.examinee.NotChangeDevice') {
            // 本场考试过程中不能更换设备，您初次参与考试的设备是xx，请在原设备上继续考试
            btnText = this.$t('pc_ote_msg_cannotchangedevice', [this.getDeviceType()]);
          } else if (this.info.errorKey === 'apis.ote.examinee.NotFound') {
            // 无权参加考试
            btnText = this.$t('pc_ote_msg_nopermissionexam');
          } else if (this.info.errorKey === 'apis.ote.userexam.passed.forbid.attend.marking') {
            // 考试开启了通过后不允许再考，等批阅完成后再操作
            btnText = this.$t('pc_ote_msg_examended');
            this.btnDisabledTips = this.$t(this.info.errorValue);
          } else if (this.info.errorKey === 'apis.ote.userexam.passed.forbid.attend.passed') {
            // 考试已通过，无需再考
            btnText = this.$t('pc_ote_msg_examnotstart');
            this.btnDisabledTips = this.$t(this.info.errorValue);
          } else {
            // 其他错误数据
            btnText = this.$t(this.info.errorValue);
          }
        } else {
          if (this.info.lastStatus === 'null' || this.info.lastStatus === '' || this.info.lastStatus === 0) {
            btnDisabled = false;
            btnText = this.$t('pc_ote_btn_startexam'); // 开始考试
          } else if (this.info.lastStatus === 1) {
            btnDisabled = false;
            btnText = this.$t('pc_ote_btn_continueexam'); // 继续考试
          } else {
            // 单次考或者多次考次数已用完
            if (this.info.repeated === 1) {
              btnDisabled = false;
              btnText = this.$t('pc_ote_btn_examagain'); // 再次考试
              if (this.info.usedExamTimes === this.info.repeatTimes) {
                if (this.info.lastStatus === 2) {
                  btnText = this.$t('pc_ote_lbl_submitted'); // 已提交
                } else if (this.info.lastStatus === 3) {
                  btnText = this.$t('pc_ote_lbl_marking'); // 批阅中
                } else if (this.info.lastStatus === 4) {
                  btnText = this.$t('pc_ote_lbl_done'); // 已完成
                }
              }
            } else {
              if (this.info.usedExamTimes === this.info.repeatTimes) {
                if (this.info.lastStatus === 2) {
                  btnText = this.$t('pc_ote_lbl_submitted'); // 已提交
                } else if (this.info.lastStatus === 3) {
                  btnText = this.$t('pc_ote_lbl_marking'); // 批阅中
                } else if (this.info.lastStatus === 4) {
                  btnText = this.$t('pc_ote_lbl_done'); // 已完成
                }
              }
            }
          }
          // 当前IP是否可以考试 0不禁止 1禁止
          if (this.info.rejectIpExam) {
            btnDisabled = true;
            btnText = this.$t('pc_ote_btn_appoint_ip_exam'/* 请到指定IP下参加考试 */);
          }
        }
        this.btnText = btnText;
        this.btnDisabled = btnDisabled;
        this.currentSystemTime = this.info.currentSystemTimes;
        if (this.info.uemId) {
          this.params.uemId = this.info.uemId;
        }
        if (this.info.onlyMobile === 1) {
          this.tabIndex = 1;
        }
        if (this.info.onlyPc !== 1) {
          this.getUrl();
        }
        if (this.info.canAnswered === 0 && this.info.errorKey === 'apis.ote.userexam.validation.TimeLimited') {
          // 判断是否未开始
          const now = Date.parse(this.info.currentSystemTime.replace(/-/g, '/'));
          const startDate = Date.parse(this.info.entryStartTime.replace(/-/g, '/'));
          if (startDate > now) {
            this.showTimeCounter = true;
            this.generateTimeCountDown(this.info.currentSystemTime, this.info.entryStartTime);
          }
        }

        // 是否显示考试考前说明
        if (this.info.declared) {
          getDeclareInfo(this.info.arrId, localStorage.orgId || '').then((res) => {
            this.declareInfo = res || {};
          }).catch((error) => {
            this.declareInfo = {};
            this.handleError(error);
          });
        }

        this.showBody = true;
        // 计算考试说明高度，判断要不要出现展开/收起的效果
        this.$nextTick(() => {
          if (this.info.description) {
            this.nowrapEleHeight();
          }
          this.$refs.commonStickyRef && this.$refs.commonStickyRef.resetFun();
        });
      }
    },
    dateFormatUtil,
    formatTimeRange(item) {
      if (!item.detail) return '--';
      try {
        const data = JSON.parse(item.detail);
        return dateFormatUtil(data[0], 'yyyy-MM-dd HH:mm') + ' ' + this.$t('pc_ote_lbl_to' /* 至 */) + ' ' + dateFormatUtil(data[1], 'yyyy-MM-dd HH:mm');
      } catch (error) {
        return '--';
      }
    },
    getOptionDetail(item) {
      if (!item.detail || !item.fieldOptions || item.fieldOptions.length === 0) return '--';
      const data = item.fieldOptions.find(d => d.lanKey === item.detail);
      return data ? data.content : '--';
    },
    async getUrl() {
      let h5Url = 'stu/exampreview?';
      let query = {
        arrangeId: this.arrangeId,
        batchId: this.batchId,
        btid: this.btid,
        gwnlUrl: this.gwnlUrl, // 人才发展url进行面包屑点击回退
        trackId: this.trackId
      };
      let session;
      if (sessionStorage[this.oteExamArrangeId]) {
        session = JSON.parse(sessionStorage[this.oteExamArrangeId]);
      }
      if (session) {
        query = {
          ...query,
          targetId: session.targetId,
          taskId: session.taskId,
          targetCode: session.targetCode
        };
      }
      const params = this.params;
      // 第三方考试来源ID
      if (params.masterId) {
        query.masterId = params.masterId;
      }
      // 第三方考试来源类型
      if (params.masterType) {
        query.masterType = params.masterType;
      }
      // 课程包ID
      if (params.packageId) {
        query.packageId = params.packageId;
      }

      for (const key in query) {
        if (query[key]) h5Url += `${key}=${query[key]}&`;
      }
      // 去掉最后一个 &
      h5Url = h5Url.slice(0, h5Url.length - 1);
      generateShortUrl(h5Url, '', 0, this.info.groupDataType === 1, this.info.projectOrgId).then(res => {
        if (res && res.url) {
          console.log('二维码url', res.url);
          this.url = res.url;
        }
      }).catch(this.handleError);
    },
    // 切换右上角
    togTab() {
      this.tabIndex = this.tabIndex === 0 ? 1 : 0;
    },
    // 开始考试/继续考试
    takeExam() {
      if (Object.keys(this.declareInfo).length) {
        this.isShowDeclare = true;
      } else if ((this.info.exitTimes > 0 || this.info.maxLeaveTime > 0) && !this.browserChecked) {
        // 检查浏览器
        this.checkBrowser();
      } else if (this.info.needSignInd && !this.isSigned) {
        // 考前签名
        this.isShowSign = true;
      } else {
        // 没有开启防作弊，直接跳转至用户答题页面
        this.linkToExam();
        this.reportYxtLog(110, '用户进入考试');
      }
    },
    // 跳转至考试答题页面
    linkToExam() {
      const queryUrl = {
        trackId: this.trackId,
        batchId: this.batchId, // 循环考试批次ID
        btid: this.btid, // 在线课堂批次ID
        arrId: this.arrangeId,
        signId: this.signId
      };
      const params = this.params;
      // 用户考试关联ID
      if (params.uemId) {
        queryUrl.uemId = params.uemId;
      }
      // 第三方考试来源ID
      if (params.masterId) {
        queryUrl.masterId = params.masterId;
      }
      // 第三方考试来源类型
      if (params.masterType) {
        queryUrl.masterType = params.masterType;
      }
      // 课程包ID
      if (params.packageId) {
        queryUrl.packageId = params.packageId;
      }
      // 人才发展url
      if (params.gwnlUrl) {
        queryUrl.gwnlUrl = params.gwnlUrl;
      }

      this.$emit('changeStep', this.UserExamStep.examing, queryUrl);
      // this.$router.push({
      //   name: 'StuUserExam',
      //   query: queryUrl
      // })
    },
    // 检查浏览器版本
    checkBrowser() {
      const browserInfo = new window.Browser();
      const versionCurrent = parseInt(browserInfo.version);
      const passBrowser = {
        Chromium: 0,
        Chrome: 0,
        Firefox: 0,
        QQBrowser: 0,
        Sogou: 0,
        Safari: 0,
        Edge: 0,
        Opera: 0
      };
      if (!(passBrowser[browserInfo.browser] === 0 || passBrowser[browserInfo.browser] <= versionCurrent)) {
        // 检查不通过，弹出浏览器提示
        this.showBrowserTip();
      } else {
        // 检查通过，弹出防作弊提示
        this.showAntiCheatTip();
      }
    },
    // 防作弊提示框
    showAntiCheatTip() {
      this.antiCheatDialog = true;
    },
    // 关闭防作弊提示框，进入考试
    closeAntiCheatTip() {
      this.antiCheatDialog = false;
      this.browserChecked = true;
      this.takeExam();
    },
    // 浏览器版本不支持提示
    showBrowserTip() {
      this.browserDialog = true;
    },
    // 获取上次考试的设备端
    getDeviceType() {
      let deviceName = '';
      if (this.info.lastSource && this.info.lastSource !== '501') {
        switch (this.info.lastSource) {
          case '502':
            deviceName = this.$t('pc_ote_lbl_appios'); // APP端IOS
            break;
          case '503':
            deviceName = this.$t('pc_ote_lbl_appandriod'); // APP端Andriod
            break;
          case '504': // 微信
          case '505': // 企业微信
          case '506': // H5
          case '507': // 钉钉
            deviceName = this.$t('pc_ote_lbl_deviceh5'); // H5端
            break;
          default:
            break;
        }
      }
      return deviceName;
    },
    /**
     * 倒计时生成器
     * type 1-开始倒计时，2-结束倒计时
     */
    generateTimeCountDown(currentTime, startTime) {
      const controlCountDown = () => {
        currentTime = Date.parse(currentTime.replace(/-/g, '/'));
        startTime = Date.parse(startTime.replace(/-/g, '/'));
        this.times = startTime - currentTime;
        const vm = this;
        vm.handleTimeStr();
        this.timer = setInterval(() => {
          vm.handleTimeStr();
        }, 1000);
      };
      controlCountDown();
    },
    // 处理倒计时文本
    handleTimeStr() {
      if (this.times > 0) {
        this.countDownInfo.dd = Math.floor(this.times / 1000 / 60 / 60 / 24);
        this.countDownInfo.hh = Math.floor((this.times / 1000 / 60 / 60) % 24);
        this.countDownInfo.mm = Math.floor((this.times / 1000 / 60) % 60);
        this.countDownInfo.ss = Math.floor((this.times / 1000) % 60);
        this.times = this.times - 1000;
        this.$forceUpdate();
      } else {
        window.clearInterval(this.timer);
        this.showTimeCounter = false; // 展示开始考试按钮
        this.btnDisabled = false; // 取消按钮禁用
        this.btnText = this.$t('pc_ote_btn_startexam'); // 按钮文本改为开始考试
      }
    },
    nowrapEleHeight() {
      const nowrapEleHeight = this.$refs.nowrapEle && this.$refs.nowrapEle.offsetHeight;
      nowrapEleHeight > this.nowrapHeight ? this.isShowMoreText = true : this.isShowMoreText = false;
    },
    shortDateTime,
    // 获取考试结果box背景颜色
    getResultStyle() {
      let boxBg = '#f6f6f6'; // 结果框背景色
      let passedColor = ''; // 通过不通过文字颜色
      let passedIcon = ''; // 通过不通过icon
      if (this.info.userStatus === this.ExamUserStatusEnum.submitted || this.info.userStatus === this.ExamUserStatusEnum.marking) { // 试卷批阅中
        boxBg = '#fff8f6';
        passedColor = '#262626';
        passedIcon = 'ote_marking';
      } else if (this.info.allowViewResultPassed === 1) { // 允许查看通过不通过
        if (this.info.userPassed === 1) { // 通过
          boxBg = '#f5faf6';
          passedColor = '#262626';
          passedIcon = 'ote_pass';
        } else {
          boxBg = '#fff8f6';
          passedColor = '#262626';
          passedIcon = 'ote_unpass';
        }
      }
      return {
        boxBg: boxBg,
        passedColor: passedColor,
        passedIcon: passedIcon
      };
    },
    // 查看考试结果
    viewExamResult() {
      let session;
      if (sessionStorage[this.oteExamArrangeId]) {
        session = JSON.parse(sessionStorage[this.oteExamArrangeId]);
      }
      if (session) {
        this.$emit('changeStep', this.UserExamStep.result, {
          trackId: this.trackId,
          arrangeId: this.arrangeId,
          targetId: session.targetId,
          taskId: session.taskId,
          batchId: this.batchId, // 循环考试批次ID
          btid: this.btid, // 在线课堂批次ID
          targetCode: session.targetCode,
          gwnlUrl: this.gwnlUrl, // 人才发展url进行面包屑点击回退
          ueId: (this.info.allowViewResultPassed === 1 || this.info.viewResultScore === 1) ? (this.info.bestUeId || '') : '' // (允许查看分数 || 允许查看通过)最好成绩考试id
        });
      } else {
        this.$emit('changeStep', this.UserExamStep.result, {
          trackId: this.trackId,
          arrangeId: this.arrangeId,
          batchId: this.batchId, // 循环考试批次ID
          btid: this.btid, // 在线课堂批次ID
          gwnlUrl: this.gwnlUrl, // 人才发展url进行面包屑点击回退
          ueId: (this.info.allowViewResultPassed === 1 || this.info.viewResultScore === 1) ? (this.info.bestUeId || '') : '' // (允许查看分数 || 允许查看通过)最好成绩考试id
        });
      }
    },
    changeShowMore() {
      this.isShowMore = !this.isShowMore;
      this.$nextTick(() => {
        this.$refs.commonStickyRef && this.$refs.commonStickyRef.resetFun();
      });
    },
    // this.getWidth()=> 860-1248之间，paddingLeft => 24-64之间
    getInfoLeftStyle(type) {
      // 获取left百分比
      let left = (this.getWidth() - 860) / (1248 - 860) * (64 - 24) + 24;
      if (left < 24) {
        left = 24;
      } else if (left > 64) {
        left = 64;
      }
      if (type === 'right') {
        left -= 16;
      }
      return {
        paddingLeft: left + 'px'
      };
    },
    confirmDeclare() {
      this.isShowDeclare = false;
      this.declareInfo = {};
      this.takeExam();
    }
  },
  beforeDestroy() {
    if (this.timer) {
      window.clearInterval(this.timer);
    }
  }
};
</script>
