<template>
  <div class="yxtulcdsdk-uexam-res-main__content__sta">
    <div class="standard-size-18 yxt-weight-5 color-gray-10 ph24 pt24 pb16">{{ $t('pc_ote_lbl_currentexamresultanalysis'/** 本次考试情况分析 */) }}</div>
    <div class="flex">
      <div class="yxtulcdsdk-uexam-res-main__content__sta--left">
        <div class="mt16 pt42 pb30">
          <yxtf-progress
            :width="200"
            :stroke-width="12"
            type="circle"
            :percentage="info.wholeRate"
          >
            <div slot="content">
              <!-- 总体正确率 -->
              <div class="font-size-16 color-gray-9 lh24">{{ $t('pc_ote_lbl_wholecorrectrate') }}</div>
              <div class="font-size-32 yxt-weight-5 color-gray-9 mt8 lh40">{{ info.wholeRate }}%</div>
            </div>
          </yxtf-progress>
        </div>
        <yxtf-divider />
        <div class="standard-size-16 flex flex-wrap flex-justify-center">
          <!-- 用时 -->
          <div class="ph12">
            <span class="color-gray-9">{{ $t('pc_ote_lbl_examusetimes') }}</span>
            <span class="ml8 yxt-weight-5 color-gray-10" v-html="getTimeStr(info.usedTimes)"></span>
          </div>
          <!-- 排名 -->
          <div class="ph12">
            <span class="color-gray-9">{{ $t('pc_ote_lbl_usetimerank' /** 用时排名 */) }}</span>
            <span v-if="info.usedTimesRank > 0" class="ml8 yxt-weight-5 color-gray-10">{{ info.usedTimesRank }}/{{ info.totalQty }}</span>
            <span v-else class="ml8 yxt-weight-5 color-gray-10">--/{{ info.totalQty }}</span>
          </div>
        </div>
      </div>
      <div class="yxtulcdsdk-uexam-res-main__content__sta--right">
        <ul>
          <!-- 总体正确率、排名 -->
          <li class="bg-gray-1">
            <div class="p16">
              <div class="standard-size-16 color-gray-10 yxt-weight-5 nowrap-1">{{ $t('pc_ote_lbl_wholecorrectrate') }}</div>
              <div class="pt16 clearfix flex flex-mid">
                <span class="standard-size-14 color-gray-9 yxtulcdsdk-flex-shrink-0">{{ $t('pc_ote_lbl_accuracy') }}</span>
                <yxtf-progress
                  :percentage="info.wholeRate"
                  :show-text="false"
                  class="pl12 box-size-contentbox yxtulcdsdk-flex-shrink-1"
                />
                <span class="pl12 box-size-contentbox standard-size-14 color-gray-10 yxt-weight-5">{{ info.wholeRate }}%</span>
              </div>
              <div class="pt16 standard-size-14 color-gray-9">
                <span>{{ $t('pc_ote_lbl_examusetimes') }}</span>
                <span class="ml8 color-gray-10 yxt-weight-5" v-html="getTimeStr(info.usedTimes)"></span>
                <span class="ml24">{{ $t('pc_ote_lbl_rank') }}</span>
                <span v-if="info.usedTimesRank > 0" class="ml8 yxt-weight-5 color-gray-10">{{ info.usedTimesRank }}/{{ info.totalQty }}</span>
                <span v-else class="ml8 yxt-weight-5 color-gray-10">--/{{ info.totalQty }}</span>
              </div>
            </div>
          </li>
          <!-- 各题型 正确率、排名-->
          <li v-for="(item, index) in info.ue4QuesStaList" :key="index" class="bg-gray-1">
            <div class="p16">
              <!-- 题型/组合题名称 -->
              <div class="standard-size-16 color-gray-10 yxt-weight-5 nowrap-1">{{ item.alias ? item.alias : getQuestionType(item.quesType) }}</div>
              <!-- 正确率 -->
              <div class="pt16 clearfix flex flex-mid">
                <span class="standard-size-14 color-gray-9 yxtulcdsdk-flex-shrink-0">{{ $t('pc_ote_lbl_accuracy') }}</span>
                <yxtf-progress
                  :percentage="item.correctRate"
                  :show-text="false"
                  class="pl12 box-size-contentbox yxtulcdsdk-flex-shrink-1"
                />
                <span class="pl12 box-size-contentbox standard-size-14 color-gray-10 yxt-weight-5">{{ item.correctRateText }}</span>
              </div>
              <!-- 答对/排名 -->
              <div class="pt16 standard-size-14 color-gray-9 flex flex-wrap">
                <div class="mr24">
                  <span>{{ $t('pc_ote_lbl_answercorrect') }}</span>
                  <span class="ml8 color-gray-10 yxt-weight-5">{{ item.correctQty }}/{{ item.totalQty }}</span>
                </div>
                <div>
                  <span>{{ $t('pc_ote_lbl_rank') }}</span>
                  <span class="ml8 color-gray-10 yxt-weight-5">{{ item.rank > 0 ? item.rank : '--' }}/{{ info.totalQty }}</span>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { QUES_TYPE_NAMES } from '../../configs/const';
export default {
  name: 'ExamResultAnalysis',
  props: {
    info: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  methods: {
    // 获取题型名称
    getQuestionType(val) {
      return QUES_TYPE_NAMES.length > val ? this.$t(QUES_TYPE_NAMES[val]) : '';
    },
    getTimeStr(val) {
      const s = val;
      let timeStr = '';
      if (s > 0) {
        const min = Math.floor(s / 60);
        const sec = s % 60;
        if (min > 0) { timeStr += min + this.$t('pc_ote_lbl_shortminute'); }
        if (sec > 0) { timeStr += sec + this.$t('pc_ote_lbl_shortsecond'); }
      } else {
        timeStr = '0 ' + this.$t('pc_ote_lbl_shortsecond');
      }
      return timeStr;
    }
  }
};
</script>
