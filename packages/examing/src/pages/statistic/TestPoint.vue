<template>
  <div v-if="tableData && tableData.length" class="ph24 pv32 bg-white box-border yxtbizf-br-4">
    <div class="standard-size-18 yxt-weight-5 color-gray-10">{{ $t('pc_ote_tit_point_result' /* 考核点得分情况 */) }}</div>

    <yxt-table
      v-loading="loading"
      :data="tableData"
      class="mt16"
      border
    >
      <yxt-table-column
        prop="pointName"
        :label="$t('pc_ote_lbl_assessmentpoint' /* 考核点 */)"
      />
      <yxt-table-column
        prop="totalScore"
        width="132"
        align="center"
        :label="$t('pc_ote_lbl_totalscore' /* 总分 */)"
      >
        <template slot="headerIcon">
          <yxt-tooltip :content="$t('pc_ote_lbl_totalscore_desc' /* 试卷中该考核点对应试题的总分 */)" placement="top" effect="light">
            <yxt-svg
              class="color-gray-7 ml2"
              width="16px"
              height="16px"
              icon-class="question-cirlce-o"
            />
          </yxt-tooltip>
        </template>
      </yxt-table-column>
      <yxt-table-column
        prop="userGainScore"
        width="132"
        align="center"
        :label="$t('pc_ote_lbl_scores' /* 得分 */)"
      >
        <template slot="headerIcon">
          <yxt-tooltip :content="$t('pc_ote_lbl_score_desc' /* 试卷中该考核点对应试题该考生的总得分 */) " placement="top" effect="light">
            <yxt-svg
              class="color-gray-7 ml2"
              width="16px"
              height="16px"
              icon-class="question-cirlce-o"
            />
          </yxt-tooltip>
        </template>
      </yxt-table-column>
      <yxt-table-column
        prop="correctRate"
        width="132"
        align="center"
        :label="$t('pc_ote_lbl_accuracy' /* 正确率 */)"
      >
        <template slot-scope="scope">{{ Math.round(scope.row.correctRate * 100) }}%</template>
        <template slot="headerIcon">
          <yxt-tooltip :content="$t('pc_ote_lbl_rate_desc' /* 得分/总分 */)" placement="top" effect="light">
            <yxt-svg
              class="color-gray-7 ml2"
              width="16px"
              height="16px"
              icon-class="question-cirlce-o"
            />
          </yxt-tooltip>
        </template>
      </yxt-table-column>
    </yxt-table>
    <yxtf-pagination
      class="mt16 text-right"
      :current-page="current"
      :page-size="limit"
      simple-total
      layout="total, prev, pager, next"
      :total="total"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import { getPointSta } from 'packages/examing/src/service/user.service';

export default {
  name: 'TestPoint',
  props: {
    ueId: {
      type: String
    },
    arrangeId: {
      type: String
    }
  },
  components: {},
  data() {
    return {
      loading: false,
      tableData: [],

      limit: 10,
      current: 1,
      total: 0
    };
  },

  created() {
    this.ueId && this.getPointSta();
  },

  methods: {
    getPointSta() {
      this.loading = true;
      getPointSta({ limit: this.limit, current: this.current, ueId: this.ueId, arrangeId: this.arrangeId }).then(res => {
        const { datas, paging } = res;
        this.tableData = datas;
        this.total = paging.count;
      }).catch((error) => { console.log(error); }).finally(() => {
        this.loading = false;
      });
    },

    handleCurrentChange(v) {
      this.current = v;
      this.getPointSta();
    }
  },

  watch: {
    ueId(v) {
      v && this.getPointSta();
    }
  }
};
</script>
