<template>
  <div
    class="wp-100"
  >
    <div
      v-if="!firstLoaded"
      v-loading="!firstLoaded"
      :style="{
        minHeight: getHeight() + 'px'
      }"
    >
    </div>
    <div>
      <CommonSticky
        v-if="firstLoaded && (deepStudy || $route.name !== 'examresultcom')"
        ref="stickyLeft"
        position="top"
        class="z-999"
        :by-class="deepStudy"
      >
        <div class="yxtulcdsdk-exam-header">
          <div
            class="flex flex-mid hand hover-primary-6"
            @click="linkToPreview()"
          >
            <yxt-svg icon-class="icons/arrow-left" width="16px" height="16px" />
            <span class="ml8">{{ $t('pc_ote_lbl_backtoexampreview'/** 返回考试说明页 */) }}</span>
          </div>
        </div>
      </CommonSticky>
      <div
        v-show="firstLoaded"
        class="yxtulcdsdk-uexam-res"
        :class="{
          'yxtulcdsdk-uexam-res--small': deepStudy && (getWidth() < (1024 + 48)),
          'yxtulcdsdk-uexam-res__tourist': $route.name === 'examresultany',
          'mb24 ph24 box-size-contentbox': deepStudy}"
      >
        <div v-if="!deepStudy && $route.name === 'examresultcom'" class="yxtulcdsdk-uexam-preview__task">
          <stu-breadcrumb
            v-if="$route.name === 'examresultcom' && isLoaded"
            :breadcrumb="breadcrumb"
            :current-name="$t('pc_ote_lbl_examresult')"
          />
          <yxtbiz-skip-task v-if="arrInfo.masterType === 1 && arrInfo.masterId" :biz-data="{taskId: arrInfo.id, projectId: arrInfo.masterId, trackId, gwnlUrl}" />
        </div>
        <!-- 考生试卷信息 -->
        <div class="yxtulcdsdk-uexam-res-top">
          <div class="yxtulcdsdk-uexam-res-top__base">
            <div class="flex flex-top wp-100">
              <div class="flex-g-1 over-auto yxtulcdsdk-flex-shrink-1 pr mr32">
                <yxtf-tooltip
                  :content="arrInfo.batchName || arrInfo.name"
                  placement="top"
                  open-filter
                  :max-width="780"
                >
                  <div class="color-gray-10 font-size-20 yxt-weight-5 lh28  ellipsis">{{ arrInfo.batchName || arrInfo.name }}</div>
                </yxtf-tooltip>
              </div>
              <div class="yxtulcdsdk-flex-shrink-0 pr">
                <!-- 通过、不通过、批阅中 -->
                <custom-icon
                  v-if="(arrInfo.allowViewResultPassed === 1) && infoLoaded"
                  width="66px"
                  height="66px"
                  :type="arrInfo.userStatus > 3 ? (arrInfo.passed ? 'sucess' : 'error'):'warning' "
                >
                  {{ $t(arrInfo.userStatus > 3 ?(arrInfo.passed ? 'pc_ote_lbl_passed' : 'pc_ote_lbl_unpass'):'pc_ote_lbl_marking') }}
                </custom-icon>
                <!-- 再次考试 -->
                <yxtf-button
                  v-if="allowExamAgain"
                  :type="allowRedoWrongs && !oteWrongQues.hidden ? '' : 'primary'"
                  @click="linkToPreview(1)"
                >
                  {{ $t('pc_ote_btn_examagain') }}
                </yxtf-button>
                <!-- 重做错题 -->
                <yxtf-button
                  v-if="allowRedoWrongs && !oteWrongQues.hidden"
                  v-checkButtons="oteWrongQues.value"
                  :disabled="oteWrongQues.disable"
                  type="primary"
                  @click="linkToWrongs('exam')"
                >
                  {{ $t('pc_ote_lbl_redoWrongQuestion') }}
                </yxtf-button>
              </div>
            </div>
            <div class="yxtulcdsdk-info-list mt8">
              <!-- 考生 -->
              <span>
                <span>{{ $t('pc_ote_lbl_examinee') }}</span>
                <span>
                  <yxtbiz-user-name :name="info.fullname" /><template v-if="info.userName || info.username">（{{ info.userName || info.username }}）</template>
                </span>
              </span>
              <!-- 工号 -->
              <span>
                <span>{{ $t('pc_ote_lbl_job_no') }}</span>
                <span>{{ info.userNo || '--' }}</span>
              </span>
              <!-- 部门 -->
              <span>
                <span>{{ $t('pc_ote_lbl_department') }}</span>
                <span><yxtbiz-dept-name :name="info.deptName || '--'" /></span>
              </span>
            </div>
            <div class="yxtulcdsdk-info-list">
              <!-- 试卷总分 -->
              <span>
                <span>{{ $t('pc_ote_lbl_exampapertotalscore') }}</span>
                <span>{{ $t('pc_ote_lbl_many_score',[arrInfo.totalScore]/*{0}分*/) }}</span>
              </span>
              <!-- 最高分（学员维度） -->
              <span>
                <span>{{ $t('pc_ote_lbl_highestscore') }}</span>
                <span v-if="arrInfo.viewResultScore === 1 && maxScore === -1">--</span>
                <span v-else-if="arrInfo.viewResultScore === 1">{{ $t('pc_ote_lbl_many_score',[maxScore]/*{0}分*/) }}</span>
                <span v-else>**</span>
              </span>
              <!-- 考试次数 -->
              <span>
                <span>{{ $t('pc_ote_lbl_examtimes') }}</span>
                <span v-if="arrInfo.repeated && arrInfo.repeatTimes === 0">{{ $t('pc_ote_lbl_nolimit') }}</span>
                <span v-else-if="arrInfo.repeated">{{ $t('pc_ote_lbl_many_time',[ arrInfo.repeatTimes]/*{0}次*/) }}</span>
                <span v-else>{{ $t('pc_ote_lbl_one_times'/*1次*/) }}</span>
              </span>
              <!-- 入场 -->
              <span>
                <span>{{ $t('pc_ote_lbl_entryperiod') }}</span>
                <span v-if="arrInfo.entryStartTime || arrInfo.entryEndTime">
                  <span class="ltr-text">{{ shortMonTimeYear(arrInfo.entryStartTime) }}</span> ~ <span class="ltr-text">{{ shortMonTimeYear(arrInfo.entryEndTime) }}</span>
                </span>
                <span v-else>{{ $t('pc_ote_lbl_timenotlimit') }}</span>
              </span>
            </div>
          </div>
        </div>
        <div class="yxtulcdsdk-uexam-res-main">
          <!-- 考试历史 -->
          <exam-result-history
            v-if="uemId"
            :uem-id="uemId"
            :arr-info="arrInfo"
            :ue-id="ueId"
            :batch-id="batchId"
            :btid="btid"
            @getStaDetail="getStaDetail"
            @setMaxScore="setMaxScore"
            @noData="() => firstLoaded = true"
          />
          <div v-loading="loadingDetail" class="yxtulcdsdk-uexam-res-main__content">
            <div>
              <!-- ue基本信息 -->
              <exam-result-base
                :arr-info="arrInfo"
                :info="info"
                :ue-id="ueId"
                :query-datas="queryDatas"
                :deep-study="deepStudy"
                @changeStep="changeStep"
              />
              <!-- 得分&通过 允许查看 -->
              <test-point
                v-if="arrInfo.viewResultScore && arrInfo.allowViewResultPassed"
                :arrange-id="arrangeId"
                :ue-id="ueId"
                class="mt16"
              />
              <!-- 待批阅 -->
              <div v-if="viewStatus === 3" class="yxtulcdsdk-uexam-res-main__content__pending">
                <div class="standard-size-18 yxt-weight-5 color-gray-10 pt22 pl24">{{ $t('pc_ote_lbl_examresultanalysis') }}</div>
                <div class="yxtulcdsdk-uexam-res-main__content__pending--img">
                  <img :src="emptyImg" alt="" class="exam-empty">
                </div>
                <div class="mt24 standard-size-18 color-gray-8 text-center">{{ $t('pc_ote_lbl_marking') }}</div>
              </div>
              <!-- 仅展示考试排名 -->
              <div v-else-if="info.viewResultScore === 0" class="yxtulcdsdk-uexam-res-main__content__rank">
                <!-- 考试排名分析 -->
                <div class="standard-size-18 yxt-weight-5 color-gray-10 pt22 pl24">{{ $t('pc_ote_lbl_examrankanalysis') }}</div>
                <div class="ph24 pb24">
                  <ul class="clearfix">
                    <li v-for="(item, index) in info.ue4QuesStaList" :key="index" class="fl mt16 mr64">
                      <!-- 排名 -->
                      <div class="standard-size-12 color-gray-8">{{ $t('pc_ote_lbl_rankof', [item.alias ? item.alias : getQuestionType(item.quesType)]) }}</div>
                      <div class="standard-size-24 color-gray-10 yxt-weight-5 mt4">
                        <span v-if="info.viewResultScore">{{ item.rank > 0 ? item.rank : '--' }}/{{ info.totalQty }}</span>
                        <span v-else>**</span>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <!-- 未分析数据 -->
              <div v-else-if="info.rank === 0" class="yxtulcdsdk-uexam-res-main__content__pending">
                <div class="standard-size-18 yxt-weight-5 color-gray-10 pt22 pl24">{{ $t('pc_ote_lbl_examresultanalysis') }}</div>
                <div class="yxtulcdsdk-uexam-res-main__content__pending--img">
                  <img :src="emptyImg" alt="" class="exam-empty">
                </div>
                <!-- 考试详情待分析 -->
                <div class="mt24 standard-size-18 color-gray-8 text-center">{{ $t('pc_ote_lbl_examdetailpendinganalyze') }}</div>
              </div>
              <!-- 考试情况分析 -->
              <exam-result-analysis v-else :info="info" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { QUES_TYPE_NAMES } from '../../configs/const';
import { getExamResultSta, getArrangeInfoStu, getO2OTaskInfo } from '../../service/user.service';
import { shortDateTime, shortMonTimeYear } from '../../core/utils';
import ExamResultHistory from './ExamResultHistory.vue';
import ExamResultBase from './ExamResultBase.vue';
import ExamResultAnalysis from './ExamResultAnalysis.vue';
import StuBreadcrumb from '../components/StuBreadcrumb.vue';
import CustomIcon from '../components/customIcon.vue';
import { LoopActivateTimeType, LoopEndType } from '../../core/enums';
import deepStudyPage from '../../mixins/deepStudyPage';
import CommonSticky from '../components/CommonSticky.vue';
import wrong from '../mixins/wrong';
import TestPoint from './TestPoint';

export default {
  name: 'UserExamResult',
  components: {
    ExamResultHistory,
    ExamResultBase,
    ExamResultAnalysis,
    StuBreadcrumb,
    CustomIcon,
    CommonSticky,
    TestPoint
  },
  mixins: [deepStudyPage, wrong],
  props: {
    arrangeId: {
      type: String,
      default: ''
    },
    userExamId: {
      type: String,
      default: ''
    },
    batchId: {
      type: String,
      default: ''
    }
  },
  inject: ['getHeight', 'getWidth', 'getFSWidth'],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      firstLoaded: false,
      trackId: routeParams.trackId,
      viewStatus: -1,
      arrInfo: {},
      loading: true,
      loadingDetail: true,
      info: {},
      infoLoaded: false,
      maxScore: -1, // 最高分
      isAllowView: false, // 考试安排允许学员查看答卷
      ulData: [],
      allowExamAgain: false,
      isLoaded: false,
      ueId: this.userExamId,
      uemId: '',
      breadcrumb: [],
      oteExamArrangeId: 'ote_exam_' + this.arrangeId,
      gwnlUrl: routeParams.gwnlUrl,
      btid: routeParams.btid
    };
  },
  computed: {
    orgId() {
      return window.localStorage.getItem('orgId') || '';
    },

    currentStep() {
      return { type: 'exam', typeValue: this.UserExamStep.result };
    }
  },
  created() {
    this.emptyImg = this.mediaPath + 'ufd/55a3e0/ote/pc/img/exam-empty.png';
    this.getArrangeInfo();
  },
  methods: {
    // 保存项目来源处理课程详情面包屑导航
    setSessionStorage() {
      const session = {
        taskId: this.routeParams.taskId,
        targetId: this.routeParams.targetId,
        targetCode: this.routeParams.targetCode,
        btid: this.btid
      };
      if (session.taskId && session.targetId && session.targetCode === 'o2o') {
        sessionStorage[this.oteExamArrangeId] = JSON.stringify(session);
      }
    },
    // 再次考试，跳转至考试预览中转页
    linkToPreview(isDoAgain) {
      if (isDoAgain && this.arrInfo.passedForbidAttend) {
        if (this.arrInfo.hasMarking) {
          // 批阅中 只看最新的是不是批阅中
          this.$message({
            type: 'warning',
            message: this.$t('pc_ote_msg_reviewedtojoin' /** 考试开启了通过后不允许再考，等批阅完成后再操作 */)
          });
          return;
        } else if (this.arrInfo.passed) {
          // 已通过
          this.$message({
            type: 'warning',
            message: this.$t('pc_ote_msg_passednojoin' /** 考试已通过，无需再考 */)
          });
          return;
        }
      }

      if (this.arrInfo.masterType === 1 && this.arrInfo.masterId && !this.deepStudy) {
        // 项目下的考试，成绩通知进去是非沉浸式的，需要回到沉浸式的页面
        let taskId = '';
        const targetId = this.arrInfo.resited ? this.arrInfo.originId : this.arrInfo.id;
        getO2OTaskInfo(this.arrInfo.masterId, targetId).then(res => {
          taskId = (res.datas && res.datas.id) || '';
          window.localStorage._ote_switch_redo = taskId ? targetId : ''; // 标记需要再考一次，进入沉浸式后清除
          window.location.href = `/o2o/#/playinfo?projectid=${this.arrInfo.masterId}&taskId=${taskId}`;
        }).catch((err) => {
          this.handlerPublicError(err);
        });
      } else {
        this.$emit('changeStep', this.UserExamStep.preview,
          {
            trackId: this.routeParams.trackId,
            arrangeId: this.arrangeId,
            batchId: this.batchId, // 循环考试批次ID
            btid: this.btid, // 在线课堂批次ID
            gwnlUrl: this.gwnlUrl, // 人才发展url进行面包屑点击回退
            uemId: this.uemId,
            redo: 1
          });
      }
    },
    // 获取考试安排基本信息
    getArrangeInfo(reload) {
      getArrangeInfoStu(this.arrangeId, { batchId: this.batchId, thirdBatchId: this.btid }).then(async res => {
        this.arrInfo = res;
        /**
           * M5V2对接新的课程包考试、练习方案，课程包内考试、练习会归为项目类型。ID会重新生成
           */
        if (this.arrInfo.masterBizCode === 1) {
          this.arrInfo.masterType = 2; // 这里按课程包处理页面
          this.arrInfo.masterId = this.arrInfo.masterSecondId || this.arrInfo.masterId; // 换为课程包的ID
        }
        this.isLoaded = true;
        this.uemId = res.uemId;
        this.allowExamAgain = this.checkRepeatExam();
        if (!reload && !this.deepStudy) {
          const myEnt = {
            url: '/study/#/userhome',
            name: this.$t('pc_ote_lbl_mylearning')
          };
          // 普通考试安排 我的企大>考核中心>考试预览
          this.breadcrumb.push(myEnt);
          if (this.arrInfo.masterId) {
            this.setSessionStorage();
            // 0-单独安排 1-项目安排 2-课程安排 3-练习 4-人才发展
            switch (this.arrInfo.masterType) {
              case 1:
                this.breadcrumb.push({
                  url: `/o2o/#/project/detail/${this.arrInfo.masterId}`,
                  name: this.$t('pc_ote_lbl_traindetail')
                });
                break;
              case 2:
                let url = '';
                if (sessionStorage[this.oteExamArrangeId]) {
                  const session = JSON.parse(sessionStorage[this.oteExamArrangeId]);
                  url = `&targetId=${session.targetId}&taskId=${session.taskId}&targetCode=${session.targetCode}&trackId=${this.trackId}&btid=${this.btid || ''}`;
                }
                this.breadcrumb.push({
                  url: `/kng/#/course/detail?courseId=${this.arrInfo.masterId}${url}`,
                  name: this.$t('pc_ote_lbl_coursedetail')
                });
                break;
              case 4:
                this.breadcrumb.push({
                  url: this.gwnlUrl || '/gwnl/#/web/map',
                  name: this.$t('pc_ote_lbl_talentdevelopment')
                });
                break;
              default:
                this.breadcrumb.push({
                  path: '/stu/myexam',
                  name: this.$t('pc_ote_lbl_exampracticecenter')
                });
                break;
            }
          } else {
            this.breadcrumb.push({
              path: '/stu/myexam',
              name: this.$t('pc_ote_lbl_exampracticecenter')
            });
          }
        }

        res.userExamFinished && this.updateProgress(2);
        await this.initRedoWrong(this.arrangeId);
        this.loading = false;
      }).catch(this.handleError);
    },
    setMaxScore(score) {
      this.maxScore = score;
      this.$forceUpdate();
    },
    // 判断是否允许重复考试
    checkRepeatExam() {
      let allowAgain = false;
      // 进行中、允许重复考
      if (this.arrInfo.status === 2 && this.arrInfo.repeated === 1) {
        if (this.arrInfo.repeatTimes === 0 || this.arrInfo.userExamTimes < this.arrInfo.repeatTimes || (this.arrInfo.userExamTimes === this.arrInfo.repeatTimes && (this.arrInfo.lastUeStatus === 1 || this.arrInfo.lastUeStatus === undefined))) {
          allowAgain = true;
          if (this.arrInfo.dimensionType === 1) {
            // 循环考试 无限循环，且结束时间为项目结束前的考试结束时间是null
            if (this.arrInfo.endType === LoopEndType.infinitely && this.arrInfo.effectType === LoopActivateTimeType.loopEnd) {
              allowAgain = true;
            } else {
              allowAgain = this.checkTime(this.arrInfo.entryStartTime, this.arrInfo.entryEndTime, this.arrInfo.currentSystemTime);
            }
          } else {
            if (this.arrInfo.entryTimeCtrl === 1) {
              allowAgain = this.checkTime(this.arrInfo.entryStartTime, this.arrInfo.entryEndTime, this.arrInfo.currentSystemTime);
            }
          }

          if (allowAgain && this.arrInfo.forceSubmitCtrl === 1) {
            allowAgain = this.checkTime(null, this.arrInfo.forceSubmitTime, this.arrInfo.currentSystemTime);
          }
        }
      }
      // 进行中 && 有未提交的考试记录 =》 显示再次考试
      if (this.arrInfo.status === 2 && this.arrInfo.lastUeStatus === 1) {
        allowAgain = true;
      }
      return allowAgain;
    },
    // 获取ue统计详情
    getStaDetail(ueId) {
      this.loadingDetail = true;
      this.ueId = ueId;
      const query = {
        batchId: this.batchId, // 循环考试批次ID
        thirdBatchId: this.btid, // 在线课堂批次ID
        orgId: this.orgId
      };
      getExamResultSta(ueId, query).then(res => {
        this.infoLoaded = true;
        this.info = res;
        this.info.wholeRate = 0;
        this.viewStatus = res.status;
        this.calPerRate();
        this.calWholeRate();
        // this.checkViewStatus()
        this.loadingDetail = false;
        this.firstLoaded = true;
      }).catch(this.handleError);
    },
    // 校验查看试卷的时间段
    checkTime(startTime, endTime, sysTime) {
      startTime = startTime && Date.parse(startTime.replace(/-/g, '/'));
      endTime = Date.parse(endTime.replace(/-/g, '/'));
      sysTime = sysTime ? Date.parse(sysTime.replace(/-/g, '/')) : new Date().getTime();
      return (!startTime || (startTime < sysTime)) && endTime > sysTime;
    },
    // 计算整体正确率
    calWholeRate() {
      let quesQty = 0;
      let correctQty = 0;
      this.info.ue4QuesStaList.forEach(function(e) {
        quesQty += e.totalQty;
        correctQty += e.correctQty;
      });
      if (quesQty > 0) {
        this.info.wholeRate = parseFloat((correctQty * 100 / quesQty).toFixed(1));
      }
    },
    // 计算每题正确率
    calPerRate() {
      if (this.info.ue4QuesStaList && this.info.ue4QuesStaList.length > 0) {
        this.info.ue4QuesStaList.forEach(function(e) {
          if (e.totalQty > 0) {
            e.correctRate = parseFloat((e.correctQty * 100 / e.totalQty).toFixed(1));
            e.correctRateText = e.correctRate + '%';
          } else {
            e.correctRate = 0;
            e.correctRateText = '0%';
          }
        });
      }
    },
    // 获取题型名称
    getQuestionType(val) {
      return QUES_TYPE_NAMES.length > val ? this.$t(QUES_TYPE_NAMES[val]) : '';
    },
    shortDateTime,
    shortMonTimeYear
  }
};
</script>
