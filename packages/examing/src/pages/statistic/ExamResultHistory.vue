<template>
  <div v-if="ulData && ulData.length > 1" class="yxtulcdsdk-uexam-res-main__bar">
    <div class="yxtulcdsdk-uexam-res-main__bar--box">
      <div class="yxtulcdsdk-uexam-res-main__bar--title">
        <div class="pt18 pb14 pl20 flex">
          <span class="standard-size-16 yxt-weight-5 ">{{ $t('pc_ote_lbl_examhistory') }}({{ ulData.length }})</span>
        </div>
      </div>
      <div class="yxtulcdsdk-uexam-res-main__bar--ul">
        <div class="width-percent-100">
          <yxt-scrollbar :fit-height="true">
            <ul class="clearfix pb12">
              <li
                v-for="(item, index) in ulData"
                :key="item.ueId"
                class="hand"
                :class="{'actived': ueId === item.id || hoverId === item.id}"
                @click="changeUser(item.id, item.status, index)"
                @mouseenter="enterUser(item.id, index)"
                @mouseleave="leaveUser"
              >
                <div v-if="index !== 0">
                  <yxtf-divider />
                </div>
                <div class="pv12 pl12">
                  <!-- 成绩 -->
                  <div v-if="item.status === 4 || item.status === 3" class="color-gray-10 standard-size-14 flex flex-mid">
                    {{ $t('pc_ote_lbl_academy') }}：
                    <span v-if="item.status === 3 && arrInfo.viewResultScore">--</span>
                    <span v-else-if="arrInfo.viewResultScore">{{ item.score }}</span>
                    <span v-else>**</span>
                    <span v-if="item.status === 3" class="ml12 standard-size-12 text-warning">{{ $t('pc_ote_lbl_marking') }}</span>
                    <span v-else-if="arrInfo.allowViewResultPassed && item.passed" class="ml8 standard-size-12 yxtf-color-success">{{ $t('pc_ote_lbl_pass') }}</span>
                    <span v-else-if="arrInfo.allowViewResultPassed && item.passed === 0" class="ml8 standard-size-12 yxtf-color-danger">{{ $t('pc_ote_lbl_unpass') }}</span>
                  </div>
                  <!-- 时间 -->
                  <div class="color-gray-8 standard-size-14 mt8">{{ $t('pc_ote_lbl_time') }}：<span class="ltr-text">{{ shortMonTime(item.submitTime) }}</span></div>
                </div>
              </li>
            </ul>
          </yxt-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { shortMonTime } from '../../core/utils';
import { getExamHistory } from '../../service/user.service';
export default {
  name: 'ExamResultHistory',
  props: {
    uemId: {
      type: String,
      default: ''
    },
    batchId: {
      type: String,
      default: ''
    },
    arrInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    ueId: {
      type: String,
      default: ''
    },
    btid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      hoverId: '',
      // viewStatus: -1,
      currIndex: -1,
      currIndex1: -1,
      ulData: []
    };
  },
  computed: {
    orgId() {
      return window.localStorage.getItem('orgId') || '';
    }
  },
  mounted() {
    this.getHistory();
  },
  methods: {
    // 获取考试历史
    getHistory() {
      const query = {
        batchId: this.batchId, // 循环考试批次ID
        thirdBatchId: this.btid, // 在线课堂批次ID
        orgId: this.orgId
      };
      getExamHistory(this.uemId, query).then(res => {
        this.ulData = res;
        if (!res || res.length < 1) {
          this.$emit('noData');
          return;
        }
        if (this.arrInfo.viewResultScore === 1) {
          this.getMaxScore();
        }
        this.$emit('getStaDetail', this.ueId ? this.ueId : res[0].id);
        if (this.currIndex === -1) {
          this.matchIndex(this.ueId);
        }
      }).catch(this.handleError);
    },
    // 首次进入页面，定为ueId所在位置，处理分割线
    matchIndex() {
      for (let i = 0; i < this.ulData.length; i++) {
        if (this.ulData[i].id === this.ueId) {
          this.currIndex = i;
          break;
        }
      }
    },
    // 切换考试历史用户
    changeUser(id, status, index) {
      // this.viewStatus = status
      this.currIndex = index;
      this.$emit('getStaDetail', id);
    },
    // 移入
    enterUser(id, index, type) {
      this.hoverId = id;
      this.currIndex1 = index;
    },
    // 移出
    leaveUser() {
      this.hoverId = '';
      this.currIndex1 = -1;
    },
    // 通过考试历史获得最高分
    getMaxScore() {
      let maxScore = -1;
      for (let i = 0; i < this.ulData.length; i++) {
        const e = this.ulData[i];
        if (e.status === 4 && e.score > maxScore) {
          maxScore = e.score + '';
        }
      }
      this.$emit('setMaxScore', maxScore);
    },
    shortMonTime
  }
};
</script>
