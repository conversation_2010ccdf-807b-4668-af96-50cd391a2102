<template>
  <div class="yxtulcdsdk-uexam-res-main__content__base">
    <yxt-row class="yxtulcdsdk-uexam-res-main__content__base--shape">
      <!-- 是否通过 -->
      <div class="yxtulcdsdk-ueresult-ev">
        <div class="yxtulcdsdk-ueresult-ev-1">
          <yxt-svg-icon
            icon-class="res-passed"
            class-name="color-primary-6"
            width="32px"
            height="32px"
          />
        </div>
        <div class="yxtulcdsdk-ueresult-ev-2">
          <div class="standard-size-14 color-gray-7 mb4">{{ $t('pc_ote_lbl_ispass') }}</div>
          <div v-if="info.allowViewResultPassed === 1 && isMarking" class="standard-size-32 yxt-weight-5 color-gray-10 ">--</div>
          <div v-else-if="info.allowViewResultPassed === 1" class="standard-size-32 font-size-24 yxt-weight-5 color-gray-10">{{ info.passed === 1 ? $t('pc_ote_lbl_pass') : $t('pc_ote_lbl_unpass') }}</div>
          <div v-else class="standard-size-32 yxt-weight-5 color-gray-10">**</div>
        </div>
      </div>
      <!-- 考试成绩 -->
      <div class="yxtulcdsdk-ueresult-ev">
        <div class="yxtulcdsdk-ueresult-ev-1">
          <yxt-svg-icon
            icon-class="res-score"
            class-name="color-primary-6"
            width="32px"
            height="32px"
          />
        </div>
        <div class="yxtulcdsdk-ueresult-ev-2">
          <div class="standard-size-14 color-gray-7 mb4">{{ $t('pc_ote_lbl_examscore') }}</div>
          <div class="standard-size-32 color-gray-10">
            <div v-if="info.viewResultScore === 1 && isMarking" class="text-center yxt-weight-5">--</div>
            <span v-else-if="info.viewResultScore === 1"><span class="yxt-weight-5">{{ info.score }}</span><span class="standard-size-16">/{{ info.totalScore }}</span></span>
            <span v-else>**</span>
            <!-- 优秀分数标识 -->
            <yxt-svg-icon
              v-if="arrInfo.resited !== 1 && info.viewResult && info.allowViewResultPassed && info.excellenceScore > 0 && info.score >= info.excellenceScore"
              :icon-class="setLanguageClass('excellent-')"
              class="fr ml8"
              width="33px"
              height="38px"
            />
          </div>
        </div>
      </div>
      <!-- 考试用时 -->
      <div class="yxtulcdsdk-ueresult-ev">
        <div class="yxtulcdsdk-ueresult-ev-1">
          <yxt-svg-icon
            icon-class="res-usedtime"
            class-name="color-primary-6"
            width="32px"
            height="32px"
          />
        </div>
        <div class="yxtulcdsdk-ueresult-ev-2">
          <div class="standard-size-14 color-gray-7 mb4">{{ $t('pc_ote_lbl_answerusetime') }}</div>
          <div v-unsafehtml="convertTimeStr(info.usedTimes, 1)" class="standard-size-32 yxt-weight-5 color-gray-10"> </div>
        </div>
      </div>
      <!-- 本次考试排名 -->
      <div class="yxtulcdsdk-ueresult-ev">
        <div class="yxtulcdsdk-ueresult-ev-1">
          <yxt-svg-icon
            icon-class="res-rank"
            class-name="color-primary-6"
            width="32px"
            height="32px"
          />
        </div>
        <div class="yxtulcdsdk-ueresult-ev-2">
          <div class="standard-size-14 color-gray-7 mb4">{{ $t('pc_ote_lbl_thisexamrank') }}</div>
          <!-- 2022-12-06 新增逻辑，如果管理端设置不允许学员查看考试成绩时，正确率和考试排名隐藏 -->
          <div v-if="info.viewResultScore && isMarking" class="standard-size-32 yxt-weight-5 color-gray-10">--</div>
          <div v-else-if="info.rank > 0 && info.viewResultScore" class="standard-size-32 yxt-weight-5 color-gray-10">{{ info.rank }}</div>
          <div v-else class="standard-size-32 yxt-weight-5 color-gray-10">
            <yxtf-tooltip :content="$t('pc_ote_msg_noviewpermission')" placement="right" popper-class="yxtulcdsdk-ueresult-ev-starpop"><span class="d-in-block">*</span></yxtf-tooltip>
          </div>
        </div>
      </div>
    </yxt-row>
    <div class="yxtulcdsdk-uexam-res-main__content__base--time clearfix">
      <div class="fl color-gray-7">
        <!-- 答题时间 -->
        <span>{{ $t('pc_ote_lbl_answertime') }}：</span>
        <span class="ltr-text">{{ shortMonTimeYear(info.startTime) }}</span> ~ <span class="ltr-text">{{ shortMonTimeYear(info.submitTime) }}</span>
      </div>
      <!-- 查看答卷详情 -->
      <yxtf-popover
        :key="viewResultDetailMsg"
        :content="viewResultDetailMsg"
        placement="top"
        trigger="hover"
        class="fr"
        :disabled="!viewResultDetailMsg"
        :padding-small="true"
      >
        <div slot="reference">
          <yxtf-button
            v-if="isCanView"
            :disabled="isCanView === 2"
            @click="viewPaper()"
          >
            {{ $t('pc_ote_lbl_viewpaperdetail') }}
          </yxtf-button>
        </div>
      </yxtf-popover>
    </div>
  </div>
</template>
<script>
import { shortMonTimeYear } from '../../core/utils';
import { setLanguageClass } from '../../configs/language/index.js';
import { UserExamStep } from '../../core/enums';
import YxtSvgIcon from '../components/svgIcon.vue';
import deepStudyPage from '../../mixins/deepStudyPage';
export default {
  name: 'ExamResultBase',
  components: {
    YxtSvgIcon
  },
  props: {
    arrInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    info: {
      type: Object,
      default: () => {
        return {};
      }
    },
    ueId: {
      type: String,
      default: ''
    }
  },
  mixins: [deepStudyPage],
  data() {
    return {
      UserExamStep
    };
  },
  computed: {
    isMarking() {
      return this.info.status <= 3;
    },
    isCanView() {
      if (this.info.allowViewResultDetail === 1) {
        // 允许查看答卷详情
        // this.isAllowView = true
        if (this.info.status === 4 || this.info.status === 3) {
          // 已批阅 || 批阅中
          if (this.info.allowViewResultDetailType === 1) {
            // 考试通过
            return this.info.uemPassed ? 1 : 2;
          } else if (this.info.allowViewResultDetailType === 2) {
            // 考试结束
            return this.arrInfo.status >= 3 ? 1 : 2;
          } else if (this.info.allowViewResultDetailType === 0 && !this.info.allowViewResultDetailStartTime && !this.info.viewResultDetailEndTime) {
            // 不设置答卷查看范围
            return 1;
          } else if (this.info.allowViewResultDetailType === 0) {
            // 在答卷查看时间范围内
            return this.info.viewTimeExpired === 0 ? 1 : 2;
          }
        }
      }
      return 0;
    },
    viewResultDetailMsg() {
      if (this.info.allowViewResultDetail === 1) {
        // 允许查看答卷详情
        // this.isAllowView = true
        if (this.info.status === 4 || this.info.status === 3) {
          // 已批阅 || 批阅中
          if (this.info.allowViewResultDetailType === 1 && !this.info.uemPassed) {
            // 考试通过
            return this.$t('pc_ote_msg_viewbypassed');
          } else if (this.info.allowViewResultDetailType === 2 && !(this.arrInfo.status >= 3)) {
            // 考试结束
            return this.$t('pc_ote_msg_viewbyended');
          } else if (this.info.allowViewResultDetailType === 0 && !this.info.allowViewResultDetailStartTime && !this.info.viewResultDetailEndTime) {
            // 不设置答卷查看范围
            return '';
          } else if (this.info.allowViewResultDetailType === 0) {
            // 在答卷查看时间范围内
            return this.$t('pc_ote_lbl_viewdetailtime') + this.shortMonTimeYear(this.info.allowViewResultDetailStartTime) + ' ~ ' + this.shortMonTimeYear(this.info.viewResultDetailEndTime);
          }
        }
      }

      return '';
    }
  },

  methods: {
    setLanguageClass,
    shortMonTimeYear,
    // 秒转时分秒
    convertTimeStr(val) {
      const s = val;
      let timeStr = '';
      if (s > 0) {
        const min = Math.floor(s / 60);
        const sec = s % 60;
        if (min > 0) { timeStr += '<span class="standard-size-32 yxt-weight-5">' + min + '</span><span class="standard-size-16 yxt-weight-4"> ' + this.$t('pc_ote_lbl_shortminute') + ' </span>'; }
        if (sec > 0) { timeStr += sec + '<span class="standard-size-16 yxt-weight-4"> ' + this.$t('pc_ote_lbl_shortsecond') + ' </span>'; }
      } else {
        timeStr = '<span class="standard-size-32 yxt-weight-5">0</span><span class="standard-size-16 yxt-weight-4"> ' + this.$t('pc_ote_lbl_shortsecond') + '</span>';
      }
      return timeStr;
    },
    // 查看答卷详情
    viewPaper() {
      this.$emit('changeStep', this.UserExamStep.answers, {
        ueId: this.ueId,
        refparams: this.deepStudy ? this.queryDatas : undefined
      }, false, true);
    }
  }
};
</script>
