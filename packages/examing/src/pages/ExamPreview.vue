<template>
  <div class="box-size-borderbox minwidth850 ph24 flex flex-dir-ver">
    <div v-if="!deepStudy && $route.name !== 'StuExamEntryTr'" class="yxtulcdsdk-uexam-preview__task width-percent-100">
      <stu-breadcrumb
        v-if="$route.name === 'StuExamEntry' && isLoaded"
        :breadcrumb="breadcrumb"
        :current-name="$t('pc_ote_lbl_userexampreview')"
      />
      <yxtbiz-skip-task v-if="info.masterType === 1 && info.masterId" :biz-data="{taskId: arrangeId, projectId: info.masterId, trackId, gwnlUrl}" />
    </div>
    <user-exam-preview
      v-if="isLoaded"
      :deep-study="deepStudy"
      :info="info"
      :arrange-id="arrangeId"
      :batch-id="params.batchId"
      :uem-id="params.uemId"
      :master-id="params.masterId"
      :master-type="params.masterType"
      :package-id="params.packageId"
      :track-id="trackId"
      :btid="params.thirdBatchId"
      :gwnl-url="gwnlUrl"
      :redo="redo"
      @changeStep="changeStep"
    />
  </div>
</template>
<script>
import { getExamPreview, arrangeAttendCheckMakeup } from '../service/user.service';
import UserExamPreview from './preview/UserExamPreview.vue';
import StuBreadcrumb from './components/StuBreadcrumb.vue';
import deepStudyPage from '../mixins/deepStudyPage';
import { ExamUserStatusEnum } from '../core/enums';
export default {
  components: {
    UserExamPreview,
    StuBreadcrumb
  },
  mixins: [deepStudyPage],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      trackId: routeParams.trackId,
      rdtmku: routeParams.rdtmku === '1', // 是否重定向到补考
      arrangeId: routeParams.arrangeId, // 考试安排ID
      params: {
        uemId: routeParams.uemId, // 用户考试关联ID
        masterId: routeParams.masterId, // 第三方考试来源ID
        masterType: routeParams.masterType, // 第三方考试来源类型
        packageId: routeParams.packageId, // 课程包ID
        batchId: routeParams.batchId, // 循环考试批次ID
        thirdBatchId: routeParams.btid, // 在线课堂批次ID
        orgId: window.localStorage.getItem('orgId') || ''
      },
      gwnlUrl: routeParams.gwnlUrl,
      info: {},
      isLoaded: false,
      breadcrumb: [],
      oteExamArrangeId: 'ote_exam_' + routeParams.arrangeId,
      redo: routeParams.redo
    };
  },
  created() {
    if (this.rdtmku) {
      arrangeAttendCheckMakeup({
        arrangeId: this.arrangeId
      }).then((res) => {
        if (res.resitFlag) {
          this.arrangeId = res.resitArrangeId; // 换为补考的安排ID

          // 刷新成补考地址
          this.$emit('changeStep', this.UserExamStep.preview, {
            ... this.routeParams,
            rdtmku: '0',
            arrangeId: this.arrangeId,
            masterType: this.routeParams.masterType || res.type
          }, true);
        } else {
          if (!this.params.masterType) {
            this.params.masterType = res.type;
          }
          this.getDetail();
        }
      }).catch((err) => {
        this.handlerPublicError(err);
      });
    } else {
      this.getDetail();
    }
  },
  methods: {
    // 保存项目来源处理课程详情面包屑导航
    setSessionStorage() {
      const session = {
        taskId: this.routeParams.taskId,
        targetId: this.routeParams.targetId,
        targetCode: this.routeParams.targetCode,
        btid: this.routeParams.btid
      };
      if (session.taskId && session.targetId && session.targetCode === 'o2o') {
        sessionStorage[this.oteExamArrangeId] = JSON.stringify(session);
      }
    },
    getDetail() {
      getExamPreview(this.arrangeId, this.params).then(res => {
        this.info = res;
        /**
           * M5V2对接新的课程包考试、练习方案，课程包内考试、练习会归为项目类型。ID会重新生成
           */
        if (this.info.masterBizCode === 1) {
          this.info.masterType = 2; // 这里按课程包处理页面
          this.arrangeId = this.info.arrId; // 换为真正的考试安排ID
          this.info.masterId = this.info.masterSecondId || this.info.masterId; // 换为课程包的ID
          this.oteExamArrangeId = 'ote_exam_' + this.arrangeId;
        }
        this.setSessionStorage();
        let session;
        if (sessionStorage[this.oteExamArrangeId]) {
          session = JSON.parse(sessionStorage[this.oteExamArrangeId]);
        }
        // 已完成 && 在批阅 && 不是重考 =》跳转到结果页
        const isSwitchRedo = window.localStorage._ote_switch_redo === this.arrangeId; // 非沉浸式再考一次，返回沉浸式
        window.localStorage._ote_switch_redo = '';
        if ((res.userStatus === ExamUserStatusEnum.finished || res.userStatus === ExamUserStatusEnum.marking) && !this.routeParams.redo && !isSwitchRedo) {
          // 第三方考试，已经结束了的直接去考试结果页面
          if (session) {
            this.$emit('changeStep', this.UserExamStep.result, {
              trackId: this.routeParams.trackId,
              arrangeId: this.arrangeId,
              targetId: session.targetId,
              taskId: session.taskId,
              batchId: this.routeParams.batchId, // 循环考试批次ID
              btid: this.routeParams.btid, // 在线课堂批次ID
              targetCode: session.targetCode,
              gwnlUrl: this.gwnlUrl, // 人才发展url进行面包屑点击回退
              ueId: (this.info.allowViewResultPassed === 1 || this.info.viewResultScore === 1) ? (this.info.bestUeId || '') : '' // (允许查看分数 || 允许查看通过)最好成绩考试id
            }, true);
            // this.$router.replace({
            //   name: 'examresult',
            //   query: {
            //     trackId: this.routeParams.trackId,
            //     arrangeId: this.arrangeId,
            //     targetId: session.targetId,
            //     taskId: session.taskId,
            //     batchId: this.routeParams.batchId, // 循环考试批次ID
            //     btid: this.routeParams.btid, // 在线课堂批次ID
            //     targetCode: session.targetCode,
            //     gwnlUrl: this.gwnlUrl // 人才发展url进行面包屑点击回退
            //   }
            // })
          } else {
            this.$emit('changeStep', this.UserExamStep.result, {
              trackId: this.routeParams.trackId,
              batchId: this.routeParams.batchId, // 循环考试批次ID
              btid: this.routeParams.btid, // 在线课堂批次ID
              arrangeId: this.arrangeId,
              gwnlUrl: this.gwnlUrl,
              ueId: (this.info.allowViewResultPassed === 1 || this.info.viewResultScore === 1) ? (this.info.bestUeId || '') : '' // (允许查看分数 || 允许查看通过)最好成绩考试id
            }, true);
            // this.$router.replace({
            //   name: 'examresult',
            //   query: {
            //     trackId: this.routeParams.trackId,
            //     batchId: this.routeParams.batchId, // 循环考试批次ID
            //     btid: this.routeParams.btid, // 在线课堂批次ID
            //     arrangeId: this.arrangeId,
            //     gwnlUrl: this.gwnlUrl
            //   }
            // })
          }
        }

        const myEnt = {
          url: '/study/#/userhome',
          name: this.$t('pc_ote_lbl_mylearning')
        };
        // 普通考试安排 我的企大>考核中心>考试预览
        this.breadcrumb.push(myEnt);
        if (this.info.masterId) {
          // 0-单独安排 1-项目安排 2-课程安排 3-练习 4-人才发展
          switch (this.info.masterType) {
            case 1:
              this.breadcrumb.push({
                url: `/o2o/#/project/detail/${this.info.masterId}`,
                name: this.$t('pc_ote_lbl_traindetail')
              });
              break;
            case 2:
              let url = '';
              if (session) {
                url = `&targetId=${session.targetId}&taskId=${session.taskId}&targetCode=${session.targetCode}&trackId=${this.trackId}`;
              }
              this.breadcrumb.push({
                url: `/kng/#/course/detail?courseId=${this.info.masterId}${url}`,
                name: this.$t('pc_ote_lbl_coursedetail')
              });
              break;
            case 4:
              this.breadcrumb.push({
                url: this.gwnlUrl || '/gwnl/#/web/map',
                name: this.$t('pc_ote_lbl_talentdevelopment')
              });
              break;
            default:
              this.breadcrumb.push({
                path: '/stu/myexam',
                name: this.$t('pc_ote_lbl_exampracticecenter')
              });
              break;
          }
        } else {
          this.breadcrumb.push({
            path: '/stu/myexam',
            name: this.$t('pc_ote_lbl_exampracticecenter')
          });
        }
        this.isLoaded = true;
      }).catch((err) => {
        this.handlerPublicError(err);
      });
    }
  }
};
</script>
