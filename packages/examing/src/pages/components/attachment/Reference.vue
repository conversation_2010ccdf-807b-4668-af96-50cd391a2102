<template>
  <div class="yxtulcdsdk-reference-list ote-file-reference">
    <yxt-svg
      :remote-url="mediaUrl"
      :icon-class="svgName"
      width="24px"
      height="24px"
    />
    <div class="yxtulcdsdk-flex-center flex-1 ml4 over-hidden" :class="{'hand': !isEdit}" @click="$emit('detail')">
      <div class="nowrap ellipsis">
        {{ fileName }}
      </div>
      <span class="yxtulcdsdk-flex-shrink-0">{{ suffix }}</span>
      <div v-if="reference.status === 3" class="font-size-12 color-gray-7 ml24 flex-shrink-0">{{ $t('pc_ote_tip_uploading' /* 上传中... */) }}</div>
    </div>

    <div class="ml8 flex-align-center">
      <yxt-tooltip :content="$t('pc_ote_btn_download' /* 下载 */)" placement="top" effect="light">
        <yxt-svg
          v-if="reference.status !== 3"
          icon-class="download-center"
          width="20px"
          height="20px"
          class="ote-reference-download color-gray-6 hand"
          @click.native.stop="$emit('download')"
        />
      </yxt-tooltip>
      <yxt-svg
        v-if="isEdit"
        icon-class="close-circle"
        width="20px"
        height="20px"
        class="ote-reference-delete color-gray-6 ml8 hand"
        @click.native.stop="$emit('del', reference)"
      />
    </div>

    <div class="ote-reference-uploading" :style="{'width': comPerWidth}"></div>
  </div>
</template>

<script>
import { SvgMediaNewH5Url } from 'packages/examing/src/configs/const';

const fileIconMap = {
  'xls,xlsx': 'ote-file-excel',
  'ppt,pptx,pps': 'ote-file-ppt',
  'doc,docx': 'ote-file-word',
  pdf: 'ote-file-pdf',
  'jpg,jpeg,png,gif,bmp,ico': 'ote-file-image',
  'wmv,mp4,flv,avi,rmvb,mpg,mkv,mov,mts,m2ts': 'ote-file-video',
  'w4v,m4a,wma,wav,mp3,amr': 'ote-file-audio',
  'zip,rar': 'ote-file-zip'
};

export default {
  name: 'Reference',
  props: {
    reference: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },

    percent: {
      type: [String, Number]
    }
  },
  components: {
  },
  data() {
    return {
      comPerWidth: 0,
      mediaUrl: SvgMediaNewH5Url
    };
  },
  computed: {
    svgName() {
      const name = this.referenceName;
      const RegEx = /\.([^.]+)$/;
      const extendName = (name && name.match(RegEx)[1].toLowerCase()) || '';
      const matchedKey = Object.keys(fileIconMap).find(key => key.split(',').includes(extendName)) || '';
      return fileIconMap[matchedKey];
    },

    referenceName() {
      return this.reference.name || this.reference.fileName;
    },

    suffix() {
      const pointIndex = (this.referenceName || '').lastIndexOf('.');
      if (pointIndex >= 0) {
        return this.referenceName.substr(pointIndex);
      }
      return '';
    },
    fileName() {
      const pointIndex = (this.referenceName || '').lastIndexOf('.');
      if (pointIndex > 0) {
        return this.referenceName.substr(0, pointIndex);
      }
      return this.svgName;
    }
  },

  watch: {
    percent(v) {
      if (v) {
        const referenceWidth = document.querySelector('.ote-file-reference').clientWidth;
        this.comPerWidth = referenceWidth * (Number(this.percent) / 100) + 'px';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.yxtulcdsdk-reference-list {
  position: relative;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 40px;
  margin-top: 12px;
  padding: 0 14px;
  background-color: #fff;
  border-radius: 4px;

  &:hover {
    background-color: #f5f5f5;

    .ote-reference-delete,
    .ote-reference-download {
      visibility: visible;
    }
  }

  .ote-reference-delete,
  .ote-reference-download {
    visibility: hidden;
  }
}

.yxtulcdsdk-reference-list:nth-of-type(1),
.yxtulcdsdk-reference-list:nth-of-type(2) {
  margin-top: 0;
}

.ote-reference-uploading {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background-color: var(--color-primary);
}
</style>

