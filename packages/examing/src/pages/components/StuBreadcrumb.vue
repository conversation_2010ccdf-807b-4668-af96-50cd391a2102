<template>
  <yxtf-breadcrumb separator-class="yxtf-icon-arrow-right">
    <yxtf-breadcrumb-item
      v-for="nav in breadcrumb"
      :key="nav.name"
      :url=" nav.url"
      :to="{ path: nav.path }"
    >
      {{ nav.name }}
    </yxtf-breadcrumb-item>
    <yxtf-breadcrumb-item v-if="currentName">{{ currentName }}</yxtf-breadcrumb-item>
  </yxtf-breadcrumb>
</template>
<script>
export default {
  name: 'OteStuBreadcrumb',
  props: {
    breadcrumb: {
      type: Array,
      default: () => []
    },
    currentName: {
      type: String,
      default: ''
    }
  }
};
</script>
