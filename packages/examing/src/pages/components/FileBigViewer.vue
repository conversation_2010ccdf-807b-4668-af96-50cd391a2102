<!-- 创建时间2022/08/11 16:26:42 -->
<!-- 创建人：朱云东 -->
<!-- 组件描述：弹窗查看文件和媒体 -->
<template>
  <yxtbiz-image-viewer
    v-if="fileType === EnumFileType.image&&imageList.length"
    ref="imageViewer"
    :show-navbar="false"
    no-list
    :data="imageList"
  />
  <div v-else-if="visible" class="yxtulcdsdk-big-viewer">
    <div class="yxtulcdsdk-big-viewer__mask flex yxtulcdsdk-flex-center-center">
      <common-media-player
        v-if="fileType === EnumFileType.audio || fileType === EnumFileType.video"
        class="yxtulcdsdk-big-viewer__video"
        auto-start
        :height="405"
        :width="720"
        :type="type"
        :file-id="fileId"
        :options="videoOptions"
      />
      <yxtbiz-doc-viewer
        v-else-if="fileType === EnumFileType.file&&fileInfo"
        class="yxtulcdsdk-big-viewer__video"
        :file-id="fileId"
        :file-info="fileInfo"
        :width="720"
      />
      <div class="yxtulcdsdk-big-viewer__del" @click="close">
        <div class="color-white del-icon">
          <yxt-svg-icon
            :is-new-svg-url="true"
            width="14px"
            height="14px"
            icon-class="close2"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { EnumFileType } from '../../configs/const';
import CommonMediaPlayer from './CommonMediaPlayer';
import YxtSvgIcon from './svgIcon.vue';
export default {
  name: 'FileBigViewer',
  components: { CommonMediaPlayer, YxtSvgIcon},
  props: {
    fileId: {
      type: String,
      default: ''
    },
    fileType: {
      type: Number
    },
    imageUrl: {
      type: [String, Array],
      default: ''
    },
    fileInfo: {
      type: Object,
      default: undefined
    },
    videoOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      EnumFileType,
      visible: false
    };
  },
  computed: {
    imageList() {
      if (Array.isArray(this.imageUrl)) {
        return this.imageUrl;
      }

      if (typeof this.imageUrl === 'string') {
        return [{ url: this.imageUrl }];
      }

      return [{ url: '' }];
    },
    type() {
      switch (this.fileType) {
        case EnumFileType.video:
          return 'video';
        case EnumFileType.audio:
          return 'audio';
        default:
          return '';
      }
    }
  },
  mounted() {
    this.addNode();
  },
  destroyed() {
    this.removeNode();
  },
  methods: {
    show() {
      if (this.fileType === EnumFileType.image) {
        this.$refs.imageViewer.view(0);
      } else {
        this.visible = true;
      }
    },
    close() {
      this.visible = false;
    },
    removeNode() {
      if (this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el);
      }
      document.body.style.overflow = this.visible ? 'hidden' : '';
    },
    addNode() {
      document.body.appendChild(this.$el);
      document.body.style.overflow = this.visible ? 'hidden' : '';
    }
  }
};
</script>
