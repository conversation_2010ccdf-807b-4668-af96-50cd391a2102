<template>
  <div>
    <template v-if="isWrongQuestionMode || showOldDialog">
      <div class="yxtulcdsdk-confirm-dialog__tip-icon">
        <yxt-svg-icon icon-class="tip-good" width="80px" height="80px" />
      </div>
      <div class="yxtulcdsdk-confirm-dialog__tip-msg">
        {{ $t('pc_ote_msg_practicedonealltip') }}
      </div>
      <div class="yxtulcdsdk-confirm-dialog__btns">
        <yxtf-button
          size="larger"
          :type="repeated || isWrongQuestionMode ? '' : 'primary'"
          :plain="!!repeated"
          @click="$emit('quit-practice')"
        >
          {{ $t('pc_ote_btn_quitpractice') }}
        </yxtf-button>
        <yxtf-button
          v-if="repeated || isWrongQuestionMode"
          size="larger"
          type="primary"
          @click="$emit('do-one-more')"
        >
          {{ $t('pc_ote_btn_practiceagain') }}
        </yxtf-button>
      </div>
    </template>

    <template v-else>
      <div class="color-gray-10 font-size-24 lh30 font-bolder">{{ submitStaInfo.correctRate }}</div>
      <div class="font-size-16 color-gray-10 lh24 mt4">{{ $t('pc_ote_lbl_current_accuracy' /* 本次正确率 */) }}</div>
      <div class="mt48 ml48 mr48 bg-gray-2 pv32">
        <div v-for="(sta, index) in staInfo" :key="index" class=" yxtulcd-practice-sta__info">
          <div class="color-gray-8 d-in-block flex flex-mid">
            <yxt-svg-icon
              width="18px"
              height="18px"
              :icon-class="sta.iconClass"
              is-new-svg-url
            />
            <span class="ml8">{{ sta.text }}</span>
          </div>
          <div class="yxtulcd-practice-sta__data font-bolder">{{ sta.val }}</div>
        </div>
      </div>

      <div class="mt32 pb24">
        <yxtf-button-group :class="{'yxtulcdsdk-button-group__btns': btnInfo.third.show}">
          <yxtf-button
            v-if="btnInfo.third.show"
            size="others"
            plain
            @click="$emit('go-third')"
          >{{ btnInfo.third.text }}</yxtf-button>
          <yxtf-button
            v-if="btnInfo.cancel.show"
            size="others"
            plain
            @click="$emit('go-cancel')"
          >{{ btnInfo.cancel.text }}</yxtf-button>
          <yxtf-button size="others" type="primary" @click="$emit('go-main')">{{ $t('pc_ote_btn_quitpractice' /* 退出练习 */) }}</yxtf-button>
        </yxtf-button-group>
      </div>
    </template>
  </div>
</template>

<script>
import { convertSecToMS } from '../../core/utils';
import YxtSvgIcon from '../components/svgIcon.vue';

export default {
  name: 'PracticeStatistics',
  props: {
    submitStaInfo: Object,
    btnInfo: {
      type: Object,
      default: {}
    },
    isWrongQuestionMode: [Number, Boolean],
    repeated: Boolean,
    showOldDialog: Boolean
  },

  components: {
    YxtSvgIcon
  },

  data() {
    return {
      staInfo: [
        { iconClass: 'practice-ques-num', text: this.$t('pc_ote_lbl_questotal' /* 试题总数 */), val: this.$t('pc_ote_lbl_many_question', [this.submitStaInfo.totalQty]) },
        { iconClass: 'practice-wrong', text: this.$t('pc_ote_lbl_current_number_wrongs' /* 本次错题数 */), val: this.$t('pc_ote_lbl_many_question', [this.submitStaInfo.errorQty]) },
        { iconClass: 'practice-time', text: this.$t('pc_ote_lbl_current_practice_duration' /* 本次练习时长 */), val: this.convertTimeStr(this.submitStaInfo.usedTime) }
      ]
    };
  },

  methods: {
    // 答题时长转换（最大单位分钟）
    convertTimeStr(val) {
      return convertSecToMS(val, this);
    }
  }
};
</script>
