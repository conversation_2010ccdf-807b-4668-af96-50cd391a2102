<template>
  <div class="exam-error-page">
    <yxt-svg-icon
      icon-class="exam-error"
      width="428px"
      height="253px"
      is-new-svg-url
    />
    <div class="font-size-24 mt24 lh32 color-gray-9 font-bolder">{{ errorMessage }}</div>
    <yxtf-button
      v-if="!deepStudy && !trackId"
      size="larger"
      plain
      class="mt32"
      @click="$emit('customClick')"
    >{{ $t(pageEnum.btnStr) }}</yxtf-button>
  </div>
</template>

<script>
import YxtSvgIcon from './svgIcon.vue';
import { ErrorPageEnum } from 'packages/examing/src/core/enums';

export default {
  name: 'ErrorPage',
  props: {
    pageType: {
      type: Number,
      default: 1
    },
    errorMessage: String,
    deepStudy: Boolean
  },
  components: {
    YxtSvgIcon
  },
  computed: {
    pageEnum() {
      return ErrorPageEnum[this.pageType];
    },

    trackId() {
      return this.$route.query.trackId;
    }
  }
};
</script>
