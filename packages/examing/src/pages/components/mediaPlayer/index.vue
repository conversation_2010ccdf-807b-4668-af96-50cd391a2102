<template>
  <div
    v-if="canPlay"
    class="yxtulcdsdk-media-player-container"
    :style="{
      width: '100%',
      maxWidth: width + 'px',
      height: height+ 'px'
    }"
  >
    <yxtbiz-video
      ref="player"
      class="yxtulcdsdk-media-player"
      :type="playerType"
      :file-id="fileId"
      :options="fileId ? [] : options"
      :starttime="starttime"
      :auto-start="autoStart"
      :width="'100%'"
      :height="'100%'"
      :play-rate="playRate"
      :is-show-watermark="isShowWatermark"
      :is-need-token="isNeedToken"
      :watermark-obj="watermark"
      is-open-mutiple
      :ticket="ticket"
      @onBeforePlay="onBeforePlay"
      @onReady="onReady"
      @onPlay="onPlay"
      @onPause="onPause"
      @onComplete="onComplete"
      @onError="onError"
      @onFullscreen="onFullscreen"
      @onSeek="onSeek"
      @onTime="onTime"
      @onQualityChange="onQualityChange"
      @onPlaybackRate="onPlaybackRate"
    />
  </div>
  <div v-else class="yxtulcdsdk-media-player-container">
    <div class="yxtulcdsdk-media-player yxtulcdsdk-file-transcodeing wp-100">
      <div v-if="!exporting">
        <div
          class="bg-black pr"
          :style="{
            width: '100%',
            maxWidth: width + 'px',
            height: height+ 'px'
          }"
        >
          <div class="transcoding-tips">
            <yxt-svg
              width="14px"
              height="14px"
              icon-class="message-error"
            />
            <span class="ml4">{{ errMsg }}</span>
          </div></div>
        <span v-if="$slots.action" class="player-action-button">
          <slot name="action"></slot>
        </span>
      </div>
      <div
        v-else
        class="yxtulcdsdk-player-fake"
        :style="{
          width: '100%',
          maxWidth: width + 'px',
          height: height+ 'px'
        }"
      >
        <button class="yxtulcdsdk-player-fake__play"></button>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
export default {
  props: {
    type: {
      type: [String, Number],
      default: 'video'
    },
    options: {
      type: Array,
      default: () => []
    },
    starttime: {
      type: Number,
      default: 0
    },
    width: {
      type: String,
      default: '400'
    },
    height: {
      type: String,
      default: '300'
    },
    playRate: {
      type: Boolean,
      default: false
    },
    autoStart: {
      type: Boolean,
      default: false
    },
    isShowWatermark: {
      type: Boolean,
      default: false
    },
    watermark: {
      type: Object,
      default: () => {}
    },
    tranStatus: {
      type: Number,
      default: 0
    },
    playerKey: {
      type: [Number, String],
      default: 0
    },
    fileId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isNeedToken: true,
      needReload: false,
      exporting: this.$route.query.exporting
    };
  },
  computed: {
    ...mapState({
      ticket: state => state.ticket
    }),
    playerType() {
      if (this.type === 2 || this.type === 'audio') {
        return 'audio';
      } else {
        return 'video';
      }
    },
    canPlay() {
      return !this.needReload && this.tranStatus === 2 && !this.exporting;
    },
    errMsg() {
      return this.$t(this.tranStatus === 3 ? 'pc_ote_msg_obj_tanslate_error' : 'pc_ote_msg_obj_translate_coding', [this.$t(this.playerType === 'audio' ? 'pc_ote_msg_audio' : 'pc_ote_msg_vedio')]);
    }
  },
  created() {
  },
  beforeDestroy() {
    this.$refs.player && this.$refs.player.dispose();
    this.clearMediaPlayerOnPlay();
  },
  methods: {
    clearMediaPlayerOnPlay() {
      if (window.__MEDIA_PLAYER_ONPLAY__ === this) {
        window.__MEDIA_PLAYER_ONPLAY__ = null;
      }
    },
    onBeforePlay(event) {
      this.$emit('onBeforePlay', event);
    },
    onReady(event) {
      this.$emit('onReady', event);
    },
    onPlay(event) {
      if (window.__MEDIA_PLAYER_ONPLAY__ && window.__MEDIA_PLAYER_ONPLAY__ !== this) {
        window.__MEDIA_PLAYER_ONPLAY__.$refs.player.pause();
      }
      window.__MEDIA_PLAYER_ONPLAY__ = this;
      this.$emit('onPlay', event);
    },
    onPause(event) {
      this.clearMediaPlayerOnPlay();
      this.$emit('onPause', event);
    },
    onComplete(event) {
      this.clearMediaPlayerOnPlay();
      this.$emit('onComplete', event);
    },
    onError(event) {
      this.clearMediaPlayerOnPlay();
      this.$emit('onError', event);
    },
    onFullscreen(event) {
      this.$emit('onFullscreen', event);
    },
    onSeek(event) {
      this.$emit('onSeek', event);
    },
    onTime(event) {
      this.$emit('onTime', event);
    },
    onQualityChange(event) {
      this.$emit('onQualityChange', event);
    },
    onPlaybackRate(event) {
      this.$emit('onPlaybackRate', event);
    },
    pause() {
      this.$refs.player.pause();
    },
    play() {
      this.$refs.player.play();
    },
    seek(time) {
      this.$refs.player.seek(time);
    },
    stop() {
      this.$refs.player.stop();
    },
    // 销毁播放器
    dispose() {
      this.$refs.player.dispose();
    },
    getPlayer() {
      const player = this.$refs.player.getPlayer();
      return player;
    }
  },
  watch: {
    playerKey() {
      this.needReload = true;
      this.$nextTick(() => {
        this.needReload = false;
      });
    }
  }
};
</script>
