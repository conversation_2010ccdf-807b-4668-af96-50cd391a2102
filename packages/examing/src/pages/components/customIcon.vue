<template>
  <div class="yxtulcdsdk-custom-icon pr" :class="className" :style="{ width, height, color}">
    <span class="yxtulcdsdk-custom-icon__content" :style="{ color }"><slot></slot></span>
    <yxt-svg-icon
      class="pa"
      icon-class="ue-status"
      :width="width"
      :height="height"
      is-new-svg-url
    />
  </div>
</template>
<script>
import { remoteUrls } from '../../configs/const';
import YxtSvgIcon from './svgIcon.vue';
export default {
  name: 'CustomIcon',
  components: {
    YxtSvgIcon
  },
  props: {
    width: {
      default: '72px',
      type: String
    },
    height: {
      default: '72px',
      type: String
    },
    type: {
      default: 'sucess',
      type: String
    },
    remoteUrl: {
      default: '',
      type: String
    },
    className: {
      default: '',
      type: String
    },
    iconColor: {
      default: '',
      type: String
    }
  },
  data() {
    return {};
  },
  computed: {
    url() {
      if (this.remoteUrl) return this.remoteUrl;
      if (this.type === 'error') return remoteUrls[0];
      return remoteUrls[1];
    },
    color() {
      if (this.iconColor) return this.iconColor;
      if (this.type === 'error') return '#FF7875';
      if (this.type === 'warning') return '#FA8C16';
      return '#73D13D';
    }
  }
};
</script>
