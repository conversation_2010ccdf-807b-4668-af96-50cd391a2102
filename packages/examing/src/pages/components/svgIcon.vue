<template>
  <yxt-svg
    :remote-url="mediaUrl"
    :icon-class="iconClass"
    :class="className"
    :width="width"
    :height="height"
  />
</template>

<script>
import { SvgMediaUrl, SvgMediaNewUrl } from '../../configs/const';
export default {
  name: 'YxtSvgIcon',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    className: {
      type: String
    },
    width: {
      type: String
    },
    height: {
      type: String
    },
    // 是否是新的svg地址
    isNewSvgUrl: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    iconName() {
      return `#yxt-otepc-${this.iconClass}`;
    },
    mediaUrl() {
      return this.isNewSvgUrl ? SvgMediaNewUrl : SvgMediaUrl;
    }
  }
};
</script>
