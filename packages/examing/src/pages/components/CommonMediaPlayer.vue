<template>
  <yxtbiz-video
    ref="video"
    :type="type"
    :file-id="fileId"
    :options="fileId ? [] : options"
    :width="width+''"
    :height="height+''"
    :auto-start="autoStart"
    play-rate
    is-open-mutiple
    is-need-token
    @onBeforePlay="onBeforePlay"
    @onQualityChange="onQualityChange"
    @onTime="onTime"
    @onError="onError"
  />
</template>
<script>
export default {
  name: 'CommonMediaPlayer',
  props: {
    type: {
      type: String,
      default: 'video'
    },
    width: {
      type: [String, Number],
      default: '800'
    },
    height: {
      type: [String, Number],
      default: '600'
    },
    autoStart: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => []
    },
    fileId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  destroyed() {
    if (this.$refs.video) this.$refs.video.dispose();
  },
  methods: {
    onBeforePlay() {},
    onQualityChange() {},
    onTime() {},
    onError(error) {
      console.log(error);
    }
  }
};
</script>
