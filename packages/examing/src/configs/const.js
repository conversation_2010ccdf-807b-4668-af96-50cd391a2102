// import { domainBaseUrl } from '@/configs/domain'
export const staticBaseUrl = window.feConfig.common.staticBaseUrl || 'https://stc.yxt.com/';
const u = navigator.userAgent.toLowerCase();
const p = navigator.platform.toLowerCase();
const d = /firefox/.test(u); // isFirefox
const r = /opera|opr\/[\d]+/.test(u); // isOpera
const k = !r && /(msie|trident)/.test(u); // isIE
const e = /edge\/(\d+)/.test(u); // isEdge
const b = !r && !e && /chrome/.test(u) && /webkit/.test(u); // isChrome
const o = !r && !e && !b && /safari/.test(u); // isSafari

export const Browser = {
  // 移动终端浏览器版本信息
  win: p ? /win/.test(p) : /win/.test(u),
  mac: p ? /mac/.test(p) : /mac/.test(u),
  trident: u.indexOf('trident') > -1, // IE内核
  opera: r, // opera
  webKit: u.indexOf('applewebkit') > -1, // 苹果、谷歌内核
  firefox: d, // 火狐
  ie: k,
  ieVersion: '',
  safari: o,
  edge: e,
  chrome: b,
  mobile: !!u.match(/applewebkit.*mobile.*/), // 是否为移动终端
  ios: !!u.match(/\(i[^;]+;?( u;)? cpu.+mac os x/), // ios终端
  android: u.indexOf('android') > -1 || u.indexOf('linux') > -1, // android终端或者uc浏览器
  iPhone: u.indexOf('iphone') > -1 || u.indexOf('mac') > -1, // 是否为iPhone
  iPad: u.indexOf('ipad') > -1, // 是否iPad
  webApp: u.indexOf('safari') === -1, // 是否web应该程序，没有头部与底部
  weixin: !!u.match(/micromessenger/i),
  winwechat: u.indexOf('windowswechat') >= 0, // windows 微信客户端
  dingtalk: u.indexOf('dingtalk') >= 0, // 钉钉
  qqbrowser:
    !u.match(/micromessenger/i) && u.indexOf('mqqbrowser') >= 0 && u.indexOf('yxtapp') < 0, // 手机qq浏览器
  ios9: u.match(/os [9]_\d[_\d]* like mac os x/i),
  qq: u.indexOf('qq/') > -1, // qq壳
  fxiaoke: u.indexOf('fsbrowser') > -1,
  lanxin: u.toLowerCase().indexOf('lanxin') > -1, // 蓝信
  language: window.localStorage.getItem('locale') || navigator.browserLanguage || navigator.language
};

export const Source = 501;

export const config = {
  isDebug: process.env.NODE_ENV === 'development'
};

// 公共CDN资源地址
export const remoteCdn = `${staticBaseUrl}ufd/55a3e0/ote`;
// 老的mediasvg迁移后的地址
export const SvgMediaUrl = remoteCdn + '/pc/other/ote/svg';

// 新的mediasvg地址
export const SvgMediaNewUrl = remoteCdn + '/pc/svg';
// 不想pc&h5都传一份相同文件
export const SvgMediaNewH5Url = remoteCdn + '/h5/svg';

export const cdnMedia = remoteCdn + '/pc/other/';

export const SvgMediaUrlCommon = `${staticBaseUrl}ufd/3f5568/common/pc_backstage/svg`;
export const SvgMediaUrlFrontCommon = `${staticBaseUrl}ufd/3f5568/common/pc_foreground/svg`;

/* ----------------多语言枚举------------- */
// 判断题 正确、错误
export const QUES_JUDGE_CHOICE = ['pc_ote_lbl_wrong', 'pc_ote_lbl_correct'];
// 试题题型 0-单选题 1-多选题 2-判断题 3-填空题 4-问答题 5-组合题
export const QUES_TYPE_NAMES = ['pc_ote_lbl_singlechoice', 'pc_ote_lbl_multiplechoice', 'pc_ote_lbl_judgment', 'pc_ote_lbl_fillin', 'pc_ote_lbl_question', 'pc_ote_lbl_combinatorialquestions'];
// 试题状态 0-已保存 1-已提交 2-已审核 3-不通过
export const QUES_STATUS_NAMES = ['pc_ote_lbl_saved', 'pc_ote_lbl_submitted', 'pc_ote_lbl_audited', 'pc_ote_lbl_notpass'];

// 试题难度 0-易 1-中 2-难
// 试题难度全量 0-易 3-较易 1-中 4-较难 2-难
export const QUES_LEVEL_NAMES = {
  0: 'pc_ote_lbl_easy',
  1: 'pc_ote_lbl_medium',
  2: 'pc_ote_lbl_difficult',
  3: 'pc_ote_lbl_littleeasy',
  4: 'pc_ote_lbl_littledifficult',

  // 实际开启的难易度及其顺序，通常用于筛选等菜单。 全量开启时： 0-易 3-较易 1-中 4-较难 2-难  默认： 0-易 1-中 2-难
  // values: [0, 3, 1, 4, 2],
  values: [0, 1, 2],

  // 全量的难易度，用于展示现有数据的难易度。
  fullValues: [0, 3, 1, 4, 2],
  // 减量的难易度
  simpleValues: [0, 1, 2]
}
QUES_LEVEL_NAMES.values = QUES_LEVEL_NAMES.simpleValues
export const changeLevelType = (all, cb) => {
  const newValues = all ? QUES_LEVEL_NAMES.fullValues : QUES_LEVEL_NAMES.simpleValues
  if (newValues !== QUES_LEVEL_NAMES.values) {
    QUES_LEVEL_NAMES.values = newValues
    cb && typeof (cb) === 'function' && cb()
  }
}

// 试卷状态 0：已保存；1：已提交；2：已审核；3：发布中；4：已发布；5：审核未通过
export const EXAM_STATUS_NAMES = ['pc_ote_lbl_saved', 'pc_ote_lbl_submitted', 'pc_ote_lbl_audited', 'pc_ote_lbl_publishing', 'pc_ote_lbl_announced', 'pc_ote_lbl_didnotpass'];
// 考试安排来源 0-单独安排 1-项目安排 2-课程安排 3-练习 4-人才发展 5-面授工具
export const ARRANGE_SOURCE = ['pc_ote_lbl_arrangetypedefault', 'pc_ote_lbl_arrangetypeproject', 'pc_ote_lbl_arrangetypecourse', '', 'pc_ote_lbl_talentdevelopment', 'pc_ote_lbl_fliptool'];
// 考试安排状态 0-未发布 1-发布中 2-进行中 3-已结束 4-归档中 5-已归档
export const ARRANGE_STATUS_NAMES = ['pc_ote_lbl_unpublished', 'pc_ote_lbl_publishing', 'pc_ote_lbl_progress', 'pc_ote_lbl_ended', 'pc_ote_lbl_archiving', 'pc_ote_lbl_archived'];
// 考试安排类型 0-单场考试 1-循环考试 2-线下考试
export const ARRANGE_TYPE = ['pc_ote_lbl_online_exam', 'pc_ote_lbl_online_loop_exam', 'pc_ote_lbl_offlineExam'];

/* ----------------localstorage、 cookie------------- */
// 本地未提交的考试用时（单位：秒）
export const USER_EXAM_USED_TIME = 'userAnswerUsedTime';
// 提交失败的试题信息
export const USER_EXAM_ANSWER_INFO = 'userAnswerInfo';
// 用户考试试卷试题标记
export const USER_EXAM_QUES_MARK = 'examQuesAnswerMark';
// 用户练习未提交时长
export const USER_PRACTICE_USED_TIME = 'userPracticeUsedTime';

/* ----------------API错误------------- */
// 用户练习，考试 空的错误key，返回我的
export const USER_PACTICE_NONE_KEY = ['apis.ote.pra.validation.NotFound', 'apis.ote.pra.user.examinee.NotFound', 'apis.ote.pra.NoPublish'];
export const USER_EXAM_NONE_KEY = ['apis.ote.arrangement.NotFound', 'apis.ote.examinee.NotFound'];
// 试卷类型 0：题库；1：试题；2：练习卷
export const EXERCISE_TYPE_MAPPER = ['pc_ote_lbl_queslib', 'pc_ote_lbl_paperques', 'pc_ote_lbl_practicejuan'];

// 文件
export const EnumFileType = {
  image: 0, // 图片
  file: 1, // 文档
  video: 2, // 视频
  audio: 3 // 音频
};

// 这里主要用于处理现在入库的数据与组件不一致的问题
// 游客信息收集项 UDP游客组件的key
export const TOURISTINFOCOLLECTOR_CPNTYPES = ['fullname', 'mobile', 'sex', 'email', 'company', 'department', 'userNo', 'position', 'idNo', 'leavingmsg', 'city'];
// 游客信息收集项 考试业务的key
export const TOURISTINFOCOLLECTOR_ALLTYPES = ['name', 'mobile', 'sex', 'email', 'company', 'department', 'userNo', 'position', 'identitycard', 'leavingmsg', 'city'];

// 压缩图片远程地址
export const remoteUrls = [
  `${staticBaseUrl}assets/1f123737/8e994c3f/unpass.png`, // 未通过底图
  `${staticBaseUrl}assets/1f123737/8e994c3f/passed.png` // 已通过底图
];

// 渠道号枚举
export const SOURCE_CODE = {
  WeiXin: 100,
  100: 'WeiXin',
  // 中欧 China Europe Business Online
  CEBO: 101,
  101: 'CEBO',
  // 内容BU
  BU: 102,
  102: 'BU',
  // 线下渠道
  Offline: 103,
  103: 'Offline',
  // 钉钉
  DingTalk: 104,
  104: 'DingTalk',
  // 领带金融
  LingDai: 105,
  105: 'LingDai',
  // 飞书
  FeiShu: 106,
  106: 'FeiShu'
};

// 附件的上传状态
export const UploadStatus = {
  going: 1,
  success: 2,
  fail: 3
};

// 文件转码状态
export const TranscodingStatus = {
  wait: 0, // 等待中
  going: 1, // 进行中
  success: 2, // 已完成
  fail: 3 // 失败
};

// 试卷状态
export const ExamStatus = {
  saved: 0, // 已保存
  submitted: 1, // 已提交
  audited: 2, // 已审核
  published: 4, // 已发布
  notPass: 5 // 未通过
};
