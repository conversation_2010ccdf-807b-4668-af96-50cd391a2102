<script>
import deepContainer from './mixins/deepContainer';

import PracticePreview from './pages/PracticePreview';
import Practicing from './pages/Practicing';
import Wrong from './pages/Wrong';

const PracticeComponentMap = ['PracticePreview', 'Practicing', 'Wrong'];
const PracticeRouterMap = ['StuPracticePreview', 'StuUserPractice', 'StuWrongQuesBookAnswer'];

export default {
  name: 'YxtUlcdSdkPracticing',
  mixins: [
    deepContainer
  ],
  components: {
    PracticePreview,
    Practicing,
    Wrong
  },
  props: {
    // 是否是随堂练习
    coursePractice: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.init(PracticeComponentMap, PracticeRouterMap, 2);
  },
  methods: {
    courseEvent(eventName, info) {
      this.$emit(eventName, info);
    }
  }
};
</script>
