import { o2oApi } from '../../../api';
import { oteApi } from '../core/api';

/**
 * 考前签名
 * @param {string} uemId uemId
 * @param {string} signUrl 签名图片地址
 */
export const saveSign = (uemId, signUrl) => {
  return oteApi.put(`/uem/${uemId}/sign?signUrl=${signUrl}`);
};

/**
 * 获取用户答题答题信息
 * @param {string} uemId uemId
 * @param {object} bodyObj 请求参数
 */
export const getUemStartInfo = (uemId, bodyObj) => {
  return oteApi.get(`/uem/${uemId}/start`, bodyObj);
};

/**
 * 用户做答校验是否补考
 * @param {object} bodyObj 请求参数
 */
export const arrangeAttendCheckMakeup = (bodyObj) => {
  return oteApi.post('/uem/arr/attend/choose', bodyObj);
};

/**
 * 单题提交
 * @param {string} ueId 用户试卷ID
 * @param {object} bodyObj 请求体
 */
export const submitSingleAnswer = (ueId, bodyObj) => {
  return oteApi.put(`/ue/${ueId}/answer`, bodyObj);
};

/**
 * 单题提交-返回体增加切屏信息
 * @param {object} bodyObj 请求体
 */
export const postSingleAnswer = (bodyObj) => {
  return oteApi.post('/ue/single/answer', bodyObj);
};

/**
 * 提交考试用时
 * @param {string} ueId 用户试卷ID
 * @param {object} bodyObj 请求体
 */
export const submitUsedTime = (ueId, bodyObj) => {
  return oteApi.put(`/ue/${ueId}/exam/time`, bodyObj);
};

/**
 * 提交试卷
 * @param {string} ueId 用户试卷ID
 * @param {object} bodyObj 请求体
 */
export const submitPaper = (ueId, bodyObj) => {
  return oteApi.put(`/ue/${ueId}/submit`, bodyObj);
};

/**
 * 保存退出次数
 * @param {string} ueId 用户试卷ID
 * @param {object} bodyObj 请求体
 */
export const saveExitInfo = (ueId, bodyObj) => {
  return oteApi.put(`/ue/${ueId}/exam/exit`, bodyObj);
};

/**
 * 获取考生试卷试题答案（服务器缓存）
 * @param {string} ueId 用户试卷ID
 */
export const getUserAnswerCache = (ueId, orgId) => {
  return oteApi.get(`/ue/${ueId}/answer/result?orgId=${orgId}`);
};

/**
 * 获取考生考试的最新设置
 * @param {string} id 考试
 */
export const getExamNewInfo = (id) => {
  return oteApi.get(`/arrange/${id}/control4Stu`);
};

/**
 * 练习预览
 * @param {string} praId 练习ID
 * @param {string} masterId 第三方考试来源ID
 * @param {string} masterType 第三方考试来源类型
 * @param {string} packageId 课程包ID
 * @param {string} praBatchId 循环练习的批次ID
 */
export const getPraPreview = (praId, masterId = '', masterType = '', packageId = '', praBatchId = '') => {
  return oteApi.get(`/upm/preview?praId=${praId}&masterId=${masterId}&masterType=${masterType}&packageId=${packageId}&praBatchId=${praBatchId}`);
};

/**
 * 练习开始检查
 * @param {string} praId 练习ID
 * @param {object} bodyObj
 */
export const checkPraStart = (praId, bodyObj) => {
  return oteApi.put(`/upm/${praId}/start/check`, bodyObj);
};

/**
 * 练习开始
 * @param {string} praId 练习ID
 * @param {number} usedTime 时长补漏
 * @param {string} praBatchId 循环练习的批次ID
 */
export const getPraStart = (praId, usedTime = 0, praBatchId = '') => {
  return oteApi.get(`/upm/start?praId=${praId}&usedTime=${usedTime}&praBatchId=${praBatchId}`);
};

/**
 * 错题练习开始
 * @param {string} praId 练习ID
 * @param {number} usedTime 时长补漏
 * @param {string} praBatchId 循环练习的批次ID
 */
export const getErrorPraStart = (praId, usedTime = 0, praBatchId = '') => {
  return oteApi.get(`/upm/wrong/start?praId=${praId}&usedTime=${usedTime}&praBatchId=${praBatchId}`);
};

/**
 * 练习单题提交
 * @param {string} praId 练习ID
 * @param {string} puId 练习用户ID
 * @param {object} bodyObj 请求体
 */
export const submitSingleAnswerOfpra = (praId, puId, bodyObj) => {
  return oteApi.put(`/upm/${praId}/users/${puId}/answer`, bodyObj);
};

/**
 * 练习单题提交
 * @param {string} praId 练习ID
 * @param {string} pumId 用户练习ID
 * @param {object} bodyObj 请求体
 */
export const submitSingleErrorAnswerOfpra = (praId, pumId, bodyObj) => {
  return oteApi.put(`/upm/${praId}/users/${pumId}/wrong/answer`, bodyObj);
};

/**
 * 练习退出
 * @param {string} praId 练习ID
 * @param {string} puId 练习用户ID
 * @param {string} uniqueId
 * @param {number} usedTime
 * @param {string} praBatchId 循环练习的批次ID
 */
export const submitPractice = (praId, puId, uniqueId, usedTime = 0, praBatchId = '', trackId = '') => {
  return oteApi.put(`/upm/${praId}/halfway/submit?uniqueId=${uniqueId}&usedTime=${usedTime}${puId ? ('&puId=' + puId) : ''}&praBatchId=${praBatchId}&trackId=${trackId}`);
};

/** 删除用户最新作答中数据(修复试题删除bug)
* @param {string} praId 练习ID
* @returns
*/
export const deleteUserPracticeInfo = (praId) => {
  return oteApi.post(`/upm/deleteLastUserPra/${praId}`);
};

/**
 * 考试预览
 * @param {string} arrId 考试安排ID
 * @param {object} bodyObj 请求参数
 */
export const getExamPreview = async(arrId, bodyObj) => {
  // 拆分权限校验逻辑，减少preview接口响应时间，让后端接口性能能达标...
  try {
    await oteApi.get(`/uem/${arrId}/pre-check/external`, bodyObj);
  } catch (error) {
  }

  return oteApi.get(`/uem/${arrId}/preview`, bodyObj);
};

/**
 * 获取考生考试历史
 * @param {string} uemId
 * @param {object} query
 * @param {string} [query.batchId]
 * @param {string} [query.thirdBatchId]
 */
export const getExamHistory = (uemId, query) => {
  return oteApi.get(`/ue/${uemId}/list`, query);
};

/**
 * 获取用户答卷详情
 * @param {string} ueId 用户试卷ID
 */
export const getUserPaperDetail = (ueId, orgId) => {
  return oteApi.get(`/ue/${ueId}/result/view?orgId=${orgId}`);
};

/**
 * 获取用户试卷提交成功之后的细腻
 * @param {string} ueId 用户试卷ID
 */
export const getSubmitInfo = (ueId, orgId) => {
  return oteApi.get(`/ue/${ueId}/finished?orgId=${orgId}`);
};

/**
 * 获取考试结果统计页面
 * @param {string} ueId 用户试卷ID
 * @param {object} query
 */
export const getExamResultSta = (ueId, query) => {
  return oteApi.get(`/ue/${ueId}/sta`, query);
};

/**
 * 重新考试
 * @param {string} uemId 用户考试关联ID
 * @param {object} body
 */
export const resetExam = (uemId, body) => {
  return oteApi.put(`/uem/${uemId}/resetuem`, body);
};

/**
 * 获取文件下载地址
 * @param {string} ueId 用户试卷ID
 * @param {string} fileId 文件ID
 */
export const getDownloadUrl = (ueId, fileId) => {
  return oteApi.get(`/ue/${ueId}/${fileId}`);
};

/**
 * 获取文件播放地址
 * @param {string} ueId 用户试卷ID
 * @param {string} fileId 文件ID
 */
export const getDocViewUrl = (ueId, fileId) => {
  return oteApi.get(`/ue/${ueId}/${fileId}/play`);
};

/**
 * 获取对应的游客收集配置
 */
export const getTouristSet = (id, orgId) => {
  return oteApi.get(`/arrange/${id}/touristset?orgId=${orgId}`);
};

/**
 * 获取对应的游客收集配置,若不需要密码会直接加入考试
 */
export const getTouristJoin = (id) => {
  return oteApi.get(`/arrange/${id}/tourist-join`);
};

/**
 * 保存游客收集信息，进行游客密码校验
 */
export const putCheckTouristPwd = (arrangeId, pwd) => {
  return oteApi.put(`/arrange/${arrangeId}/tourist-pwd-join?pwd=${pwd || ''}`);
};

// 获取考试安排基本信息（答卷结果统计用）
export const getArrangeInfoStu = (id, query) => {
  return oteApi.get(`/stu/arrange/${id}`, query);
};

/**
 * 推送练习时长
 * @param {object} data
 * @returns
 */
export const postPracticeTime = data => oteApi.put('/upm/practice/time', data);

/**
 * 获取学员简略统计信息
 */
export const getSimpleSta = (puId) => {
  return oteApi.get(`upm/${puId}/simple/sta`);
};

/**
 * 文件中心批量下载附件接口
 */
export const postFileBatchDown = (params) => {
  return oteApi.post('external/file/batch/down', params);
};

/**
 * 获取考核点列表
 * @param {} param
 * @returns
 */
export const getPointSta = ({ ueId, arrangeId, limit, current }) => {
  return oteApi.get(`ue/point/sta?ueId=${ueId}&arrangeId=${arrangeId}&limit=${limit}&current=${current}`);
};

/**
 * 强制再次进入提交练习
 */
export const putSubmitPractice = (puId) => {
  return oteApi.put(`upm/submitUserPra/${puId}/force`);
};

/**
 * 获取考试考前声明内容
 */
export const getDeclareInfo = (arrangeId, orgId) => {
  return oteApi.get(`arrange/${arrangeId}/extend4Stu?orgId=${orgId}`);
};

/**
 * 获取考试关联项目信息
 * @param {string} projectId 考试安排对应的项目ID
 * @param {string} taskId 主考ID
 */
export const getO2OTaskInfo = (projectId, taskId) => {
  return o2oApi.get(`/study/project/current/${projectId}/${taskId}`);
};

/**
 * 导出所选人的答卷
 * @param {object} bodyObj 请求参数
 */
export const postExportStuPaper = (bodyObj) => {
  return oteApi.post('/file/export/ue/answers', bodyObj);
};
