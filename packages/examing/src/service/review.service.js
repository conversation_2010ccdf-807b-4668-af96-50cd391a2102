import { oteApi } from '../core/api';

/**
 * 获取用户试卷（批阅、批阅后的查看入口）
 * @param {string} ueId 用户试卷ID
 * @param {object} bodyObj 请求参数
 * @param {string} querySource 	查询路径：0-默认，1-我的团队
 */
export const getMarkingDetail = (ueId, bodyObj, querySource = 0) => {
  return oteApi.get(`/ue/${ueId}/result/mark/view?querySource=${querySource}`, bodyObj);
};

/**
 * 获取用户试卷（批阅、批阅后的查看入口）（管理员入口）
 * @param {string} ueId 用户试卷ID
 * @param {object} bodyObj 请求参数
 */
export const getMarkingDetailAdmin = (ueId, bodyObj) => {
  return oteApi.get(`/ue/${ueId}/result/mark/admin/view`, bodyObj);
};

/**
 * 获取用户试卷（考试跟踪入口）
 * @param {string} ueId 用户试卷ID
 */
export const getExamPaperAdmin = (ueId) => {
  return oteApi.get(`/ue/${ueId}/result/admin/view`);
};

