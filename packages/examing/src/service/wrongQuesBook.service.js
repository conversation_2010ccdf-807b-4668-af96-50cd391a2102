import { oteApi } from '../core/api';

/** 生成错题考试试卷
 * @param {Object} data
 * @param {1|0} data.buildType - 0-单题模式手动选择创建，1-基于项目创建
 * @param {string[]} [data.wrongQuesIdList] - 错题id列表
 * @param {string} [data.wupId] - 项目id
 * */
export const genWrongExam = (data) => {
  return oteApi.post('/wrong/gen/exam', data);
};

/**
 * 获取考试或者练习能否重做错题
 * 练习新老入口机构参   IsPracticeWrongRemove
 * @param {*} prjId
 * @returns
 */
export const getIsCanRedoWrongs = (prjId) => {
  return oteApi.get('/wrong/entry/show', { prjId });
};

/** 错题本提交后的详情页面
 * @param {string} uaId
 * */
export const getWrongExamAnswerReview = (wupId, uaId) => {
  const wupIdStr = wupId ? `?wupId=${wupId}` : '';
  return oteApi.get(`/wrong/${uaId}/detail${wupIdStr}`);
};

/** 获取虚拟考卷信息
 * @param {string} uaId
 * */
export const getWrongExamInfo = (uaId) => {
  return oteApi.get(`/wrong/exam?uaId=${uaId}`);
};

/** 获取项目下错题列表
 * @param {string} projectId */
export const getWrongProjectQues = (projectId) => {
  return oteApi.get(`/wrong/project/wrong?wupId=${projectId}`);
};

/** 手动删除本次考试里的所有错题
 * @param {string} id
 * @param {'exam' | 'project'} type
 * */
export const removeAllWrongQues = (id, type = 'exam') => {
  if (type === 'project') return oteApi.post(`/wrong/prj/${id}/ques/remove`);
  return oteApi.post(`/wrong/${id}/ques/remove`);
};

/** 手动移除单个错题
 * @param {string[]} ids */
export const removeOneWrongQues = (ids) => {
  return oteApi.post('/wrong/remove', { ids });
};

/** 错题本试卷提交
 * @param {string} uaId
 * @param {Object[]}data
 * */

export const submitWrongExamAnswer = (uaId, data) => {
  return oteApi.post(`/wrong/${uaId}/submit`, data);
};

/** 再做一次
 * @param {string} uaId */
export const wrongQuesDoAgain = (uaId) => {
  return oteApi.post(`/wrong/gen/exam/again?uaId=${uaId}`);
};
