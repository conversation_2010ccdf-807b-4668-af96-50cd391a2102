/**
 * 学员端游客参与考试相关方法
 */
import { USER_EXAM_NONE_KEY, TOURISTINFOCOLLECTOR_CPNTYPES, TOURISTINFOCOLLECTOR_ALLTYPES, config } from '../configs/const';
import { putCheckTouristPwd, getTouristJoin, getTouristSet } from '../service/user.service';
export default {
  data() {
    return {
      collected: this.$route.query.collected || this.$route.query.redo
    };
  },
  created() {
    // 临时处理的代码，后期调查那边改版的需要删除
    const token = window.localStorage.token;
    const surveyToken = window.localStorage.survey_visitor_token;
    if (token && token === surveyToken) {
      window.localStorage.token = '';
      window.localStorage.survey_visitor_token = '';
    }
  },
  computed: {
    /** 判断当前用户是否游客 */
    isTourist() {
      return (window.localStorage.isVisitor === '1') || !localStorage.getItem('token');
    }
  },
  methods: {
    getTouristJoin,
    getTouristSet,
    /**
     * 检测考试相关的信息（扫码主入口）
     */
    checkTestSetting(res, err) {
      if (res) {
        // 游客鉴权失败
        const isTAF = res.canAnswered === 0 && (res.errorKey === 'apis.ote.userexam.tourist.unauthorized' || res.errorKey === 'apis.ote.userexam.tourist.authorized.faild');

        // 发现授权失败,清掉本地的token让他重新登录
        if (isTAF && localStorage.getItem('token')) {
          localStorage.token = '';
        }

        // 判断是否需要收集游客信息（每次扫码都需要）
        if (res.touristType !== 99 && (this.isTourist || !localStorage.token) && (!this.collected || isTAF)) {
          this.goTouristLogin(res);
          return false;
        }
      } else {
        this.handlerPublicError(err, true, () => {}, err && USER_EXAM_NONE_KEY.includes(err.key) && !this.isTourist);
        return false;
      }
      return true;
    },
    /**
     * 获取游客考试相关信息，若无需密码会直接加入考试
     */
    touristJoin(id, cb) {
      // 如果内部用户直接进入考试预览
      if (!this.isTourist) {
        this.$router.replace({
          name: 'StuExamEntry',
          query: { arrangeId: id, collected: 1 }
        });
        return;
      }

      // 游客情况尝试加入考试
      this.getTouristJoin(id).then((res) => {
        if (res.needPwd) {
          cb(res);
        } else {
          this.$router.replace({
            name: 'StuExamEntryTr',
            query: { arrangeId: id, uemId: res.uemId, collected: 1 }
          });
        }
      }).catch((err) => {
        this.checkTestSetting(null, err);
      });
    },
    /**
     * 保存游客收集的信息
     */
    saveTouristInfo(id, pwd) {
      return new Promise((resolve, reject) => {
        putCheckTouristPwd(id, pwd).then(res => {
          this.$router.replace({
            name: 'StuExamEntryTr',
            query: { arrangeId: id, uemId: res.uemId, collected: 1 }
          });
          resolve();
        }).catch((e) => {
          this.handleError(e);
          reject(e);
        });
      });
    },
    /**
     * 前往游客登录
     */
    goTouristLogin(info) {
      const { arrId, arrName, orgId, attendType } = info;
      this.getTouristSet(arrId, orgId || '').then((res) => {
        // 收集完成后的回调地址
        const cbUrl = window.location.origin + window.location.pathname + this.$router.resolve({
          name: 'collectTouristInfor',
          query: {
            collected: 1,
            ...this.$route.query
          }
        }).href;
        // 信息收集项转化

        const vrequired = [];
        const voptional = [];
        for (const key in res) {
          if (Object.hasOwnProperty.call(res, key)) {
            const index = TOURISTINFOCOLLECTOR_ALLTYPES.indexOf(key);
            let type = key;
            if (index >= 0) {
              // 转化组件的key
              type = TOURISTINFOCOLLECTOR_CPNTYPES[index];
            }
            if (res[key] === 1) {
              vrequired.push(type);
            } else if (res[key] === 0) {
              voptional.push(type);
            }
          }
        }

        let url = `/#/visitor?biztype=3&type=mb&pname=${encodeURIComponent(arrName)}&orgid=${orgId || ''}&allemp=${attendType === 99 ? 0 : 1}&vrequired=${vrequired.join(',')}&voptional=${voptional.join(',')}&edit=1&url=${encodeURIComponent(cbUrl)}`;

        window.location.replace(url);
      }).catch((err) => {
        this.checkTestSetting(null, err);
      });
    }
  }
};
