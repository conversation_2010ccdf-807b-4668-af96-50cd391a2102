import { Image } from 'yxt-pc';
import Vue from 'vue';

export default {
  data() {
    return {
      previewImgUrl: ''
    };
  },

  mounted() {
    this.initImgPreviewer();
  },
  methods: {
    initImgPreviewer(remove) {
      const mainEl = this.$el;

      // 移除
      if (remove && mainEl) {
        mainEl.removeEventListener(
          'click',
          this.imgPreviewerHandler
        );
        mainEl.removeAttribute('data-ue-img-preview');

        return;
      }

      // 父元素处理过就不需要处理了
      if (!(mainEl && mainEl.addEventListener &&
            !mainEl.hasAttribute('data-ue-img-preview') &&
            !this.closestEl(mainEl, '[data-ue-img-preview]'))) {
        return;
      }
      // 打标记， 防止重复绑定
      mainEl.setAttribute('data-ue-img-preview', '1');

      // 增加富文本图片点击放大预览的功能
      mainEl.addEventListener(
        'click',
        this.imgPreviewerHandler
      );
    },
    closestEl(el, s) {
      if (el.closest) {
        return el.closest(s);
      }
      do {
        if (el.matches(s)) return el;
        el = el.parentElement || el.parentNode;
      } while (el !== null && el.nodeType === 1);
      return null;
    },
    imgPreviewerHandler(e) {
      const clickedElement = e.target;
      this.pointerEventsIsNone = false;

      if (this.closestEl(clickedElement, '[data-rich-text="1"]')) {
        let src = '';
        // 提取点击的图片地址
        if (clickedElement.tagName === 'IMG') {
          src = clickedElement.src;
        } else if (
          clickedElement.children &&
          clickedElement.children.length > 0
        ) {
          const clickX = e.clientX;
          const clickY = e.clientY;
          // 处理p标签内包裹行级图片，导致click触发对象为p的问题。这里判断是否落点为子元素的p标签
          for (let index = 0; index < clickedElement.children.length; index++) {
            const element = clickedElement.children[index];
            if (element.tagName === 'IMG' && !src) {
              const imgRect = element.getBoundingClientRect();
              if (
                clickX >= imgRect.left &&
                clickX <= imgRect.right &&
                clickY >= imgRect.top &&
                clickY <= imgRect.bottom
              ) {
                src = element.src;
                // 防作弊可能会让图片不可操作，这里预览也需要处理
                this.pointerEventsIsNone = true;
              }
            }
          }
        }
        if (src) {
          this.previewImgUrl = src;
          if (!this.imgPreviewer) {
            this.imgPreviewer = new (Vue.extend(Image))({
              propsData: {
                class: 'd-none',
                src: this.previewImgUrl,
                zIndex: 10000,
                previewSrcList: [this.previewImgUrl]
              }
            });
            this.imgPreviewer.$mount();
            this.imgPreviewer.$el.style.display = 'none';

            try {
              this.imgPreviewer.$refs.imageViewer.addEventListener('contextmenu', (e) => {
                // 禁止右键下载， 直接去掉pointerEvent会影响正常操作
                this.pointerEventsIsNone && e.preventDefault();
              });
            } catch (error) {
            }

            document.body.appendChild(this.imgPreviewer.$el);
          } else {
            this.imgPreviewer.src = this.previewImgUrl;
            this.imgPreviewer.previewSrcList = [this.previewImgUrl];
          }

          this.imgPreviewer.clickHandler();

          e.preventDefault()
        }
      }
    }
  },
  beforeDestroy() {
    this.initImgPreviewer(true);
  }
};
