import axios from 'axios';
import { domainBaseUrl } from '../configs/domain';
import { commonUtil } from 'yxt-biz-pc';
import { postExportStuPaper } from '../service/user.service';

export default {
  data() {
    return {
      exporting: this.$route.query.exporting,
      downloading: false,
      pdfName: ''
    };
  },

  computed: {
    isVisitor() {
      return ~~localStorage.isVisitor;
    }
  },

  methods: {
    // 获取如果是游客的基本信息
    getVisitorStr() {
      if (!this.isVisitor) return '';

      return `&isVisitor=${this.isVisitor}&userId=${localStorage.userId}&orgId=${localStorage.orgId}`;
    },

    // 获取当前语言类型
    getCurrentLangStr() {
      const language = commonUtil.getLanguage();
      return `&yxtLang=${language}`;
    },

    // 如果是游客，处理url和请求接口的名称
    visitorRouteUrl() {
      if (this.isVisitor) {
        return { route: 'generate-pdf', suffixUrl: '' };
      }

      return { route: 'pdf/down', suffixUrl: `&name=${encodeURIComponent(this.pdfName)}&appCode=ote` };
    },

    // 处理游客的导出pdf
    handleVisitorExportPdf(exportUrl) {
      axios.get(exportUrl, {
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/pdf',
          Token: window.localStorage.token
        }
      }).then((res) => {
        const pdfFileName = this.pdfName + '.pdf';
        const blob = new Blob([res.data], { type: 'application/pdf' });
        if (window.navigator && window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveOrOpenBlob(blob, pdfFileName);
        } else {
          window.URL = window.URL || window.webkitURL;
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = pdfFileName;
          link.click();
        }
      }).catch(err => {
        console.log(err);
      }).then(() => {
        this.downloading = false;
      });
    },

    handleExportPdf(domain, urlNow, container) {
      postExportStuPaper({
        pdfApiUrl: `${domain}pdf/down`,
        pdfToken: localStorage.token,
        reqUrl: urlNow,
        name: this.pdfName,
        exportFileType: 1,
        needA4: 1,
        autoContainer: container
      }).then(() => {
        this.$confirm(this.$t('pc_core_lbl_downloadingfile'), this.$t('pc_core_lbl_viewexportfile'), {
          confirmButtonText: this.$t('down_common_btn_view'), // 查看
          cancelButtonText: this.$t('pc_ote_btn_cancel'), // 取消
          type: 'success'
        }).then(() => {
          const href = window.location.origin + '/down/#/download';
          window.open(href, '_blank');
        });
      }).catch(err => {
        console.log(err);
      }).then(() => {
        this.downloading = false;
      });
    },

    exportPdf(url, container) {
      if (this.downloading) {
        return;
      }
      this.downloading = true;
      let urlNow = url || window.location.href;
      const domain = domainBaseUrl('apiBaseUrl') + 'exporttoolpdf/';
      urlNow += `${urlNow.indexOf('?') >= 0 ? '&' : '?'}exporting=1${this.getCurrentLangStr()}${this.getVisitorStr()}`;
      // 获取游客和非游客的路由处理
      const { route, suffixUrl } = this.visitorRouteUrl();
      let exportUrl = `${domain}${route}?url=${encodeURIComponent(urlNow)}${suffixUrl}&needA4=1&autoContainer=${container}`;

      // 游客直接下载，非游客通过下载中心下载
      if (this.isVisitor) {
        this.handleVisitorExportPdf(exportUrl);
      } else {
        this.handleExportPdf(domain, urlNow, container);
      }
    }
  }
};
