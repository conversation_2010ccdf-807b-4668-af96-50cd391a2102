import { getPlayDetail, getFileConvertStatus } from '../service/file.service';
import { getDocViewUrl } from '../service/user.service';
import { mapState } from 'vuex';
import { fileApi } from 'yxt-biz-pc';

export default {
  data() {
    return {
      fileInfo: {},
      showFile: false
    };
  },

  computed: {
    customUeId() {
      return this.ueId || this.$route.query.ueId || '';
    },

    ...mapState(['ticket'])
  },

  methods: {
    viewDetail(fileInfo) {
      if (!fileInfo.fileId) return this.$message.error(this.$t('pc_ote_msg_fileid_null_tips' /* 文件id为空，请确认文件是否上传成功 */));
      if (fileInfo.fileType === 4) return this.$message.error(this.$t('pc_ote_lbl_zip_not_supported') /* 压缩包不支持预览 */);

      // 游客登陆下直接调用播放接口，正常登陆下优先再次获取转码状态再去调用播放接口
      if (~~localStorage.isVisitor) {
        // 0-待转码 1-转码中 2-成功 3-失败
        this.fileStatusTips(fileInfo, fileInfo.status, 1);
        return;
      }

      if (fileInfo.status !== 2) {
        this.getFileStatus(fileInfo);
        return;
      }

      this.playFile(fileInfo);
    },

    // 根据文件转码状态的提示：调用接口再次判断
    fileStatusTips(fileInfo, status, isVisitor) {
      if ((isVisitor && [0, 1].includes(status)) || (!isVisitor && [1, 2].includes(status))) {
        this.$message.warning(this.$t('pc_ote_msg_file_transcoding' /* 文件正在转码中，请稍后重试 */));
      }

      if ((isVisitor && status === 2) || (!isVisitor && status === 0)) {
        this.playFile(fileInfo);
      }

      if (status === 3) {
        this.$message.error(this.$t('pc_ote_msg_file_transcode_fail' /* 文件转码失败 */));
      }
    },

    // 进入页面时未转码成功，需要再次调用确认是否转码完成
    getFileStatus(fileInfo) {
      getFileConvertStatus(fileInfo.fileId).then(res => {
        // 0-成功 1-待转码 2-转码中 3-失败
        const code = res && res.length && res[0].status;
        this.fileStatusTips(fileInfo, code, 0);
      });
    },

    getUrlList(arr) {
      return (arr.length && arr.map(item => {
        return { url: item.url };
      })) || [];
    },

    // 拼接视频的参数
    getVideoOption(fileArr, fileInfo) {
      if (!fileArr || !fileArr.length) return [];

      return fileArr.map(option => {
        return {
          fileFullUrl: option.url,
          fileId: fileInfo.fileId,
          resolution: option.desc
        };
      });
    },

    // 如果转码成功进行调用播放接口播放
    playFile(fileInfo) {
      const api = this.customUeId ? getDocViewUrl(this.customUeId, fileInfo.fileId) : getPlayDetail(fileInfo.fileId);
      api.then((res) => {
        const result = res || [];

        if (result.length) {
          this.fileInfo = {
            fileId: fileInfo.fileId,
            fileType: fileInfo.fileType,
            imageUrl: result[0].url || '',
            lists: fileInfo.fileType === 1
              ? {
                width: JSON.parse(result[0].desc).width || 0,
                height: JSON.parse(result[0].desc).height || 0,
                list: this.getUrlList(result)
              }
              : {},
            videoOptions: [2, 3].includes(fileInfo.fileType) ? this.getVideoOption(res, fileInfo) : []
          };

          this.showFile = true;

          if (this.ticket) {
            fileApi.defaults.headers.ticket = this.ticket;
          }

          this.$nextTick(() => {
            this.$refs.bigViewer && this.$refs.bigViewer.show();
          });
        } else {
          this.$message.error(this.$t('pc_ote_lbl_file_url_empty' /* 文件播放地址为空 */));
        }
      }).catch(this.handleError);
    }
  }
};

