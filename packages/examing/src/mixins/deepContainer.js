import '../core/extend';
import { addResizeListener, removeResizeListener } from 'yxt-pc';
import { FACTOR_FUCTIONS } from '../core/factor';
import { commonUtil } from 'yxt-biz-pc';
import { oteApi, setApiTrackId } from '../core/api';
import YxtSvgIcon from '../pages/components/svgIcon.vue';
import errorPage from '../pages/mixins/errorpage';
// import ueImagePreview from './ueImagePreview'

export default {
  components: {
    YxtSvgIcon
  },
  mixins: [errorPage],
  props: {
    // 参数
    params: {
      type: Object,
      default: () => {}
    },
    // 当前考试的步骤
    step: {
      type: Number,
      default: 0
    },
    // 是否沉浸式
    deepStudy: {
      type: Boolean,
      default: true
    },
    // 是否需要圆角效果
    radius: {
      type: Boolean,
      default: true
    },
    // 是否显示全屏按钮
    fullScreen: {
      type: Boolean,
      default: true
    },
    // 内部滚动的模式
    scrollInner: {
      type: Boolean,
      default: true
    }
  },
  provide() {
    return {
      // 处理css不好计算的高宽问题
      getHeight: () => this.height, // 组件的容器大小
      getWidth: () => this.width, // 组件的容器大小
      getFSWidth: () => this.fsWisth // 浏览器可见区域大小
    };
  },
  data() {
    return {
      exporting: this.$route.query.exporting,
      queryDatas: {},
      height: 0,
      heightOuter: 0,
      width: 0,
      fsWisth: 72,
      isFullscreen: false,
      keyIndex: 1,
      componentMap: [],
      componentName: '',
      routerMap: [],
      pageType: 1
    };
  },
  watch: {
    step: {
      handler: function() {
        this.changeStep(this.step);
      }
    },
    params: {
      handler: function() {
        this.init();
      },
      deep: true
    },
    keyIndex: {
      handler: function() {
        // this.initImgPreviewer(true);
        // this.$nextTick(() => {
        //   this.initImgPreviewer();
        // });
      },
      deep: true
    }
  },
  created() {
    commonUtil.preCheckFunctions(FACTOR_FUCTIONS);
  },
  mounted() {},
  methods: {
    init(cm, rm, pageType = 1) {
      if (cm) this.componentMap = cm;
      if (rm) this.routerMap = rm;
      this.queryDatas = {
        ...this.params
      };
      this.currentStep = this.step;
      this.componentName = this.componentMap[this.currentStep];
      this.keyIndex++;
      this.pageType = pageType;

      this.sizeChangeFun();
      // trackId处理
      if (this.deepStudy) {
        this.params && setApiTrackId('oteApi', this.params.trackId || '');
      } else {
        this.$route.query.trackId &&
          setApiTrackId('oteApi', this.$route.query.trackId || '');
      }
    },

    sizeChangeFun() {
      this.$nextTick(() => {
        if (this.deepStudy) {
          addResizeListener(this.$el, this.setSize);
          this.setSize();
        } else {
          window.addEventListener('resize', this.setSizeInPage);
          this.setSizeInPage();
        }
      });
    },

    changeStep(step, params, replace, newPage) {
      if (step === this.currentStep && this.deepStudy) {
        this.keyIndex++;
        this.sizeChangeFun();
      }
      if (this.deepStudy) {
        // 沉浸式学习组件内切换
        this.currentStep = step;
        this.componentName = this.componentMap[this.currentStep];
        this.queryDatas = params;
      } else {
        // 考试使用还是正常页面的跳转
        const url = this.$router.resolve({
          name: this.routerMap[step],
          query: {
            ...params
          }
        }).href;
        if (newPage) {
          window.open(url);
        } else if (replace) {
          window.location.replace(url);
        } else {
          window.location.href = url;
        }
      }
    },
    setSize() {
      this.height = (this.$el && this.$el.clientHeight) || 0;
      !this.heightOuter && (this.heightOuter = this.height);
      this.width = (this.$el && this.$el.clientWidth) || 0;
      const rfFS = this.$refs.fullScreen;
      this.fsWisth = (rfFS && rfFS.clientWidth) || 72;
    },
    setSizeInPage() {
      const header = document.getElementsByClassName('yxtbiz-nav-top-stu');
      const footer = document.getElementsByClassName('yxtbiz-nav-footer');

      this.height =
        window.innerHeight -
        ((footer && footer[0] && footer[0].offsetHeight) || 0) -
        ((header && header[0] && header[0].offsetHeight + 56) || 0);
      !this.heightOuter && (this.heightOuter = this.height);
      this.width = window.innerWidth;
    },
    changeFullScreen() {
      this.isFullscreen = !this.isFullscreen;
      this.$emit('fullScreen', this.isFullscreen);

      this.$nextTick(() => {
        this.setSize();
      });
    },
    updateProgress(type) {
      this.$emit('updateProgress', type);
    },
    // 提供给使用方，在离开页面时做组件内的二次确认
    confirmLeave(cb) {
      try {
        const pager =
          (this.$refs.refPage && this.$refs.refPage[0]) || this.$refs.refPage;
        if (pager && pager.confirmLeaveStudy) {
          pager.confirmLeaveStudy(cb);
        } else {
          cb && cb(true);
        }
      } catch (error) {
        cb && cb(true);
      }
    },

    renderErrorPage(h) {
      return h('ErrorPage', {
        props: {
          'page-type': this.pageType,
          'error-message': this.errorMessage,
          'deep-study': this.deepStudy
        },
        on: {
          customClick: this.backToList
        }
      });
    },

    renderPage(h) {
      return h(this.componentName, {
        props: {
          'deep-study': this.deepStudy,
          'query-datas': this.queryDatas,
          'course-practice': this.coursePractice
        },
        class: {},
        style: {
          minHeight: (this.scrollInner ? this.height : this.heightOuter) + 'px',
          minWidth: this.deepStudy && this.isFullscreen ? '860px' : undefined
        },
        on: {
          changeStep: this.changeStep,
          updateProgress: this.updateProgress,
          courseEvent: this.courseEvent,
          errorPublic: this.errorPublic
        },
        ref: 'refPage'
      });
    },

    // 用于pdf快速导出切换答卷，组件部分工作
    switchResultTarget(switchConfig) {
      oteApi.defaults.headers.token = switchConfig.token;
    }
  },
  render(h) {
    return (
      <div
        key={this.keyIndex}
        class={{
          'yxtulcdsdk-ulcdsdk yxtulcdsdk-exam-container pr': true,
          'yxtulcdsdk-exam-container--exporting': this.exporting,
          'yxtulcdsdk-exam-container--page': !this.deepStudy,
          'yxtulcdsdk-exam-container--fs': this.isFullscreen,
          'yxtulcdsdk-exam-container--no-radius': !this.radius,
          'yxtulcdsdk-practice-container--course': this.coursePractice
        }}
      >
        {this.isShowErrorPage ? (
          this.renderErrorPage(h)
        ) : this.deepStudy && this.scrollInner ? (
          <yxt-scrollbar is-auto-overscroll={true} fit-height={true}>{this.renderPage(h)}</yxt-scrollbar>
        ) : (
          this.renderPage(h)
        )}

        {// 全屏/退出全屏
          this.deepStudy && !this.coursePractice && this.fullScreen && (
            <div
              ref="fullScreen"
              class="yxtulcdsdk-full-screen"
              onClick={this.changeFullScreen}
            >
              <yxt-svg-icon
                is-new-svg-url
                icon-class={this.isFullscreen ? 'fullscreen-back' : 'fullscreen'}
                width="16px"
                height="16px"
              />
              <span class="ml8">
                {this.$t(
                  this.isFullscreen
                    ? 'pc_ote_btn_exitfullscreen'
                    : 'pc_ote_btn_fullscreen'
                )}
              </span>
            </div>
          )}
      </div>
    );
  },
  beforeDestroy() {
    if (this.deepStudy) {
      removeResizeListener(this.$el, this.setSize);
    } else {
      window.removeEventListener('resize', this.setSizeInPage);
    }
  }
};
