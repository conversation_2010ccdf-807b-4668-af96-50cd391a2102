
import { UserExamStep, UserPracticeStep } from '../core/enums';
import { Message } from 'yxt-pc';
import { getStaticCdnUrl } from '../../../common-util/domain';
import { reportYxtLog } from '../core/utils';
const mediaPath = getStaticCdnUrl().staticBaseUrl;

export default {
  props: {
    // 页面参数
    queryDatas: {
      type: Object,
      required: true
    },
    // 是否沉浸式
    deepStudy: {
      type: Boolean,
      default: true
    },

    // 是否是随堂练习
    coursePractice: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      UserExamStep,
      UserPracticeStep,
      mediaPath
    };
  },
  computed: {
    // 需要根据沉浸式还是正常页面跳转，以不同对应方式获取页面的参数
    routeParams() {
      return this.deepStudy ? this.queryDatas : this.$route.query;
    }
  },
  created() {
  },
  methods: {
    reportYxtLog,
    // 切换页面
    changeStep(step, params, replace, newPage) {
      this.$emit('changeStep', step, params, replace, newPage);
    },
    // 完成考试、练习
    updateProgress(type) {
      this.$emit('updateProgress', type);
    },
    handleError(error) {
      if (error && error.error) {
        // 兼容处理
        error = error.error;
      }
      if (error) {
        if (error.key && error.key.indexOf('global.token') >= 0) {
          return; // 不弹出token错误
        }
        const msg = error.message;
        if (typeof (msg) !== 'string') return;
        Message({
          message: msg,
          type: 'error'
        });
      }
    },

    // 随课练习报错 & 正常页面报错逻辑
    handlerPublicError(err, isRun = true, customFn, customIf) {
      if (this.coursePractice) {
        this.$emit('error', err);
        return;
      }

      if (isRun) {
        this.$emit('errorPublic', err, customFn, customIf);
      } else {
        this.handleError(err);
      }
    },

    // 触发公共的随课练习事件
    handlerCoursePracticeEvent(eventName) {
      this.$emit('courseEvent', eventName, this.courseInfo);
    }
  }
};
