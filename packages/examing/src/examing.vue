<script>
import deepContainer from './mixins/deepContainer';

import ExamPreview from './pages/ExamPreview';
import UserExam from './pages/Examing';
import ExamResult from './pages/ExamResult';
import ExamResultPaper from './pages/ExamResultPaper';
import Wrong from './pages/Wrong';

const ExamComponentMap = ['ExamPreview', 'UserExam', 'ExamResult', 'ExamResultPaper', 'Wrong'];
const ExamRouterMap = ['StuExamPreview', 'StuUserExam', 'examresult', 'StuMyExamResultPaper', 'StuWrongQuesBookAnswer'];

export default {
  name: 'YxtUlcdSdkExaming',
  mixins: [
    deepContainer
  ],
  components: {
    ExamPreview,
    UserExam,
    ExamResult,
    ExamResultPaper,
    Wrong
  },
  data() {
    return {
    };
  },
  watch: {
  },
  created() {
    this.init(ExamComponentMap, ExamRouterMap);
  },
  mounted() {
  },
  methods: {
  },
  beforeDestroy() {
  }
};
</script>
