<template>
  <div>
    <div ref="wrap" class="row_limit_wrap" :style="styleWrap">
      <div ref="content">
        <slot name="content"></slot>
      </div>
    </div>
    <slot name="behind" :over-limit="overLimit">
      <yxt-button
        v-if="overLimit"
        class="mt8"
        type="text"
        @click="expandChanged"
      >{{ $t(expand ? 'pc_o2o_lbl_hidemore' : 'pc_o2o_lbl_showmore') }}</yxt-button>
    </slot>
  </div>
</template>

<script>
import { addResizeListener, removeResizeListener } from 'yxt-pc';

export default {
  props: {
    lineHeight: {
      required: true,
      type: Number,
      default: 22
    },
    row: {
      type: Number,
      validator(v) {
        return /^\d+$/.test(String(v));
      },
      default: 2
    },
    expand: {
      required: true,
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      overLimit: false
    };
  },
  computed: {
    styleWrap() {
      const ellipsis = {
        display: '-webkit-box',
        textOverflow: 'ellipsis',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': this.row,
        overflow: 'hidden'
      };
      return Object.assign({
        maxHeight: !this.expand ? this.maxHeight + 'px' : '',
        wordBreak: 'break-all'
      }, this.overLimit && !this.expand ? ellipsis : {});
    },
    maxHeight() {
      return this.row * this.lineHeight;
    }
  },
  mounted() {
    addResizeListener(this.$refs.content, this.resize);
  },
  methods: {
    resize() {
      const { wrap, content } = this.$refs;
      if (!wrap || !content) {
        return;
      }
      if (wrap.offsetHeight < content.offsetHeight) {
        this.overLimit = true;
        removeResizeListener(this.$refs.content, this.resize);
      } else {
        this.overLimit = false;
      }
    },
    expandChanged() {
      this.$emit('update:expand', !this.expand);
    }
  },
  destroyed() {
    removeResizeListener(this.$refs.content, this.resize);
  }
};
</script>
<style lang='scss' scoped>
.row_limit_behind--mask-gray {
  background: linear-gradient(180deg, rgba(250, 250, 250, 0) 0%, #fafafa 100%);
}
</style>
