export default {
  name: 'buttonTooltip',
  functional: true,
  render(h, context) {
    return context.props.disabled && context.props.content
      ? <yxtf-tooltip
        {...{
          props: {
            content: context.props.content,
            effect: context.props.effect,
            visibleArrow: context.props.visibleArrow,
            placement: 'top'
          }
        }}
      >
        <span class="d-in-block">
          <yxtf-button
            {...context.data}
          >
            {context.children}
          </yxtf-button>
        </span>
      </yxtf-tooltip>
      : <yxtf-button
        {...context.data}
      >
        {context.children}
      </yxtf-button>;
  }
};
