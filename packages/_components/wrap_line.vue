<template>
  <component :is="tag" :style="style">
    <span ref="html" class="break" :style="{whiteSpace: pre ? 'pre-wrap' : ''}"></span><span v-if="innerContent.length < content.length" class="break">...</span>
    <slot v-if="!iconHide" name="right-icon"></slot>
  </component>
</template>

<script>
import { addResizeListener, removeResizeListener } from 'yxt-pc';
export default {
  props: {
    tag: {
      default: 'div',
      type: String
    },
    lineHeight: {
      required: true, // 行高必须
      type: String,
      default: '20px'
    },
    row: {
      type: Number,
      validator(v) {
        return /^\d+$/.test(String(v));
      },
      default: 0
    },
    content: {
      default: '',
      type: String
    },
    hideOnExtend: {
      default: true,
      type: Boolean
    },
    pre: Boolean
  },
  data() {
    return {
      innerContent: this.content
    };
  },
  computed: {
    style() {
      const style = { overflow: 'hidden' };
      style['line-height'] = this.lineHeight;
      if (this.row !== 0) {
        style.maxHeight = this.row * parseFloat(this.lineHeight) + 'px';
      }
      return style;
    },
    maxHeight() {
      return this.row * parseFloat(this.lineHeight);
    },
    iconHide() {
      return (this.row === 0 || this.content === this.innerContent) && this.hideOnExtend;
    }
  },
  mounted() {
    this.$refs.html.innerHTML = this.content;
    this.reCalc();
    addResizeListener(document.body, this.resize);
  },
  methods: {
    resize() {
      this.$refs.html.innerHTML = this.content;
      this.innerContent = this.content;
      this.reCalc();
    },
    reCalc() {
      // 0表示不限制行数
      if (this.row === 0) {
        this.$refs.html.innerHTML = this.content;
        return false;
      }

      if (this.$el.scrollHeight > this.maxHeight + 10) {
        //  防止卡死
        if (this.innerContent.length > (300 * this.row)) {
          this.innerContent = this.innerContent.substr(0, (300 * this.row));
        }
        this.innerContent = this.innerContent.substr(0, this.innerContent.length - 2);
        this.$refs.html.innerHTML = this.innerContent;
      } else {
        return false;
      }

      this.$nextTick(() => {
        this.reCalc();
      });
    }
  },
  watch: {
    content(v) {
      this.innerContent = v;
      this.$refs.html.innerHTML = v;
      this.reCalc();
    },
    row(r) {
      this.innerContent = this.content;
      this.resize();
    }
  },
  destroyed() {
    removeResizeListener(document.body, this.resize);
  }
};
</script>
