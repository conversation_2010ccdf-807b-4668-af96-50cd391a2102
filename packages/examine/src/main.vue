<template>
  <div class="yxtulcdsdk-examine o2oplayframe-task-container yxtulcdsdk-ulcdsdk">
    <div class="pt240 font-size-18 lh26 weight-bold yxtulcdsdk-flex-center-center">考核任务需要跳转到人才发展模块学习，点击后进行跳转</div>
    <div class="mt48 yxtulcdsdk-flex-center-center">
      <yxtf-button size="larger" type="primary" @click="goExamine">立即前往</yxtf-button>
    </div>
  </div>
</template>

<script>
import {goGwnlPage } from 'packages/_utils/core/utils.js';
import { commonUtil } from 'yxt-biz-pc';

const SP_FACTOR = 'talentmanagement';

export default {
  name: 'YxtUlcdSdkExamine',
  props: {
    params: {
      type: String,
      default: {}
    }
  },
  created() {
    commonUtil.preCheckFunctions([SP_FACTOR]);
  },
  methods: {
    goExamine() {
      const originQuery = window.location.href.split('?')[1];
      const OBJ_ENABLED_VALUES = [2, 3, 4]; // 功能可用状态
      let isSp = commonUtil.checkTimeOutFnc(SP_FACTOR);
      isSp = OBJ_ENABLED_VALUES.indexOf(isSp) >= 0;
      goGwnlPage(`web/examine/${this.params.pid}`, { knowledgeId: this.params.targetId, trackId: this.params.trackId, gwnlUrl: window.location.href }, originQuery, true, isSp);
    }
  }
};
</script>
