export const getFilenameAndExtname = filename => {
  if (typeof filename !== 'string') throw new Error('filename must be a string!');
  const lastDotIndex = filename.lastIndexOf('.');
  let prefix = '';
  let suffix = '';
  if (lastDotIndex > -1) {
    prefix = filename.substring(0, lastDotIndex);
    suffix = filename.substring(lastDotIndex + 1).toLowerCase();
  } else {
    prefix = filename;
  }

  return {
    prefix,
    suffix
  };
};

/**
 * 截取文件-取文件的前缀和后缀
 *  */
export const getFilePrefix = (file, index) => {
  if (Object.keys(file).length && file.name.includes('.')) {
    if (index === 0) {
      return file.name.substring(0, file.name.lastIndexOf('.'));
    } else {
      return file.name.substring(file.name.lastIndexOf('.'));
    }
  }
  return file.name;
};
