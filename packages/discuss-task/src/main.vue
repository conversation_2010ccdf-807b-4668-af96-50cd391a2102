<template>
  <div class="yxtulcdsdk-discuss-task yxtulcdsdk-ulcdsdk minh100 pr" :class="{'yxtulcdsdk-discuss-task--radius': radius, 'bg-white yxtulcdsdk-discuss-task-wrap': inner}">
    <div class="center-block pr hline" :class="{'w1200': !inner}">
      <div class="hline" :class="{'center-wrap': inner}">
        <yxtf-tooltip
          placement="top-start"
          trigger="hover"
          show-scroll
          :max-width="560"
          :max-height="168"
          :content="taskData.name"
          open-filter
        >
          <div class="font-size-24 c-26 lh36 break weight-bold ellipsis-2 mb12" :class="{'pt30': inner}">{{ taskData.name }}</div>
        </yxtf-tooltip>
        <wrapLineHtml
          :line-height="22"
          :row="2"
          :expand.sync="contentExpand"
        >
          <div slot="content" class="color-75 font-size-14 text-justify lh22 break">{{ taskData.content || '--' }}</div>
        </wrapLineHtml>
        <yxtbiz-discuss-question
          ref="discussQuestion"
          class="mt40"
          :project-id="pid"
          :user-id="userId"
          :board-id="boardId"
          :target-id="targetId"
          :batch-id="batchId || params.batchId"
          :target-code="params.targetCode"
          :should-tab-change="shouldTabChange"
          :sync-user-process="params.syncUserProcess"
          @on-question-change="handleQuestionChange"
          @on-complete-change="hasCompleted"
        />
        <content-input
          ref="contentInput"
          :target-id="postId"
          :target-code="params.targetCode"
          :discuss-target-id="targetId"
          :is-user-silence="isUserSilence"
          :anonymous="taskData.anonymous === 1"
          :batch-id="batchId || params.batchId"
          :class="attachLength ? 'mt32': 'mt40'"
          @submit="handleSubmit"
        />
        <yxtbiz-discuss-speech
          ref="discurssSpeech"
          class="mt40"
          :allow-anonymous="taskData.anonymous === 1"
          :has-completed="complete"
          :is-user-silence="isUserSilence"
          :board-id="boardId"
          :batch-id="batchId || params.batchId"
          :question-id="postId"
          :target-code="params.targetCode"
          :discuss-target-id="targetId"
          @on-delete="onDelete"
        />
      </div>
    </div>
  </div>
</template>

<script>
import contentInput from './components/content-input.vue';
import mixin from 'packages/_mixins/polling';
import { getUserSilence, getBbsDiscussDetail } from './service';
import wrapLineHtml from 'packages/_components/wrap-line-html';

export default {
  name: 'YxtUlcdSdkDiscussTask',
  mixins: [mixin],
  components: {
    contentInput,
    wrapLineHtml
  },
  props: {
    params: {
      type: Object,
      default: () => ({
        taskId: '',
        pid: '',
        boardId: '',
        targetId: '',
        targetCode: 'o2o',
        syncUserProcess: false
      })
    },
    inner: {
      type: Boolean,
      default: true
    },
    // 是否需要圆角效果
    radius: {
      type: Boolean,
      default: false
    },
    batchId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      taskId: '',
      targetId: '',
      boardId: '',
      taskData: {},
      pid: '',
      postId: '', // 当前q几
      isUserSilence: true,
      bizzType: 0,
      attachLength: 0,
      complete: false,
      contentExpand: false
    };
  },
  watch: {
    params: {
      handler(data) {
        this.taskId = data.taskId;
        this.pid = data.pid;
        this.boardId = data.boardId;
        this.targetId = data.targetCode === 'kng' ? data.targetId : data.pid;
      },
      immediate: true
    }
  },
  created() {
    this.discussDetail();
    this.getUserSilence();
  },
  methods: {
    getUserSilence() {
      getUserSilence().then(({silence})=>{
        this.isUserSilence = silence === 1; // 0是可以发言 1是禁言;
      });
    },
    handleSubmit() {
      this.$refs.discussQuestion && this.$refs.discussQuestion.update(); // 更新状态
      this.$refs.discurssSpeech && this.$refs.discurssSpeech.refresh(true); // 更新数据
      // this.$emit('updateProgress', 2);
    },
    onDelete() {
      this.$refs.discussQuestion && this.$refs.discussQuestion.update(); // 更新状态
      // this.$emit('updateProgress', 2);
    },
    hasCompleted(flag) {
      this.$emit('updateProgress', flag ? 2 : 4);
    },
    discussDetail() {
      getBbsDiscussDetail(this.boardId, {
        boardId: this.boardId,
        targetId: this.targetId,
        targetCode: this.params.targetCode,
        batchId: this.batchId || this.params.batchId,
        projectId: this.pid,
        userCompleted: !!this.params.syncUserProcess
      }).then((res) => {
        this.taskData = res;
      });
    },
    shouldTabChange() {
      return this.$refs.contentInput.change();
    },
    handleQuestionChange(v, complete) {
      console.log('vvvv', v);
      if (complete === 1) {
        this.$emit('initUpdateProgress', 2);
      }
      this.attachLength = v.attachmentList ? v.attachmentList.length : 0;
      this.postId = v.postId;
      this.complete = !!v.complete;
    },
    confirmLeave(cb = (() => {})) {
      try {
        this.leaveCB = cb;
        this.leaveDialog();
      } catch (error) {
        cb && cb(true);
      }
    },
    leaveDialog() {
      if (!this.$refs.contentInput.confirmLeave()) {
        return this.confirmLeaved(true);
      }

      this.$confirm(this.$t('pc_o2o_lbl_cancel_describe').d('当前操作尚未保存'), this.$t('pc_o2o_give_edit_confirm').d('确定放弃此次编辑吗？'), {
        confirmButtonText: this.$t('pc_o2o_btn_done'),
        cancelButtonText: this.$t('pc_o2o_btn_cancel'),
        type: 'warning'
      }).then(() => {
        this.confirmLeaved(true);
      }).catch(() => {
        this.confirmLeaved(false);
      });
    },
    // 离开前的确认后
    confirmLeaved(leave) {
      if (!this.leaveCB) return;
      // 沉浸式的时候告知播放器是否离开
      this.leaveCB(leave);
      this.leaveCB = undefined;

    }
  }
};
</script>
