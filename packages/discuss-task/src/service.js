
import { basebbsApi, ccApi, hwbaseApi, bbsApi, o2oApi } from 'packages/api';

export const discussAreacreateDiscuss = data => {
  return basebbsApi.post('stu/common/comments', data);
};

// 查询用户是否被禁言
export const getUserSilence = () => {
  return ccApi.post('silencewords/users/status', {});
};

/**
 * 获取文件下载 地址
 * @param {String} fileId
 * @returns
 */
export const getFileDownloadUrl = fileId => hwbaseApi.get(`attachment/download?fileId=${fileId}`);

export const getBbsDiscussDetail = (boardId, params) => {
  return bbsApi.post(`bbseng/stu/discuss/info/${boardId}`, params);
};

// 下载文件
export const putThirdUrl = fileId => {
  return o2oApi.put(`third/file/${fileId}`);
};
