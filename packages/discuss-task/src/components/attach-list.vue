<template>
  <div class="o2o-hwk-attachments">
    <ul class="clearfix mr-24">
      <li
        v-for="file of showList"
        :key="file.uuid || file.fileId"
        class="o2o-task-file-wrap pull-left pr o2o-task-file o2o-attach-file"
        :class="inDrawer ? 'o2o-task-file-drawer' : 'mt16 mr16'"
      >
        <!-- 删除按钮 -->
        <i class="o2o-task-file-del right-top mask hand opah0" @click="del(file)">
          <yxtf-svg
            icon-class="delete-1"
            width="14px"
            height="14px"
            class="pa pa-center c-f"
          />
        </i>
        <!-- 上传中 -->
        <div v-if="file.loading" class="o2o-task-file-loading radius4 over-hidden pr">
          <span class="pa pa-center">{{ file.per }} %</span>
        </div>
        <div v-else-if="file.error" class="o2o-task-file-error radius4 over-hidden pr o2o-flex-center-center layout-flex-vertical">
          <yxtf-image class="w24" :src="`${$staticBaseUrl}ufd/b0174a/common/pc/img/load-error.png`" fit="contain" />
          <span class="fail-text mt5 font-size-14 c-fb">{{ $t('pc_o2o_lbl_uploadfail') }}</span>
        </div>
        <div v-else class="o2o-task-file-img text-center radius4 over-hidden pr">
          <i v-if="(file.fileType === 'audio' && file.status !== 1) || (!file.viewUrl && ['video'].includes(file.fileType))" class="wline d-block bg-center hline pr">
            <yxtf-svg
              :icon-class="'icons/f_kng-' + file.fileSubType"
              width="54px"
              height="54px"
              class="pa pa-center"
            />
          </i>
          <template v-else>
            <!-- 使用背景图 -->
            <yxtf-image
              v-if="file.viewUrl"
              :src="file.viewUrl"
              fit="contain"
              class="d-block text-8c bg-f5 wline hline"
            >
              <div slot="placeholder" class="image-slot font-size-12">{{ $t('pc_o2o_lbl_loading') }}</div>
              <div slot="error" class="image-slot font-size-24">
                <i class="yxtf-icon-picture-outline"></i>
              </div>
            </yxtf-image>
            <!-- 音频转码成功后使用背景图 -->
            <i v-if="file.fileType === 'audio' && file.status === 1" class="yxt-o2o-audio-bg d-block wline hline"></i>
          </template>

          <!-- hover 遮罩 -->
          <div v-if="[ 'image','audio', 'video'].includes(file.fileType)" class="layout-flex layout-align-center layout-justify-center pa right-top mask wline hline opah0 opah1">
            <div
              class="h24 layout-flex layout-align-center color-white hand"
              @click="preview(file)"
            >
              <yxtf-tooltip :content="$t('pc_o2o_lbl_preview' /* 预览 */)" placement="top">
                <yxtf-svg
                  icon-class="look"
                  width="16px"
                  height="16px"
                  class="v-mid"
                />
              </yxtf-tooltip>
            </div>
            <div class="ml20 h24 layout-flex layout-align-center color-white hand" @click="download(file)">
              <yxtf-tooltip :content="$t('pc_o2o_lbl_download' /* 下载 */)" placement="top">
                <yxtf-svg
                  icon-class="download"
                  width="16px"
                  height="16px"
                  class="v-mid"
                />
              </yxtf-tooltip>
            </div>

            <!-- <div v-if="(file.status === 1 || file.fileType === 'image')" class="h24 layout-flex layout-align-center color-white hand ml20 lhnormal">
              <yxtbiz-file-join-knglib
                width="16px"
                height="16px"
                :file-id="file.fileId"
                :file-name="getFilePrefix(file, 0)"
                :file-size="file.fileSize"
                :file-ext="getFilePrefix(file, 1)"
              />
            </div> -->
          </div>
        </div>
        <div class="o2o-task-file-name mt8 wline nowrap ellipsis" :class="{'text-center':!file.showConvert}">
          <!-- 转码中显示 loading -->
          <i
            v-if="file.showConvert"
            v-floading="true"
            yxtf-loading-spinner="yxtf-icon-loading"
            class="d-in-block v-mid"
          ></i>
          <yxtf-tooltip
            open-filter
            :content="file.name"
            placement="top"
          >
            <span class="v-mid nowrap ellipsis flex-1 flex-shrink-0" :class="{'ml8' : file.showConvert}">{{ file.name }}</span>
          </yxtf-tooltip>
        </div>
        <div class="font-size-12 yxtf-color-danger o2o-attachment-error text-center ellipsis">
          <span v-if="file.error">
            {{ file.errorMsg }}
          </span>
        </div>
      </li>
    </ul>
    <!-- 展开全部/收起全部 -->
    <OpenCloseAll v-if="list.length > (inDrawer ? 9 : 8)" :open.sync="open" class="mt16 mb8" />
    <yxtbiz-course-player
      :download-cb="(fileId)=>download({fileId})"
      :visible.sync="showCoursePlayer"
      :join-kng="false"
      :file-id-list="fileIdList"
      :start="coursePlayerStart"
    />
    <!-- <yxtbiz-file-join-knglib
      ref="knglib"
      class="d-none"
      :file-id="kngLibFile.fileId"
      :file-name="kngLibFile.fileName"
      :file-size="kngLibFile.fileSize"
      :file-ext="kngLibFile.fileExt"
      :scene="1"
    >
      <span></span>
    </yxtbiz-file-join-knglib> -->
  </div>
</template>

<script>
import OpenCloseAll from './open-close-all.vue';
import { getFilePrefix, getFilenameAndExtname } from '../utils';
import { putThirdUrl } from '../service';

export default {
  name: 'AttachmentList',
  components: { OpenCloseAll },
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    inDrawer: Boolean
  },
  data() {
    return {
      kngLibFile: {
        fileId: '',
        fileName: '',
        fileSize: 0,
        fileExt: ''
      },
      coursePlayerStart: 0,
      showCoursePlayer: false,
      orgId: window.localStorage.getItem('orgId'),
      showImageViewer: false,
      open: false // 是否展开全部
    };
  },
  computed: {
    showList() {
      const isDrawerNumber = this.inDrawer ? 9 : 8;
      return this.open ? this.list : this.list.slice(0, isDrawerNumber);
    },
    fileIdList: ({ list }) => list.map(file => file.fileId)
  },

  methods: {
    getFilePrefix,
    preview(file) {
      const startIndex = this.fileIdList.findIndex(id => id === file.fileId);
      Object.assign(this, {
        coursePlayerStart: startIndex > -1 ? startIndex : 0,
        showCoursePlayer: true
      });
    },
    // 删除上传文件
    del(file) {
      this.$emit('delete', file);
    },
    async download(file) {
      putThirdUrl(file.fileId).then(res => {
        window.location.href = res;
      });
    },
    getFilenameAndExtname: filename => getFilenameAndExtname(filename)
    // addToKngStoreCb(fileId) {
    //   const { name, fileSize } = this.list.find(file => file.fileId === fileId);
    //   const { prefix, suffix } = getFilenameAndExtname(name);
    //   this.kngLibFile = {
    //     fileId,
    //     fileName: prefix,
    //     fileSize,
    //     fileExt: suffix
    //   };
    //   this.$refs.knglib.showDialog();
    // }
  }
};
</script>

<style lang="scss">
.o2o-task-file-wrap {
  &:hover {
    .o2o-task-file-del {
      opacity: 1;
    }
  }

  .yxtf-button--text:hover {
    color: #fff;
  }
}

.o2o-task-file {
  width: 188px;
}

.o2o-task-file-drawer {
  width: calc((100% - 48px) / 3);
}

.o2o-task-file-del {
  z-index: 19;
  width: 20px;
  height: 20px;
  border-radius: 0 4px;
}

.o2o-task-file-loading {
  background-color: rgba(0, 0, 0, 0.01);
}

.o2o-task-file-img,
.o2o-task-file-loading {
  box-sizing: border-box;
  height: 106px;
  line-height: 106px;
  background-color: #f5f5f5;
  border: 1px solid #f5f5f5;
}

.o2o-task-file-error {
  box-sizing: border-box;
  height: 106px;
  line-height: 1;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
}

.o2o-task-file-name {
  display: flex;
  align-items: center;

  i {
    width: 14px;
    height: 14px;
  }

  .yxtf-loading-spinner {
    margin-top: -8px;
  }
}

</style>
<style lang="scss">
.o2o-attachment-error {
  height: 16px;
  line-height: 16px;
}
</style>
