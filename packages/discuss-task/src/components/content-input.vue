<template>
  <div class="content-input wline">
    <div class="flex-mid mb12">
      <h1 class="c-26 font-size-18 font-bolder">{{ $t('pc_ulcd_sdk_lbl_statement').d('我要发言') }}</h1>
      <p class="flex-mid ml12 font-size-14 c-8c">
        {{ $t('pc_ulcd_sdk_lbl_upload_note').d('上传须知') }}
        <yxtf-popover
          trigger="hover"
          placement="top"
        >
          <div class="space-pre-line">{{ $t('pc_ulcd_sdk_lbl_upload_discuss_tip', ['\n']) }}</div>
          <div slot="reference" class="lh-1 ml4 font-size-0">
            <yxt-svg
              class="hand color-gray-6 hover-primary-6"
              icon-class="question-cirlce-o"
              width="16px"
              height="16px"
            />
          </div>
        </yxtf-popover>
      </p>
    </div>
    <yxtf-tooltip placement="top" :content="$t('pc_ulcd_sdk_disabled_content_admin').d('您已被禁言，暂不可发表任何言论。如需取消禁言，请与管理员联系')" :disabled="!isUserSilence">
      <div>
        <yxtf-input
          v-model="content"
          type="textarea"
          :disabled="isUserSilence"
          :placeholder="$t('pc_ulcd_sdk_discuss_placeholder').d('请输入讨论问题的发言内容（5~2000字）')"
          :autosize="{ minRows: 1, maxRows: 8}"
          maxlength="2000"
          show-word-limit
        />
        <!-- 附件上传按钮 -->
        <UploadAttach
          ref="uploadAttach"
          v-model="attachmentsList"
          :file-size="maxUploadFile"
          :disabled="isUserSilence"
          :submit-disabled="getDisabledContent"
          :anonymous="anonymous"
          class="mt18"
          @statement="handleStatement"
        />
      </div>
    </yxtf-tooltip>

    <AttachmentList :list="attachmentsList" @delete="handleDeleteList" />
  </div>
</template>

<script>
import UploadAttach from './upload-attach.vue';
import AttachmentList from './attach-list.vue';
import { discussAreacreateDiscuss } from '../service';
import { Loading } from 'yxt-pc';

export default {
  name: 'ContentInput',
  components: { UploadAttach, AttachmentList },
  data() {
    this.maxUploadFile = 9;
    return {
      content: '',
      attachmentsList: []
    };
  },
  props: {
    anonymous: Boolean,
    isUserSilence: Boolean,
    targetId: {
      type: String,
      default: ''
    },
    // 完成任务来源第三方id,o2o为projectId，kng为targetId
    discussTargetId: {
      type: String,
      default: ''
    },
    targetCode: {
      type: String,
      default: 'o2o'
    },
    batchId: {
      type: String,
      default: ''
    }
  },
  computed: {
    getDisabledContent() {
      const content = this.content.trim();
      return !(content.length >= 5 && content.length <= 2000);
    }
  },
  methods: {
    confirmLeave() {
      return !!(this.content || this.attachmentsList.length);
    },
    change() {
      return new Promise((resolve, reject)=> {
        if (this.content || this.attachmentsList.length) {
          this.$confirm(
            this.$t('pc_ulcd_sdk_content_toggle_question_content').d('切换问题将清空当前未提交的发言内容与附件。'),
            this.$t('pc_ulcd_sdk_content_toggle_question_tip').d('当前发言内容未提交，确定切换问题吗？'),
            {
              confirmButtonText: this.$t('pc_o2o_btn_done'),
              cancelButtonText: this.$t('pc_o2o_btn_cancel'),
              type: 'warning'
            }
          ).then(()=>{
            this.content = '';
            this.attachmentsList = [];
            this.$refs.uploadAttach && this.$refs.uploadAttach.done();
            resolve();
          }, reject);
        } else {
          resolve();
        }
      });
    },
    handleStatement(data) {
      if (data.attachList.some(file => file.loading)) {
        return this.$fmessage.warning(this.$t('pc_o2o_msg_wait_upload' /* 您有附件正在上传，请稍后再试 */));
      }

      const fullLoading = Loading.service({ fullscreen: true, lock: true });
      discussAreacreateDiscuss({
        anonymous: data.anonymity ? 1 : 0,
        content: this.content.trim(),
        attachments: data.attachList.map(v => v.fileId).join(';'),
        targetId: this.targetId,
        targetType: this.targetCode === 'kng' ? 15 : 14, // 14-项目讨论任务,15-课程讨论任务
        targetCode: this.targetCode,
        batchId: this.batchId,
        discussTargetId: this.discussTargetId
      }).then(()=>{
        this.$fmessage.success(this.$t('pc_ulcd_sdk_sub_success').d('提交成功'));
        this.content = '';
        this.attachmentsList = [];
        this.$refs.uploadAttach && this.$refs.uploadAttach.done();
        this.$emit('submit');
      }).finally(()=>{
        fullLoading.close();
      });
    },
    handleDeleteList(fileData) {
      const fileType = fileData.fileType;
      const unit = this.$t(`pc_o2o_lbl_${fileType}_unit`);
      return this.$confirm(
        this.$t('pc_o2o_msg_delfileconfirm', [unit + `${this.$isEnglish ? ' ' : ''}` + this.$t(`pc_o2o_lbl_${fileType}`)]),
        this.$t('pc_o2o_lbl_tip'),
        {
          confirmButtonText: this.$t('pc_o2o_btn_done'),
          cancelButtonText: this.$t('pc_o2o_btn_cancel'),
          type: 'warning'
        }
      ).then(() => {
        // 中断上传
        fileData.abort && fileData.abort();
        this.attachmentsList = this.attachmentsList.filter(file => file.fileId !== fileData.fileId);
      });
    }
  }
};
</script>
