<template>
  <div class="text-center font-size-12 c-59">
    <span class="hand c-43-h" @click="toggleShow">
      <!-- 展开全部/收起全部 -->
      <span class="v-mid mr8">{{ $t(open ? 'pc_o2o_lbl_hidemore' : 'pc_o2o_lbl_showmore') }}</span>
      <yxtf-svg
        :icon-class="open ? 'up' : 'down'"
        width="16px"
        height="16px"
        class="v-mid"
      />
    </span>
  </div>
</template>

<script>
export default {
  props: {
    open: Boolean
  },
  data() {
    return {

    };
  },
  methods: {
    toggleShow() {
      this.$emit('update:open', !this.open);
    }
  }
};
</script>
