<template>
  <div class="flex-mid justify-between">
    <ul>
      <li
        v-for="(act, index) in configs"
        :key="index"
        :class="disabled ? 'notallowed' :'hand'"
        class="lh24 d-in-block mr32"
      >
        <yxtbiz-upload
          :ref="`bizUpload-${act.type}`"
          class="lh24"
          app-code="o2o"
          module-name="o2o"
          function-name="o2o"
          :config-key="act.configKey"
          :filters="act.filters"
          :max-size="act.maxSize"
          multipe
          :on-ready="onReady"
          :files-filter="filesFilter"
          :files-added="addFile"
          :on-progress="onProgress"
          :on-uploaded="onUploaded"
          :on-error="onError"
        >
          <div :class="{'hover-primary-6':!disabled}" class=" text-8c text-no-select" @click.stop="showSelectFile($event, act)">
            <yxt-ulcd-sdk-svg
              :icon-class="'o2o-up-' + act.type"
              class="d-in-block mr4 v-mid"
              width="16px"
              height="16px"
              module="o2o"
              type="other/svg"
            />
            <span :class="{'hover-primary-6':!disabled}" class="v-mid text-59">{{ act.name }}</span>
          </div>
        </yxtbiz-upload>
      </li>
      <li v-if="anonymous" class="lh24 hand d-in-block">
        <yxtf-checkbox v-model="anonymity" :disabled="disabled">{{ $t('pc_ulcd_sdk_lbl_anonymity').d('匿名发言') }}</yxtf-checkbox>
      </li>
    </ul>
    <yxtf-button type="primary" :disabled="disabled || submitDisabled" @click="handleClick">{{ $t('pc_ulcd_sdk_lbl_make_statement').d('发言') }}</yxtf-button>
  </div>
</template>

<script>
export default {
  name: 'UploadAttach',
  data() {
    return {
      attachList: [],
      anonymity: false
    };
  },
  props: {
    anonymous: Boolean,
    value: {
      type: Array,
      default: ()=> []
    },
    fileSize: { // 文件总数
      type: Number,
      default: 9
    },
    disabled: Boolean,
    submitDisabled: Boolean
  },
  watch: {
    value: {
      handler(v) {
        if (Array.isArray(v)) {
          this.attachList = v;
        }
      },
      immediate: true
    }
  },
  computed: {
    configs() {
      const disabledMsg = this.$t('pc_o2o_msg_maxfilesize', [this.fileSize]);

      const isDisabled = (type) => {
        const len = this.attachList.filter(item=>item.fileType === type).length;
        return this.fileSize ? len >= this.fileSize : false;
      };

      return [
        { // 图片
          name: this.$t('pc_o2o_lbl_addpic'),
          type: 'image',
          filters: '.jpg,.jpeg,.gif,.png,.bmp',
          disabled: isDisabled('image'),
          disabledMsg,
          maxSize: 1024 * 10,
          configKey: 'AttachOpenConfigKey'
        },
        { // 视频
          name: this.$t('pc_o2o_lbl_addvideo'),
          type: 'video',
          filters: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.mts',
          disabled: isDisabled('video'),
          disabledMsg,
          maxSize: 1024 * 1024 * 2,
          configKey: 'AttachConfigKey'
        },
        { // 音频
          name: this.$t('pc_o2o_lbl_addaudio'),
          type: 'audio',
          filters: '.w4v,.m4a,.wma,.wav,.mp3,.amr',
          disabled: isDisabled('audio'),
          disabledMsg,
          maxSize: 1024 * 200,
          configKey: 'AttachConfigKey'
        }
      ];

    }
  },
  methods: {
    handleClick() {
      this.$emit('statement', {
        attachList: this.attachList,
        anonymity: this.anonymity
      });
    },
    done() {
      this.anonymity = false;
    },
    showSelectFile(e, btn) {
      if (this.disabled) return;
      if (btn.disabled) {
        return this.$message(btn.disabledMsg);
      }
      e.currentTarget.parentElement.querySelector('input').click();
    },
    onReady() {},
    // 添加到附件列表
    addFile(files) {
      let isSizeError = false;
      console.log('files', files);
      files.forEach((file)=>{
        const len = this.attachList.filter(item=>item.fileType === file.fileType).length;
        if (this.fileSize - len <= 0) {
          file.abort && file.abort();
          isSizeError = true;
          return;
        }
        this.attachList.push({
          uuid: file.uuid,
          fileId: '',
          url: '',
          name: file.name,
          fileSize: file.size,
          fileType: file.fileType,
          fileSubType: this.getFileSubType(file), // 子类型
          duration: 0, // 录音时间
          progress: 0, // 录音播放进度
          playState: 2, // 录音播放状态 1-播放中 2-暂停 3-加载中
          status: file.fileType === 'image' ? 1 : -1, // -1 未发起转码 0转码中 1转码成功 2转码失败
          loading: true,
          per: 0, // 文件上传进度
          abort: null // 方法，调用此方法可以终止文件上传
        });
      });

      this.$emit('input', this.attachList);

      if (isSizeError) {
        this.$message.warning(this.$t('pc_o2o_msg_maxfilesize', [this.fileSize]));
      }
    },
    // 上传前对文件过滤
    // 返回的数组中包含要上传的文件，空数组表示不上传
    filesFilter(files) {
      return files;
    },
    onProgress(file, progress) {
      const per = (progress * 100).toFixed(1);
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (f) {
        f.per = per;
        !f.abort && (f.abort = file.abort);
      }
    },
    // 上传成功 voiceTime-录音时间
    async onUploaded(file, voiceTime) {
      this.uploadCallback && this.uploadCallback();
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (!f) {
        return;
      }
      f.fileId = file.id;
      f.url = file.fullUrl;
      // 图片设置预览地址
      if (file.fileType === 'image') {
        // 获取 image url
        this.$set(f, 'viewUrl', f.url);
      }
      delete file.abort;
      f.loading = false;
    },
    onError(type, file) {
      // 删除附件
      const index = this.attachList.findIndex(item => item.uuid === file.uuid);
      index >= 0 && this.attachList.splice(index, 1);
      // 文件格式错误
      if (type === 'forbidden') {
        return this.$message.error(this.$t('pc_o2o_msg_fileformatincorrect'));
      }
      // 超过文件尺寸
      if (type === 'oversize') {
        const c = this.configs.find(item => item.type === file.fileType);
        return this.$message.error(`${c.name}${this.$t('pc_o2o_lbl_max_support')}${c.maxSize / 1024}M`);
      }
      // 录音时间太短
      if (type === 'tooshort') {
        return this.$message(this.$t('pc_o2o_tip_voicetimetooshort'));
      }
      this.$message.error(this.$t('pc_o2o_lbl_uploadfail'));
    },
    getFileSubType(file) {
      let type = '';
      if (file.fileType === 'audio') {
        type = 'audio';
      } else if (file.fileType === 'zip') {
        type = 'zip';
      } else {
        type = file.fileClass || file.fileType;
      }
      return type;
    }
  }
};
</script>
