import { getHomeworkTemplateDetail } from '../service';

export default {
  watch: {
    'params.preview': {
      handler(v) {
        v && this.getHomeworkPreview();
      },
      immediate: true
    }
  },
  methods: {
    getHomeworkPreview() {
      this.workLoading = true;
      return getHomeworkTemplateDetail(this.params.taskId).then(res => {
        this.handleFormData(res);
      }).finally(() => {
        this.workLoading = false;
      });
    },
    // 详情
    handleFormData(d) {
      this.pid = d.id;
      // 打分方式（0-直接打分 1-是否合格 2-不评分）
      this.scoreType = d.scoreType;
      // 作业完成标准
      this.finishStandard = d.finishStandard;
      // 基础信息
      this.basicinfo = {
        name: d.templateName, // 标题
        totalScore: d.totalScore, // 总分
        passScore: d.passScore, // 通过分数
        description: d.requirement, // 作业要求
        attachments: d.attachments || [] // 附件
      };

      this.multiItem = d.multiItem;

      this.state = 0;

      this.itemCommunionDetails = [];
      d.items && d.items.forEach((item, index) => {
        const hwkContent = {
          itemId: item.id,
          itemName: item.itemName,
          row: 2,
          hideOnExtend: true,
          isOpen: !index,
          itemDescription: item.description,
          attachments: [],
          desc: item.desc,
          homeWorkTypes: item.types || '1,2,3,4,5,6,7'
        };
        this.itemCommunionDetails.push(hwkContent);
      });
    }
  }
};
