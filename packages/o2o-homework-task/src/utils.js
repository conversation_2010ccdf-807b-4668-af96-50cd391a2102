
import Vue from 'vue';
const isServer = Vue.prototype.$isServer;

export const isFirefox = function() {
  return !!window.navigator.userAgent.match(/firefox/i);
};

export function rafThrottle(fn) {
  let locked = false;
  return function(...args) {
    if (locked) return;
    locked = true;
    window.requestAnimationFrame(_ => {
      fn.apply(this, args);
      locked = false;
    });
  };
}

/* istanbul ignore next */
export const on = (function() {
  if (!isServer && document.addEventListener) {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false);
      }
    };
  } else {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler);
      }
    };
  }
})();

/* istanbul ignore next */
export const off = (function() {
  if (!isServer && document.removeEventListener) {
    return function(element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false);
      }
    };
  } else {
    return function(element, event, handler) {
      if (element && event) {
        element.detachEvent('on' + event, handler);
      }
    };
  }
})();

// 格式化时间所用的数字
export const addTime0 = function(n) {
  return n >= 10 ? n : '0' + n;
};
export const VOICE_TIME = 5; // 录音时间（分钟）
export const MIN_VOICE_TIME = 2; // 最小录音时间（秒）

export const isPrivatizationOrg = () => window.feConfig && window.feConfig.orgCode;

export const linkStringToHtml = (prev) => {
  if ((typeof prev !== 'string') || !prev) return '';
  if (prev.indexOf('el-editor-context') > 0) return prev;
  // eslint-disable-next-line no-useless-escape
  const urlReg = /(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?\s{1}/g;
  return prev.replace(urlReg, (str) => {
    // // 去除最后的空格
    // const s = str.substring(0, str.length - 1)
    return `<a href="${str}" target="blank">${str}</a>`;
  });
};

/**
 * 截取文件-取文件的前缀和后缀
 *  */
export const getFilePrefix = (file, index) => {
  if (Object.keys(file).length && file.name.includes('.')) {
    if (index === 0) {
      return file.name.substring(0, file.name.lastIndexOf('.'));
    } else {
      return file.name.substring(file.name.lastIndexOf('.'));
    }
  }
  return file.name;
};

/**
 * 判断图片文件是否存在
 * */
export const checkImgExists = (imgurl) => {
  return new Promise((resolve, reject) => {
    const ImgObj = new Image();
    ImgObj.src = imgurl;
    ImgObj.onload = (res) => {
      resolve(res);
    };
    ImgObj.onerror = (err) => {
      reject(err);
    };
  });
};

/**
 * 格式化上传附件数据
 * @param {*} attachList 附件列表
 * @param {*} pid 项目 id
 * @param {*} type 0-作业 3-批阅
 * @returns Object
 */
export const formatSvFileData = function(attachList, pid, type = 0) {
  return attachList.map((f, i) => {
    let needTransStatus = 1;
    if (['notNeedTrans'].includes(Object.keys(f))) {
      needTransStatus = Number(!f.notNeedTrans);
    }
    return {
      trainingId: pid, // 培训的id
      orderIndex: i, // 排序号
      name: f.name, // 附件的名称
      url: f.url, // 附件保存路径
      fileSize: f.fileSize, // 文件大小
      fileType: f.fileType === 'voice' ? 'audio' : f.fileType, // 文件类型
      fileSubType: ['doc', 'image'].includes(f.fileType)
        ? f.fileSubType
        : f.fileType, // 子类型
      fileId: f.fileId, // 文件id
      type,
      duration: f.duration,
      needTransStatus
      // status: 0
      // viewUrl: ''
    };
  });
};

export const getFilenameAndExtname = filename => {
  if (typeof filename !== 'string') throw new Error('filename must be a string!');
  const lastDotIndex = filename.lastIndexOf('.');
  let prefix = '';
  let suffix = '';
  if (lastDotIndex > -1) {
    prefix = filename.substring(0, lastDotIndex);
    suffix = filename.substring(lastDotIndex + 1).toLowerCase();
  } else {
    prefix = filename;
  }

  return {
    prefix,
    suffix
  };
};
