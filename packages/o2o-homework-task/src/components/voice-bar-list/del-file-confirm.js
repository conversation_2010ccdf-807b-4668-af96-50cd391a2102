// 删除文件提示
export default {
  methods: {
    delConfirm(fileData, list) {
      const fileType = fileData.fileType;
      let unit;
      if (fileType === 'doc') {
        unit = this.$t('pc_o2o_lbl_count');
      } else {
        unit = this.$t(`pc_o2o_lbl_${fileType === 'voice' ? 'audio' : fileType}_unit`);
      }
      // 您确认要删除这条语音吗？
      return this.$confirm(
        this.$t('pc_o2o_msg_delfileconfirm', [unit + `${this.$isEnglish ? ' ' : ''}` + this.$t(`pc_o2o_lbl_${fileType}`)]),
        this.$t('pc_o2o_lbl_tip'),
        {
          confirmButtonText: this.$t('pc_o2o_btn_done'),
          cancelButtonText: this.$t('pc_o2o_btn_cancel'),
          type: 'warning'
        }
      ).then(() => {
        // 中断上传
        fileData.abort && fileData.abort();
        // 终端请求文件数据
        fileData.cancel && fileData.cancel();
        list.splice(list.findIndex(item => item.fileId === fileData.fileId), 1);
        return Promise.resolve('success');
      })
        .catch(() => {
          return Promise.reject();
        });
    }
  }
};
