<!-- 语音条 -->
<template>
  <div>
    <ul class="clearfix">
      <li
        v-for="(item,index) of showList"
        :key="item.fileId"
        :class="{mt16: index !== 0}"
      >
        <VoiceBar
          :file-data="item"
          :factor="factor"
          :is-edit="isEdit"
          :width="width"
          :placement="placement"
          :popper-class="popperClass"
          :service="service"
          @play="togglePlay"
          @del="del"
          @change-time="changeTime"
        />
      </li>
    </ul>
    <!-- 展开全部/收起全部 -->
    <OpenCloseAll
      v-if="list.length > 3"
      :open.sync="open"
      class="mt16"
    />
    <!-- 音频预览 -->
    <audio
      ref="audio"
      :src="src"
      preload="metadata"
      @play="audioPlay"
      @ended="audioEnded"
      @pause="audioPause"
      @error="audioError"
      @timeupdate="audioTimeupdate"
    ></audio>
  </div>
</template>

<script>
import VoiceBar from './voice-bar';
import OpenCloseAll from '../open-close-all.vue';
import delFileConfirm from './del-file-confirm';
import { getFilesDownloadUrl, postThirdUrl } from '../../service';
import axios from 'axios';

export default {
  mixins: [delFileConfirm],
  components: {
    VoiceBar,
    OpenCloseAll
  },
  props: {
    service: {
      type: String,
      default: 'o2o', //  区分 o2o 和 作业引擎的服务
      validator: value => ['o2o', 'hwkEngine'].includes(value)
    },
    isEdit: Boolean,
    delFileSelf: Boolean, // 删除附件，不用外抛事件
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    width: String,
    placement: String,
    popperClass: String,
    factor: {
      type: Object,
      default: () => ({
        transferText: false
      })
    },
    hwState: {
      type: Object,
      default: () => ({ voiceState: '' })
    }
  },
  data() {
    return {
      src: '',
      open: false
    };
  },
  computed: {
    audioCmpt() {
      return this.$refs.audio;
    },
    showList() {
      return this.open ? this.list : this.list.slice(0, 3);
    }
  },
  watch: {
    'hwState.voiceState'(val) {
      this.stopAudio();
    }
  },
  methods: {
    // 切换播放、暂停
    togglePlay(fileData, playState) {
      console.log(fileData, playState, '8888888');
      // 暂停
      if (playState === 2) {
        this.stopAudio(false);
      } else {
        // 暂停续播
        if (this.cacheFileData && this.cacheFileData.fileId === fileData.fileId) {
          fileData.playState = 1;
          this.audioCmpt.play();
        } else {
          this.$emit('voiceState'); // 通知全局停止录音播放
          // voiceState 监听是异步的
          setTimeout(() => {
            this.cacheFileData = fileData; // 缓存
            const setUrl = url => {
              fileData.playUrl = url; // 缓存 url
              this.src = url;
              this.$nextTick(() => {
                fileData.playState = 1; // 播放状态
                this.audioCmpt.play();
              });
            };
            // 请求过 url
            if (fileData.playUrl) {
              setUrl(fileData.playUrl);
              return;
            }
            fileData.playState = 3; // 加载中
            // 终止请求
            this.list.forEach(item => item.cancel && item.cancel());
            // 用于终止其他请求，防止出现请求延迟，导致播放混乱
            const source = axios.CancelToken.source();
            fileData.cancel = source.cancel;
            if (fileData.fileSize === 0) { // fileSize 标志钉钉企业微信上传的录音
              postThirdUrl(fileData.fileId, 'voice', { cancelToken: source.token })
                .then(res => {
                  const datas = res.datas;
                  setUrl(datas[0].url);
                }).catch(e => {
                  fileData.playState = 2;
                }).finally(() => {
                  fileData.cancel = null;
                });
            } else {
              getFilesDownloadUrl(fileData.fileId, { cancelToken: source.token }, 2).then(d => {
                setUrl(d.downloadUrl);
              }).catch(e => {
                fileData.playState = 2;
              }).finally(() => {
                fileData.cancel = null;
              });
            }
          }, 0);
        }
      }
    },
    del(fileData) {
      this.stopAudio();
      if (this.delFileSelf) {
        this.delConfirm(fileData, this.list);
      } else {
        this.$emit('del', fileData);
      }
    },
    // 拖动进度条改变时间
    changeTime(fileData, time) {
      this.cacheFileData = fileData;
      this.audioCmpt.currentTime = time;
    },
    // 音频播放
    audioPlay() {
      this.cacheFileData.playState = 1; // 播放状态
      console.log('statuesss', this.cacheFileData);
    },
    // 音频结束
    audioEnded() {
      this.stopAudio();
    },
    // 音频暂停
    audioPause() {
      this.stopAudio(false);
    },
    // 音频加载失败
    audioError() {
      this.stopAudio();
    },
    // currentTime 表示时间已经改变
    audioTimeupdate(e) {
      this.cacheFileData && (this.cacheFileData.progress = e.target.currentTime);
    },
    // 停止所有音频、录音，clear：是否清除音频地址，暂停时不清除
    stopAudio(clear = true) {
      // 暂停
      this.audioCmpt && this.audioCmpt.pause();
      this.list.forEach(item => { item.playState = 2; });
      if (clear) {
        this.cacheFileData && (this.cacheFileData.progress = 0);
        this.cacheFileData = null; // 清除缓存当前播放文件
        this.src = undefined;
      }
    },
    // 拖动进度条改变时间
    changeAudioTime(fileData, time) {
      this.audioCmpt.currentTime = time;
    }
  }
};
</script>
