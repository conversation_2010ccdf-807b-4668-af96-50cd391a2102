<!-- 语音条 -->
<template>
  <div class="o2o-voice-bar-item pr hand" :style="width ? `width:${width}` : ''">
    <div class="o2o-voice-bar-wrap radius4 wline">
      <div
        slot="reference"
        class="o2o-voice-bar hand border-box radius4 layout-flex layout-align-center ph12"
      >
        <!-- 播放、暂停按钮 -->
        <i
          class="o2o-voice-play-btn bg-primary circle-img text-center pr"
          @click="togglePlay()"
        >
          <!-- 图标 -->
          <yxt-ulcd-sdk-svg
            width="20px"
            height="20px"
            module="o2o"
            type="other/svg"
            :icon-class="svgName"
            class="c-f pa"
            :class="{ 'yxt-icon-loading': fileData.playState === 3 }"
          />
        </i>
        <!-- slider -->
        <div
          ref="slider"
          class="o2o-voice-bar-runway col-flex-1 ml20 mr12 pr hand"
          @click="onSliderClick"
        >
          <i
            class="o2o-voice-bar-value bg-primary d-block"
            :style="{ width: `${progress}%` }"
          ></i>
          <i
            class="o2o-voice-bar-btn pa pa-center-l-v circle-img bg-white"
            :style="{ left: `calc(${progress}% - 5px)` }"
            @mousedown.stop="onButtonDown"
          ></i>
        </div>
        <i class="f-n font-size-12 text-75">{{ playTimeStr || timeStr }}</i>
      </div>
      <!-- 文字 -->
      <yxtf-scrollbar>
        <div v-if="open" class="pt16 pl16  bg-gray-2 pr16 o2o-voice-text lh22 c-59 break">
          {{ text }}
        </div>
      </yxtf-scrollbar>

      <!-- 转换状态 -->
      <div v-if="open" class="mt8 mb16 pl16 pr16 lh20 font-size-12 text-8c">
        <yxtf-svg
          v-if="tranObj.icon"
          width="16px"
          height="16px"
          :icon-class="tranObj.icon"
          class="v-mid"
        />
        <span class="v-mid ml6">{{ tranObj.text }}</span>
        <!-- 重新转换 -->
        <yxtf-link
          v-if="tranState === 2"
          :underline="false"
          class="ml12 font-size-12"
          @click="voice2Text(true)"
        >
          {{ $t('pc_o2o_lbl_transagain') }}
        </yxtf-link>
      </div>
    </div>

    <div class="o2o-voice-trans-action" :class="{'d-block-impt': transIcon}" :style="comVoiceBar">
      <!-- button 转文字 -->
      <yxtf-popover
        v-if="!o2oTransformShow"
        placement="top"
        trigger="hover"
        :effect="true"
        padding-small
        @show="transIcon = true"
        @hide="transIcon = false"
      >
        <span>{{ $t(open ? 'pc_o2o_lbl_closetext' : 'pc_o2o_lbl_transtext') }}</span>
        <span slot="reference" class="o2o-voice-trans-text hand ml8 mt9 text-75 pr" @click="voice2Text()">
          <yxt-ulcd-sdk-svg
            width="16px"
            height="16px"
            module="o2o"
            type="other/svg"
            :icon-class="open? 'o2o-voice-hwk-close-text' : 'o2o-voice-hwk-convert-charactor'"
            class="mt4"
          />
        </span>
      </yxtf-popover>
      <!-- button 删除 -->
      <span v-if="isEdit" class="hand ml4 text-75 o2o-voice-trans-text mt9" @click="del">
        <yxtf-popover
          placement="top"
          trigger="hover"
          :effect="true"
          padding-small
          @show="transIcon = true"
          @hide="transIcon = false"
        >
          <span>{{ $t('pc_o2o_lbl_delete') }}</span>
          <span slot="reference" class="d-in-block">
            <yxtf-svg
              width="16px"
              height="16px"
              icon-class="delete-1"
              class="mt4"
            />
          </span>
        </yxtf-popover>
      </span>
    </div>
  </div>
</template>

<script>
import { addTime0 } from '../../utils';
import { convertVoice2Text } from '../reference-list/diff-o2o-homewokengine';

export default {
  props: {
    service: {
      type: String,
      default: 'o2o', //  区分 o2o 和 作业引擎的服务
      validator: value => ['o2o', 'hwkEngine'].includes(value)
    },
    fileData: {
      type: Object,
      default() {
        return {};
      }
    },
    isEdit: Boolean,
    width: String,
    placement: {
      type: String,
      default: 'right-start'
    },
    popperClass: String,
    factor: {
      type: Object,
      default: ()=>({
        transferText: false
      })
    }
  },
  data() {
    return {
      visible: false,
      progress: 0,
      open: false, // 转文字是否展开
      tranState: 0, // 0-转换中 1-成功 2-失败
      text: '', // 转换文字
      playTimeStr: '', // 正在播放时间
      transIcon: false
    };
  },
  computed: {
    // 转文字页面的展示
    o2oTransformShow() {
      return this.factor.transferText;
    },
    timeStr() {
      return this.formatTime(this.fileData.duration);
    },
    svgName() {
      switch (this.fileData.playState) {
        case 1: // 播放
          return 'o2o-voice-playing';
        case 2: // 暂停
          return 'o2o-voice-stop';
        case 3: // 缓冲
        default:
          return 'o2o-voice-loading';
      }
    },
    disabled() {
      return this.fileData.playState !== 1;
    },
    tranObj() {
      switch (this.tranState) {
        case 0:
          return { text: this.$t('pc_o2o_lbl_traning') }; // 转换中
        case 1:
          return { text: this.$t('pc_o2o_lbl_transuccess'), icon: 'ok' }; // 转换完成
        case 2:
          return { text: this.$t('pc_o2o_lbl_tranfail'), icon: 'delete-0' }; // 转换失败
        default:
          return {};
      }
    },

    comVoiceBar() {
      return { 'inset-inline-end': this.isEdit && !this.o2oTransformShow ? '-62px' : '-30px' };
    }
  },
  watch: {
    // 播放进度改变
    'fileData.progress'(val) {
      // 计算播放进度
      const progress = (val / this.fileData.duration) * 100;
      this.progress = progress >= 100 ? 100 : progress;
      // 实时播放时间
      this.playTimeStr = val ? this.formatTime(val) : '';
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resetSize);
  },
  methods: {
    // 格式化时间
    formatTime(time) {
      const minute = parseInt(time / 60);
      const secode = parseInt(time - minute * 60);
      return `${addTime0(minute)}:${addTime0(secode)}`;
    },
    // 切换播放状态 1-播放中 2-暂停 3-加载中
    togglePlay(state) {
      this.$emit(
        'play',
        this.fileData,
        state || (this.fileData.playState === 2 ? 1 : 2)
      );
    },
    // 语音转文字
    voice2Text(isRelod) {
      // 当前展开、不是点击失败重新加载
      if (this.open && !isRelod) {
        this.open = false;
        return;
      }
      this.open = true;
      this.togglePlay(2);
      // 缓存过文字
      if (this.fileData.text) {
        this.tranState = 1;
        this.text = this.fileData.text;
        return;
      }
      this.tranState = 0;
      convertVoice2Text(this.service, this.fileData.fileId)
        .then(res => {
          this.tranState = 1;
          this.text = res.data.map(item => item.onebest).join('');
          this.fileData.text = this.text; // 缓存文字
        })
        .catch(e => {
          this.tranState = 2;
        });
    },
    // 删除
    del() {
      this.$emit('del', this.fileData);
    },
    // 计算轨道宽度
    resetSize() {
      const slider = this.$refs.slider;
      if (slider) {
        this.sliderWidth = slider.clientWidth;
        this.sliderOffsetLeft = slider.getBoundingClientRect().left;
      }
    },
    // 在 slider 上点击
    onSliderClick(event) {
      if (this.disabled || this.dragging) return;
      this.resetSize();
      const sliderOffsetLeft = this.$refs.slider.getBoundingClientRect().left;
      this.setPosition(event.clientX - sliderOffsetLeft, true);
      this.$emit(
        'change-time',
        this.fileData,
        (this.progress / 100) * this.fileData.duration
      );
    },
    // 滑块按钮 mousedown
    onButtonDown(event) {
      if (this.disabled) return;
      // 记录当前状态
      this.currentState = this.fileData.playState;
      // 当前状态不是暂停，先暂停播放
      this.currentState !== 2 && this.togglePlay();
      event.preventDefault();
      this.resetSize();
      this.onDragStart(event);
      window.addEventListener('mousemove', this.onDragging);
      window.addEventListener('mouseup', this.onDragEnd);
      window.addEventListener('contextmenu', this.onDragEnd);
    },
    onDragStart(event) {
      this.dragging = true;
      this.isClick = true;
      this.startX = event.clientX;
      this.newPosition = this.progress; // 保存当前进度，用于拖拽结束时计算
    },
    onDragging(event) {
      if (this.dragging) {
        this.isClick = false;
        this.setPosition(event.clientX - this.startX);
      }
    },
    onDragEnd() {
      if (this.dragging) {
        /*
         * 防止在 mouseup 后立即触发 click，导致滑块有几率产生一小段位移
         * 不使用 preventDefault 是因为 mouseup 和 click 没有注册在同一个 DOM 上
         */
        setTimeout(() => {
          this.dragging = false;
          if (!this.isClick) {
            this.newPosition = this.progress;
          }
        }, 0);
        this.$emit(
          'change-time',
          this.fileData,
          (this.progress / 100) * this.fileData.duration
        );
        // 当前状态不是暂停，继续播放
        this.currentState !== 2 && this.togglePlay();

        window.removeEventListener('mousemove', this.onDragging);
        window.removeEventListener('mouseup', this.onDragEnd);
        window.removeEventListener('contextmenu', this.onDragEnd);
      }
    },
    // useDiff 只使用偏移量
    setPosition(diff, useDiff) {
      let p = (useDiff ? 0 : this.newPosition) + (diff / this.sliderWidth) * 100;
      if (p < 0) {
        p = 0;
      } else if (p > 100) {
        p = 100;
      }
      this.progress = p;
    }
  }
};
</script>

<style lang="scss">
.o2o-voice-bar-wrap {
  width: 596px;
  background-color: #f5f5f5;
  border: 1px solid #f5f5f5;
}

.o2o-voice-bar {
  height: 40px;
}

.o2o-voice-play-btn {
  width: 24px;
  height: 24px;

  svg {
    top: 50%;
    left: 50%;
    margin-top: -10px;
    margin-left: -10px;
  }
}

.o2o-voice-bar-runway,
.o2o-voice-bar-value {
  height: 6px;
  border-radius: 8px;
}

.o2o-voice-bar-runway {
  background-color: #e9e9e9;
}

.o2o-voice-bar-value {
  width: 0%;
}

.o2o-voice-bar-btn {
  box-sizing: border-box;
  width: 10px;
  height: 10px;
  border: 1px solid #f5f5f5;

  &:hover {
    border: 1px solid var(--color-primary);
  }
}

.o2o-voice-text {
  max-height: 66px;
}
</style>
