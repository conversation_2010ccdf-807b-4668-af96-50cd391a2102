/*
 * @FileDescription: reference mixin
 * @Author: 刘凯
 * @Date: 2022-02-25 14:49:54
 * @LastEditors: 刘凯
 * @LastEditTime: 2022-02-25 14:50:57
 */

const fileIconMap = {
  pdf: 'icons/f_kng-pdf',
  'ppt,pptx': 'icons/f_kng-ppt',
  'doc,docx': 'icons/f_kng-word',
  'xls,xlsx': 'icons/f_kng-excel',
  'jpg,jpeg,png,gif': 'icons/f_kng-img',
  'zip,rar': 'icons/f_kng-zip',
  'wmv,mp4,flv,avi,rmvb,mpg,mkv,mov,mts': 'icons/f_kng-video',
  'w4v,m4a,wma,wav,mp3,amr': 'icons/f_kng-audio'
};

export default {
  props: {
    file: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: true
    };
  },
  computed: {
    fileName() {
      const { name } = this.file;
      const RegEx = /(.*)(\.[^.]+)$/;
      const [, mainName, extName] = (name || '').match(RegEx);
      return {
        mainName,
        extName
      };
    },
    transcodeResult() {
      const { transcode } = this.file;
      switch (transcode) {
        case 'transcoding':
          return this.$t('pc_o2o_lbl_transcoding'); // 转码中
        case 'failed':
          return this.$t('pc_o2o_lbl_transcodefail'); // '转码失败'
        default: return '';
      }
    },

    fileSvg() {
      const RegEx = /\.([^.]+)$/;
      const extendName = this.file.name.match(RegEx)[1].toLowerCase();
      const matchedKey = Object.keys(fileIconMap).find(key => key.split(',').includes(extendName)) || '';
      return fileIconMap[matchedKey];
    }
  }
};
