<template>
  <div :style="{height: height}" class="o2o-hwk-reference hand layout-flex layout-align-center text-no-select ph12" @click="$emit('preview',file)">
    <div class="layout-flex layout-align-center flex-1 over-hidden">
      <div class="o2o-hwk-reference__filename-wrap layout-flex layout-align-center over-hidden">
        <yxtf-svg
          class="flex-shrink-0 o2o-hwk-reference__prefix"
          :icon-class="fileSvg"
          width="24px"
          height="24px"
        />
        <div class="o2o-hwk-reference__filename color-gray-10 layout-flex layout-align-center ml4 mr8 lh22 font-size-14 over-hidden color-gray-10">
          <div class="ellipsis">{{ fileName.mainName }}</div>
          <div class="flex-shrink-0">{{ fileName.extName }}</div>
        </div>
      </div>
      <div class="color-gray-7 lh20 font-size-12 flex-shrink-0">{{ file.fileSize | formatFileSize('size') }}</div>
      <div v-if="file.transcode === 'checking' || transcodeResult" class="ml8 layout-flex layout-align-center flex-shrink-0">
        <Loading v-if="file.transcode === 'checking'" />
        <span v-else class="lh18 c-bf font-size-12">{{ transcodeResult }}</span>
      </div>
    </div>

    <div v-if="!preview" class="p4 ml10 layout-flex o2o-hwk-reference__download radius4">
      <yxtf-tooltip :content="$t('pc_o2o_lbl_download' /* 下载 */)" placement="top">
        <yxt-ulcd-sdk-svg
          class="flex-shrink-0 color-gray-7"
          icon-class="o2o-hwk-reference-download"
          width="16px"
          height="16px"
          type="other/svg"
          module="o2o"
          @click.native.stop="$emit('download',file)"
        />
      </yxtf-tooltip>
    </div>

    <!-- 文件的转码状态为转码成功 || 文件类型为image scene 记录神策 为2是后台 1是前台-->
    <yxtbiz-file-join-knglib
      v-if="(file.status === 1 || file.fileType === 'image') && !preview"
      class="o2o-hwk-reference__download color-gray-7 o2o-hwk-reference__download ml8 p4 radius4"
      width="16px"
      height="16px"
      :file-id="file.fileId"
      :file-name="fileName.mainName"
      :file-size="file.fileSize"
      :file-ext="'.' + fileName.extName"
    />
  </div>
</template>

<script>
import Loading from '../loading.vue';
import referencMixin from './reference-mixin';

export default {
  mixins: [referencMixin],
  props: {
    height: {
      type: String,
      default: '40px'
    },
    preview: Boolean
  },
  components: {
    Loading
  },
  filters: {
    formatFileSize: function(value, form) {
      if (!value) {
        if (form) {
          return '0 Bytes';
        } else {
          return ['0 Bytes', 0, ' Bytes'];
        }
      }

      const unitArr = [' Bytes', ' KB', ' MB', ' GB', ' TB'];
      let index = 0;
      const srcsize = parseFloat(value);
      index = Math.floor(Math.log(srcsize) / Math.log(1024));
      let size = srcsize / Math.pow(1024, index);
      size = size.toFixed(2); // 保留的小数位数
      if (form === 'size') {
        return size + unitArr[index];
      } else {
        return [size + unitArr[index], size, unitArr[index]];
      }
    }
  }
};
</script>
