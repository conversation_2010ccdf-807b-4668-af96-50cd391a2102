<template>
  <div>
    <div class="pr24">
      <yxtf-tooltip
        placement="top-start"
        trigger="hover"
        show-scroll
        :max-width="560"
        :max-height="168"
        :content="data.name"
        open-filter
      >
        <div class="font-size-32 lh44 break weight-bold ellipsis-2">
          {{ data.name }}
        </div>
      </yxtf-tooltip>
      <yxtf-row class="mt24 mb12 color-lightgrey">
        <!-- 评分模式 -->
        <template v-if="scoreType===0 && needRemarkStatus === 1">
          <yxtf-col v-if="data.totalScore" :span="8">
            <span>{{ $t('pc_o2o_lbl_hwkTotalScore' /* 作业总分 */) }}：</span>
            <span class="color-gray-10">{{ data.totalScore }}</span>
          </yxtf-col>
          <yxtf-col v-if="data.passScore" :span="8">
            <span>{{ $t('pc_o2o_lbl_passscore2') }}：</span>
            <span class="color-gray-10">{{ data.passScore }}</span>
          </yxtf-col>
        </template>
        <yxtf-col v-if="data.projectName" :span="(scoreType===0 && needRemarkStatus === 1) ? 8 : 24" class="ellipsis nowrap">
          <span>{{ $t('pc_o2o_lbl_projectname') }}：</span>
          <yxtf-tooltip :content="data.projectName" placement="top-start">
            <span class="color-gray-10">{{ data.projectName }}</span>
          </yxtf-tooltip>
        </yxtf-col>
      </yxtf-row>
      <yxtf-row v-if="data.startTime && data.endTime" class="mb12 color-lightgrey">
        <yxtf-col :span="8">
          <span>{{ $t('pc_o2o_lbl_starttime') }}：</span>
          <span class="color-gray-10">{{ data.startTime | formatTime }}</span>
        </yxtf-col>
        <yxtf-col :span="8">
          <span>{{ $t('pc_o2o_lbl_endtime') }}：</span>
          <span class="color-gray-10">{{ data.endTime | formatTime }}</span>
        </yxtf-col>
      </yxtf-row>
      <div class="color-lightgrey layout-flex">
        <span class="nowrap lh22">{{ $t('pc_o2o_lbl_hwrequire') }}：</span>
        <div class="text-pre break lh22 color-gray-10" v-html="data.description"></div>
      </div>
      <div v-if="data.hwTips" class="color-lightgrey layout-flex mt12">
        <span class="nowrap lh22">{{ $t('pc_o2o_lbl_hwtipcontent') }}：</span>
        <div class="text-pre break lh22 color-gray-10" v-text="data.hwTips"></div>
      </div>
    </div>
    <div v-if="data.attachments.length" class="color-lightgrey layout-flex layout-flex-vertical">
      <div class="lh22 color-75 font-size-14 flex-shrink-0 mt12">{{ $t('pc_o2o_lbl_attachmentfile') }}：</div>
      <ReferenceList
        class="flex-1 mt4"
        :list="data.attachments"
        :preview="preview"
        @voiceState="$emit('voiceState')"
      />
    </div>
  </div>
</template>

<script>
import ReferenceList from './reference-list/reference-list.vue';
export default {
  name: 'Basicinfo',
  components: {
    ReferenceList
  },
  props: {
    data: {
      type: Object,
      default() {
        return {};
      }
    },
    preview: Boolean,
    scoreType: Number,
    needRemarkStatus: Number
  },
  methods: {
    download(file) {}
  }
};
</script>
