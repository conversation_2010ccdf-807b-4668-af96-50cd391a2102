<!--
  * @FileDescription: 作业附件上传
 * @Author: wh
 * @Date: 2020-11-02 10:51:45
 * @LastEditors: 刘凯
 * @LastEditTime: 2022-03-01 17:27:29
 -->
<template>
  <ul>
    <li
      v-for="(act, index) in configs"
      :key="index"
      :class="{'mr32': index < configs.length - 1}"
      class="lh24 hand d-in-block"
    >
      <!-- 录音 -->
      <RecordBtn
        v-if="act.type === 'voice'"
        :config="act"
        :disabled="disabled"
        app-code="hw"
        @success="onUploaded"
      />
      <div
        v-else-if="disabled"
        class="text-8c text-no-select opa7 not-allowed"
      >
        <yxt-ulcd-sdk-svg
          :icon-class="'o2o-up-' + act.type"
          module="o2o"
          type="other/svg"
          class="d-in-block mr4 v-mid"
          width="16px"
          height="16px"
        />
        <span class="text-59">{{ act.name }}</span>
      </div>
      <yxtbiz-upload
        v-else
        :ref="`bizUpload-${act.type}`"
        class="lh24"
        app-code="hw"
        module-name="o2o"
        function-name="o2o"
        version="v2"
        :trans-callback-url="transCallbackUrl"
        :scene-code="act.sceneCode || 'hw'"
        :config-key="act.configKey"
        :filters="act.filters"
        :max-size="act.maxSize"
        :multipe="!isAiHw"
        :drag="act.type === 'doc' && isAiHw"
        :on-ready="onReady"
        :files-filter="filesFilter"
        :files-added="addFile"
        :on-progress="onProgress"
        :on-uploaded="onUploaded"
        :on-error="onError"
      >
        <div class="hover-primary-6 text-8c text-no-select" @click.stop.native="showSelectFile($event, act)">
          <div v-if="act.type === 'doc' && isAiHw" class="yxtulcdsdk-hw-ai__upload">
            <yxt-ulcd-sdk-svg
              width="40px"
              height="40px"
              module="o2o"
              icon-class="o2o-hw-ai-upload"
            />
            <div class="color-gray-10 lh22 mt8">{{ $t('pc_ulcdsdk_lbl_drag_or_click_to_select_file' /* 拖拽或点击此处选择文件 */) }}</div>
            <div class="font-size-12 color-gray-7 lh20 mt4">{{ $t('pc_ulcdsdk_lbl_upload_limit_notice' /* 请上传5页以内的文本文档/文件，最多上传1个附件 */) }}</div>
          </div>
          <template v-else>
            <yxt-ulcd-sdk-svg
              :icon-class="'o2o-up-' + act.type"
              class="d-in-block mr4 v-mid"
              width="16px"
              height="16px"
              module="o2o"
              type="other/svg"
            />
            <span class="v-mid text-59 hover-primary-6">{{ act.name }}</span>
          </template>
        </div>
      </yxtbiz-upload>
    </li>
  </ul>
</template>

<script>
import RecordBtn from '../record-btn';
import fileUploadSuccess from './file-upload-success';
export default {
  name: 'Attachlist',
  mixins: [fileUploadSuccess],
  components: {
    RecordBtn
  },
  data() {
    return {};
  },
  props: {
    attachList: {
      type: Array,
      default() {
        return [];
      }
    },
    disabled: Boolean,
    fileSize: { // 文件总数
      type: Number,
      default: 30
    },
    voiceSize: { // 录音数量
      type: Number,
      default: 10
    },
    type: String, // '1,2...' 1 图片 2 文字 3 文档 4 视频 5 语音 6 音频
    isAiHw: Boolean // 是否是ai作业
  },
  computed: {
    transCallbackUrl() {
      return window.feConfig.common.apiBaseUrl + 'external/file/trans/callback';
    }
  },
  methods: {
    showSelectFile(e, btn) {
      if (btn.disabled) {
        return this.$message(btn.disabledMsg);
      }
      e.currentTarget.parentElement.querySelector('input').click();
    }
  }
};
</script>
