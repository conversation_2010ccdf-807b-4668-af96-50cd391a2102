// 文件处理
export default {
  computed: {
    configs() {
      // 根据文件限制数量，设置按钮状态
      const disabled = this.fileSize ? (this.isAiHw ? this.attachList.length === 1 : this.attachList.length >= this.fileSize) : false;
      // 录音是先上传，再加入文件列表
      const voiceDisabled = this.voiceSize ? this.attachList.filter(f => f.fileType === 'voice').length >= this.voiceSize : false;
      // 最多可上传30个文件
      const disabledMsg = this.$t('pc_o2o_msg_maxfilesize', [this.fileSize]);
      // 最多录制10条，无法继续录制
      const voiceDisabledMsg = this.$t('pc_o2o_msg_maxvoicesize', [this.voiceSize]);
      const showIcon = (t) => {
        return this.type.includes(t);
      };
      return [
        { // 图片
          name: this.$t('pc_o2o_lbl_addpic'),
          type: 'image',
          filters: '.jpg,.jpeg,.gif,.png,.bmp,.ico',
          disabled,
          disabledMsg,
          show: showIcon('1'),
          maxSize: 1024 * 10,
          configKey: 'AttachOpenConfigKey'
        },
        { // 视频
          name: this.$t('pc_o2o_lbl_addvideo'),
          type: 'video',
          filters: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov',
          disabled,
          disabledMsg,
          show: showIcon('4'),
          maxSize: 1024 * 1024 * 2,
          configKey: 'AttachConfigKey'
        },
        { // 文档
          name: this.$t('pc_o2o_lbl_adddoc'),
          type: 'doc',
          filters: '.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pps,.pdf',
          disabled,
          disabledMsg,
          show: showIcon('3'),
          maxSize: 1024 * 200,
          configKey: 'hwStuDoc',
          sceneCode: 'hwStuDoc'
        },
        { // 音频
          name: this.$t('pc_o2o_lbl_addaudio'),
          type: 'audio',
          filters: '.w4v,.m4a,.wma,.wav,.mp3,.amr',
          disabled,
          disabledMsg,
          show: showIcon('6'),
          maxSize: 1024 * 200,
          configKey: 'AttachConfigKey'
        },
        { // 录音
          name: this.$t('pc_o2o_lbl_addvoice'),
          type: 'voice',
          disabled: disabled || voiceDisabled,
          disabledMsg: voiceDisabled ? voiceDisabledMsg : disabledMsg,
          show: showIcon('5'),
          maxSize: 1024 * 30
        },
        { // 压缩包
          name: this.$t('pc_o2o_lbl_addzip'),
          type: 'zip',
          filters: '.zip,.rar',
          disabled,
          disabledMsg,
          show: showIcon('7'),
          maxSize: 1024 * 1024 * 1,
          configKey: 'AttachConfigKey'
        }
      ].filter(icon => icon.show);
    }
  },
  methods: {
    onReady() {},
    // 添加到附件列表
    addFile(files) {
      // const file = files[0];
      let restSize = this.fileSize - this.attachList.length; // 还能上传的数量
      restSize = restSize > 0 ? restSize : 0;
      files.slice(0, restSize).forEach(file => {
        this.attachList.push({
          uuid: file.uuid,
          fileId: '',
          url: '',
          name: file.name,
          fileSize: file.size,
          fileType: file.fileType,
          fileSubType: this.getFileSubType(file), // 子类型
          duration: 0, // 录音时间
          progress: 0, // 录音播放进度
          playState: 2, // 录音播放状态 1-播放中 2-暂停 3-加载中
          status: file.fileType === 'image' ? 1 : -1, // -1 未发起转码 0转码中 1转码成功 2转码失败
          loading: true,
          per: 0, // 文件上传进度
          abort: null // 方法，调用此方法可以终止文件上传
        });
      });
      if (restSize < files.length) {
        // 部分超限
        this.$message.warning(this.$t('pc_o2o_msg_maxfilesize', [this.fileSize]));
        const restFiles = files.slice(restSize);
        restFiles.forEach(file => {
          file.abort();
        });
      }
    },
    // 上传前对文件过滤
    // 返回的数组中包含要上传的文件，空数组表示不上传
    filesFilter(files) {
      return files;
    },
    onProgress(file, progress) {
      const per = (progress * 100).toFixed(1);
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (f) {
        f.per = per;
        !f.abort && (f.abort = file.abort);
      }
    },
    // 上传成功 voiceTime-录音时间
    async onUploaded(file, voiceTime) {
      console.log('file', file);
      this.uploadCallback && this.uploadCallback();
      // 录音先添加到附件列表
      if (file.fileType === 'voice') {
        this.addFile([file]);
      }
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (!f) {
        return;
      }

      f.fileId = file.id;
      f.url = file.fullUrl;

      if (['notNeedTrans'].includes(Object.keys(file))) {
        f.notNeedTrans = file.notNeedTrans;
      }
      // 图片设置预览地址
      if (file.fileType === 'image') {
        // 获取 image url
        this.$set(f, 'viewUrl', f.url);
      }
      delete file.abort;
      if (file.fileType === 'voice') {
        f.duration = file.duration;
      }
      f.loading = false;
    },
    onError(type, file) {
      // 删除附件
      const index = this.attachList.findIndex(item => item.uuid === file.uuid);
      index >= 0 && this.attachList.splice(index, 1);
      // 文件格式错误
      if (type === 'forbidden') {
        return this.$message.error(this.$t('pc_o2o_msg_fileformatincorrect'));
      }
      // 超过文件尺寸
      if (type === 'oversize') {
        const c = this.configs.find(item => item.type === file.fileType);
        return this.$message.error(`${c.name}${this.$t('pc_o2o_lbl_max_support')}${c.maxSize / 1024}M`);
      }
      // 录音时间太短
      if (type === 'tooshort') {
        return this.$message(this.$t('pc_o2o_tip_voicetimetooshort'));
      }
      this.$message.error(this.$t('pc_o2o_lbl_uploadfail'));
    },
    getFileSubType(file) {
      let type = '';
      if (file.fileType === 'audio') {
        type = 'audio';
      } else if (file.fileType === 'zip') {
        type = 'zip';
      } else {
        type = file.fileClass || file.fileType;
      }
      return type;
    }
  }
};
