<!-- 精华作业列表 -->
<template>
  <div class="hw-essential">
    <template v-if="!loading && list.length">
      <div class="hw-essential__list">
        <div
          v-for="item in list"
          :key="item.communionId"
          class="flex hw-essential__item hand"
          @click="toDetail(item)"
        >
          <yxtf-portrait
            size="36px"
            :img-url="item.studentIcon"
            :username="item.studentName"
            class="mr8"
          />
          <div class="flex-1">
            <div>
              {{ item.studentName }}
            </div>
            <div class="standard-size-12 color-gray-7">
              <span>
                {{ $t('pc_ulcdsdk_lbl_submit_time'/** 提交时间： */) }}{{ item.submitTime }}
              </span>
              <span class="ml20">
                {{ item.studentDept }}
              </span>
            </div>
            <div class="mt8 ulcdsdk-break-word">
              {{ item.itemCommunionDetails[0].submitContent }}
            </div>
            <template v-if="item.attachments && item.attachments.length">
              <!-- 其他附件列表 -->
              <AttachmentList
                for-list
                :list="item.attachments.filter(f => f.fileType !== 'voice')"
                @voiceState="setVoiceState"
              />
              <!-- 录音列表 -->
              <VoiceBarList
                :list="item.attachments.filter(f => f.fileType === 'voice')"
                width="100%"
                :class="item.attachments.filter(f => f.fileType === 'voice').length===0 ? '':'mt8'"
                service="hwkEngine"
              />
              <div class="standard-size-12 color-gray-7 mt12">
                {{ $t('pc_ulcdsdk_attachment_count',[item.attachments.length]/** 共{0}个附件 */) }}
              </div>
            </template>
          </div>
        </div>
      </div>
      <yxtf-pagination
        class="mt16 text-right"
        :current-page="pager.current"
        :page-size="pager.limit"
        simple-total
        layout="total, prev, pager, next"
        :total="pager.count"
        @current-change="handleCurrentChange"
      />
    </template>
    <yxtf-empty v-else-if="!loading" class="mb24" />
  </div>
</template>

<script>
import { getEssentailCommunion } from '../service.js';
import VoiceBarList from './voice-bar-list/index.vue';
import AttachmentList from './attachment-list.vue';
export default {
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  components: {
    VoiceBarList,
    AttachmentList
  },
  data() {
    return {
      factor: {
        transferText: false
      },
      list: [],
      hwState: {
        voiceState: ''
      },
      loading: false,
      pager: {
        limit: 10,
        current: 1,
        count: 0
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    setVoiceState() {
      this.hwState.voiceState = Date.now();
    },
    getList() {
      if (this.loading) return;
      const params = {
        homeWorkId: this.id,
        offset: (this.pager.current - 1) * this.pager.limit,
        limit: this.pager.limit
      };
      this.loading = true;
      getEssentailCommunion(params).then((res) => {
        this.loading = false;
        this.list = res.datas.map((item) => {
          if (item.itemCommunionDetails[0].submitAttachments && item.itemCommunionDetails[0].submitAttachments.length) {
            item.itemCommunionDetails[0].submitAttachments.forEach(item => {
              if (item.fileType === 'image') { // 图片不需要等待转码
                item.status = 1;
              }
              if (item.fileSubType === 'voice') { // 录音
                item.fileType = 'voice';
                item.playState = 2; // 录音播放状态 1-播放中 2-暂停 3-加载中
                item.progress = 0; // 播放进度
              }
            });
            item.attachments = item.itemCommunionDetails[0].submitAttachments;
          }
          return item;
        });
        this.pager.count = res.paging.count;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleCurrentChange(val) {
      this.pager.current = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.pager.limit = val;
      this.getList();
    },
    toDetail(item) {
      this.$emit('toEssentialDetail', item);
    }
  }
};
</script>

<style lang="scss">
.hw-essential__list {
  margin-top: -10px;

  .hw-essential__item {
    padding: 24px 0;
    border-bottom: 1px solid #f0f0f0;
  }
}
</style>
