<template>
  <div class="o2o-hwk-attachments-container">
    <ul
      v-if="!forList"
      class="clearfix mr-24"
    >
      <slot></slot>
      <li
        v-for="file of showList"
        :key="file.uuid || file.fileId"
        class="o2o-task-file-wrap pull-left pr o2o-task-file o2o-attach-file"
        :class="inDrawer ? 'o2o-task-file-drawer' : 'mt16 mr16'"
      >
        <!-- 删除按钮 -->
        <i
          v-if="isEdit"
          class="o2o-task-file-del right-top mask hand opah0"
          @click="del(file)"
        >
          <yxtf-svg
            icon-class="delete-1"
            width="14px"
            height="14px"
            class="pa pa-center c-f"
          />
        </i>
        <!-- 上传中 -->
        <div
          v-if="file.loading"
          class="o2o-task-file-loading radius4 over-hidden pr"
        >
          <span class="pa pa-center">{{ file.per }} %</span>
        </div>
        <div
          v-else-if="file.error"
          class="o2o-task-file-error radius4 over-hidden pr o2o-flex-center-center layout-flex-vertical"
        >
          <yxtf-image
            class="w24"
            :src="`${$staticBaseUrl}ufd/b0174a/common/pc/img/load-error.png`"
            fit="contain"
          />
          <span class="fail-text mt5 font-size-14 c-fb">{{ $t('pc_o2o_lbl_uploadfail') }}</span>
        </div>
        <div
          v-else
          class="o2o-task-file-img text-center radius4 over-hidden pr"
        >
          <i
            v-if="file.fileType === 'zip' || (file.fileType === 'audio' && file.status !== 1) || (!file.viewUrl && ['doc', 'video'].includes(file.fileType))"
            class="wline d-block bg-center hline pr"
          >
            <yxtf-svg
              :icon-class="'icons/f_kng-' + file.fileSubType"
              width="54px"
              height="54px"
              class="pa pa-center"
            />
          </i>
          <template v-else>
            <!-- 使用背景图 -->
            <yxtf-image
              v-if="file.viewUrl"
              :src="file.viewUrl"
              fit="contain"
              class="d-block text-8c bg-f5 wline hline"
            >
              <div
                slot="placeholder"
                class="image-slot font-size-12"
              >{{ $t('pc_o2o_lbl_loading') }}</div>
              <div
                slot="error"
                class="image-slot font-size-24"
              >
                <i class="yxtf-icon-picture-outline"></i>
              </div>
            </yxtf-image>
            <!-- 音频转码成功后使用背景图 -->
            <i
              v-if="file.fileType === 'audio' && file.status === 1"
              class="yxt-o2o-audio-bg d-block wline hline"
            ></i>
          </template>

          <!-- hover 遮罩 -->
          <div
            v-if="['doc', 'image', 'zip', 'audio', 'video'].includes(file.fileType)"
            class="layout-flex layout-align-center layout-justify-center pa right-top mask wline hline opah0 opah1"
          >
            <div
              v-if="file.fileType !== 'zip'"
              class="h24 layout-flex layout-align-center color-white hand"
              @click="preview(file)"
            >
              <yxtf-tooltip
                v-if="front"
                :content="$t('pc_o2o_lbl_preview' /* 预览 */)"
                placement="top"
              >
                <yxtf-svg
                  icon-class="look"
                  width="16px"
                  height="16px"
                  class="v-mid"
                />
              </yxtf-tooltip>
              <yxt-tooltip
                v-else
                :content="$t('pc_o2o_lbl_preview' /* 预览 */)"
                placement="top"
              >
                <yxtf-svg
                  icon-class="look"
                  width="16px"
                  height="16px"
                  class="v-mid"
                />
              </yxt-tooltip>
            </div>
            <div
              :class="{ml20: file.fileType !== 'zip'}"
              class="h24 layout-flex layout-align-center color-white hand"
              @click="download(file)"
            >
              <yxtf-tooltip
                v-if="front"
                :content="$t('pc_o2o_lbl_download' /* 下载 */)"
                placement="top"
              >
                <yxtf-svg
                  icon-class="download"
                  width="16px"
                  height="16px"
                  class="v-mid"
                />
              </yxtf-tooltip>
              <yxt-tooltip
                v-else
                :content="$t('pc_o2o_lbl_download' /* 下载 */)"
                placement="top"
              >
                <yxtf-svg
                  icon-class="download"
                  width="16px"
                  height="16px"
                  class="v-mid"
                />
              </yxt-tooltip>
            </div>

            <div
              v-if="(file.status === 1 || file.fileType === 'image') && showKng"
              class="h24 layout-flex layout-align-center color-white hand ml20 lhnormal"
            >
              <yxtbiz-file-join-knglib
                width="16px"
                height="16px"
                :file-id="file.fileId"
                :file-name="getFilePrefix(file, 0)"
                :file-size="file.fileSize"
                :file-ext="getFilePrefix(file, 1)"
              />
            </div>
          </div>
        </div>
        <div
          v-if="showName"
          class="o2o-task-file-name mt8 wline nowrap ellipsis"
          :class="{'text-center':!file.showConvert}"
        >
          <!-- 转码中显示 loading -->
          <i
            v-if="file.showConvert"
            v-floading="true"
            yxtf-loading-spinner="yxtf-icon-loading"
            class="d-in-block v-mid"
          ></i>
          <yxtf-tooltip
            v-if="front"
            open-filter
            :content="file.name"
            placement="top"
          >
            <span
              class="v-mid nowrap ellipsis flex-1 flex-shrink-0"
              :class="{'ml8' : file.showConvert}"
            >{{ file.name }}</span>
          </yxtf-tooltip>
          <yxt-tooltip
            v-else
            open-filter
            :content="file.name"
            placement="top"
          >
            <span
              class="v-mid nowrap ellipsis flex-1 flex-shrink-0 c-59 font-size-12"
              :class="{'ml8' : file.showConvert}"
            >{{ file.name }}</span>
          </yxt-tooltip>
        </div>
        <div class="font-size-12 yxtf-color-danger o2o-attachment-error text-center ellipsis">
          <span v-if="file.error">
            {{ file.errorMsg }}
          </span>
        </div>
      </li>
    </ul>
    <ul
      v-else
      class="hwk-attachments--list flex"
    >
      <li
        v-for="file of showList"
        :key="file.uuid || file.fileId"
        class="hwk-attachments--list__item"
        @click.stop="preview(file)"
      >
        <yxtf-svg
          class="yxtulcdsdk-flex-shrink-0 mr8"
          width="24px"
          height="24px"
          :icon-class="getIconClass(file)"
        />
        <span class="ellipsis">
          {{ file.name }}
        </span>
      </li>
    </ul>
    <!-- 展开全部/收起全部 -->
    <OpenCloseAll
      v-if="list.length > (inDrawer ? 9 : 8) && !isEdit"
      :open.sync="open"
      class="mt16 mb8"
    />
    <yxtbiz-course-player
      :download-cb="(fileId)=>download({fileId})"
      :add-to-kng-store-cb="addToKngStoreCb"
      :visible.sync="showCoursePlayer"
      :file-id-list="fileIdList"
      :start="coursePlayerStart"
    />
    <yxtbiz-file-join-knglib
      ref="knglib"
      class="d-none"
      :file-id="kngLibFile.fileId"
      :file-name="kngLibFile.fileName"
      :file-size="kngLibFile.fileSize"
      :file-ext="kngLibFile.fileExt"
      :scene="1"
    >
      <span></span>
    </yxtbiz-file-join-knglib>

    <!-- 图片预览 -->
    <!-- <ViewImage
      v-show="showImageViewer"
      ref="viewImage"
      :url-list="imgUrlList"
      :on-close="imageViewerClose"
    /> -->
    <!-- 视频、音频预览 -->
    <!-- <ViewMedia ref="viewMedia" /> -->
    <!-- 文档播放器 -->
    <!-- <ViewDoc ref="viewDoc" /> -->
  </div>
</template>

<script>
import OpenCloseAll from './open-close-all.vue';
import { fetchDownloadUrl } from './reference-list/diff-o2o-homewokengine';
import { getFilePrefix, getFilenameAndExtname } from '../utils';
// import ViewImage from './view-image';
// import ViewMedia from './view-media';
// import ViewDoc from './view-doc';
const iconClass = {
  video: 'icons/f_kng-video',
  image: 'icons/f_kng-img',
  audio: 'icons/f_kng-audio',
  zip: 'icons/f_kng-zip',
  excel: 'icons/f_kng-excel',
  pdf: 'icons/f_kng-pdf',
  word: 'icons/f_kng-word',
  ppt: 'icons/f_kng-ppt',
  other: 'icons/f_kng-other'
};
export default {
  name: 'AttachmentList',
  components: {
    // ViewImage,
    // ViewMedia,
    // ViewDoc,
    OpenCloseAll
  },
  props: {
    service: {
      type: String,
      default: 'o2o', //  区分 o2o 和 作业引擎的服务
      validator: value => ['o2o', 'hwkEngine'].includes(value)
    },
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    isEdit: Boolean,
    inDrawer: Boolean,
    view: Boolean,
    showKng: {
      type: Boolean,
      default: true
    },
    showName: {
      type: Boolean,
      default: true
    },
    front: {
      type: Boolean,
      default: true
    },
    // 列表附件预览
    forList: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      kngLibFile: {
        fileId: '',
        fileName: '',
        fileSize: 0,
        fileExt: ''
      },
      coursePlayerStart: 0,
      showCoursePlayer: false,
      orgId: window.localStorage.getItem('orgId'),
      showImageViewer: false,
      open: false // 是否展开全部
    };
  },
  computed: {
    imgUrlList() {
      return this.list.filter(f => f.fileType === 'image').map(f => f.viewUrl || f.url);
    },
    showList() {
      const isDrawerNumber = this.inDrawer ? 9 : 8;
      return this.open || this.isEdit ? this.list : this.list.slice(0, isDrawerNumber);
    },
    fileIdList: ({ list }) => list.map(file => file.fileId)
  },

  methods: {
    getFilePrefix,
    // 预览图片、视频、音频、文档
    async preview(file) {
      this.$emit('voiceState'); // 停止录音播放
      // const fileType = file.fileType;
      // 图片
      // if (fileType === 'image') {
      //   const index = this.imgUrlList.findIndex(url => url === (file.viewUrl || file.url));
      //   this.$refs.viewImage.index = index;
      //   this.$nextTick(() => {
      //     this.showImageViewer = true;
      //   });
      // } else {
      //   // 视频、音频、文档
      //   // 动态添加是否在加载中标志
      //   this.$set(file, 'showConvert', true);
      //   // 校验转码状态
      //   const state = await this.checkFileStatus(file);
      //   if (state !== 1) { // 转码中、转码失败
      //     this.$set(file, 'showConvert', state === 0);
      //     return;
      //   }
      //   // 文档
      //   if (fileType === 'doc') {
      //     this.$refs.viewDoc.init(file.fileId, {
      //     }, file.name);
      //     this.$set(file, 'showConvert', false);
      //     return;
      //   }
      //   // 转码成功才获取播放地址
      //   fetchPlayUrls(this.service, file.fileId, fileType).then(d => {
      //     const datas = d.data.datas || [];
      //     if (datas.length === 0) return;
      //     // 视频、音频
      //     const dd = datas[0];
      //     this.$refs.viewMedia.init({
      //       fileFullUrl: dd.url,
      //       fileId: file.fileId,
      //       resolution: dd.desc,
      //       fileType
      //     });
      //   }).finally(() => {
      //     this.$set(file, 'showConvert', false);
      //   });
      // }
      this.previewDocAndImg(file.fileId);
    },
    previewDocAndImg(fileId) {
      const startIndex = this.fileIdList.findIndex(id => id === fileId);
      Object.assign(this, {
        coursePlayerStart: startIndex > -1 ? startIndex : 0,
        showCoursePlayer: true
      });
    },
    // 验证文件转码状态 0-转码中 1-转码成功 2-转码失败
    // async checkFileStatus(fileData) {
    //   const t = this.$t.bind(this);
    //   const name = t(`pc_o2o_lbl_${fileData.fileType}`);

    //   if (fileData.status === 0 || this.view) {
    //     // 实时检查文件状态
    //     const api = this.view ? fetchCodeState : fetchTranscodeState;
    //     const params = this.view ? [this.service, fileData.fileId, '', { orgId: this.orgId }] : [this.service, fileData.fileId];

    //     const result = await api(...params).then(({ data }) => {
    //       fileData.status = data;
    //       switch (data) {
    //         case -1: // 未转码
    //           this.$fmessage.warning(`${t('pc_o2o_preview_after_submit' /* 提交后可进行预览 */)}`);
    //           return -1;
    //         case 0: // 转码中
    //           this.$fmessage.warning(`${name}${t('pc_o2o_lbl_transcoding')}`);
    //           return 0;
    //         case 2: // 转码失败
    //           this.$fmessage.warning(`${name}${t('pc_o2o_lbl_transcodefail')}`);
    //           return 2;
    //         case 1: // 转码成功
    //         default:
    //           return 1;
    //       }
    //     });
    //     return result;
    //   } else if (fileData.status === 2) {
    //     this.$message.error(`${name}${t('pc_o2o_lbl_transcodefail')}`);
    //     return 2;
    //   } else if (fileData.status === -1) {
    //     this.$fmessage.warning(`${t('pc_o2o_preview_after_submit' /* 提交后可进行预览 */)}`);
    //     return -1;
    //   }
    //   return 1;
    // },
    // 关闭图片预览
    // imageViewerClose() {
    //   this.showImageViewer = false;
    // },
    // 删除上传文件
    del(file) {
      this.$emit('del', file);
    },
    async download(file) {
      fetchDownloadUrl(this.service, file.fileId).then(res => {
        window.location.href = res.data;
      });
    },
    getFilenameAndExtname: filename => getFilenameAndExtname(filename),
    addToKngStoreCb(fileId) {
      const { name, fileSize } = this.list.find(file => file.fileId === fileId);
      const { prefix, suffix } = getFilenameAndExtname(name);
      this.kngLibFile = {
        fileId,
        fileName: prefix,
        fileSize,
        fileExt: suffix
      };
      this.$refs.knglib.showDialog();
    },
    getIconClass(item) {
      if (item.fileType === 'doc') {
        const extension = `.${getFilenameAndExtname(item.name).suffix}`;
        if (['.doc', '.docx'].includes(extension)) {
          return iconClass.word;
        } else if (['.ppt', '.pptx'].includes(extension)) {
          return iconClass.ppt;
        } else if (['.xls', '.xlsx', 'excel'].includes(extension)) {
          return iconClass.excel;
        } else if (['.pps', '.pdf'].includes(extension)) {
          return iconClass.pdf;
        }
        return iconClass.other;
      }
      return iconClass[item.fileType] || iconClass.other;
    }
  }
};
</script>

<style lang="scss">
.o2o-task-file-wrap {
  &:hover {
    .o2o-task-file-del {
      opacity: 1;
    }
  }

  .yxtf-button--text:hover {
    color: #fff;
  }
}

.o2o-task-file {
  width: 188px;
}

.o2o-task-file-drawer {
  width: calc((100% - 48px) / 3);
}

.o2o-task-file-del {
  z-index: 19;
  width: 20px;
  height: 20px;
  border-radius: 0 4px;
}

.o2o-task-file-loading {
  background-color: rgba(0, 0, 0, 0.01);
}

.o2o-task-file-img,
.o2o-task-file-loading {
  box-sizing: border-box;
  height: 106px;
  line-height: 106px;
  background-color: #f5f5f5;
  border: 1px solid #f5f5f5;
}

.o2o-task-file-error {
  box-sizing: border-box;
  height: 106px;
  line-height: 1;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
}

.o2o-task-file-name {
  display: flex;
  align-items: center;

  i {
    width: 14px;
    height: 14px;
  }

  .yxtf-loading-spinner {
    margin-top: -8px;
  }
}

.o2o-attachment-error {
  height: 16px;
  line-height: 16px;
}

.hwk-attachments--list {
  flex-wrap: wrap;
  margin-left: -8px;

  .hwk-attachments--list__item {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    width: calc((100% - 16px) / 2);
    height: 40px;
    margin-top: 8px;
    margin-left: 8px;
    padding: 0 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }
}
</style>
