<template>
  <div>
    <yxt-link type="primary" target="_blank" @click="openXuankeUrl()">{{ $t('pc_o2o_lbl_seeuseranswers') }}</yxt-link>
  </div>
</template>

<script>
import { getStudyXuankeUrl } from '../service';
export default {
  props: {
    itemId: {
      type: String,
      default: ''
    },
    taskId: {
      type: String,
      default: ''
    },
    userId: {
      type: String,
      default: ''
    },
    xuankeInfo: {
      type: Object,
      default: ()=>({})
    }
  },
  mounted() {

  },
  methods: {
    async openXuankeUrl() {
      let taskId = this.taskId;
      if (this.xuankeInfo && this.xuankeInfo.taskId) {
        taskId = this.xuankeInfo.taskId;
      }
      const data = await getStudyXuankeUrl(this.itemId, taskId, this.userId);
      if (data) {
        window.open(data);
      } else {
        this.$fmessage.error(this.$t('pc_o2o_lbl_stu_not_answer' /* 学员未作答 */));
      }
    }
  }
};
</script>
