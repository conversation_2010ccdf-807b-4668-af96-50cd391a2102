import previewDownloadMixin from './preview-download-mixin';
// import { getFilenameAndExtname } from '../../utils';
import { putThirdUrl } from '../../service';

export default {
  data() {
    return {
      imgUrlList: [],
      kngLibFile: {
        fileId: null,
        fileName: '',
        fileSize: 0,
        fileExt: ''
      }
    };
  },
  mixins: [previewDownloadMixin],
  methods: {
    async previewImage(file) {
      if (!file.canPreview) return;
      const urls = await this.fetchPreviewUrl(file.fileId, file.fileType);
      this.imgUrlList = urls.map(ele => ele.url);
      this.showImageViewer = true;
      file.canPreview = false; // 预览结束后数据重置
    },
    // getFilenameAndExtname: filename => getFilenameAndExtname(filename),
    // addToKngStoreCb(fileId) {
    //   const { name, fileSize } = this.files.find(file => file.fileId === fileId);
    //   const { prefix, suffix } = getFilenameAndExtname(name);
    //   this.kngLibFile = {
    //     fileId,
    //     fileName: prefix,
    //     fileSize,
    //     fileExt: suffix
    //   };
    //   this.$refs.knglib.showDialog();
    // },
    downloadFile(fileId) {
      return putThirdUrl(fileId).then(res => {
        window.location.href = res;
      });
    }
  }
};
