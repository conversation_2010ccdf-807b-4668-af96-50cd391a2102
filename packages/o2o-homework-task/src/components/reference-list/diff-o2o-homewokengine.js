/*
 * @FileDescription: 批阅中心和学员端组件复用，用于作各自私有数据映射
 * @Author: 刘凯
 * @Date: 2021-09-23 11:27:19
 * @LastEditors: 刘凯
 * @LastEditTime: 2021-10-14 23:03:18
 */

import { putThirdUrl, postThirdUrl, postCheckFileStatus, getFileTranscodeState, getFileDownloadUrl, getFilePlayUrls, voice2Text, convertVioce2Text } from '../../service';

// 组件的复用针对 学员端或者批阅中心，进行 各自的数据映射
export const serviceMap = new Map([
  ['hwkEngine', {
    fetchTranscodeState: getFileTranscodeState,
    fetchDownloadUrl: getFileDownloadUrl,
    fetchPlayUrls: getFilePlayUrls,
    voice2Text: convertVioce2Text
  }],
  ['o2o', {
    fetchTranscodeState: postCheckFileStatus,
    fetchDownloadUrl: putThirdUrl,
    fetchPlayUrls: postThirdUrl,
    voice2Text
  }]
]);

export const fetchTranscodeState = (service, ...data) => {
  if (service === 'o2o') data.push(6); // o2o 原接口，需要添加taskType 6 来与 作业引擎文件区分
  return serviceMap.get(service).fetchTranscodeState(...data).then((res)=>({data: res}));
};

export const fetchDownloadUrl = (service, ...data) => {
  return serviceMap.get(service).fetchDownloadUrl(...data).then((res)=>({data: res}));
};

export const fetchPlayUrls = (service, ...data) => {
  return serviceMap.get(service).fetchPlayUrls(...data)
    .then(res => service === 'hwkEngine' ? { data: { datas: res } } : {data: res}); // 数据结构处理，保证两个服务下保持返回的数据结构相同
};

export const fetchCodeState = (service, ...data) => {
  return serviceMap.get(service).fetchTranscodeState(...data).then((res)=>({data: res}));
};

export const convertVoice2Text = (service, ...data) => {
  return serviceMap.get(service).voice2Text(...data)
    .then(res => service === 'hwkEngine' ? { data: res.map(text => ({ onebest: text })) } : {data: res}); // 保持数据返回字段的一致性，  以历史 o2o为准
};
