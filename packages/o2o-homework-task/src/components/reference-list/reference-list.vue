<template>
  <div>
    <yxtf-row
      v-for="count in Math.ceil(files.length/2)"
      :key="count"
      type="flex"
      :gutter="24"
      :class="{mt4: count != 1}"
    >
      <yxtf-col :span="12">
        <File
          :preview="preview"
          :file="files[(count-1) * 2]"
          @preview="handlePreview"
          @download="handleDownload"
        />
      </yxtf-col>
      <yxtf-col :span="12" class="">
        <File
          v-if="files[count * 2 - 1]"
          :preview="preview"
          :file="files[count * 2 - 1]"
          @preview="handlePreview"
          @download="handleDownload"
        />
      </yxtf-col>
    </yxtf-row>
    <!-- <yxtbiz-course-player
      v-if="showCoursePlayer"
      :download-cb="downloadFile"
      :add-to-kng-store-cb="addToKngStoreCb"
      :visible.sync="showCoursePlayer"
      :file-id-list="fileIdList"
      :start="coursePlayerStart"
    />
    <yxtbiz-file-join-knglib
      ref="knglib"
      class="d-none"
      :file-id="kngLibFile.fileId"
      :file-name="kngLibFile.fileName"
      :file-size="kngLibFile.fileSize"
      :file-ext="kngLibFile.fileExt"
      :scene="1"
    >
      <span></span>
    </yxtbiz-file-join-knglib> -->
    <ViewImage
      v-show="showImageViewer"
      ref="viewImage"
      :url-list="imgUrlList"
      :on-close="() => showImageViewer = false"
    />
    <!-- 视频、音频预览 -->
    <ViewMedia ref="viewMedia" />
    <ViewDoc ref="viewDoc" />
  </div>
</template>
<script>
import File from '../reference/reference.vue';
import ReferenceListMixin from './reference-list-mixin.js';

export default {
  components: {
    File
  },
  props: {
    preview: Boolean
  },
  mixins: [ReferenceListMixin]
};
</script>
