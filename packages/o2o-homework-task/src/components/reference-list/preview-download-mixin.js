import { fetchTranscodeState, fetchDownloadUrl, fetchPlayUrls } from './diff-o2o-homewokengine.js';
import ViewImage from '../view-image.vue';
import ViewMedia from '../view-media.vue';
import ViewDoc from '../view-doc.vue';

export default {
  props: {
    service: {
      type: String,
      default: 'o2o', //  区分 o2o 和 作业引擎的服务
      validator: value => ['o2o', 'hwkEngine'].includes(value)
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  components: {
    ViewImage,
    ViewMedia,
    ViewDoc
  },
  data() {
    return {
      coursePlayerStart: 0,
      showCoursePlayer: false,
      showImageViewer: false,
      transcodeMaps: {
        '-1': 'noTranscode',
        0: 'transcoding',
        1: 'success',
        2: 'failed'
      } // -1 : 未发起转码 ,0: // 转码中 1: // 转码成功 2: // 转码失败
    };
  },
  computed: {
    fileIdList: ({ list }) => list.map(file => file.fileId),
    files() {
      this.list.forEach(file => {
        if (!file.transcode) {
          this.addCustomProperty(file);
        }
      });
      return this.list;
    }
  },
  methods: {
    addCustomProperty(file) {
      const anObject = {
        downloading: false, // 下载中
        canPreview: false, // 弱网多次触发的时候, 只有最后一次点击的元素可以 preview
        transcode: 'initial'// initial： 初始化，未知; checking 校验中; transcoding： 转码中 ;  failed: 转码失败; success: 转码成功
      };
      for (const key in anObject) {
        this.$set(file, key, anObject[key]);
      }
    },
    // TODO fmessage 前后台理论上应当分开
    async handlePreview(file) {
      this.$emit('voiceState'); // 停止录音播放
      if (file.fileType === 'zip') return this.$fmessage.warning(this.$t('pc_o2o_tip_no_support_preview'));// 该文件不支持预览，请下载该文件
      if (file.fileType !== 'image') { // 非图片文件进行转码校验
        if (file.transcode === 'checking') return;
        if (file.transcode === 'initial') file.transcode = this.transcodeMaps[file.status];
        if (file.transcode === 'transcoding') {
          try {
            file.transcode = 'checking';
            const fileStatus = await this.checkFileStatus(file.fileId);
            file.status = fileStatus;
          } finally {
            file.transcode = this.transcodeMaps[file.status]; // 假如接口错误也可以恢复之前的文件状态
          }
        }
        if (file.transcode === 'noTranscode') { // 未发起转码
          return this.$fmessage.warning(this.$t('pc_o2o_preview_after_submit' /* 提交后可进行预览 */));
        }

        if (file.transcode === 'transcoding') { // 转码中
          return this.$fmessage.warning(this.$t('pc_o2o_transcoding_no_preview' /* 转码中，暂不支持预览 */));
        }

        if (file.transcode !== 'success') { // 转码失败
          return this.$fmessage.warning(this.$t('pc_o2o_transcode_fail_no_preview' /* 转码失败，暂不支持预览 */));
        }
      }

      // 转码成功, 获取预览地址进行预览
      // 弱网多次触发，只展示最后一个
      this.files.filter(view => view.canPreview).forEach(fileFor => (fileFor.canPreview = false));
      file.canPreview = true; // 当前可以展示
      if (file.fileType === 'image') return this.previewImage(file);
      if (file.fileType === 'doc') return this.previewDoc(file);
      const urls = await this.fetchPreviewUrl(file.fileId, file.fileType);
      if (['audio', 'video'].includes(file.fileType)) return this.previewMedia(file, urls);
      // this.previewDocAndImg(file.fileId);
    },
    // previewDocAndImg(fileId) {
    //   const startIndex = this.fileIdList.findIndex(id => id === fileId);
    //   Object.assign(this, {
    //     coursePlayerStart: startIndex > -1 ? startIndex : 0,
    //     showCoursePlayer: true
    //   });
    // },
    previewDoc(file) {
      if (!file.canPreview) return;
      this.$refs.viewDoc.init(file.fileId, {
      }, file.name);
      file.canPreview = false;
    },
    previewMedia(file, urls) {
      if (!file.canPreview) return;
      const firstEle = urls[0];
      this.$refs.viewMedia.init({
        fileFullUrl: firstEle.url,
        fileId: file.fileId,
        resolution: firstEle.desc,
        fileType: file.fileType
      });
      file.canPreview = false;
    },
    fetchPreviewUrl(fileId, fileType) {
      return fetchPlayUrls(this.service, fileId, fileType)
        .then(({ data: { datas } }) => datas || []);
    },
    checkFileStatus(fileId) {
      return fetchTranscodeState(this.service, fileId)
        .then(({ data }) => {
          return data;
        });
    },
    handleDownload(file) {
      const { fileId, downloading } = file;
      if (downloading) return;
      file.downloading = true;
      fetchDownloadUrl(this.service, fileId).then(res => {
        window.location.href = res.data;
      })
        .finally(() => (file.downloading = false));
    }
  }
};
