<template>
  <div @click="disabled ? null: recordHandle()">
    <div class="text-8c" :class="disabled ? 'opa7 not-allowed': 'hover-primary-6'">
      <yxt-ulcd-sdk-svg
        :icon-class="'o2o-up-' + config.type"
        module="o2o"
        type="other/svg"
        width="16px"
        height="16px"
        class="d-in-block mr4 v-mid"
        :class="{disabled:config.disabled}"
      />
      <span class="v-mid text-59" :class="disabled ? '': 'hover-primary-6'">{{ config.name }}</span>
    </div>
    <RecordDialog
      v-if="showRecord"
      ref="recordDialog"
      :app-code="appCode"
      :show.sync="showRecord"
      @success="success"
    />
  </div>
</template>

<script>
import RecordDialog from './record-dialog';
import Recorder from 'js-audio-recorder';
export default {
  components: {
    RecordDialog
  },
  props: {
    appCode: String,
    config: {
      type: Object,
      default() { return {}; }
    },
    disabled: Boolean
  },
  data() {
    return {
      showRecord: false
    };
  },
  methods: {
    // 显示录音
    recordHandle() {
      if (this.config.disabled) {
        this.$message(this.config.disabledMsg);
        return;
      }
      Recorder.getPermission().then(() => {
        this.showRecord = true;
        this.$nextTick(() => {
          this.$refs.recordDialog.init(Recorder);
        });
      }).catch(e => {
        if (e.name === 'NotFoundError') {
          this.$message.warning(this.$t('pc_o2o_msg_audionotfound'));
        } else if (e.name === 'NotAllowedError') {
          this.$message.warning(this.$t('pc_o2o_msg_audionotallowed'));
        } else {
          this.$message.warning(e.message);
        }
      });
    },
    // 录音上传成功
    success(file, time) {
      this.$emit('success', file, time);
    }
  }
};
</script>
