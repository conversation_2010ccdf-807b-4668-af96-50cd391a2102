<template>
  <div v-if="show" class="yxt-dialog__wrapper mask opa-tran yxt-o2o-media-wrap" :style="{ 'z-index': zIndex }">
    <i class="yxt-icon-circle-close c-f0 font-size-32 hand pa pa-center yxt-o2o-media-close" @click="close"></i>
    <!-- 音频预览 -->
    <yxtbiz-video
      v-if="showAudio"
      ref="audio"
      type="audio"
      :options="audioOptions"
      width="800"
      height="600"
      class="pa pa-center"
      play-rate
      @onBeforePlay="onBeforeAudioPlay"
      @onQualityChange="onQualityAudioChange"
      @onTime="onAudioTime"
      @onError="onAudioError"
    />
    <!-- 视频预览 -->
    <yxtbiz-video
      v-if="showVideo"
      ref="video"
      type="video"
      :options="videoOptions"
      width="800"
      height="600"
      class="pa pa-center"
      play-rate
      @onBeforePlay="onBeforeAudioPlay"
      @onQualityChange="onQualityAudioChange"
      @onTime="onAudioTime"
      @onError="onAudioError"
    />
  </div>
</template>

<script>
export default {
  name: 'TaskMedia',
  props: {
    zIndex: {
      type: Number,
      default: 2000
    }
  },
  data() {
    return {
      show: false,
      showAudio: false,
      showVideo: false,
      audioOptions: [],
      videoOptions: []
    };
  },
  methods: {
    init(option) {
      this.show = true;
      if (option.fileType === 'video') {
        this.videoOptions = [option];
        this.showVideo = true;
      } else {
        this.audioOptions = [option];
        this.showAudio = true;
      }
    },
    close() {
      this.showAudio = false;
      this.showVideo = false;
      this.show = false;
    },
    onBeforeAudioPlay() {},
    onQualityAudioChange() {},
    onAudioTime() {},
    onAudioError() {}
  }
};
</script>
