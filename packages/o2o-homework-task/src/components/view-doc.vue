<template>
  <yxt-dialog
    :title="title"
    custom-class="o2o-view-doc-container"
    :visible.sync="show"
    width="100%"
    :cutline="false"
    :append-to-body="true"
  >
    <yxtbiz-doc-viewer
      v-if="show"
      :file-id="fileId"
      :file-info="fileInfo"
      style="height: 100%;"
      :show-zoom="false"
    />
  </yxt-dialog>
</template>

<script>
export default {
  data() {
    return {
      fileInfo: {
        width: 0,
        height: 0,
        list: []
      },
      show: false,
      title: '',
      fileId: ''
    };
  },
  methods: {
    init(id, option, name) {
      this.fileInfo = option;
      this.fileId = id;
      this.show = true;
      this.title = name.split('.')[0];
    }
  }
};
</script>
