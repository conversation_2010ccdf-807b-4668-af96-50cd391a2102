<!-- 录音 -->
<template>
  <yxt-dialog
    :visible.sync="visible"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :cutline="true"
    width="520px"
    class="cursor-d"
    custom-class="yxtulcdsdk-ulcdsdk"
    @closed="closed"
  >
    <div slot="title">
      <span class="weight-bold"> {{ $t('pc_o2o_tip_recordvoice') }}</span><span class="ml16 text-8c font-size-12">{{ tip }} </span>
    </div>
    <div class="layout-flex layout-align-center mv16">
      <div class="mr8 col-flex-1 o2o-record-dialog-progress">
        <!-- 进度条 -->
        <yxtf-progress
          :width="48"
          :stroke-width="3"
          type="circle"
          :percentage="percentage"
          color="#fff"
          class="pull-right"
          :class="{'o2o-show-progress-record': state !== 0}"
        >
          <!-- 图标 -->
          <template slot="content">
            <yxt-ulcd-sdk-svg
              v-if="state === 0"
              width="32px"
              height="32px"
              icon-class="o2o-voice"
              module="o2o"
              type="other/svg"
              class="color-primary-6 pa pa-center hand"
              @click.native="startRecord"
            />

            <div v-else ref="lavContainer" class="o2o-record-lottie color-primary-6 pa pa-center"></div>
          </template>
        </yxtf-progress>
      </div>
      <div class="ml8 col-flex-1">
        <!-- // 0-初始 1-录音中 2-录音结束 -->
        <template v-if="state === 1">
          <div class="font-size-18 lh18">{{ timeStr }}</div>
          <!-- 正在录音… -->
          <span
            class="text-8c font-size-12"
            :class="{ 'text-f52': warnText }"
          >{{ warnText || $t('pc_o2o_tip_recording2') }}</span>
        </template>
        <!-- 开始录音 -->
        <span
          v-else
          class="hand hover-primary-6 font-size-16 text-26"
          @click="startRecord"
        >{{ $t('pc_o2o_tip_startrecord2') }}</span>
      </div>
    </div>
    <!-- 上传中提示 -->
    <div
      v-if="uploading"
      class="o2o-record-loading pa right-top wline hline text-center text-8c font-size-12"
    >
      <i class="yxt-icon-loading v-mid"></i>
      <span class="ml8 v-mid">{{
        $t('pc_o2o_msg_data_processing', [$t('pc_o2o_lbl_voice')])
      }}</span>
    </div>
    <!-- 上传组件 -->
    <yxtbiz-upload
      ref="upload"
      :app-code="appCode"
      version="v2"
      scene-code="hw"
      class="d-none-i"
      module-name="o2o"
      function-name="o2o"
      config-key="AttachConfigKey"
      filters=".w4v,.m4a,.wma,.wav,.mp3,.amr"
      :transcode-options="transcodeOptions"
      :on-progress="onProgress"
      :on-uploaded="onUploaded"
      :on-error="onError"
    />
    <span
      slot="footer"
      class="dialog-footer d-block layout-flex layout-align-center"
    >
      <span class="text-8c text-left font-size-12 flex-1">{{
        $t('pc_o2o_lbl_recordmiss')
      }}</span>
      <yxtf-button plain @click="closed">{{
        $t('pc_o2o_btn_cancel')
      }}</yxtf-button>
      <yxtf-button type="primary" @click="endRecord()">{{
        $t('pc_o2o_lbl_complete')
      }}</yxtf-button>
    </span>
  </yxt-dialog>
</template>

<script>
import { addTime0, VOICE_TIME, MIN_VOICE_TIME, isPrivatizationOrg } from '../utils';
import lottie from 'lottie';
import voiceJson from '../voice.json';
import pageHideListener from '../mixins/page-hide-listener';
import ConvertToMp3Worker from '../convert-to-mp3.worker';

export default {
  mixins: [pageHideListener],
  props: {
    appCode: {
      type: String,
      default: 'o2o'
    },
    show: Boolean
  },
  data() {
    this.allTime = VOICE_TIME * 60;
    return {
      transcodeOptions: isPrivatizationOrg() ? { audio: { transcode: 'Y', private: 'N' } } : undefined,
      visible: false,
      time: 0, // 计时
      percentage: 0,
      state: 0, // 0-初始 1-录音中 2-录音结束
      warnText: '',
      lottieOptions: { animationData: voiceJson },
      uploading: false,
      uploadPer: 0,
      tip: this.$t('pc_o2o_tip_recordtime', [VOICE_TIME])
    };
  },
  computed: {
    timeStr() {
      const minute = parseInt(this.time / 60);
      const secode = parseInt(this.time - minute * 60);
      return `${addTime0(minute)}:${addTime0(secode)}`;
    }
  },
  watch: {
    show: {
      handler(val) {
        this.visible = val;
      },
      immediate: true
    },

    state(v) {
      if (v !== 0) {
        this.$nextTick(() => {
          this.anim = lottie.loadAnimation({
            container: this.$refs.lavContainer,
            renderer: 'svg',
            loop: this.lottieOptions.loop !== false,
            autoplay: this.lottieOptions.autoplay !== false,
            animationData: this.lottieOptions.animationData,
            rendererSettings: this.lottieOptions.rendererSettings
          });

          this.handleAnimationProgress();
        });
      }
    }
  },
  mounted() {
    this.eventListener();
  },
  beforeDestroy() {
    this._recorder && this._recorder.destroy(); // 组件销毁的时候，销毁录音实例
  },
  methods: {
    init(Recorder) {
      this._recorder = new Recorder({
        sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
        sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
        numChannels: 1 // 声道，支持 1 或 2， 默认是1
        // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
      });
      this._recorder.onprogress = ({ duration }) => {
        duration = Math.floor(duration);
        this.updateTime(duration);
      };
    },
    // 页面隐藏时，销毁
    pageHide() {
      this.closed();
    },
    // 开始录音
    startRecord() {
      this._recorder.start().then(
        () => {
          this.state = 1; // 录音中
        },
        error => {
          console.log(error);
        }
      );
    },
    // 结束录音 isTimeover-时间结束
    endRecord(isTimeover) {
      if (this.time < MIN_VOICE_TIME) {
        // 录制时间太短
        this.$message(this.$t('pc_o2o_tip_voicetimetooshort'));
        return this.closed();
      }
      if (isTimeover) {
        // 最多录制{0}分钟，已上传语音
        this.$message(this.$t('pc_o2o_tip_timeoutuploadvoice', [VOICE_TIME]));
      }
      this.stop();
      this.convertAndUploadMp3();
    },
    updateTime(duration) {
      this.time = duration;
      this.percentage = (this.time / this.allTime) * 100;
      // 小于 30 秒
      const modeTime = this.allTime - this.time;
      if (modeTime <= 30) {
        this.warnText = this.$t('pc_o2o_tip_recordmodtime', [modeTime]);
      }
      modeTime === 0 && this.endRecord(true);
    },
    stop() {
      this.progressAnimation && this.progressAnimation.pause();
      this._recorder.stop();
    },
    closed() {
      this.$emit('update:show', false);
    },
    onProgress(file, progress) {
      this.uploadPer = (progress * 100).toFixed(1);
    },
    onUploaded(file) {
      file.fileType = 'voice';
      this.$emit('success', file, this.time);
      this.closed();
    },
    onError(type, file) {
      this.uploading = false;
      this.$message.error(this.$t('pc_o2o_lbl_uploadfail'));
    },
    handleAnimationProgress(anim) {
      this.progressAnimation = anim;
    },
    eventListener() {
      window.addEventListener('offline', e => {
        this.connectNetWork = false;
      });

      window.addEventListener('online', e => {
        this.connectNetWork = true;
      });
    },
    convertAndUploadMp3() {
      this.uploading = true;
      const worker = new ConvertToMp3Worker();
      worker.addEventListener('message', ({ data: audioBlob }) => {
        if (audioBlob) {
          audioBlob.name = `${Date.now()}.mp3`;
          audioBlob.duration = this.time;
          const upload = this.$refs.upload;
          upload.addFile(audioBlob);
          upload.start();
        }
      });
      const wavDataView = this._recorder.getWAV();
      const channelData = this._recorder.getChannelData();

      worker.postMessage({ wavDataView, channelData });
    }
  }
};
</script>

<style lang="scss">
.d-none-i {
  display: none !important;
}

.o2o-record-lottie {
  width: 29px !important;
  height: 16px !important;
  margin: 0 auto;
  overflow: hidden;
}

.o2o-record-loading {
  line-height: 320px;
}

.o2o-show-progress-record {
  .yxtf-progress-circle {
    box-sizing: border-box;
    padding: 2px;
    background-color: var(--color-primary);
    border-radius: 50%;
  }

  .yxt-progress-circle__track {
    stroke: var(--color-primary-4) !important;
  }
}
</style>
