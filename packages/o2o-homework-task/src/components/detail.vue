<template>
  <div class="bg-white">
    <label v-if="multiItem" class="font-size-14 lh24 mt20  d-block weight-medium pl2">{{ $t('pc_o2o_tit_hw_stu_answer' /*  学员作答*/) }}</label>
    <div v-if="answerFormat" class="text-pre break color-gray-9 mt18" v-html="answerFormat"></div>
    <div v-if="!content && isPdf" class="text-pre break color-gray-9 mt18">{{ $t('pc_o2o_tit_hw_stu_answer_pdfno'/* 学员已作答 */) }}</div>
  </div>
</template>

<script>
import { linkStringToHtml } from '../utils';
export default {
  name: 'Detail',
  props: {
    content: {
      type: String,
      default: ''
    },
    multiItem: {
      type: Boolean,
      default: false
    },
    isPdf: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 作业内容有链接时可点击跳转
    answerFormat() {
      return linkStringToHtml(this.content);
    }
  }
};
</script>
