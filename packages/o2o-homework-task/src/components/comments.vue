<!--
 * @FileDescription: 批阅评语
 * @Author: wh
 * @Date: null
 * @LastEditors: 刘凯
 * @LastEditTime: 2022-03-02 11:32:50
-->
<template>
  <div>
    <!-- 评价 -->
    <label class="font-size-18 mt32 d-block weight-medium c-26 flex-center" :class="{'justify-between': isCommentEssential}">
      <span>{{ $t('pc_o2o_lbl_hwt_comment_result' /* 评价结果 */) }}</span>
      <div class="ml8 color-gray-7 font-size-14">{{ scoringRuleTip }}</div>
      <!--仅评分展示在这边-->
      <yxt-tag
        v-if="isCommentEssential"
        type="success"
        effect="dark"
        size="mini"
      >{{ $t('pc_pd_hw_label_essential').d('精华作业') }}</yxt-tag>
    </label>

    <template v-if="result.t">
      <!-- AI批阅-精华作业的样式 -->
      <ai-container v-if="isAiHw && isAIEssential && !taskResult.instructorUserName" ref="aiContainer">
        <div ref="aiContent" class="o2o-comment-ai-result review-ai-bg" :class="result.class">
          <div class="layout-flex layout-align-center">
            <span v-if="result.score" class="font-size-16 weight-bold color-gray-10">{{ result.score }}</span>
            <span :class="{'ml8': result.score}">{{ result.t }}</span>
          </div>
          <yxt-tag
            v-if="isAIEssential"
            type="success"
            effect="dark"
            size="mini"
          >{{ $t('pc_pd_hw_label_essential').d('精华作业') }}</yxt-tag>
        </div>
      </ai-container>
      <!-- 合格结果 -->
      <div v-else class="o2o-comment-result flex-between-center" :class="result.class">
        <div class="layout-flex layout-align-center">
          <span v-if="result.score" class="font-size-16 weight-bold color-gray-10">{{ result.score }}</span>
          <span :class="{'ml8': result.score}">{{ result.t }}</span>
        </div>
        <yxt-tag
          v-if="isAIEssential"
          type="success"
          effect="dark"
          size="mini"
        >{{ $t('pc_pd_hw_label_essential').d('精华作业') }}</yxt-tag>
      </div>
    </template>

    <!-- 评语 -->
    <div
      v-if="taskResult.reply"
      class="mt16 lh22 text-59 text-pre break"
      :class="{'mb16': otherList.length || voiceList.length}"
      v-html="dealLineFeed(taskResult.reply)"
    ></div>

    <AttachmentList
      v-if="otherList.length"
      :service="service"
      :show-limit="8"
      :list="otherList"
    />
    <!-- 语音列表 -->
    <VoiceBarList
      v-if="voiceList.length"
      :service="service"
      :list="voiceList"
      class="mb24"
      width="596px"
    />
  </div>
</template>

<script>
import VoiceBarList from './voice-bar-list/index.vue';
import AttachmentList from './attachment-list.vue';
import aiContainer from './ai-container.vue';
import { dealLineFeed } from 'packages/examing/src/core/utils.js';
export default {
  components: {
    VoiceBarList,
    AttachmentList,
    aiContainer
  },
  props: {
    service: {
      type: String,
      default: 'o2o', //  区分 o2o 和 作业引擎的服务
      validator: value => ['o2o', 'hwkEngine'].includes(value)
    },
    isStu: Boolean, // 是否是学员端
    taskResult: {
      type: Object,
      default() {
        return {};
      }
    },
    scoreType: { // 打分方式（0-直接打分 1-是否合格 2-不评分）
      type: Number,
      default: 0
    },
    attachList: {
      type: Array,
      default() { return []; }
    },
    inDrawer: Boolean,
    scoringRule: Number,
    isAiHw: Boolean
  },
  computed: {
    isBack() {
      return this.taskResult.status === 4;
    },
    result() {
      // 退回重做
      if (this.isBack) return { t: this.$t('pc_o2o_lbl_returnredo'), s: 2, class: 'review-return' };

      const { passed, score } = this.taskResult;
      const passedText = passed === 1 ? this.$t('pc_o2o_lbl_qualified') : this.$t('pc_o2o_lbl_notquaified');
      // 0-直接打分 1-是否合格
      if (this.scoreType === 0) {
        return {
          t: passedText,
          s: passed,
          score: score !== null ? this.$t('pc_o2o_lbl_score_holder' /* {n}分 */, { n: score }) : null,
          class: this.isPassed ? 'review-success' : 'review-error'
        };
      }
      if (this.scoreType === 1) return { t: passedText, s: passed, class: this.isPassed ? 'review-success' : 'review-error' };

      // 如果未评分的话则不显示整个评分模块
      return { t: false };
    },
    svgName() {
      return this.isPassed ? 'biz/tag-qualified' : 'biz/tag-unqualified';
    },

    isPassed() {
      return this.taskResult.passed === 1;
    },
    otherList() {
      return this.attachList.filter(f => f.fileType !== 'voice') || [];
    },
    // 语音列表
    voiceList() {
      return this.attachList.filter(f => f.fileType === 'voice') || [];
    },
    scoringRuleTip() {
      if (this.scoreType !== 0) {
        return '';
      }
      // 评分规则
      if (this.scoringRule === 2) {
        if (this.isAiHw) return this.$t('pc_o2o_hw_tip_scoring_rule_last_ai' /* 按最后一次批阅分数为准 */);
        return this.$t('pc_o2o_hw_tip_scoring_rule_last'/* 按最后一人批阅分数为准 */);
      }
      if (this.scoringRule === 1) {
        return this.$t('pc_o2o_hw_tip_scoring_rule_2'/* 按批阅人平均分为准 */);
      }
      if (this.scoringRule === 3) {
        return this.$t('pc_o2o_tips_hw_rule_3'/* 按批阅的最高得分为准 */);
      }
      return '';
    },
    isAIEssential() {
      return this.taskResult.essential === 1;
    },

    // 是否是评分模式下的精华作业
    isCommentEssential() {
      return this.isAIEssential && this.scoreType === 2;
    }
  },

  mounted() {
    this.setAiContainerHeight();
    window.addEventListener('resize', this.setAiContainerHeight);
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.setAiContainerHeight);
    });
  },

  methods: {
    dealLineFeed,
    setAiContainerHeight() {
      this.$nextTick(() => {
        if (!this.$refs) return;
        const aiContent = this.$refs.aiContent;
        const aiContainer = this.$refs.aiContainer;
        if (!aiContent || !aiContainer) return;
        const aiContentHeight = aiContent.clientHeight ? aiContent.clientHeight + 2 : 160;
        aiContainer.$el.style.height = `${aiContentHeight}px`;
      });
    }
  },

  watch: {
    taskResult() {
      this.setAiContainerHeight();
    }
  }
};
</script>

<style lang="scss" scoped>
.o2o-comment-result {
  display: flex;
  align-items: center;
  height: 48px;
  margin-top: 16px;
  padding: 0 16px;
  border-radius: 4px;
}
</style>
