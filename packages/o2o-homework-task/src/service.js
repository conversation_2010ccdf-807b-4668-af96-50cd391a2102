import { o2oApi, hwbaseApi, fileApi } from 'packages/api';
import qs from 'qs';

// check附件转码状态 taskType  6 表示 作业引擎的文件
export const postCheckFileStatus = (fileId, taskType = '', query) => {
  return o2oApi.get(`/attachment/get/status/${fileId}?taskType=${taskType}&${qs.stringify(query)}`);
};

// 下载文件
export const putThirdUrl = fileId => {
  return o2oApi.put(`third/file/${fileId}`);
};

// 获取附件地址
export const postThirdUrl = (fileId, type, config) => {
  return o2oApi.post(
    `third/file/${fileId}${type === 'video' ? '?clientType=1' : ''}`,
    config
  );
};

// 1.0实操炫课地址获取
export const getStudyXuankeUrl = (itemId, taskId, userId) => {
  return o2oApi.get(`study/xuankeUrl?itemId=${itemId}&taskId=${taskId}&userId=${userId}`);
};

// 语音转文字
export const voice2Text = fileId => {
  return o2oApi.get(`attachment/speech2text/${fileId}`);
};

// 获取文件下载接口地址(根据文件id)
// bucketType: 1 公开空间 2 私密空间
export const getFilesDownloadUrl = (fileId, config, bucketType = 2) => {
  return fileApi.get(
    `download/saveas?fileId=${fileId}&bucketType=${bucketType}`,
    config
  );
};

// 提交作业
export const postHomework = (taskId, body, config) => {
  return o2oApi.post(`study/homework/${taskId}`, body, config);
};

// 提交草稿作业
export const postDraftHomework = (taskId, body, config) => {
  return o2oApi.post(`/study/homework/draft/${taskId}`, body, config);
};

/**
 * 提交协同作业
 * @param {String} homeworkId
 * @param {Object} data
 * @returns
 */
export const submitCooperationHwk = (homeworkId, data, config) => {
  return o2oApi.post(`study/teamwork/${homeworkId}`, data, config);
};

export const submitDraftCooperationHwk = (homeworkId, data, config) => {
  return o2oApi.post(`study/teamwork/draft/${homeworkId}`, data, config);
};

// 作业模版详情 (管理端)
export const getHomeworkTemplateDetail = id => {
  return o2oApi.get(`homework/template/detail?templateId=${id}`);
};

/**
 * 获取文件转码状态
 * @param {String} fileId:
 * @returns
 */
export const getFileTranscodeState = fileId => hwbaseApi.get(`attachment/status?fileId=${fileId}`);

/**
 * 获取文件下载 地址
 * @param {String} fileId
 * @returns
 */
export const getFileDownloadUrl = fileId => hwbaseApi.get(`attachment/download?fileId=${fileId}`);

/**
 * 获取文件播放地址列表
 * 沿用以前的接口传参，video 的时候 为1 没看懂
 * @returns
 */
export const getFilePlayUrls = (fileId, type) => hwbaseApi.get(`attachment/play-list?${qs.stringify({ fileId, clientType: type === 'video' ? 1 : '' })}`);

/**
 * 语音转文字
 * @param {String} fileId
 * @returns
 */
export const convertVioce2Text = fileId => hwbaseApi.get(`attachment/speech2text?fileId=${fileId}`);

// 分页获取学员提交的精华作业信息
export const getEssentailCommunion = (params) => {
  return hwbaseApi.post(`remark/listuseressentialcommunion?${qs.stringify(params)}`);
};

export const getStuTeachers = (projectId, userId = '', config = {}) => {
  return o2oApi.get(`ojt/my/teacher?projectId=${projectId}&userId=${userId}`, config);
};

// 学员端-带教导师审核状态
export const getAuditTeacherStatus = (taskId, params) => {
  return o2oApi.get(`appraisal/auditteacher/status/${taskId}`, { params });
};

// 获取项目负责人（分页）
export const getProjectManage = (projectId, params) => {
  return o2oApi.get(`project/client/principal/${projectId}`, { params });
};

// 获取AI批阅结果
export const getAIReviewResult = (params) => {
  return hwbaseApi.get('communion/getintellectreviewresult', { params });
};
