<template>
  <div class="yxtulcdsdk-o2o-homework-task yxtulcdsdk-ulcdsdk minh100 pr" :class="{'mt24': !params.preview && !params.isPdf && !inner}">
    <div
      v-loading="workLoading || downloadLoading"
      class="flex-1 bg-white pr"
      :class="{'hline over-hidden': downloadLoading}"
    >
      <template v-if="basicinfo">
        <!-- 导出作业处理 -->
        <yxtf-tooltip
          v-if="!params.isPdf && !isEssential"
          class="item"
          trigger="hover"
          :content="$t('pc_o2o_btn_download_homework')"
          placement="top"
        >
          <div
            v-if="(basicinfo && basicinfo.status >= 2) && state !== 0 && !checkCooperHwk"
            class="o2o-hw-download"
            @click="downloadPdf"
          >
            <yxt-svg
              class="c-59"
              :remote-url="`${$staticBaseUrl}assets/573b6947/8808ea99`"
              width="14px"
              height="14px"
              icon-class="download"
            />
          </div>
        </yxtf-tooltip>

        <!-- 作业详情 -->
        <div class="hline width-percent-100">
          <yxtpd-download-pdf
            ref="downloadPdf"
            :file-name="pdfName"
            :page-url="pdfUrl"
            :query="pdfQuery"
            pure-name
          />
          <div
            class="center-block pr ph24 box-border"
            :class="[`o2o-hwk-block${isAiHw ? `-ai${isExpandAIReview ? '-expand' : ''}` : ''}`, isEssential ? 'pt32': '']"
          >
            <div
              v-if="!isEssential"
              class="radius4 pb40 pt32"
            >
              <Basicinfo
                v-if="basicinfo"
                :data="basicinfo"
                :score-type="scoreType"
                :need-remark-status="needRemarkStatus"
                :preview="params.preview"
                @voiceState="setVoiceState"
              />
            </div>
            <div class="o2o-homework-multiple-topics radius4 pb24">
              <!-- 非精华作业的展示 -->
              <div
                v-if="!isEssential"
                class="font-size-20 lh28 weight-bold"
              >
                {{ $t('pc_o2o_lbl_homeworkcontent') }}
                <div v-if="submitTime" class="font-size-12 lh28 pull-right lh20 color-gray-7">
                  <span>{{ $t('pc_o2o_lbl_submittime' /* 提交时间 */) + $t('pc_o2o_lbl_colon') }}</span>
                  <span class="ml4">{{ submitTime ? dateFormat(submitTime, 'yyyy-MM-dd hh:mm'): '--' }}</span>
                </div>
              </div>
              <!-- 查看精华作业的展示 -->
              <div v-else class="flex mb6">
                <yxtf-portrait
                  size="36px"
                  :img-url="essentialInfo.studentIcon"
                  :username="essentialInfo.studentName"
                  class="mr8"
                />
                <div class="flex-1">
                  <div class="ellipsis">
                    {{ essentialInfo.studentName }}
                  </div>
                  <div class="standard-size-12 color-gray-7">
                    <span>
                      {{ $t('pc_ulcdsdk_lbl_submit_time'/** 提交时间： */) }}{{ essentialInfo.submitTime }}
                    </span>
                    <span class="ml20">
                      {{ essentialInfo.studentDept }}
                    </span>
                  </div>
                </div>
              </div>
              <div
                v-for="(item, index) in itemCommunionDetails"
                :key="index"
              >
                <div
                  v-if="multiItem"
                  class="font-size-16 hand layout-flex word-break-all pt32 weight-bold"
                  @click="item.isOpen=!item.isOpen"
                >
                  <div class="lh18">
                    <yxt-ulcd-sdk-svg
                      icon-class="o2o-ojt-arrow"
                      class="v-mid color-gray-7 font-size-16 o2o-back-task-svg text-no-select mr8"
                      :class="{'o2o-myteaching-task-svg':!item.isOpen}"
                      width="16px"
                      height="16px"
                      module="o2o"
                    />
                  </div>
                  <div> {{ index+1 }}.{{ item.itemName }}</div>
                </div>
                <div
                  v-if="item.isOpen"
                  :class="{'ml24': multiItem}"
                >
                  <O2o-wrap-line
                    v-if="item.itemDescription&&multiItem"
                    line-height="22px"
                    class="pt8 font-size-14 color-8c"
                    :content="item.itemDescription"
                    :row="item.row"
                    :hide-on-extend="item.hideOnExtend"
                  >
                    <yxt-link
                      slot="right-icon"
                      :underline="false"
                      class="v-top ml2"
                      :class="{'pull-right':item.hideOnExtend}"
                      @click="extendMore(item)"
                    >
                      {{ item.hideOnExtend?$t('pc_o2o_btn_expand'):$t('pc_o2o_btn_stow') }}<i :class="item.hideOnExtend?'yxt-icon-arrow-down':'yxt-icon-arrow-up'"></i>
                    </yxt-link>
                  </O2o-wrap-line>
                  <div
                    v-if="item.workId"
                    class="mt16"
                  > <Open-xuanke-url
                    :item-id="item.itemId"
                    :task-id="taskId"
                    :xuanke-info="params.xuankeInfo"
                  /></div>
                  <div :class="state !== 0&&multiItem?'mt20 pl20 font-size-14 pr20 o2o-homework-multiple-userline pb30':''">
                    <!-- 编辑状态 -->
                    <template v-if="state === 0">
                      <!-- type: 1 图片 2 文字 3 文档 4 视频 5 语音 6 音频 -->
                      <!-- 文本输入 -->
                      <yxtf-input
                        v-if="item.homeWorkTypes.includes(2)"
                        v-model="item.submitContent"
                        class="mt22"
                        :disabled="params.preview || startTimeNotAssigned || endTimeLimited"
                        type="textarea"
                        :placeholder="placeholder"
                        maxlength="5000"
                        :autosize="{ minRows: 4}"
                        show-word-limit
                      />
                      <!-- 附件上传按钮 -->
                      <UploadAttach
                        v-if="isAiHw ? !item.attachments.length : true"
                        :disabled="params.preview || startTimeNotAssigned || endTimeLimited"
                        :type="item.homeWorkTypes.join(',')"
                        :attach-list="item.attachments"
                        :is-ai-hw="isAiHw"
                        class="mb8"
                        :class="isAiHw ? 'mt12' : 'mt8'"
                      />
                    </template>
                    <!-- 只读状态, 作业内容 -->
                    <Detail
                      v-else
                      :multi-item="multiItem"
                      :content="item.submitContent"
                      :is-pdf="params.isPdf"
                    />
                    <!-- 其他附件列表 -->
                    <AttachmentList
                      v-if="!params.preview"
                      :list="item.attachments.filter(f => f.fileType !== 'voice')"
                      :is-edit="state === 0"
                      @del="deleteAttach"
                      @voiceState="setVoiceState"
                    />
                    <!-- 录音列表 -->
                    <VoiceBarList
                      v-if="!params.preview"
                      :factor="params.factor"
                      :hw-state="hwState"
                      :list="item.attachments.filter(f => f.fileType === 'voice')"
                      :is-edit="state === 0"
                      width="596px"
                      :class="item.attachments.filter(f => f.fileType === 'voice').length===0 ? '':'mt24'"
                      @del="deleteAttach"
                      @voiceState="setVoiceState"
                    />
                  </div>
                </div>
              </div>

              <ai-container v-if="!!aiFeedback" ref="aiContainer">
                <div ref="aiContent" class="ph24 pv24">
                  <div class="yxtulcdsdk-flex-center">
                    <yxt-ulcd-sdk-svg
                      width="20px"
                      height="20px"
                      module="o2o"
                      icon-class="hw-stu-ai"
                    />

                    <span class="comment-ai-text">{{ $t('pc_ulcdsdk_lbl_ai_comment' /* AI点评 */) }}</span>
                  </div>

                  <div class="mt16 color-gray-7 font-size-12 lh20 text-left">{{ $t('pc_ulcdsdk_msg_content_generated_by_ai' /* 内容由AI生成，请自行判断并谨慎采用 */) }}</div>

                  <div class="mt8 lh22 color-gray-10 text-left" v-html="dealLineFeed(aiFeedback)">
                  </div>
                </div>
              </ai-container>
              <!-- state 0-新作业 1-批阅完成 2-被退回 3-已阅 4-未阅 5-不需要批阅且已完成 6 批阅中-->
              <!-- state ai批阅完成后也可以编辑，1-批阅完成后 -->
              <!-- AI作业-人工批阅后不允许再次修改 -->
              <div
                v-if="(isAiHw ? ([1, 3, 4, 6, 7].includes(state) && !taskResult.instructorUserName) : [2, 3, 4, 5].includes(state)) && !params.isPdf && !isEssential"
                class="pt32 text-center pr24"
              >
                <yxt-tooltip
                  :content="$t('pc_ulcdsdk_msg_assignment_ai_review_in_progress' /* 作业正在AI批阅中，请等待批阅完成 */)"
                  placement="top"
                  :disabled="(startTimeNotAssigned || endTimeLimited) ? false : !isDisabledSubmitHw"
                >
                  <!-- 编辑、重新编辑 -->
                  <yxt-button
                    type="primary"
                    :disabled="startTimeNotAssigned || endTimeLimited || isDisabledSubmitHw"
                    @click="handleEdit"
                  >{{ state === 2 ? $t('pc_o2o_btn_edit') : $t('pc_o2o_lbl_reedit') }}</yxt-button>
                </yxt-tooltip>
              </div>

              <!-- state 0-新作业 -->
              <div
                v-if="state === 0 && !params.preview && !isEssential"
                class="pt24 o2o-flex-center-center pr24"
              >
                <!-- 保存 -->
                <yxtf-button
                  v-if="!(startTimeNotAssigned || endTimeLimited) && isSave"
                  :size="isAiHw ? 'larger' : ''"
                  :class="{'min68 font-size-14': isAiHw}"
                  :loading="saveLoading"
                  :disabled="saveDisabled"
                  @click="handleSave"
                >
                  {{ $t('pc_o2o_btn_save') }}
                </yxtf-button>

                <template v-if="state === 0">
                  <yxt-tooltip
                    v-if="isAiHw && !endTimeLimited"
                    :key="1"
                    :content="$t('pc_ulcdsdk_msg_assignment_not_filled' /* 作业未填写，请检查 */)"
                    placement="top"
                    :disabled="(startTimeNotAssigned || submitting) ? true : !saveDisabled"
                  >
                    <!-- AI提交批阅 -->
                    <div
                      class="o2o-newtask-ai-submit__btn"
                      :class="{'disabled': startTimeNotAssigned || submitting || saveDisabled }"
                      @click="submit(true)"
                    >
                      <yxt-ulcd-sdk-svg
                        width="16px"
                        height="16px"
                        module="o2o"
                        icon-class="o2o-ai-btn"
                      />
                      <span class="ml4">{{ $t('pc_ulcdsdk_btn_submit_ai_review' /* 提交AI批阅 */) }}</span>
                    </div>
                  </yxt-tooltip>

                  <!-- 提交 -->
                  <yxtf-button
                    v-else
                    :disabled="startTimeNotAssigned || endTimeLimited"
                    type="primary"
                    :loading="submitting"
                    @click="submit"
                  >
                    {{ endTimeLimited ? $t('pc_ulcdsdk_lbl_ended' /* 已结束 */) : $t('pc_o2o_lbl_submit') }}
                  </yxtf-button>
                </template>
              </div>
              <p v-if="startTimeNotAssigned" class="text-center lh-1 mt16 c-8c">
                {{ basicinfo.startTime | formatTime }} {{ $t('pc_pd_lbl_homework_submit' /* 可提交作业 */) }}
              </p>
              <ojt-tip
                ref="ojtTip"
                :show-continue="canTransfer"
                :project-id="pid"
                :audit-status="auditStatus"
                :student-choose-teacher="!!workDetail.studentChooseTeacher"
                :ojt-teacher-info="ojtTeacherInfo"
                @apply="submitHomeWork"
                @update="afterOjtSelect"
              />

              <template v-if="isAiHwReviewEnd">
                <!-- 批阅评语 communionRemarkStatus 1 已批阅 2-进行中 -->
                <Comments
                  v-if="taskResult.communionRemarkStatus === 1 && needRemarkStatus"
                  is-stu
                  :score-type="scoreType"
                  :task-result="taskResult"
                  :attach-list="reviewAttachList"
                  :scoring-rule="scoringRule"
                  :is-ai-hw="isAiHw"
                />
                <div v-if="isAiHw ? taskResult.instructorUserName : isEssential" class="standard-size-12 color-gray-7 mt8">
                  <div>
                    {{ $t('pc_o2o_lbl_remarkperson') }}:<yxtbiz-user-name class="ml4" :name="taskResult.instructorFullName" />
                  </div>
                  <div class="mt8">
                    {{ $t('pc_o2o_lbl_remarktime') }}:<span class="ml4">{{ taskResult.updateTime | formatTime }}</span>
                  </div>
                </div>

                <div v-if="isAiHw && !taskResult.instructorUserName" class="standard-size-12 color-gray-7 mt8">
                  {{ $t('pc_biz_common_generate_by_ai' /* 内容由AI生成，仅供参考 */) }}
                </div>
              </template>
            </div>

            <template v-if="isAiHwReviewEnd && !isEssential">
              <!-- 批阅记录 -->
              <yxt-tabs
                v-if="(essenceSetOperation === 1 || (taskResult.communionRemarkStatus === 1 && essenceSetOperation === 2)) && !params.isPdf"
                v-model="tabsActive"
              >
                <yxt-tab-pane
                  :label="$t('pc_pd_hw_label_essential'/** 精华作业 */)"
                  name="essential"
                >
                  <essential-list
                    :id="targetId"
                    ref="essentialList"
                    @toEssentialDetail="toEssentialDetail"
                  />
                </yxt-tab-pane>
                <!-- AI作业不显示批阅纪录 -->
                <yxt-tab-pane
                  v-if="state !== 0 && state !== 5 && !isAiHw"
                  :label="$t('pc_ulcdsdk_review_records'/** 批阅记录 */)"
                  name="record"
                >
                  <div class="pb20">
                    <yxtpd-identify-flow
                      v-if="!params.preview"
                      ref="records"
                      :service="hwService"
                      :task-id="taskId"
                      :user-id="userIdOrGroupId"
                      :bizz-type="params.bizzType"
                      web
                      hide-title
                      :can-view-detail="!params.isPdf"
                      @dataLoaded="recordsLoaded"
                    />
                  </div>
                </yxt-tab-pane>
              </yxt-tabs>
              <div
                v-else-if="$route.query.isEssential !== 'true' && !isAiHw"
                v-show="isShowRecords"
                class="pb32 radius4"
              >
                <div>
                  <yxtf-divider v-if="isShowRecords" class="mt0" />
                  <div v-if="isShowRecords" class="ellipsis color-gray-10 lh26 weight-bold font-size-18 mb24">
                    {{ $t('pc_o2o_lbl_reviewlog' /* 批阅记录 */) }}
                  </div>
                  <yxtpd-identify-flow
                    v-if="!params.preview"
                    ref="records"
                    :service="hwService"
                    :task-id="taskId"
                    :user-id="userIdOrGroupId"
                    :bizz-type="params.bizzType"
                    web
                    hide-title
                    :can-view-detail="!params.isPdf"
                    @dataLoaded="recordsLoaded"
                  />
                </div>
              </div>
            </template>
          </div>
        </div>
      </template>
    </div>

    <!-- 右侧AI批阅纪录 -->
    <template v-if="!params.isPdf">
      <div v-show="!downloadLoading && !isEssential && aiReviewStatus !== null" class="ml12 mr12">
        <!-- AI批阅详情数据 -->
        <yxtbiz-ai-hw-review
          v-show="isExpandAIReview"
          ref="aiReview"
          :review-status="aiReviewStatus"
          :review-data="aiReviewData"
          is-stu
          :is-inner="inner"
          @hide-ai-review="isExpandAIReview = false;"
          @get-review-detail="getReviewDetail"
        />
        <!-- AI批阅收起图标 -->
        <div v-show="!isExpandAIReview" class="o2o-newtask-ai-sticky">
          <div class="o2o-newtask-ai-container" @click="isExpandAIReview = true;">
            <div class="container-bg">
              <yxt-ulcd-sdk-svg
                width="24px"
                height="24px"
                module="o2o"
                icon-class="hw-zk"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import Basicinfo from './components/basic-info.vue';
import O2oWrapLine from './components/wrap-line.vue';
import UploadAttach from './components/upload-attach/index.vue';
import OpenXuankeUrl from './components/open-xuanke-url.vue';
import Detail from './components/detail.vue';
import AttachmentList from './components/attachment-list.vue';
import VoiceBarList from './components/voice-bar-list/index.vue';
import Comments from './components/comments.vue';
import { getAuditTeacherStatus, getStuTeachers, postHomework, submitCooperationHwk, postDraftHomework, submitDraftCooperationHwk, getAIReviewResult } from './service';
import pagehideStopAudio from './mixins/pagehide-stop-audio.js';
import delFileConfirm from './components/voice-bar-list/del-file-confirm';
import pcPreview from './mixins/pc-preview';
import { formatSvFileData, checkImgExists } from './utils';
import { fetchPlayUrls } from './components/reference-list/diff-o2o-homewokengine';
import mixin from 'packages/_mixins/task';
import { dateFormat } from 'packages/_utils/core/date-format';
import { getTimeStamp } from 'packages/_utils/core/utils.js';
import EssentialList from './components/essential-list.vue';
import OjtTip from './components/ojtTip.vue';
import {getHomeworkDetail, getCooperationHwk} from 'packages/_services/taskDetail.service';
import AiContainer from './components/ai-container.vue';
import Svg from 'packages/_components/svg.vue';
import { dealLineFeed } from 'packages/examing/src/core/utils.js';

export default {
  name: 'YxtUlcdSdkO2oHomeworkTask',
  detailMethod: 'formatHomeWordDetail',
  mixins: [mixin, pagehideStopAudio, delFileConfirm, pcPreview],
  props: {
    params: {
      type: Object,
      default: () => ({
        service: 'o2o', //  区分 o2o 和 作业引擎的服务 'o2o', 'hwkEngine'
        xuanKeInfo: {},
        factor: {
          transferText: false
        },
        preview: false,
        submitting: false,
        isPdf: false, // 是否pdf下载样式
        groupId: '',
        bizzType: 0,
        taskId: '',
        btid: '',
        checkCooperHwk: false // 协同作业
      })
    },
    fromStudyRecord: Boolean,
    inner: Boolean,
    checkedPermission: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    params: {
      handler(data) {
        this.taskId = data.taskId;
        this.hwService = data.service || 'o2o';
        this.preview = data.preview;
        this.checkCooperHwk = data.checkCooperHwk;
        this.bthid = data.btid || '';
        // this.init();
      },
      immediate: true,
      deep: true
    },
    userId() {
      this.init();
    }
  },
  components: {
    O2oWrapLine,
    Basicinfo,
    OpenXuankeUrl,
    UploadAttach,
    Detail,
    AttachmentList,
    VoiceBarList,
    Comments,
    EssentialList,
    OjtTip,
    AiContainer,
    [Svg.name]: Svg
  },
  data() {
    // AI批阅状态枚举
    const AI_REVIEW_STATUS = {
      GENERATING: 0, // 生成中
      PARSING: 2, // 文档解析中
      COMPLETED: 1, // 完成
      FAILED: -1, // 失败
      PARSE_FAILED: -2, // 文档解析失败
      CAPACITY_EXHAUSTED: -3 // AI批阅容量耗尽
    };

    return {
      AI_REVIEW_STATUS,
      hwService: 'o2o',
      itemCommunionDetails: [],
      multiItem: false, // 当前作业是否是多题模式，true-是、false-否
      pid: null,
      basicinfo: null, // 基础信息
      attachList: [], // 上传附件列表
      reviewAttachList: [], // 批阅附件列表
      commentData: null, // 批阅数据
      taskResult: {},
      type: '', // 1 图片 2 文字 3 文档 4 视频 5 语音 6 音频
      state: -1, // 0-新作业 1-批阅完成 2-被退回 3-已阅 4-未阅 5-不需要批阅且已完成 6 批阅中
      scoreType: -1, // 打分方式（0-直接打分 1-是否合格 2-不评分）
      content: '', // 作业内容 - 文本, 不限类型、图文有
      placeholder: '',
      submitTime: '',
      finishStandard: 0, // 作业完成标准：0-提交作业 1-批阅合格
      pdfName: '', // 生成的pdf名称
      pdfUrl: '', // 生成的pdf地址
      downloadLoading: false, // pdf loading
      pdfQuery: {
        autoHeight: 1,
        autoContainer: '#app'
      },
      isShowRecords: false,
      hwState: {
        voiceState: ''
      },
      communionDetailsStr: [],
      isLeave: false,
      scoringRule: null, // 评分规则
      needRemarkStatus: 0, // 是否需要批阅
      startTimeNotAssigned: false,
      endTimeLimited: false, // 是否开启结束后不允许提交并且已经结束
      bthid: '',
      essenceSetOperation: 0, // 作业精华状态
      tabsActive: 'essential',
      targetId: '', // 作业id
      essentialParams: {
        taskId: ''
      },
      isEssential: false,
      essentialInfo: {},
      saveLoading: false,
      submitting: false,
      isSave: true,
      workDetail: {
        processOnlyOjt: null,
        hasTeacher: null,
        studentChooseTeacher: null,
        auditOpened: null,
        noAuditer: null
      },
      ojtTeacherInfo: {
        canChooseTe: null,
        ojtMode: null,
        period: null,
        projectId: null
      },
      auditStatus: 0,
      aiFeedback: '',
      reviewMethod: 1, // 批阅方式
      aiReviewStatus: null, // ai批阅状态
      isExpandAIReview: false, // 是否展开了AI批阅
      pollingTimer: null,
      isUpdateReviewResult: false, // 是否是更新了批阅结果数据
      aiReviewData: {}, // ai批阅的数据汇总
      clickEssenceState: -1 // 点击作业精华时如果点击了编辑，则需要重置state数据
    };
  },
  computed: {
    // 是否禁用提交作业
    isDisabledSubmitHw() {
      return [
        this.AI_REVIEW_STATUS.GENERATING,
        this.AI_REVIEW_STATUS.PARSING
      ].includes(this.aiReviewStatus);
    },
    isOjtAndWithoutTeacher() {
      return this.needRemarkStatus && this.workDetail.processOnlyOjt && !this.workDetail.hasTeacher;
    },
    canTransfer() {
      // noAuditer 批阅人为空时：0自动转交（项目负责人） 1转交给指定成员处理 2自动通过 5：不转交"
      return this.workDetail.noAuditer !== 5;
    },
    // 其他附件列表
    otherList() {
      return this.attachList.filter(f => f.fileType !== 'voice');
    },
    // 语音列表
    voiceList() {
      return this.attachList.filter(f => f.fileType === 'voice');
    },
    userIdOrGroupId() {
      return Number(this.params.bizzType) === 4 && this.hwService === 'o2o' ? this.params.groupId : this.userId;
    },
    saveDisabled() {
      return this.itemCommunionDetails && !this.itemCommunionDetails.some(item=>item.submitContent || (item.attachments && item.attachments.length));
    },
    isAiHw() {
      return this.reviewMethod === 3;
    },

    // ai批阅完成
    aiReviewEnd() {
      return [this.AI_REVIEW_STATUS.COMPLETED, this.AI_REVIEW_STATUS.FAILED, this.AI_REVIEW_STATUS.PARSE_FAILED, this.AI_REVIEW_STATUS.CAPACITY_EXHAUSTED].includes(this.aiReviewStatus);
    },

    isAiHwReviewEnd() {
      return this.isAiHw ? this.aiReviewEnd : true;
    }
  },
  created() {
    this.$root.$on('GOBACK', this.goBack);
  },
  mounted() {
    this.init();

    window.addEventListener('resize', this.setAiContainerHeight);
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.setAiContainerHeight);
    });
  },
  beforeDestroy() {
    this.setBreadcrumbUpdate(false);
    this.$root.$off('GOBACK', this.goBack);
    // 清除轮询定时器
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer);
      this.pollingTimer = null;
    }
  },
  methods: {
    dealLineFeed,
    goBack() {
      this.isEssential = false;
      this.isLeave = true;
      this.userId = localStorage.getItem('userId') || this.$route.query.uid;
      this.init();
      this.setBreadcrumbUpdate(false);
    },
    setBreadcrumbUpdate(v) {
      const breadcrumb = {
        show: v,
        list: [{ name: this.$t('pc_o2o_lbl_homework'), eventName: 'GOBACK' }, { name: this.$t('pc_pd_hw_label_essential'/* 精华作业 */) }]
      };
      this.$root.$emit('SETBREADCRUMBUPDATE', breadcrumb);
    },
    dateFormat,
    setVoiceState() {
      this.hwState.voiceState = Date.now();
    },
    extendMore(item) {
      // 展开更多
      item.row = item.hideOnExtend ? 0 : 2;
      item.hideOnExtend = !item.hideOnExtend;
    },
    // 详情
    formatHomeWordDetail(d) {
      this.pid = d.projectId;
      // 作业类型( '1,2...' 1 图片 2 文字 3 文档 4 视频 5 语音 6 音频 )
      this.type = d.subType || '1,2,3,4,5,6,7';
      // 打分方式（0-直接打分 1-是否合格 1-不评分）
      this.scoreType = d.scoreType;
      // 作业内容文本框提示语
      this.placeholder = d.reviewMethod === 3 ? this.$t('pc_o2o_lbl_input_ai_holder' /* 请在此处输入作业内容 */) : this.$t('pc_o2o_lbl_inputLinkBlank' /* 请在此处输入作业内容，如需输入链接，则请在链接末尾添加空格 */);
      // 作业完成标准
      this.finishStandard = d.finishStandard;
      // 是否做过作业
      const taskResult = d.taskResult[0];
      // 是否展示精华作业
      this.essenceSetOperation = d.essenceSetOperation;
      // 作业id
      this.targetId = d.targetId;
      // 批阅方式
      this.reviewMethod = d.reviewMethod;
      // 基础信息
      this.basicinfo = {
        name: d.name, // 标题
        projectName: d.projectName, // 项目名称
        totalScore: d.totalScore, // 总分
        passScore: d.passScore, // 通过分数
        startTime: d.startTime, // 开始时间
        endTime: d.endTime, // 结束时间
        description: d.description, // 作业要求
        status: d.status, // 项目状态
        attachments: d.attachments || [], // 附件
        hwTips: d.hwTips
      };

      this.workDetail = {
        processOnlyOjt: d.processOnlyOjt,
        hasTeacher: d.hasTeacher,
        studentChooseTeacher: d.studentChooseTeacher,
        auditOpened: d.auditOpened,
        noAuditer: d.noAuditer
      };

      this.startTimeNotAssigned = (d.startTime && getTimeStamp(d.startTime) > Date.now());
      if (d.endTime && d.overdueForbiddenSubmitSwitch) {
        const endTimeInMillis = getTimeStamp(d.endTime);
        this.endTimeLimited = endTimeInMillis < Date.now();
      }

      this.$emit('hwInfo', { ...d, ...{ pid: this.pid, basicinfo: this.basicinfo } });
      this.multiItem = taskResult.multiItem;
      this.itemCommunionDetails = [];
      this.submitTime = taskResult.submitTime;
      this.scoringRule = d.scoringRule; // 评分规则
      this.needRemarkStatus = d.needRemarkStatus; // 是否需要批阅
      const itemDetail = taskResult.itemDraftDetails || taskResult.itemCommunionDetails;
      itemDetail && itemDetail.forEach((item, index) => {
        let hwkContent = {
          workId: item.workId,
          itemId: item.itemId,
          itemName: item.itemName,
          row: 2,
          hideOnExtend: true,
          isOpen: index < 50,
          itemDescription: item.itemDescription,
          attachments: this.getAttachments(item.submitAttachments),
          submitContent: item.submitContent,
          desc: item.desc,
          xuanKeUrl: item.xuanKeUrl,
          homeWorkTypes: item.homeWorkTypes || '1,2,3,4,5,6,7'
          // submitTime: item.submitTime
        };
        this.itemCommunionDetails.push(hwkContent);
        this.communionDetailsStr.push({ ...hwkContent, attachments: this.getAttachments(item.submitAttachments) });
      });

      // 人工+AI批阅处理
      if (d.reviewMethod === 2) {
        this.aiFeedback = d.feedback;
      }

      if (!taskResult || (taskResult.status === 0 && taskResult.valid !== 0)) {
        this.state = 0;
      } else {
        // 作业内容
        this.content = taskResult.answer || '';
        taskResult.attachments = taskResult.attachments || [];
        const { attachments, ...result } = taskResult;
        this.taskResult = result;
        const [attachList, reviewAttachList] = [[], []];
        // 附件列表
        attachments.forEach(item => {
          if (item.fileType === 'image') { // 图片不需要等待转码
            item.status = 1;
            this.setErrorImgUrl(item);
          }
          if (item.fileSubType === 'voice') { // 录音
            item.fileType = 'voice';
            item.playState = 2; // 录音播放状态 1-播放中 2-暂停 3-加载中
            item.progress = 0; // 播放进度
          }
          if (item.type === 3) {
            reviewAttachList.push(item);
          } else {
            attachList.push(item);
          }
        });
        this.attachList = attachList;
        this.reviewAttachList = reviewAttachList;
        // 状态
        if (taskResult.replyStatus === 1) { // 已批阅（0-未批阅 1-已批阅）
          if (!this.needRemarkStatus) {
            this.state = 5;
          } else if (taskResult.status === 2) { // 已完成(包含合格、不合格)
            this.state = 1;
          } else if (taskResult.status === 4) { // 被退回
            this.state = 2;
          } else if (this.isAiHw && taskResult.status === 1 && this.finishStandard === 1) {
            // 合格即完成 & 作业处于进行中 & ai作业
            this.state = 7;
          }
        } else { // 未批阅
          if (taskResult.communionRemarkStatus === 2) {
            this.state = 6;
          } else if (taskResult.readed === 1) { // 已阅（（0否，1是））
            this.state = 3;
          } else { // 未阅
            this.state = 4;
          }
        }

        if (taskResult.valid === 0) {
          this.state = -1;
        }
      }

      this.getReviewHistory();
      this.setAiContainerHeight();

      if (this.params.isPdf) {
        this.aiReviewStatus = d.aiReviewStatus;
      } else {
        // 如果是AI批阅，则调用此接口获取AI状态
        // 导出pdf不请求作业ai请求
        d.communionId && !this.isUpdateReviewResult && this.getAIReviewResult(d.communionId);
      }
    },
    getAttachments(attachments) {
      let attachment = [];
      // 附件列表
      attachments && attachments.forEach(item => {
        if (item.fileType === 'image') { // 图片不需要等待转码
          item.status = 1;
          this.setErrorImgUrl(item);
        }
        if (item.fileSubType === 'voice') { // 录音
          item.fileType = 'voice';
          item.playState = 2; // 录音播放状态 1-播放中 2-暂停 3-加载中
          item.progress = 0; // 播放进度
        }
        attachment.push(item);
      });
      return attachment;
    },
    // 获取历史批阅记录
    getReviewHistory() {
      this.$nextTick(() => {
        this.$refs.records && this.$refs.records.getReviewHistory();
      });
    },

    setAiContainerHeight() {
      this.$nextTick(() => {
        if (!this.$refs || !this.aiFeedback) return;
        const aiContent = this.$refs.aiContent;
        const aiContainer = this.$refs.aiContainer;
        const aiContentHeight = aiContent.clientHeight ? aiContent.clientHeight + 2 : 160;
        aiContainer.$el.style.height = `${aiContentHeight}px`;
      });
    },

    handleEdit() {
      this.clickEssenceState = this.state;
      this.state = 0;
      this.isSave = false;
    },
    // 作业保存
    handleSave() {
      if (!this.isSave) return;
      const submitItems = this.beforeSubmit(true);
      if (!submitItems) return;
      this.saveLoading = true;
      const body = {
        batchId: this.batchid || this.bthid,
        itemCommunions: submitItems,
        type: 1, // 0草稿 1提交
        submitModel: 0 // 0-pc 1-app
      };
      (this.checkCooperHwk ? submitDraftCooperationHwk : postDraftHomework)(this.taskId, body, { module: 'o2o' }).then(() => {
        this.$message.success(this.$t('pc_o2o_tip_savesuccess'));
        this.isLeave = true;
        this.getDetail();
      }).catch(err => {
        if (err && err.key === 'apis.hw.has.finished') {
          this.$message.warning(this.$t('pc_o2o_hw_over_tip'/* 作业已结束 */));
        }
      }).finally(() => { this.saveLoading = false; });
    },
    beforeSubmit(isComplete) {
      let uploadingFileExisted = false;
      let ischeckanswer = false;
      let saveItems = JSON.parse(JSON.stringify(this.itemCommunionDetails));
      saveItems = saveItems.map(item => {
        let {
          attachments,
          itemId,
          submitContent
        } = item;
        if (!uploadingFileExisted) {
          uploadingFileExisted = attachments.some(file => file.loading);
        }
        if (!submitContent && attachments.length === 0) {
          ischeckanswer = true;
        }
        attachments = formatSvFileData(attachments, this.pid);
        return {
          attachments,
          itemId,
          submitContent
        };
      });
      if (uploadingFileExisted) {
        this.$fmessage.warning(this.$t('pc_o2o_msg_wait_upload' /* 您有附件正在上传，请稍后再试 */));
        return;
      }
      if (ischeckanswer && !isComplete) {
        // 作业内容未填写，请检查
        this.$fmessage.warning(this.$t('pc_o2o_msg_pleasecheckcontent'));
        return;
      }
      return saveItems;
    },
    async ojtTipShow() {
      // 重新获取详情信息，确保数据及时性
      let d = null;
      if (this.checkCooperHwk) {
        const { taskId: homeworkId, userId } = this;
        d = await getCooperationHwk({ homeworkId, userId });
      } else {
        d = await getHomeworkDetail(this.taskId, this.userId, '0', this.batchid || this.bthid);
      }

      if (d) {
        this.workDetail = {
          processOnlyOjt: d.processOnlyOjt,
          hasTeacher: d.hasTeacher,
          studentChooseTeacher: d.studentChooseTeacher,
          auditOpened: d.auditOpened,
          noAuditer: d.noAuditer
        };
        if (this.workDetail.hasTeacher) {
          // 如果重新获取详情后已经有导师，则直接申请,不用打开弹窗了
          this.submitHomeWork();
          return;
        }
      }
      // 获取带教信息
      const ojtRes = await getStuTeachers(this.pid, localStorage.getItem('userId'));
      this.ojtTeacherInfo = {
        canChooseTe: ojtRes.canChooseTe,
        ojtMode: ojtRes.ojtMode,
        period: ojtRes.period,
        projectId: this.pid
      };
      if (!this.workDetail.studentChooseTeacher && this.workDetail.auditOpened) {
        // 师徒互选判断审核中, (数据库 师徒互选-学员选择导师 0:选择 1：没选择 )
        await this.getAuditStatus();
      } else {
        // 未开启审核认为无导师
        this.auditStatus = false;
      }
      this.$refs.ojtTip && this.$refs.ojtTip.show();
    },
    async getAuditStatus() {
      const res = await getAuditTeacherStatus(this.taskId).catch(() => {});
      this.auditStatus = res.auditStatus;
    },
    async afterOjtSelect() {
      this.$refs.ojtTip && this.$refs.ojtTip.hide();
    },
    submit(isAI = false) {
      if ((isAI && this.saveDisabled) || this.startTimeNotAssigned || this.submitting) return;

      if (isAI) {
        this.aiReviewStatus = null;
        this.aiReviewData = {};
        this.isExpandAIReview = false;
        this.isUpdateReviewResult = false;
        this.$refs.aiReview && this.$refs.aiReview.resetData();
      }

      if (this.isOjtAndWithoutTeacher) {
        this.ojtTipShow();
      } else {
        this.submitHomeWork();
      }
    },
    submitHomeWork() {
      const submitItems = this.beforeSubmit();
      if (!submitItems) return;
      this.submitting = true;
      const body = {
        batchId: this.batchid || this.bthid,
        itemCommunions: submitItems,
        type: 1, // 0草稿 1提交
        submitModel: 0 // 0-pc 1-app
      };
        // bizzType 4 的时候为协同小作业
      (this.checkCooperHwk ? submitCooperationHwk : postHomework)(this.taskId, body, { module: 'o2o' }).then(() => {
        this.$message.success(this.finishStandard ? this.$t('pc_o2o_tip_homework_save' /* 提交成功，等待批阅合格后算完成该学习任务 */) : this.$t('pc_o2o_tip_savesuccess' /* 保存成功 */));
        this.$emit('updateProgress', 3);
        this.isLeave = true;
        this.getDetail();
        this.getReviewHistory();
      }).catch(err => {
        if (err && err.key === 'apis.hw.has.finished') {
          this.$message.warning(this.$t('pc_o2o_hw_over_tip'/* 作业已结束 */));
        }

        // 您的作业已被批阅，不支持再编辑，请刷新页面
        const errorKey = ['apis.hw.submisson.validation.communion.hasRemarked', 'apis.hw.remark.ai.manager.remarked', 'apis.hw.remark.ai.under.review'];
        if (err && errorKey.includes(err.key)) {
          setTimeout(() => {
            window.location.reload();
          }, 300);
        }
      }).finally(() => { this.submitting = false; });
    },
    // 删除上传文件
    deleteAttach(attach) {
      const itemDetail = this.itemCommunionDetails.find(ele => ele.attachments.some(file => file.fileId === attach.fileId)) || {};
      this.delConfirm(attach, itemDetail.attachments);
    },

    setErrorImgUrl(file) {
      checkImgExists(file.viewUrl).then(() => { }).catch(() => {
        checkImgExists(file.url).then(() => {
          this.$set(file, 'viewUrl', file.url);
        }).catch(async() => {
          if (!file.fileId) return;
          const result = await fetchPlayUrls(this.hwService, file.fileId, file.fileType);
          this.$set(file, 'viewUrl', result.data.datas[0].url);
        });
      });
    },
    // pdf下载
    downloadPdf() {
      this.downloadLoading = true;
      const date = new Date();
      this.pdfName = `${this.basicinfo.projectName}-${this.basicinfo.name}-${date.Format('yyyyMMdd')}`;
      this.pdfUrl = encodeURIComponent(window.location.origin + `/o2o/#/homework/pdf/${this.taskId}?tid=${this.taskId}&isEssential=${this.isEssential}&btid=${this.batchid || this.bthid}`);
      this.$nextTick(() => {
        const { downloadPdf } = this.$refs;
        downloadPdf && downloadPdf.downloadPdf().finally(() => {
          this.downloadLoading = false;
        });
      });
    },
    // 加载完记录数据
    recordsLoaded(data) {
      if (data && data.length && !this.fromStudyRecord) {
        const remarkStatus = data[data.length - 1].remarkStatus;
        if (this.params.isPdf && remarkStatus === 0) {
          // 导出报告在未批阅时不展示批阅记录
          this.isShowRecords = false;
        } else {
          this.isShowRecords = true;
        }
      } else {
        this.isShowRecords = false;
      }
    },
    // 提供给沉浸式外部调用
    confirmLeave(cb = (() => { })) {
      try {
        this.leaveCB = cb;
        this.leaveDialog();
      } catch (error) {
        cb && cb(true);
      }
    },
    leaveDialog() {
      if (this.isEssential || this.isLeave || !this.itemCommunionDetails.length || (JSON.stringify(this.communionDetailsStr) === JSON.stringify(this.itemCommunionDetails))) {
        this.confirmLeaved(true);
        return;
      }

      this.$confirm(this.$t('pc_o2o_lbl_cancel_describe').d('当前操作尚未保存'), this.$t('pc_o2o_give_edit_confirm').d('确定放弃此次编辑吗？'), {
        confirmButtonText: this.$t('pc_o2o_btn_done'),
        cancelButtonText: this.$t('pc_o2o_btn_cancel'),
        type: 'warning'
      }).then(() => {
        this.confirmLeaved(true);
      }).catch(() => {
        this.confirmLeaved(false);
      });
    },
    // 离开前的确认后
    confirmLeaved(leave) {
      if (!this.leaveCB) return;
      // 沉浸式的时候告知播放器是否离开
      this.leaveCB(leave);
      this.leaveCB = undefined;
    },
    toEssentialDetail(item) {
      this.userId = item.studentId;
      this.essentialInfo = item;
      this.isEssential = true;

      if (this.clickEssenceState !== -1) {
        this.state = this.clickEssenceState;
      }

      this.setBreadcrumbUpdate(true);
      this.setAiContainerHeight();
    },

    // 检查是否需要轮询
    needPolling(status, res) {
      return status === null || status === this.AI_REVIEW_STATUS.PARSING || res === null;
    },

    // 轮询获取AI批阅状态
    startPolling(communionId) {
      this.pollingTimer = setInterval(async() => {
        try {
          const res = await getAIReviewResult({ communionId });
          this.setResultVal(communionId, res);

          // 如果不需要继续轮询,则清除定时器
          if (!this.needPolling(this.aiReviewStatus)) {
            this.stopPolling();
          }
        } catch (error) {
          this.handleError(error);
          this.stopPolling();
        }
      }, 2000);
    },

    // 停止轮询
    stopPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
    },

    // 批阅有结果的时候展开
    expandAIReview() {
      // 最终批阅状态才展开
      if (this.aiReviewEnd) {
        this.isExpandAIReview = true;
      }
    },

    // 设置最终的AI结果值
    setResultVal(communionId, res) {
      if (!res) {
        // 如果是文档，则是文档解析中，否则就是生成中
        const { attachments } = this.itemCommunionDetails[0];
        this.aiReviewStatus = (attachments && attachments.length) ? this.AI_REVIEW_STATUS.PARSING : this.AI_REVIEW_STATUS.GENERATING;
        return;
      }
      this.aiReviewStatus = res.aiReviewStatus;
      this.aiReviewData = {
        communionId,
        submissionHistoryId: res.submissionHistoryId,
        homeWorkId: this.targetId,
        scoreType: this.scoreType,
        batchId: this.batchid || this.bthid
      };

      this.expandAIReview();
    },

    async getAIReviewResult(communionId) {
      if (!this.isAiHw) return;

      try {
        const res = await getAIReviewResult({ communionId });
        this.setResultVal(communionId, res);
        // 第一次请求结果的时候展开批阅数据
        this.isExpandAIReview = true;

        // 如果需要轮询,则启动轮询
        if (this.needPolling(this.aiReviewStatus, res)) {
          this.startPolling(communionId);
        }
      } catch (error) {
        this.handleError(error);
      }
    },

    getReviewDetail() {
      this.aiReviewStatus = this.AI_REVIEW_STATUS.COMPLETED;
      this.isUpdateReviewResult = true;
      this.expandAIReview();
      this.getDetail();
      this.$nextTick(() => {
        // 获取精华作业列表
        this.$refs.essentialList && this.$refs.essentialList.getList();
      });
    }
  }
};
</script>
<style lang="scss">
.yxtbiz-attachment-check {
  margin-left: 0;
}

.o2o-homework-multiple-topics .o2o-task-file {
  width: 182px;
}

.o2o-homework-multiple-topics .o2o-homework-multiple-userline .o2o-task-file {
  width: 171px;
}
</style>
