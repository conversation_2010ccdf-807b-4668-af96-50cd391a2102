<template>
  <yxtf-dialog
    padding-size="small"
    center
    :cutline="false"
    :visible.sync="visible"
    width="400px"
    :close-on-click-modal="false"
    :append-to-body="true"
  >
    <div class="yxtulcdsdk-chapterscompletedialog yxtulcdsdk-ulcdsdk">
      <div class="text-center flex-justify-center mt2">
        <yxt-ulcd-sdk-svg
          class=""
          width="259px"
          height="140px"
          icon-class="congratulations"
        />
      </div>
      <div class="font-size-24 lh34 text-26 text-center font-bolder mt20">
        {{ $t('pc_ulcdsdk_lbl_congratulations') }}

      </div>
      <div class="font-size-14 mt4 pb6 lh20 text-26 text-center"> {{ $t('pc_ulcdsdk_lbl_finishedlearning', [chapterName]) }}</div>
      <div v-if="chapterScore ||chapterPoint||chapterCerNum" class=" mt14 score yxtulcdsdk-flex-center text-center">

        <div v-if="chapterPoint" class="yxtulcdsdk-flex-auto">
          <div class="font-size-20 r lh28 text-26 text-center font-bolder">
            +{{ chapterPoint }}
          </div>
          <div class="font-size-14 lh20 text-26 text-center pt4">
            {{ $t('pc_ulcdsdk_lbl_earnpoints'/* 获得积分 */) }}
          </div>
        </div>
        <div v-if="chapterScore" class="yxtulcdsdk-flex-auto">
          <div class="font-size-20 r lh28 text-26 text-center font-bolder">
            +{{ chapterScore }}
          </div>
          <div class="font-size-14 lh20 text-26 text-center pt4">
            {{ $t('pc_ulcdsdk_lbl_obtainingcredits'/** 获得学分 */) }}
          </div>
        </div>
        <div v-if="chapterCerNum" class="yxtulcdsdk-flex-auto"> <div class="font-size-20 r lh28 text-26 text-center font-bolder">
                                                                  +{{ chapterCerNum }}
                                                                </div>
          <div class="font-size-14 lh20 text-26 text-center pt4">
            {{ $t('pc_o2o_lbl_studentcer'/* 获得证书 */) }}
          </div></div>

      </div>
      <div class="text-center mt26 mb4">
        <button
          type="button"
          class="ulcdsdk-nextchapterbutton"
          :disabled="!isChapterForNext"
          :class="!isChapterForNext?'is-disabled':''"
          @click="
            playNext()"
        ><span>{{ isChapterForNext?continueTheNextChapter:lastChapter }}</span></button>
      </div>
    </div>
  </yxtf-dialog>

</template>

<script>
import Svg from 'packages/_components/svg.vue';
export default {
  name: 'YxtUlcdSdkChaptersCompleteDialog',
  props: {
    chapterScore: {
      type: Number
    },
    chapterPoint: {
      type: Number,
      default: 0
    },
    chapterCerNum: {
      type: Number,
      default: 0
    },
    visible: {
      type: Boolean,
      default: false
    },
    isChapterForNext: {
      type: Boolean,
      default: false
    },
    appCode: {
      type: String,
      default: ''
    },
    chapterName: {
      type: String
    }
  },
  watch: {
    visible: {
      handler: function(val) {
        this.$emit('update:visible', val);
      }
    }
  },
  data() {
    return {
      show: false
    };
  },
  computed: {
    continueTheNextChapter() {
      return this.appCode === 'o2o' ? this.$t('pc_ulcdsdk_lbl_learnthenextstage') : this.$t('pc_ulcdsdk_lbl_continuethenextchapter');
    },
    lastChapter() {
      return this.appCode === 'o2o' ? this.$t('pc_ulcdsdk_lbl_alreadythelaststage') : this.$t('pc_ulcdsdk_lbl_thisthelastchapter');
    }
  },
  components: {
    [Svg.name]: Svg
  },
  methods: {
    playNext() {
      this.$emit('playNextChapter');
    }
  }
};

</script>
