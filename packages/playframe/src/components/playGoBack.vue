<template>
  <div class="yxtulcdsdk-flex-center yxtulcdsdk-play-goback mr12 hand" @click="goBack">
    <yxt-ulcd-sdk-svg
      class="color-white transformrtl"
      width="16px"
      height="16px"
      icon-class="returnarrow"
    />
  </div>
</template>

<script>
import Svg from 'packages/_components/svg.vue';
export default {
  components: {
    [Svg.name]: Svg
  },
  methods: {
    goBack() {
      this.$emit('setConfirmgoBack');
    }
  }
};
</script>

