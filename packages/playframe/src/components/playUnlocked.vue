<template>
  <div class="yxtulcdsdk-unlock__wrap">
    <div class="yxtulcdsdk-unlock-bg">
      <div class="yxtulcdsdk-unlock">
        <div>
          <yxt-ulcd-sdk-svg
            class=""
            width="150px"
            height="125px"
            icon-class="unlock"
          />
        </div>
        <div class="lh28 mt24 textcolor font-size-20">
          {{ $t('pc_ulcdsdk_lbl_taskNo') }}
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import Svg from 'packages/_components/svg.vue';
export default {
  name: 'YxtUlcdSdkUnlock',
  data() {
    return {
    };
  },
  components: {
    [Svg.name]: Svg
  }
};
</script>
