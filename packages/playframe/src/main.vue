<template>
  <div class="yxtulcdsdk-playframe yxtulcdsdk-ulcdsdk pr">
    <span class="gray">
      <!-- 这边一定要用v-show，course-summary里面有进度计算，需要往其他组件传递 -->
      <header v-show="isUsedByKng" class="yxtulcdsdk-header bgheader">
        <div class="yxtulcdsdk-flex-center title pr24 pl10">
          <playGoBack v-if="isUsedByKng&&isGoBack" @setConfirmgoBack="setConfirmgoBack" />
          <course-operation v-if="isUsedByKng" class="yxtulcdsdk-flex-1 w0" />
          <course-summary ref="courseSummary" class="ml40" />
          <template v-if="showSkipTask">
            <yxtf-divider class="opacity4 mh20" direction="vertical" />
            <yxtbiz-skip-task
              :biz-data="skipTaskBizData"
            />
          </template>
          <study-plan-btns class="ml24" />
        </div>
      </header>
      <div class="yxtulcdsdk-container pr bg" :class="isUsedByKng?'':'h100p'">
        <template v-if="isUsedByKng && courseId">
          <transition
            name="width-in"
          >
            <yxtf-scrollbar
              v-if="leftshow"
              class="yxtulcdsdk-aside__wrap"
              style="flex-shrink: 0;"
              :fit-height="true"
              bar-size="6px"
            >
              <aside class="yxtulcdsdk-aside pr">
                <div class="pr">
                  <div class="mt24 yxtulcdsdk-flex-center mr4 ml16 " :class="isChapter()?'mb24':'mb16'">
                    <yxt-ulcd-sdk-svg
                      class="mr4 textcolorop8"
                      width="16px"
                      height="16px"
                      icon-class="ulcdoutline2"
                    />
                    <span class="font-bolder textcolorop8 font-size-14 lh22">{{ $t('pc_ulcdsdk_lbl_curriculum') }}</span>
                  </div>
                  <div class="yxtulcdsdk-catalog mb20">
                    <ul>
                      <li v-for="(item, index) in catalogLists" :key="index">
                        <!-- 章节标题 -->
                        <template v-if="item.type===92">
                          <div class="font-bolder flex textcolorop7 mr28 ellipsis mt20 hand font-size-14 ml16 mb12 lh22" @click="clickKngChapter(item)">
                            <span><yxt-ulcd-sdk-svg
                              class="mr12  hand"
                              width="12px"
                              height="12px"
                              :icon-class="selectedChapter(item)?'putaway':'expand'"
                            />
                            </span>
                            <yxtf-tooltip
                              class="item"
                              :open-filter="true"
                              :content="item.name"
                              :open-delay="2000"
                              placement="top"
                            >
                              <span class="ulcdsdk-ellipsis-2 ulcdsdk-break">{{
                                item._name
                              }}</span>
                            </yxtf-tooltip>
                          </div>
                          <ul v-if="!selectedChapter(item)">
                            <li
                              v-for="(chapter, chapterIndex) in item.child"
                              :key="chapterIndex"
                              class="hand font-w400 textcolorop6 linozj mb4 font-size-14 lh22 ml16 mr16"
                              :class="activeId === chapter.id ? 'liactive' : ''"
                              @click="clickKng(chapter)"
                            >
                              <!-- 左侧标题 -->
                              <div class="ml24 flex-center flex-space-between">

                                <yxtf-tooltip
                                  class="item"
                                  :open-filter="true"
                                  :content="chapter.name"
                                  :open-delay="2000"
                                  placement="top"
                                >
                                  <span class="ulcdsdk-ellipsis-2 ulcdsdk-break"><span class="tag-type mr6">{{ chapter._typeName }}</span>{{
                                    chapter.name
                                  }}</span>
                                </yxtf-tooltip>
                                <yxt-ulcd-sdk-svg
                                  class="mr14 ml14 hand"
                                  width="20px"
                                  height="20px"
                                  :icon-class="getKngStatus(chapter)"
                                />
                              </div>
                            </li>
                          </ul>
                        </template>
                        <template v-else>
                          <div
                            class="hand font-w400 linozj mb4 font-size-14 textcolorop6 lh22 ml16 mr16"
                            :class="activeId === item.id ? 'liactive' : ''"
                            @click="clickKng(item)"
                          >
                            <div class="ml20 flex-center flex-space-between">
                              <yxtf-tooltip
                                class="item"
                                :open-filter="true"
                                :content="item.name"
                                :open-delay="2000"
                                placement="top"
                              >
                                <span class="ulcdsdk-ellipsis-2 ulcdsdk-break"><span class="tag-type mr6">{{ item._typeName }}</span>{{
                                  item.name
                                }}</span>
                              </yxtf-tooltip>
                              <yxt-ulcd-sdk-svg
                                class="mr14 ml14 hand"
                                width="20px"
                                height="20px"
                                :icon-class="getKngStatus(item)"
                              />
                            </div>
                          </div>
                        </template>
                      </li>
                    </ul>
                  </div>
                </div>
              </aside>
            </yxtf-scrollbar>
          </transition>
          <div class="pr dividerbg">
            <span
              v-if="!isFullScreen"
              class="hand flex-center left-expand-icon leftico"
              :class="leftshow?'leftico-show':'leftico-hide'"
              @click="leftshow = !leftshow"
            > <yxt-ulcd-sdk-svg
              class="ml2"
              width="12px"
              height="12px"
              icon-class="arrowhead"
            /></span>
          </div>
        </template>

        <main class="yxtulcdsdk-main pr bg" :class="{ 'playFullScreen': isFullScreen }">
          <template v-if="activeId&&playKngDetail">
            <points-exchange
              v-if="statusType===2"
              :kng-id="playKngDetail.id"
              :cover-url="courseDetail.needToBuy ? courseDetail.coverUrl : playKngDetail.bizAtt.coverUrl"
              :price="price"
              @pointExchangeCompleted="pointExchangeCompleted"
            />
            <play-unlocked v-else-if="statusType===1" />
            <div v-else-if="playKngDetail.type === KNG_TYPE.PRACTICE" class="hline" :class="{ 'yxtulcdsdk-playframe__wrap': !isFullScreen }">
              <yxt-ulcd-sdk-practicing
                ref="practicing"
                :params="params"
                @fullScreen="fullScreen"
                @updateProgress="updateProgress"
              />
            </div>
            <div v-else-if="playKngDetail.type === KNG_TYPE.EXAM" class="hline" :class="{ 'yxtulcdsdk-playframe__wrap': !isFullScreen }">
              <yxt-ulcd-sdk-examing
                ref="examing"
                :params="params"
                @fullScreen="fullScreen"
                @updateProgress="updateProgress"
              />
            </div>
            <div v-else-if="playKngDetail.type === KNG_TYPE.SURVEY" class="hline" :class="{ 'yxtulcdsdk-playframe__wrap': !isFullScreen }">
              <yxt-ulcd-sdk-surveying
                ref="surveying"
                :params="params"
                @fullScreen="fullScreen"
                @updateProgress="updateProgress"
              />
            </div>
            <div v-else-if="playKngDetail.type === KNG_TYPE.EVALUATION" class="hline" :class="{ 'yxtulcdsdk-playframe__wrap': !isFullScreen }">
              <yxt-ulcd-sdk-evaluation
                ref="evaluation"
                :params="params"
                :inner="true"
                :radius="false"
                @fullScreen="fullScreen"
                @updateProgress="updateProgress"
              />
            </div>
            <div v-else-if="playKngDetail.type === KNG_TYPE.DISCUSS" class="hline" :class="{ 'yxtulcdsdk-playframe__wrap': !isFullScreen }">
              <yxt-scrollbar :fit-height="true" class="yxtbizf-br-8 bg-white">
                <yxt-ulcd-sdk-discuss-task
                  ref="discuss"
                  :radius="true"
                  :params="params"
                  @updateProgress="updateProgress"
                  @initUpdateProgress="updateProgress"
                />
              </yxt-scrollbar>
            </div>
            <yxt-ulcd-sdk-course-player v-else :id="activeId" @updateProgress="updateProgress" />
            <play-chapters-complete-dialog
              v-if="isUsedByKng"
              :visible.sync="chaptersCompleteVisible"
              :chapter-name="chapterName"
              :chapter-score="chapterScore"
              :is-chapter-for-next="isChapterForNext"
              @updateProgress="updateProgress"
              @playNextChapter="playNextChapter"
            />
          </template>
        </main>
        <div v-if="showCourseNav" class="yxtulcdsdk-playframe__right">
          <course-nav />
        </div>
      </div>
    </span>
  </div>
</template>

<script>
import Svg from 'packages/_components/svg.vue';
import CourseSummary from 'yxt-ulcd-sdk/packages/course-player/src/components/course-summary.vue';
import courseOperation from 'yxt-ulcd-sdk/packages/course-player/src/components/course-operation.vue';
import playUnlocked from 'yxt-ulcd-sdk/packages/playframe/src/components/playUnlocked.vue';
import playChaptersCompleteDialog from 'yxt-ulcd-sdk/packages/playframe/src/components/playChaptersCompleteDialog.vue';
import playGoBack from 'yxt-ulcd-sdk/packages/playframe/src/components/playGoBack.vue';
import studyPlanBtns from 'yxt-ulcd-sdk/packages/course-player/src/components/studyPlanBtns.vue';
import pointsExchange from 'yxt-ulcd-sdk/packages/points-exchange/src/main.vue';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import { LOG_TYPE } from 'yxt-ulcd-sdk/packages/course-page/enum';
import CourseNav from 'yxt-ulcd-sdk/packages/course-page/src/components/course-nav.vue';
import workerTimer from 'yxt-ulcd-sdk/packages/course-player/timer';
export default {
  name: 'YxtUlcdSdkPlayframe',
  inject: ['getScanQueryData', 'getIsUsedByKng'],
  props: {
    kngId: {
      type: String,
      default: ''
    },
    courseId: {
      type: String,
      default: ''
    },
    targetCode: {
      type: String,
      default: ''
    },
    courseDetail: {
      type: Object,
      default: undefined
    },
    catalogLists: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      KNG_TYPE,
      leftshow: true,
      activeId: '',
      playKngDetail: undefined,
      listActiveChapter: [],
      listBakKngs: [],
      isFullScreen: false,
      chaptersCompleteVisible: false,
      price: 0, // 兑换积分
      chapterName: '', // 当前播放章节名称
      chapterScore: 0, // 当前播放章节总积分
      chapterForNext: 0, // 下一章节的索引
      isChapterForNext: true, // 是否已是最后一章节
      statusType: 0, // 1锁定 2需要兑换
      timer: null,
      timeNum: 5,
      backKngId: '',
      params: {} // 考试练习需要的参数
    };
  },
  components: {
    CourseSummary,
    [Svg.name]: Svg,
    courseOperation,
    playUnlocked,
    playChaptersCompleteDialog,
    pointsExchange,
    playGoBack,
    studyPlanBtns,
    CourseNav
  },
  computed: {
    scanQueryData() {
      return this.getScanQueryData();
    },
    isGoBack() {
      return window.history.length > 1;
    },
    isUsedByKng() {
      return this.getIsUsedByKng();
    },
    showSkipTask() {
      const { targetCode} = this.scanQueryData;
      return this.isUsedByKng && ['o2o', 'gwnl'].includes(targetCode);
    },
    skipTaskBizData() {
      const { targetId, taskId, gwnlUrl} = this.scanQueryData;
      return {
        projectId: targetId,
        taskId: taskId,
        gwnlUrl: encodeURIComponent(gwnlUrl || '')
      };
    },
    showCourseNav() {
      return this.courseId &&
        (!this.playKngDetail || this.statusType !== 0 || [KNG_TYPE.SURVEY, KNG_TYPE.EVALUATION, KNG_TYPE.DISCUSS, KNG_TYPE.EXAM, KNG_TYPE.PRACTICE].includes(this.playKngDetail.type))
        && this.isUsedByKng;
    }
  },
  created() {
    this.$root.$on('CHAPTER_PREV', this.previousChapter);
    this.$root.$on('CHAPTER_NEXT', this.nextChapter);
    this.$root.$on('CHAPTER_ITEM_PLAY', this.clickKng);
    this.$root.$on('AUTO_NEXT_MSG', this.showMsg);
    this.$root.$on('BACK', this.setConfirmgoBack);
    this.$root.$on('CLOSE_CATALOG', this.closeCatalog);
  },
  beforeDestroy() {
    this.clearTimer();
    this.$root.$off('CHAPTER_PREV', this.previousChapter);
    this.$root.$off('CHAPTER_NEXT', this.nextChapter);
    this.$root.$off('CHAPTER_ITEM_PLAY', this.clickKng);
    this.$root.$off('AUTO_NEXT_MSG', this.showMsg);
    this.$root.$off('BACK', this.setConfirmgoBack);
    this.$root.$off('CLOSE_CATALOG', this.closeCatalog);
    document.getElementsByTagName('body')[0].classList.remove('over-hidden');
  },
  watch: {
    catalogLists: {
      immediate: true,
      handler() {
        this.dealData();
      }
    }
  },
  methods: {
    getKngStatus(chapter) {
      let svgname = '';
      if (chapter.lockStatus === 0 || chapter.bizAtt.needToBuy === 1) {
        return 'knglock';
      }
      let studyStatus = chapter.studyStatus;
      switch (studyStatus) {
        case 0:
          svgname = 'kngnotstarted';
          break;
        case 1:
          svgname = 'kngprogress';
          break;
        case 2:
          svgname = 'kngcompleted1';
          break;
      }
      return svgname;
    },
    clickKngChapter(item) {
      if (this.selectedChapter(item)) {
        this.listActiveChapter = this.listActiveChapter.filter((c)=>{
          return c !== item.id;
        });
      } else {this.listActiveChapter.push(item.id);}

    },
    selectedChapter(item) {
      return this.listActiveChapter.some(s => s === item.id);
    },
    // 点击单任务
    clickKng(knginfo) {
      if (this.$refs.examing) {
        this.backKngId = knginfo.id;
        this.$refs.examing.confirmLeave(this.setBackKng);
      } else {
        this.setKng(knginfo.id);
      }
    },
    setBackKng(leave) {
      if (leave) {
        this.setKng(this.backKngId);
      }
    },
    setKng(id, ...other) {
      this.hadPopupAutoMessage = false;
      this.clearTimer();
      this.activeId = '';
      this.playKngDetail = undefined;
      this.$emit('changeKng', id, ...other);
      this.$nextTick(() => {
        this.activeId = id;
        this.getPlayKngDetail();
      });
    },
    getPlayKngDetail() {
      if (this.courseDetail.needToBuy) {
        this.statusType = 2;
        this.playKngDetail = this.courseDetail;
        this.price = this.playKngDetail.price;
        return;
      }
      if (!this.courseId) {
        this.playKngDetail = {...this.courseDetail};
      } else {
        this.playKngDetail = this.listBakKngs.find(item => item.id === this.activeId);
      }
      if (!this.playKngDetail) {
        return;
      }
      this.setExam(this.playKngDetail);
      if (this.playKngDetail.lockStatus === 0) {
        this.statusType = 1;
      } else if (this.playKngDetail.bizAtt && this.playKngDetail.bizAtt.needToBuy) {
        this.statusType = 2;
        this.price = this.playKngDetail.bizAtt.price;
      } else {
        this.statusType = 0;
      };
    },
    setExam(knginfo) {
      const { btid, targetId, taskId, targetCode, projectId } = this.scanQueryData;
      if (knginfo.type === KNG_TYPE.EXAM || knginfo.type === KNG_TYPE.PRACTICE) {
        this.params = {
          masterId: this.courseDetail.syncProcess === 0 ? targetId : this.courseId,
          masterType: this.courseDetail.syncProcess === 0 ? 1 : 2,
          packageId: this.courseId,
          trackId: knginfo.trackId || '',
          btid,
          targetId,
          taskId,
          targetCode
        };
        if (knginfo.type === KNG_TYPE.EXAM) this.params.arrangeId = knginfo.id;
        else this.params.praId = knginfo.id;
      } else if (knginfo.type === KNG_TYPE.SURVEY) {
        this.params = {
          id: knginfo.id, // 调研项目 ID
          type: 2, // 调查类型 (1:调研中心独立调查项目, 2:业务项目)。默认为 1
          taskType: 100,
          hideBack: true,
          allowResult: 1,
          trackId: knginfo.trackId || '',
          batchId: btid || '',
          masterId: this.courseDetail.syncProcess === 0 ? targetId : '',
          packageId: this.courseDetail.syncProcess === 0 ? this.courseId : '',
          sourceParam: JSON.stringify({
            targetCode,
            courseId: this.courseId,
            surveyId: knginfo.id,
            projectId: targetId || '',
            taskId,
            batchId: btid || ''
          })
        };
      } else if (knginfo.type === KNG_TYPE.EVALUATION) {
        this.params = {
          evaluationId: knginfo.id,
          trackId: knginfo.trackId || '',
          btid,
          batchId: btid || '',
          targetId,
          taskId,
          targetCode,
          masterId: this.courseDetail.syncProcess === 0 ? targetId : this.courseId,
          packageId: this.courseId
        };
      } else if (knginfo.type === KNG_TYPE.DISCUSS) {
        this.params = {
          taskId,
          pid: targetId || '',
          trackId: knginfo.trackId || '',
          btid,
          batchId: btid || '',
          boardId: knginfo.id,
          targetId: this.courseId,
          targetCode,
          syncUserProcess: this.courseDetail.syncProcess === 1
        };
      }
    },
    // 下一节
    // islock 是否为解锁
    playNext(islock = false) {
      let thisIndex = this.listBakKngs.findIndex((value)=>value.id === this.activeId);
      if (thisIndex >= this.listBakKngs.length - 1) {
        return;
      }
      if (islock) {
        this.listBakKngs[thisIndex + 1].lockStatus = 1;
      } else {
        this.setKng(this.listBakKngs[thisIndex + 1].id);
      }
    },
    // 上一节
    playPrevious() {
      let thisIndex = this.listBakKngs.findIndex((value)=>value.id === this.activeId);
      if (thisIndex <= 0) {
        return;
      }
      this.setKng(this.listBakKngs[thisIndex - 1].id);
    },
    /**
     * @param {*} loop 第一个的上一个是否到最后一个
     */
    previousChapter(loop) {
      if (!this.checkPreviousDisable()) {
        const index = this.listBakKngs.findIndex((value)=>value.id === this.activeId);
        this.clickKng(this.listBakKngs[index - 1]);
      } else if (loop) {
        this.clickKng(this.listBakKngs[this.listBakKngs.length - 1]);
      }
    },
    /**
     * @param {*} loop 最后一个是否到第一个
     */
    nextChapter(loop) {
      if (!this.checkNextDisable()) {
        const index = this.listBakKngs.findIndex((value)=>value.id === this.activeId);
        this.clickKng(this.listBakKngs[index + 1]);
      } else if (loop) {
        this.clickKng(this.listBakKngs[0]);
      }
    },
    checkPreviousDisable() {
      return this.activeId === this.firstId();
    },
    checkNextDisable() {
      return this.activeId === this.lastId();
    },
    // 是否存在章节
    isChapter() {
      return this.catalogLists && this.catalogLists[0] && this.catalogLists[0].type === 92;
    },
    firstId() {
      return this.listBakKngs && this.listBakKngs.length > 0 ? this.listBakKngs[0].id : '';

    },
    lastId() {
      return this.listBakKngs && this.listBakKngs.length > 0 ? this.listBakKngs[this.listBakKngs.length - 1].id : '';
    },
    // 是否全屏
    fullScreen(isFullScreen) {
      if (isFullScreen) {
        this.isFullScreen = true;
        document.getElementsByTagName('body')[0].classList.add('over-hidden');
      } else {
        document.getElementsByTagName('body')[0].classList.remove('over-hidden');
        this.isFullScreen = false;
      }
    },
    // 打平知识数据
    dealData() {
      if (this.isChapter()) {
        this.catalogLists.forEach((item, index) => {
          this.listBakKngs.push(...item.child.map(value => {
            value.pid = item.id;
            value.pname = this.$t('pc_kng_detail_lbl_index_chapter', { num: index + 1 }) + item.name;
            return value;
          })
          );
        });
      } else {
        this.listBakKngs.push(...this.catalogLists);
      }
    },
    clearTimer() {
      if (this.timer) {
        this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '取消自动跳转'});
        clearInterval(this.timer);
        this.timer = null;
        this.$fmessage.closeAll();
      }
    },
    // 完课、完课自动下一个、自动下一个, 单个课件任务自动下一个只需要弹一次
    showMsg(isAutoPlay, hadComplete = false) {
      this.fullScreen(false);
      if (isAutoPlay && !this.checkNextDisable()) {
        if (this.hadPopupAutoMessage) return;
        this.hadPopupAutoMessage = true;
        this.timeNum = 5;
        const h = this.$createElement;
        const messageContent = [
          h('span', {class: 'ulcd_spantimewarning'}, this.timeNum),
          h('span', {class: 'ml4'}, this.$t('pc_ulcdsdk_btn_skiptothenexttask'/** s 后跳转下个任务 */)),
          h('span', {
            class: 'ml32 color-primary-6 hand',
            on: {
              click: this.clearTimer
            }
          }, this.$t('pc_ulcdsdk_lbl_canceljump'/* 取消跳转 */))
        ];
        if (!hadComplete) {
          const isDistributeTime = !(this.courseId && this.courseDetail.scoreRewardType === 1);
          if (isDistributeTime && this.playKngDetail.bizAtt.studyScores) {
            messageContent.unshift(h('span', {class: 'mr2'}, this.$t('pc_ulcdsdk_lbl_completedcurrenttask'/** 您已完成当前任务，获得{0}学分， */, [this.playKngDetail.bizAtt.studyScores])));
          } else {
            messageContent.unshift(h('span', {class: 'mr2'}, this.$t('pc_ulcdsdk_lbl_completedcurrenttasko2o'/** 您已完成当前任务， */)));
          }
        }
        this.$fmessage({
          message:
          h('p', null, messageContent),
          showClose: false,
          duration: 5000,
          type: 'success',
          customClass: 'z-99999'
        });

        this.timer = setInterval(() => {
          --this.timeNum;
          if (this.timeNum === 0) {
            this.playNext();
            this.clearTimer();
          } else {
            if (document.querySelector('.ulcd_spantimewarning')) {
              document.querySelector('.ulcd_spantimewarning').innerHTML = this.timeNum;
            }
          }
        }, 1000);
      } else {
        this.$fmessage({
          message: this.$t('pc_ulcdsdk_lbl_havecompletedthecurrenttask'),
          type: 'success'
        });
      }
    },
    // 	学习状态，0未学习，1学习中，2已完成
    async updateProgress(studyStatus, studySchedule = 0, isAutoPlay = true) {
      if (!this.courseId) { // 单课件
        this.$root.$emit('UPDATE_SUMMARY', this.courseDetail);
        return;
      }
      if (studyStatus > 2) studyStatus = 2; // 讨论撤回  4
      if (this.playKngDetail.studyStatus > studyStatus) {
        return;
      }
      if (this.playKngDetail.studyStatus !== studyStatus && studyStatus === 2) {
        if ([KNG_TYPE.SURVEY, KNG_TYPE.DISCUSS, KNG_TYPE.EXAM, KNG_TYPE.PRACTICE].includes(this.playKngDetail.type)) {
          await this.sleep(1000);
        }
        this.$root.$emit('UPDATE_SUMMARY');
        this.showChapterComplete();
        if (this.playKngDetail.id !== this.lastId && !this.chaptersCompleteVisible) {this.showMsg(isAutoPlay);}
      }
      this.playKngDetail.studyStatus = studyStatus;
      if (this.playKngDetail.bizAtt) this.playKngDetail.bizAtt.studySchedule = studySchedule;
    },
    async sleep(time = 5) {
      return new Promise(resolve => {
        workerTimer.setTimeout(() => {
          resolve();
        }, time);
      });
    },
    // 是否需要展示章节学完弹框(包含解锁逻辑)
    showChapterComplete() {
      if (this.isChapter()) {
        const kngDetailBakList = this.listBakKngs.filter(item => (item.pid === this.playKngDetail.pid));
        const kngDetailBak = kngDetailBakList.filter(item => (item.studyStatus !== 2));
        if (kngDetailBak && kngDetailBak.length === 1) {
          const { scoreRewardType } = this.courseDetail;
          this.chapterScore = 0;
          let _lastID = '';
          kngDetailBakList.forEach((item, index) => {
            this.chapterScore += (item.bizAtt != null ? item.bizAtt.studyScores : 0);
            if (index === kngDetailBakList.length - 1) {
              _lastID = item.id;
            }
          });
          this.chapterScore = this.chapterScore.toFixed(2); // 精度丢失问题
          if (scoreRewardType === 1) {
            this.chapterScore = 0;
          }
          this.chapterForNext = this.listBakKngs.findIndex((value)=>value.id === _lastID);
          this.isChapterForNext = !(this.chapterForNext === this.listBakKngs.length - 1);
          this.chapterName = this.playKngDetail.pname;
          this.chaptersCompleteVisible = true;
        }
        this.setLockKng(true);
      } else {
        this.setLockKng(false);
      }
    },
    // 知识解锁
    // 章节解锁基于包完成的逻辑
    setLockKng(isChapter) {
      if (this.courseDetail && this.courseDetail.studyMode && this.courseDetail.studyMode.modeType !== 1) {
        if (isChapter) {
          if (this.courseDetail.studyMode.unitType === 1) {
            this.unlockNextKng(this.activeId);
          }
          if (this.courseDetail.studyMode.unitType === 2 && this.chaptersCompleteVisible && this.isChapterForNext) {
            const currentChapterId = this.listBakKngs.find(item => item.id === this.activeId).pid;
            const currentChapterIndex = this.catalogLists.findIndex(item => item.id === currentChapterId);
            this.unlockNextChapter(currentChapterIndex);
          }
        } else {
          this.unlockNextKng(this.activeId);
        }
      }
    },
    unlockNextKng(id) {
      let thisIndex = this.listBakKngs.findIndex((value)=>value.id === id);
      if (thisIndex >= this.listBakKngs.length - 1) {
        return;
      }
      const nextItem = this.listBakKngs[thisIndex + 1];
      if (nextItem.lockStatus !== 1) {
        nextItem.lockStatus = 1;
        if (nextItem.studyStatus === 2) {
          this.unlockNextKng(nextItem.id);
        }
      }
    },
    unlockNextChapter(index) {
      if (index >= this.catalogLists.length - 1) return;
      let nextChapter = this.catalogLists[index + 1];
      this.listBakKngs.filter(item => item.pid === nextChapter.id).forEach(item => {
        item.lockStatus = 1;
      });
      const nextAllComplete = nextChapter.child.every(item => item.studyStatus === 2);
      if (nextAllComplete) {
        this.unlockNextChapter(index + 1);
      }
    },
    // 学习下一章节
    playNextChapter() {
      this.chaptersCompleteVisible = false;
      const nextChapter = this.listBakKngs[this.chapterForNext + 1];
      if (nextChapter) {
        this.setKng(nextChapter.id);
      }
    },
    // 积分兑换完成
    pointExchangeCompleted() {
      if (this.courseDetail.needToBuy) {
        // 课程包兑换，刷新页面
        return window.location.reload();
      }
      // 课程包课件兑换-重新加载
      this.playKngDetail.bizAtt.needToBuy = 0;
      this.setKng(this.playKngDetail.id);
    },
    goBack(leave) {
      if (!leave) {
        return;
      }
      if (window.history.length > 1) {
        window.history.back(-1);
      } else if (window.opener) {
        window.location.href = opener.location.href;
      } else {
        window.location.href = `${window.location.origin}/main/#/kng/index`;
      }
    },
    setConfirmgoBack() {
      if (this.$refs.examing) {
        this.$refs.examing.confirmLeave(this.goBack);
      } else {
        this.goBack(true);
      }
    },
    closeCatalog() {
      this.leftshow = false;
    }
  }
};
</script>
