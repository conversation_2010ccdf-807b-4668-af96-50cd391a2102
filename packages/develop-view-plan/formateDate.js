import dayjs from 'dayjs';

/**
 * 格式化时间格式
 * @param data 日期
 * @param format 格式
 * @returns {string}
 */
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm') => {
  if (!date) return '';
  return dayjs(date).format(format);
};

// 跨年 YYYY-MM-DD HH:mm  今年 MM-DD HH:mm
export const getShowTime = (date, curTime = new Date()) => {
  return date.getFullYear() === curTime.getFullYear() ? formatDate(date, 'MM-DD HH:mm') : formatDate(date, 'YYYY-MM-DD HH:mm');
};

// 几天内
export const addDay = (day, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(new Date()).add(day, 'days').format(format);
};

// 学习计划详情页面时间处理
export const handleViewPlanTime = (data) => {
  data = data || [];
  data = data.map(item => {
    const fTime = new Date(item.planFinishTime.replace(/-/g, '/'));
    item.finishTime = getShowTime(fTime);
    return item;
  });
  return data;
};

// 是否早于当前时间
export const isBefore = (time) => {
  const end = (new Date(time.replace(/-/g, '/'))).getTime();
  const now = (new Date()).getTime();
  return end < now;
};
