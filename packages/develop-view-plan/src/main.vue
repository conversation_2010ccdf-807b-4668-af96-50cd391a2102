<template>
  <div class="yxtulcdsdk-develop-view-plan yxtulcdsdk-ulcdsdk">
    <yxt-dialog
      v-if="visible"
      :title="$t('pc_kng_lbl_developplan'/** 制定自学计划 */)"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="480px"
    >
      <develop-plan ref="devePlan" :kng-id="kngId" />
      <span slot="footer" class="dialog-footer">
        <yxtf-button @click="visible = false">{{ $t('pc_ulcdsdk_cancel' /** 取消 */) }}</yxtf-button>
        <yxtf-button type="primary" @click="developPlan">{{ $t('pc_ulcdsdk_done'/** 确定 */) }}</yxtf-button>
      </span>
    </yxt-dialog>

    <view-plan ref="viewPlan" @remove="removeStudy" @update="updatePlan" />
  </div>
</template>

<script>
import developPlan from './components/develop-plan.vue';
import viewPlan from './components/view-plan.vue';

export default {
  name: 'YxtUlcdSdkDevelopViewPlan',
  components: {
    developPlan,
    viewPlan
  },
  data() {
    return {
      kngId: '',
      visible: false
    };
  },
  methods: {
    init(type, kngId) {
      if (!kngId) return;
      this.kngId = kngId;
      if (type === 'develop') {
        this.visible = true;
      } else if (type === 'view') {
        this.$refs.viewPlan.init(kngId);
      }
    },
    developPlan() {
      this.$refs.devePlan.developPlan().then(() => {
        this.$message.success(this.$t('pc_kng_common_msg_develop_tips'/** 制定成功！ */));
        this.visible = false;
        this.$emit('isAddStudy', true);
        this.$emit('updatePlan');
      });
    },
    updatePlan() {
      this.$emit('updatePlan');
    },
    removeStudy() {
      this.$emit('isAddStudy', false);
      this.$emit('updatePlan');
    }
  }
};
</script>
