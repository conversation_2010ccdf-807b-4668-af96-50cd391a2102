<template>
  <div class="yxtulcdsdk-develop-view-plan__develop">
    <div class="yxtulcdsdk-develop-view-plan__develop-tip">{{ $t('pc_kng_lbl_developplantip'/** 每天进步一点点，悄悄惊艳所有人！ */) }}</div>

    <div class="mt24">
      <span class="yxtulcdsdk-required">{{ $t('pc_kng_courseware_lbl_tab_end_time'/** 完成时间 */) }}</span>
    </div>
    <div class="yxtulcdsdk-add-students-to-team__time">
      <yxtf-radio-group v-model="radio" :direction="'row'" @change="radioChange">
        <yxtf-radio :label="3">
          <span>{{ $t('pc_kng_lbl_within3days'/** 3天内 */) }}</span>
          <span v-if="time && radio == 3" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="7">
          <span>{{ $t('pc_kng_lbl_within7days'/** 7天内 */) }}</span>
          <span v-if="time && radio == 7" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="14">
          <span>{{ $t('pc_kng_lbl_within14days'/** 14天内 */) }}</span>
          <span v-if="time && radio == 14" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="30">
          <span>{{ $t('pc_kng_lbl_within30days'/** 30天内 */) }}</span>
          <span v-if="time && radio == 30" class="ml12 standard-size-12 color-gray-7">{{ $t('pc_kng_lbl_corrtime'/** 对应时间：{0} */,[time]) }}</span>
        </yxtf-radio>
        <yxtf-radio :label="0">
          <span>{{ $t('pc_kng_lbl_custom'/** 自定义 */) }}</span>
          <yxt-date-picker
            v-if="radio == 0"
            v-model="value"
            class="ml12"
            size="small"
            type="datetime"
            format="yyyy-MM-dd HH:mm"
            :placeholder="$t('pc_kng_lbl_selectdatetime'/** 选择日期时间 */)"
          />
        </yxtf-radio>
      </yxtf-radio-group>
    </div>
  </div>
</template>

<script>
import { addDay, formatDate, isBefore } from 'yxt-ulcd-sdk/packages/develop-view-plan/formateDate';
import { planSave } from './../../service.js';

export default {
  name: 'YxtUlcdSdkDevelopPlan',
  props: {
    kngId: {
      type: String
    }
  },
  data() {
    return {
      radio: 0,
      value: null,
      time: ''
    };
  },
  created() {
    this.radio = 3;
    this.finishTime = addDay(3, 'YYYY-MM-DD HH:mm:ss');
    this.time = addDay(3, 'YYYY-MM-DD HH:mm');
  },
  methods: {
    radioChange(val) {
      if (val === 0) {
        this.finishTime = '';
        this.time = '';
      } else {
        this.finishTime = addDay(val, 'YYYY-MM-DD HH:mm:ss');
        this.time = addDay(val, 'YYYY-MM-DD HH:mm');
      }
    },
    // 制定自学计划
    developPlan() {
      if (this.radio === 0 && this.value) {
        this.finishTime = formatDate(this.value, 'YYYY-MM-DD HH:mm:ss');
      }

      if (!this.finishTime) {
        this.$message.warning(this.$t('pc_kng_tip_settime'/** 请设置完成时间 */));
        return Promise.reject();
      }
      if (isBefore(this.finishTime)) {
        this.$message.warning(this.$t('pc_kng_team_timeerror'/** 完成时间不能小于当前时间 */));
        return Promise.reject();
      }

      return planSave({
        kngId: this.kngId,
        leaderRecommend: 0, // 是否上级推荐 0：否 1：是
        planFinishTime: this.finishTime // 计划完成时间,格式 yyyy-MM-dd HH:mm:ss
      });
    }
  }
};
</script>
