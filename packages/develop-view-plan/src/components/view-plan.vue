<template>
  <div>
    <yxt-dialog
      v-if="visible"
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="480px"
      :show-back="showBack"
      @back="backHandler"
    >
      <develop-plan ref="devePlan" :kng-id="kngId" />
      <span slot="footer" class="dialog-footer">
        <yxtf-button @click="visible = false">{{ $t('pc_ulcdsdk_cancel' /** 取消 */) }}</yxtf-button>
        <yxtf-button type="primary" @click="developPlan">{{ $t('pc_ulcdsdk_done'/** 确定 */) }}</yxtf-button>
      </span>
    </yxt-dialog>

    <yxt-dialog
      :title="$t('pc_kng_selfstudy_lbl_title'/** 自学计划 */)"
      :visible.sync="visible2"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="480px"
    >
      <div class="yxtulcdsdk-develop-view-plan__view">
        <div v-if="planDetailList.length == 0" class="text-center">
          <yxtf-empty size="small" :empty-text="$t('pc_kng_viewplan_btn_joinnoplan'/** 已加入自学，暂未制定自学计划 */)" />
          <div class="mt12">
            <yxt-button @click="remove">{{ $t('pc_kng_viewplan_btn_remove'/** 移出自学 */) }}</yxt-button>
            <yxt-button class="ml16" type="primary" @click="drawPlan">{{ $t('pc_kng_viewplan_btn_draw'/** 立即制定 */) }}</yxt-button>
          </div>
        </div>
        <div v-else class="color-gray-10 standard-size-14">
          <div
            v-for="item in planDetailList"
            :key="item.planUserId"
            class="yxtulcdsdk-develop-view-plan__item"
          >
            <div v-if="planDetailList.length > 1 || item.leaderRecommend == 1" class="mb20">
              <yxtf-tag v-if="item.leaderRecommend == 1" cover-type="orange" size="small">{{ $t('pc_kng_studyplan_lbl_leadre'/** 上级推荐 */) }}</yxtf-tag>
              <yxtf-tag v-else cover-type="slategrey" size="small">{{ $t('pc_kng_studyplan_lbl_join'/** 自己加入 */) }}</yxtf-tag>
            </div>
            <div v-if="item.leaderRecommend == 1" class="mb20 flex">
              <span class="flex-shrink-0">{{ $t('pc_kng_viewplan_lbl_reference'/** 推荐人：*/) }}</span>
              <yxtbiz-user-name :name="item.fullName" />
            </div>
            <div>{{ $t('pc_kng_viewplan_lbl_completetime'/** 计划完成时间：{0} */,[item.finishTime]) }}</div>
            <div v-if="item.leaderRecommend == 0" class="mt20">
              <yxt-button class="mt12" @click="remove">{{ $t('pc_kng_viewplan_btn_remove'/** 移出自学 */) }}</yxt-button>
              <yxt-button class="mt12" @click="deletePlan">{{ $t('pc_kng_viewplan_btn_delete'/** 删除计划 */) }}</yxt-button>
              <yxt-button class="mt12" @click="adjustPlan(item)">{{ $t('pc_kng_viewplan_btn_adjust'/** 调整计划 */) }}</yxt-button>
            </div>
          </div>
        </div>
      </div>
    </yxt-dialog>
  </div>
</template>

<script>
import { getPlanDetail, planDelete } from './../../service.js';

import developPlan from './develop-plan.vue';

import { handleViewPlanTime } from './../../formateDate.js';

export default {
  name: 'YxtUlcdSdkViewPlan',
  components: {
    developPlan
  },
  data() {
    return {
      visible: false,
      visible2: false,
      showBack: false,
      title: '',
      planDetailList: []
    };
  },
  mounted() {
  },
  methods: {
    async init(kngId) {
      this.kngId = kngId;
      await this.getList();
      this.visible2 = true;
    },
    async getList() {
      try {
        const res = await getPlanDetail(this.kngId) || {};
        this.info = res || {};
        this.planDetailList = handleViewPlanTime(res.planDetailList);
        console.log('planDetailList', this.planDetailList);
      } catch (error) {

      }
    },
    // 移出自学
    remove() {
      planDelete({
        kngId: this.kngId,
        leaderRecommend: 0, // 是否上级推荐 0：否 1：是
        type: 0 // 0：移除自学 1：删除计划
      }).then(() => {
        this.$message.success(this.$t('pc_kng_selfstudy_lbl_remove_success'/** 已移出自学 */));
        if (this.planDetailList.length <= 1) {
          this.visible2 = false;
          this.$emit('remove');
        }
      });
    },
    // 删除计划
    deletePlan() {
      planDelete({
        kngId: this.kngId,
        leaderRecommend: 0, // 是否上级推荐 0：否 1：是
        type: 1 // 0：移除自学 1：删除计划
      }).then(() => {
        this.$message.success(this.$t('pc_kng_selfstudy_msg_delete_tips'/** 已删除计划 */));
        this.getList();
        this.$emit('update');
      });
    },
    // 弹出调整计划
    adjustPlan(item) {
      this.dialogType = 1;
      this.title = this.$t('pc_kng_viewplan_btn_adjust'/** 调整计划 */);
      this.showBack = true;
      this.visible2 = false;
      this.visible = true;
    },
    backHandler() {
      this.visible2 = true;
      this.visible = false;
    },
    // 弹出制定计划
    drawPlan() {
      this.dialogType = 0;
      this.title = this.$t('pc_kng_lbl_developplan'/** 制定自学计划 */);
      this.showBack = false;
      this.visible2 = false;
      this.visible = true;
    },
    developPlan() {
      this.$refs.devePlan.developPlan().then(() => {
        if (this.dialogType === 0) {
          this.$message.success(this.$t('pc_kng_common_msg_develop_tips'/** 制定成功！ */));
        } else {
          this.$message.success(this.$t('pc_kng_common_msg_adjust_tips'/** 调整成功！ */));
        }
        this.visible = false;
        this.$emit('update');
      });
    }
  }
};
</script>
