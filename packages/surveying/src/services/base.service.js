import { surveyApi } from '../core/apis';

/**
 * 根据fileId 查询文件地址信息
 * clientType 0 pc 1 h5
 * @param {*} fileId
 * @returns
 */
export const getFileData = fileId => surveyApi.get(`/file/${fileId}?clientType=0`);

/** 获取下载地址 */
export const getFileDownloadUrl = (fileId, fileSourceType) => surveyApi.get(`/file/download/${fileId}?fileSourceType=${fileSourceType || 0}`);

/** 获取文件转码状态 */
export const getFileListStatus = fileIds => surveyApi.post('/file/listFileDetail', { fileIds });

// 导出学员pdf
export const postManagerExportAnswers = (params) => {
  return surveyApi.post('/manager/export/ua/answers', params);
};
