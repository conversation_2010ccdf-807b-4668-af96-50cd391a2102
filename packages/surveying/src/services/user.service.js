import { surveyApi } from '../core/apis';

/**
 * 我的调查列表，分页
 * @returns
 */
export const mySurveyList = ({ limit, current, orderby, direction, ...body }) => {
  return surveyApi.post('/ua/project/list', body, { params: { limit, current, orderby, direction } });
};

/**
 * 我的调查历史作答列表，不分页
 * @returns
 */
export const mySurveyHistoryList = (projectId) => {
  return surveyApi.get(`/ua/projectUserAnswerHis/${projectId}`);
};

/**
 * 调查作答结果查看
 * @param {string} uamId 用户调查ID
 * @param {string} uaId 用户单次作答ID
 * @returns
 */
export const surveyAnswerDetail = (uamId, uaId, manager, batchId, targetId) => {
  return surveyApi.get(`${manager ? '/manager' : ''}/ua/${uaId || uamId}/${uaId ? 'projectUserAnswerDetail' : 'projectUserAnswerMapDetail'}`, {
    batchId,
    targetId
  }
  );
};

/**
 * 调查预览
 * @param {string} id 调查项目ID
 * @param {string} tid 调查项目作答批次ID
 * @returns
 */
export const surveyPreview = (id, tid, batchId) => {
  return surveyApi.get(`/ua/${id}/preview?batchId=${batchId || ''}`);
};

/**
 * 外部调查进入作答
 * @param {object} params 参数
 * @returns
 */
export const surveyOuterPreview = (params) => {
  return surveyApi.post('/outer/urls/answer/user', params);
};

/**
 * 外部调查进入作答, 返回所需ID
 * @param {object} params 参数
 * @returns
 */
export const surveyOuterPreviewParams = (params) => {
  return surveyApi.post('/answer/user', { ...params, 'call-frequently': 1 });
};

/**
 * 调查完成页面基本信息
 * @param {string} uamId 用户调查ID
 * @param {string} uaId 用户单次作答ID
 * @returns
 */
export const surveyCompleteInfo = (uamId, uaId, batchId) => {
  return surveyApi.get(`/ua/${uamId}/projectUserAnswer`, { uaId: uaId, batchId: batchId || '' });
};

/**
 * 获取调查游客设置
 * @param {string} id 调查项目ID
 * @param {string} tid 调查项目作答批次ID
 * @returns
 */
export const getTouristSet = (id, tid) => {
  return surveyApi.get('/');
};

/**
 * 学员开始调查
 * @param {string} id 调查项目ID
 * @param {string} uamId 用户调查ID
 * @returns
 */
export const startUserSurvey = (id, uamId, time, targetId, batchId, sourceParam) => {
  return surveyApi.post(`ua/${id}/${uamId}/start`, {
    usedTime: time || 0, 
    'call-frequently': 1, 
    targetId: targetId || '',
    batchId: batchId || '',
    sourceParam: sourceParam || ''
  });
};

/**
 * 学员提交调查
 * @param {string} uamId 用户调查ID
 * @param {object} data 请求参数
 * @returns
 */
export const submitUserSurvey = (uamId, data) => {
  return surveyApi.put(`/ua/${uamId}/projectSubmit`, data);
};

/**
 * 学员调查学时提交
 * @param {string} uaId 用户单次作答ID
 * @param {object} data 请求参数
 * @returns
 */
export const submitUserSurveyTime = (uaId, data) => {
  return surveyApi.put(`/ua/${uaId}/timeSubmit`, data);
};

/**
 * 学员调查结束(使用场景为时间用尽，被动结束)
 * @param {string} uamId 用户调查ID
 * @param {string} uaId 用户单次作答ID
 * @param {object} data 请求参数
 * @returns
 */
export const endUserSurvey = (uamId, uaId, data) => {
  return surveyApi.put(`/ua/${uamId}/endProjectSubmit`, data);
};

/**
 * 学员单题提交
 * @param {string} uaId 调查项目ID
 * @param {object} data 请求参数
 * @returns
 */
export const submitUserQuestion = (uaId, data, targetId, batchId) => {
  return surveyApi.post(`/ua/${uaId}/questionSubmit`, { ...data, 'call-frequently': 1, targetId: targetId || '', batchId: batchId || '' });
};

/**
 * 调查获取消息提醒状态
 * @param {int} type 消息提醒类型
 * @returns
 */
export const getMsgRemindStatus = (type) => {
  return surveyApi.get(`/user/access/${type}`);
};

/**
 * 调查完成消息提醒
 * @param {int} type 消息提醒类型
 * @returns
 */
export const finishMsgRemindStatus = (type) => {
  return surveyApi.put(`/user/access/${type}`);
};
