<script>
import deepContainer from './mixins/deepContainer';

import Fbi from './pages/Fbi.vue';
import Preview from './pages/Preview.vue';
import Answer from './pages/Answer.vue';
import Result from './pages/Result.vue';
import './core/prototype';

const ComponentMap = ['Fbi', 'Preview', 'Answer', 'Result'];
const RouterMap = ['Fbi', 'Preview', 'Answer', 'Result'];

export default {
  name: 'YxtUlcdSdkSurveying',
  name_zh: '沉浸式调查',
  mixins: [
    deepContainer
  ],
  components: {
    Fbi,
    Preview,
    Answer,
    Result
  },
  data() {
    return {
    };
  },
  watch: {
  },
  created() {
    this.init(ComponentMap, RouterMap);
  },
  mounted() {
  },
  methods: {
  },
  beforeDestroy() {
  }
};
</script>
