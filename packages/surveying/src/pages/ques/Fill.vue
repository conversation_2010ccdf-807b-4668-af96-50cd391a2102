<!-- 创建时间2021/12/01 14:43:12 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：填空题 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div class="ml20">
        <div
          v-for="item in quesData.fillList"
          :key="item._id"
          class="mt16"
        >
          <div v-if="isMulti"><span class="ws-prewrap" v-html="item.content"></span></div>
          <yxt-input
            v-if="!disabled"
            v-blur-trim
            v-model="item.userAnswer"
            class="mxw800 wp100"
            :maxlength="quesData.maxLength>0?quesData.maxLength:''"
            :disabled="disabled"
            :class="isMulti?'mt12':''"
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 6}"
            show-word-limit
            @blur="blur"
          />
          <div v-else :class="isMulti?'mt12':''" class="mxw800 wp100 yxtulcdsdk-survey-fake-input">{{ item.userAnswer }}</div>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, SurveyQuesType, QuesScoreType } from '../../core/enum';
import { dealLineFeed } from '../../core/utils';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
import { blurTrimDirective } from 'packages/common-util/directives.js'
export default {
  components: { AttachmentsContainer },
  name: 'MultiFill',
  mixins: [ques, quesCheck],
  data() {
    return {
      QuesMode,
      QuesScoreType,
      SurveyQuesType
    };
  },
  computed: {
    isMulti() {
      return this.quesData && this.quesData.quesType === SurveyQuesType.multiFill;
    }
  },
  methods: {
    dealLineFeed,
    blur() {
      this.emitAnswer();
    }
  },
  directives: {
    'blur-trim': blurTrimDirective
  }
};
</script>
