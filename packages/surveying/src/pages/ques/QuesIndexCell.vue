<!-- 创建时间2021/11/11 19:03:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：题目的编辑，预览，答题组件 -->
<template>
  <div :class="`yxtulcdsdk-survey-ques-container yxtulcdsdk-survey-ques-container--mode-${mode}${readonly?' yxtulcdsdk-survey-ques-container--readonly':''}${!quesData.showQuesNum?' yxtulcdsdk-survey-ques-container--nonum':''}`">
    <!-- 这里可以用component合并 -->
    <component
      :is="componentName"
      v-if="componentName"
      :ques-data="quesData"
      :index="index"
      :mode="mode_"
      :disabled="disabled"
      :template-type="templateType"
      :rank-mode="rankMode"
      :emit-init-answer="emitInitAnswer"
      @user-submit="emitQuesSave"
      @jump="emitQuesLogic"
    />
    <divide
      v-else
      :page-index="quesData.quesType === SurveyQuesType.page ? pageIndex : null"
      :mode="mode_"
    />

    <!-- 错误信息 -->
    <div v-if="autoCheck && quesData.errorMessage" class="yxtulcdsdk-survey-ques-container__error standard-size-14 yxt-color-danger ml20">
      {{ quesData.errorMessage }}
    </div>

  </div>
</template>

<script>
import { SurveyQuesType, QuesMode } from '../../core/enum';
import { isInViewPort } from '../../core/utils';
import dataHandle from '../../mixins/dataHandle';
import Choose from './Choose.vue';
import MediaChoose from './MediaChoose.vue';
import Judgment from './Judgment.vue';
import Classify from './Classify.vue';
import Sort from './Sort.vue';
import Fill from './Fill.vue';
import Essay from './Essay.vue';
import UploadQues from './UploadQues.vue';
import Scale from './Scale.vue';
import SlideScore from './SlideScore.vue';
import Info from './Info.vue';
import PageInfo from './PageInfo.vue';
import Divide from './Divide.vue';
import TextVote from './TextVote.vue';
import { setQuesAnswerStatus } from '../../mixins/commonQues';
export default {
  components: {
    Choose,
    MediaChoose,
    Judgment,
    Classify,
    Sort,
    Fill,
    Essay,
    UploadQues,
    Scale,
    Info,
    PageInfo,
    SlideScore,
    Divide,
    TextVote
  },
  name: 'YxtUlcdSdkSurveyQues',
  mixins: [dataHandle],
  props: {
    ques: { // 试题数据
      type: Object,
      required: false,
      default: () => ({})
    },
    mode: { // 当前模式
      default: QuesMode.edit
    },
    index: { // 题目序号
      type: Number,
      default: 1
    },
    pageIndex: { // 分页的页码
      type: Number,
      default: null
    },
    readonly: {
      // 是否只读，用于区分编辑页面的预览，和预览界面的预览
      type: Boolean,
      default: false
    },
    disabled: {
      // 区分答题页面和查看答卷页面
      type: Boolean,
      default: false
    },
    autoCheck: {
      // 是否显示的错误信息
      type: Boolean,
      default: false
    },
    templateType: {
      // 模板类型  SurveyTemplateType
      type: Number,
      default: 1
    },
    rankMode: { // 允许参与者查看排行，针对投票题类型
      type: [Boolean, Number],
      default: false
    },
    converted: { // ques 试题数据 是否处理过了
      type: Boolean,
      default: false
    },
    emitInitAnswer: { // 初始化的答案是否触发做答
      type: Boolean,
      default: false
    },
    hideQuesSort: { // 隐藏题目序号
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      quesData: {},
      QuesMode,
      SurveyQuesType,
      mode_: this.mode,
      error: '',
      buttonLoading: false
    };
  },
  computed: {
    componentName() {
      if (this.quesData.quesType >= SurveyQuesType.name && this.quesData.quesType <= SurveyQuesType.number) return 'info';
      switch (this.quesData.quesType) {
        case SurveyQuesType.singleChoose:
        case SurveyQuesType.multiChoose:
          return 'choose';
        case SurveyQuesType.judge:
          return 'judgment';
        case SurveyQuesType.classify:
          return 'classify';
        case SurveyQuesType.sort:
          return 'sort';
        case SurveyQuesType.fill:
        case SurveyQuesType.multiFill:
          return 'fill';
        case SurveyQuesType.essay:
          return 'essay';
        case SurveyQuesType.upload:
          return 'upload-ques';
        case SurveyQuesType.scale:
        case SurveyQuesType.multiScale:
        case SurveyQuesType.nps:
        case SurveyQuesType.score:
        case SurveyQuesType.evaluation:
          return 'scale';
        case SurveyQuesType.slideScore:
          return 'slide-score';
        case SurveyQuesType.pageIntro:
          return 'page-info';
        case SurveyQuesType.textChoose:
        case SurveyQuesType.multiTextChoose:
          return 'text-vote';
        case SurveyQuesType.picChoose:
        case SurveyQuesType.multiPicChoose:
        case SurveyQuesType.videoChoose:
        case SurveyQuesType.multiVideoChoose:
          return 'media-choose';
        default:
          return '';
      }
    }
  },
  watch: {
    mode: {
      immediate: true,
      handler(val) {
        // 编辑模式下先拷贝数据，用于编辑取消的时候数据还原
        if (val === QuesMode.edit) {
          this.quesCopyData = JSON.parse(JSON.stringify(this.quesData));
        }
        this.mode_ = val;
      }
    },
    ques: {
      immediate: true,
      handler(value) {
        if (value && !this.converted && !value._converted) {
          this.quesData = this.interfaceDataToViewData(this.ques, !this.hideQuesSort);
          setQuesAnswerStatus(this.quesData);
          this.quesData._converted = true;
          this.quesData.quesNum = this.index;
          this.$emit('update:ques', this.quesData);
        } else {
          this.quesData = this.ques;
        }
      }
    }
  },
  created() {
  },
  mounted() {
    if (!this.quesData.questionId && (this.quesData.quesType === SurveyQuesType.page || this.quesData.quesType === SurveyQuesType.divide)) {
      // 直接保存，没有编辑状态
      this.confirm();
    }
    this.scrollToWindow();
  },
  methods: {
    // 删除当前数据
    remove() {
      this.$emit('remove', this.quesData);
    },
    // 编辑
    edit() {
      this.$emit('edit', this.quesData);
    },
    // 复制
    copy() {
      this.$emit('copy', this.quesData);
    },

    // 取消编辑
    cancel() {
      this.buttonLoading = false;
      if (this.quesData.questionId) {
        // 有id的时候，取消就是更换模式 重置数据
        this.$emit('update:quesData', this.quesCopyData);
        this.$emit('update:mode', QuesMode.view);
      } else {
        // 第一次新增，如是没有id，取消就是删除该数据
        this.remove();
      }
    },

    // STEP4 确定添加题目，拼装接口数据；转化_id为真实数据（itemId or index）
    // 确定编辑
    confirm() {
      this.buttonLoading = true;
      this.$emit('confirm', this.quesData, this.cancelLoading);
    },
    cancelLoading() {
      this.buttonLoading = false;
    },

    scrollToWindow() {
      if (this.mode === QuesMode.edit && !isInViewPort(this.$el)) {
        this.$el.scrollIntoView({ block: 'center' });
      }
    },

    /**
     * @param answer 答案数据对象
     */
    emitQuesSave(answer) {
      answer.quesType = this.quesData.quesType;
      answer.questionId = this.quesData.questionId;
      this.$emit('user-submit', this.quesData, answer);
    },
    /**
     * @param targetId 跳到的目标题目id
     * @param hideId 追加隐藏的题目id
     */
    emitQuesLogic(targetId, hideId) {
      this.$emit('jump', this.quesData.questionId, targetId, hideId);
    }
  }
};
</script>
