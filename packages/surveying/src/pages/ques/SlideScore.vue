<!-- 创建时间2022/04/20 20:40:15 -->
<!-- 创建人：make -->
<!-- 组件描述：滑动打分 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">

    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div
        v-for="(item,index) in quesData.itemGroups"
        :key="index"
        class="ml20 flex"
      >
        <div class="flex flex-vertical flex-top mt12">
          <div v-if="item.groupName && quesData.itemGroups.length > 1" class="mb12 flex flex-top">
            <span class="yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(index).toLowerCase() }}.</span>
            <span class="ws-prewrap" v-html="item.groupName"></span></div>
          <div class="flex flex-vertical">
            <slider-bar
              v-model="item.scaleValue"
              :min="quesData.scaleMinValue"
              :max="quesData.scaleMaxValue"
              :style="{width: (quesData.itemGroups.length > 1 ? 476 : 522) + 'px'}"
              larger
              show-all-tick
              :readonly="disabled"
              @change="changeValue"
            />
            <div class="yxtulcdsdk-survey-slider-tick-num flex flex-between standard-size-14 color-gray-8 mh-5 mt-3">
              <template v-for="(_, index) in (quesData.scaleMaxValue - quesData.scaleMinValue + 1 )">
                <span v-if="showTick(index + quesData.scaleMinValue) !== ''" :key="index">{{ showTick(index + quesData.scaleMinValue) }}</span>
              </template>
            </div>
            <div class="flex flex-between standard-size-12 color-gray-7 mt12" :class="quesData.scaleStyle === ScaleQuesStyleType.number?'mt8':''">
              <template>
                <span class="ws-prewrap flex-1 flex-shrink-0" :style="{width: (quesData.itemGroups.length > 1 ? 230 : 253) + 'px'}">{{ quesData.scaleMinLabel }}</span>
                <span class="ws-prewrap flex-1 flex-shrink-0 tr ml16" :style="{width: (quesData.itemGroups.length > 1 ? 230 : 253) + 'px'}">{{ quesData.scaleMaxLabel }}</span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, ScaleQuesStyleType, SurveyQuesType } from '../../core/enum';
import { convertASCIIForNum } from '../../core/utils';
import SliderBar from '../components/SliderBar2.vue';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
export default {
  components: { SliderBar, AttachmentsContainer },
  mixins: [ques, quesCheck],
  name: 'SlideScore',
  data() {
    return {
      QuesMode,
      ScaleQuesStyleType,
      SurveyQuesType
    };
  },
  computed: {
    // 用于预览统一处理
    groupData() {
      const groups = JSON.parse(JSON.stringify(this.quesData.itemGroups));
      if (this.quesData.quesType === SurveyQuesType.score && this.quesData.itemGroups.length === 1) {
        groups[0].groupName = '';
      }
      return groups;
    }
  },

  mounted() {
    this.emitLogic();
  },
  methods: {
    showTick(value) {
      if ((this.quesData.scaleMaxValue - this.quesData.scaleMinValue) % 10 === 0) {
        return (value - this.quesData.scaleMinValue) % 10 === 0 ? value : '';
      }
      return value === this.quesData.scaleMinValue || value === this.quesData.scaleMaxValue ? value : '';
    },
    convertASCIIForNum,
    changeValue() {
      if (this.mode !== QuesMode.write) return;
      this.emitAnswer();
    }
  }
};
</script>
