<!-- 创建时间2021/12/02 20:40:15 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：量表题 多级量表 nps 评价 打分 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">

    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div
        v-for="(item,index) in answerGroupData"
        :key="index"
        class="ml20"
        :class="userInCenter?'text-center':''"
      >
        <div class="d-in-flex max-width-100 flex-vertical flex-top" :class="index>0?'mt24':'mt12'">
          <div v-if="item.groupName" class="mb12 flex flex-top">
            <span class="yxtulcdsdk-flex-shrink-0">{{ convertASCIIForNum(index).toLowerCase() }}.</span>
            <span class="ws-prewrap" v-html="item.groupName"></span></div>
          <div class="flex flex-vertical">
            <div class="flex">
              <slider-bar
                v-if="quesData.scaleStyle === ScaleQuesStyleType.calibration"
                v-model="item._userAnswer"
                :min="scaleMinVal"
                :style="`width:${52 * ((itemLength-1) < 4 ? 4: (itemLength-1))}px`"
                :length="itemList.length"
                operation-type="range"
                :ques-type="quesData.quesType"
                larger
                show-all-tick
                :readonly="disabled"
                @change="changeValue"
              />
              <rate
                v-else
                v-model="item._userAnswer"
                front
                :min="scaleMinVal"
                :min-val-number="quesData.scaleMinValue || 0"
                :start-with-zero="quesData.quesType === SurveyQuesType.nps||quesData.quesType === SurveyQuesType.score"
                :item-class="quesData.scaleStyle === ScaleQuesStyleType.number?'item-sep':'yxtulcdsdk-survey-ques__score-icon'"
                operation-type="range"
                :ques-type="quesData.quesType"
                :disabled="disabled"
                :length="itemList.length"
                :type="quesData.scaleStyle"
                @change="changeValue"
              />
            </div>
            <div v-if="quesData.scaleStyle === ScaleQuesStyleType.calibration" class="yxtulcdsdk-survey-slider-tick-num flex flex-between standard-size-14 color-gray-8 mh-5 mt-3">
              <template v-if="[SurveyQuesType.scale, SurveyQuesType.multiScale].includes(quesData.quesType)">
                <span v-for="calibra in itemList.length" :key="calibra">
                  {{ getUserAnswer(calibra) }}
                </span>
              </template>
              <template v-else>
                <span v-for="calibra in (itemList.length - (quesData.scaleMinValue || 0))" :key="calibra">
                  {{ [SurveyQuesType.nps, SurveyQuesType.score].includes(quesData.quesType) ? getUserAnswer(calibra, true) : calibra + (quesData.scaleMinValue || 0) }}
                </span>
              </template>
            </div>
            <div class="flex flex-between child-spacex-12" :class="quesData.scaleStyle === ScaleQuesStyleType.number||quesData.scaleStyle === ScaleQuesStyleType.calibration?'standard-size-14 color-gray-8 mt12':'standard-size-12 color-gray-6 mt8'">
              <template v-if="quesData.quesType === SurveyQuesType.nps">
                <span>{{ $t('pc_survey_lbl_npsimpossible')/** 不可能 */ }}</span>
                <span>{{ $t('pc_survey_lbl_npspossible')/** 极有可能 */ }}</span>
              </template>
              <template v-else-if="quesData.quesType === SurveyQuesType.evaluation">
                <span>{{ $t('pc_survey_lbl_npsunsatisfaction')/** 非常不满意 */ }}</span>
                <span>{{ $t('pc_survey_lbl_npssatisfaction')/** 非常满意 */ }}</span>
              </template>
              <template v-else>
                <span :style="{width: getMaxWidth}" class="ws-prewrap flex-g-0 flex-shrink-1 tl">{{ quesData.scaleMinLabel }}</span>
                <span :style="{width: getMaxWidth}" class="ws-prewrap flex-g-0 flex-shrink-1 ml16 tr">{{ quesData.scaleMaxLabel }}</span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, ScaleQuesStyleType, SurveyQuesType } from '../../core/enum';
import { convertASCIIForNum, guid, isNumber } from '../../core/utils';
import SliderBar from '../components/SliderBar.vue';
import Rate from '../components/Rate.vue';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
export default {
  components: { SliderBar, Rate, AttachmentsContainer },
  mixins: [ques, quesCheck],
  name: 'Scale',
  data() {
    return {
      QuesMode,
      ScaleQuesStyleType,
      SurveyQuesType,
      styleList: [
        { value: ScaleQuesStyleType.star, icon: 'star_chosen' },
        { value: ScaleQuesStyleType.praise, icon: 'praise_chosen' },
        { value: ScaleQuesStyleType.heart, icon: 'heart_chosen' },
        { value: ScaleQuesStyleType.smile, icon: 'smile_chosen' },
        { value: ScaleQuesStyleType.number, icon: 'number_chosen' },
        { value: ScaleQuesStyleType.calibration, icon: 'calibration_chosen1' }
      ],
      maxScore: 5,
      answerGroupData: []
    };
  },
  computed: {
    isMulti() {
      return this.quesData.quesType === SurveyQuesType.multiScale || this.quesData.quesType === SurveyQuesType.score;
    },
    // 学员端答题，是否显示在中间
    userInCenter() {
      return false;
      // 需求迭代，不需要再居中了，和后台一致保持居左
      // return this.quesData.quesType !== SurveyQuesType.multiScale &&
      //   this.groupData.length === 1 &&
      //   this.quesData.scaleStyle !== ScaleQuesStyleType.number &&
      //   this.quesData.scaleStyle !== ScaleQuesStyleType.calibration;
    },
    // 用于预览统一处理
    groupData() {
      if (this.isMulti) {
        const groups = JSON.parse(JSON.stringify(this.quesData.itemGroups));
        // 当mode==管理端批量录入试题展示样式时，单个选项也展示
        if (this.quesData.quesType === SurveyQuesType.score && this.quesData.itemGroups.length === 1 && this.mode - 0 !== QuesMode.batchImportView) {
          groups[0].groupName = '';
        }
        return groups;
      }

      return [{ groupName: '' }];
    },
    itemLength() {
      const startV = [SurveyQuesType.scale, SurveyQuesType.multiScale].includes(this.quesData.quesType) ? 0 : (this.quesData.scaleMinValue || 0);
      return this.itemList.length - startV;
    },
    itemList: {
      get() {
        if (this.isMulti) {
          return this.quesData.itemGroups[0] ? this.quesData.itemGroups[0].itemList : [];
        }
        return this.quesData.itemList;
      },
      set(val) {
        if (this.isMulti) {
          this.quesData.itemGroups.forEach(el => {
            el.itemList = val;
          });
        } else {
          this.quesData.itemList = val;
        }
      }

    },
    level: {
      get() {
        return this.itemList.length - 1;
      },
      set(val) {
        this.handItemList(val);
      }
    },
    // 根据量表类型，计算下面说明文字的最大宽度，从而使得题目宽度不撑开
    getMaxWidth() {
      let numberWidth = 50; // 每个数字量表框的宽度
      let svgWidth = 44; // 每个表情量表框的宽度
      if (this.quesData.scaleStyle === ScaleQuesStyleType.calibration) {
        // 刻度类型 =》最少5级宽度
        const min = (this.itemLength - 1) < 4 ? 4 : (this.itemLength - 1);
        return (44 * min - 16) / 2 + 'px';
      } else if (this.quesData.scaleStyle === ScaleQuesStyleType.number) {
        return (numberWidth * this.itemLength + 10 * (this.itemLength - 1) - 16) / 2 + 'px';
      } else {
        return (36 + svgWidth * (this.itemLength - 1) - 16) / 2 + 'px';
      }
    },

    isScale() {
      return [SurveyQuesType.scale, SurveyQuesType.multiScale].includes(this.quesData.quesType);
    },
    scaleMinVal() {
      return this.isScale ? 0 : (this.quesData.scaleMinValue || 0);
    }
  },
  watch: {
    itemList: {
      immediate: true,
      handler(val) {
        this.maxScore = (val || []).length - 1;
      }
    },
    maxScore(val) {
      if (isNumber(val) && val > 0) {
        this.handItemList(val);
        if (val < this.quesData.scaleMinValue) {
          this.quesData.scaleMinValue = val;
        }
      }
    },
    level(val) {
      if (val < this.quesData.scaleMinValue) {
        this.quesData.scaleMinValue = val;
      }
    },
    'quesData.scaleMinValue'(val) {
      if (val > this.level && this.quesData.quesType !== SurveyQuesType.score) {
        this.level = val;
      } else if (val > this.maxScore && this.quesData.quesType === SurveyQuesType.score) {
        this.maxScore = val;
      }
    }
  },
  mounted() {
    this.setAnswerGroupData();
    this.emitLogic();
  },
  methods: {
    convertASCIIForNum,
    handItemList(val) {
      const length = val + 1;
      if (length >= this.itemList.length) {
        const list = [];
        for (let i = 0; i < length - this.itemList.length; i++) {
          list.push({ points: 0, _id: guid() });
        }
        this.itemList = [...this.itemList, ...list];
      } else {
        this.itemList = JSON.parse(JSON.stringify(this.itemList.slice(0, length)));
      }
      this.deleteItemUpdateLogic();
    },
    changeValue() {
      if (this.disabled) return;
      this.updateItemData();
      this.emitAnswer();
    },
    setAnswerGroupData() {
      if (this.mode !== QuesMode.write && this.mode !== QuesMode.managerView) return;
      if (this.isMulti) {
        this.answerGroupData = this.quesData.itemGroups.map(group => {
          const answerItem = group.itemList.find(item => item.userAnswerFlag === 1);
          let answer = answerItem ? (answerItem.itemId * 1) : null;
          if (this.isScale && (answer || answer === 0)) {
            answer = answer - this.quesData.scaleMinValue + 1;
          }
          const groupName = this.quesData.itemGroups.length === 1 && this.quesData.quesType === SurveyQuesType.score ? '' : group.groupName;
          return { groupName: groupName, groupId: group.groupId, _userAnswer: answer };
        });
      } else {
        const answerItem = this.quesData.itemList.find(item => item.userAnswerFlag === 1);
        let answer = answerItem ? (answerItem.itemId * 1) : null;
        if (this.isScale && (answer || answer === 0)) {
          answer = answer - this.quesData.scaleMinValue + 1;
        }
        this.answerGroupData = [{ groupName: '', _userAnswer: answer }];
      }
    },

    /**
     * 这个方法是统一答案的处理
     */
    getUserAnswer(userAnswer, isInit) {
      if (userAnswer) {
        if (this.isScale) {
          return userAnswer - 1 + (this.quesData.scaleMinValue || 0);
        } else if ([SurveyQuesType.nps].includes(this.quesData.quesType)) {
          return isInit ? (userAnswer - 1) : userAnswer;
        } else if (SurveyQuesType.score === this.quesData.quesType) {
          return isInit ? (userAnswer - 1 + (this.quesData.scaleMinValue || 0)) : userAnswer;
        } else {
          return userAnswer;
        }
      }

      return userAnswer;
    },

    // 作答完更新选项数据
    updateItemData() {
      this.answerGroupData.forEach((group, index) => {
        // 如果是量表题因为传入的渲染的minVal为0，不是根据后端返回的scaleMinValue取值，所以这里更新Flag的时候需要计算
        // 比如itemId是10000，则现在_userAnswer默认为1，需要+minVal再-1
        const userAnswer = this.getUserAnswer(group._userAnswer);

        if (this.isMulti) {
          this.quesData.itemGroups[index].itemList.forEach((item) => {
            if (item.itemId * 1 === userAnswer) item.userAnswerFlag = 1;
            else item.userAnswerFlag = 0;
          });
        } else {
          this.quesData.itemList.forEach((item) => {
            if (item.itemId * 1 === userAnswer) item.userAnswerFlag = 1;
            else item.userAnswerFlag = 0;
          });
        }
      });
    },
    // 获取评价题文件结果
    getEvalutionContent(index) {
      const key = ['', 'pc_survey_lbl_npsunsatisfaction', 'pc_survey_lbl_somedissatisfied', 'pc_survey_lbl_ordinarysatisfaction', 'pc_survey_lbl_somesatisfaction', 'pc_survey_lbl_npssatisfaction'];
      return this.$t(key[index]);
    }
  }
};
</script>
