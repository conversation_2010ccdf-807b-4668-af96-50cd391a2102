<!-- 创建时间2021/11/11 19:10:54 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：选择题 单选+多选-->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div
        v-if="multiSelectCountTip"
        class="ml20 color-gray-6 standard-size-12"
        :class="{
          'mt12': quesData.enabledExplain,
          'mt4': !quesData.enabledExplain
        }"
      >
        {{ multiSelectCountTip }}
      </div>
      <!--答题模式--下拉题-->
      <template v-if="quesData.layoutType === QuesOptionLayoutCol.dropdown && !voteRankMode">
        <div class="ml20 mt16 prlt">
          <template v-for="(group, index) in quesData.itemGroups">
            <div
              v-if="quesData.itemGroups.length > 1"
              :key="index"
              class="font-size-14 color-gray-8 mt16 ws-prewrap"
              v-html="dealLineFeed(group.groupName)"
            ></div>
            <yxt-select
              :key="index"
              v-model="group.answer"
              :multiple="isMulti"
              :disabled="disabled"
              class="mxw800 wp100"
              :class="quesData.itemGroups.length > 1 ? 'mt8' : ''"
              :popper-class="'yxtulcdsdk-survey-layouttype-select yxtulcdsdk-survey'"
              @remove-tag="removeMultiOption($event, group)"
            >
              <yxt-option
                v-for="item in group.itemList"
                :key="item.itemId"
                :label="replaceHtmlTags(item.content)"
                :value="item.itemId"
                :disabled="disabled || getDisabledStatus(item)"
                class="flex flex-mid"
                @click.native="change(item, group)"
              >
                <custom-choice-box
                  :checked="item.userAnswerFlag"
                  :disabled="disabled || getDisabledStatus(item)"
                  :is-multi="isMulti"
                  class="flex flex-mid"
                  :icon-offset="0"
                >
                  <span class="ml8 preline">{{ replaceHtmlTags(item.content) }}</span>
                </custom-choice-box>
              </yxt-option>
            </yxt-select>
          </template>
        </div>
      </template>
      <template v-else>
        <div class="ml20">
          <div v-for="(group,index) in quesData.itemGroups" :key="`group_${index}`">
            <div
              v-if="quesData.itemGroups.length >1 && group.groupName"
              class="ws-prewrap"
              :class="index>0?'mt24':'mt12'"
              v-html="dealLineFeed(group.groupName)"
            ></div>
            <div class="flex flex-wrap m-f8 mt8">
              <template v-for="(item,itemIndex) in group.itemList">
                <choose-option
                  :key="item._id"
                  :is-multi="isMulti"
                  :class="{
                    'wp100':(quesData.layoutType === QuesOptionLayoutCol.one) || (quesData.layoutType === QuesOptionLayoutCol.dropdown),
                    'wp50':quesData.layoutType === QuesOptionLayoutCol.two,
                    'mt-4':quesData.itemGroups.length>1&&group.groupName&&(quesData.layoutType === QuesOptionLayoutCol.one&&itemIndex===0||quesData.layoutType === QuesOptionLayoutCol.two&&itemIndex<=1),
                    'p8':true
                  }"
                  :ques-option-data="item"
                  :ques-data="quesData"
                  :mode="mode"
                  :disabled="disabled || getDisabledStatus(item)"
                  :layout-type="quesData.layoutType"
                  @changeNote="emitAnswer"
                  @click.native="change(item)"
                >
                  <template slot="voteStatic" v-if="voteRankMode">
                    <div class="mt8 ml-24 flex flex-mid" :class="item.userAnswerFlag? 'color-primary-6': 'color-gray-7'">
                      <yxtf-progress
                        class="w160 flex-shrink-0"
                        :percentage="item.percentage"
                        :show-text="false"
                        :stroke-width="4"
                        :color="!item.userAnswerFlag? '#8c8c8c': undefined"
                      />
                      <span class="ml8 flex-shrink-0">{{ $t('pc_survey_lbl_votenums'/** 票 */, [item.cnt]) }} &nbsp;{{ `${item.percentage}%` }}</span>
                    </div>
                  </template>
                </choose-option>
              </template>
            </div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import { QuesMode, QuesOptionLayoutCol, SurveyQuesType } from '../../core/enum';
import ChooseOption from './widgets/ChooseOption.vue';
import { dealLineFeed, replaceHtmlTags } from '../../core/utils';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
import CustomChoiceBox from '../components/customChoiceBox/CustomChoiceBox.vue';
export default {
  components: { ChooseOption, AttachmentsContainer, CustomChoiceBox },
  name: 'Choose',
  mixins: [ques, quesCheck],
  data() {
    return {
      QuesMode,
      QuesOptionLayoutCol,
      dataList: [
        { label: this.$t('pc_survey_lbl_commonoption'/** 普通选项 */), action: () => this.add(0) },
        { label: this.$t('pc_survey_lbl_filloption'/** 输入选项 */), action: () => this.add(1) }
      ]
    };
  },
  computed: {
    isMulti() {
      return this.quesData && this.quesData.quesType === SurveyQuesType.multiChoose;
    },
    selectedCount() {
      let count = 0;
      this.quesData.itemGroups.forEach(group => {
        group.itemList.forEach(item => {
          if (item.userAnswerFlag) count++;
        });
      });
      return count;
    }
  },
  mounted() {
    this.emitLogic();
    this.setSelectAnswer();
  },
  methods: {
    dealLineFeed,
    getDisabledStatus(item) {
      if (this.quesData.maxChoose > 0 && this.selectedCount >= this.quesData.maxChoose && !item.userAnswerFlag) {
        return true;
      }
      return false;
    },
    change(item, grp) {
      if (this.disabled) return;
      if (this.isMulti) {
        if (this.getDisabledStatus(item)) {
          return;
        }
        this.$set(item, 'userAnswerFlag', item.userAnswerFlag === 1 ? 0 : 1);
      } else {
        this.$set(item, 'userAnswerFlag', item.userAnswerFlag === 1 ? 0 : 1);
        this.quesData.itemGroups.forEach(group => {
          group.itemList.forEach(it => {
            if (it !== item && it.userAnswerFlag === 1) {
              this.$set(it, 'userAnswerFlag', 0);
            }
          });
          // 下拉模式手动处理答案
          if (grp) {
            // 单选取消选择，清空当前分组答案
            if (item.userAnswerFlag === 0) {
              this.$set(grp, 'answer', '');
            }
            // 单选取消选择后 =》清空其他分组答案
            if (grp.groupId !== group.groupId) {
              this.$set(group, 'answer', '');
            }
          }
        });
      }
      this.emitAnswer();
    },
    replaceHtmlTags,
    // 初始化下拉选择框显示答案，适用于单选多选的下拉模式
    setSelectAnswer() {
      // 判断是否是下拉模式
      if (this.quesData.layoutType !== this.QuesOptionLayoutCol.dropdown) {
        return;
      }
      this.quesData.itemGroups.forEach(group => {
        // 初始化答案字段
        this.$set(group, 'answer', (this.isMulti ? [] : ''));
        group.itemList.forEach(item => {
          if (item.userAnswerFlag === 1) {
            // 给答案字段赋值
            if (this.isMulti) {
              group.answer.push(item.itemId);
            } else {
              group.answer = item.itemId;
            }
          }
        });
      });
    },
    // 多选点击tag清除标签事件 =》 找到对应的option，将userAnswerFlag置为0
    removeMultiOption(val, group) {
      group.itemList.forEach(item => {
        if (item.itemId === val) {
          this.$set(item, 'userAnswerFlag', 0);
        }
      });
      this.emitAnswer();
    }
  }
};
</script>
