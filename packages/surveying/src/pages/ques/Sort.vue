<!-- 创建时间2021/12/01 13:19:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：排序题 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">

    <!-- 答题模式 -->
    <template v-if="mode === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="ml20" :is-front="mode === QuesMode.write" :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div class="ml20 mt21">
        <div
          v-for="item in quesData.itemList"
          :key="item._id"
          :class="mode === QuesMode.view?['mt8','pv3']:['mt10','pv3']"
        >
          <div class="flex flex-top pointer" @click="doSort(item)">
            <div class="yxtulcdsdk-survey-ques__sort-box pointer text-center" :class="getSortNo(item.itemId)?'bg-primary-6-i color-white':''">{{ getSortNo(item.itemId) }}</div>
            <div class="ml8 ws-prewrap" v-html="item.content"></div>
          </div>
          <attachments-container :class="mode === QuesMode.write?'':'ml28'" :is-front="mode === QuesMode.write" :attachments="item.contentAttachList" />
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode } from '../../core/enum';
import { guid } from '../../core/utils';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
export default {
  components: { AttachmentsContainer },
  mixins: [ques, quesCheck],
  name: 'Sort',
  data() {
    return {
      QuesMode
    };
  },
  computed: {
    answerDetail() {
      const answerDetail = this.quesData.answerDetail || [];
      answerDetail.sort((a, b) => a.sortNo - b.sortNo);
      return answerDetail.map((item, index) => ({ ...item, sortNo: index + 1 }));
    }
  },
  methods: {
    del(index) {
      this.quesData.itemList.splice(index, 1);
    },
    addOption() {
      this.quesData.itemList.push({ _id: guid(), content: `${this.$t('pc_survey_lbl_option')/** 选项 */}${this.quesData.itemList.length + 1}` });
    },
    doSort(item) {
      if (this.disabled) return;
      if (this.mode !== QuesMode.write) return;
      if (!this.quesData.answerDetail) {
        this.$set(this.quesData, 'answerDetail', []);
      }
      const sortNo = this.getSortNo(item.itemId);
      if (sortNo) { // 取消
        this.quesData.answerDetail.remove(answer => answer.itemId === item.itemId);
      } else { // 添加排序
        this.quesData.answerDetail.push({ itemId: item.itemId, sortNo: this.answerDetail.length + 1 });
      }
      this.quesData.answered = this.answerDetail.length === this.quesData.itemList.length && this.answerDetail.length > 0;
      this.emitAnswer();
    },
    getSortNo(itemId) {
      const answer = this.answerDetail.find(item => item.itemId === itemId);
      return answer && answer.sortNo || '';
    }
  }
};
</script>
