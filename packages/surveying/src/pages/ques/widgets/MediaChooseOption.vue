<!-- 创建时间2021/11/16 09:37:49 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：文字、视频或者图片投票题的选择项 -->
<template>
  <div
    class="yxtulcdsdk-survey-ques__media-option"
    :style="style"
    :class="mode === QuesMode.write?'media-option--write': ''"
    @click="handleClick"
  >
    <attachment-viewer
      :width="mediaWidth"
      :height="mediaHeight"
      :mode="mode"
      :file.sync="fileData"
      @delete="del"
    />
    <div class="mt12">
      <!-- 非管理端查看学员调查问卷详情模式 -->
      <custom-choice-box
        :disabled="disabled"
        :icon-offset="4"
        :checked="quesOptionData.userAnswerFlag"
        :is-multi="isMulti"
      >
        <yxt-tooltip :content="quesOptionData.content" open-filter>
          <div v-if="mode === QuesMode.write" class="flex-1 w0 ellipsis ml8">
            {{ quesOptionData.content }}
          </div>
          <div v-else class="ml8 ellipsis3">
            {{ quesOptionData.content }}
          </div>
        </yxt-tooltip>
      </custom-choice-box>
      <slot name="voteLimit"></slot>
    </div>
  </div>
</template>

<script>
import AttachmentViewer from '../../components/attachmentTextfield/AttachmentViewer.vue';
import { QuesMode } from '../../../core/enum';
import CustomChoiceBox from '../../components/customChoiceBox/CustomChoiceBox.vue';
export default {
  components: { AttachmentViewer, CustomChoiceBox },
  name: 'MediaChooseOption',
  props: {
    width: {
      type: Number,
      default: 192
    },
    mediaWidth: {
      type: Number,
      default: 168 // 168  95
    },
    mediaHeight: {
      type: Number,
      default: 168 // 168  95
    },
    quesOptionData: {
      type: Object,
      default: () => ({})
    },
    mode: {
      default: QuesMode.edit
    },
    isMulti: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return { QuesMode };
  },
  computed: {
    style() {
      return { width: this.width + 'px', padding: (this.width - this.mediaWidth) / 2 + 'px' };
    },
    fileData: {
      get() {
        return this.quesOptionData.contentAttachList && this.quesOptionData.contentAttachList.length ? this.quesOptionData.contentAttachList[0] : {};
      },
      set(val) {
        this.quesOptionData.contentAttachList.splice(0, 1, val);
      }
    }
  },
  methods: {
    del() {
      this.$emit('delete');
    },
    blur() {
      this.$emit('blur');
    },
    handleClick() {
      if (this.mode === QuesMode.write) this.$emit('click');
    }
  }
};
</script>
