<!-- 创建时间2021/12/01 08:47:19 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：分类题、排序题 多级量表的 选项部件 -->
<template>
  <div class="flex flex-top yxtulcdsdk-survey-ques__options">
    <span class="yxtulcdsdk-survey-ques__options_img mover" :class="move? '':'yxtulcdsdk-survey-ques__options_img--hidden'"></span>
    <yxt-input
      v-if="pureText"
      v-model="text"
      class="flex-1"
      :maxlength="1000"
      :readonly="readonly"
      :placeholder="placeholder||$t('pc_survey_lbl_pleaseenter'/** 请输入*/)"
      v-blur-trim
      @blur="blur"
    />
    <attachment-textfield
      v-else
      :maxlength="1000"
      class="flex-1"
      :content.sync="quesOptionData[contentKeyName]"
      :attachments.sync="quesOptionData.contentAttachList"
      :menus="menus"
      :placeholder="placeholder"
      @blur="blur"
    />
    <div class="w24">
      <media-svg
        v-if="showDelete"
        class="mt12 ml8 del-bar pointer flex-shrink-0"
        width="16px"
        height="16px"
        is-stc
        icon-class="close"
        @click.native="del"
      />
    </div>
  </div>
</template>

<script>
import AttachmentTextfield from '../../components/attachmentTextfield/AttachmentTextfield.vue';
import { blurTrimDirective } from 'packages/common-util/directives.js'
export default {
  components: { AttachmentTextfield },
  name: 'ClassifyOption',
  props: {
    quesOptionData: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: ''
    },
    showDelete: {
      type: Boolean,
      default: false
    },
    pureText: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: ''
    },
    menus: {},
    readonly: {
      type: Boolean,
      default: false
    },
    move: {
      type: Boolean,
      default: true
    },
    // 用来设定attachment-textfield组件中content从quesOptionData中取值的key，
    contentKeyName: {
      type: String,
      default: 'content'
    }
  },
  data() {
    return {};
  },
  computed: {
    text: {
      get() {
        return this.content;
      },
      set(val) {
        this.$emit('update:content', val);
      }
    }
  },
  methods: {
    del() {
      this.$emit('delete');
    },
    blur() {
      this.$emit('blur');
    }
  },
  directives: {
    'blur-trim': blurTrimDirective
  }
};
</script>
