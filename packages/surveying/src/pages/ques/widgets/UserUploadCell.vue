<!-- 创建时间2021/12/29 10:50:17 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：学员答题上传文件的样式 -->
<template>
  <div class="flex">
    <div class="yxtulcdsdk-survey-ques__upload-cell flex flex-mid color-gray-7">
      <div class="flex-grow flex-shrink-1 ellipsis flex flex-mid" :class="{'pointer hover-primary-6': disabled}" @click="handleClick">
        <media-svg
          icon-class="upload_file_icon"
          is-stc
          width="16px"
          height="16px"
        />
        <yxt-tooltip :content="fileName + suffix" open-filter placement="top">
          <span class="flex-grow flex-shrink-1 ellipsis ml4">{{ fileName }}</span>
        </yxt-tooltip>

        <span class="flex-shrink-0">{{ suffix }}</span>
      </div>
      <!-- 进度条 -->
      <div v-if="file._status === UploadStatus.going" class="w160 ml12">
        <yxt-progress :stroke-width="6" :percentage="parseInt(file.progress * 100) " />
      </div>
      <!-- 文件大小 -->
      <span v-if="fileFailAndNotGoing && fileSize" class="ml12">{{ fileSize }}</span>
      <!-- 一些状态: 上传失败 转码中 转码失败  -->
      <span v-if="error || fileFail" class="ml12 standard-size-12 yxtulcdsdk-flex-shrink-0" :class="fileFail ? 'color-danger' : 'color-gray-6'">{{ fileFail ? $t('pc_survey_tip_uploadefail') : error }}</span>
      <!-- 下载 -->
      <media-svg
        v-if="fileFailAndNotGoing && showDownloadBtn"
        class="pointer ml12"
        is-stc
        icon-class="uplod_file_download"
        width="16px"
        height="16px"
        @click.native="download"
      />
      <!-- 删除按钮 -->
      <media-svg
        v-if="!disabled"
        class="upload-delete hover-primary-6 ml12 pointer"
        is-stc
        icon-class="upload_file_close"
        width="16px"
        height="16px"
        @click.native="del"
      />
    </div>
    <file-big-viewer
      ref="bigViewer"
      :file-id="viewObject.fileId"
      :file-type="viewObject.fileType"
      :file-info="viewObject.fileInfo"
      :image-url="viewObject.fileUrl"
      :video-options="viewObject.fileOptions"
    />
  </div>
</template>

<script>
import MediaSvg from '../../components/MediaSvg.vue';
import { EnumFileType, UploadStatus } from '../../../core/enum';
import { downFileUrl, handleFileSize } from '../../../core/utils';
import FileBigViewer from '../../components/attachmentTextfield/FileBigViewer.vue';
import { getFileData, getFileDownloadUrl } from '../../../services/base.service';
export default {
  name: 'UserUploadCell',
  components: { MediaSvg, FileBigViewer },
  props: {
    file: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showDownloadBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      UploadStatus,
      error: '',
      viewObject: {
        fileType: '',
        fileId: '',
        fileUrl: '',
        fileOptions: []
      }
    };
  },
  computed: {
    suffix() {
      const pointIndex = (this.file.fileName || '').lastIndexOf('.');
      if (pointIndex >= 0) {
        return this.file.fileName.substr(pointIndex).toLowerCase();
      }
      return '';
    },
    fileName() {
      const pointIndex = (this.file.fileName || '').lastIndexOf('.');
      if (pointIndex > 0) {
        return this.file.fileName.substr(0, pointIndex);
      }
      return this.file.fileName;
    },
    fileSize() {
      const [fileSize, value] = handleFileSize(this.file.fileSize / 1000);
      if (value > 0) return fileSize;
      return '';
    },

    // 文件上传失败
    fileFail() {
      return this.file._status === UploadStatus.fail;
    },

    // 文件没有上传失败并且没有上传中
    fileFailAndNotGoing() {
      return !this.fileFail && this.file._status !== UploadStatus.going;
    }
  },
  methods: {
    del() {
      this.$emit('delete');
    },
    async handleClick() {
      // 作答页面和文件上传失败点击取消事件
      if (!this.disabled || this.fileFail) return;

      const viewObject = { fileType: this.file.fileType };
      viewObject.fileId = this.file.fileId;
      if (this.file.fileType === EnumFileType.image) {
        viewObject.fileUrl = this.file.fileUrl;
        if (!viewObject.fileUrl) {
          const res = await getFileData(this.file.fileId);
          const data = res && res.data && res.data.playDetails || [];
          const url = data && data.length ? data[0].url : '';
          viewObject.fileUrl = url;
        }
      } else {
        const suffix = this.file.fileName.substring(this.file.fileName.lastIndexOf('.'));
        if (suffix === '.zip' || suffix === '.rar') {
          this.$message.warning(this.$t('pc_survey_msg_zipdownloadview')/** 压缩文件请下载后查看 */);
          return;
        }
        if (!this.file.fileId) return;
        const { data } = await getFileData(this.file.fileId);
        if (!(data && data.playDetails && data.playDetails.length)) {
          this.$message.warning(this.$t('pc_survey_tip_transcoding')/** 转码中 */);
          return;
        }
        switch (this.file.fileType) {
          case EnumFileType.video:
          case EnumFileType.audio:
            viewObject.fileOptions = data && data.playDetails && data.playDetails.map(item => ({ fileFullUrl: item.url, resolution: item.desc, fileId: this.file.fileId })) || [];
            break;
          case EnumFileType.file:
            const list = data && data.playDetails && data.playDetails.map(item => ({ url: item.url })) || [];
            viewObject.fileInfo = { width: 960, list };
            break;
          default:
            break;
        }
      }

      this.viewObject = viewObject;
      this.$nextTick(() => {
        this.$refs.bigViewer.show();
      });
    },
    async download() {
      if (this.file.fileId) {
        const { data } = await getFileDownloadUrl(this.file.fileId, this.file.fileSourceType);
        downFileUrl(this.file.fileName, data);
      } else {
        downFileUrl(this.file.fileName, this.file.fileUrl);
      }
    }
  }
};
</script>
