<!-- 创建时间2021/11/12 12:59:46 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：选择题、判断题的选择项 -->
<template>
  <div
    class="flex flex-top yxtulcdsdk-survey-ques__options"
  >

    <!-- 答题模式下的显示 -->
    <template v-if="mode === QuesMode.write">
      <custom-choice-box
        :checked="quesOptionData.userAnswerFlag"
        :is-multi="isMulti"
        :disabled="disabled"
        class="flex-1"
        :icon-offset="3"
      >
        <div class="ml8 wp100">
          <div class="flex flex-vertical">
            <span class="minh22 ws-prewrap" v-html="quesOptionData.content"></span>
            <template v-if="quesOptionData.userAnswerFlag && quesOptionData.itemNote===1 ">
              <yxt-input
                v-if="!disabled"
                ref="inputExplain"
                v-model="quesOptionData.answerExplain"
                v-blur-trim
                :disabled="quesOptionData.userAnswerFlag!==1 || disabled"
                size="small"
                type="textarea"
                :autosize="{ minRows: 1}"
                :maxlength="quesData.maxLength>0?quesData.maxLength:''"
                show-word-limit
                class="yxtulcdsdk-survey-ques__options-user-input inline-block flex-shrink-0"
                :placeholder="$t('pc_survey_lbl_pleaseenter'/**请输入*/)"
                @blur="blur"
                @click.native.stop
              />
              <div v-else class="yxtulcdsdk-survey-fake-input mt8">{{ quesOptionData.answerExplain }}</div>
            </template>
            <slot name="voteLimit"></slot>
          </div>
          <div class="flex ml-24">
            <attachments-container
              class=" flex-1 w0"
              is-front
              :scale="layoutType === QuesOptionLayoutCol.one ? 1: 2"
              :attachments="quesOptionData.contentAttachList"
            />
          </div>
          <slot name="voteStatic"></slot>
        </div>
      </custom-choice-box>
    </template>
  </div>
</template>

<script>
import { QuesMode, QuesOptionLayoutCol } from '../../../core/enum';
import CustomChoiceBox from '../../components/customChoiceBox/CustomChoiceBox.vue';
import AttachmentsContainer from '../../components/attachmentTextfield/AttachmentsContainer.vue';
import { blurTrimDirective } from 'packages/common-util/directives.js';

export default {
  components: {
    CustomChoiceBox,
    AttachmentsContainer
  },
  name: 'ChooseOption',
  props: {
    mode: {
      default: QuesMode.edit
    },
    quesOptionData: {
      type: Object,
      default: () => ({})
    },
    quesData: {
      type: Object,
      default: () => ({})
    },
    showDelete: {
      type: Boolean,
      default: false
    },
    layoutType: {
      default: QuesOptionLayoutCol.one
    },
    isMulti: {
      type: Boolean,
      default: false
    },
    menus: {
      type: Array,
      default: () => ['image', 'video', 'audio', 'richText']
    },
    disableSort: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      QuesOptionLayoutCol,
      QuesMode,
      val: ''
    };
  },
  watch: {
    'quesOptionData.userAnswerFlag'(v) {
      v && this.$nextTick(()=> {
        this.$refs.inputExplain && this.$refs.inputExplain.focus();
      });
    }
  },
  methods: {
    blur() {
      this.$emit('changeNote');
    }
  },
  directives: {
    'blur-trim': blurTrimDirective
  }
};
</script>
