<!-- 创建时间2021/12/02 19:31:53 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：上传题 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">

    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div class="ml20 mt16">
        <yxtbiz-upload
          v-if="!disabled"
          ref="bizUploadDrag"
          :disabled="disabled"
          :ticket="getTicket && getTicket()"
          :filters="fileFiles"
          :auto-upload="false"
          config-key="AttachConfigKey"
          app-code="survey"
          module-name="front"
          function-name="answer"
          multipe
          :drag="!quesData.userAnswer||quesData.userAnswer.length===0"
          :files-filter="filesFilter"
          :files-added="filesAdd"
          :on-progress="onProgress"
          :on-uploaded="onUploaded"
          :on-error="onError"
        >
          <yxt-button
            v-if="quesData.userAnswer&&quesData.userAnswer.length"
            class="color-gray-8"
            icon="yxt-icon-upload2"
            plain
          >
            {{ $t('pc_survey_upload_file') }}
          </yxt-button>
        </yxtbiz-upload>
        <user-upload-cell
          v-for="(item,index) in quesData.userAnswer"
          :key="index"
          class="mt16"
          :file="item"
          :disabled="disabled"
          @delete="deleteFile(index)"
        />
      </div>
    </template>

  </div>
</template>

<script>
// import { mapState } from 'vuex';
import { EnumFileType, QuesMode, TranscodingStatus, UploadStatus } from '../../core/enum';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import quesCheck from '../../mixins/quesCheck';
import ques from '../../mixins/ques';
import UserUploadCell from './widgets/UserUploadCell.vue';
import { getFileData } from '../../services/base.service';

const FILTER = '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.w4v,.m4a,.wma,.wav,.mp3,.amr,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pps,.pdf,.zip,.rar,.jpg,.jpeg,.gif,.png,.bmp,.ico,.svg';
export default {
  components: { AttachmentsContainer, UserUploadCell },
  name: 'UploadQues',
  mixins: [ques, quesCheck],
  data() {
    return {
      QuesMode,
      fileFiles: FILTER,
      canRemoveFiles: []
    };
  },
  inject: ['getTicket'],
  computed: {
    // 正在上传的文件id
    progressIds() {
      if (this.quesData.userAnswer && this.quesData.userAnswer.length) {
        return this.quesData.userAnswer.filter(item => {
          return item._status === UploadStatus.going;
        }).map(item => item.fileId || item.uuid) || [];
      }

      return [];
    }
  },
  methods: {
    deleteFile(index) {
      const file = this.quesData.userAnswer[index];
      this.quesData.userAnswer.splice(index, 1);
      this.emitAnswer();

      file && file._origin && file._origin.abort();
    },

    // 获取文件后缀
    getFileExtension(fileName) {
      return fileName.indexOf('.') > -1 ? `.${ fileName.split('.').pop().toLowerCase() }` : '';
    },

    checkFile(files = []) {
      const filters = FILTER.split(',');
      const filterFiles = files.filter(item => {
        const extension = item.extension || this.getFileExtension(item.name);
        return filters.indexOf(extension) >= 0;
      });

      let msg;
      if (files.length !== filterFiles.length) {
        msg = this.$t('pc_survey_filetype_not_matched' /* 文件类型不符合 */);
      }

      const answerLength = this.quesData.userAnswer ? this.quesData.userAnswer.length : 0;
      if ((answerLength + filterFiles.length) > 20) {
        return { files: [], msg: this.$t('pc_survey_msg_maxfilesize', [20]) };
      }

      return { files: filterFiles, msg };
    },

    filesFilter(files) {
      const result = this.checkFile(files);
      if (result.msg) {
        this.$message.error(result.msg);
      }
      if (result.files.length === 0) return [];
      return result.files;
    },

    filesAdd(files) {
      if (!files.length) return;

      const f = files.map(item => ({
        fileName: item.name,
        fileSize: item.size,
        uuid: item.uuid,
        progress: 0,
        fileType: this.getFileType(item.fileType),
        fileStatus: ['image', 'zip', 'rar'].includes(item.fileType) ? TranscodingStatus.success : TranscodingStatus.going,
        _status: UploadStatus.going,
        _origin: item
      }));

      this.canRemoveFiles = this.canRemoveFiles.concat(files);

      if (!this.quesData.userAnswer) this.$set(this.quesData, 'userAnswer', []);
      this.quesData.userAnswer = this.quesData.userAnswer.concat(f);
      this.setAnswerStatus(this.quesData);

      // 开始上传
      this.$refs.bizUploadDrag && this.$refs.bizUploadDrag.start();
    },
    getFileType(type) {
      switch (type) {
        case 'image':
          return EnumFileType.image;
        case 'video':
          return EnumFileType.video;
        case 'audio':
          return EnumFileType.audio;
        case 'zip':
        case 'doc':
          return EnumFileType.file;
        default:
          return '';
      }
    },
    onProgress(file, progress) {
      const f = this.quesData.userAnswer.find(item => item.uuid === file.uuid);
      if (!f) return;
      if (!f.progress) this.$set(f, 'progress', progress);
      else f.progress = progress;
    },
    async onUploaded(file) {
      const f = this.quesData.userAnswer.find(item => item.uuid === file.uuid);
      if (!f) return;
      f._status = UploadStatus.success;
      f.fileId = file.id;
      delete f._origin;
      if (f.fileType === EnumFileType.image) {
        const res = await getFileData(f.fileId);
        const data = res && res.data && res.data.playDetails || [];
        const url = data && data.length ? data[0].url : '';
        f.fileDataType = 1;
        f.fileUrl = url;
      }
      this.emitAnswer();
    },
    onError(error, file) {
      console.log(error);
      const f = this.quesData.userAnswer.find(item => item.uuid === file.uuid);
      if (!f) return;
      this.$set(f, '_status', UploadStatus.fail);
      this.setAnswerStatus(this.quesData);
    },

    // 退出页面的时候把正在上传的文件取消上传
    removeFileAndClear() {
      if (this.progressIds.length && this.canRemoveFiles.length) {
        this.canRemoveFiles.forEach(item => {
          if (this.progressIds.includes(item.id) || this.progressIds.includes(item.uuid)) {
            item.abort();
          }
        });
      }
    }
  },
  beforeDestroy() {
    this.removeFileAndClear();
  }
};
</script>
