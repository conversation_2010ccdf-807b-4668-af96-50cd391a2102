<!-- 创建时间2021/11/30 16:10:54 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：选择题 单选+多选-->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <div v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</div>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div class="ml20">
        <div class="flex flex-wrap">
          <template v-for="item in quesData.itemList">
            <choose-option
              :key="item._id"
              :is-multi="isMulti"
              class="mt16"
              :disabled="disabled"
              :class="quesData.layoutType === QuesOptionLayoutCol.one ?'wp100':'wp50 pr8'"
              :ques-option-data="item"
              :ques-data="quesData"
              :mode="mode"
              @click.native="change(item)"
              @changeNote="emitAnswer"
            >
              <template slot="voteStatic" v-if="voteRankMode">
                <div class="mt8 ml-24 flex flex-mid" :class="item.userAnswerFlag? 'color-primary-6': 'color-gray-7'">
                  <yxtf-progress
                    class="w160 flex-shrink-0"
                    :percentage="item.percentage"
                    :show-text="false"
                    :stroke-width="4"
                    :color="!item.userAnswerFlag? '#8c8c8c': undefined"
                  />
                  <span class="ml8 flex-shrink-0">{{ $t('pc_survey_lbl_votenums'/** 票 */, [item.cnt]) }} &nbsp;{{ `${item.percentage}%` }}</span>
                </div>
              </template>
            </choose-option>
          </template>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, QuesOptionLayoutCol, SurveyQuesType, SurveyTemplateType } from '../../core/enum';
import ChooseOption from './widgets/ChooseOption.vue';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
import { dealLineFeed } from '../../core/utils';
export default {
  components: { ChooseOption, AttachmentsContainer },
  name: 'Judgment',
  mixins: [ques, quesCheck],

  data() {
    return {
      QuesMode,
      QuesOptionLayoutCol
    };
  },
  computed: {
    isMulti() {
      return this.quesData && this.quesData.quesType === SurveyQuesType.multiChoose;
    },
    isDetemine() {
      // 是否培训使用的鉴定表，需要隐藏输入选项设置
      return this.templateType === SurveyTemplateType.detemineSlide || this.templateType === SurveyTemplateType.detemineJudge;
    }
  },
  mounted() {
    this.emitLogic();
  },
  methods: {
    dealLineFeed,
    change(item) {
      if (this.disabled) return;
      this.$set(item, 'userAnswerFlag', 1);
      this.quesData.itemList.forEach(it => {
        if (it !== item) {
          this.$set(it, 'userAnswerFlag', 0);
        }
      });
      this.emitAnswer();
    }
  }
};
</script>
