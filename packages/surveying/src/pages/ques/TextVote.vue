<!-- 创建时间2021/12/07 17:57:23 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：文字投票题-->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">

    <!-- 答题模式 -->
    <template v-if="mode === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div
        v-if="multiSelectCountTip"
        class="ml20 color-gray-6 standard-size-12"
        :class="{
          'mt12': quesData.enabledExplain,
          'mt4': !quesData.enabledExplain
        }"
      >
        {{ multiSelectCountTip }}
      </div>
      <div class="ml20 flex flex-wrap">
        <div
          v-for="(item) in quesData.itemList"
          :key="item._id"
          class="flex flex-wrap mt16"
          :class="[(quesData.layoutType === QuesOptionLayoutCol.one || voteRankMode) ?'wp100':'wp50 pr8']"
        >
          <choose-option
            :is-multi="isMulti"
            :ques-option-data="item"
            :ques-data="quesData"
            :mode="mode"
            :class="voteRankMode ? 'color-gray-9':''"
            :disabled="disabled || getDisabledStatus(item)"
            @click.native="change(item)"
          >
            <template slot="voteLimit" v-if="voteRankMode">
              <div class="mt8 ml-24 flex flex-mid" :class="item.userAnswerFlag? 'color-primary-6': 'color-gray-7'">
                <yxtf-progress
                  class="w160 flex-shrink-0"
                  :percentage="item.percentage"
                  :show-text="false"
                  :stroke-width="4"
                  :color="!item.userAnswerFlag? '#8c8c8c': undefined"
                />
                <span class="ml8 flex-shrink-0">{{ $t('pc_survey_lbl_votenums'/** 票 */, [item.cnt]) }} &nbsp;{{ `${item.percentage}%` }}</span>
              </div>
            </template>
            <span v-else-if="getLimitNum(item)" slot="voteLimit" class="color959595 standard-size-12">({{ $t('pc_survey_lbl_votenumber'/**票数 */) }}：{{ getLimitNum(item) - item.remainNum }}/{{ getLimitNum(item) }}<span v-if="!item.remainNum" class="ml8">{{ $t('pc_survey_lbl_votefull'/**已投满 */) }}</span>)</span>
          </choose-option>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, QuesOptionLayoutCol, SurveyQuesType, VoteQuesLimitType } from '../../core/enum';
import ChooseOption from './widgets/ChooseOption.vue';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
export default {
  components: { ChooseOption, AttachmentsContainer },
  mixins: [ques, quesCheck],
  name: 'Choose',
  data() {
    return {
      QuesMode,
      QuesOptionLayoutCol,
      VoteQuesLimitType
    };
  },
  computed: {
    isMulti() {
      const type = this.quesData && this.quesData.quesType;
      return type === SurveyQuesType.multiTextChoose;
    },
    optionLength() {
      return this.quesData.itemList.length;
    },
    selectedCount() {
      let count = 0;
      this.quesData.itemList.forEach((item => {
        if (item.userAnswerFlag) count++;
      }));
      return count;
    }
  },
  methods: {
    getDisabledStatus(item, change) {
      // 总数量上限
      if (this.getLimitNum(item) > 0 && !item.remainNum && (!item.userAnswerFlag || !change)) {
        return true;
      }
      // 最多选择
      if (this.quesData.maxChoose > 0 && this.selectedCount >= this.quesData.maxChoose && !item.userAnswerFlag) {
        return true;
      }
      return false;
    },
    change(item) {
      if (this.disabled || this.getDisabledStatus(item, true)) return;
      if (this.isMulti) {
        this.$set(item, 'userAnswerFlag', item.userAnswerFlag === 1 ? 0 : 1);
      } else {
        this.$set(item, 'userAnswerFlag', item.userAnswerFlag === 1 ? 0 : 1);
        this.quesData.itemList.forEach(it => {
          if (it !== item && it.userAnswerFlag === 1) {
            this.$set(it, 'userAnswerFlag', 0);
          }
        });
      }
      this.emitAnswer();
    }
  }
};
</script>
