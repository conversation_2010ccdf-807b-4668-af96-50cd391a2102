<!-- 创建时间2021/11/30 17:33:54 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：分类题目-->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>

      <div class="ml20 flex mt16">
        <div class="w245">
          <div class="text-center wp100">{{ $t('pc_survey_lbl_optiongroup')/**选项组 */ }}</div>
          <div class="mt26">
            <vuedraggable
              v-model="quesData.chosenList"
              :disabled="disabled"
              handle=".drag-bar"
              :group="{name: 'site' + quesData.questionId,pull:'clone',put:false}"
              :clone="getCloneData"
              :sort="false"
              @choose="startChoose"
              @unchoose="endChoose"
            >
              <div
                v-for="(item,index) in quesData.chosenList"
                :key="index"
                :class="{'classify-option--disabled':optionIsRelated(item.itemId),'drag-bar':!optionIsRelated(item.itemId),'sortable-chosen-custom':currentDragId === item.itemId}"
                class="text-center classify-option-write color-gray-8 ws-prewrap"
              >{{ item.content }}</div>
            </vuedraggable>
          </div>
        </div>
        <div class="w64 flex-shrink-1"></div>
        <div class="w400">
          <div class="text-center wp100">{{ $t('pc_survey_lbl_typearea')/**分类区*/ }}</div>
          <div class="mt26">
            <div v-for="(item,index) in quesData.classifyList" :key="index" class="classify-group-write flex flex-vertical">
              <div class="classify-group-title text-center ws-prewrap">{{ item.content }}</div>
              <vuedraggable
                v-model="item._relatedOptionIds"
                class=" flex-1 flex flex-wrap classify-group-write__put"
                :group="{name: 'site' + quesData.questionId,pull:false ,put:groupPut}"
                :sort="false"
                :disabled="disabled"
                handle=".drag-bar"
                @change="change"
              >
                <div v-for="(itemId,opIndex) in item._relatedOptionIds" :key="itemId" class="classify-group-write__put-item color-gray-9 flex flex-mid">
                  <div class="flex-1 flex-shrink-0 ellipsis ws-prewrap">{{ relatedOptionName(itemId) }}</div>
                  <div class="ml4">
                    <media-svg
                      v-if="!disabled"
                      class="pointer"
                      is-stc
                      width="16px"
                      height="16px"
                      icon-class="drag_item_del"
                      @click.native="delRelatedOption(item._relatedOptionIds,opIndex)"
                    />
                  </div>
                </div>
                <span v-if="!item._relatedOptionIds||item._relatedOptionIds.length === 0" class="no-data flex flex-mid flex-center color-gray-6 standard-size-12 pb20">{{ $t('pc_survey_lbl_draghere')/**拖到此处*/ }}</span>
              </vuedraggable>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { QuesMode, SurveyQuesType } from '../../core/enum';
import MediaSvg from '../components/MediaSvg.vue';
import vuedraggable from 'vuedraggable';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
export default {
  components: { MediaSvg, AttachmentsContainer, vuedraggable },
  name: 'Classify',
  mixins: [ques, quesCheck],
  data() {
    return {
      QuesMode,
      currentDragId: ''
    };
  },
  computed: {
    isMulti() {
      return this.quesData && this.quesData.quesType === SurveyQuesType.multiChoose;
    },
    optionLength() {
      let count = 0;
      this.quesData.itemGroups.forEach(group => {
        count += group.itemList.length;
      });
      return count;
    }
  },
  mounted() {
    this.initAnswerData();
    this.emitLogic();
  },
  methods: {
    startChoose(e) {
      const item = this.quesData.chosenList[e.oldIndex];
      this.currentDragId = item.itemId;
    },
    endChoose() {
      this.currentDragId = '';
    },
    getCloneData(chosenItem) {
      return chosenItem.itemId;
    },
    groupPut(e, t) {
      // 排除不同组的数据
      return e.options.group.name === t.options.group.name;
    },
    // 根据答案初始数据
    initAnswerData() {
      if (this.mode !== QuesMode.write && this.mode !== QuesMode.managerView) return;
      this.quesData.classifyList.forEach(item => {
        if (!item._relatedOptionIds) this.$set(item, '_relatedOptionIds', []);
      });
      const answerDetail = this.quesData.answerDetail || {};
      for (const key in answerDetail) {
        const classify = this.quesData.classifyList.find(item => item.itemId === key);
        classify._relatedOptionIds = answerDetail[key] || [];
      }
    },
    relatedOptionName(itemId) {
      const obj = this.quesData.chosenList.find(item => item.itemId === itemId);
      return obj && obj.content;
    },
    // 选项是否被选
    optionIsRelated(itemId) {
      return this.quesData.classifyList.some(item => item._relatedOptionIds && item._relatedOptionIds.includes(itemId));
    },
    change() {
      this.updateAnswerData();
      this.emitAnswer();
    },
    delRelatedOption(list, index) {
      list.splice(index, 1);
      this.updateAnswerData();
      this.emitAnswer();
    },
    updateAnswerData() {
      const answerDetail = {};
      this.quesData.classifyList.forEach(item => {
        answerDetail[item.itemId] = item._relatedOptionIds;
      });
      this.quesData.answerDetail = answerDetail;
    }
  }
};
</script>
