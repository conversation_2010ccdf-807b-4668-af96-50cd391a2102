<!-- 创建时间2021/12/07 09:33:48 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：分页说明 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div class="ws-prewrap" v-html="quesData.content"></div>
    </template>

  </div>
</template>

<script>
import { QuesMode } from '../../core/enum';
export default {
  name: 'PageInfo',
  components: { },
  props: {
    mode: {
      default: QuesMode.edit
    },
    quesData: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    return {
      QuesMode
    };
  },
  methods: {
    blur() {
      if (!this.quesData.content) {
        this.$set(this.quesData, 'content', this.$t('pc_survey_lbl_ques_type_' + this.quesData.quesType)/** 分页说明 */);
      }
    }
  }
};
</script>
