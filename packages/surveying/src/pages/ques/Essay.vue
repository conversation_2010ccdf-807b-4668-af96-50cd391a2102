<!-- 创建时间2021/12/02 19:03:58 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：简答题 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode !== QuesMode.write?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div class="ml20 mt16 mxw800">
        <yxt-input
          v-if="!disabled"
          v-model="quesData.userAnswer"
          type="textarea"
          :placeholder="$t('pc_survey_lbl_pleaseenter'/**请输入*/)"
          :disabled="disabled"
          :autosize="{ minRows: 4, maxRows: 6}"
          show-word-limit
          :maxlength="quesData.maxLength>0?quesData.maxLength:''"
          v-blur-trim
          @blur="blur"
        />
        <div v-else class="yxtulcdsdk-survey-fake-input yxtulcdsdk-survey-fake-input--multi">{{ quesData.userAnswer }}</div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, QuesScoreType } from '../../core/enum';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
import { dealLineFeed } from '../../core/utils';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import { blurTrimDirective } from 'packages/common-util/directives.js'
export default {
  components: { AttachmentsContainer },
  mixins: [ques, quesCheck],
  name: 'Essay',
  data() {
    return {
      QuesMode,
      QuesScoreType
    };
  },
  methods: {
    dealLineFeed,
    blur() {
      this.emitAnswer();
    }
  },
  directives: {
    'blur-trim': blurTrimDirective
  }
};
</script>
