<!-- 创建时间2021/12/07 17:17:12 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：分割线或者页面 -->
<template>
  <div class="yxtulcdsdk-survey-ques">
    <!-- 答题模式 -->
    <template v-if="mode === QuesMode.write">
      <yxt-divider />
    </template>

  </div>
</template>

<script>
import { QuesMode } from '../../core/enum';
export default {
  components: { },
  name: 'Sort',
  props: {
    mode: {
      default: QuesMode.edit
    },
    pageIndex: {
      type: Number,
      default: null
    }

  },
  data() {
    return {
      QuesMode
    };
  }
};
</script>
