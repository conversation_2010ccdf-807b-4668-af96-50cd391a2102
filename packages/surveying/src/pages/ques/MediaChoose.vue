<!-- 创建时间2021/11/11 19:10:54 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：视频&图片投票题 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode!==QuesMode.write?'yxtulcdsdk-survey-ques--required':''">
    <!-- 答题模式 -->
    <template v-if="mode === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <attachments-container class="mt8 ml20" is-front :attachments="quesData.contentAttachList" />
      <!-- 题目说明 -->
      <div v-if="quesData.enabledExplain" class="yxtulcdsdk-survey-ques__desc">
        <div v-if="quesData.explainText" class="color-gray-7" v-html="quesData.explainText"></div>
        <attachments-container is-front :attachments="quesData.explainAttachList" />
      </div>
      <div
        v-if="multiSelectCountTip"
        class="ml20 color-gray-6 standard-size-12"
        :class="{
          'mt12': quesData.enabledExplain,
          'mt4': !quesData.enabledExplain
        }"
      >
        {{ multiSelectCountTip }}
      </div>
      <div class="mt16 ml20">
        <div
          class=" flex flex-wrap yxtulcdsdk-survey-ques-vote-wrap"
          :class="{
            'flex-vertical': voteRankMode
          }"
        >
          <template
            v-for="item in quesData.itemList"
          >
            <media-choose-option
              :key="item.itemId"
              :ques-option-data="item"
              :ques-data="quesData"
              class="mt16 write-option"
              :is-multi="isMulti"
              :width="260"
              :class="voteRankMode ? 'color-gray-9':''"
              :media-width="240"
              :media-height="isVideo?135:240"
              :mode="mode"
              :disabled="disabled || getDisabledStatus(item)"
              @click="change(item)"
            >
              <div v-if="!voteRankMode && getLimitNum(item)" slot="voteLimit" class="color959595 mt8 standard-size-12">{{ $t('pc_survey_lbl_votenumber'/**票数 */) }}：{{ getLimitNum(item) - item.remainNum }}/{{ getLimitNum(item) }}<span v-if="!item.remainNum">（{{ $t('pc_survey_lbl_votefull'/**已投满 */) }}）</span></div>
            </media-choose-option>
            <div
              v-if="voteRankMode"
              :key="item.itemId+'_rank'"
              class="ml16 mb16 flex flex-mid"
              :class="item.userAnswerFlag? 'color-primary-6': 'color-gray-7'"
            >
              <yxtf-progress
                class="w160 flex-shrink-0"
                :percentage="item.percentage"
                :show-text="false"
                :stroke-width="4"
                :color="!item.userAnswerFlag? '#8c8c8c': undefined"
              />
              <span class="ml8 flex-shrink-0">{{ $t('pc_survey_lbl_votenums'/** 票 */, [item.cnt]) }} &nbsp;{{ `${item.percentage}%` }}</span>
            </div>
          </template>
        </div>
      </div>
    </template>

  </div>
</template>

<script>
import { QuesMode, SurveyQuesType, VoteQuesLimitType } from '../../core/enum';
import MediaChooseOption from './widgets/MediaChooseOption.vue';
import AttachmentsContainer from '../components/attachmentTextfield/AttachmentsContainer.vue';
import ques from '../../mixins/ques';
import quesCheck from '../../mixins/quesCheck';
export default {
  components: { MediaChooseOption, AttachmentsContainer },
  mixins: [ques, quesCheck],
  name: 'MediaChoose',
  data() {
    return {
      QuesMode,
      SurveyQuesType,
      VoteQuesLimitType,
      moreClick: false,
      isOpen: false
    };
  },
  mounted() {
  },
  watch: {
  },
  computed: {
    isMulti() {
      return this.quesData.quesType === SurveyQuesType.multiVideoChoose || this.quesData.quesType === SurveyQuesType.multiPicChoose;
    },
    isVideo() {
      return this.quesData.quesType === SurveyQuesType.videoChoose || this.quesData.quesType === SurveyQuesType.multiVideoChoose;
    },
    selectedCount() {
      let count = 0;
      this.quesData.itemList.forEach((item => {
        if (item.userAnswerFlag) count++;
      }));
      return count;
    }
  },
  methods: {
    getDisabledStatus(item, change) {
      // 总数量上限
      if (this.getLimitNum(item) > 0 && !item.remainNum && (!item.userAnswerFlag || !change)) {
        return true;
      }
      // 最多选择
      if (this.quesData.maxChoose > 0 && this.selectedCount >= this.quesData.maxChoose && !item.userAnswerFlag) {
        return true;
      }
      return false;
    },
    change(item) {
      if (this.disabled || this.getDisabledStatus(item, true)) return;
      if (this.isMulti) {
        this.$set(item, 'userAnswerFlag', item.userAnswerFlag === 1 ? 0 : 1);
      } else {
        this.$set(item, 'userAnswerFlag', item.userAnswerFlag === 1 ? 0 : 1);
        this.quesData.itemList.forEach(it => {
          if (it.itemId !== item.itemId && it.userAnswerFlag === 1) {
            this.$set(it, 'userAnswerFlag', 0);
          }
        });
      }
      this.emitAnswer();
    }
  }
};
</script>
