<!-- 创建时间2021/12/06 20:19:22 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：个人信息 -->
<template>
  <div class="yxtulcdsdk-survey-ques" :class="quesData.mustAnswer&&mode!==QuesMode.write?'yxtulcdsdk-survey-ques--required yxtulcdsdk-survey-ques--required-info':''">
    <!-- 答题模式 -->
    <template v-if="mode-0 === QuesMode.write">
      <div v-if="showQuesType" class="multi-tag mb8">{{ $t(`pc_survey_lbl_ques_type_${quesData.quesType}`) }}</div>
      <div :class="{'yxt-weight-5': !isRichText(quesData.content)}" class="flex flex-top standard-size-16 pr">
        <span v-if="quesData.mustAnswer" class="require-tag"></span>
        <span v-if="quesData.showQuesNum" class="mw14 mr6 yxtulcdsdk-flex-shrink-0">{{ index }}.</span>
        <div class="flex-1">
          <span class="ws-prewrap" v-html="quesData.content"></span>
        </div>
      </div>
      <div class="ml20 mt16">
        <yxt-date-picker
          v-if="quesData.answerMode === 1"
          v-model="quesData.answer"
          :disabled="disabled"
          class="w164important"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="$t('pc_survey_lbl_selectdate')"
          @change="emitAnswer"
        />
        <yxtbiz-area-select
          v-else-if="quesData.quesType === SurveyQuesType.city && quesData.sourceQuesType === SurveyQuesType.city && !disabled"
          :value="quesData.pcaId || []"
          @change="changeCity"
        />
        <yxt-input
          v-else-if="!disabled"
          v-model="quesData.answer"
          v-blur-trim
          class="mxw800 wp100"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 6}"
          :disabled="disabled"
          @blur="emitAnswer"
        />
        <div v-else class="mxw800 wp100 yxtulcdsdk-survey-fake-input">{{ quesData.answer }}</div>
      </div>
    </template>
  </div>
</template>

<script>
import { QuesMode, SurveyQuesType } from '../../core/enum';
import ques from '../../mixins/ques';
import { blurTrimDirective } from 'packages/common-util/directives.js';
export default {
  components: { },
  mixins: [ques],
  name: 'Info',
  data() {
    return {
      QuesMode,
      SurveyQuesType
    };
  },
  computed: {
    label() {
      return this.$t('pc_survey_lbl_ques_type_' + this.quesData.quesType);
    }
  },
  mounted() {
    if (this.quesData.quesType === SurveyQuesType.date) {
      return;
    }
    this.quesData.content = this.label;
  },
  methods: {
    changeCity({ ids, names }) {
      this.quesData.pcaId = ids;
      this.quesData.pcaName = names;
      this.quesData.answer = names.reduce((r, v) => {
        r += '-' + v;
        return r;
      });
      this.emitAnswer();
    }
  },
  directives: {
    'blur-trim': blurTrimDirective
  }
};
</script>
