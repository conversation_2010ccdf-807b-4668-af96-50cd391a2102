<template>
  <div class="flex flex-mid flex-center">
    <svg
      v-if="isDev&&isStc"
      class="dev-svg-icon"
      :style="iconStyle"
      aria-hidden="true"
    >
      <use :xlink:href="iconName" />
    </svg>
    <yxt-svg
      v-else
      :remote-url="mediaUrl"
      :icon-class="iconClass"
      :width="width"
      :height="height"
    />
  </div>
</template>

<script>
export default {
  name: 'MediaSvg',
  props: {
    iconClass: {
      type: String,
      required: true
    },
    front: {
      type: Boolean,
      default: false
    }, // 前台图标
    manage: {
      type: Boolean,
      default: false
    }, // 后台图标
    isStc: {
      type: Boolean,
      default: false
    }, // 静态
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    let mediaUrl = '';
    if (this.isStc) {
      mediaUrl = `${this.$staticBaseUrl}ufd/55a3e0/survey/pc/svg`;
    } else {
      // 老的媒体迁移过来的svg
      mediaUrl = `${this.$staticBaseUrl}ufd/55a3e0/survey/pc/svg/svgs`;

      // 老的公共svg
      if (this.front) {
        mediaUrl = `${this.$staticBaseUrl}ufd/3f5568/common/pc_foreground/svg`;
      }
      if (this.manage) {
        mediaUrl = `${this.$staticBaseUrl}ufd/3f5568/common/pc_backstage/svg`;
      }
    }

    return {
      mediaUrl
    };
  },
  computed: {
    isDev() {
      return false;
    },
    iconName() {
      return `#icon-${this.iconClass}`;
    },
    iconStyle() {
      return { width: this.width, height: this.height };
    }
  }
};
</script>
<style scoped>
.dev-svg-icon {
  width: 1em;
  height: 1em;
  overflow: hidden;
  vertical-align: -0.15em;
  fill: currentColor;
}
</style>
