<template>
  <div class="yxtbizf-br-4 bg-white flex-mid flex-center ph12 pv24">
    <yxtf-empty :empty-text="text">
      <media-svg
        class="flex-center"
        slot="image"
        :width="'300'"
        icon-class="noPermission"
      />
      <div
        v-if="showCode"
        class="mb48 color-gray-8"
      >
        {{ $t('pc_survey_lbl_errorcode') }}：{{ code }}
      </div>
    </yxtf-empty>
  </div>
</template>

<script>
import MediaSvg from './MediaSvg.vue';
export default {
  name: 'ErrorPage',
  components: {MediaSvg},
  props: ['code', 'msg'],
  data() {
    return {
      text: this.$t('pc_survey_lbl_systemError'),
      showCode: false
    };
  },
  created() {
    const keyText = this.msg || this.$t('pc_survey_lbl_' + this.code);
    if (keyText !== 'pc_survey_lbl_' + this.code) {
      this.text = keyText;
    } else {
      this.showCode = true;
    }
  }
};
</script>
