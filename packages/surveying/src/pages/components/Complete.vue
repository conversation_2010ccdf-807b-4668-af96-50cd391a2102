<!-- 学员端作答完成页面 -->
<template>
  <div>
    <yxtf-dialog
      :visible="!loading && !hide"
      width="640px"
      :cutline="false"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="() => hideDialog()"
      custom-class="yxtulcdsdk-survey"
      :append-to-body="true"
      :modal-append-to-body="true"
    >
      <div class="yxtulcdsdk-survey-complete">
        <div class="flex flex-vertical flex-mid yxtulcdsdk-survey-complete__main">
          <div class="yxtulcdsdk-survey-complete__title standard-size-24 yxt-weight-5">
            {{ survey.projectName }}
          </div>
          <img
            class="mt44"
            :src="`${$staticBaseUrl}ufd/55a3e0/survey/pc/img/exam-empty.png`"
            width="296"
            height="223"
            alt=""
          >
          <template v-if="isUseup">
            <div class="mt24 standard-size-18 yxt-weight-5">
              {{ $t('pc_survey_lbl_surveytimeuseout') /** 您的作答次数已用尽 */ }}
            </div>
          </template>
          <template v-else>
            <div class="mt24 standard-size-18 yxt-weight-5 ws-prewrap" v-html="survey.templateEndMsg || $t('pc_survey_lbl_surveythankjoin') /** 感谢您参与本次调查！ */"></div>
            <div class="color-gray-7 mt12">{{ $t('pc_survey_lbl_submittimeat', [survey.submitTime]) /** {0} 提交 */ }}</div>
          </template>

          <div class="mt32">
            <yxt-button type="primary" @click="viewResult">{{ $t('pc_survey_btn_showsubmitinfo')/** 查看提交内容 */ }}</yxt-button>
            <yxt-button v-if="survey.remainNum > 0" plain @click="doAgain">{{ $t('pc_survey_btn_joinagain') /** 再次参与 */ }}</yxt-button>
            <yxt-button v-if="!routeParams.skipPreview" plain @click="goback">{{ $t('pc_survey_btn_exitsurvey') /** 退出调查 */ }}</yxt-button>
          </div>
        </div>
      </div>
    </yxtf-dialog>
  </div>
</template>

<script>
import { surveyCompleteInfo } from '../../services/user.service.js';
import deepStudyPage from '../../mixins/deepStudyPage';
export default {
  name: 'Complete',
  mixins: [ deepStudyPage ],
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      loading: true,
      hide: false,
      surveyId: routeParams.id, // 调查项目的项目的ID
      uamId: routeParams.uamId, // 用户调查项目的的ID
      uaId: routeParams.uaId, // 调查项目的用户调查具体某一次ID
      batchId: routeParams.batchId,
      survey: {},
      isUseup: true
    };
  },
  created() {
    this.init();
  },
  computed: {
  },
  methods: {
    init() {
      surveyCompleteInfo(this.uamId, this.uaId, this.batchId).then((res) => {
        this.loading = false;
        this.survey = res.data;
        // 多次作答才会出次数用尽的提示
        this.isUseup = this.survey.remainNum <= 0 && this.survey.allowMultiSubmit;
        this.uaId = this.survey.uaId;
      }).catch((err) => {
        this.$handleError(err);
      });
    },
    goback() {
      this.$emit('backToIndex');
    },
    doAgain() {
      if (this.deepStudy) {
        this.$emit('changeStep', this.UserSurveyStep.answer, {
          ...this.routeParams,
          id: this.surveyId,
          uamId: this.uamId,
          collected: 1
        }, true);
      } else {
        window.location.reload();
      }
    },
    viewResult() {
      this.$emit('changeStep', this.UserSurveyStep.result, {
        ...this.routeParams,
        id: this.surveyId,
        uamId: this.uamId
      }, true, true);
    },
    hideDialog() {
      this.hide = true;
      this.$emit('close');
    }
  }
};
</script>
