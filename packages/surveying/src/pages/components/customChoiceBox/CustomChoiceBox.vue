<!-- 创建时间2021/11
  components: { CustomRadio },/25 09:24:55 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：自定义单选框和多选框 -->
<template>
  <custom-checkbox
    v-if="isMulti"
    :checked="checked"
    :disabled="disabled"
    :icon-offset="iconOffset"
  >
    <slot></slot>
  </custom-checkbox>
  <custom-radio
    v-else
    :checked="checked"
    :disabled="disabled"
    :icon-offset="iconOffset"
  >
    <slot></slot>
  </custom-radio>
</template>

<script>
import CustomCheckbox from './CustomCheckbox.vue';
import CustomRadio from './CustomRadio.vue';

export default {
  name: 'CustomChoiceBox',
  components: { CustomCheckbox, CustomRadio },
  props: {
    isMulti: {
      type: Boolean,
      default: false
    },
    checked: {
      default: false
    },
    iconOffset: {
      type: Number
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  }
};
</script>
