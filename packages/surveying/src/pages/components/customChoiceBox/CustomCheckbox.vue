<!-- 创建时间2021/11/19 14:07:11 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：自定义单选 -->
<template>
  <div
    class="yxtulcdsdk-survey-custom-checkbox  flex-1 flex is-checked"
    :class="{
      'yxtulcdsdk-survey-custom-checkbox--disabled': disabled
    }"
    @click="$emit('click')"
  >
    <div class="yxtulcdsdk-survey-custom-checkbox__icon flex-shrink-0  flex flex-mid" :class="disabled ?'yxtulcdsdk-survey-custom-type__cursor':''" :style="style">
      <yxt-checkbox
        :value="checked"
        :true-label="1"
      />
    </div>
    <div class="yxtulcdsdk-survey-custom-checkbox__label flex-1 standard-size-14 flex"><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: 'CustomCheckbox',
  props: {
    checked: {
      default: false
    },
    iconOffset: {
      type: Number,
      default: 3
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {
    style() {
      return { marginTop: this.iconOffset + 'px' };
    }
  }
};
</script>
