<!-- 创建时间2021/11/19 14:07:11 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：自定义单选 -->
<template>
  <div class="yxtulcdsdk-survey-custom-radio flex-1 flex is-checked flex-top" @click="$emit('click')">
    <div class="yxtulcdsdk-survey-custom-radio__icon flex flex-top flex-shrink-0" :style="style">
      <yxt-radio
        :value="checked"
        :label="1"
        :class="disabled ?'yxtulcdsdk-survey-custom-type__cursor':''"
      />
    </div>
    <div class="yxtulcdsdk-survey-custom-radio__label flex-1 standard-size-14 flex"><slot></slot></div>
  </div>
</template>

<script>
export default {
  name: 'CustomRadio',
  props: {
    checked: {
      default: ''
    },
    iconOffset: {
      type: Number,
      default: 3 // 因为默认字体行高是22 默认大小的尺寸是16*16
    },
    disabled: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {};
  },
  computed: {
    style() {
      return { marginTop: this.iconOffset + 'px' };
    }
  }
};
</script>
