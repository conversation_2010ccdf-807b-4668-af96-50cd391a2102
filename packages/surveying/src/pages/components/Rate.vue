<!-- 创建时间2021/12/06 13:28:30 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：评价 1.只展示灰色 2.只可以点击单个 3.答题 -->
<template>
  <div class="yxtulcdsdk-survey-rate flex flex-mid">
    <template v-if="type === ScaleQuesStyleType.number">
      <yxt-tooltip
        v-for="(item, index) in computedLength"
        :key="item"
        :disabled="(operationType!=='hover'&&operationType!=='range') || (quesType !== SurveyQuesType.score&&quesType !== SurveyQuesType.evaluation)"
        :content="getTooltipContent(item)"
      >
        <div
          class="yxtulcdsdk-survey-rate__item flex flex-mid flex-center pointer"
          :class="getNumberClass(item, index === 0)"
          @mouseover="hoverIt(item)"
          @mouseleave="hoverIt(-1)"
          @click="check(item)"
        >
          {{ isScale ? (item + minValNumber - 1) : item }}
        </div>
      </yxt-tooltip>
    </template>
    <template v-else>
      <div
        v-for="(item, index) in computedLength"
        :key="item"
        class="flex flex-mid over-hidden"
        :class="itemClass"
        @mouseover="hoverIt(item)"
        @mouseleave="hoverIt(-1)"
        @click="check(item)"
      >
        <!-- 解决iconClass频繁变动而引起的页面异常 -->
        <yxt-tooltip
          v-show="operationType !== 'none'&&hoverIndex === item&&hoverIndex>=min"
          :key="index + '_' + (index===hoverIndex? 'hv':'lv')"
          :disabled="(operationType!=='hover'&&operationType!=='range') || (quesType !== SurveyQuesType.score&&quesType !== SurveyQuesType.evaluation)"
          :content="getTooltipContent(item)"
        >
          <media-svg
            class="pointer"
            :class="{[notFirstItemClass]:index>0}"
            is-stc
            :width="iconSize"
            :height="iconSize"
            :icon-class="hoverIconClass[item]"
          />
        </yxt-tooltip>

        <media-svg
          v-show="operationType === 'none'||hoverIndex !== item || hoverIndex<min"
          :key="item"
          class="pointer"
          :class="{'cursor-disabled':hoverIndex<min,[notFirstItemClass]:index>0}"
          is-stc
          :width="iconSize"
          :height="iconSize"
          :icon-class="iconList[item]"
        />
      </div>
    </template>
  </div>
</template>

<script>
import { ScaleQuesStyleType, SurveyQuesType } from '../../core/enum';
import MediaSvg from './MediaSvg.vue';
import { isNumber } from '../../core/utils';
// 默认样式         点亮样式      选中样式
// smile_default  slime_light  smile_s
// heart_default  heart_light  heart_s
// star_default   star_light   star_s
// smile_default  smile_light  根据个数显示
export default {
  name: 'Rate',
  components: { MediaSvg },
  props: {
    length: { // 长度
      type: Number,
      default: 11
    },
    type: { // 颜样式
      type: Number,
      default: ScaleQuesStyleType.heart
    },
    value: {
      type: Number,
      default: null
    },
    checkValue: {
      type: Number,
      default: null // 用于选中效果
    },
    larger: { // 加大样式
      type: Boolean,
      default: false
    },
    startWithZero: { // nps,打分 从0开始
      type: Boolean,
      default: false
    },
    itemClass: {
      type: String,
      default: ''
    },
    notFirstItemClass: { // 非第一个的itemClass
      type: String,
      default: ''
    },
    operationType: {
      type: String,
      default: 'none' // none 全灰色，无法操作 single 单个选择  range 范围选择, hover 预览时的hover
    },
    disabled: {
      type: Boolean,
      default: false
    },
    min: {
      type: Number,
      default: 0
    },
    quesType: {
      type: Number,
      default: 0
    },
    front: {
      type: Boolean,
      default: false
    },
    // 针对题目类型是打分类型的需要把真实的最小值传入
    minValNumber: {
      Number,
      default: 0
    }

  },
  data() {
    return {
      ScaleQuesStyleType,
      hoverIndex: -1,
      SurveyQuesType
    };
  },
  computed: {
    isScale() {
      return [SurveyQuesType.scale, SurveyQuesType.multiScale].includes(this.quesType);
    },
    computedLength() {
      const length = [SurveyQuesType.scale, SurveyQuesType.multiScale, SurveyQuesType.evaluation].includes(this.quesType) ? this.length + 1 : this.length;
      return new Array(length - this.actualMin).fill('').map((v, i) => i + this.actualMin);
    },
    actualMin() {
      return this.min + !this.startWithZero;
    },
    smileSelectedSvgs() {
      switch (this.length + !this.startWithZero) {
        case 1:
          return ['smile0_sel'];
        case 2:
          return ['smile0_sel', 'smile10_sel'];
        case 3:
          return ['smile0_sel', 'smile7_sel', 'smile10_sel'];
        case 4:
          return ['smile0_sel', 'smile3_sel', 'smile7_sel', 'smile10_sel'];
        case 5:
          return ['smile0_sel', 'smile1_sel', 'smile7_sel', 'smile9_sel', 'smile10_sel'];
        case 6:
          return ['smile0_sel', 'smile1_sel', 'smile3_sel', 'smile7_sel', 'smile9_sel', 'smile10_sel'];
        case 7:
          return ['smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel'];
        case 8:
          return ['smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel'];
        case 9:
          return ['smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel'];
        case 10:
          return ['smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel'];
        case 11:
          return ['smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile6_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel'];
        case 12:
          return ['smile0_sel', 'smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile6_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel'];
        case 13:
          return ['smile0_sel', 'smile0_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile6_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel', 'smile10_sel'];
        case 14:
          return ['smile0_sel', 'smile0_sel', 'smile1_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile6_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile10_sel', 'smile10_sel'];
        case 15:
          return ['smile0_sel', 'smile0_sel', 'smile1_sel', 'smile1_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile6_sel', 'smile7_sel', 'smile8_sel', 'smile9_sel', 'smile9_sel', 'smile10_sel', 'smile10_sel'];
        case 16:
          return ['smile0_sel', 'smile0_sel', 'smile1_sel', 'smile1_sel', 'smile2_sel', 'smile2_sel', 'smile3_sel', 'smile4_sel', 'smile5_sel', 'smile6_sel', 'smile8_sel', 'smile8_sel', 'smile9_sel', 'smile9_sel', 'smile10_sel', 'smile10_sel'];
        default:
          return [];
      }
    },
    iconList() {
      const list = [];
      for (let i = 0; i < this.length + 1; i++) {
        const iconClass = this.getIconClass(i, this.value);
        list.push(iconClass);
      }
      return list;
    },
    hoverIconClass() {
      const list = [];
      for (let i = 0; i < this.length + 1; i++) {
        if (this.type === ScaleQuesStyleType.heart) list.push('heart_sel');
        else if (this.type === ScaleQuesStyleType.star) list.push('star_sel');
        else if (this.type === ScaleQuesStyleType.praise) list.push('praise_sel');
        else if (this.type === ScaleQuesStyleType.smile) list.push(this.smileSelectedSvgs[i]);
      }
      return list;
    },
    iconSize() {
      let size = this.larger ? 40 : 36;
      // if (this.computedLength.length > 11) {
      //   size = 32;
      // }
      return size + 'px';
    }
  },
  methods: {
    isNumber,
    getNumberClass(index, first) {
      // {'ml10':index>0,'color-white-i bg-primary-6-i':(isNumber(checkValue)&&checkValue === index) || (isNumber(value)&&index<=value),'larger':larger,[itemClass]:true}
      let className = '';
      if (!first) className += this.computedLength.length > 11 ? ' ml8' : ' ml10';
      if (this.front) className += ' front standard-size-14';
      else if (this.larger) className += ' larger';
      if (!this.front && !this.larger) className += ' standard-size-12';
      if (this.itemClass) className += ' ' + this.itemClass;
      if (!first && this.notFirstItemClass) className += ' ' + this.notFirstItemClass;
      if (this.hoverIndex < this.min && this.hoverIndex === index) className += ' cursor-disabled';
      if ((this.operationType === 'range' && ((isNumber(this.value) && index <= this.value) || (this.hoverIndex >= this.min && this.hoverIndex === index))) ||
      (this.operationType === 'single' && (index === this.value || this.hoverIndex === index)) ||
      (this.operationType === 'hover' && this.hoverIndex === index)) className += ' bg-primary-6-i color-white-i yxtulcdsdk-survey-rate__item--select';
      return className;
    },
    getIconClass(index, value) {
      if (this.type === ScaleQuesStyleType.heart) {
        if (this.operationType === 'range' && (isNumber(this.value) && index <= this.value)) {
          return 'heart_sel';
        } else if (this.operationType === 'single' && index === this.value) {
          return 'haert_light';
        }
        return 'heart_default';
      } else if (this.type === ScaleQuesStyleType.star) {
        if (this.operationType === 'range' && (isNumber(this.value) && index <= this.value)) {
          return 'star_sel';
        } else if (this.operationType === 'single' && index === this.value) {
          return 'star_light';
        }
        return 'star_default';
      } else if (this.type === ScaleQuesStyleType.praise) {
        if (this.operationType === 'range' && (isNumber(this.value) && index <= this.value)) {
          return 'praise_sel';
        } else if (this.operationType === 'single' && index === this.value) {
          return 'praise_light';
        }
        return 'praise_default';
      } else if (this.type === ScaleQuesStyleType.smile) {
        if (this.operationType === 'range' && (isNumber(this.value) && index <= this.value)) {
          return this.smileSelectedSvgs[this.value];
        } else if (this.operationType === 'single' && index === this.value) {
          return 'smile_light';
        }
        return 'smile_default';
      }
      return '';
    },
    check(index) {
      if (index < this.min) return;
      index = Math.max(this.min || 0, index);
      if (this.disabled) return;
      this.$emit('input', index);
      this.$emit('change', index);
    },
    hoverIt(index) {
      this.hoverIndex = index;
    },
    getTooltipContent(index) {
      if (this.quesType === SurveyQuesType.score) {
        return index + this.$t('pc_survey_lbl_scorelabel');
      } else if (this.quesType === SurveyQuesType.evaluation) {
        const key = ['', 'pc_survey_lbl_npsunsatisfaction', 'pc_survey_lbl_somedissatisfied', 'pc_survey_lbl_ordinarysatisfaction', 'pc_survey_lbl_somesatisfaction', 'pc_survey_lbl_npssatisfaction'];
        return this.$t(key[index]);
      }
      return '';
    }
  }
};
</script>
