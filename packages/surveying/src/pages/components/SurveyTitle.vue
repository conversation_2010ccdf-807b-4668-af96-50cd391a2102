<template>
  <div v-if="survey && survey.templateInfo" class="yxtulcdsdk-survey-answer__title-wrap">
    <div
      class="text-center ws-prewrap"
      :class="{
        'standard-size-14':isRichTitle,
        'standard-size-24 yxt-weight-5': !isRichTitle
      }"
      v-html="survey.templateInfo.templateTitle"
    ></div>
    <div v-if="showAnonymousTip || survey.templateInfo.templateDesc || isManager" class="mt24">
      <div v-if="showAnonymousTip" class="standard-size-16 color-gray-8 ws-prewrap text-center">{{ $t('pc_survey_lbl_anonymousdotip')/** 注：该问卷为匿名调查，请放心填写 */ }}</div>

      <template v-if="isManager">
        <div class="yxtulcdsdk-flex flex-wrap">
          <div v-for="info in personInfo.slice(0, 3)" :key="info.name" class="mr32 mb8">
            <span class="color-gray-8">{{ info.name }}：</span>
            <yxtbiz-dept-name v-if="info.dept" class="color-gray-10" :name="showAnonymous(info.value)" />
            <span v-else class="color-gray-10">{{ showAnonymous(info.value) }}</span>

            <template v-if="info.fullname">
              <!-- 用户名不是**或者--才显示username -->
              <span v-if="!['**', '--'].includes(showAnonymous(info.value)) && survey.username">（{{ survey.username }}）</span>
              <yxt-tag v-if="[1, 2, 3].includes(survey.freeLoginType)" class="ml5" size="mini">{{ $t(loginTypes[survey.freeLoginType || 1]) }}</yxt-tag>
            </template>
          </div>
        </div>

        <div class="yxtulcdsdk-flex mt8">
          <div v-for="info in personInfo.slice(3)" :key="info.name" class="mr32">
            <span class="color-gray-8">{{ info.name }}：</span>
            <span class="color-gray-10">{{ info.value || '--' }}</span>
          </div>
        </div>
      </template>
      <div
        v-if="survey.templateInfo.templateDesc"
        :class="{
          'mt16': isManager || showAnonymousTip
        }"
        class="standard-size-16 color-gray-8 ws-prewrap"
        v-html="survey.templateInfo.templateDesc"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SurveyTitle',
  props: {
    showAnonymousTip: [Boolean, Number],
    isRichTitle: Boolean,
    survey: Object,
    isManager: Boolean
  },

  data() {
    this.loginTypes = {
      1: 'pc_survey_msg_wechat' /* 微信 */,
      2: 'pc_survey_lbl_ques_type_103' /* 手机号 */,
      3: 'pc_survey_tracksurvey_lbl_visitor' /* 游客 */
    };

    return {
      personInfo: [
        { name: this.$t('pc_survey_lbl_member' /* 学员 */), value: this.survey.fullname, fullname: true },
        { name: this.$t('pc_survey_lbl_ques_type_111' /* 部门 */), value: this.survey.deptName, dept: true },
        { name: this.$t('pc_survey_lbl_ques_type_112' /* 岗位 */), value: this.survey.positionName },
        { name: this.$t('pc_survey_lbl_startime' /* 开始时间 */), value: this.survey.startTime },
        { name: this.$t('pc_survey_tracksurvey_lbl_submitTime' /* 提交时间 */), value: this.survey.submitTime }
      ]
    };
  },
  methods: {
    // 匿名的处理
    showAnonymous(value) {
      if (this.survey.answerAnonymous) {
        return '**';
      }
      return value || '--';
    }
  }
};
</script>
