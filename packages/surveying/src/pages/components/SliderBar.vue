<!-- 创建时间2021/12/03 17:12:12 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：量表题的拖动条 -->
<template>
  <div class="yxtulcdsdk-survey-slider" :class="larger?'is-large':''">
    <div ref="barTooltip" class="yxtulcdsdk-survey-slider__bar-wrap" @mousedown="start">
      <!-- <yxt-tooltip ref="buttonTooltip" :content="value_.toString()" popper-class="text-center"> -->
      <div ref="bar" class="yxtulcdsdk-survey-slider__bar bg-primary-6 pointer"></div>
      <!-- </yxt-tooltip> -->
    </div>

    <div ref="cover" class="yxtulcdsdk-survey-slider__cover bg-primary-6"></div>
    <div class="flex flex-between yxtulcdsdk-survey-slider__tick">
      <yxt-tooltip
        v-for="(item,index) in computedLength"
        :key="item"
        popper-class="text-center"
        :disabled="operationType!=='hover' || (quesType !== SurveyQuesType.score&&quesType !== SurveyQuesType.evaluation)"
        :content="getTooltipContent(item)"
      >
        <div class="yxtulcdsdk-survey-slider__tick-item-wrap">
          <div class="yxtulcdsdk-survey-slider__tick-item-wrap" @click="change(item)">
            <div
              class="yxtulcdsdk-survey-slider__tick-item"
              :class="{'bg-primary-6-i':value_>=item,'not-show':!showAllTick &&(index === 0 || index === computedLength.length - 1)}"
            >
              <!--              <div v-if="showSlot('label')" class="yxtulcdsdk-survey-slider__label"><slot name="label" :index="item"></slot></div>-->
              <!--              <div>{{ item }}</div>-->
            </div>
          </div>
        </div>
      </yxt-tooltip>
    </div>
    <div class="yxtulcdsdk-survey-slider__line"></div>
  </div>
</template>

<script>
import { SurveyQuesType } from '../../core/enum';
export default {
  name: 'SliderBar',
  props: {
    length: {
      type: Number,
      default: 11
    },
    value: {
      type: Number,
      default: null
    },
    larger: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    min: {
      type: Number,
      default: 0
    },
    quesType: {
      type: Number,
      default: 0
    },
    operationType: {
      type: String,
      default: 'none' // none 全灰色，无法操作 single 单个选择  range 范围选择, hover 预览时的hover
    },
    showAllTick: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      originLeft: 0,
      originX: 0,
      colorTickLength: 0, // 需要变色的tick个数，注意拖动过程中的变色
      value_: 0,
      SurveyQuesType
    };
  },
  computed: {
    computedLength() {
      const length = [SurveyQuesType.scale, SurveyQuesType.multiScale, SurveyQuesType.evaluation].includes(this.quesType) ? this.length + 1 : this.length;
      return new Array(length - this.actualMin).fill('').map((v, i) => i + this.actualMin);
    },
    minLeft() {
      return this.showAllTick ? 0 : 10;
    },
    actualMin() {
      return [SurveyQuesType.scale, SurveyQuesType.multiScale, SurveyQuesType.evaluation].includes(this.quesType) ? this.min + 1 : this.min;
    }
  },
  watch: {
    value(val) {
      if (this.value_ !== val) {
        this.value_ = (!this.value || this.actualMin > this.value) ? this.actualMin : this.value;
        this.setBarLeft(val);
        this.setCoverWidth(val);
      }
    }
  },
  mounted() {
    this.value_ = (!this.value || this.actualMin > this.value) ? this.actualMin : this.value;
    this.setBarLeft(this.value_);
    this.setCoverWidth(this.value_);
  },
  methods: {
    setBarLeft(value = 0) {
      const w = this.$el.offsetWidth;
      const left = Math.max(this.minLeft, (value - this.actualMin) / (this.computedLength.length - 1) * w);
      this.$refs.barTooltip.style.left = left / w * 100 + '%';
    },
    setCoverWidth(value = 0) {
      const w = this.$el.offsetWidth;
      const width = Math.max(this.minLeft, (value - this.actualMin) / (this.computedLength.length - 1) * w);
      this.$refs.cover.style.width = width / w * 100 + '%';
    },
    showSlot(name) {
      return this.$scopedSlots[name];
    },
    start(e) {
      this.originLeft = e.target.parentNode.offsetLeft;
      this.originX = e.clientX;
      if (!this.readonly) {
        window.addEventListener('mousemove', this.move);
        window.addEventListener('mouseup', this.end);
      }
    },
    move(e) {
      this.$refs.buttonTooltip && (this.$refs.buttonTooltip.showPopper = true);
      const length = e.clientX - this.originX;
      const step = Math.min(Math.max(this.minLeft, this.originLeft + length), this.$el.offsetWidth);
      this.$refs.barTooltip.style.left = step + 'px';
      this.$refs.cover.style.width = step + 'px';
      this.value_ = this.calcNumber(false);
      this.$nextTick(() => {
        this.$refs.buttonTooltip && this.$refs.buttonTooltip.updatePopper();
      });
    },
    end() {
      // 计算出value值
      const number = this.calcNumber(true);
      this.$refs.buttonTooltip && (this.$refs.buttonTooltip.showPopper = false);
      this.change(number);
    },
    change(number) {
      if (this.readonly) return;
      // number = Math.max(number, this.actualMin || 0)
      this.value_ = number;
      this.$emit('input', number);
      this.$emit('change', number);
      this.setBarLeft(number);
      this.setCoverWidth(number);
      window.removeEventListener('mousemove', this.move);
      window.removeEventListener('mouseup', this.end);
    },
    calcNumber(auto) {
      const perW = this.$el.offsetWidth / (this.computedLength.length - 1);
      let number = Math.floor(this.$refs.barTooltip.offsetLeft / perW);
      const remain = this.$refs.barTooltip.offsetLeft - number * perW;
      if (auto && remain > perW / 2) {
        number++;
      }
      return number + this.actualMin;
    },
    getTooltipContent(index) {
      if (this.quesType === SurveyQuesType.score) {
        return index + this.$t('pc_survey_lbl_scorelabel');
      } else if (this.quesType === SurveyQuesType.evaluation) {
        const key = ['', 'pc_survey_lbl_npsunsatisfaction', 'pc_survey_lbl_somedissatisfied', 'pc_survey_lbl_ordinarysatisfaction', 'pc_survey_lbl_somesatisfaction', 'pc_survey_lbl_npssatisfaction'];
        return this.$t(key[index]);
      }
      return '';
    }

  }
};
</script>
