<!-- 创建时间2021/12/03 17:12:12 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：滑动打分题，复制自SliderBar -->
<template>
  <div class="yxtulcdsdk-survey-slider" :class="larger?'is-large':''">
    <div ref="barWrap" class="yxtulcdsdk-survey-slider__bar-wrap" @mousedown="start">
      <yxt-tooltip
        ref="buttonTooltip"
        :content="value_.toString()"
        popper-class="text-center"
      >
        <div ref="bar" class="yxtulcdsdk-survey-slider__bar bg-primary-6 pointer"></div>
      </yxt-tooltip>
    </div>

    <div ref="cover" class="yxtulcdsdk-survey-slider__cover bg-primary-6"></div>
    <div class="flex flex-between yxtulcdsdk-survey-slider__tick">
      <div
        v-for="item in ticks"
        :key="item"
        class="yxtulcdsdk-survey-slider__tick-item-wrap"
      >
        <div class="yxtulcdsdk-survey-slider__tick-item-wrap" @click="change(item)">
          <div
            class="yxtulcdsdk-survey-slider__tick-item"
            :class="{'bg-primary-6-i':value_>=item,'not-show':!showTick(item)}"
          >
            <!--            <div class="yxtulcdsdk-survey-slider__label">-->
            <!--              <div class="flex flex-between standard-size-14 color-gray-8 mh-5 mt-3"> {{ item }}</div>-->
            <!--            </div>-->
          </div>
        </div>
      </div>
    </div>
    <div class="yxtulcdsdk-survey-slider__line"></div>
  </div>
</template>

<script>
import { SurveyQuesType } from '../../core/enum';
export default {
  name: 'SliderBar',
  props: {
    max: {
      type: Number,
      default: 11
    },
    value: {
      type: Number,
      default: null
    },
    larger: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    min: {
      type: Number,
      default: 0
    },
    quesType: {
      type: Number,
      default: 0
    },
    operationType: {
      type: String,
      default: 'none' // none 全灰色，无法操作 single 单个选择  range 范围选择, hover 预览时的hover
    },
    showAllTick: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      originLeft: 0,
      originX: 0,
      colorTickLength: 0, // 需要变色的tick个数，注意拖动过程中的变色
      value_: 0,
      SurveyQuesType
    };
  },
  computed: {
    computedLength() {
      return new Array(this.max - this.min + 1).fill('').map((v, i) => i + this.min);
    },
    minLeft() {
      return this.showAllTick ? 0 : 10;
    },
    ticks() {
      const res = [];
      if ((this.max - this.min) % 10 === 0) {
        for (let i = this.min; i <= this.max; i += 10) {
          res.push(i);
        }
      } else {
        res.push(this.min);
        res.push(this.max);
      }
      return res;
    }
  },
  watch: {
    value(val) {
      if (this.value_ !== val) {
        let v = 0;
        if (val === null || val < this.min) {
          v = this.min;
        } else {
          v = val;
        }
        this.value_ = v;
        this.setBarLeft(v);
        this.setCoverWidth(v);
      }
    }
  },
  mounted() {
    this.value_ = (this.value === null || this.value < this.min) ? this.min : this.value;
    this.setBarLeft(this.value_);
    this.setCoverWidth(this.value_);
  },
  methods: {
    showTick(value) {
      if (this.showAllTick) return true;
      return !(value === this.min || value === this.max);
    },
    setBarLeft(value = 0) {
      const w = this.$el.offsetWidth;
      const left = Math.max(this.minLeft, (value - this.min) / (this.computedLength.length - 1) * w);
      this.$refs.barWrap.style.left = left / w * 100 + '%';
    },
    setCoverWidth(value = 0) {
      const w = this.$el.offsetWidth;
      const width = Math.max(this.minLeft, (value - this.min) / (this.computedLength.length - 1) * w);
      this.$refs.cover.style.width = width / w * 100 + '%';
    },
    start(e) {
      this.originLeft = e.target.parentNode.offsetLeft;
      this.originX = e.clientX;
      if (!this.readonly) {
        window.addEventListener('mousemove', this.move);
        window.addEventListener('mouseup', this.end);
      }
    },
    move(e) {
      this.$refs.buttonTooltip && (this.$refs.buttonTooltip.showPopper = true);
      const length = e.clientX - this.originX;
      const step = Math.min(Math.max(this.minLeft, this.originLeft + length), this.$el.offsetWidth);
      this.$refs.barWrap.style.left = step + 'px';
      this.$refs.cover.style.width = step + 'px';
      this.value_ = this.calcNumber(false);
      this.$nextTick(() => {
        this.$refs.buttonTooltip && this.$refs.buttonTooltip.updatePopper();
      });
    },
    end() {
      // 计算出value值
      const number = this.calcNumber(true);
      this.$refs.buttonTooltip && (this.$refs.buttonTooltip.showPopper = false);
      this.change(number);
    },
    change(number) {
      if (this.readonly) return;
      number = Math.max(number, this.min || 0);
      this.value_ = number;
      this.$emit('input', number);
      this.$emit('change', number);
      this.setBarLeft(number);
      this.setCoverWidth(number);
      window.removeEventListener('mousemove', this.move);
      window.removeEventListener('mouseup', this.end);
    },
    calcNumber(auto) {
      if (!this.$el) return this.value_;
      const perW = this.$el.offsetWidth / (this.computedLength.length - 1);
      let number = Math.floor((this.$refs.barWrap.offsetLeft - this.minLeft) / perW);
      const remain = (this.$refs.barWrap.offsetLeft - this.minLeft) - number * perW;
      if (auto && remain > perW / 2) {
        number++;
      }
      return number + this.min;
    }
  }
};
</script>
