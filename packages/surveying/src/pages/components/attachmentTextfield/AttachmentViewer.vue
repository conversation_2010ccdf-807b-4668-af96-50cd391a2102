<!-- 创建时间2021/06/29 13:46:49 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述： -->
<template>
  <div
    class="yxtulcdsdk-survey-attachment-viewer"
    :style="style"
    @click.stop="toPreview"
  >
    <!------------------------ 学员端 ------------------------->
    <template v-if="isFront">
      <div v-if="fileType === EnumFileType.audio||fileType === EnumFileType.video" class="yxtulcdsdk-survey-attachment-viewer--front">
        <div v-if="file.fileStatus === TranscodingStatus.fail" class="yxtulcdsdk-survey-attachment-viewer--transcoding flex flex-mid flex-center">{{ $t('pc_survey_tip_transcodefail') }}</div>
        <div v-else-if="file.fileStatus !== TranscodingStatus.success" class="yxtulcdsdk-survey-attachment-viewer--transcoding flex flex-mid flex-center">{{ $t('pc_survey_tip_transcoding') }}</div>
        <common-media-player
          v-else
          ref="commonPlayer"
          :height="height"
          :width="width"
          :cover="file.coverUrl"
          :type="fileType === EnumFileType.audio?'audio':'video'"
          :file-id="file.fileId"
          :options="file.fileOptions"
        />
      </div>
      <template v-else-if="fileUrl">
        <yxt-image
          fit="contain"
          :src="fileUrl"
          :lazy="!exporting"
          class="yxtulcdsdk-survey-attachment-viewer__image"
          @click.native="viewImage"
        />
      </template>
    </template>

    <!-------------------管理端 ------------------------>
    <template v-else>
      <!-- 音频封面 -->
      <div v-if="fileType === EnumFileType.audio" class="yxtulcdsdk-survey-attachment-viewer__image is-audio flex flex-mid flex-center">
        <media-svg
          is-stc
          width="54px"
          height="54px"
          icon-class="audio_icon2"
        />
      </div>
      <!-- 视频 -->
      <div v-else-if="fileType === EnumFileType.video" class="yxtulcdsdk-survey-attachment-viewer__image is-video flex flex-mid flex-center">
        <yxt-image
          v-if="file.coverUrl"
          fit="cover"
          :src="file.coverUrl"
          :lazy="!exporting"
          class="yxtulcdsdk-survey-attachment-viewer__image"
        />
        <media-svg
          v-else
          is-stc
          width="54px"
          height="54px"
          icon-class="video_icon"
        />
      </div>
      <!-- 图片封面 -->
      <yxt-image
        v-else-if="fileUrl"
        fit="cover"
        :src="fileUrl"
        :lazy="!exporting"
        class="yxtulcdsdk-survey-attachment-viewer__image"
      />

      <!--  上传中&转码中&转码失败&上传失败 -->
      <template v-if="file._status === UploadStatus.going || file._status === UploadStatus.fail || file.fileStatus !== TranscodingStatus.success">
        <div class="yxtulcdsdk-survey-attachment-viewer__cover flex flex-mid flex-center color-white">
          {{ tooltip }}
        </div>
      </template>
      <!-- 视频播放按钮+hover效果 -->
      <div v-show="file._status !== UploadStatus.going && file._status !== UploadStatus.fail && file.fileStatus === TranscodingStatus.success">
        <!-- 音视频的播放按钮 -->
        <div v-if="fileType === EnumFileType.video || fileType === EnumFileType.audio" class="yxtulcdsdk-survey-attachment-viewer__play flex flex-mid flex-center">
          <media-svg
            is-stc
            class="color-white ml6"
            width="14px"
            heigth="20px"
            icon-class="play"
          />
        </div>
      </div>
    </template>
    <file-big-viewer
      ref="bigViewer"
      :file-id="file.fileId"
      :file-type="fileType"
      :image-url="file.fileUrl||file._fileUrl"
      :video-options="file.fileOptions"
    />
  </div>
</template>

<script>
import { EnumFileType, QuesMode, TranscodingStatus, UploadStatus } from '../../../core/enum';
import MediaSvg from '../MediaSvg.vue';
import { getFileData } from '../../../services/base.service';
import FileBigViewer from './FileBigViewer.vue';
import CommonMediaPlayer from '../CommonMediaPlayer.vue';
export default {
  name: 'AttachmentViewer',
  components: {
    MediaSvg,
    FileBigViewer,
    CommonMediaPlayer
  },
  props: {
    width: {
      type: [String, Number],
      default: 168
    },
    height: {
      type: [String, Number],
      default: 95
    },
    file: {
      type: Object,
      required: true
    },
    mode: {
      default: QuesMode.view
    },
    isFront: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      UploadStatus,
      QuesMode,
      TranscodingStatus,
      EnumFileType,
      visible: false
    };
  },
  computed: {
    transcodingStatus() {
      // 成功, 失败, 进行中
      return this.file.fileStatus || TranscodingStatus.wait;
    },
    fileType() { // 文件类型
      return this.file.fileType;
    },
    fileId() { // 文件id
      return this.file.fileId || '';
    },
    fileUrl() { // 图片地址
      if (this.file._fileUrl) {
        return this.file._fileUrl;
      }
      return this.file.fileUrl || '';
    },
    style() {
      return { width: this.width + 'px', height: this.height + 'px' };
    },
    tooltip() {
      if (this.file._status === UploadStatus.going) {
        return this.$t('pc_survey_tip_uploading'/** 正在上传... */);
      } else if (this.file._status === UploadStatus.fail) {
        return this.$t('pc_survey_tip_uploadefail'/** 上传失败 */);
      } else if (this.file.fileStatus === TranscodingStatus.wait) {
        return this.$t('pc_survey_tip_transcoding'/** 转码中... */);
      } else if (this.file.fileStatus === TranscodingStatus.going) {
        return this.$t('pc_survey_tip_transcoding'/** 转码中... */);
      } else if (this.file.fileStatus === TranscodingStatus.fail) {
        return this.$t('pc_survey_tip_transcodefail'/** 转码失败 */);
      }
      return this.$t('pc_survey_tip_loadingerror'/** 加载失败 */);
    },
    // 页面是否是导出页面
    exporting() {
      return !!this.$route.query.exporting;
    }
  },
  watch: {
    file(newVal, oldVal) {
      // 处理刷新页面数据后 播放中断、消失问题
      if (newVal.fileId === oldVal.fileId && oldVal.requested) {
        Object.assign(newVal, oldVal);
      } else {
        this.getFileInfo();
      }
    }
  },
  created() {
    this.getFileInfo();
  },
  methods: {
    //  通过接口获取文件转码状态
    async getFileInfo() {
      if (!this.fileId || this.file.requested) return;
      if (this.transcodingStatus === TranscodingStatus.success) {
        switch (this.fileType) {
          case EnumFileType.video:
          case EnumFileType.audio:
            let fileDetail = this.file.fileDetail;
            if (!fileDetail.playDetails) {
              const res = await getFileData(this.fileId);
              const playDetails = res && res.data && res.data.playDetails || [];
              const previewUrl = res && res.data && res.data.thumbnailUrl || [];
              fileDetail = { playDetails, previewUrl };
            }

            const fileOptions = fileDetail && fileDetail.playDetails && fileDetail.playDetails.map(item => ({ fileFullUrl: item.url, resolution: item.desc, fileId: this.file.fileId })) || [];
            this.$set(this.file, 'fileOptions', fileOptions);
            this.$set(this.file, 'coverUrl', fileDetail && fileDetail.previewUrl);
            break;
          case EnumFileType.image:
            if (this.file.fileDataType !== 1) this.file.fileDataType = 1;
            if (!this.file.fileUrl) {
              const res = await getFileData(this.fileId);
              const data = res && res.data && res.data.playDetails || [];
              const url = data && data.length ? data[0].url : '';
              this.$set(this.file, 'fileUrl', url);
            }
            break;
          default:
            break;
        }

        this.file.requested = true;
      }
    },

    // 预览
    toPreview() {
      if (this.mode === QuesMode.edit || this.isFront) return;
      if (this.transcodingStatus === TranscodingStatus.success) this.play();
      if (this.fileType === EnumFileType.image) this.viewImage();
    },
    // 编辑模式下播放音视频
    play() {
      if (this.transcodingStatus !== TranscodingStatus.success) return;
      this.$refs.bigViewer.show();
    },
    // 编辑模式下预览图片
    viewImage() {
      if (!this.fileUrl) return;
      this.$refs.bigViewer.show();
    },
    // 重新上传的更换
    filesAdd(files) {
      const file = files[0];
      this.$emit('update:file', { ...file, fileType: this.fileType });
    },
    fileUploaded(file) {
      this.$emit('update:file', { ...this.file, ...file });
    }
  }
};
</script>
