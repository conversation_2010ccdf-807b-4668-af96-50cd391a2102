<!-- 创建时间2021/12/21 16:26:42 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：查看文件和媒体 -->
<template>
  <yxtbiz-image-viewer
    v-if="fileType === EnumFileType.image&&imageList.length"
    ref="imageViewer"
    :show-navbar="false"
    no-list
    :data="imageList"
  />
  <div v-else-if="visible" class="yxtulcdsdk-survey-big-viewer yxtulcdsdk-survey">
    <div class="yxtulcdsdk-survey-big-viewer__mask flex flex-mid flex-center">
      <common-media-player
        v-if="fileType === EnumFileType.audio || fileType === EnumFileType.video"
        class="yxtulcdsdk-survey-big-viewer__video"
        auto-start
        :height="405"
        :width="720"
        :type="type"
        :file-id="fileId"
        :options="videoOptions"
      />
      <yxtbiz-doc-viewer
        v-else-if="fileType === EnumFileType.file&&fileInfo"
        class="yxtulcdsdk-survey-big-viewer__video"
        :file-id="fileId"
        :file-info="fileInfo"
        :width="720"
      />
      <div class="yxtulcdsdk-survey-big-viewer__del" @click="close">
        <media-svg
          class="del-icon"
          manage
          width="32px"
          height="32px"
          icon-class="close"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { EnumFileType } from '../../../core/enum';
import CommonMediaPlayer from '../CommonMediaPlayer.vue';
import MediaSvg from '../MediaSvg.vue';
export default {
  name: 'FileBigViewer',
  components: { MediaSvg, CommonMediaPlayer },
  props: {
    fileId: {
      type: String,
      default: ''
    },
    fileType: {
    },
    imageUrl: {
      type: String,
      default: ''
    },
    fileInfo: {
      type: Object,
      default: undefined
    },
    videoOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      EnumFileType,
      visible: false
    };
  },
  computed: {
    imageList() {
      return [{ url: this.imageUrl }];
    },
    type() {
      switch (this.fileType) {
        case EnumFileType.video:
          return 'video';
        case EnumFileType.audio:
          return 'audio';
        default:
          return '';
      }
    }

  },
  mounted() {
    this.addNode();
  },
  destroyed() {
    this.removeNode();
  },
  methods: {
    show() {
      if (this.fileType === EnumFileType.image) {
        this.$refs.imageViewer.view(0);
      } else {
        this.visible = true;
      }
    },
    close() {
      this.visible = false;
    },
    removeNode() {
      if (this.$el && this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el);
      }
      document.body.style.overflow = this.visible ? 'hidden' : '';
    },
    addNode() {
      document.body.appendChild(this.$el);
      document.body.style.overflow = this.visible ? 'hidden' : '';
    }
  }
};
</script>
