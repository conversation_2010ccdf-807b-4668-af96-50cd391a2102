<!-- 创建时间2021/12/21 17:18:21 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：附件的列表 -->
<template>
  <div v-if="attachments&&attachments.length" class="flex flex-wrap yxtulcdsdk-survey-attach-container">
    <div v-for="item in attachments" :key="item.id" class="yxtulcdsdk-survey-attach-container__attachment-wrap">
      <attachment-viewer
        :width="width(item)"
        :height="height(item)"
        :mode="QuesMode.view"
        :is-front="isFront"
        :file="item"
      />
    </div>
  </div>
</template>

<script>
import { EnumFileType, QuesMode } from '../../../core/enum';
import AttachmentViewer from './AttachmentViewer.vue';
export default {
  components: { AttachmentViewer },
  name: 'AttachmentsContainer',
  props: {
    attachments: {
      type: Array,
      default: () => []
    },
    // 学端和学员端处理成一样的，这里不再需要此参数的处理
    // isFront: { // 是否是学员端
    //   type: Boolean,
    //   default: false
    // },
    scale: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      EnumFileType,
      isFront: false,
      QuesMode
    };
  },
  methods: {
    width(item) {
      // if (this.isFront) return 640 / this.scale;
      // if (item.fileType !== EnumFileType.image) return 168;
      // return 95;
      return 168;
    },
    height(item) {
      if (this.isFront) {
        if (item.fileType === EnumFileType.image) return 'auto';
        return 320 / this.scale;
      }
      return 95;
    }
  }
};
</script>
