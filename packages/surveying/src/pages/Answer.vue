<!-- 创建时间2021/11/08 17:31:31 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：作答页面 -->
<template>
  <div v-loading="loading" class="flex flex-vertical">
    <CommonSticky
      v-if="!routeParams.hideBack"
      ref="stickyLeft"
      position="top"
      class="z-999"
      :style="{
        minWidth: innerWidth ? (innerWidth + 'px'): undefined
      }"
      :by-class="deepStudy"
    >
      <div class="yxtulcdsdk-survey-header">
        <div
          class="flex flex-mid flex-shrink-0 hand hover-primary-6"
          @click="back()"
        >
          <yxt-svg icon-class="quit" width="16px" height="16px" />
          <span class="ml8 flex-shrink-0">{{ $t('pc_survey_btn_sign_out'/** 退出 */) }}</span>
        </div>
      </div>
    </CommonSticky>

    <div
      v-show="!loading"
      ref="mainContainer"
      class="yxtulcdsdk-survey-answer"
      :class="{
        'yxtulcdsdk-survey-answer--list': isO2oSurveyList,
        'yxtulcdsdk-survey-answer--full': isFullPage
      }"
    >
      <!-- o2o评价列表 及 o2o提示弹窗。 由于合并项目内的评价，提升作答连贯度。 -->
      <div v-if="isO2oSurveyList" ref="o2oListContainer" class="yxtulcdsdk-survey-answer__leftpanel">
        <yxtbiz-merge-evaluation
          ref="o2oList"
          :project-id="routeParams.mergeEvalProjectId"
          :default-task-id="routeParams.mergeEvalTaskId"
          @changeSurvey="changeSurvey"
        />
        <yxtbiz-o2o-multi-evaluate-dialog
          :visible.sync="o2oSubmitedTip"
          :project-id="routeParams.mergeEvalProjectId"
          :default-task-id="routeParams.mergeEvalTaskId"
          :success-msg="survey.templateInfo.templateEndMsg"
          @next="goNextSurvey"
          @detail="goCurrentSurvey"
        />
      </div>
      <!-- 中间答题区域 -->
      <div
        class="yxtulcdsdk-survey-answer__main"
        :style="{
          maxWidth: pageSize + 'px'
        }"
      >
        <div v-if="loadingInner" v-loading="!loading && loadingInner" class="flex-g-1"></div>
        <template v-else>
          <!-- 标题简介 -->
          <survey-title :survey="survey" :is-rich-title="isRichTitle" :show-anonymous-tip="survey.enableAnonymous" />
          <!-- 题目 -->
          <div class="yxtulcdsdk-survey-answer__content-wrap pb12">
            <template v-for="(page,index) in quesPages">
              <div
                v-for="ques in quesPages[index]"
                v-show="index === pageIndex"
                :key="index + ques.questionId"
              >
                <ques-index-cell
                  v-if="!checkQuesJumped(ques)"
                  :ref="'ques_'+ques.questionId"
                  :key="ques.questionId + pageKey"
                  :mode="QuesMode.write"
                  :ques="ques"
                  :disabled="viewResult || submitedTip"
                  :index="ques.quesNum"
                  :auto-check="checkSubmit"
                  :rank-mode="survey.allowViewVoteRank && viewResult"
                  converted
                  @user-submit="doQuestion"
                  @jump="jumpQues"
                />
              </div>
            </template>
          </div>
          <!-- 按钮区 -->
          <common-sticky
            v-if="!viewResult"
            ref="cmstk"
            :by-class="deepStudy"
            :first-delay="100"
            :offset="-24"
            class="z-999"
          >
            <div class="yxtulcdsdk-survey-answer__submit">
              <yxtf-button
                v-if="canPrevPage()"
                class="w-142"
                size="huge"
                @click="checkAndGoPage(true)"
              >
                {{ $t('pc_survey_btn_prevpage')/** 上一页 */ }}
              </yxtf-button>
              <yxtf-button
                v-if="canNextPage()"
                class="w-142"
                size="huge"
                @click="checkAndGoPage(false)"
              >
                {{ $t('pc_survey_btn_nextpage')/** 下一页 */ }}
              </yxtf-button>
              <yxtf-button
                v-else-if="!loading"
                size="huge"
                class="w-275"
                type="primary"
                :loading="submiting"
                @click="submit"
              >
                {{ $t('pc_survey_btn_submit')/** 提交 */ }}
              </yxtf-button>
            </div>
          </common-sticky>
          <!-- <common-sticky
            v-if="!viewResult"
            ref="cmstk"
            :by-class="deepStudy"
            :first-delay="100"
            :offset="-24"
            class="z-999"
          >
            <div class="pb24 bg-white z-999 yxtbizf-br-4"></div>
          </common-sticky> -->
        </template>
      </div>
      <!-- 右侧倒计时 -->
      <div
        v-if="!loading && survey.enableAnswerTime && !viewResult"
        ref="rightPanel"
        class="yxtulcdsdk-survey-answer__subpanel"
      >
        <common-sticky
          :offset="60"
          :by-class="deepStudy"
          position="top"
          force-fixed
          :first-delay="100"
        >
          <div class="yxtulcdsdk-survey-answer__subpanel-fix">
            <div class="yxtulcdsdk-survey-answer__subpanel-inner">
              <div>{{ $t('pc_survey_lbl_lasttime') }}</div>
              <div
                :class="{
                  'color-danger yxt-weight-5': lastTime < 60
                }"
              >
                {{ getCountDownStr(lastTime) }}
              </div>
            </div>
            <div v-if="showQuesNum" class="yxtulcdsdk-survey-answer__subpanel-inner">
              <div>{{ $t('pc_survey_lbl_surveyprogress') }}</div>
              <div><span class="font-medium">{{ answeredQuesNum() }}</span>/{{ quesNum }}</div>
            </div>
          </div>
        </common-sticky>
      </div>

      <!-- 提交完成提示窗 -->
      <complete
        v-if="submitedTip"
        :deep-study="deepStudy"
        :query-datas="queryDatas"
        @close="closeSubmitedTip"
        @changeStep="changeStep"
        @backToIndex="backToIndex"
        @updateProgress="updateProgress"
      />
    </div>
  </div>
</template>

<script>
import { QuesMode, SurveyQuesType } from '../core/enum';
import CommonSticky from './components/CommonSticky.vue';
import { getCountDownStr, getScrollParent} from '../core/utils';
import surveying from '../mixins/surveying.js';
import touristJoin from '../mixins/touristJoin.js';
import QuesIndexCell from './ques/QuesIndexCell.vue';
import SurveyTitle from './components/SurveyTitle.vue';
import deepStudyPage from '../mixins/deepStudyPage';
import { addResizeListener, removeResizeListener } from 'yxt-pc';
import Complete from './components/Complete';
export default {
  components: {
    CommonSticky,
    QuesIndexCell,
    SurveyTitle,
    Complete
  },
  mixins: [surveying, touristJoin, deepStudyPage],
  inject: [ 'getWidth' ],
  name: 'Answer',
  data() {
    return {
      QuesMode,
      SurveyQuesType,
      quesList: [],
      pageSize: 920, // 计算页面的最大宽度。沉浸式下会出现较窄的情况，需要通过限制题目区域的宽度来做宽度适配。
      innerWidth: 0
    };
  },
  created() {
    // PC o2o 组件会触发一次初始化，这里不主动触发
    !this.isO2oSurveyList && this.startSurvey();
  },
  mounted() {
    this.addPageRezise();
  },
  methods: {
    getCountDownStr,
    // 提交问卷
    submit() {
      // 校验
      if (!this.checkQuesAndTip()) return;

      // 提交
      this.submitSurvey((res) => {
        if (!res) {
          this.$nextTick(() => {
            this.checkQuesAndTip();
          });
        }
      });
    },
    /**
     * 检查当前页面作答并跳转上一页、下一页
     * 上一题、下一题（H5每页一题模式就是一页放一道题目）
     * @param {string} isPrev 上一页
     */
    checkAndGoPage(isPrev) {
      if (!isPrev && !this.checkQuesAndTip()) return;

      this.goPage(isPrev);

      // 回到顶部
      this.$nextTick(() => {
        this.scrollToY(getScrollParent(this.$el, true),
          window.document.getElementsByClassName('yxtulcdsdk-survey-answer__content-wrap')[0].offsetTop,
          false);
        this.$refs.cmstk.scrollHandler();
      });
    },
    /**
     * 校验试题作答，并给予提示
     */
    checkQuesAndTip() {
      // 校验提示
      if (this.checkCurrentPageHasAbnormal()) {
        this.$confirm(this.$t('pc_survey_lbl_undonetips'), this.$t('pc_survey_lbl_undonetipstitle'), {
          type: 'info',
          showCancelButton: false
        }).finally(() => {
          const errQues = this.getfirstErrorQues();
          if (errQues) {
            this.scrollToQues(errQues);
          }
        });

        return false;
      }
      return true;
    },
    scrollToQues(ques) {
      const quesEl = this.$refs['ques_' + ques.questionId][0];
      const scroller = quesEl && getScrollParent(quesEl.$el, true);
      if (quesEl && scroller) {
        console.log(quesEl.$el.offsetTop);
        this.scrollToY(scroller, quesEl.$el.offsetTop);
      }
    },
    /**
     * 页面超时警告提示
     */
    warningTimeOut() {
      const tipMsg = this.$t('pc_survey_msg_timeout');
      this.$message.warning(tipMsg);
      // 结束后返回学员我的调查
      if (this.isTourist || this.deepStudy) {
        this.$emit('errorPublic', '', tipMsg);
      } else {
        this.$router.replace({
          name: 'Index'
        });
      }
    },
    // 做答后退出调查，独立调查优先返回调研中心，游客返回预览，其他的走正常退出逻辑
    backToIndex() {
      if (!this.isTourist && !this.deepStudy) {
        this.$router.replace({
          name: 'Index'
        });
      } else if (this.isTourist) {
        this.$emit('changeStep', this.UserSurveyStep.preview, {
          ...this.routeParams,
          back: 1,
          uamId: '',
          uaId: ''
        }, false);
      } else {
        this.back(true);
      }
    },
    closeSubmitedTip() {
      // 非沉浸式直接跳转结果页面
      if (!this.deepStudy) {
        this.$emit('changeStep', this.UserSurveyStep.result, {
          ...this.routeParams,
          id: this.surveyId,
          uamId: this.uamId
        }, true);
      } else {
        this.loadAnswerResult();
      }
    },
    // 退出
    back(noConfirm, cb) {
      const leaveFun = ()=>{
        if (cb) {
          cb(true);
          return;
        }

        if (this.deepStudy) {
          if (this.routeParams.skipPreview) {
            this.$emit('back');
          } else {
            this.$emit('changeStep', this.UserSurveyStep.preview, {
              ...this.routeParams,
              back: 1,
              uamId: '',
              uaId: ''
            }, false);
          }
        } else if (window.history.length > 1) {
          window.history.go(-1);
        } else {
          window.close();
        }
      };

      if (noConfirm || this.viewResult) {
        leaveFun();
        return;
      }

      this.$confirm('', this.$t('pc_survey_msg_surveyingleaveconfirm'/** 您还未提交，确认退出吗？ */), {
        showCancelButton: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        type: 'warning'
      }) .then(() => {
        leaveFun();
      }).catch(() => {
        if (cb) {
          cb(false);
        }
      });
    },
    // 沉浸式外部主动离开的处理
    confirmLeaveStudy(cb) {
      this.back(false, cb);
    },
    setPageSize() {
      const o2oWidth = this.$refs.o2oListContainer && (this.$refs.o2oListContainer.offsetWidth) || 0;
      const rightWidth = this.$refs.rightPanel && (this.$refs.rightPanel.offsetWidth + 24) || 0;
      this.pageSize = this.getWidth() - (this.isFullPage ? 0 : 48) - o2oWidth - rightWidth;
      !this.isFullPage && (this.pageSize = Math.min(this.pageSize, 920));
      this.innerWidth = this.$refs.mainContainer && this.$refs.mainContainer.offsetWidth || 0;
    },
    addPageRezise() {
      if (!this.deepStudy) return;
      addResizeListener(this.$el, this.setPageSize);
      this.isO2oSurveyList && addResizeListener(this.$refs.o2oListContainer, this.setPageSize);
    },
    clearPageResize() {
      if (!this.deepStudy) return;
      removeResizeListener(this.$el, this.setPageSize);
      this.isO2oSurveyList && removeResizeListener(this.$refs.o2oListContainer, this.setPageSize);
    }
  },
  beforeDestroy() {
    this.clearPageResize();
  }
};
</script>
