<!-- 学员端答题结果查看页 -->
<template>
  <div class="flex flex-vertical">
    <CommonSticky
      v-if="!routeParams.hideBack && !exporting"
      ref="stickyLeft"
      position="top"
      class="z-999"
      :by-class="deepStudy"
    >
      <div class="yxtulcdsdk-survey-header">
        <div
          class="flex flex-mid hand hover-primary-6"
          @click="back()"
        >
          <yxt-svg icon-class="icons/arrow-left" width="16px" height="16px" />
          <span class="ml8">{{ $t( showClose ? 'pc_survey_btn_close' : 'pc_survey_btn_back' /** 关闭/返回 */) }}</span>
        </div>
        <yxtf-divider direction="vertical" />
        <span class="d-in-block flex-g-1 ellipsis">{{ $t('pc_survey_lbl_submitinfo'/** 历史提交详情 */) }}</span>
        <yxt-button v-if="manager" :loading="pdfMenu.downLoading" @click="exportPdfExplain">{{ $t('pc_survey_btn_exportpdf' /* 导出pdf */) }}</yxt-button>
      </div>
    </CommonSticky>
    <div
      v-if="!loading"
      class="yxtulcdsdk-survey-answer yxtulcdsdk-survey-result flex-g-1"
      :class="{
        'yxtulcdsdk-survey-answer--full': isFullPage
      }"
    >
      <!-- 中间答题区域 -->
      <div
        class="yxtulcdsdk-survey-answer__main wp-100"
        :class="exporting ? '' : 'yxtulcdsdk-survey-answer__max-width'"
      >
        <!-- 标题简介 -->
        <survey-title :survey="survey" :is-rich-title="isRichTitle" :is-manager="manager" />
        <!-- 题目 -->
        <div class="yxtulcdsdk-survey-answer__content-wrap">
          <template v-for="ques in quesPages[pageIndex]">
            <ques-index-cell
              v-if="!checkQuesJumped(ques)"
              :ref="'ques_'+ques.questionId"
              :key="ques.questionId"
              converted
              :mode="QuesMode.write"
              :ques="ques"
              :rank-mode="survey.allowViewVoteRank"
              disabled
              :index="ques.quesNum"
              @jump="jumpQues"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { QuesMode, SurveyQuesType, RouteModule } from '../core/enum';
import { surveyAnswerDetail } from '../services/user.service.js';
import surveying from '../mixins/surveying.js';
import touristJoin from '../mixins/touristJoin.js';
import QuesIndexCell from './ques/QuesIndexCell.vue';
import SurveyTitle from './components/SurveyTitle.vue';
import deepStudyPage from '../mixins/deepStudyPage';
import { fileApi } from 'yxt-biz-pc';
import CommonSticky from './components/CommonSticky.vue';
import exportPdf from 'packages/surveying/src/mixins/exportPdf';

export default {
  name: 'Result',
  components: {
    QuesIndexCell,
    SurveyTitle,
    CommonSticky
  },
  mixins: [surveying, touristJoin, deepStudyPage, exportPdf],
  computed: {
    showClose() {
      // 非沉浸式下，新开窗口来的顶上显示为关闭。直接关闭当前页面
      return !this.deepStudy && window.history.length === 1 && !this.routeParams.fp;
    }
  },
  data() {
    return {
      QuesMode,
      SurveyQuesType,
      manager: !this.deepStudy && this.$route.meta && this.$route.meta.module === RouteModule.manage,
      loading: true,
      pdfMenu: {
        downLoading: false
      },
      projectInfo: {}
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.surveyId = this.routeParams.id; // 调查项目的项目的ID
      this.uamId = this.routeParams.uamId; // 用户调查项目的的ID
      this.uaId = this.routeParams.uaId; // 调查项目的用户调查具体某一次ID
      this.batchId = this.routeParams.batchId;
      this.targetId = this.routeParams.targetId;
      this.onePage = true;
      // uamId和uaId都为空，则不请求接口
      if (this.$isNullOrUndefined(this.uamId) && this.$isNullOrUndefined(this.uaId)) {
        this.loading = true;
        return;
      }
      surveyAnswerDetail(this.uamId, this.uaId, this.manager, this.batchId, this.targetId).then((res) => {
        this.loading = false;
        this.initSurveyInfo(res.data);
        this.projectInfo = res.data;
        if (res.data.ticket) {
          fileApi.defaults.headers.ticket = res.data.ticket;
        }

        this.refreshVoteRank();
      }).catch((errorData) => {
        this.$emit('errorPublic', errorData && errorData.code, errorData && errorData.message);
      });
    },
    back() {
      if (this.showClose) {
        window.close();
      } else if (!this.routeParams.skipPreview || this.routeParams.skipPreview === '0') {
        this.$emit('changeStep', this.UserSurveyStep.preview, {
          ...this.routeParams,
          id: this.surveyId,
          back: 1,
          uamId: '',
          uaId: ''
        }, false);
      } else if (this.deepStudy) {
        this.$emit('back');
      } else {
        window.history.go(-1);
      }
    },

    exportPdfExplain() {
      this.pdfName = this.projectInfo.projectName;
      const lsOrigin = localStorage.domain ? `https://${localStorage.domain}` : window.location.origin;
      const curUrl = `${lsOrigin}/survey/${location.hash}`;
      this.exportPdf(curUrl, false, true, '.yxtulcdsdk-survey', false, this.pdfMenu, '', 1);
    }
  }
};
</script>
