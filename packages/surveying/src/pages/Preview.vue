<!-- 创建时间2021/11/08 17:37:35 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：调查答题前，基本信息预览 -->
<template>
  <div
    v-if="(!isTourist || !loading) && !needCollect && !routeParams.skipPreview"
    class="yxtulcdsdk-survey-info"
    :class="{
      'yxtulcdsdk-survey-info--tourist':isTourist,
      'pv24 ph24': deepStudy || isTourist
    }"
  >
    <yxtf-breadcrumb v-if="!isTourist && !deepStudy" separator-class="yxtf-icon-arrow-right" class="layout-self-start mb24">
      <yxtf-breadcrumb-item :to="{ path: '/' }">{{ $t('pc_survey_tit_center') /** 调研中心 */ }}</yxtf-breadcrumb-item>
      <yxtf-breadcrumb-item>{{ $t('pc_survey_tit_surveypreview') /** 调查预览 */ }}</yxtf-breadcrumb-item>
    </yxtf-breadcrumb>
    <div v-loading="loading" class="yxtulcdsdk-survey-info__main">
      <div class="standard-size-24 yxt-weight-5 text-center word-wrap">{{ survey.projectName || '&nbsp;' }}</div>
      <div class="yxtulcdsdk-survey-info__panel mt48 bg-gray-1 pv30 flex standard-size-18">
        <div class="flex flex-1 border-right">
          <div class="w64 flex-shrink-1"></div>
          <div class="flex-shrink-0">
            <div class="flex flex-mid">
              <media-svg
                width="18px"
                height="18px"
                is-stc
                icon-class="shijian"
              />
              <span class="ml8 color-gray-8">{{ $t('pc_survey_lbl_startime') /** 开始时间 */ }}：</span>
              <span v-show="!loading" class="yxt-weight-5">{{ dateFormat(survey.startTime,'yyyy-MM-dd HH:mm') }}</span>
            </div>
            <div class="mt32 flex flex-mid">
              <media-svg
                width="18px"
                height="18px"
                is-stc
                icon-class="shuliang"
              />
              <span class="ml8 color-gray-8">{{ $t('pc_survey_lbl_quesnum') /** 题目数量 */ }}：</span>
              <span v-show="!loading" class="yxt-weight-5">{{ survey.quesNum }}</span>
            </div>
            <div class="mt32 flex flex-mid">
              <media-svg
                width="18px"
                height="18px"
                is-stc
                icon-class="xianshi"
              />
              <span class="ml8 color-gray-8">{{ $t('pc_survey_lbl_surveytimelimit') /** 问卷限时 */ }}：</span>
              <span v-show="!loading" class="yxt-weight-5">{{ timeLimit }}</span>
            </div>
          </div>
          <div class="w64 flex-shrink-1 flex-g-1"></div>
        </div>
        <div class="flex flex-1">
          <div class="w64 flex-shrink-1"></div>
          <div class="flex-shrink-0">
            <div class="flex flex-mid">
              <media-svg
                width="18px"
                height="18px"
                is-stc
                icon-class="jieshu"
              />
              <span class="ml8 color-gray-8">{{ $t('pc_survey_lbl_endtime') /** 结束时间 */ }}：</span>
              <span v-show="!loading" class="yxt-weight-5">{{ dateFormat( survey.endTime,'yyyy-MM-dd HH:mm') }}</span>
            </div>
            <div class="mt32 flex flex-mid">
              <media-svg
                width="18px"
                height="18px"
                is-stc
                icon-class="cishu"
              />
              <span class="ml8 color-gray-8">{{ $t('pc_survey_lbl_numsjoin') /** 参与次数 */ }}：</span>
              <span v-show="!loading" class="yxt-weight-5">{{ survey.usedNum || 0 }}/{{ numLimit }}</span>
            </div>
            <div class="mt32">{{ " " }}</div>
          </div>
          <div class="w64 flex-shrink-1 flex-g-1"></div>
        </div>
      </div>

      <template v-if="survey.projectDesc">
        <div class="mt24 yxt-weight-5 flex flex-mid">
          <span class="standard-size-16">{{ $t('pc_survey_lbl_surveydesc') /** 调查说明 */ }}</span>
        </div>
        <div class="color-gray-9 mt8 ws-prewrap" v-html="survey.projectDesc">
        </div>
      </template>

      <template v-if="customFields && customFields.length">
        <div class="mt24 yxt-weight-5 flex flex-mid">
          <span class="standard-size-16">{{ $t('pc_survey_lbl_otherinfo') }}</span>
        </div>
        <div class="color-gray-9 mt10">
          <div v-for="item in customFields" :key="item.id">
            <!-- 单行文本 -->
            <div v-if="item.fieldType === 0" class="color-gray-9 mt10 mb16">
              <yxt-tooltip
                :content="item.detail"
                placement="top"
                open-filter
              >
                <div class="ellipsis">
                  {{ item.fieldName }}：{{ item.detail || '--' }}
                </div>
              </yxt-tooltip>
            </div>
            <!-- 多行文本 -->
            <VueClamp v-else-if="item.fieldType === 1" :max-lines="3" autoresize>
              {{ item.fieldName }}：{{ item.detail || '--' }}
              <template #after="{ clamped,expanded, toggle }">
                <div v-if="clamped || expanded" class="color-primary-6 d-in-block" @click="toggle">
                  {{ !expanded ? $t('pc_survey_lbl_expand2'/** 展开 */) : $t('pc_survey_lbl_stow'/** 收起 */) }}
                </div>
              </template>
            </VueClamp>
            <!-- 选项 -->
            <div v-else-if="item.fieldType === 2" class="color-gray-9 mt10 mb16 ellipsis">
              {{ item.fieldName }}：{{ getOptionDetail(item) }}
            </div>
            <!-- 时间 -->
            <div v-else-if="item.fieldType === 3" class="color-gray-9 mt10 mb16 ellipsis">
              {{ item.fieldName }}：{{ item.detail ? new Date(item.detail).format('yyyy-MM-dd HH:mm') : '--' }}
            </div>
            <!-- 时间范围 -->
            <div v-else-if="item.fieldType === 4" class="color-gray-9 mt10 mb16 ellipsis">
              {{ item.fieldName }}：{{ formatTimeRange(item) }}
            </div>
            <!-- 数值 -->
            <div v-else-if="item.fieldType === 6" class="color-gray-9 mt10 mb16 ellipsis">
              {{ item.fieldName }}：{{ item.detail || '--' }}
            </div>
          </div>
        </div>
      </template>
      <div class="pt36"></div>
      <common-sticky
        v-if="!loading"
        :first-delay="100"
        :by-class="deepStudy"
      >
        <div class="pv12 flex flex-center bg-white">
          <yxtf-button
            type="primary"
            size="larger"
            :disabled="!canAnswer"
            @click="goAnswering(false)"
          >
            {{ canAnswer ? $t('pc_survey_btn_join' /** 立即参与 */) : survey.error.message }}
          </yxtf-button>
        </div>
      </common-sticky>
    </div>
  </div>
</template>

<script>
import MediaSvg from './components/MediaSvg.vue';
import CommonSticky from './components/CommonSticky.vue';
import { surveyPreview } from '../services/user.service.js';
import touristJoin from '../mixins/touristJoin.js';
import VueClamp from 'vue-clamp';
import deepStudyPage from '../mixins/deepStudyPage';
import { dateFormat } from '../core/utils';

const SUBMITFREQ_LANS = {
  D: ['pc_survey_lbl_numofperday'],
  W: ['pc_survey_lbl_numofperweek'],
  M: ['pc_survey_lbl_numofpermonth'],
  Q: ['pc_survey_lbl_numofperseason'],
  Y: ['pc_survey_lbl_numofperyear'],
  T: null,
  WP: ['pc_survey_lbl_numofpersomeday', 7],
  MP: ['pc_survey_lbl_numofpersomeday', 30],
  QP: ['pc_survey_lbl_numofpersomeday', 90],
  YP: ['pc_survey_lbl_numofpersomeday', 365]
};
export default {
  name: 'Preview',
  mixins: [touristJoin, deepStudyPage],
  components: {
    MediaSvg,
    VueClamp,
    CommonSticky
  },
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      loading: true,
      surveyId: routeParams.id, // 调查项目的项目的ID
      type: routeParams.type, // 调查来源类型
      targetId: routeParams.tid, // 调查项目的评价批次的ID
      batchId: routeParams.batchId,
      survey: {}
    };
  },
  computed: {
    timeLimit() {
      return this.survey.answerTime ? this.$t('pc_survey_lbl_minutesunit', [this.survey.answerTime / 60]) : this.$t('pc_survey_lbl_surveytimenolimit');
    },
    numLimit() {
      if (!this.survey.submitFreq) {
        return this.$t('pc_survey_manageindex_lbl_unlimited');
      }
      const limtType = this.survey.submitFreq.split('|');
      const lanSetting = SUBMITFREQ_LANS[limtType[0]];
      if (lanSetting) {
        const lanparam = [limtType[1]];
        if (lanSetting[1]) {
          lanparam.push(lanSetting[1]);
        }
        return this.$t(lanSetting[0], lanparam);
      } else {
        return limtType[1];
      }
    },
    canAnswer() {
      return !(this.survey.error && this.survey.error.key);
    },
    customFields() {
      return this.survey.customFieldProject && this.survey.customFieldProject.filter(d => d.visible && d.detail);
    }
  },
  created() {
    this.init();
  },
  mounted() {
  },
  methods: {
    dateFormat,
    init() {
      surveyPreview(this.surveyId, this.targetId, this.batchId).then((res) => {
        this.survey = res.data;
        this.checkTestSetting(this.survey);
        this.loading = false;

        if (this.routeParams.skipPreview) {
          this.goAnswering(true);
        }
        const { error } = this.survey;
        // 非做答后回到预览页时，做完了需要跳结果查看
        error && error.key === 'apis.survey.uam.valid.remain_num.error' && !this.routeParams.back && this.goResult();
      }).catch((errorData) => {
        this.checkError(errorData);
      });
    },
    // 前往问卷作答页面
    goAnswering(replace) {
      this.$emit('changeStep', this.UserSurveyStep.answer, {
        ... this.routeParams,
        id: this.surveyId,
        uamId: this.survey.uamId
      }, replace);
    },
    // 前往作答结果查看页面
    goResult() {
      this.$emit('changeStep', this.UserSurveyStep.result, {
        ... this.routeParams,
        id: this.surveyId,
        uamId: this.survey.uamId,
        fp: this.routeParams.skipPreview ? undefined : 1 // 标识预览来的，可以返回
      }, true);
    },
    formatTimeRange(item) {
      if (!item.detail) return '--';
      try {
        const data = JSON.parse(item.detail);
        return new Date(data[0]).format('yyyy-MM-dd HH:mm') + ' ' + this.$t('pc_survey_lbl_to' /* 至 */) + ' ' + new Date(data[1]).format('yyyy-MM-dd HH:mm');
      } catch (error) {
        return '--';
      }
    },
    getOptionDetail(item) {
      if (!item.detail || !item.fieldOptions || item.fieldOptions.length === 0) return '--';
      const data = item.fieldOptions.find(d => d.lanKey === item.detail);
      return data ? data.content : '--';
    },
    checkError(error) {
      if (!error) {
        return false;
      }
      // 次数用尽时前往
      if (error.key === 'apis.survey.uam.valid.remain_num.error') {
        this.goResult();
        return true;
      } else if (error.key && error.key.indexOf('global.token') >= 0) {
        return true;// 不弹出token错误
      }

      this.$emit('errorPublic', error && error.code, error && error.message);

      return true;
    }
  },
  beforeDestroy() {
  }
};
</script>
