<!--  第三方调查中转 -->
<template>
  <div></div>
</template>

<script>
import { surveyOuterPreviewParams } from '../services/user.service.js';
import deepStudyPage from '../mixins/deepStudyPage';
export default {
  name: 'Fbi',
  mixins: [deepStudyPage],
  components: {
  },
  data() {
    return {
    };
  },
  computed: {
  },
  created() {
    this.init();
  },
  mounted() {
  },
  methods: {
    init() {
      // masterId, packageId, taskType  不拆包、不同步进度时课程包对应的项目ID、课程包ID,taskType:100
      // 面授讲师评价学员 taskType 50
      // sourceParam 第三方来源的参数集，json字符串，用于后续回调第三方
      const {
        id, type, tid, tname, success, batchId,
        hideBack, allowResult, skipPreview, // 页面展示处理用的参数
        ...others
      } = this.routeParams;
      const _skipPreview =  skipPreview === undefined ||  skipPreview; // 中转页默认跳过预览页，这里skipPreview默认true
      const isSurvey = !type || type === '1' || type === 1;
      const postData = {
        objId: id,
        objType: type,
        targetId: tid,
        targetName: tname,
        batchId: batchId || '',
        successUrl: encodeURIComponent(success || ''),
        ...others
      };
      surveyOuterPreviewParams(postData).then((res) => {
        this.$emit('changeStep', isSurvey || !_skipPreview ? this.UserSurveyStep.preview : this.UserSurveyStep.answer, {
          id: res.data.projectId,
          uamId: res.data.uamId,
          hideBack,
          allowResult,
          skipPreview: isSurvey ? undefined : (_skipPreview ? 1 : 0),
          ...postData
        }, true);
      }).catch((errorData) => {
        this.checkError(errorData);
      });
    },
    checkError(error) {
      if (!error) {
        return false;
      }
      this.$emit('errorPublic', error && error.code, error && error.message);

      return true;
    }
  },
  beforeDestroy() {
  }
};
</script>
