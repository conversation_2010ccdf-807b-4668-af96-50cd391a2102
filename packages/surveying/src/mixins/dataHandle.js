import { QuesScoreType, SurveyQuesType, UploadStatus } from '../core/enum';
import {setQuesAnswerStatus} from './commonQues';

/**
 * 选择题的分组数据处理
 */
export default {
  methods: {
    // 根据前台_id获取对应数据的index
    getItemIndexInGroup(_id, groupList, isItemList) {
      const list = this.getGroupFlatList(groupList, isItemList);
      return list.findIndex(item => item._id === _id);
    },
    // 根据前台_id获取对应数据
    getItemInGroup(_id, groupList, isItemList) {
      const list = this.getGroupFlatList(groupList, isItemList);
      return list.find(item => item._id === _id);
    },
    // 根据后台_id获取对应数据
    getItemInGroupById(itemIdOrIndex, groupList, isItemList) {
      const list = this.getGroupFlatList(groupList, isItemList);
      return list.find((item, i) => item.itemId === itemIdOrIndex || i === itemIdOrIndex);
    },
    // 数组扁平
    getGroupFlatList(groupList, isItemList) {
      groupList = groupList || [];
      if (isItemList) return groupList;
      const list = [];
      groupList.forEach(group => {
        group.itemList.forEach(item => {
          list.push(item);
        });
      });
      return list;
    },
    // 逻辑设置接口数据转试图数据 设置_id
    getJumpConfigPostData(jumpConfig, groupList, isItemList) {
      jumpConfig = jumpConfig || [];
      jumpConfig.forEach(config => {
        config.itemIds.forEach((val, i) => {
          const item = this.getItemInGroup(val, groupList, isItemList);
          if (item && item.itemId) {
            // val = item.itemId
          } else {
            config.itemIds[i] = this.getItemIndexInGroup(val, groupList, isItemList);
            // val = index
          }
        });
        // hideQuesId数组转化成字符串
        config.hideQuesId = config.hideQuesId ? config.hideQuesId.toString() : '';
      });
      return jumpConfig;
    },
    // 逻辑设置视图数据转接口数据 还原_id
    getJumpConfigViewData(jumpConfig, groupList, isItemList) {
      jumpConfig.forEach(config => {
        config.itemIds.forEach((val, i) => {
          const item = this.getItemInGroupById(val, groupList, isItemList);
          config.itemIds[i] = item._id;
        });
      });
      return jumpConfig;
    },

    // 初始化数据源
    // 设置itemList中的_id
    setItemGroupData(groupList) {
      groupList = groupList || [];
      groupList.forEach(group => {
        group.itemList.forEach(item => {
          item._id = item.itemId;
        });
      });
      return groupList;
    },
    setItemIdData(list, isGroup) {
      if (isGroup) {
        return this.setItemGroupData(list);
      }
      list.forEach(item => {
        item._id = item.itemId;
      });
      return list;
    },

    // 处理逻辑设置数据
    setJumpConfigData(jumpConfig) {
      if (!jumpConfig || !jumpConfig.length) {
        return jumpConfig;
      }
      jumpConfig.forEach(item => {
        // 将hideQuesId字符串转为数组
        item.hideQuesId = item.hideQuesId ? item.hideQuesId.split(',') : [];
      });
      return jumpConfig;
    },

    // 两个数组中的值是否互相包含
    containListItem(list1, list2) {
      return list1.length === list2.length && list1.every(item1 => list2.includes(item1));
    },

    // 试题的接口数据转化为前端数据格式
    interfaceDataToViewData(item, showQuesNum) {
      const { content, contentAttachList, templateId, sortNo, questionId, mustAnswer, showQuesType, answerMode, quesType, sourceQuesType, ...option } = item;
      let data = { content, contentAttachList, templateId, sortNo, questionId, mustAnswer, showQuesType, answerMode, quesType, sourceQuesType };
      // 兼容老日期类型数据
      if (data.answerMode === null && item.quesType === SurveyQuesType.date) {
        data.answerMode = 0;
      }
      data.showQuesNum = showQuesNum;
      if (item.quesType === SurveyQuesType.singleChoose || item.quesType === SurveyQuesType.multiChoose) {
        // 单选 多选 的选项添加前端属性_id作为唯一索引，保存的时候再次转化
        const selectQuestion4Create = option.selectQuestion4Create || option.multiSelectQuestion4Create;
        selectQuestion4Create.jumpConfig = this.setJumpConfigData(selectQuestion4Create.jumpConfig);
        selectQuestion4Create.itemGroups = this.setItemIdData(selectQuestion4Create.itemGroups, true);
        data = { ...data, ...selectQuestion4Create };
      } else if (item.quesType === SurveyQuesType.judge) {
        const judgeQuestion4Create = option.judgeQuestion4Create;
        judgeQuestion4Create.jumpConfig = this.setJumpConfigData(judgeQuestion4Create.jumpConfig);
        judgeQuestion4Create.itemList = this.setItemIdData(judgeQuestion4Create.itemList);
        data = { ...data, ...judgeQuestion4Create };
      } else if (item.quesType === SurveyQuesType.classify) {
        const classifyQuestion4Create = option.classifyQuestion4Create;
        classifyQuestion4Create.jumpConfig = this.setJumpConfigData(classifyQuestion4Create.jumpConfig);
        classifyQuestion4Create.chosenList = this.setItemIdData(classifyQuestion4Create.chosenList);
        classifyQuestion4Create.classifyList = this.setItemIdData(classifyQuestion4Create.classifyList);
        data = { ...data, ...classifyQuestion4Create };
      } else if (item.quesType === SurveyQuesType.textChoose || item.quesType === SurveyQuesType.multiTextChoose ||
      item.quesType === SurveyQuesType.picChoose || item.quesType === SurveyQuesType.multiPicChoose ||
      item.quesType === SurveyQuesType.videoChoose || item.quesType === SurveyQuesType.multiVideoChoose) {
        const voteQuestion4Create = option.voteQuestion4Create;
        data = { ...data, ...voteQuestion4Create };
      } else if (item.quesType === SurveyQuesType.sort) {
        const orderQuestion4Create = option.orderQuestion4Create;
        orderQuestion4Create.itemList = this.setItemIdData(orderQuestion4Create.itemList);
        data = { ...data, ...orderQuestion4Create };
      } else if (item.quesType === SurveyQuesType.fill) {
        const fillQuestion4Create = option.fillQuestion4Create;
        fillQuestion4Create.fillList = this.setItemIdData(fillQuestion4Create.fillList);
        data = { ...data, ...fillQuestion4Create };
      } else if (item.quesType === SurveyQuesType.multiFill) {
        const multiFillQuestion4Create = option.multiFillQuestion4Create;
        multiFillQuestion4Create.fillList = this.setItemIdData(multiFillQuestion4Create.fillList);
        data = { ...data, ...multiFillQuestion4Create };
      } else if (item.quesType === SurveyQuesType.essay) {
        const shortAnswerQuestion4Create = option.shortAnswerQuestion4Create;
        data = { ...data, ...shortAnswerQuestion4Create };
      } else if (item.quesType === SurveyQuesType.scale) {
        const scaleQuestion4Create = option.scaleQuestion4Create;
        scaleQuestion4Create.jumpConfig = this.setJumpConfigData(scaleQuestion4Create.jumpConfig);
        scaleQuestion4Create.itemList = this.setItemIdData(scaleQuestion4Create.itemList);
        data = { ...data, ...scaleQuestion4Create };
      } else if (item.quesType === SurveyQuesType.multiScale) {
        const multiScaleQuestion4Create = option.multiScaleQuestion4Create;
        multiScaleQuestion4Create.jumpConfig = this.setJumpConfigData(multiScaleQuestion4Create.jumpConfig);
        multiScaleQuestion4Create.itemGroups = this.setItemIdData(multiScaleQuestion4Create.itemGroups, true);
        data = { ...data, ...multiScaleQuestion4Create };
      } else if (item.quesType === SurveyQuesType.nps) {
        const npsScaleQuestion4Create = option.npsScaleQuestion4Create;
        npsScaleQuestion4Create.jumpConfig = this.setJumpConfigData(npsScaleQuestion4Create.jumpConfig);
        npsScaleQuestion4Create.itemList = this.setItemIdData(npsScaleQuestion4Create.itemList);
        data = { ...data, ...npsScaleQuestion4Create };
      } else if (item.quesType === SurveyQuesType.score) {
        const scoreQuestion4Create = option.scoreQuestion4Create;
        scoreQuestion4Create.itemGroups = this.setItemIdData(scoreQuestion4Create.itemGroups, true);
        data = { ...data, ...scoreQuestion4Create };
      } else if (item.quesType === SurveyQuesType.evaluation) {
        const feedBackQuestion4Create = option.feedBackQuestion4Create;
        feedBackQuestion4Create.itemGroups = this.setItemIdData(feedBackQuestion4Create.itemList);
        data = { ...data, ...feedBackQuestion4Create };
      } else if (item.quesType >= SurveyQuesType.name && item.quesType <= SurveyQuesType.number) {
        const baseInfoQuestion4Create = option.baseInfoQuestion4Create;
        data = { ...data, ...baseInfoQuestion4Create };
      } else if (item.quesType === SurveyQuesType.upload) {
        const uploadQuestion4Create = option.uploadQuestion4Create;
        data = { ...data, ...uploadQuestion4Create };
      } else if (item.quesType === SurveyQuesType.slideScore) {
        const slideScoreQuestion4Create = option.slideScoreQuestion4Create;
        data = { ...data, ...slideScoreQuestion4Create };
      }

      setQuesAnswerStatus(data);
      return data;
    },

    // 试题的前端数据格式转化为接口数据
    viewDataToInterfaceData(item) {
      const { templateId, quesType, mustAnswer, showQuesType, answerMode, content, sortNo, questionId, contentAttachList = [], ...other } = JSON.parse(JSON.stringify(item));
      const params = { templateId, quesType, mustAnswer, showQuesType, answerMode, content, sortNo, questionId, contentAttachList };
      if (!this.checkAttach(item)) return false;
      if (!this.checkCorrectAnswer(item)) return false;
      if (!this.checkRange(item)) return false;
      if (quesType === SurveyQuesType.singleChoose) {
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        if (other.enabledJump) other.jumpConfig = this.getJumpConfigPostData(other.jumpConfig, other.itemGroups);
        params.selectQuestion4Create = other;
      } else if (quesType === SurveyQuesType.multiChoose) {
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        if (other.enabledJump) other.jumpConfig = this.getJumpConfigPostData(other.jumpConfig, other.itemGroups);
        params.multiSelectQuestion4Create = other;
      } else if (quesType === SurveyQuesType.judge) {
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        if (other.enabledJump) other.jumpConfig = this.getJumpConfigPostData(other.jumpConfig, other.itemList, true);
        params.judgeQuestion4Create = other;
      } else if (quesType === SurveyQuesType.classify) {
        // 分类题不涉及选项的选择，所以不用转化_id
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        params.classifyQuestion4Create = other;
      } else if (quesType === SurveyQuesType.fill) {
        params.fillQuestion4Create = other;
      } else if (quesType === SurveyQuesType.multiFill) {
        params.multiFillQuestion4Create = other;
      } else if (quesType === SurveyQuesType.essay) {
        params.shortAnswerQuestion4Create = other;
      } else if (quesType === SurveyQuesType.textChoose || quesType === SurveyQuesType.multiTextChoose ||
      quesType === SurveyQuesType.picChoose || quesType === SurveyQuesType.multiPicChoose ||
      quesType === SurveyQuesType.videoChoose || quesType === SurveyQuesType.multiVideoChoose) {
        if (other.itemList && other.itemList.length <= 1) {
          this.$message.warning(this.$t('pc_survey_msg_lastchoosenum', [2]));// 最少添加{0}个选项
          return false;
        }
        params.voteQuestion4Create = other;
      } else if (quesType === SurveyQuesType.sort) {
        params.orderQuestion4Create = other;
      } else if (quesType === SurveyQuesType.scale) {
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        if (other.enabledJump) other.jumpConfig = this.getJumpConfigPostData(other.jumpConfig, other.itemList, true);
        params.scaleQuestion4Create = other;
      } else if (quesType === SurveyQuesType.multiScale) {
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        if (other.enabledJump) other.jumpConfig = this.getJumpConfigPostData(other.jumpConfig, other.itemGroups);
        params.multiScaleQuestion4Create = other;
      } else if (quesType === SurveyQuesType.nps) {
        other.enabledJump = other.jumpConfig && other.jumpConfig.length ? 1 : 0;
        if (other.enabledJump) other.jumpConfig = this.getJumpConfigPostData(other.jumpConfig, other.itemList, true);
        params.npsScaleQuestion4Create = other;
      } else if (quesType === SurveyQuesType.score) {
        params.scoreQuestion4Create = other;
      } else if (quesType === SurveyQuesType.evaluation) {
        params.feedBackQuestion4Create = other;
      } else if (quesType === SurveyQuesType.slideScore) {
        params.slideScoreQuestion4Create = other;
      }
      return params;
    },
    // 检查投票题的上传状态
    checkAttach(ques) {
      let uploadStatus = true;
      if ((ques.contentAttachList || []).some(item => item._status === UploadStatus.going || item._status === UploadStatus.fail)) {
        uploadStatus = false;
      }
      if (ques.itemList && ques.itemList.length) {
        ques.itemList.forEach(item => {
          if ((item.contentAttachList || []).some(e => e._status === UploadStatus.going || e._status === UploadStatus.fail)) {
            uploadStatus = false;
          }
        });
      }
      if (ques.itemGroups && ques.itemGroups.length) {
        ques.itemGroups.forEach(group => {
          if (group && group.itemList) {
            group.itemList.forEach(item => {
              if ((item.contentAttachList || []).some(e => e._status === UploadStatus.going || e._status === UploadStatus.fail)) {
                uploadStatus = false;
              }
            });
          }
        });
      }

      // if (ques.quesType === SurveyQuesType.picChoose || ques.quesType === SurveyQuesType.multiPicChoose || ques.quesType === SurveyQuesType.videoChoose || ques.quesType === SurveyQuesType.multiVideoChoose) {
      //   ques.itemList.forEach(item => {
      //     if (item.contentAttachList && item.contentAttachList.length) {
      //       let attach = item.contentAttachList[0]
      //       if (attach._status === UploadStatus.going || attach._status === UploadStatus.fail) {
      //         uploadStatus = false
      //       }
      //     }
      //   })
      // }

      if (!uploadStatus) {
        const isPic = ques.quesType === SurveyQuesType.picChoose || ques.quesType === SurveyQuesType.multiPicChoose;
        this.$message.warning(isPic ? this.$t('pc_survey_msg_imageUploadFailed') : this.$t('pc_survey_msg_videoUploadFailed')); /** 未上传完成，请检查后再试 */
      }
      return uploadStatus;
    },
    getUploadFail(file) {
      return file._status === UploadStatus.going || file._status === UploadStatus.fail;
    },
    // 检查正确答案是否设置
    checkCorrectAnswer(ques) {
      let tag = true;
      if (ques.enabledScore === 1 &&
        (ques.scoreType === QuesScoreType.correct ||
          ques.scoreType === QuesScoreType.part ||
          ques.scoreType === QuesScoreType.matchKeyword ||
          ques.scoreType === QuesScoreType.matchAnswer ||
          ques.scoreType === QuesScoreType.matchAllKeyword)) {
        if (ques.quesType === SurveyQuesType.singleChoose ||
              ques.quesType === SurveyQuesType.multiChoose
        ) {
          // 选择题 判断题
          const list = this.getGroupFlatList(ques.itemGroups);
          tag = list.some(item => item.correctAnswer === 1);
        } else if (ques.quesType === SurveyQuesType.judge) {
          // 判断题
          tag = ques.itemList.some(item => item.correctAnswer === 1);
        } else if (ques.quesType === SurveyQuesType.fill ||
                ques.quesType === SurveyQuesType.multiFill) {
          // 填空题
          tag = ques.fillList.every(item => !!item.rightAnswer.trim());
        } else if (ques.quesType === SurveyQuesType.essay) {
          // 简答题
          tag = !!ques.rightAnswer.trim();
        }
      }
      if (!tag) {
        this.$message.warning(this.$t('pc_survey_msg_pleasesetanswer')/** 请设置正确答案 */);
      }
      return tag;
    },
    // 校验多项填空题，填空题的字数范围 || 多选题的选项范围
    checkRange(ques) {
      if (ques.quesType === SurveyQuesType.multiChoose) {
        if (ques.minChoose > 0 && ques.maxChoose > 0 && ques.minChoose > ques.maxChoose) {
          this.$message.warning(this.$t('pc_survey_msg_chooserangeerror')/** 多选题可选最少不能大于可选最大 */);
          return false;
        }
        let itemListNum = 0;
        ques.itemGroups.forEach(item => {
          itemListNum += item.itemList.length;
        });
        if (ques.minChoose > 0 && ques.minChoose > itemListNum) {
          this.$message.warning(this.$t('pc_survey_msg_minchooseerror')/** 多选题可选最少不能大于选项数 */);
          return false;
        }
      } else if (ques.quesType === SurveyQuesType.fill || ques.quesType === SurveyQuesType.multiFill || ques.quesType === SurveyQuesType.essay) {
        if (ques.minLength > 0 && ques.maxLength > 0 && ques.minLength > ques.maxLength) {
          this.$message.warning(this.$t('pc_survey_msg_wordrangeerror')/** 最少填写字数不能大于最大填写字数 */);
          return false;
        }
      }
      return true;
    }

  }

};
