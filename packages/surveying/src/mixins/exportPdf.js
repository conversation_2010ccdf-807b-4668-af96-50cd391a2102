import { domainBaseUrl } from 'packages/examing/src/configs/domain.js';
import { commonUtil } from 'yxt-biz-pc';
import { postManagerExportAnswers } from '../services/base.service';

export default {
  data() {
    return {
      pdfName: ''
    };
  },

  computed: {
    // 页面是否是导出页面
    exporting() {
      return !!this.$route.query.exporting;
    },

    paging() {
      return !!this.$route.query.paging;
    }
  },

  methods: {
    // 获取当前语言类型
    getCurrentLangStr() {
      const language = commonUtil.getLanguage();
      return `&yxtLang=${language}`;
    },

    // 是否分页导出
    getPagingStr(paging) {
      return paging ? `&paging=${paging}&allOption=1` : '';
    },

    exportPdf(url, autoW, autoH, container, isNeedAllPage, menu, reportMsg, isPage) {
      menu.downLoading = true;

      let urlNow = url || window.location.href;
      const domain = domainBaseUrl('apiBaseUrl') + 'exporttoolpdf/';
      urlNow += `${urlNow.indexOf('?') >= 0 ? '&' : '?'}exporting=1${this.getPagingStr(isPage)}${this.getCurrentLangStr()}`;

      let answerObj = {
        pdfApiUrl: `${domain}pdf/down`,
        pdfToken: localStorage.token,
        reqUrl: urlNow,
        name: this.pdfName,
        autoContainer: container,
        exportFileType: isPage ? 1 : isNeedAllPage ? 2 : 1
      };

      if (isPage) {
        answerObj.needA4 = 1;
      } else {
        if (isNeedAllPage) {
          answerObj.isNeedAllPage = 1;
        }
        if (autoW) {
          answerObj.autoWidth = 1;
        }
        if (autoH) {
          answerObj.autoHeight = 1;
        }
        if (!autoW && !autoH) {
          answerObj.needA4 = 1;
        }
      }

      postManagerExportAnswers(answerObj).then(() => {
        this.$confirm(this.$t('pc_core_lbl_downloadingfile'), this.$t('pc_core_lbl_viewexportfile'), {
          confirmButtonText: this.$t('down_common_btn_view'), // 查看
          cancelButtonText: this.$t('down_btn_cancle'), // 取消
          type: 'success'
        }).then(() => {
          const href = window.location.origin + '/down/#/download';
          window.open(href, '_blank');
        });
      }).catch(err => {
        console.log(err);
      }).then(() => {
        menu.downLoading = false;
        // 报告导出时关闭message
        reportMsg && reportMsg.close();
      });
    }
  }
};
