/**
 * 自动填值的mixins
 */
import { isNumber, replaceHtmlTags } from '../core/utils';

export default {
  methods: {
    /**
     * 组名自动赋值
     * @param {*} obj 组数据对象
     * @param {*} index 序号 1、2、3
     */
    checkGroupNull(obj, index, defaultContent) {
      if (this._needSetDefaultContent({ content: obj.groupName })) {
        this.$set(obj, 'groupName', defaultContent || this.$t('pc_survey_lbl_group') + index);// 分组1
      }
    },
    /**
     * 组下面的item自动赋值
     * @param {*} obj item数据对象
     * @param {*} groups 组数据
     */
    checkGroupItemNull(obj, groups) {
      if (this._needSetDefaultContent(obj)) {
        const items = groups.map(item => (item.itemList)).flat();
        const index = items.findIndex(item => item === obj);
        this.$set(obj, 'content', this.$t('pc_survey_lbl_option') + (index + 1));// 选项1
      }
    },
    /**
     * item赋值
     * @param {*} obj 数据对象
     * @param {*} index 序号 1、2、3
     * @param {*} defaultContent 默认文案
     */
    checkItemNull(obj, index, defaultContent) {
      console.log(this._needSetDefaultContent(obj));
      if (this._needSetDefaultContent(obj)) {
        this.$set(obj, 'content', defaultContent || this.$t('pc_survey_lbl_option') + index);
      }
    },
    /**
     * 数值默认值设置
     * @param {*} obj
     * @param {*} key
     * @param defaultVal
     */
    setNumberDefaultValue(obj, key, defaultVal = 0) {
      const val = obj[key];
      if (!isNumber(val)) {
        this.$set(obj, key, defaultVal);
      }
    },
    setStringDefaultValue(obj, key, defaultVal) {
      const val = obj[key];
      if (!val) {
        this.$set(obj, key, defaultVal || '');
      }
    },
    /**
     * @param {*} obj 数据对象
     */
    checkContentNull(obj) {
      if (this._needSetDefaultContent(obj)) {
        // this.index 为组件props属性，为题的序号
        this.$set(obj, 'content', this.$t('pc_survey_lbl_question') + this.index);// 题目1
      }
    },

    _needSetDefaultContent({ content, contentAttachList = [] }) {
      if (this._richTextIsEmpty(content) && (!contentAttachList || contentAttachList.length === 0)) {
        return true;
      }
      return false;
    },
    _richTextIsEmpty(text = '') {
      return !replaceHtmlTags(text) && !text.includes('img');
    }
  }
};
