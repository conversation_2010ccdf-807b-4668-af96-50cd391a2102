/**
 * 学员端游客参与考试相关方法
 */

import { TOURISTINFOCOLLECTOR_CPNTYPES, TOURISTINFOCOLLECTOR_ALLTYPES } from '../core/config';
import { getTouristSet } from '../services/user.service';

export default {
  data() {
    return {
      collected: this.$route.query.collected || this.$route.query.redo,
      needCollect: false
    };
  },
  computed: {
    /**
     * 判断当前用户是否游客
     * @returns 是否游客
     */
    isTourist() {
      return (window.localStorage.isVisitor === '1' || window.localStorage.survey_visitor_token === window.localStorage.token) || (this.$route.meta.anonymous && !localStorage.getItem('token'));
    }
  },
  beforeCreate() {
    // 在token被别的业务清理掉的时候，回填
    if (window.localStorage.survey_visitor_token && !window.localStorage.token) {
      window.localStorage.token = window.localStorage.survey_visitor_token;
    }
  },
  methods: {
    getTouristSet,
    /**
     * 检测考试相关的信息（扫码主入口）
     */
    checkTestSetting(res, err) {
      if (res) {
        if (this.isTourist && res.visitor && res.visitor.visitorEnabled && !this.collected) {
          if (res.visitor.visitorLoginStrategy === 3) {
            // 免登录 记录临时token
            if (res.token) {
              window.localStorage.token = res.token;
              window.localStorage.survey_visitor_token = res.token;
              // 填入当前机构ORGID,防止身份被扫码中转页清除
              window.localStorage.orgId = res.orgId;
            }
          } else {
            this.goTouristLogin(res);
          }
        }
      } else {
        this.$handleError(err);
      }
    },
    /**
     * 前往游客登录
     */
    goTouristLogin(info) {
      // 清除之前旧的免登token
      if (window.localStorage.token && window.localStorage.survey_visitor_token === window.localStorage.token) {
        window.localStorage.survey_visitor_token = '';
        window.localStorage.token = '';
      }

      this.needCollect = true;
      let { projectName, orgId, visitor } = info;
      orgId = orgId || window.localStorage.orgId;
      // 收集完成后的回调地址
      const isMobile = visitor.visitorLoginStrategy === 2;
      const cbUrl = window.location.origin + window.location.pathname + this.$router.resolve({
        name: 'Preview',
        query: {
          collected: 1,
          ...this.$route.query
        }
      }).href;

      const visitorInfoOld = JSON.parse(visitor.visitorInfo);
      const visitorInfo = {};
      visitorInfoOld.required.forEach((k) => { visitorInfo[k] = 1; });
      visitorInfoOld.optional.forEach((k) => { visitorInfo[k] = 0; });
      // 信息收集项转化
      const vrequired = [];
      const voptional = [];
      for (const key in visitorInfo) {
        if (Object.hasOwnProperty.call(visitorInfo, key)) {
          const index = TOURISTINFOCOLLECTOR_ALLTYPES.indexOf(key);
          let type = key;
          if (index >= 0) {
            // 转化组件的key
            type = TOURISTINFOCOLLECTOR_CPNTYPES[index];
          }
          if (visitorInfo[key] === 1) {
            vrequired.push(type);
          } else if (visitorInfo[key] === 0) {
            voptional.push(type);
          }
        }
      }

      let url = `/#/visitor?biztype=4&type=${isMobile ? 'mb' : 'wx'}&pname=${encodeURIComponent(projectName)}&orgid=${orgId || ''}&allemp=1&vrequired=${vrequired.join(',')}&voptional=${voptional.join(',')}&edit=${vrequired.length > 0 || voptional.length > 0 ? 1 : 0}&url=${encodeURIComponent(cbUrl)}`;

      window.location.replace(url);
    }
  }
};
