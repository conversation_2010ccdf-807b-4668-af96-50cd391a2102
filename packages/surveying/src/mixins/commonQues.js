import { SurveyQuesType, UploadStatus, FillQuesFormat, VoteQuesLimitType } from '../core/enum';
import { isNumber } from '../core/utils';
import { i18n } from '../core/common';

const NumberReg = /^\d{1,}$/;
const CharReg = /^[a-zA-Z]+$/;
const ChineseReg = /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/;
const EmailReg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
const PhoneReg = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
// eslint-disable-next-line no-useless-escape
const WebsiteReg = /^(((ht|f)tps?):\/\/)?[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-\(\)]*[\w@?^=%&/~+#-\(\)])?$/;
const IDReg = /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/;
const Reg = {
  [FillQuesFormat.number]: NumberReg,
  [FillQuesFormat.letter]: CharReg,
  [FillQuesFormat.chinese]: ChineseReg,
  [FillQuesFormat.email]: EmailReg,
  [FillQuesFormat.phone]: PhoneReg,
  [FillQuesFormat.website]: WebsiteReg,
  [FillQuesFormat.identity]: IDReg
};

/**
    * 获取投票题的选项上限
    * @param {*} item
    * @returns
    */
export const getVoteLimitNum = (ques, item) => {
  if (ques.enableUpperLimit === VoteQuesLimitType.same && ques.upperLimit > 0) return ques.upperLimit;
  else if (ques.enableUpperLimit === VoteQuesLimitType.different && item.availableNum > 0) return item.availableNum;
  return 0;
};

export const setQuesAnswerStatus = ques => {
  let status = false;
  let errorMessage = '';
  const quesType = ques.quesType;
  if ([
    SurveyQuesType.singleChoose,
    SurveyQuesType.multiChoose,
    SurveyQuesType.multiTextChoose,
    SurveyQuesType.multiPicChoose,
    SurveyQuesType.multiVideoChoose,
    SurveyQuesType.judge
  ].includes(quesType)) {
  // if (quesType === SurveyQuesType.singleChoose || quesType === SurveyQuesType.multiChoose) {
    // 1.必答题未答  2.非必答题选项个数是否合法
    let selectedCount = 0;
    let noteIsWrite = true;
    let noteIsFit = true;

    // 投票类的原来没有组的概念
    if ([
      SurveyQuesType.multiTextChoose,
      SurveyQuesType.multiPicChoose,
      SurveyQuesType.multiVideoChoose,
      SurveyQuesType.judge
    ].includes(quesType)) {
      for (const i in ques.itemList) {
        const item = ques.itemList[i];
        if (item.userAnswerFlag) {
          selectedCount++;
          status = true;

          if (item.itemNote === 1 && !item.answerExplain) {
            noteIsWrite = false;
          } else if (item.itemNote === 1 && (ques.minLength > 0) && (item.answerExplain.length < ques.minLength)) {
            noteIsFit = false;
          }
        }
      }
    } else {
      for (const i in ques.itemGroups) {
        for (const j in ques.itemGroups[i].itemList) {
          const item = ques.itemGroups[i].itemList[j];
          if (item.userAnswerFlag) {
            selectedCount++;
            status = true;
            if (item.itemNote === 1 && !item.answerExplain) {
              noteIsWrite = false;
            } else if (item.itemNote === 1 && (ques.minLength > 0) && (item.answerExplain.length < ques.minLength)) {
              noteIsFit = false;
            }
          }
        }
      }
    }

    if (status && !noteIsWrite) {
      errorMessage = i18n.t('pc_survey_msg_filliniteminfo'/** 请输入选项说明 */);
    } else if (status && !noteIsFit) {
      errorMessage = i18n.t('pc_survey_msg_fillinleast', [ques.minLength]/** 最少填写 */);
    } else if (status && ques.minChoose > 0 && selectedCount < ques.minChoose) {
      errorMessage = i18n.t('pc_survey_msg_itemleast', [ques.minChoose] /** 此题最少选择 */);
    }
  } else if (quesType === SurveyQuesType.classify) {
    // 初始和中间状态不一样
    let count = 0;
    for (const key in ques.answerDetail) {
      count += ques.answerDetail[key].length;
    }
    status = count > 0;
  } else if (quesType === SurveyQuesType.fill || quesType === SurveyQuesType.multiFill) {
    status = ques.fillList.every(item => !!item.userAnswer);
    if (status && ques.minLength > 0 && ques.fillList.some(item => item.userAnswer.length < ques.minLength)) {
      errorMessage = i18n.t('pc_survey_msg_fillinleast', [ques.minLength]/** 最少填写 */);
    }
    if (quesType === SurveyQuesType.fill && status && ques.answerFormat !== FillQuesFormat.unlimited) {
      const reg = Reg[ques.answerFormat];
      if (!reg.test(ques.fillList[0].userAnswer)) errorMessage = i18n.t('pc_survey_msg_formaterror-' + ques.answerFormat);
    }
  // } else if (quesType === SurveyQuesType.email || quesType === SurveyQuesType.phone) {
    // 格式校验过于严格，先不加
    // status = !!ques.answer;
    // const answerFormat = quesType === SurveyQuesType.email ? FillQuesFormat.email : FillQuesFormat.phone;
    // const reg = Reg[answerFormat];
    // if (status && !reg.test(ques.answer)) errorMessage = i18n.t('pc_survey_msg_formaterror-' + answerFormat);
  } else if (quesType === SurveyQuesType.essay) {
    status = !!ques.userAnswer;
    if (status && (ques.minLength > 0) && (ques.userAnswer.length < ques.minLength)) {
      errorMessage = i18n.t('pc_survey_msg_fillinleast', [ques.minLength]/** 最少填写 */);
    }
    if (status && ques.answerFormat !== FillQuesFormat.unlimited) {
      const reg = Reg[ques.answerFormat];
      if (!reg.test(ques.userAnswer)) errorMessage = i18n.t('pc_survey_msg_formaterror-' + ques.answerFormat);
    }
  } else if (quesType === SurveyQuesType.scale || quesType === SurveyQuesType.nps || quesType === SurveyQuesType.evaluation) {
    status = ques.itemList.some(item => item.userAnswerFlag === 1);
  } else if (quesType === SurveyQuesType.multiScale || quesType === SurveyQuesType.score) {
    status = ques.itemGroups.every(group => {
      return group.itemList.some(item => item.userAnswerFlag === 1);
    });
  } else if (quesType === SurveyQuesType.slideScore) {
    status = ques.itemGroups.every(group => group.scaleValue !== null);
  } else if (quesType === SurveyQuesType.sort) {
    const answers = (ques.answerDetail || []).filter(item => item.itemId && isNumber(item.sortNo));
    status = answers.length === ques.itemList.length;
    if (!status && answers.length > 0) {
      errorMessage = i18n.t('pc_survey_msg_entercomplete');// '请填写完整'
    }
  } else if (quesType >= SurveyQuesType.name && quesType <= SurveyQuesType.number) {
    status = !!ques.answer;
  } else if (quesType === SurveyQuesType.upload) {
    const fileList = (ques.userAnswer || []).filter(item => item._status !== UploadStatus.going && item._status !== UploadStatus.fail);
    status = !!fileList.length;
    if ((ques.userAnswer || []).some(item => item._status === UploadStatus.going || item._status === UploadStatus.fail)) {
      errorMessage = i18n.t('pc_survey_msg_uploadfail');// '文件上传成功未完成'
    }
  } else if (quesType === SurveyQuesType.textChoose || quesType === SurveyQuesType.multiTextChoose ||
    quesType === SurveyQuesType.picChoose || quesType === SurveyQuesType.multiPicChoose ||
    quesType === SurveyQuesType.videoChoose || quesType === SurveyQuesType.multiVideoChoose) {
    status = ques.itemList.some(item => item.userAnswerFlag === 1);
    const fullIndexArr = [];
    ques.itemList.forEach((item, index) => {
      if (getVoteLimitNum(ques, item) > 0 && !item.remainNum && !!item.userAnswerFlag) {
        fullIndexArr.push(index + 1);
      }
    });
    if (fullIndexArr.length > 0) {
      errorMessage = i18n.t('pc_survey_msg_optionfull', [fullIndexArr.join(',')]);// `第{0}选项已满，请更换选项`
    }
  }
  if (!status && !errorMessage && ques.mustAnswer) errorMessage = i18n.t('pc_survey_msg_mustanswer'/** 此题为必答题，请填写 */);
  ques.answered = status;
  ques.errorMessage = errorMessage;
};
