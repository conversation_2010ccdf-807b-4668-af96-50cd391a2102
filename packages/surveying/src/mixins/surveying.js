/**
 * 学员参与调查公共处理方法
 */
import { startUserSurvey, submitUserSurvey, submitUserSurveyTime, submitUserQuestion, endUserSurvey, surveyOuterPreviewParams, surveyAnswerDetail } from '../services/user.service.js';
import { SurveyQuesType } from '../core/enum';
import { debounceByKey, callDebouncedFunByKey, replaceHtmlTags, filterXss, setTitle } from '../core/utils';
import dataHandle from './dataHandle.js';
import { mapActions } from 'vuex';

const SURVEY_TIME_SUBMIT_INTERVAL = 30; // 学时记录的间隔（秒）
const SURVEY_TIME_SUBMIT_QUES_TIMEOUT = 1000; // 单题提交的延迟（毫秒）
// 页面初始化的数据
const INIT_DATA = {
  loadingInner: true, // 答题区加载中
  pageKey: Date.now(), // 页面当前状态key, 用于分页重绘组件
  submiting: false, // 调查提交中
  submitingFinal: false, // 调查问卷提交中
  o2oSubmitedTip: false, // o2o调查提示
  viewResult: false, // 查看作答结果
  checkSubmit: false, // 是否校验作答情况，点提交按钮后没做答的需要给出必答提示
  onePage: false, // 不分页
  timerSTV: null, // 计时器
  rankSTV: null, // 投票排名筛选计时器
  lastTime: 0, // 剩余作答时间
  timeNeedSumit: 0, // 未提交时间
  operationDisabled: true, // 是否可操作
  survey: {
    projectName: '',
    uaId: '',
    enableAnonymous: 0,
    enableVisitor: 0,
    visitorLoginStrategy: 0,
    enableAnswerTime: 0,
    usedTime: 0,
    answerTime: 0,
    enableBreakpoint: 0,
    mobileSetting: 0,
    hideQuesSort: 1,
    templateInfo: {
      templateId: '',
      templateTitle: '',
      templateDesc: '',
      templateEndMsg: '',
      questions: []
    }
  },
  ticket: '',
  pageIndex: 0, // 当前答题的页码
  quesIndex: 0, // 当前答题的题号
  quesId: '', // 当前答题的id
  quesAnswers: {}, // 当前页面的作答答案
  quesSubmiting: {}, // 当前页面提交中的题目
  quesEndSubmiting: {}, // 最终需要全量提交的试题
  quesPages: [],
  quesNum: 0,
  submitFun: null // 提交调查的方法。由于异步提交完成试题后提交问卷
};
export default {
  mixins: [dataHandle],
  provide() {
    return {
      getTicket: () => this.ticket
    };
  },
  props: {
    isSuccessPage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const routeParams = this.deepStudy ? this.queryDatas : this.$route.query;
    return {
      ...INIT_DATA,
      loading: true, // 加载中
      submitedTip: false,
      showResult: routeParams.showResult === '1' || routeParams.showResult === 1,
      loadedO2O: false,
      surveyId: routeParams.id, // 调查项目的项目的ID,对应projectId
      uamId: routeParams.uamId, // 用户调查项目Id
      uaId: routeParams.uaId || '',
      objType: routeParams.type || 1, // 调查来源类型
      targetId: routeParams.targetId || '',
      batchId: routeParams.batchId || '',
      continueUrl: decodeURIComponent(routeParams.successUrl || '') // 做完后的跳转地址
    };
  },
  computed: {
    // 页面计时的缓存key,存储没提交后端的时间
    timeStorageKey() {
      return `survey_dotime_${this.surveyId}_${this.uaId}`;
    },
    // 页面答案的缓存key,存储没提交后端的答案
    answerStorageKey() {
      return `survey_answer_${this.surveyId}_${this.uaId}`;
    },
    // 答题次数限制缓存key
    answerNumStorageKey() {
      return `survey_answernum_${this.surveyId}_${this.uaId}`;
    },
    // 是否单页一题
    isSinglePage() {
      return this.survey.mobileSetting === 0;
    },
    isRichTitle() {
      return replaceHtmlTags(this.survey.templateInfo.templateTitle) !== filterXss(this.survey.templateInfo.templateTitle);
    },
    // 是否培训下的批量作答模式。此模式下需要实现切换不同问卷的功能。需要加载o2o的列表组件
    isO2oSurveyList() {
      return !!this.routeParams.mergeEvalProjectId;
    },
    showQuesNum() {
      return this.survey && !this.survey.hideQuesSort;
    }
  },
  methods: {
    ...mapActions(['setTicket']),
    /**
       * 用户开始调查问卷
       * @param {Function} cb 获取信息后的回调
       */
    startSurvey(cb) {
      if (!this.surveyId || !this.uamId) {
        this.$emit('errorPublic', 'pc_survey_lbl_systemError', this.$t('pc_survey_lbl_systemError'));
        return;
      }

      this.startSurveyCB = cb;
      startUserSurvey(this.surveyId, this.uamId, 0, this.targetId, this.batchId, this.routeParams.sourceParam).then((res) => {
        if (res) {
          this.initSurveyInfo(res.data);
          this.initAnswerOfLocalStorage();
          this.setLimitTimer(this.survey.usedTime, this.survey.answerTime);
          cb && typeof (cb) === 'function' && cb(this.survey);

          this.loading = false;
          this.loadingInner = false;
          !this.deepStudy && setTitle(this.survey.templateInfo.templateTitleText);

          this.checkInternet();
        }
        this.$emit('updateProgress', 1);
      }).catch((err) => {
        // 异常处理
        this.goErrorPage(err);
      });
    },
    /**
       * 查看作答结果
       */
    loadAnswerResult() {
      this.onePage = true;
      this.viewResult = true;
      surveyAnswerDetail(this.uamId, this.uaId, this.manager, this.batchId, this.targetId).then((res) => {
        this.loading = false;
        this.loadingInner = false;
        this.initSurveyInfo(res.data);
      }).catch(this.goErrorPage);
    },
    /**
       * 初始化页面的数据
       * @param {object} data 调查信息
       */
    initSurveyInfo(data) {
      this.survey = data;
      this.uaId = this.survey.uaId;
      this.initQuesPages();
      this.setTicket(data.ticket);
      this.ticket = data.ticket;
    },
    /**
       * 初始化试题分页
       */
    initQuesPages() {
      const pages = [];
      let pi = 0;
      let qi = 0;
      this.survey.templateInfo.questions.forEach(ques => {
        // 数据转化
        const question = this.interfaceDataToViewData(ques, this.showQuesNum);

        if (!pages[pi]) {
          pages[pi] = [];
        }

        // 普通试题，插入当前页
        if (question.quesType < SurveyQuesType.page) {
          question.quesNum = ++qi; // 题号，跳过的题目题号也占用
          pages[pi].push(question);
          this.quesNum++;
        }

        // 分页说明，分割线。无须作答制作呈现使用
        if (question.quesType > SurveyQuesType.page && !this.isSinglePage) {
          pages[pi].push(question);
        }
        // 分页或者H5的每页一题, 答题结果查看只需要一页
        if (!this.onePage && (question.quesType === SurveyQuesType.page || this.isSinglePage)) {
          pi++;
        }
      });
      // 一题一页的情况下，去除空数据页(包括分页、分页说明等)
      for (let i = 0; i < pages.length; i++) {
        if (!pages[i].length) {
          pages.splice(i, 1);
          i--;
        }
      }
      this.quesPages = pages;
    },
    /**
       * 定时刷新投票排名
       */
    refreshVoteRank() {
      if (!this.survey.allowViewVoteRank) {
        return;
      }
      this.rankSTV = setInterval(()=> {
        this.loadAnswerResult();
      }, 2 * 60 * 1000); // 2分钟刷新一次统计
    },
    // 是否可以跳转上一页/上一题
    canPrevPage() {
      return this.quesPages.filter((p, index) => {
        return index < this.pageIndex && this.checkPageEnable(index);
      }).length > 0;
    },
    // 是否可以跳转下一页/下一题
    canNextPage() {
      return this.quesPages.filter((p, index) => {
        return index > this.pageIndex && this.checkPageEnable(index);
      }).length > 0;
    },
    /**
       * 上一页、下一页
       * 上一题、下一题（H5每页一题模式就是一页放一道题目）
       * @param {string} isPrev 上一页
       */
    goPage(isPrev) {
      for (
        let index = this.pageIndex + (isPrev ? -1 : 1);
        isPrev ? (index >= 0) : (index <= this.quesPages.length);
        isPrev ? index-- : index++
      ) {
        if (this.checkPageEnable(index)) {
          this.pageIndex = index;
          break;
        }
      }
      this.checkSubmit = false;
      this.pageKey = Date.now();
    },
    /**
       * 已经作答的题目数量
       */
    answeredQuesNum() {
      let num = 0;
      this.quesPages.forEach(page => {
        num += page.filter((q) => {
          return this.checkQuesIsNeedWrite(q) && (this.checkQuesJumped(q) || this.checkQuesIsAnswered(q));
        }).length;
      });

      return num;
    },
    // 第一个异常试题
    getfirstErrorQues() {
      for (let index = 0; index < this.quesPages[this.pageIndex].length; index++) {
        const q = this.quesPages[this.pageIndex][index];

        if (this.checkQuesEnable(q) && (!this.checkQuesIsMustAndAnswered(q) || (!this.checkQuesJumped(q) && q.errorMessage))) {
          return q;
        }
      }
      return null;
    },
    /**
       * 检查当前页是否有异常作答题目
       * @returns 是否有异常 true false无异常
       */
    checkCurrentPageHasAbnormal() {
      this.checkSubmit = true;
      let has = false;
      this.quesPages.forEach((page, index) => {
        if (has || index > this.pageIndex) {
          return;
        }
        const hasNow = page.filter((q) => !this.checkQuesIsMustAndAnswered(q) || (!this.checkQuesJumped(q) && q.errorMessage)).length > 0;
        if (hasNow && !has) {
          this.pageIndex = index;
          has = hasNow;
        }
      });
      return has;
    },
    /**
       * 检查试题是否已经作答
       * @param {object} q 试题
       * @returns 是否已作答
       */
    checkQuesIsAnswered(q) {
      return q.answered;
    },
    /**
       * 检查试题是否必答且已经作答 或者非必达 或者跳过了
       * @param {object} q 试题
       * @returns 是否已作答
       */
    checkQuesIsMustAndAnswered(q) {
      return (q.mustAnswer && this.checkQuesEnable(q) && this.checkQuesIsAnswered(q)) || !q.mustAnswer || !this.checkQuesEnable(q);
    },
    /**
       * 试题是否是需要做的类别
       * @param {object} q 试题
       * @returns 是需要做的类别
       */
    checkQuesIsNeedWrite(q) {
      return q.quesType < SurveyQuesType.page;
    },
    /**
       * 试题是否可用（跳过、或者分页类不可用
       * @param {object} q 试题
       * @returns 是否可用
       */
    checkQuesEnable(q) {
      return this.checkQuesIsNeedWrite(q) && !this.checkQuesJumped(q);
    },
    /**
       * 试题是否跳过
       * @param {object} q 试题
       * @returns 是否可用
       */
    checkQuesJumped(q) {
      return q.jumpedIds && q.jumpedIds.length > 0;
    },
    /**
       * 所有跳过的试题ID
       */
    getJumpedQuesIds() {
      const ids = [];
      this.quesPages.forEach(page => {
        page.forEach(ques => {
          if (this.checkQuesJumped(ques)) {
            ids.push(ques.questionId);
          }
        });
      });
      return ids;
    },
    /**
       * 检查某页试题是否可用（全跳过的时候不可用）
       * @returns boolean
       */
    checkPageEnable(index) {
      const quesList = this.quesPages[index] || [];
      return quesList.filter((q) => this.checkQuesEnable(q)).length > 0;
    },
    /**
        * 跳转试题，不会真的做试题跳转，只是将要跳转的试题置于下一题，其他试题隐藏
        * @param {string} quesId 当前作答的试题ID
        * @param {string} jumpQuesId 跳转的试题ID, -1时跳至结尾, '' 清除所有
        * @param {Array} hideQuesId 追加隐藏的试题ID数组
        */
    jumpQues(quesId, jumpQuesId, hideQuesId, pId) {
      let jumpStart = false;
      let isfindJump = false;
      // 重新判断试题是否跳过
      this.quesPages.forEach(page => {
        page.forEach((ques) => {
          // 非作答类试题或者跳转逻辑处理完了
          if (!ques.questionId) {
            return;
          }

          // 找到当前试题，开始处理
          if (quesId === ques.questionId && !this.checkQuesJumped(ques)) {
            jumpStart = true;
            return;
          }

          // 找到目标试题，增加跳过逻辑结束。接下来恢复多余隐藏，取消跳过
          if (jumpStart && ques.questionId === jumpQuesId) {
            jumpQuesId = '';
            isfindJump = true; // 表示找到了目标试题
          }

          // 处理逻辑
          if (jumpStart) {
            ques.jumpedIds = ques.jumpedIds || [];

            if (jumpQuesId || (hideQuesId && hideQuesId.includes(ques.questionId))) {
              // 增加跳过，这里采用数组来应对一道题目被多道题目跳过的逻辑
              if (ques.questionId !== jumpQuesId && ques.jumpedIds.indexOf(quesId) < 0) {
                ques.jumpedIds.push(quesId);

                // 把跳过的题目的跳转逻辑清除
                this.jumpQues(ques.questionId, '', '', pId + '-' + ques.quesNum);
              }
            } else if (ques.jumpedIds.indexOf(quesId) >= 0) {
              // 取消跳过，只取消当前试题产生的跳过
              ques.jumpedIds = ques.jumpedIds.filter((id) => id !== quesId);
            }
          }
        });
      });

      // 若需要跳转的试题未找到，则无效处理，做恢复。 -1为结尾跳转。
      if (!isfindJump && jumpQuesId !== -1) {
        this.quesPages.forEach(page => {
          page.forEach((ques) => {
            ques.jumpedIds = ques.jumpedIds ? ques.jumpedIds.filter((id) => id !== quesId) : [];
          });
        });
      }

      !pId && this.$forceUpdate();
    },
    /**
        * 用户单题作答
        * @param {Boolean} ques 试题
        * @param {Boolean} answer 作答答案
        * @param {Function} cb 获取信息后的回调
        */
    doQuestion(ques, answer, cb) {
      this.quesAnswers[ques.questionId] = answer;
      this.quesSubmiting[ques.questionId] = true;
      // 暂时只有断点续答的模式
      this.survey.enableBreakpoint = true;
      if (this.survey.enableBreakpoint) {
        // 断点作答时提交单题至服务器
        this.submitQuestion(ques, answer, cb);
      } else {
        // 非断点续答是否存入本地
        this.saveQuestion(ques, answer, cb);
      }
    },
    /**
        * 记录单题作答,放本地缓存
        * @param {Boolean} ques 试题信息
        * @param {Boolean} answer 作答答案
        * @param {Function} cb 获取信息后的回调
        */
    saveQuestion(ques, answer, cb) {
      // todo 本地缓存记录试题？
      this.getOrUpdateAnswerLocalstorage(ques.questionId, answer);
      cb && typeof (cb) === 'function' && cb();
    },
    /**
        * 提交单题作答
        * @param {Boolean} ques 试题信息
        * @param {Boolean} answer 作答答案
        * @param {Function} cb 获取信息后的回调
        */
    submitQuestion(ques, answer, cb) {
      debounceByKey(ques.questionId, () => {
        // 做过了，且上一次请求还在提交中，可能会产生并发乱序的问题，最终交卷需要提交一下
        this.quesEndSubmiting[ques.questionId] = typeof (this.quesSubmiting[ques.questionId]) === 'number';

        const quesSubmitingIndex = new Date().getTime();
        this.quesSubmiting[ques.questionId] = quesSubmitingIndex;
        // 做防抖处理
        submitUserQuestion(this.uamId, answer, this.targetId, this.batchId).then((res) => {
          // 修复弱网下最后一题多次提交后直接提交答卷会丢失答案的问题
          if (this.quesSubmiting[ques.questionId] === quesSubmitingIndex) {
            this.quesSubmiting[ques.questionId] = false;
          }

          this.saveQuestion(ques.questionId, undefined);
          this.checkQuestionsAllSubmited();
          cb && typeof (cb) === 'function' && cb(res);
        }).catch((err) => {
          // 记录提交失败的，最终提交问卷需要补充提交
          this.quesEndSubmiting[ques.questionId] = true;

          if (this.submitingFinal) {
            return;
          }
          this.$handleError(err);

          this.submiting = false;
          // 存储失败，先放本地
          this.saveQuestion(ques.questionId, answer);
          // 出异常也应该清理提交中状态
          if (this.quesSubmiting[ques.questionId] === quesSubmitingIndex) {
            this.quesSubmiting[ques.questionId] = false;
          }
        });
      }, SURVEY_TIME_SUBMIT_QUES_TIMEOUT);
    },
    /**
       * 用户提交调查问卷,需要处理待提交的单题
       * @param {Function} cb 获取信息后的回调
       */
    submitSurvey(cb) {
      if (this.submiting) return;
      this.submiting = true;
      this.checkSubmit = true;

      // 最终提交的方法
      this.submitFun = () => {
        if (this.submitingFinal) return;
        this.submitingFinal = true;

        // 必须等页面上的单题提交都做完，不然可能存在遗漏、乱序等问题
        this.submitFinally(cb);
      };
      // 提交所有的题目，提交完成后再提交调查
      this.submitAllQuestions();
    },

    /**
     * 最终提交调查问卷，提交可能出问题的题目，然后提交答卷
     * @param {*} cb
     */
    async submitFinally(cb) {
      try {
        const quesNeedSubmit = Object.values(this.quesAnswers).filter(q => this.quesEndSubmiting[q.questionId]);

        const jumpedIds = this.getJumpedQuesIds();
        const data = {
          targetId: this.targetId,
          batchId: this.batchId,
          usedTime: this.timeNeedSumit,
          jumpedIds: jumpedIds,
          batchSubmit: quesNeedSubmit // 补充提交可能会丢失的题目
        };

        const res = await submitUserSurvey(this.uamId, data);
        // 清计时器
        this.timerSTV && window.clearInterval(this.timerSTV);

        cb && typeof (cb) === 'function' && cb(res);
        this.goSuccessPage();
      } catch (err) {
        this.submiting = false;
        this.submitingFinal = false;
        this.submitFun = null;
        // 异常处理
        if (err && err.key === 'apis.survey.ua.vote.item.unavailable') {
          // 投票题数量占尽
          this.refrenshSurvey(() => {
            cb && typeof (cb) === 'function' && cb();
          });
        } else {
          this.$handleError(err);
        }
      }
    },
    /**
       * 提交当前调查的所有待提交的题目
       */
    submitAllQuestions() {
      if (this.checkQuestionsAllSubmited()) {
        return;
      }

      // 补提交
      if (this.survey.enableBreakpoint) {
        for (const key in this.quesAnswers) {
          callDebouncedFunByKey(key);
        }
      } else {
        // 非断点续答
        for (const key in this.quesAnswers) {
          this.submitQuestion({
            questionId: key
          }, this.quesAnswers[key]);
          callDebouncedFunByKey(key);
        }
      }
    },
    /**
       * 检查当前页面试题是否已经全部提交
       * @returns
       */
    checkQuestionsAllSubmited() {
      for (const id in this.quesSubmiting) {
        if (this.quesSubmiting[id]) {
          return false;
        }
      }

      this.submitFun && this.submitFun();
      return true;
    },
    /**
       * 限时计时器
       * @param {Number} usedTime 已使用时间
       */
    setLimitTimer(usedTime, limitTime) {
      // 限制作答时间、开启倒计时
      if (!this.survey.enableAnswerTime) {
        return;
      }

      const oldTime = parseInt(window.getLocalStorage(this.timeStorageKey) || 0);
      this.lastTime = limitTime - usedTime - oldTime;
      this.timeNeedSumit = oldTime;

      this.timerSTV = setInterval(() => {
        this.timeNeedSumit++;
        this.lastTime--;
        if (limitTime && this.lastTime <= 0) {
          // 强制结束
          this.operationDisabled = true;
          this.updateTimeLocalstorage(0);
          clearInterval(this.timerSTV);

          this.endSurvey(this.timeNeedSumit, () => {
            this.warningTimeOut && this.warningTimeOut();
          });
        } else {
          // 定时提交
          if (this.timeNeedSumit >= SURVEY_TIME_SUBMIT_INTERVAL) {
            this.submitSurveyTime();
            this.timeNeedSumit -= SURVEY_TIME_SUBMIT_INTERVAL;
          }
          // 定时存储未提交的时间
          this.updateTimeLocalstorage(this.timeNeedSumit);
        }
      }, 1000);
    },
    /**
       * 提交学时
       */
    submitSurveyTime(t, cb) {
      submitUserSurveyTime(this.uaId, {
        usedTime: t || SURVEY_TIME_SUBMIT_INTERVAL,
        batchId: this.batchId
      }).then(() => {
        cb && typeof (cb) === 'function' && cb();
      }).catch((err) => {
        this.$handleError(err);
      });
    },
    /**
       * 结束调查
       */
    endSurvey(t, cb) {
      const quesNeedSubmit = Object.values(this.quesAnswers).filter(q => this.quesEndSubmiting[q.questionId]);

      endUserSurvey(this.uamId, this.uaId, {
        usedTime: t || SURVEY_TIME_SUBMIT_INTERVAL,
        batchId: this.batchId,
        batchSubmit: quesNeedSubmit // 补充提交可能会丢失的题目
      }).then(() => {
        cb && typeof (cb) === 'function' && cb();
      }).catch((err) => {
        this.$handleError(err);
      });
    },
    /**
       * 刷新页面数据
       */
    refrenshSurvey(cb) {
      this.checkSubmit = false;
      startUserSurvey(this.surveyId, this.uamId, 0).then((res) => {
        const newQueses = res.data.templateInfo.questions;
        this.quesPages.forEach(page => {
          page.forEach(ques => {
            const newQues = newQueses.find((q) => q.questionId === ques.questionId);
            if (newQues) {
              const quesViewData = this.interfaceDataToViewData(newQues, this.showQuesNum);

              Object.assign(ques, quesViewData);
            }
          });
        });
        this.$forceUpdate();
        cb && typeof (cb) === 'function' && cb();
      }).catch((err) => {
        // 异常处理
        this.goErrorPage(err);
      });
    },
    /**
       * 切换别的调查问卷。下一份的情况
       * @param {*} 参数同/survey/fbi.html页面
       * @param {*} newQuery 培训的新的参数，主要用于修改当前的路由地址，保证手动刷新页面还能定位到最新的
       */
    goNextSurvey({ id, type, tid, tname, batchId}, newQuery) {
      if (this.$refs.o2oList) {
        // 通过组件的刷新方法来实现
        this.$refs.o2oList.getData(newQuery.mergeEvalTaskId);
      } else {
        this.changeSurvey({ id, type, tid, tname, batchId }, newQuery);
      }
    },
    /**
       * 切换为当前调查问卷。主要是刷新状态
       * */
    goCurrentSurvey() {
      if (this.isO2oSurveyList && this.$refs.o2oList && this.showResult) {
        // 通过组件的刷新方法来实现
        this.$refs.o2oList.getData(this.routeParams.mergeEvalTaskId);
      } else {
        this.goDetailPage();
      }
    },
    /**
       * 切换别的调查问卷。
       * @param {*} 参数同/survey/fbi.html页面
       * @param {*} newQuery 培训的新的参数，主要用于修改当前的路由地址，保证手动刷新页面还能定位到最新的
       * @param {*} loadResult 做过了，加载结果
       */
    changeSurvey({ id, type, tid, tname, batchId }, newQuery, loadResult) {
      this.clearPageItv();
      // 提交时间
      if (this.uaId && this.timeNeedSumit) {
        this.submitSurveyTime(this.timeNeedSumit);
      }

      this.loadingInner = true;
      // 试题提交完成后切换新调查 this.submitFun =
      const innerSubmitFun = () => {
        const postData = {
          objId: id,
          objType: type,
          targetId: tid,
          targetName: tname,
          batchId: batchId
        };
        surveyOuterPreviewParams(postData).then((res) => {
          // 刷新参数，保证和页面显示的调查对应上
          if (!this.deepStudy) {
            this.$emit('changeStep', this.UserSurveyStep.answer, {
              ...this.routeParams,
              id: res.data.projectId,
              uamId: res.data.uamId,
              uaId: res.data.uaId || '',
              targetId: tid,
              batchId: batchId,
              ...newQuery
            }, true);
          } else {
            Object.assign(this.routeParams, newQuery);
          }

          // 调整为新调查的参数
          Object.assign(this, Object.assign(INIT_DATA, {
            surveyId: res.data.projectId, // 调查项目的项目的ID,对应projectId
            uamId: res.data.uamId, // 用户调查项目Id
            uaId: res.data.uaId || '',
            targetId: tid || '',
            batchId: batchId || ''
          }));

          // 开始调查
          if (loadResult) {
            this.goDetailPage();
          } else {
            this.startSurvey(this.startSurveyCB);
          }
          this.loadedO2O = true;
        }).catch(this.$handleError);
      };
      // 提交所有的题目，提交完成后再切换
      this.submitAllQuestions();

      // 这里不等上面的单题提交，不然可能会阻断页面跳转
      innerSubmitFun();
    },
    // 成功跳转
    goSuccessPage() {
      // 作答完成
      this.$emit('updateProgress', 2);
      // 自定义结束处理
      if (this.routeParams.customSuccess) {
        return;
      }
      if (this.isSuccessPage) {
        this.$emit('goSuccessPage', this.survey);
        return;
      }
      if (this.isO2oSurveyList) {
        this.o2oSubmitedTip = true;
      } else if (this.continueUrl && this.checkUrlSafe(this.continueUrl)) {
        // 业务自定义的地址跳转
        window.location.href = this.continueUrl;
      } else if (!this.survey.allowViewVoteRank) {
        // 沉浸式后，改为当前页面提示
        this.submitedTip = true;
      } else {
        this.goDetailPage();
      }
    },
    // 结果跳转
    goDetailPage(showResult) {
      if (this.isO2oSurveyList && this.$refs.o2oList && (this.showResult || showResult)) {
        this.loadAnswerResult();
      } else {
        // 调查项目默认跳转
        this.$emit('changeStep', this.UserSurveyStep.result, {
          ...this.routeParams,
          id: this.surveyId,
          uamId: this.uamId,
          uaId: this.uaId,
          batchId: this.batchId || '',
          targetId: this.targetId || ''
        }, true);
      }
    },
    // 跳转错误页面
    goErrorPage(errorData) {
      if (errorData && errorData.error) {
        errorData = errorData.error;
      }

      // 超过做答次数的上限，是否报错/跳结果？
      if (errorData && errorData.key === 'apis.survey.uam.valid.remain_num.error' && this.routeParams.allowResult) {
        this.goDetailPage();
        return;
      }

      this.$emit('errorPublic', errorData && errorData.code, errorData && errorData.message);
    },
    /**
       * 校验地址的安全性,防止跳转不正常的链接
       * @param {string} cburl 做完调查后的跳转地址
       * @returns 安全否
       */
    checkUrlSafe(cburl) {
      // TODO 校验地址的安全性，优化点
      return true;
    },
    /**
       * 本地计时缓存，会存储未提交的时间
       * @param {number} newVal 未提交的时间
       */
    updateTimeLocalstorage(newVal) {
      if (!newVal) {
        window.removeLocalStorage(this.timeStorageKey);
      } else {
        window.setLocalStorage(this.timeStorageKey, newVal);
      }
    },
    /**
       * 初始化本地作答缓存
       */
    initAnswerOfLocalStorage() {
      this.quesAnswers = this.getOrUpdateAnswerLocalstorage();

      for (const key in this.quesAnswers) {
        this.quesSubmiting[key] = true;
      }
    },
    /**
       * 本地作答缓存
       * @param {quesId} quesId 题目Id
       * @param {object} newVal 答案
       */
    getOrUpdateAnswerLocalstorage(quesId, newVal) {
      const answersLocal = JSON.parse(window.getLocalStorage(this.answerStorageKey) || '{}');
      if (quesId) {
        answersLocal[quesId] = newVal;
        if (Object.values(answersLocal).filter((v) => v !== undefined).length === 0) {
          window.removeLocalStorage(this.answerStorageKey);
        } else {
          window.setLocalStorage(this.answerStorageKey, JSON.stringify(answersLocal));
        }
      }
      return answersLocal;
    },
    /**
       * 本地作答次数缓存（通过临时token后端校验了,前端无需校验）
       * @param {quesId} quesId 题目Id
       * @param {object} newVal 答案
       */
    getOrUpdateAnswerNumLocalstorage(num) {
      const answerNum = parseInt(window.getLocalStorage(this.answerNumStorageKey) || 0);
      if (num) {
        window.setLocalStorage(this.answerNumStorageKey, JSON.stringify(num));
      }
      return answerNum;
    },
    /**
     * 清空定时器
     */
    clearPageItv() {
      this.timerSTV && window.clearInterval(this.timerSTV);
      this.rankSTV && window.clearInterval(this.rankSTV);
      this.checkInternetSTV && window.clearInterval(this.checkInternetSTV);
    },
    /**
     * 检查网络，如果长时间提交未完成，给予用户提示
     */
    checkInternet() {
      this.checkInternetSTV = setInterval(() => {
        const currentTime = Date.now();

        for (const key in this.quesSubmiting) {
          const value = this.quesSubmiting[key];
          // 检查值是否为时间戳类型
          if (typeof value === 'number' && value > 10000000) {
            if ((currentTime - value) > 5000) { // 10秒 = 10000毫秒
              // 当前网络不佳，请检查网络连接或刷新页面后，重新提交
              this.$handleError({
                message: this.$t('pc_survey_msg_networkpending')
              });

              break;
            }
          }
        }
      }, 2000);
    }
  },
  beforeDestroy() {
    this.clearPageItv();

    this.updateTimeLocalstorage(this.timeNeedSumit);
  }
};
