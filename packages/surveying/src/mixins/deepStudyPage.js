import { Message } from 'yxt-pc';
import { UserSurveyStep } from '../core/enum';
import { isNullOrUndefined } from '../core/utils';

export default {
  props: {
    // 页面参数
    queryDatas: {
      type: Object,
      required: true
    },
    // 是否沉浸式
    deepStudy: {
      type: Boolean,
      default: true
    },
    // 直接撑满容器，去除内部自带的边距
    fullPage: {
      type: Boolean,
      default: undefined
    }
  },
  data() {
    return {
      UserSurveyStep
    };
  },
  computed: {
    // 需要根据沉浸式还是正常页面跳转，以不同对应方式获取页面的参数
    routeParams() {
      return this.deepStudy ? this.queryDatas : this.$route.query;
    },
    isFullPage() {
      return this.deepStudy && ((this.fullPage === undefined && this.routeParams.hideBack) || this.fullPage);
    }
  },
  created() {
  },
  methods: {
    // 切换页面
    changeStep(step, params, replace, newPage) {
      this.$emit('changeStep', step, params, replace, newPage);
    },
    // 更新对外任务状态用
    updateProgress(type) {
      this.$emit('updateProgress', type);
    },

    // 滚动到指定位置
    scrollToY(scroller, top, smooth = true) {
      if (scroller) {
        if (scroller.style) scroller.style.scrollBehavior = smooth ? 'smooth' : '';
        if (scroller === window && window.scrollTo) {
          window.scrollTo({
            top: top,
            behavior: smooth ? 'smooth' : 'auto'
          });
        } else {
          scroller.scrollTop = top;
        }
      }
    },

    // 处理普通接口报错
    $handleError(error) {
      if (error && error.error) {
        // 兼容处理
        error = error.error;
      }
      if (error) {
        if (error.key && error.key.indexOf('global.token') >= 0) {
          return; // 不弹出token错误
        }
        const msg = error.message;
        if (typeof (msg) !== 'string') return;
        Message({
          message: msg,
          type: 'error'
        });
      }
    },

    // 正常页面报错逻辑
    handlerPublicError(err, isRun = true, customFn, customIf) {
      if (this.coursePractice) {
        this.$emit('error', err);
        return;
      }

      if (isRun) {
        this.$emit('errorPublic', err, customFn, customIf);
      } else {
        this.$handleError(err);
      }
    },

    $isNullOrUndefined: isNullOrUndefined
  }
};
