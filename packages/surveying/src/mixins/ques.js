/**
 * 题目
 */
import { EnumFileType, QuesMode, SurveyLogicType, SurveyQuesType, UploadStatus } from '../core/enum';
import { isRichText } from '../core/utils';
import { setQuesAnswerStatus, getVoteLimitNum } from './commonQues';
export default {
  props: {
    index: {},
    mode: {
      default: QuesMode.edit
    },
    quesData: {
      type: Object,
      required: true,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    templateType: {
      // 模板类型  SurveyTemplateType
      type: Number,
      default: 1
    },
    rankMode: { // 允许参与者查看排行，针对投票题类型
      type: [Boolean, Number],
      default: false
    },
    emitInitAnswer: { // 初始化的答案是否触发做答
      type: Boolean,
      default: false
    }
  },
  computed: {
    voteRankMode() {
      return this.rankMode && [
        SurveyQuesType.singleChoose,
        SurveyQuesType.multiChoose,
        SurveyQuesType.judge,
        SurveyQuesType.textChoose,
        SurveyQuesType.multiTextChoose,
        SurveyQuesType.picChoose,
        SurveyQuesType.multiPicChoose,
        SurveyQuesType.videoChoose,
        SurveyQuesType.multiVideoChoose
      ].includes(this.quesData.quesType);
    },
    /**
    * 获取多选题的选项限制的提示文案
    */
    multiSelectCountTip() {
      if (![
        SurveyQuesType.multiChoose,
        SurveyQuesType.multiTextChoose,
        SurveyQuesType.multiPicChoose,
        SurveyQuesType.multiVideoChoose
      ].includes(this.quesData.quesType)) {
        return '';
      }

      let tip = '';
      if (this.quesData.minChoose && !this.quesData.maxChoose) {
        tip = this.$t('pc_survey_msg_selectoptionsmin', [this.quesData.minChoose]);
      } else if (this.quesData.maxChoose && !this.quesData.minChoose) {
        tip = this.$t('pc_survey_msg_selectoptionsmax', [this.quesData.maxChoose]);
      } else if (this.quesData.maxChoose && this.quesData.minChoose) {
        tip = this.$t(this.quesData.maxChoose === this.quesData.minChoose ? 'pc_survey_msg_selectoptions' : 'pc_survey_msg_selectoptionsrange', [this.quesData.minChoose, this.quesData.maxChoose]);
      }

      return tip;
    },
    /**
     * 是否显示题型
     */
    showQuesType() {
      return this.quesData.showQuesType;
    }
  },
  mounted() {
    // 首次全部处理完成后，如果没错误就触发一次做答
    setTimeout(() => {
      if (this.emitInitAnswer && this.quesData.answered && !this.quesData.errorMessage) {
        this.emitAnswer();
      }
    });
  },
  methods: {
    isRichText,
    /**
     * 实时触发保存
     */
    emitAnswer() {
      this.setAnswerStatus(this.quesData);
      this.emitLogic();
      let params = {};
      const quesType = this.quesData.quesType;
      if (quesType === SurveyQuesType.singleChoose || quesType === SurveyQuesType.multiChoose) {
        if (quesType === SurveyQuesType.multiChoose) {
          params.multiSelectQuestionAnswer = { answerDetail: [] };
        } else {
          params.selectQuestionAnswer = { answerDetail: {} };
        }
        for (let i = 0; i < this.quesData.itemGroups.length; i++) {
          for (let j = 0; j < this.quesData.itemGroups[i].itemList.length; j++) {
            const item = this.quesData.itemGroups[i].itemList[j];
            if (item.userAnswerFlag && quesType === SurveyQuesType.multiChoose) {
              params.multiSelectQuestionAnswer.answerDetail.push({ itemId: item.itemId, answerExplain: item.answerExplain });
            } else if (item.userAnswerFlag) {
              params.selectQuestionAnswer.answerDetail = {
                itemId: item.itemId,
                answerExplain: item.answerExplain
              };
              break;
            }
          }
        }
      } else if (quesType === SurveyQuesType.judge) {
        params = { judgeSelectQuestionAnswer: {} };
        for (let i = 0; i < this.quesData.itemList.length; i++) {
          const item = this.quesData.itemList[i];
          if (item.userAnswerFlag) {
            params.judgeSelectQuestionAnswer.answerDetail = {
              itemId: item.itemId,
              answerExplain: item.answerExplain
            };
          }
        }
      } else if (quesType === SurveyQuesType.classify) {
        params = { classifyQuestionAnswer: { answerDetail: {} } };
        this.quesData.classifyList.forEach(item => {
          if (item._relatedOptionIds) {
            params.classifyQuestionAnswer.answerDetail[item.itemId] = item._relatedOptionIds;
          }
        });
      } else if (quesType === SurveyQuesType.fill) {
        params = { fillQuestionAnswer: { answerDetail: { } } };
        params.fillQuestionAnswer.answerDetail.userAnswer = this.quesData.fillList[0].userAnswer;
      } else if (quesType === SurveyQuesType.multiFill) {
        params = { multiFillQuestionAnswer: { answerDetail: [] } };
        params.multiFillQuestionAnswer.answerDetail = this.quesData.fillList.map(item => ({ userAnswer: item.userAnswer }));
      } else if (quesType === SurveyQuesType.essay) {
        params = { shortQuestionAnswer: { answerDetail: { userAnswer: this.quesData.userAnswer } } };
      } else if (quesType === SurveyQuesType.scale || quesType === SurveyQuesType.multiScale ||
        quesType === SurveyQuesType.nps || quesType === SurveyQuesType.score || quesType === SurveyQuesType.evaluation) {
        let answerDetail = null;
        if (quesType === SurveyQuesType.multiScale) {
          answerDetail = [];
          this.answerGroupData.forEach(item => {
            const answer = this.itemList[item._userAnswer - 1];
            answerDetail.push({ itemId: answer ? answer.itemId : '-1', groupId: item.groupId });
          });
        } else if (quesType === SurveyQuesType.score) {
          answerDetail = [];
          this.answerGroupData.forEach(item => {
            const answer = this.itemList[item._userAnswer];
            answerDetail.push({ itemId: answer ? answer.itemId : '-1', groupId: item.groupId });
          });
        } else if (quesType === SurveyQuesType.scale || quesType === SurveyQuesType.evaluation) {
          const item = this.itemList[this.answerGroupData[0]._userAnswer - 1];
          answerDetail = { itemId: item.itemId };
        } else {
          const item = this.itemList[this.answerGroupData[0]._userAnswer];
          answerDetail = { itemId: item.itemId };
        }
        if (this.quesData.quesType === SurveyQuesType.scale) {
          params = { scaleQuestionAnswer: { answerDetail } };
        } else if (this.quesData.quesType === SurveyQuesType.multiScale) {
          params = { multiScaleQuestionAnswer: { answerDetail } };
        } else if (this.quesData.quesType === SurveyQuesType.nps) {
          params = { npsScaleQuestionAnswer: { answerDetail } };
        } else if (this.quesData.quesType === SurveyQuesType.score) {
          params = { scoreQuestionAnswer: { answerDetail } };
        } else if (this.quesData.quesType === SurveyQuesType.evaluation) {
          params = { feedbackQuestionAnswer: { answerDetail } };
        }
      } else if (quesType === SurveyQuesType.sort) {
        // this.answerDetail在组件内部
        params = { orderQuestionAnswer: { answerDetail: this.answerDetail } };
      } else if (quesType >= SurveyQuesType.name && quesType <= SurveyQuesType.number) {
        params = { baseInfoQuestionAnswer: {
          answerDetail: this.quesData.answer,
          pcaId: this.quesData.pcaId,
          pcaName: this.quesData.pcaName
        } };
      } else if (quesType === SurveyQuesType.upload) {
        const fileList = this.quesData.userAnswer.filter(item => item._status !== UploadStatus.going && item._status !== UploadStatus.fail);
        const uploadFile = fileList.map(file => ({
          fileId: file.fileId,
          fileName: file.fileName,
          fileSize: file.fileSize,
          fileDataType: file.fileType === EnumFileType.image ? 1 : 0,
          fileUrl: file.fileUrl
        }));
        params = { uploadQuestionAnswer: { answerDetail: { uploadFile } } };
      } else if (quesType === SurveyQuesType.textChoose || quesType === SurveyQuesType.multiTextChoose ||
        quesType === SurveyQuesType.picChoose || quesType === SurveyQuesType.multiPicChoose ||
        quesType === SurveyQuesType.videoChoose || quesType === SurveyQuesType.multiVideoChoose) {
        params = { voteQuestionAnswer: { answerDetail: [] } };
        this.quesData.itemList.forEach(item => {
          if (item.userAnswerFlag === 1) {
            params.voteQuestionAnswer.answerDetail.push({ itemId: item.itemId, answerExplain: item.answerExplain });
          }
        });
      } else if (quesType === SurveyQuesType.slideScore) {
        params = { slideScoreQuestionAnswer: { answerDetail: [] } };
        this.quesData.itemGroups.forEach(group => {
          params.slideScoreQuestionAnswer.answerDetail.push({
            groupId: group.groupId,
            score: group.scaleValue
          });
        });
      }
      this.$emit('user-submit', params);
    },
    /**
     * 删除选项，清除对应选项的逻辑设置
     * 有逻辑设置的题型：单选 多选 判断 分类 量表 多级量表 nps
     */
    deleteItemUpdateLogic() {
      const jumpConfig = this.quesData.jumpConfig || [];
      const _ids = this._getQuesItemIds(this.quesData);
      const newJumpConfig = [];
      jumpConfig.forEach(item => {
        if (item.conditionType !== SurveyLogicType.some || item.itemIds.every(id => _ids.includes(id))) {
          newJumpConfig.push(item);
        }
      });
      this.quesData.jumpConfig = newJumpConfig;
    },
    _getQuesItemIds(ques) {
      const list = [];
      if (ques.itemGroups && ques.itemGroups.length) {
        ques.itemGroups.forEach(group => {
          (group.itemList || []).forEach(item => {
            list.push(item._id);
          });
        });
      } else if (ques.itemList && ques.itemList.length) {
        ques.itemList.forEach(item => {
          list.push(item._id);
        });
      }
      return list;
    },
    /**
     * 实时触发逻辑设置
     * 有逻辑设置的题型：单选 多选 判断 分类 量表 多级量表 nps
     */
    emitLogic() {
      const target = this._getLogicTarget(this.quesData);
      this.$emit('jump', target.toQuesId, target.hideQuesId);
    },
    /**
     * 在设置answered后面调用
     * 检查逻辑跳转
     * @returns 跳转的对应题目
     */
    _getLogicTarget(ques) {
      const endTarget = { toQuesId: -1 };
      const jumpConfig = ques.jumpConfig || [];
      for (const i in jumpConfig) {
        const item = jumpConfig[i];
        if (item.conditionType === SurveyLogicType.some) {
          const answer = this._getAnsweredQuesItemList(ques);
          if (item.itemIds && item.itemIds.length && this._listContainList(answer, item.itemIds)) {
            if (item.toEnd) return endTarget;
            else if (item.toQuesId) return item;
          }
        } else if ((item.conditionType === SurveyLogicType.any && this.quesData.answered) ||
        (item.conditionType === SurveyLogicType.unanswered && !this.quesData.answered) ||
        (item.conditionType === SurveyLogicType.finish && this.quesData.answered)) {
          if (item.toEnd) return endTarget;
          else if (item.toQuesId) return item;
        }
      }
      return {};
    },
    // 获取被选择的选项itemId数组
    _getAnsweredQuesItemList(ques) {
      const answer = [];
      if (ques.itemGroups && ques.itemGroups.length) {
        ques.itemGroups.forEach(group => {
          (group.itemList || []).forEach(item => {
            if (item.userAnswerFlag) {
              answer.push(item.itemId);
            }
          });
        });
      } else if (ques.itemList && ques.itemList.length) {
        ques.itemList.forEach(item => {
          if (item.userAnswerFlag) {
            answer.push(item.itemId);
          }
        });
      }
      return answer;
    },
    _listContainList(list1 = [], list2 = []) {
      return list1.length && list1.length === list2.length && list1.every(item => list2.includes(item));
    },
    /**
     * 实时更改答题状态
     * answered true 答了，而且符合要求
     */
    setAnswerStatus(ques) {
      setQuesAnswerStatus(ques);
    },
    /**
    * 获取投票题的选项上限
    * @param {*} item
    * @returns
    */
    getLimitNum(item) {
      return getVoteLimitNum(this.quesData, item);
    }
  }
};
