import { i18n } from './common';

// 路由的模块类型
export const RouteModule = {
  front: 'front', // 学员端路由
  manage: 'manage' // 管理端路由
};

// 调查项目status定义
export const SurveyStatus = {
  unpublish: 0, // 未发布
  publishing: 1, // 发布中
  process: 2, // 进行中
  end: 3, // 结束
  archiving: 4, // 归档中
  archived: 5 // 归档
};

// 调查项目中问卷收集状态
export const SurveyCollectStatus = {
  stop: 0, // 停止
  process: 1, // 收集中
  end: 2 // 结束
};

// 调查的来源
export const SurveyOrigin = {
  independent: 0, // 独立调查
  program: 2, // 项目
  talent: 4, // 人才发展
  flip: 5 // 面授
};

// 文件
export const EnumFileType = {
  image: 0, // 图片
  file: 1, // 文档
  video: 2, // 视频
  audio: 3 // 音频
};

// 系统分类时模板分类的类型.  1:问卷管理  2:投票评选  3:报名登记  4:评价、满意度调查  5:鉴定表)
export const SurveyTemplateType = {
  questionnaire: 1, // 问卷
  vote: 2, // 评选
  sign: 3, // 报名登记
  evaluate: 4, // 评价
  detemineJudge: 5, // 鉴定表判断
  detemineSlide: 6 // 鉴定表滑动打分
};

// 创建模版的时候，题型的分组信息
export const QuesTypeGroup = {
  quesType: 301, // 题型选择
  infoSelect: 302, // 信息选择
  other: 303, // 其他

  choose: 304, // 选择
  fill: 305, // 填空
  sortScore: 306, // 排序打分
  info: 307, // 个人信息
  pageInfo: 308, // 分页说明
  textVote: 309, // 文字投票
  picVote: 310, // 图片投票
  videoVote: 311, // 视频投票
  otherQues: 312 // 其他题型
};

// 创建问卷，题型
export const SurveyQuesType = {
  singleChoose: 1, // 单选
  multiChoose: 2, // 多选
  judge: 3, // 判断
  classify: 4, // 分类
  fill: 5, // 填空
  essay: 6, // 简答
  multiFill: 7, // 多项填空
  upload: 8, // 文件上传
  scale: 9, // 量表
  multiScale: 10, // 多级量表
  sort: 11, // 排序
  nps: 12, // nps
  score: 13, // 打分
  evaluation: 14, // 评价
  textChoose: 15, // 文字单选
  multiTextChoose: 16, // 文字多选
  picChoose: 17, // 图片单选
  multiPicChoose: 18, // 图片多选
  videoChoose: 19, // 视频单选
  multiVideoChoose: 20, // 视频多选
  slideScore: 21, // 滑动打分
  name: 101, // 姓名
  sex: 102, // 性别
  phone: 103, // 手机
  email: 104, // 邮箱
  date: 105, // 日期
  city: 106, // 城市
  job: 107, // 职业
  school: 108, // 学校
  age: 109, // 年龄
  company: 110, // 公司
  department: 111, // 部门
  post: 112, // 岗位
  number: 113, // 工号
  page: 201, // 分页
  pageIntro: 202, // 分页说明
  divide: 203 // 分割线
};

// 题型组件显示的类型
export const QuesMode = {
  edit: 0, // 编辑模式
  view: 1, // 查看模式
  write: 2, // 答题模式
  managerView: 3, // 管理端查看学员调查问卷详情
  batchImportView: 4 // 管理端批量录入
};

// 题型设置的布局设置类型
export const QuesOptionLayoutCol = {
  one: 1, // 1列
  two: 2, // 2列
  three: 3, // 3列
  dropdown: 4 // 下拉模式
};

// 积分设置的类型
export const QuesScoreType = {
  every: 1, // 每个选项都有分
  correct: 2, // 单选题/多选题 答对给分 答错其他分
  part: 3, // 多选题 答对几个得几分
  sortPart: 4, // 排对几个得几分，排错不得分或扣分
  sortAll: 5, // 全部排对给分 没有全部排对不得分或扣分
  matchPart: 6, // 连对几个得几分，连错的不得分或扣分
  matchAll: 7, // 全部连对给分 没有全部连对不得分或扣分
  matchAllKeyword: 8, // 填空题/简答题所有关键字全匹配到
  matchKeyword: 9, // 填空题/简答题匹配关键词得分
  matchAnswer: 10, // 填空题/简答题匹配答案得分
  // artificial: 11, // 人工评分
  npsEvery: 13, // nps题按0-10量表值计分
  _matchAll: 999 // 答对或者 答出全部关键字得分 ； 前端合并属性，对应后台真实数据是 matchAllKeyword，matchAnswer
};

// 逻辑设置的类型
export const SurveyLogicType = {
  finish: 1, // 答完本题
  unanswered: 2, // 不答本题
  some: 3, // 一个或多个（需要管理员设置）
  any: 4 // 任意一个
};

// 填空题的格式设置
export const FillQuesFormat = {
  unlimited: 0, // 不限
  number: 1, // 数字
  letter: 2, // 字母
  chinese: 3, // 中文
  email: 4, // 邮箱
  phone: 5, // 手机
  website: 6, // 网址
  identity: 7 // 身份证
};

// nps样式类型
export const ScaleQuesStyleType = {
  star: 1, // 星星
  praise: 2, // 赞美
  heart: 3, // 爱心
  smile: 4, // 笑脸
  number: 5, // 数字
  calibration: 6 // 刻度

};

// 投票题数量限制的类型
export const VoteQuesLimitType = {
  none: 0, // 不限
  same: 1, // 每个选项一样的数量限制
  different: 2 // 每个选项不同的数量限制
};

// 文件转码状态
export const TranscodingStatus = {
  wait: 0, // 等待中
  going: 1, // 进行中
  success: 2, // 已完成
  fail: 3 // 失败
};

// 附件的上传状态
export const UploadStatus = {
  going: 1,
  success: 2,
  fail: 3
};

export const ReportRangeType = {
  all: 0, // 全部
  chart: 1, // 图标
  table: 2 // 表格
};

export const ChartDataType = {
  number: 1, // 数量
  percent: 2 // 百分比
};

// 用户账号状态
export const UserStatus = {
  enabled: 0, // 已启用
  deleted: 1, // 已删除
  disabled: 2 // 已禁用
};

// 用户账号状态 多语言
export const UserStatusLan = {
  0: 'pc_survey_lbl_hasenabled', // 已启用
  1: 'pc_survey_lbl_hasdeleted', // 已删除
  2: 'pc_survey_lbl_hasdisnabled' // 已禁用
};
// 用户调查状态
export const UserSurveyStatus = {
  completed: 1, // 已完成
  uncomplete: 0 // 未完成
};

// 用户调查状态 多语言
export const UserSurveyStatusLan = {
  0: 'pc_survey_lbl_uncompleted', // 未完成
  1: 'pc_survey_frontindex_lbl_completed' // 已完成
};

// 调查提醒弹窗
export const MsgRemindType = {
  tourist: 0 // 调查关闭默认允许游客扫码参与功能改动提醒
};

// 用户调研的几个页面步骤
export const UserSurveyStep = {
  fbi: 0, // 调研中转页
  preview: 1, // 调研预览
  answer: 2, // 调研答题
  // complete: 3, // 调研结果
  result: 3 // 答卷详情
};
