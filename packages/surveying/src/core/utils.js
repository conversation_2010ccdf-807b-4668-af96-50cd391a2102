import browser from './browser';
import { TitleUtil, commonUtil } from 'yxt-biz-pc';
export const staticBaseUrl = window.feConfig.common.staticBaseUrl || 'https://stc.yxt.com/';
/**
 * 定义公共方法
 */

/**
 * html编码
 * @param {String} str html代码
 */
export const htmlEncode = function(str) {
  if (str === undefined || str === null || str.length === 0) return '';
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/ /g, '&nbsp;')
    .replace(/'/g, '&#39;')
    .replace(/"/g, '&quot;')
    .replace(/·/g, '&#183;')
    .replace(/\\/g, '\\\\')
    .replace(/%/g, '%25');
};

/**
 * html解码
 * @param {String} str 字符串
 */
export const htmlDecode = function(str) {
  if (str === undefined || str === null || str.length === 0) return '';
  return str
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&nbsp;/g, ' ')
    .replace(/&#39;/g, '\'')
    .replace(/&quot;/g, '"')
    .replace(/&#183;/g, '·')
    .replace(/\\\\/g, '\\')
    .replace(/%25/g, '%');
};

/**
 * 处理换行符
 * @param {String} txt 文本
 */
export const dealLineFeed = (txt) => {
  if (txt) {
    // 去掉所有的换行符
    txt = txt.replace(/\r\n/g, '<br />');
    txt = txt.replace(/\r/g, '<br />');
    txt = txt.replace(/\n/g, '<br />');
  }
  return txt;
};

export const replaceAllName = String.prototype.ReplaceAll ? 'ReplaceAll' : 'replaceAll';

/**
 * 日期格式化
 * @param {Object} value 日期值
 * @param {String} part 格式
 */
export const dateFormat = (value, part) => {
  let date;
  if (value instanceof Date) {
    date = value;
  } else if (typeof value === 'number') { // 时间戳
    date = new Date(value);
  } else {
    if (typeof value === 'undefined' || value === null || value === '' || value === '-' || value.indexOf('0001') >= 0 || value.indexOf('1900') >= 0) {
      return '-';
    }
    if (value.length > 10) {
      value = value.replace(/T/, ' ');
    }
    date = new Date(value.replace(/-/g, '/').split('.')[0]);
  }
  if (date === new Date(1970, 0, 1)) {
    return '-';
  }
  let redate = '';
  part = (part == null) ? 'yyyy-MM-dd HH:mm' : part;
  const y = date.getFullYear();
  const M = date.getMonth() + 1;
  const d = date.getDate();
  const H = date.getHours();
  const m = date.getMinutes();
  const s = date.getSeconds();
  const MM = (M > 9) ? M : '0' + M;
  const dd = (d > 9) ? d : '0' + d;
  const HH = (H > 9) ? H : '0' + H;
  const mm = (m > 9) ? m : '0' + m;
  const ss = (s > 9) ? s : '0' + s;
  redate = part.replace('yyyy', y).replace('MM', MM).replace('dd', dd).replace('HH', HH).replace('mm', mm).replace('ss', ss).replace('M', M).replace('d', d).replace('H', H).replace('m', m).replace('s', s);
  return redate;
};

/**
 * 获取日期时间(long)
 * @param {Object} value 日期值
 */
export const getDateTime = (value) => {
  let date = dateFormat(value, 'yyyy-MM-dd HH:mm:ss');
  if (date === '-') {
    date = new Date();
  } else {
    date = new Date(value.replace(/-/g, '/').split('.')[0]);
  }
  return date.getTime();
};

/**
 * 获取文件扩展名
 * @param {String} fileName 文件名
 */
export const getFileExtend = (fileName) => {
  const lastIndex = fileName.lastIndexOf('.');
  return lastIndex >= 0 ? fileName.substring(lastIndex + 1).toLowerCase() : '';
};

/**
 * 数字使用千分位分隔
 * @param {Number} number 需要格式化的数字
 * @param {Int} fixed 保留小数位数
 */
export const toThousands = (number, fixed) => {
  if (/^-?\d+(\.\d+)?$/.test(number)) { // 判断输入内容是否为整数或小数
    let n = Math.abs(number);
    if (typeof fixed !== 'undefined' && fixed > -1) {
      n = n.toFixed(fixed);
    }
    const reg = /\d{1,3}(?=(\d{3})+$)/g;
    const n1 = (n + '').replace(/^(\d+)((\.\d+)?)$/, function(s, s1, s2) { return s1.replace(reg, '$&,') + s2; });
    return (number < 0 ? '-' : '') + n1;
  } else {
    return '0';
  }
};

/**
 * 获取url中的query参数
 * @param {string} name 参数名
 */
export const getQueryString = (name) => {
  try {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    r = window.location.hash
      .substr(window.location.hash.indexOf('?') + 1)
      .match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return null;
  } catch (e) {
    return null;
  }
};

/**
 * url中的参数转换成json
 * @param {string} url
 * @returns {Object}
 */
export const param2Obj = (url) => {
  const search = url.split('?')[1];
  if (!search) {
    return {};
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, ' ') +
    '"}'
  );
};

/**
 * 加载js
 * @param {string} src js地址
 */
export const loadScript = src => {
  return new window.Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.defer = true;
    script.src = src;
    script.onload = () => resolve();
    document.head.appendChild(script);
  });
};

/**
 * 过滤xss攻击代码
 * @param {String} text 文本
 */
export const filterXss = (text) => {
  if (!text) {
    return '';
  }

  // 去掉script标签
  text = text.replace(/<script[\s\S]*?(<\/script>|\/>)/ig, '');
  // 去除on事件
  const onTagReg = /<[^>]*?( |\/)(on[\W\w]*?=([""'])?([^'""]+)([""'])?)[\S\s]*?>/ig;
  const onReg = /( |\/)on[\W\w]*?=([""'])?([^'""]+)([""'])?/ig;
  let onMatches = onTagReg.exec(text);
  while (onMatches) {
    text = text.replace(onMatches[0], onMatches[0].replace(onReg, ''));
    onMatches = onTagReg.exec(text);
  }
  // 去除非链接href
  const hrefReg = /<a[^>]*(href=([""']?([^'""]+)([""'])?))[\S\s]*?>/ig;
  let hrefMatches = hrefReg.exec(text);
  while (hrefMatches) {
    hrefMatches[3].indexOf('http') !== 0 && (text = text.replace(hrefMatches[0], hrefMatches[0].replace(hrefMatches[1], '')));
    hrefMatches = hrefReg.exec(text);
  }
  // 去除eval(...)和expression(...)
  text = text.replace(/(eval|expression)\(.*?\)/ig, '');

  return text;
};

/**
 * 加载字体
 * @param {*} fileName
 * @param {*} fontFamily
 */
export const loadFont = (fileName, fontFamily) => {
  const media = `${this.$staticBaseUrl}media/`;
  const style = document.createElement('style');
  style.type = 'text/css';
  style.innerText = `@font-face {font-family:${fontFamily};src:url(${media}survey/font/${fileName})};font-display: swap`;
  document.getElementsByTagName('head')[0].appendChild(style);
};

/**
 * 节流
 * @param {*} fn
 * @param {*} wait
 * @param {*} immediate
 * @returns
 */
export const throttling = (fn, wait, immediate) => {
  let timer;
  let context, args;

  const run = () => {
    timer = setTimeout(() => {
      if (!immediate) {
        fn.apply(context, args);
      }
      clearTimeout(timer);
      timer = null;
    }, wait);
  };

  return function() {
    context = this;
    args = arguments;
    if (!timer) {
      // throttle, set
      if (immediate) {
        fn.apply(context, args);
      }
      run();
    } else {
      // throttle, ignore
    }
  };
};

/**
 * 是否是有效数据，排除undefined & null
 * @param {*} obj
 * @param {'string'} type
 * @returns
 */
export const isValidData = (obj, type = '') => {
  const types = ['undefined', 'object', 'boolean', 'number', 'string', 'function', 'symbol', 'bigint'];
  if (obj === undefined || obj === null) return false;
  if (type) {
    const index = types.indexOf(type);
    // eslint-disable-next-line valid-typeof
    if (index >= 0) return typeof obj === types[index];
    return true;
  }
  return true;
};

// 是否是富文本
export const isRichText = s => {
  return replaceHtmlTags(s) !== s;
};
// 去除html标记 获取纯文字
export const replaceHtmlTags = (s) => {
  s = filterXss(s);
  const div = document.createElement('div');
  div.innerHTML = s;
  return div.innerText || div.textContent;
};

export const arrayToMap = (array = [], valueKey = 'value', labelKey = 'label') => {
  const map = {};
  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    map[item[valueKey]] = item[labelKey];
  }
  return map;
};

// 获取可滚动的父亲
export const getScrollParent = (element) => {
  let node = element;
  while (node &&
    node.nodeType === document.ELEMENT_NODE &&
    node.tagName !== 'HTML' &&
    node.tagName !== 'BODY' &&
    node !== window
  ) {
    const { overflowY } = window.getComputedStyle(node);

    if (/scroll|auto/i.test(overflowY)) {
      // if (element.scrollHeight > element.clientHeight) {
      return node;
      // }
    }

    node = node.parentNode;
  }

  return window;
};

/**
 * 获取倒计时时分秒
 */
export const getCountDownStr = (seconds) => {
  const padZero = (num) => {
    return num < 10 ? ('0' + num) : num;
  };
  if (!seconds) {
    seconds = 0;
  }
  // const dd = Math.floor(this.times / 1000 / 60 / 60 / 24)
  const hh = padZero(Math.floor((seconds / 60 / 60)));
  const mm = padZero(Math.floor((seconds / 60) % 60));
  const ss = padZero(Math.floor((seconds) % 60));
  return `${hh}:${mm}:${ss}`;
};

/**
 * 判断是否是数字
 * @param {*} val
 * @returns
 */
export const isNumber = (val) => {
  return (typeof val === 'number') && !isNaN(val);
};

/**
 * 对象数据是否一致
 * @param {*} obj1
 * @param {*} obj2
 * @returns
 */
export const sameObjectData = (obj1, obj2) => {
  try {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  } catch (e) {
    return false;
  }
};
// uuid 生成
export const guid = () => {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }

  return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4());
};

/**
 * 数字转字母
 * @param {string} num 数字
 */
export const convertASCIIForNum = (num) => {
  let itemCode = ''
  num = parseInt(num) + 1

  while (num > 0) {
    const remainder = (num - 1) % 26
    itemCode = String.fromCharCode(remainder + 65) + itemCode
    num = Math.floor((num - 1) / 26)
  }

  return itemCode
};

/*
* 图片地址
* */
export const imgUrl = `${staticBaseUrl}ufd/55a3e0/survey/pc/img/`;

const debounceKeyFun = {};
/**
 * 按照key来做节流拆分
 * @param {*} key 防抖唯一key
 * @param {*} fun 执行方法
 * @param {*} time 执行最小间隔
 */
export const debounceByKey = (key, fun, time) => {
  if (debounceKeyFun[key]) {
    clearTimeout(debounceKeyFun[key].timeout);
  }

  debounceKeyFun[key] = {
    fun,
    timeout: setTimeout(fun, time)
  };
};

/**
 * 直接调用
 * @param {*} key 防抖唯一key
 */
export const callDebouncedFunByKey = (key) => {
  const df = debounceKeyFun[key];
  if (df) {
    clearTimeout(df.timeout);
    df.fun && df.fun();
  }
};

export const isInViewPort = (element) => {
  const viewWidth = window.innerWidth || document.documentElement.clientWidth;
  const viewHeight = window.innerHeight || document.documentElement.clientHeight;
  const {
    top,
    right,
    bottom,
    left
  } = element.getBoundingClientRect();

  return (
    top >= 0 &&
    left >= 0 &&
    right <= viewWidth &&
    bottom <= viewHeight
  );
};

export const handleFileSize = value => {
  const unitArr = ['KB', 'MB', 'GB', 'TB'];
  if (!value) {
    return ['0kB', 0, unitArr[0]];
  }
  let index = 0;
  const srcsize = parseFloat(value);
  index = Math.floor(Math.log(srcsize) / Math.log(1024));
  if (index < 0) {
    return ['0kB', 0, unitArr[0]];
  }
  let size = srcsize / Math.pow(1024, index);
  size = parseFloat(size.toFixed(2)); // 保留的小数位数
  return [size + unitArr[index], size, unitArr[index]];
};

export const getTextWidth = (text, style) => {
  const span = document.createElement('span');
  span.innerText = text;
  span.style.display = 'inline-block';
  span.style.position = 'fixed';
  span.style.visibility = 'hidden';

  for (const attr in style) {
    span.style[attr] = style[attr];
  }
  document.body.appendChild(span);
  const width = span.clientWidth;
  removeDom(span);
  return width;
};

export const removeDom = dom => {
  if (browser.ie) {
    dom.removeNode();
  } else {
    dom.remove();
  }
};

export const downloadUrl = (url, name) => {
  const a = document.createElement('a');
  a.style.display = 'none';
  a.setAttribute('href', url);
  a.setAttribute('target', '_blank');
  a.setAttribute('download', name);
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

/**
 * 设置网页的标题
 */
export const setTitle = (title, pageCode) => {
  TitleUtil.setTitleByPageCode(title ? undefined : pageCode, title);
  // try {
  //   document.title = title
  //   if (browser.dingtalk) {
  //     window.dd && window.dd.ready(() => {
  //       window.dd.biz.navigation.setTitle({
  //         title: title
  //       })
  //     })
  //   }
  // } catch (error) {
  // }
};

export const downFileUrl = (name, url) => {
  if (url) {
    commonUtil.common.downloadFileIE(name, url);
  }
};

export const isNullOrUndefined = s => {
  return [undefined, null, 'undefined', 'null'].includes(s);
};
