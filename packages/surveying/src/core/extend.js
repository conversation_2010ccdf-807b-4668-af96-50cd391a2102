window.setCookie = function(cname, cvalue, expiration) {
  let myCookie = cname + '=' + cvalue + ';';
  if (expiration) {
    // 设置有效期
    myCookie += 'expires=' + expiration + ';';
  }
  document.cookie = myCookie;
};
// 获取cookie
window.getCookie = function(cname) {
  let result = null;
  const myCookie = '' + document.cookie + ';';
  const searchName = '' + cname + '=';
  let startOfCookie = myCookie.indexOf(searchName);
  let endOfCookie;
  if (startOfCookie !== -1) {
    startOfCookie += searchName.length;
    endOfCookie = myCookie.indexOf(';', startOfCookie);
    result = myCookie.substring(startOfCookie, endOfCookie);
  }
  return result;
};
// 清除cookie
window.clearCookie = function(name) {
  window.setCookie(name, '');
};
// 取缓存value
window.getLocalStorage = function(key) {
  let value;
  try {
    localStorage.setItem('TestKey', '123');
    value = localStorage.getItem(key);
  } catch (e) {
    value = window.getCookie(key);
  }
  return value;
};
// 设置缓存值
window.setLocalStorage = function(key, value) {
  try {
    localStorage.setItem(key, value);
  } catch (e) {
    window.setCookie(key, value);
  }
};
window.removeLocalStorage = function(key) {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    window.clearCookie(key, '');
  }
};

window.clearCookieForKey = function() {
  window.clearCookie('isClickMy');
  window.clearCookie('userId');
  window.clearCookie('orgId');
  window.clearCookie('email');
  window.clearCookie('mobile');
  window.clearCookie('headPictureUrl');
  window.clearCookie('isEmailValidated');
  window.clearCookie('isMobileValidated');
  window.clearCookie('token');
  window.clearCookie('isClickMy');
  window.clearCookie('toolbar');
  window.clearCookie('apps');
  window.clearCookie('clientKey');
  window.clearCookie('deviceId');
  // window.clearCookie("returnUrl");
  window.clearCookie('toolbarnew');
};

window.clearlocalStorageForKey = function() {
  localStorage.removeItem('userId');
  localStorage.removeItem('orgName');
  localStorage.removeItem('websitepicurl');
  localStorage.removeItem('email');
  localStorage.removeItem('mobile');
  localStorage.removeItem('headPictureUrl');
  localStorage.removeItem('isEmailValidated');
  localStorage.removeItem('isMobileValidated');
  localStorage.removeItem('token');
  localStorage.removeItem('toolbar');
  localStorage.removeItem('apps');
  localStorage.removeItem('isClickMy');
  localStorage.removeItem('clientKey');
  localStorage.removeItem('deviceId');
  localStorage.removeItem('schemeversion');
  localStorage.removeItem('newIndex');
  localStorage.removeItem('toolbarnew');
  sessionStorage.clear();
  localStorage.removeItem('sex');
  localStorage.removeItem('ouId');
  localStorage.removeItem('ouName');
  localStorage.removeItem('positionId');
  localStorage.removeItem('positionName');
  localStorage.removeItem('searchKnowledgeKeywords');
  localStorage.removeItem('oldhuazhuad');
  localStorage.removeItem('examSource');
  localStorage.removeItem('communitySearch');
  localStorage.removeItem('personalPermissionCodes');
  localStorage.removeItem('indexFunctionRoutes');
  localStorage.removeItem('indexLastNotice');
  window.clearLocalStorageCache();
};

window.clearLocalStorageCache = function() {
  const storage = window.localStorage;
  const len = storage.length;
  for (let i = len; i >= 0; i--) {
    const key = storage.key(i);
    if (key && key.indexOf('CACHEDATA') > -1) {
      localStorage.removeItem(key);
    }
  }
};

if (!window.location.origin) {
  window.location.origin = window.location.protocol + '//' + window.location.hostname;
}

window.getAuthUrlByOrgId = function(key, aliceKey) {
  // const orgId = getQueryString(key) || getQueryString(aliceKey)
  // let hash = `/login?og=${orgId}`
  const hash = '/login';
  const href = window.location.origin + '/#' + hash;
  // if ((window.isDing || window.isWeixin) && orgId) {
  //   hash = `/scanentry/${orgId}?lru=${encodeURIComponent(window.location.href)}`
  //   href = window.location.origin + '/#' + hash
  // }
  return { hash, href };
};
