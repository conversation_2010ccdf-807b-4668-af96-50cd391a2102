/**
 * 扩展原生方法
 */

import { dateFormat } from './utils';

try {
  /**
 * 将 Date 转化为指定格式的String
 */
  Date.prototype.format = function(part) {
    return dateFormat(this, part);
  };

  /**
 * 数组增加删除元素方法
 * filter：为Number时，根据index删除；为function时，根据筛选条件删除
 */
  Array.prototype.remove = function(filter) {
    if (typeof filter === 'number') {
      this.splice(~~filter, 1);
      return;
    }
    if (typeof filter === 'function') {
      for (let i = 0; i < this.length; i++) {
        if (filter(this[i], i)) {
          this.splice(i, 1);
        }
      }
    }
  };

  /**
 * flat方法重写，支持ie
 * @param {*} count
 * @returns
 */
  Array.prototype.flat = function(count) {
    let c = count || 1;
    const len = this.length;
    let ret = [];
    if (this.length === 0) return this;
    while (c--) {
      const _arr = [];
      let flag = false;
      if (ret.length === 0) {
        flag = true;
        for (let i = 0; i < len; i++) {
          if (this[i] instanceof Array) {
            ret.push(...this[i]);
          } else {
            ret.push(this[i]);
          }
        }
      } else {
        for (let i = 0; i < ret.length; i++) {
          if (ret[i] instanceof Array) {
            flag = true;
            _arr.push(...ret[i]);
          } else {
            _arr.push(ret[i]);
          }
        }
        ret = _arr;
      }
      if (!flag && c === Infinity) {
        break;
      }
    }
    return ret;
  };
} catch (error) {
  console.log(error);
}
