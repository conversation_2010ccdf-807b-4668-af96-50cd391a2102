<template>
  <yxt-svg
    :remote-url="remoteUrl"
    :width="width"
    :height="height"
    :icon-class="iconClass"
  />
</template>

<script>
export default {
  name: 'YxtUlcdSdkSvg',
  props: ['width', 'height', 'module', 'iconClass', 'type'],
  data() {
    return {};
  },
  computed: {
    prefix() {
      return this.type || 'svg';
    },
    remoteUrl() {
      if (this.module === 'o2o') {
        return `${this.$staticBaseUrl}ufd/55a3e0/o2o/pc/${this.prefix}/`;
      }
      return `${this.$staticBaseUrl}ufd/55a3e0/yxt-ulcd-sdk/pc/${this.prefix}/`;
    }
  }
};
</script>
