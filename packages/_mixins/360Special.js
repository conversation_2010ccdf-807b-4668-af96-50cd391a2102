// 轮询查验360问题
export default {
  data() {
    return {
      // timeId: null,
      yxtPreScreenOptions: undefined
    };
  },
  methods: {
    yxtPreScreenCap(options) {
      if (!options) {
        // 没有传入任何参数,报错
        throw new Error('错误：请传入防录屏配置');
      }
      if (!options.playrefs) {
        throw new Error('错误：传入防录屏配置参数错误');
      }
      this.yxtPreScreenOptions = options;
      window.addEventListener('resize', this.yxtPreScreenWatchWindow);
    },
    yxtPreScreenInterval() {
      if (this.timeId != null) {
        clearInterval(this.timeId);
        this.timeId = null;
      }
      // 启用计时器
      this.timeId = setInterval(this.yxtPreScreenWatchWindow, 3000);
    },
    // 判断元素在小窗口下会消失
    yxtPreScreenWatchWindow() {
      if (document.documentElement.style.overflow !== 'hidden' ||
        document.body.style.overflowX !== 'hidden' ||
        document.body.style.overflowY !== 'hidden') {
      } else {
        this.yxtPreScreenTipInfo();
        // clearInterval(this.timeId);
      }
    },
    // 拿到播放元素id,移除它
    yxtPreScreenTipInfo() {
      let container = this.yxtPreScreenOptions.playrefs.$el;
      if (container) {
        container.remove();
      }
      let height = window.innerHeight.toString();
      let errorMessageDiv = document.createElement('div');
      errorMessageDiv.id = 'noSpreadTip';
      errorMessageDiv.style.backgroundColor = 'black';
      errorMessageDiv.style.width = '100%';
      errorMessageDiv.style.height = height + 'px';
      errorMessageDiv.style.color = '#fff';
      errorMessageDiv.style.position = 'absolute';
      errorMessageDiv.style.left = '0';
      errorMessageDiv.style.top = '0';
      errorMessageDiv.style.zIndex = '10000';
      let eMessage = '';
      eMessage = '视频禁止在小窗口播放,请关闭当前标签重新打开或重启浏览器继续播放!';
      errorMessageDiv.innerHTML = "<div id='errorMessage' style='position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);font-size: 20px;'>" + eMessage + '</div>';
      let body = document.body;
      // 动态插入body下第一个元素的前面
      body.insertBefore(errorMessageDiv, document.body.firstElementChild);
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.yxtPreScreenWatchWindow);
    // clearInterval(this.timeId);
  }
};
