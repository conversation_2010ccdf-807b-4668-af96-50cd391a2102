// 任务详情 (不包含作业)
// import taskHeader from '../components/taskHeader';
import {
  checkPermission,
  getHomeworkDetail,
  getCooperationHwk
} from '../_services/taskDetail.service';
import { FACTOR_FUCTIONS } from 'main/config/factor.js';
import polling from './polling';
import { commonUtil } from 'yxt-biz-pc';
import { Message } from 'yxt-pc';

export const i18n = commonUtil.i18n;

export const getErrorMsg = (code, message) => {
  switch (code) {
    case 1:
    case 16:
      return message;
    case 2:
      return i18n.t('pc_o2o_msg_lock_nostudy' /* 当前任务所在阶段已锁定，不可以学习！ */);

    case 3:
      return i18n.t('pc_o2o_msg_projectstarttimenotreached' /* 项目未到开始时间，开始时间：{0} */, [message]);

    case 4:
      return i18n.t('pc_o2o_msg_taskstarttimenotreached' /* 任务未到开始时间，开始时间：{0} */, [message]);

    case 5:
      return i18n.t('pc_o2o_msg_no_qualified_nostudy' /* 存在不合格的任务，不可以继续学习! */);

    case 6:
      return i18n.t('pc_o2o_msg_task_need_complete' /* 存在未完成的任务，不可以继续学习！ */);

    case 7:
      return i18n.t('pc_o2o_msg_no_release' /* 项目未发布 */);

    case 8:
      return i18n.t('pc_o2o_msg_task_not_found' /* 任务未找到 */);

    case 9:
    case 10:
    case 11:
      return i18n.t(message);
    case 12:
      return i18n.t('pc_o2o_msg_periodlocktime' /* 阶段未到解锁时间，解锁时间：{0} */, [message]);
    case 13:
      return i18n.t('pc_o2o_msg_taskstarttimenotunlocking' /* 任务未到解锁时间，开始时间：{0} */, [message]);
    case 14:
      return i18n.t('pc_o2o_msg_projectend' /* 项目已结束 */);
    case 20:
      return i18n.t(message);
    default:
      window.location.replace(`${window.location.origin}/error/403.html`);
  }
};

export default {
  mixins: [polling],
  components: {
    // taskHeader
  },
  props: {
    taskId: {
      type: String,
      default: ''
    },
    bizzType: {
      type: Number,
      default: 0
    },
    batchid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      task: {},
      preview: false,
      checkCooperHwk: false, // 协同作业
      loading: true,
      workLoading: true,
      isGetDetail: false,
      canViewDeitail: false, // 是否能看详情
      typeList: [3, 4, 5, 25, 28] // 调查/评价/投票/自定义调查/报名登记
    };
  },
  computed: {
    o2oOjtShow() {
      return this.$store.state.factor.status[FACTOR_FUCTIONS.PROJECT_OJT];
    }
  },
  methods: {
    init() {
      // preview 预览
      if (this.preview) return;

      if (this.checkCooperHwk) {
        this.getDetail();
      } else {
        if (Number(this.multiType) === 2) {
          return false;
        }
        this.checkPermission();
      }
    },
    checkPermission() {
      if (this.checkedPermission) {
        this.loading = true;
        this.isGetDetail = false;
        checkPermission(this.taskId, {module: 'o2o'})
          .then((data) => {
            if (data.code === 0) {
              return this.getDetail();
            }
            const errorMsg = getErrorMsg(data.code, data.message);
            this.$error(errorMsg);
          })
          .finally(() => {
            this.loading = false;
            this.isGetDetail = true;
          });
      } else {
        if (this.isIdentification) {
          // 是鉴定页面
          this.canViewDeitail = true;
          this.initData && this.initData();
        }
        this.getDetail();

        this.loading = false;
        this.isGetDetail = true;
      }
    },
    // 获取任务详情
    getDetail() {
      this.workLoading = true;
      return this[this.checkCooperHwk ? 'fetchCooperationHwk' : 'fetchTaskDetail']().finally(() => {
        this.workLoading = false;
      });
    },
    fetchCooperationHwk() {
      const { taskId: homeworkId, userId } = this;
      return getCooperationHwk({ homeworkId, userId }).then(res =>
        this[this.$options.detailMethod](res)
      );
    },
    fetchTaskDetail() {
      // 普通作业，外链， 面授....
      return getHomeworkDetail(this.taskId, this.userId, '0', this.batchid || this.bthid).then(res => {
        // 自己处理数据
        if (this.$options.detailMethod) {
          this[this.$options.detailMethod](res);
        } else {
          // 统一处理
          const data = res;
          this.pid = data.projectId;
          this.task = data;

          // 投票/调查/问卷/报名登记 & 导师任务 & 带教能力
          if (
            this.typeList.includes(this.task.type) &&
            Number(this.task.category) === 1 &&
            this.o2oOjtShow.hidden
          ) {
            window.location.replace(window.location.origin + '/unusable.html');
          }
        }
      });
    },

    handleError(error) {
      if (error && error.error) {
        // 兼容处理
        error = error.error;
      }
      if (error) {
        if (error.key && error.key.indexOf('global.token') >= 0) {
          return; // 不弹出token错误
        }
        const msg = error.message;
        if (typeof (msg) !== 'string') return;
        Message({
          message: msg,
          type: 'error'
        });
      }
    }
  }
};
