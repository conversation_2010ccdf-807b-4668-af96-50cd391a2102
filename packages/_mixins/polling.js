// 任务详情 轮询查验任务是否更新
import { getTaskHasDeleted } from '../_services/taskDetail.service';
export default {
  // props: {
  //   tid: [String, Number]
  // },
  data() {
    return {
      timer: null,
      pid: '',
      userId: localStorage.getItem('userId') || this.$route.query.uid
    };
  },
  watch: {
    $route() {
      window.location.reload();
    }
  },
  created() {
    if (this.preview) return;

    this.initPolling();
  },
  methods: {
    // 轮询接口
    initPolling() {
      clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.checkTask();
      }, 5 * 60 * 1000);
    },
    // 验证任务是否删除
    checkTask() {
      getTaskHasDeleted(this.pid, this.taskId, this.bizzType).then(res => {
        if (!res.data) return;
        clearInterval(this.timer);
        this.$confirm(this.$t('pc_o2o_msg_deletedtask'), {
          confirmButtonText: this.$t('pc_o2o_btn_withdrawstudy'),
          cancelButtonText: this.$t('pc_o2o_tip_goOnStudy'),
          center: true
        }).then(() => {
          this.$router.go(-1);
        }).catch(() => {
          // this.initPolling() // 重置计时器
        });
      });
    }
  },
  beforeDestroy() {
    clearInterval(this.timer);
  }
};
