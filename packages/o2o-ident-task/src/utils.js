export function getQueryString(url, name) {
  try {
    let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    let r = url.substr(1).match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    r = url
      .substr(url.indexOf('?') + 1)
      .match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return null;
  } catch (e) {
    return null;
  }
};
