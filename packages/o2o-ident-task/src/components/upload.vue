<template>
  <div>
    <yxtbiz-upload
      ref="bizUpload"
      source="501"
      app-code="o2o"
      config-key="AttachConfigKey"
      module-name="attach"
      function-name="upload"
      :filters="defaultAccept"
      :files-filter="filesFilter"
      :files-added="filesAdd"
      :on-progress="onProgress"
      :on-uploaded="onUploaded"
      :on-complete="onComplete"
      :on-error="onError"
      :auto-upload="false"
      md5
      multipe
      convert
    >
      <yxt-tooltip
        :content="isViewMode ? $t('pc_biz_msg_ident_upload_attach' /* 鉴定已申请，不可再上传 */) : disabledUpload ? $t('pc_apaas_msg_maxfilesize', [maxCount]) : $t('pc_biz_o2o_msg_attch_uploading' /* 附件正在上传中，不可再上传 */)"
        placement="top"
        :disabled="!(disabledUpload || isViewMode || isUploadLoading)"
      >
        <yxt-button :disabled="disabledUpload || isViewMode || isUploadLoading">{{ $t('pc_biz_uploadimg_btn_upload' /* 上传附件 */) }}</yxt-button>
      </yxt-tooltip>
    </yxtbiz-upload>

    <div class="upload-list-container" :class="{'mt-8px': attachList.length}">
      <template v-if="attachList.length || !isViewMode">
        <div v-for="(list, index) in attachList" :key="index" class="upload-list">
          <!-- 删除按钮 -->
          <div v-if="!isViewMode" class="upload-attach-del" @click="delFile(index)">
            <yxt-svg
              width="16px"
              height="16px"
              icon-class="close"
              class="color-white hand"
            />
          </div>
          <!-- 背景图 -->
          <div class="upload-attach-bg">
            <div v-if="isViewMode" class="upload-attach-operate">
              <div class="upload-attach-operate-item color-white">
                <div class="flex-center-start hand" @click="previewAttach(index)">
                  <yxt-svg
                    width="16px"
                    height="16px"
                    icon-class="view"
                  />
                  <span class="ml4">{{ $t('pc_biz_imgcropper_btn_preview' /* 预览 */) }}</span>
                </div>

                <div v-if="isSupportDownload" class="flex-center-start hand ml38" @click="getFileDownloadUrl(list)">
                  <yxt-svg
                    width="16px"
                    height="16px"
                    icon-class="download-center"
                    class="color-white"
                  />
                  <span class="ml4">{{ $t('pc_biz_import_btn_download' /* 下载 */) }}</span>
                </div>
              </div>
            </div>
            <!-- 附件-默认背景 -->
            <div class="upload-attach-default-bg" :class="{'error': list['_status'] === uploadStatus.fail && !isViewMode}">
              <div v-if="list['_status'] === 1" :style="`width: ${Math.floor(list.progress * 188)}px`" class="upload-attach-progress"></div>

              <yxt-svg
                width="54px"
                height="54px"
                :icon-class="getSvgIcon(list)"
              />
            </div>
          </div>
          <!-- 附件名称 -->
          <div class="upload-attach-name mt8">
            <yxt-tooltip
              :content="list.name"
              class="ellipsis upload-attach-tooltip"
              placement="top"
              open-filter
            >
              <span :class="{'color-danger-6': list['_status'] === uploadStatus.fail && !isViewMode}">{{ list.name }}</span>
            </yxt-tooltip>
          </div>
        </div>
      </template>

      <span v-else class="mt8">--</span>

      <yxtbiz-course-player
        v-if="attachList.length && isViewMode"
        :file-id-list="attachIdList"
        :file-list="attachList"
        :start="previewStart"
        :visible="showPreview"
        :join-kng="false"
        :close-preview="closeAttach"
        :download-cb="getFileDownloadUrl"
      />
    </div>
  </div>
</template>

<script>
import { getFileDownloadUrl } from '../service';
export default {
  props: {
    attachList: {
      type: Array,
      default: () => []
    },

    maxCount: {
      type: Number,
      default: 10
    },

    isViewMode: {
      type: Boolean,
      default: false
    },

    isSupportDownload: {
      type: Boolean,
      default: true
    }
  },
  data() {
    this.uploadStatus = {
      going: 1,
      success: 2,
      fail: 3
    };

    this.transcodingStatus = {
      wait: 0,
      ...this.uploadStatus
    };

    this.FILE_TYPES = {
      '.jpg,.jpeg,.gif,.png,.bmp,.ico': 'img',
      '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.mts': 'video',
      '.w4v,.m4a,.wma,.wav,.mp3,.amr': 'audio',
      '.zip,.rar': 'zip',
      '.xls,.xlsx': 'excel',
      '.ppt,.pptx': 'ppt',
      '.pdf': 'pdf',
      '.doc,.docx': 'word'
    };
    return {
      defaultAccept: '.jpg,.jpeg,.gif,.png,.bmp,.ico,.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.mts,.w4v,.m4a,.wma,.wav,.mp3,.amr,.zip,.rar,.xls,.xlsx,.ppt,.pptx,.pdf,.doc,.docx',
      canRemoveFiles: [],
      previewStart: 1,
      showPreview: false,
      isUploadLoading: false
    };
  },

  computed: {
    progressIds() {
      if (this.attachList && this.attachList.length) {
        return this.attachList.filter(item => item._status === this.uploadStatus.going).map(item => item.fileId || item.uuid) || [];
      }
      return [];
    },

    errorList() {
      if (this.attachList && this.attachList.length) {
        return this.attachList.filter(item => item._status === this.uploadStatus.fail);
      }
      return [];
    },

    disabledUpload() {
      return this.maxCount === this.attachList.length;
    },

    attachIdList() {
      return this.attachList.map(item => item.fileId);
    }
  },

  methods: {
    change() {
      console.log('eval Index attachList', this.attachList);
      this.$emit('update:attachList', this.attachList);
    },

    // 根据 fileName获取新的文件名称
    getFileNewName(fileName) {
      if (!fileName) return '';
      return fileName.indexOf('.') > -1 ? fileName.split('.')[0] + this.getFileExtension(fileName) : fileName;
    },

    getFileExtension(fileName) {
      if (!fileName) return '';
      return fileName.indexOf('.') > -1 ? `.${ fileName.split('.').pop().toLowerCase() }` : '';
    },

    getSvgIcon(item, concatKey = 'icons/f_kng-') {
      let extension = (item.extension && item.extension.toLowerCase()) || this.getFileExtension(item.name);
      const matchedType = Object.keys(this.FILE_TYPES).find(key => key.split(',').includes(extension));
      return `${concatKey}${this.FILE_TYPES[matchedType] || 'other'}`;
    },

    // 校验图片类型和上传最大数量
    checkFile(files = []) {
      const filters = this.defaultAccept.split(',');
      const filterFiles = files.filter(item => {
        const extension = (item.extension && item.extension.toLowerCase()) || this.getFileExtension(item.name);
        return filters.indexOf(extension) >= 0;
      });

      let msg;
      if (files.length !== filterFiles.length) {
      // 文件类型不符合
        msg = this.$t('pc_apaas_msg_filetype_not_matched');
      }

      if ((this.attachList.length + filterFiles.length) > this.maxCount) {
        return {
          files: [],
          // `最多可上传{0}个文件`
          msg: this.$t('pc_apaas_msg_maxfilesize', [this.maxCount])
        };
      }

      return {
        files: filterFiles,
        msg
      };
    },

    // 过滤附件类型
    filesFilter(files) {
      const result = this.checkFile(files);
      if (result.msg) {
        this.$message.error(result.msg);
      }
      if (result.files.length === 0) return [];
      return result.files;
    },
    // 附件添加到队列
    filesAdd(files) {
      if (!files.length) return;

      const f = files.map(item => ({
        name: this.getFileNewName(item.name),
        fileSize: item.size,
        uuid: item.uuid,
        fileType: item.fileType,
        progress: 0,
        fileStatus: ['image', 'zip', 'rar'].includes(item.fileType) ? this.transcodingStatus.success : this.transcodingStatus.going,
        _status: this.uploadStatus.going,
        _error: '',
        _origin: item
      }));

      this.canRemoveFiles = this.canRemoveFiles.concat(files);

      if (!this.attachList) this.$set(this, 'attachList', []);
      this.attachList = this.attachList.concat(f);

      // 开始上传
      this.$refs.bizUpload && this.$refs.bizUpload.start();
      // 开始上传 loading
      this.isUploadLoading = true;
    },

    // 上传进度
    onProgress(file, progress, event) {
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (!f) return;
      if (!f.progress) this.$set(f, 'progress', progress);
      else f.progress = progress;
    },

    // 单个附件上传完成
    onUploaded(file) {
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (!f) return;
      f._status = this.uploadStatus.success;
      f.fileId = file.id;
      f.url = file.fullUrl;
      delete f._origin;
    },

    // 全部附件上传完成
    onComplete() {
      this.isUploadLoading = false;
      this.change();
    },
    // 上传报错
    onError(error, file) {
      const f = this.attachList.find(item => item.uuid === file.uuid);
      if (!f) return;
      this.$set(f, '_status', this.uploadStatus.fail);

      let errorText = '';
      switch (error) {
        case 'oversize':
          // 超出文件上传大小
          errorText = this.$t('pc_apaas_msg_oversize');
          break;
        case 'forbidden':
          // 文件格式错误
          errorText = this.$t('pc_apaas_msg_upload_error_forbbiden');
          break;
        case 'overlength':
          // 上传失败,文件名超过200字
          errorText = this.$t('pc_apaas_msg_overlength');
          break;
        default:
          break;
      }

      this.$message.error(this.$t('pc_ulcd_lbl_upload_fail' /* 上传失败 */));
      this.$set(f, '_error', errorText);
    },

    delFile(index) {
      const file = this.attachList[index];
      this.attachList.splice(index, 1);
      file && file._origin && !file._origin.error && file._origin.abort();
      this.change();
    },

    getFileDownloadUrl(item) {
      const id = typeof item === 'string' ? item : (item.id || item.fileId);
      if (!id) return;
      getFileDownloadUrl(id).then(result => {
        result.downloadUrl && window.open(result.downloadUrl, '_blank');
      }).catch(error => {
        this.$message.error(error.message);
      });
    },

    // 打开预览附件的弹窗，并选择预览的index
    previewAttach(index) {
      this.previewStart = index;
      this.showPreview = true;
    },

    // 关闭预览附件的弹窗
    closeAttach() {
      this.showPreview = false;
    },

    // 退出页面的时候把正在上传的文件取消上传
    removeFileAndClear() {
      if (this.progressIds.length && this.canRemoveFiles.length) {
        this.canRemoveFiles.forEach(item => {
          if (this.progressIds.includes(item.id || item.fileId || item.uuid)) {
            item.abort();
            item && item._origin && !item._origin.error && item._origin.abort && item._origin.abort();
          }
        });
      }
    }
  },

  beforeDestroy() {
    this.removeFileAndClear();
  }
};
</script>
