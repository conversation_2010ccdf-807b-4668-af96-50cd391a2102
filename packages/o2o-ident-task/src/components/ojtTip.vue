<!--
 * 任务详情 - 活动
-->
<template>
  <div>
    <yxtf-dialog
      :title="$t('pc_o2o_identify_te_tip').d('申请鉴定提示')"
      :visible.sync="visible"
      width="640px"
      :append-to-body="true"
      custom-class="yxtulcdsdk-ulcdsdk"
      :modal-append-to-body="true"
    >
      <div>
        <div v-if="!auditStatus">
          <div>
            <span>{{ $t('pc_o2o_identify_te_tip_text1').d('你的鉴定人为带教导师，') }}</span>
            <span class="color-gray-10">{{ $t('pc_o2o_identify_te_tip_text2').d('建议先匹配导师后再申请鉴定') }}</span>
            <span>{{ $t('pc_o2o_identify_te_tip_text3').d('，这样导师才能收到鉴定任务') }}</span>
          </div>
          <div class="mt16 pl16 pt12 pb12 pr16 bg-gray-1 mb24 radius4">
            <div>{{ $t('pc_o2o_identify_te_tip_how').d('如何匹配导师？') }}</div>
            <div v-if="!isShowTeacherSelect" class="mt4"><span class="mr16 weight-medium color-gray-10">{{ $t('pc_o2o_identify_te_tip_how_text1').d('联系培训负责人进行匹配导师') }}</span><yxt-button type="text" :loading="manageLoading" @click="showManage">{{ $t('pc_o2o_identify_te_view_manage').d('查看负责人') }}</yxt-button></div>
            <div v-else class="mt10">
              <div class="mb8"><span class="mr16 weight-medium color-gray-10">{{ $t('pc_o2o_identify_te_tip_how_way1').d('方式一：联系培训负责人进行匹配导师') }}</span><yxt-button type="text" :loading="manageLoading" @click="showManage">{{ $t('pc_o2o_identify_te_view_manage').d('查看负责人') }}</yxt-button></div>
              <div class="mb4"><span class="mr16 weight-medium color-gray-10">{{ $t('pc_o2o_identify_te_tip_how_way2').d('方式二：学员自主选择导师') }}</span></div>
              <div><span class="mr16">{{ $t('pc_o2o_identify_te_tip_how_sub').d('你可根据所在岗位安排选择对应的带教导师') }}</span></div>
            </div>
          </div>
          <div class="text-right">
            <span>
              <yxt-button v-if="showContinue" @click="apply">{{ $t('pc_o2o_identify_te_apply').d('继续申请') }}</yxt-button>
            </span>
            <span>
              <yxt-button class="ml16" :type="!isShowTeacherSelect ? 'primary' : undefined" @click="hide">{{ $t('pc_o2o_identify_te_not_apply').d('暂不申请') }}</yxt-button>
            </span>
            <span>
              <yxt-button
                v-if="isShowTeacherSelect"
                class="ml16"
                type="primary"
                @click="showTeacherSelection"
              >{{ $t('pc_o2o_identify_te_select_teacher').d('选择导师') }}</yxt-button>
            </span>
          </div>
        </div>
        <div v-else class="text-center">
          <div class="layout-flex layout-justify-center">
            <yxt-svg
              class="mt16 mb16"
              :remote-url="`${$staticBaseUrl}assets/6160a644/959425fe`"
              width="64px"
              height="64px"
              icon-class="warning"
            />
          </div>
          <div class="weight-medium font-size-20 mb4 lh28 color-gray-10">{{ $t('pc_o2o_lbl_underReview').d('审核中') }}</div>
          <div class="color-gray-7 ml30 mr30 mb24">{{ $t('pc_o2o_identify_te_audit_text').d('你的鉴定人为带教导师，目前导师已选择完成，需审核通过后，才会师徒匹配成功 匹配成功后，导师才能收到鉴定任务') }}</div>
          <div class="pb32">
            <yxt-button v-if="showContinue" @click="apply">{{ $t('pc_o2o_identify_te_apply').d('继续申请') }}</yxt-button>
            <yxt-button type="primary" @click="hide">{{ $t('pc_o2o_identify_te_not_apply').d('暂不申请') }}</yxt-button>
          </div>
        </div>
      </div>
    </yxtf-dialog>
    <!--    查看培训负责人-->
    <yxt-dialog
      v-if="manageVisible"
      title="查看培训负责人"
      :visible.sync="manageVisible"
      width="640px"
      :append-to-body="true"
      :modal-append-to-body="true"
      custom-class="identify-ojt-tip-dialog"
    >
      <div v-infinite-scroll="getProjectManages" :infinite-scroll-disabled="finished || manageLoading">
        <div
          v-for="(people, index) in manageList"
          :key="people.id"
          class="layout-flex layout-align-center"
          :class="index !== 0 ? 'mt18' : ''"
        >
          <yxtf-portrait
            size="40px"
            class="hand"
            :img-url="people.imgUrl"
            :username="people.fullname"
            @click.native="openUserInfo(people.id)"
          />
          <div class="ml8 pt2 col-flex-1 over-hidden">
            <yxtbiz-user-name class="d-block lh20 ellipsis nowrap text-26" :name="people.fullname" />
            <yxtbiz-dept-name
              v-if="people.deptName"
              class="d-block mt3 lh18 nowrap ellipsis text-8c font-size-12"
              :name="people.deptName"
            />
          </div>
        </div>
      </div>
    </yxt-dialog>
    <yxtpd-choose-teacher
      :ojt-project-info="ojtTeacherInfo4Select"
      :pre-user-info="preUserInfo"
      :visible.sync="chooseTeacherVisible"
      :title="$t('pc_o2o_lbl_selectOjtTeacher' /* 选导师 */)"
      @confirmChooseTeacher="afterChooseTeacher"
    />
  </div>
</template>

<script>
import { getProjectManage } from '../service';

export default {
  name: 'OjtTip',
  props: {
    showContinue: {
      default: true
    },
    // 是否关闭师徒匹配(数据库 师徒互选-学员选择导师 0:选择 1：没选择 )
    studentChooseTeacher: {
      default: true,
      type: Boolean
    },
    // 是否审核中
    auditStatus: {
      default: true,
      type: [Number, Boolean]
    },
    projectId: String,
    ojtTeacherInfo: {
      type: Object,
      default: () => {
        return {
          canChooseTe: null,
          ojtMode: null,
          period: null
        };
      }
    }
  },
  data() {
    return {
      visible: false,
      manageVisible: false, // 培训负责人
      manageList: {},
      manageLoading: false,
      chooseTeacherVisible: false, // 选导师弹层显示
      preUserInfo: {}, // 更换导师 前一个导师的ID, 当前功能是无导师才触发，所以直接为空即可
      finished: false, // 是否加载完所有负责人
      managePage: {
        offset: 0,
        limit: 10
      }
    };
  },
  computed: {
    ojtTeacherInfo4Select() {
      return {
        ...this.ojtTeacherInfo,
        projectId: this.projectId
      };
    },
    // 是否展示选择导师按钮（开启师徒互选并且当前学员在可自主选择导师的人员范围内）
    isShowTeacherSelect() {
      return !this.studentChooseTeacher && this.ojtTeacherInfo.canChooseTe;
    }
  },
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    async showManage() {
      this.managePage.offset = 0;
      this.manageList = [];
      this.finished = false;
      this.manageVisible = true;
    },
    async showTeacherSelection() {
      this.chooseTeacherVisible = true;
    },
    afterChooseTeacher() {
      this.$emit('update');
    },
    apply() {
      this.$emit('apply');
      this.hide();
    },
    // 获取项目基本信息
    getProjectManages() {
      this.manageLoading = true;
      getProjectManage(this.projectId, {
        offset: this.managePage.offset,
        limit: this.managePage.limit
      }).then(res => {
        if (res) {
          this.manageList = this.manageList.concat(res.datas || []);
          this.finished = this.manageList.length >= res.paging.count;
          this.managePage.offset = res.paging.offset + res.paging.limit;
          this.manageLoading = false;
        } else {
          this.manageLoading = false;
          this.finished = true;
        }
      }).catch(e => {
        this.manageLoading = false;
        this.finished = true;
      });
    }
  }
};
</script>
<style lang='scss'>
.identify-ojt-tip-dialog {
  .yxt-dialog__header {
    height: 54px;
  }

  .yxt-dialog__title {
    font-size: 14px;
  }

  .yxt-dialog__headerbtn {
    svg {
      color: #bfbfbf;
    }

    &:hover {
      svg {
        color: var(--color-primary);
        background-color: transparent;
      }
    }
  }
}
</style>
