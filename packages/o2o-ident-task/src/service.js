import { o2oApi, fileApi } from 'packages/api';
import qs from 'qs';

// 学员端获取鉴定详情
export const getAppraisalDetail = params => {
  return o2oApi.get(`/appraisal/user/appraisal?${qs.stringify(params)}`);
};

// 学员提交鉴定
export const postAppraisalApply = (params, config) => {
  return o2oApi.post('/appraisal/apply', params, config);
};

// 调查局查看地址
export const getSurveyUrlToStu = (taskId, userId, projectId, processNodeId, batchId) => {
  return o2oApi.get(
    `/survey/noControl/answerurl?pageType=1&taskId=${taskId}&userId=${userId}&projectId=${projectId}&targetId=${userId}&processNodeId=${processNodeId}&batchId=${batchId || ''}`
  );
};

// 学员端-带教导师审核状态
export const getAuditTeacherStatus = (taskId, params) => {
  return o2oApi.get(`appraisal/auditteacher/status/${taskId}`, { params });
};

export const getStuTeachers = (projectId, userId = '', config = {}) => {
  return o2oApi.get(`ojt/my/teacher?projectId=${projectId}&userId=${userId}`, config);
};

// 获取项目负责人（分页）
export const getProjectManage = (projectId, params) => {
  return o2oApi.get(`project/client/principal/${projectId}`, { params });
};

// 获取文件下载接口地址(根据文件id)
// bucketType: 1 公开空间 2 私密空间
export const getFileDownloadUrl = (fileId, bucketType = 2) => {
  return fileApi.get(
    `download/saveas?fileId=${fileId}&bucketType=${bucketType}`
  );
};
