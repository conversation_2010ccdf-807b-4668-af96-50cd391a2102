<template>
  <div v-loading="loading" class="yxtulcdsdk-o2o-ident-task yxtulcdsdk-ulcdsdk minh100" :class="{'bg-white yxtulcdsdk-o2o-ident-task-wrap': inner}">
    <div v-if="canViewDeitail && !checkSurvey" class="o2o-pc-identification">
      <div class="bg-white  radius4 center-block" :class="{'mt20 w1200 mb24': !inner}">
        <div :class="{'center-wrap pl24 pr24': inner}">
          <div class="pt32 pb40 " :class="{'o2o-pc-identification-content': !inner}">
            <div class="color-26 lh36 weight-bold font-size-24 mb24 word-wrap-break">
              {{ identificationDetail.appraisalName }}
            </div>
            <div class="flex-center-start">
              <div class="w50per">
                <div class="layout-flex mb16">
                  <div class="mr64">
                    <div class="color-8c font-size-14 lh22 mb4">
                      {{ $t('pc_o2o_lbl_application_time').d('申请时间') }}
                    </div>
                    <div
                      v-if="identificationDetail.startTime"
                      class="font-size-14 color-26 lh22"
                    >
                      {{ formateDate(identificationDetail.startTime) }}
                      {{ $t('pc_o2o_lbl_to').d('至') }}
                      {{ formateDate(identificationDetail.endTime) }}
                    </div>
                    <div v-else class="font-size-14 color-26 lh22">--</div>
                  </div>
                </div>
              </div>
              <div class="w50per">
                <div class="color-8c font-size-14 lh22 mb4">
                  {{ $t('pc_o2o_lbl_finish_standard').d('完成标准') }}
                </div>
                <div class="font-size-14 lh22 color-26 mb16">
                  {{ finishTypeText || '--' }}
                </div>
              </div>
            </div>

            <div class="color-8c font-size-14 lh22 mb4 mt8">
              {{ $t('pc_o2o_lbl_evaluation_desc').d('鉴定说明') }}
            </div>
            <div class="color-26 lh22 font-size-14 word-wrap-break">
              {{ identificationDetail.description || '--' }}
            </div>

            <!-- 上传附件 -->
            <div v-if="identificationDetail.allowUploadFile" class="mt16">
              <div class="color-gray-10 font-size-14 lh22  flex-center-start">
                <span>{{ $t('pc_biz_uploadimg_btn_upload' /* 上传附件 */) }}</span>
                <i v-if="identificationDetail.requiredUploadFile" class="required-dot ml4"></i>
              </div>
              <upload
                ref="uploadList"
                :attach-list.sync="attachList"
                :is-view-mode="applyBtnDisabled"
                class="mt8"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white pb32 radius4 center-block" :class="{'w1200 mb24 pt32': !inner}">
        <div :class="{'center-wrap pl24 pr24': inner}">
          <div class="mb96" :class="{'o2o-pc-identification-content': !inner}">
            <div class="color-26 weight-bold lh26 font-size-18 mb24">
              {{ $t('pc_o2o_lbl_identify_result').d('鉴定结果') }}
              <span v-if="isIdntification" class="color-gray-9 font-normal lh24 font-size-14">
                {{ isAvgResult ? $t('pc_biz_appraise_identify_rules2' /* 按鉴定人平均分为准 */) : $t('pc_biz_appraise_identify_ruletip' /* 以最后一人鉴定结果为准 */) }}
              </span>
            </div>
            <template v-if="isIdntification">
              <div
                :style="{ backgroundColor: identificationData.bgColor }"
                class="flex-between-center radius4 pv12 ph16 mb16 relative"
                :class="{
                  'cursor-p': !isAvgResult
                }"
                @click="!isAvgResult && viewIdentifyQuestion()"
              >
                <div class="flex-mid">
                  <span v-if="!hideScore" class="font-size-14 lh24 text-75">{{
                    appraisalTypeData.passText
                  }}</span>
                  <span v-if="!hideScore" class="ml8 color-26 weight-bold font-size-16 lh24 mr8">{{ identificationData.rate }}{{ appraisalTypeData.unit }}</span>
                  <span
                    class="font-size-14 lh22"
                    :style="{ color: identificationData.textColor }"
                  >{{ identificationData.text }}</span>
                </div>
                <yxtf-button
                  v-if="!isAvgResult"
                  class="layout-self-end color-26"
                  size="small"
                  plain
                >
                  {{
                    $t('pc_o2o_lbl_viewIdentificationQusetion').d('查看鉴定问卷')
                  }}
                </yxtf-button>
              </div>
              <div class="color-59 font-size-14 lh22 mb20 word-wrap-break">
                {{ identificationData.appraisalContent }}
              </div>
              <div v-if="!isAvgResult" class="color-8c lh20 font-size-12">
                <span>{{ $t('pc_o2o_lbl_currentIdentifyUser').d('鉴定人：')
                }}<yxtbiz-user-name :name="identificationData.reviewUser" /></span>
                <span class="ml32">{{ $t('pc_o2o_lbl_currentIdentifyTime').d('鉴定时间：')
                }}{{ (identificationData.appraisalTime) }}</span>
              </div>
            </template>

            <div
              v-else
              :style="{ backgroundColor: appliedData.bgColor }"
              class="radius4 pv12 ph16 color-59 lh22 font-size-14"
            >
              {{ appliedData.text }}
            </div>
          </div>
          <div v-if="!hideBtn" class="o2o-flex-center-center">
            <yxtf-button
              size="larger"
              type="primary"
              :loading="btnLoading"
              :disabled="applyBtnDisabled"
              @click="appliedIdentification"
            >
              {{ btnText }}
            </yxtf-button>
          </div>
          <p v-if="startTimeNotAssigned" class="text-center lh-1 mt16 c-8c">{{ identificationDetail.startTime | formatTime }} {{ $t('pc_pd_lbl_ident_submit') }}</p>
        </div>
      </div>
      <div :class="{'bg-white pv32 radius4 center-block': isShowRecords, 'w1200': isShowRecords && !inner, 'mb24': !inner}">
        <div :class="{'o2o-pc-identification-content': !inner, 'center-wrap pl24 pr24': inner}">
          <template v-if="!hideFlow">
            <div v-if="isShowRecords" class="color-26 weight-bold lh26 font-size-18 mb24">
              {{ $t('pc_o2o_lbl_identify_records' /* 鉴定记录 */) }}
            </div>
            <yxtpd-identify-flow
              v-show="isShowRecords"
              v-if="shouldInitRecords"
              ref="records"
              :info="identificationDetail"
              :project-status="task.projectStatus"
              :target-id="identificationDetail.appraisalId"
              :user-id="userId"
              :project-id="task.projectId"
              :bizz-type="6"
              :task-id="taskId"
              :hide-score="hideScore"
              hide-title
              web
              :inner="inner"
              @viewSurvey="viewIdentifyQuestion"
              @dataLoaded="recordsLoaded"
            />
          </template>
        </div>
      </div>
      <ojt-tip
        ref="ojtTip"
        :show-continue="canTransfer"
        :project-id="projectId"
        :audit-status="auditStatus"
        :student-choose-teacher="!!identificationDetail.studentChooseTeacher"
        :ojt-teacher-info="ojtTeacherInfo"
        @apply="postIdentification"
        @update="afterOjtSelect"
      />
    </div>
    <yxt-ulcd-sdk-surveying
      v-if="checkSurvey"
      :full-screen="false"
      :scroll-inner="false"
      :radius="false"
      :params="surveyParams"
      :step="3"
    />
  </div>
</template>

<script>
import mixin from 'packages/_mixins/task';
import {
  getAppraisalDetail,
  postAppraisalApply,
  getSurveyUrlToStu,
  getAuditTeacherStatus,
  getStuTeachers
} from './service';
import OjtTip from './components/ojtTip.vue';
import { dateFormat } from 'packages/_utils/core/date-format';
import { getQueryString } from './utils';
import { getTimeStamp } from 'packages/_utils/core/utils.js';
import upload from './components/upload.vue';

export default {
  name: 'YxtUlcdSdkO2oIdentTask',
  components: {
    OjtTip,
    upload
  },
  mixins: [mixin],
  props: {
    params: {
      type: Object,
      default: () => ({
        gwnlUrl: '',
        pid: '',
        btid: '',
        taskType: '',
        origin: '',
        from: ''
      })
    },
    inner: Boolean
  },
  data() {
    return {
      startTimeNotAssigned: false,
      checkSurvey: false,
      surveyParams: {},
      pid: null,
      loading: false, // 页面loading
      btnLoading: false,
      isIdentification: true,
      identificationDetail: {
        appraisalStatus: null, // null 待申请 0 待鉴定 1 已鉴定
        appraisalName: '',
        startTime: '',
        endTime: '',
        description: '',
        appraisalType: 0, // 鉴定方式，0-勾选模式、1-打分模式
        appraisalContent: '' // 鉴定评语
      }, // 鉴定详情
      gwnlUrl: this.params.gwnlUrl || '', // 人才发展url
      isShowRecords: false,
      auditStatus: 0,
      ojtTeacherInfo: {
        canChooseTe: null,
        ojtMode: null,
        period: null,
        projectId: this.params.pid
      }, // 带教导师相关数据
      hideScore: false,
      attachList: []
    };
  },
  watch: {
    params: {
      handler(data) {
        this.taskId = data.taskId;
        this.init();
      },
      immediate: true,
      deep: true
    },
    shouldInitRecords: {
      immediate: true,
      handler: function(e, d) {
        if (e) {
          this.$nextTick(() => {
            this.getReviewHistory();
          });
        }
      }
    }
  },
  computed: {
    projectId() {
      return this.params.pid;
    },
    batchId() {
      return this.params.btid;
    },
    bizData() {
      return { taskId: this.taskId, projectId: this.projectId, batchId: this.batchId, gwnlUrl: this.gwnlUrl };
    },
    fromStudyRecord() {
      return this.params.from === 'studyRecord';
    },
    taskType() {
      return this.params.taskType;
    },
    hideBtn() {
      return this.params.origin === 'studyReocrd' || ![0, 1].includes(this.identificationDetail.processOnlyOjt);
    },
    // 是否已鉴定过
    isIdntification() {
      return this.identificationDetail.appraisalStatus === 1;
    },
    // 申请状态
    appliedData() {
      if ((this.identificationDetail.appraisalStatus === 0 || this.identificationDetail.appraisalStatus === 2) && !this.hideFlow) {
        return {
          text: this.$t('pc_o2o_lbl_to_be_identified').d('待鉴定'),
          bgColor: '#F0F6FF'
        };
      }
      return {
        text: this.$t('pc_o2o_lbl_not_applied').d('未申请'),
        bgColor: '#fafafa'
      };
    },
    isAvgResult() {
      return this.identificationDetail.ruleType === 2;
    },
    // 底部按钮文字
    btnText() {
      if (this.isIdntification && this.identificationDetail.passed !== 1) {
        return this.$t('pc_o2o_lbl_reIentify').d('重新鉴定');
      }
      if (this.identificationDetail.processOnlyOjt) {
        return this.$t('pc_o2o_lbl_appliedIdentify_te').d('带教导师鉴定');
      }
      return this.$t('pc_o2o_lbl_appliedIdentify').d('申请鉴定');
    },
    // 完成标准
    finishTypeText() {
      return this.identificationDetail.finishStandard === 1
        ? this.$t(
          'pc_o2o_lbl_identify_finish_standard_one'
        ).d('鉴定合格算完成任务')
        : this.$t(
          'pc_o2o_lbl_identify_finish_standard_other'
        ).d('学员申请鉴定算完成任务');
    },
    // 勾选模式相关内容
    appraisalTypeData() {
      return this.identificationDetail.appraisalType === 0
        ? {
          passText: this.$t('pc_o2o_lbl_passpercent').d('通过率'),
          unit: '%'
        }
        : {
          passText: this.$t('pc_o2o_lbl_getscore').d('得分'),
          unit: this.$t('pc_o2o_lbl_score').d('分')
        };
    },
    // 鉴定的结果
    identificationData() {
      const {
        passed,
        appraisalTime,
        appraisalResult,
        realAppraisalUserName,
        appraisalContent
      } = this.identificationDetail;
      return passed === 1
        ? {
          textColor: 'rgba(109, 205, 61, 1)',
          bgColor: '#F5FAF6',
          text: this.$t('pc_o2o_lbl_quaified').d('合格'),
          appraisalTime: this.formateDate(appraisalTime, 'yyyy-MM-dd hh:mm'),
          rate: appraisalResult,
          reviewUser: realAppraisalUserName,
          appraisalContent
        }
        : {
          textColor: '#F5222D',
          bgColor: '#FFF8F6',
          text: this.$t('pc_o2o_lbl_notquaified').d('不合格'),
          appraisalTime: this.formateDate(appraisalTime, 'yyyy-MM-dd hh:mm'),
          rate: appraisalResult,
          reviewUser: realAppraisalUserName,
          appraisalContent
        };
    },
    applyBtnDisabled() {
      if (this.hideFlow) {
        return false;
      }
      if (this.identificationDetail.appraisalStatus === 0 || this.identificationDetail.appraisalStatus === 2 || this.startTimeNotAssigned) {
        // 待鉴定、鉴定中禁用
        return true;
      } else {
        // 其他状态
        if (this.identificationDetail.passed === 1) {
          // 通过禁用
          return true;
        }
      }
      return false;
    },
    shouldInitRecords() {
      // 初始化记录的时机
      return this.canViewDeitail && this.task.projectId && this.identificationDetail.appraisalId && !this.checkSurvey;
    },
    isOjtAndWithoutTeacher() {
      return this.identificationDetail.processOnlyOjt && !this.identificationDetail.hasTeacher;
    },
    canTransfer() {
      // noAuditer 批阅人为空时：0自动转交（项目负责人） 1转交给指定成员处理 2自动通过 5：不转交"
      return this.identificationDetail.noAuditer !== 5;
    },
    hideFlow() {
      return this.identificationDetail.applicationMethod === 0;
    }
  },
  created() {
    this.$root.$on('GOBACK', this.goBack);
  },
  beforeDestroy() {
    this.setBreadcrumbUpdate(false);
    this.$root.$off('GOBACK', this.goBack);
  },
  methods: {
    goBack() {
      this.checkSurvey = false;
      this.setBreadcrumbUpdate(false);
    },
    setBreadcrumbUpdate(v) {
      const breadcrumb = {
        show: v,
        list: [{name: this.$t('pc_o2o_lbl_ident_task').d('鉴定任务'), eventName: 'GOBACK'}, {name: this.$t('pc_o2o_lbl_questionnaire')}]
      };
      this.$root.$emit('SETBREADCRUMBUPDATE', breadcrumb);
    },
    formateDate: dateFormat,
    // 初始化
    initData() {
      this.getIdentification();
    },
    // 获取鉴定详情
    getIdentification() {
      this.loading = true;
      getAppraisalDetail(this.bizData)
        .then(res => {
          this.identificationDetail = res;
          this.startTimeNotAssigned = (res.startTime && getTimeStamp(res.startTime) > Date.now());
          this.attachList = res.attachments || [];

          this.hideScore = res.hideScore; // 隐藏分数API对接
        })
        .finally(() => (this.loading = false));
    },
    // 查看鉴定调查问卷
    viewIdentifyQuestion(data) {
      if (data) {
        return this.surveyView(data);
      }
      getSurveyUrlToStu(
        this.identificationDetail.appraisalId,
        window.localStorage.userId,
        this.projectId,
        this.identificationDetail.processNodeId,
        this.batchId
      ).then(res => {
        const url = res.surveyUrl;
        const uamId = getQueryString(url, 'uamId');
        const uaId = getQueryString(url, 'uaId');
        this.surveyView({uaId, uamId});
      });
    },
    surveyView(data) {
      const { uamId, uaId } = data;
      this.surveyParams = {
        uamId,
        uaId,
        hideBack: true
      };
      this.checkSurvey = true;
      this.setBreadcrumbUpdate(this.checkSurvey);
    },
    // 申请鉴定接口
    postIdentification() {
      this.btnLoading = true;
      postAppraisalApply({
        appraisalId: this.identificationDetail.appraisalId,
        orgId: window.localStorage.orgId,
        projectId: this.projectId,
        batchId: this.params.btid,
        attachments: (this.attachList || []).map(item => {
          return {
            ...item,
            trainingId: this.projectId,
            type: 0,
            attachmentName: item.name
          };
        })
      }, { deferred: true })
        .then(res => {
          this.$message.success(this.$t('pc_o2o_lbl_applyIsSubmit').d('申请已提交'));
          this.getIdentification();
          this.getReviewHistory();
          this.$emit('updateProgress', 3);
        }).catch(error => {
          this.$message.error(error.message);
        })
        .finally(() => (this.btnLoading = false));
    },

    // 申请鉴定
    appliedIdentification() {
      // if (
      //   this.identificationDetail.startTime &&
      //   new Date(this.identificationDetail.startTime.replace(/-/g, '/')) >
      //     new Date()
      // ) {
      //   return this.$message(
      //     this.$t('pc_o2o_lbl_applicationTimeNoUp').d('未到申请时间')
      //   );
      // }

      if (this.$refs.uploadList) {
        const progressList = this.$refs.uploadList.progressIds;
        if (progressList.length) {
          this.$message.error(this.$t('pc_kng_biz_lbl_uploading' /* 上传中，请稍等 */));
          return;
        }

        const errorList = this.$refs.uploadList.errorList;
        if (errorList.length) {
          this.$message.error(this.$t('pc_biz_o2o_upload_remove_file' /* 存在上传失败的文件，请删除后再申请鉴定 */));
          return;
        }
      }

      // 判断附件是否必填字段
      const { allowUploadFile, requiredUploadFile } = this.identificationDetail;
      const list = this.attachList;
      if (allowUploadFile && requiredUploadFile && !list.length) {
        this.$message.error(this.$t('pc_biz_msg_ident_msg_must_upload_attachment' /* 申请鉴定必须上传附件 */));
        return;
      }

      // 批阅设置带教导师并且还没选择导师，走带教导师逻辑
      if (this.isIdntification) {
        // 是否已鉴定过
        this.$confirm(
          '',
          this.$t('pc_o2o_lbl_sureAppliedIdentifyAgain').d(
            '确定要重新申请鉴定吗？'
          ),
          {
            confirmButtonText: this.$t('pc_o2o_btn_confirm'),
            cancelButtonText: this.$t('pc_o2o_btn_cancel' /* 取消 */),
            type: 'info'
          }
        ).then(() => {
          if (this.isOjtAndWithoutTeacher) {
            // 当鉴定任务中，任何一级鉴定内鉴定角色包含“带教导师”但此学员又无带教导师时，点击【申请鉴定】需弹出二次确定弹窗
            this.ojtTipShow();
          } else {
            this.postIdentification();
          }
        });
      } else {
        if (this.isOjtAndWithoutTeacher) {
          // 当鉴定任务中，任何一级鉴定内鉴定角色包含“带教导师”但此学员又无带教导师时，点击【申请鉴定】需弹出二次确定弹窗
          this.ojtTipShow();
        } else {
          this.postIdentification();
        }
      }
    },
    async ojtTipShow() {
      // 重新获取详情信息，确保数据及时性
      const detailRes = await getAppraisalDetail(this.bizData).catch(() => {});
      if (detailRes) {
        this.identificationDetail = detailRes;
        if (this.identificationDetail.hasTeacher) {
          // 如果重新获取详情后已经有导师，则直接申请,不用打开弹窗了
          this.postIdentification();
          return;
        }
      }
      // 获取带教信息
      const ojtRes = await getStuTeachers(this.projectId, localStorage.getItem('userId')).catch(e => {});
      this.ojtTeacherInfo = {
        canChooseTe: ojtRes.canChooseTe,
        ojtMode: ojtRes.ojtMode,
        period: ojtRes.period,
        projectId: this.projectId
      };
      if (!this.identificationDetail.studentChooseTeacher && this.identificationDetail.auditOpened) {
        // 师徒互选判断审核中, (数据库 师徒互选-学员选择导师 0:选择 1：没选择 )
        await this.getAuditStatus();
      } else {
        // 未开启审核认为无导师
        this.auditStatus = false;
      }
      this.$refs.ojtTip && this.$refs.ojtTip.show();
    },
    async getAuditStatus() {
      const res = await getAuditTeacherStatus(this.taskId).catch(() => {});
      this.auditStatus = res.auditStatus;
    },
    // 加载完记录数据
    recordsLoaded(data) {
      if (data && data.length && !this.fromStudyRecord) {
        this.isShowRecords = true;
      } else {
        this.isShowRecords = false;
      }
    },
    // 获取批阅历史
    getReviewHistory() {
      this.$nextTick(() => {
        this.$refs.records && this.$refs.records.getReviewHistory();
      });
    },
    async afterOjtSelect() {
      this.$refs.ojtTip && this.$refs.ojtTip.hide();
    }
  }
};
</script>
