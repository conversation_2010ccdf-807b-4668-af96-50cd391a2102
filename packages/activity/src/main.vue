<template>
  <div
    v-loading="loading"
    class="yxtulcdsdk-activity o2oplayframe-task-container yxtulcdsdk-ulcdsdk"
  >
    <task-detail-container>
      <template slot="title">
        <yxtf-tooltip
          class="item"
          :open-filter="true"
          :content="task.name"
          placement="top"
        >
          <span class="ulcdsdk-ellipsis-2 ulcdsdk-break"> {{ task.name }}</span>
        </yxtf-tooltip>

      </template>
      <div v-if="isGetDetail">
        <div v-if="task && task.id">
          <!-- 说明 -->
          <div class="mb24">
            <div class="standard-size-14 color-gray-7">{{ $t('pc_o2o_activityInformation'/* 活动简介 */) }}</div>
            <wrap-line
              v-if="task.description"
              line-height="22px"
              :content="task.description"
              :hide-on-extend="hideOnExtend"
              :row="row"
              class="standard-size-16 color-gray-9"
            >
              <span
                slot="right-icon"
                class="v-top ml2 color-primary hand"
                @click="extendMore"
              >
                {{ hideOnExtend ? $t('pc_o2o_btn_expand') : $t('pc_o2o_btn_stow') }}<i :class="hideOnExtend?'yxt-icon-arrow-down':'yxt-icon-arrow-up'"></i>
              </span>
            </wrap-line>
            <div
              v-else
              class="standard-size-16 color-gray-9"
            >
              {{ $t('pc_o2o_tip_nodatas') }}
            </div>
          </div>
          <!-- 地址 -->
          <div class="mb16">
            <div class="standard-size-14 color-gray-7 mb4">{{ $t('pc_o2o_lbl_classaddress') }}</div>
            <div
              v-if="task.provinceName"
              class="standard-size-14 break"
            >
              <span v-if="task.provinceName">{{ task.provinceName }}</span><span v-if="task.cityName">{{ task.cityName }}</span><span v-if="task.districtName">{{ task.districtName }}</span>{{ task.address }}
            </div>
            <div
              v-else
              class="standard-size-14"
            >
              {{ $t('pc_o2o_tip_nodatas') }}
            </div>
          </div>
          <!-- 活动评分 -->
          <div class="mb40">
            <div class="standard-size-14 color-gray-7 mb4">{{ $t('pc_o2o_lbl_activityrating') }}</div>
            <div
              v-if="task.scoreType === 0"
              class="standard-size-14"
            >
              {{ $t('pc_o2o_lbl_activityscorelable',[task.totalScore,task.passScore]) }}
            </div>
            <div
              v-else
              class="standard-size-14"
            >
              {{ $t('pc_o2o_lbl_isqualified') }}
            </div>
          </div>

          <!-- 结果评分 -->
          <div>
            <div class="standard-size-18 font-bolder mb16">{{ $t('pc_o2o_lbl_activityachievements') }}</div>
            <div class="font-size-18 mt4">
              <template v-if="task.taskResult && task.taskResult.length > 0&&task.taskResult[0].status>1">
                <div
                  v-if="task.scoreType === 0"
                  class="lh22"
                >
                  <div class="layout-flex layout-align-center layout-justify-between">
                    <div
                      v-if="task.taskResult[0].score"
                      :class="scoreClass"
                    >
                      {{ task.taskResult[0].score }}
                    </div>
                    <o2of-svg
                      class="pull-right"
                      :name="svgName"
                      width="64px"
                      height="60px"
                      front
                    />
                  </div>
                  <div
                    v-if="task.taskResult[0].reply"
                    class="mt24 lh30 break text-pre"
                  >{{ task.taskResult[0].reply }}</div>
                </div>
                <div v-else>
                  <o2of-svg
                    :name="svgName"
                    width="64px"
                    height="60px"
                    front
                  />
                  <div
                    v-if="task.taskResult[0].reply"
                    class="mt24 lh30 break text-pre"
                  >{{ task.taskResult[0].reply }}</div>
                </div>
              </template>
              <div
                v-else
                class="mt24"
              >
                {{ $t('pc_o2o_lbl_nocommment') }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </task-detail-container>
  </div>
</template>

<script>
import mixin from '../../_mixins/task.js';
import scrollTop from '../../_mixins/scroll-top';
import taskDetailContainer from '../../_components/task-detail-container.vue';
import wrapLine from '../../_components/wrap_line.vue';
export default {
  name: 'YxtUlcdSdkActivity',
  mixins: [mixin, scrollTop],
  components: {
    taskDetailContainer,
    wrapLine
  },
  data() {
    return {
      hideOnExtend: true,
      row: 5
    };
  },
  created() {
    this.init();
  },
  computed: {
    scoreClass() {
      return this.task.taskResult[0].passed === 1 ? 'yxtf-color-success' : 'yxtf-color-danger';
    },
    svgName() {
      return this.task.taskResult[0].passed === 1 ? 'biz/tag-qualified' : 'biz/tag-unqualified';
    }
  },
  methods: {
    extendMore() {
      this.row = this.row ? 0 : 5;
      // 展开更多
      this.hideOnExtend = !this.hideOnExtend;
    }
  }
};
</script>
<style lang="scss">
.activity-detail__pre {
  margin: 0;
  white-space: pre-line;
  word-break: break-all;
}
</style>

