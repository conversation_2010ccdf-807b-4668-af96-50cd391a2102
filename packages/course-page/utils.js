import { TitleUtil } from 'yxt-biz-pc';
import {KNG_TYPE, KNG_FILE_TYPE} from 'packages/course-player/enum';
export const handleFileSize = (value) => {
  if (!value) {
    return '0 Bytes';
  }

  const unitArr = [' Bytes', ' KB', ' MB', ' GB', ' TB'];
  let index = 0;
  const srcsize = parseFloat(value);
  index = Math.floor(Math.log(srcsize) / Math.log(1024));
  let size = srcsize / Math.pow(1024, index);
  size = size.toFixed(2); // 保留的小数位数
  return [size + unitArr[index], size, unitArr[index]];
};

/**
 * 下载
 * @param url
 * @param name
 */
export const downloadUrl = (url, name) => {
  const downloadElement = document.createElement('a');
  downloadElement.href = url;
  downloadElement.download = name || true;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  // 下载完成移除元素
  document.body.removeChild(downloadElement);
};

export const staticBaseUrl = window.feConfig.common.staticBaseUrl || 'https://stc.yxt.com/';
// kng图标的svg remote-url
export const kngFileTypeUrl = `${staticBaseUrl}ufd/55a3e0/kng/pc/svg/kngicons/`;
// 获取知识图标，包含考试练习
export const getKngFileTypeIcon = (fileType, kngType) => {
  // kngType 处理fileType不存在的异常情况
  if (kngType === KNG_TYPE.EXAMINE) {
    return KNG_FILE_TYPE.QUESTIONNAIRE;
  } else if (kngType === KNG_TYPE.PRACTICE) {
    return KNG_FILE_TYPE.PRACTICE;
  } else if (kngType === KNG_TYPE.EXAM) {
    return KNG_FILE_TYPE.EXAM;
  } else if (kngType === KNG_TYPE.SURVEY) {
    return KNG_FILE_TYPE.SURVEY;
  } else if (kngType === KNG_TYPE.DISCUSS) {
    return KNG_FILE_TYPE.DISCUSS;
  }
  let type = (fileType || KNG_FILE_TYPE.NEW_LINK).toLowerCase();
  // 不在枚举内的类型用unknow
  if (type !== KNG_FILE_TYPE.NEW_LINK && !Object.values(KNG_FILE_TYPE).includes(type)) {
    type = KNG_FILE_TYPE.NEW_LINK;
  }
  return type;
};

/**
 * 生成uuid
 * @returns
 */
export const createUuid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// 优先通过pageCode去匹配对应的title
export const setDocumentTitle = (title, pageCode = '') => {
  try {
    TitleUtil.setTitleByPageCode(pageCode, title);
  } catch (e) {}
};
