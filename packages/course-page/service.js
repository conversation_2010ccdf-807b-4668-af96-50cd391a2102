import { kngApi, utilityApi, udpApi, studylogApi, coreApi} from 'packages/api';

const catchTools = async(rq) => {
  let res, error;
  try {
    res = await rq();
  } catch (e) {
    error = e;
  }
  return Promise.resolve([res, error]);
};
// 获取内容标签
export const getTags = id => catchTools(() => utilityApi.get(`tagtargets/${id}/orgtags`));

// 当前集团所有人打过的标签列表
export const getTagsInGroup = id => catchTools(() => utilityApi.get(`tagtargets/${id}/grouptags`));
// 能力标签
export const getSkillTags = id => catchTools(() => kngApi.get(`kngSkillTagMap/${id}`));
// 课程自定义属性展示设置
export const getAttributeInfo = (id, params = {}) => kngApi.get(`/attribute/detail/info/${id}`, {params});
// 获取附件下载地址
export const getKngAttachmentUrl = id => kngApi.get(`/upload/download/${id}`);
// 获取多维度开关
export const getMultiDimensionSwitch = orgId => kngApi.get(`/multi-eval/group/switch/${orgId}`);
// 获取多维度
export const getMultiDimension = orgId => kngApi.get(`/multi-eval/group/user/items/${orgId}`);
// 获取评论情况
export const getMultiDimensionResult = id => kngApi.get(`/multi-eval/group/user/${id}`);
// 评论
export const postComment = data => kngApi.post('/comment', data);
// 获取用户账号信息
export const getUserInfo = () => udpApi.get('users/self');
// 获取知识评论个数
export const getCommentCount = (id) => kngApi.get(`/comment/${id}/count`);
// 获取评论列表
export const getCommentList = (id, params) => kngApi.get(`/comment/${id}`, {params});
// 删除评论
export const deleteComment = id => kngApi.delete(`/comment/${id}`);
// 回复评论
export const postReplyComment = data => kngApi.post('comment/reply?usebody=true', data);
// 获取回复列表
export const getReplyCommentList = (id, commentId, params) => kngApi.get(`/comment/reply/${id}/${commentId}`, {params});

// 查询笔记数量
export const getNoteCount = (data) => kngApi.post('/studynote/count', data);
// 查询笔记列表
export const getNoteList = (params, data) => kngApi.post('/studynote/web/pageList', data, {params});
// 笔记点赞
export const doNotePraise = (noteId, supported) => kngApi.put(`/studynote/web/support/${noteId}/${supported}`);
// 删除笔记
export const deleteNote = id => kngApi.put(`/studynote/web/del/${id}`);
// 获取一起学人员
export const getStudyTogetherList = data => kngApi.post('knowledge/learnTogether', data);

// 日志接口
export const studyLog = data => studylogApi.post('logs', data);
// 获取机构参
export const getOrgParameter = (code) => coreApi.get(`orgparsetting/code?code=${code}&orgId=${localStorage.orgId}`);
// 学习记录
export const postUserViewRecord = data => kngApi.post('kngUserViewRecord', data);
// 查询课程列表展示信息
export const getCourseListInfo = data => kngApi.get('/kv/course/attr/show');

// 预加载，提高单接口速度
export const preloadKngInfo = data => catchTools(() =>kngApi.post('study/knginfos/preload', data));
export const preloadSubmitStudy = data => catchTools(() =>kngApi.post('study/submit/preinit', data));

// 贡献者列表
export const getContributors = (kngId) => kngApi.post(`knowledge/${kngId}/contributors`);
