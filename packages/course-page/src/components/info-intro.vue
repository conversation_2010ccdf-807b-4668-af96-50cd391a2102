<!-- 创建时间2023/02/24 16:30:47 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课程简介 -->
<template>
  <div class="mb48">
    <div class="standard-size-18 yxt-weight-5 mb16">{{ $t('pc_kng_project_tit_kng'/** 课程详情*/) }}</div>
    <course-operation :class="tagList.length?'mb16':'mb24'" type="score">
      <div v-if="!hadContent" slot="empty" class="color-gray-9 standard-size-14">{{ $t('pc_kng_detail_lbl_no_intro'/**暂无简介 */) }}</div>
    </course-operation>
    <div v-if="tagList.length" class="mb8">
      <yxt-tag
        v-for="(item) in tagList"
        :key="item.id"
        size="small"
        class="mr10 tag-info mb16"
        type="info"
      >
        {{ item.name }}
      </yxt-tag>
    </div>
    <info-users class="mb24"></info-users>
    <div v-if="base.showDesc && detail.description" class="mb24">
      <div class="yxt-weight-5 mb12">{{ base.desc.attrName }}</div>
      <expand-text :text="detail.description" />
    </div>
    <div v-if="base.showAttachment && base.attachment.kngAttachSimpleBeans && base.attachment.kngAttachSimpleBeans.length" class="mb24">
      <div class="standard-size-16 yxt-weight-5 mb8">{{ base.attachment.attrName }}</div>
      <attachments :attachments="base.attachment.kngAttachSimpleBeans" />
    </div>
    <div v-for="item in customAttrList" :key="item.id" class="mb24">
      <div class="standard-size-14 yxt-weight-5 ellipsis mb12">{{ item.attrName }}</div>
      <div class="standard-size-14 color-gray-9 ellipsis-2"> {{ item.value || '--' }}</div>
    </div>
  </div>
</template>

<script>
import {getSkillTags, getTags, getTagsInGroup} from '../../service';
import {COURSE_ATTRIBUTE} from '../../enum';
import ExpandText from './widgets/expand-text.vue';
import Attachments from './widgets/attachments.vue';
import CourseOperation from 'yxt-ulcd-sdk/packages/course-player/src/components/course-operation.vue';
import InfoUsers from './info-users.vue';
export default {
  components: { ExpandText, Attachments, CourseOperation, InfoUsers },
  name: 'InfoIntro',
  inject: ['getDetail', 'getAttributeInfo', 'getMode', 'getIsUsedByKng'],
  data() {
    return {
      contentTagList: [],
      skillTagList: []
    };
  },
  computed: {
    mode() {
      return this.getMode();
    },
    isUsedByKng() {
      return this.getIsUsedByKng();
    },
    detail() {
      return this.getDetail();
    },
    attributeInfo() {
      return this.getAttributeInfo();
    },
    baseAttributeInfo() {
      return this.attributeInfo.attributeInfoResponses || [];
    },
    customAttributeInfo() {
      return this.attributeInfo.customInfoResponses || [];
    },
    customAttrList() {
      return this.customAttributeInfo.filter(item => item.attrShow);
    },
    base() {
      const contentTag = this.baseAttributeInfo.find(item => item.attrKey === COURSE_ATTRIBUTE.CONTENT_TAG) || {};
      const skillTag = this.baseAttributeInfo.find(item => item.attrKey === COURSE_ATTRIBUTE.SKILL_TAG) || {};
      const desc = this.baseAttributeInfo.find(item => item.attrKey === COURSE_ATTRIBUTE.DESCRIBE) || {};
      const attachment = this.attributeInfo.kngCourseAttachResponse || {};
      return {
        desc,
        attachment,
        showContentTag: contentTag.attrShow,
        showSkillTag: skillTag.attrShow,
        showDesc: desc.attrShow,
        showAttachment: attachment.attrShow
      };
    },
    tagList() {
      let list = [];
      if (this.base.showContentTag) list = list.concat(this.contentTagList);
      if (this.base.showSkillTag) list = list.concat(this.skillTagList);
      return list;
    },
    // 简介是否有内容
    hadContent() {
      return this.tagList.length ||
      (this.base.showDesc && this.detail.description) ||
      (this.base.showAttachment && this.base.attachment.kngAttachSimpleBeans && this.base.attachment.kngAttachSimpleBeans.length) ||
      this.customAttrList.length;
    }
  },
  created() {
    if (this.mode === 'normal') {
      this.setContentTagList();
      this.setSkillTagList();
    }
  },
  methods: {
    async setContentTagList() {
      const {shareFlag, sourceKngId, id} = this.detail;
      // 内容标签
      const api = shareFlag === 2 ? getTagsInGroup : getTags;
      const [contentRes] = await api(sourceKngId || id);
      if (contentRes && contentRes.datas) {
        this.contentTagList = contentRes.datas.map(item => ({id: item.id, name: item.tagName}));
      }
    },
    async setSkillTagList() {
      // 能力标签
      const [skillRes] = await getSkillTags(this.detail.id);
      if (skillRes && skillRes.length) {
        this.skillTagList = skillRes.map(item => ({id: item.skillTagId, name: item.skillTagName}));
      }
    }
  }
};
</script>
