<!-- 创建时间2023/03/01 08:58:56 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：底部信息区，右侧的其他项目 -->
<template>
  <div class="yxtulcdsdk-course-page__users">
    <!-- 贡献者等 -->
    <div v-if="userConfigs.length">
      <div v-for="(item,index) in userConfigs" :key="index">
        <div class="yxt-weight-5 standard-size-16" :class="{'mt24':index>0}">{{ item.label }}</div>
        <div class="yxtulcdsdk-course-page__users-list mt12">
          <div
            v-for="(user,userIndex) in item.users"
            :key="userIndex"
            class="yxtulcdsdk-flex-center"
            @click="toUserCenter(user)"
          >
            <yxtf-portrait
              v-if="user.isUser"
              size="48px"
              class="yxtulcdsdk-flex-shrink-0 mr12"
              :img-url="user.imgUrl"
              :username="user.userName"
            />
            <div class="ulcdsdk-break-word standard-size-12">
              <div class="standard-size-14 yxt-weight-5">
                <yxtbiz-user-name :name="user.userName" />
              </div>
              <div v-if="user.orgName" class="mt4">
                <yxtf-tag size="mini" type="info"> {{ user.orgName }} </yxtf-tag>
              </div>
              <div class="color-gray-7 mt4">
                <yxtbiz-dept-name :name="user.dept" />
              </div>
              <div v-if="user.levelName" class="color-gray-7 mt4">{{ user.levelName }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InfoUsers',
  inject: ['getDetail', 'getAttributeInfo'],
  computed: {
    detail() {
      return this.getDetail();
    },
    attributeInfo() {
      return this.getAttributeInfo();
    },
    // 贡献者
    contributor () {
      return this.attributeInfo.contributorsResponseList || []
    },
    // 主讲
    lectures () {
      return this.attributeInfo.lecturesResponseList || []
    },
    // 辅讲
    assistLectures () {
      return this.attributeInfo.counseLecturesResponseList || []
    },
    // 作者
    author () {
      return this.attributeInfo.authorResponseList || []
    },
    // 上传者
    creator() {
      return this.attributeInfo.createUserResponse || {};
    },
    customer() {
      return (this.attributeInfo.customUserInfoResponses || []).filter(item => item.attrShow);
    },
    userConfigs() {
      const list = []
      if (this.contributor.length && this.contributor[0].attrShow) {
        const item = {
          label: this.contributor[0].attrName,
          users: []
        }

        for (let i = 0; i < this.contributor.length; i++) {
          const { canJump, selectRange, contributorsId, contributorsName, contributorsImage, contributorsDeptName } = this.contributor[i]
          item.users.push({
            canJump,
            isUser: !!selectRange,
            selectRange,
            userId: contributorsId,
            userName: contributorsName,
            imgUrl: contributorsImage,
            dept: contributorsDeptName,
            orgName: this.getOrgNameTag(this.contributor[i])
          })
        }
        list.push(item)
      }
      if (this.lectures.length && this.lectures[0].attrShow) {
        const item = {
          label: this.lectures[0].attrName,
          users: []
        }
        for (let i = 0; i < this.lectures.length; i++) {
          const { canJump, selectRange, lecturesId, userName, userImage, userDept, levelName } = this.lectures[i]
          item.users.push({
            levelName,
            canJump,
            isUser: !!selectRange,
            selectRange,
            userId: lecturesId,
            userName: userName,
            imgUrl: userImage,
            dept: userDept,
            orgName: this.getOrgNameTag(this.lectures[i])
          })
        }
        list.push(item)
      }
      if (this.assistLectures.length && this.assistLectures[0].attrShow) {
        const item = {
          label: this.assistLectures[0].attrName,
          users: []
        }
        for (let i = 0; i < this.assistLectures.length; i++) {
          const { canJump, selectRange, counseLorteacherIds, userName, userImage, userDept, levelName } = this.assistLectures[i]
          item.users.push({
            levelName,
            canJump,
            isUser: !!selectRange,
            selectRange,
            userId: counseLorteacherIds,
            userName: userName,
            imgUrl: userImage,
            dept: userDept,
            orgName: this.getOrgNameTag(this.assistLectures[i])
          })
        }
        list.push(item)
      }
      if (this.author.length && this.author[0].attrShow) {
        const item = {
          label: this.author[0].attrName,
          users: []
        }
        for (let i = 0; i < this.author.length; i++) {
          const { canJump, selectRange, authorId, author, authorImage, authorDept } = this.author[i]
          item.users.push({
            canJump,
            isUser: !!selectRange,
            selectRange,
            userId: authorId,
            userName: author,
            imgUrl: authorImage,
            dept: authorDept,
            orgName: this.getOrgNameTag(this.author[i])
          })
        }
        list.push(item)
      }
      if (this.creator.attrShow) {
        const { canJump, selectRange, attrName, createId, createName, createImage, createDept } = this.creator
        list.push({
          label: attrName,
          users: [
            { canJump, isUser: !!selectRange, selectRange, userId: createId, userName: createName, imgUrl: createImage, dept: createDept, orgName: this.getOrgNameTag(this.creator) }
          ]
        })
      }
      this.customer.forEach(item => {
        const { canJump, selectRange, attrName, userId, userName, userImage, userDept } = item
        list.push({
          label: attrName,
          users: [
            { canJump, isUser: !!selectRange, selectRange, userId, userName, imgUrl: userImage, dept: userDept, orgName: this.getOrgNameTag(item) }
          ]
        })
      })
      return list
    }
  },
  methods: {
    toUserCenter(item) {
      if (!item.canJump || !item.userId) return;
      const href = item.selectRange === 1 ? window.location.origin + `/#/userindex?userId=${item.userId}` : window.location.origin + `/te/#/lecturerdetail?tid=${item.userId}`;
      window.open(href, '_blank');
    },
    getOrgNameTag(item) {
      if (item.orgId !== localStorage.orgId) return item.orgName || '';
      return '';
    }
  }
};
</script>
