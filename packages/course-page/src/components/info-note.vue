<!-- 创建时间2023/02/28 17:31:17 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：详情-笔记 -->
<template>
  <div class="yxtulcdsdk-course-page__note">

    <yxtf-tabs
      v-model="activeTab"
      class="yxtulcdsdk-course-page__note-tab"
      type="nounderline"
      :bottom-line="false"
      @tab-click="search"
    >
      <yxtf-tab-pane :label="$t('pc_kng_common_lbl_all_type'/**全部 */)" name="all" />
      <yxtf-tab-pane :label="$t('pc_kng_comments_lbl_excellent_comment'/**精选 */)" name="choiceness" />
      <yxtf-tab-pane :label="$t('pc_kng_note_lbl_my'/**我的 */)" name="mine" />
    </yxtf-tabs>
    <yxtf-infinite-list
      v-if="!listFinished || listFinished && noteList.length > 0"
      tag="div"
      :loading="listLoading"
      :finished="listFinished"
      @load="getNoteList"
    >
      <note-cell
        v-for="item in noteList"
        :key="item.id"
        :cell-data="item"
        @search="search"
      />
    </yxtf-infinite-list>
    <yxtf-empty v-else class="minh500" :empty-text="emptyText" />
  </div>
</template>

<script>
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import { getNoteList } from '../../service';
import noteCell from './widgets/note-cell.vue';

export default {
  components: { noteCell },
  inject: ['getDetail'],
  name: 'InfoNote',
  props: ['value'],
  data() {
    return {
      activeTab: 'all',
      page: 0,
      listLoading: true,
      listFinished: false,
      noteList: []
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    isCourse() {
      return this.detail.type === KNG_TYPE.COURSE;
    },
    idInGroup() {
      return this.detail.sourceKngId || this.detail.id;
    },
    emptyText() {
      return this.activeTab === 'all' ? this.$t('pc_kng_note_msg_not_publish_note') : this.$t('pc_kng_note_tip_not_related_note');
    }
  },
  created() {
    this.$root.$on('UPDATE_NOTE', this.search);
    this.search();
  },
  beforeDestroy() {
    this.$root.$off('UPDATE_NOTE', this.search);
  },
  methods: {
    search() {
      this.page = 0;
      this.listFinished = false;
      this.getNoteList();
    },
    async getNoteList() {
      this.page++;
      const data = {
        queryType: this.isCourse ? 0 : 1, // 0.课程中笔记、1.课件中笔记
        courseId: this.isCourse ? this.idInGroup : '', // 课程id
        kngId: this.isCourse ? '' : this.idInGroup, // 课件id
        qryFeatured: this.activeTab === 'choiceness' ? 1 : 0, // 只查精选
        qryMine: this.activeTab === 'mine' ? 1 : 0, // 只查我的
        onlyCurr: 0
      };
      const limit = 10;
      const offset = (this.page - 1) * limit;
      this.listLoading = true;
      getNoteList({limit, offset}, data)
        .then(({datas, paging}) => {
          this.listFinished = this.page >= paging.pages;
          if (this.page === 1) this.noteList = datas;
          else this.noteList = this.noteList.concat(datas);
        })
        .finally(() => (this.listLoading = false));
    }
  }
};
</script>
