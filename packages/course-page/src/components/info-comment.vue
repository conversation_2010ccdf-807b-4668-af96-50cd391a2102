<!-- 创建时间2023/02/27 15:14:53 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：评论 -->
<template>
  <div class="yxtulcdsdk-course-page__comment">
    <template v-if="!readonly">
      <comment-textarea
        v-model="text"
        :placeholder="commentAuthConfig.placeholder"
        :dimension-list="dimensionList"
        :disabled="commentDisabled"
      />
      <!-- 多维度未评分 -->
      <div v-if="dimensionList.length && dimensionResult.score === 0" class="mt22">
        <div class="color-gray-7">{{ $t('pc_kng_course_lbl_please_rate_it'/** 请您评分*/) }}</div>
        <div class="yxtulcdsdk-flex yxtulcdsdk-flex-wrap yxtulcdsdk-mh-10">
          <dimension-rate
            v-for="item in dimensionList"
            :key="item.id"
            class="wp50 box-border minw360"
            :dimension="item"
            :disabled="commentDisabled"
          />
        </div>
      </div>
      <div class="yxtulcdsdk-flex-center mt22">
        <!-- 已评分 -->
        <span v-if="dimensionResult.score>0 || !dimensionList.length" class="mr12 color-gray-7">{{ dimensionResult.score>0?$t('pc_kng_comments_lbl_scored'/** 已评分*/):$t('pc_kng_course_lbl_please_rate_it'/** 请您评分*/) }}</span>
        <yxt-popover
          v-if="dimensionResult.score>0"
          placement="bottom-start"
          trigger="hover"
          :disabled="!dimensionList.length || !dimensionResult.itemResultBeans.length"
        >
          <div class="standard-size-12">
            <div v-for="(item,index) in dimensionResult.itemResultBeans" :key="index" :class="index>0?'mt20':''">
              <div class="color-gray-9">{{ item.dmnName }}</div>
              <div class="yxtulcdsdk-flex-center mt5">
                <yxtf-rate v-model="item.score" disabled :icon-size="10" />
                <span class="yxtf-color-warning ml8">{{ item.score.toFixed(1) }}</span>
              </div>
            </div>
          </div>
          <div slot="reference" class="yxtulcdsdk-flex-center yxtulcdsdk-inline-flex">
            <yxtf-rate
              :value="dimensionResult.score"
              disabled
            />
            <span class="yxtf-color-warning ml8">{{ dimensionResult.score.toFixed(1) }}</span>
          </div>
        </yxt-popover>
        <!-- 单维度未评分 -->
        <yxtf-rate
          v-else-if="!dimensionList.length"
          v-model="singleScore"
          text-color="#757575"
          show-text
          :disabled="commentDisabled"
          :texts="rateTexts"
        />
        <div class="yxtulcdsdk-flex-1"></div>
        <yxtf-button
          type="primary"
          :disabled="confirmDisabled"
          :loading="loading"
          @click="confirm"
        >
          {{ $t('pc_kng_course_lbl_publish'/* 发表 */) }}
        </yxtf-button>
      </div>
    </template>
    <yxtf-infinite-list
      v-if="!listFinished || listFinished && commentList.length > 0"
      class="mt24"
      tag="div"
      :loading="listLoading"
      :finished="listFinished"
      @load="getCommentList"
    >
      <comment-cell
        v-for="item in commentList"
        :key="item.id"
        :cell-data="item"
        @search="search"
        @share="toShare"
      />
    </yxtf-infinite-list>

    <yxtf-empty v-else class="minh500" />
    <!-- 分享 -->
    <comment-share ref="commentShare" />
  </div>
</template>

<script>
import qs from 'qs';
import { commonUtil } from 'yxt-biz-pc';
import { getCommentList, getMultiDimension, getMultiDimensionResult, getMultiDimensionSwitch, postComment } from '../../service';
import DimensionRate from './widgets/dimension-rate.vue';
import CommentCell from './widgets/comment-cell.vue';
import CommentShare from './widgets/comment-share.vue';
import commentTextarea from './widgets/comment-textarea.vue';
import { getQueryString } from 'yxt-ulcd-sdk/packages/_utils/core/utils';
import { htmlEncode } from 'yxt-ulcd-sdk/packages/examing/src/core/utils';
export default {
  components: { DimensionRate, CommentCell, CommentShare, commentTextarea },
  inject: ['getDetail', 'getCommentAuthConfig', 'getScanQueryData'],
  name: 'InfoComment',
  props: ['value', 'readonly'],
  data() {
    return {
      text: '',
      loading: false,
      disabled: false,
      dimensionList: [],
      dimensionResult: {itemResultBeans: []},
      rateTexts: [
        this.$t('pc_kng_course_lbl_disappointment'/* 失望 */),
        this.$t('pc_kng_course_lbl_poor'/* 较差 */),
        this.$t('pc_kng_course_lbl_commonly'/* 一般 */),
        this.$t('pc_kng_course_lbl_very_good'/* 很好 */),
        this.$t('pc_kng_course_lbl_great'/* 超赞 */)
      ],
      score: 0,
      commentList: [],
      listLoading: true,
      listFinished: false,
      page: 0,
      singleScore: 0
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    orgId() {
      return this.detail.sourceOrgId || localStorage.orgId;
    },
    // 禁止评论：禁言状态+合规+课程进度
    commentDisabled() {
      return !this.commentAuthConfig.useable;
    },
    // 禁止发布：内容填写合格
    confirmDisabled() {
      return !this.text.trim();
    },
    commentAuthConfig() {
      return this.getCommentAuthConfig();
    }
  },
  created() {
    if (!this.readonly) {
      this.setMultiDimension();
      this.setMultiDimensionResult();
    }
    this.$root.$on('UPDATE_COMMENT', this.refresh);
    this.search();
  },
  beforeDestroy() {
    this.$root.$off('UPDATE_COMMENT', this.refresh);
  },
  methods: {
    async setMultiDimension() {
      const state = await getMultiDimensionSwitch(this.orgId);
      if (state.switch) {
        const res = await getMultiDimension(this.orgId);
        this.dimensionList = res.filter(item => item.enabled === 1);
      }
    },
    async setMultiDimensionResult() {
      const {score, itemResultBeans} = await getMultiDimensionResult(this.detail.id);
      this.dimensionResult = {
        score,
        itemResultBeans: itemResultBeans || []
      };
    },
    async confirm() {
      const params = {
        kngId: this.detail.id,
        score: this.dimensionResult.score > 0 ? this.dimensionResult.score : this.singleScore,
        comment: htmlEncode(this.text)
      };
      // 多维度参数
      if (this.dimensionList.length > 0) {
        if (this.dimensionResult.score > 0) {
          // 已评分
          params.addBeans = this.dimensionResult.itemResultBeans.map(item => ({id: item.id, score: item.score}));
        } else {
          // 未评分
          const unscoredNumber = this.dimensionList.filter(item => item.score === 0).length;
          if (unscoredNumber > 0) {
            return this.$message({
              showClose: false,
              message: this.$t('pc_kng_comments_msg_click_star'/* 请点击星星进行评分 */),
              type: 'error'
            });
          }
          params.addBeans = this.dimensionList.map(item => ({id: item.id, score: item.score}));
        }
      } else {
        if (params.score === 0) {
          return this.$message({
            showClose: false,
            message: this.$t('pc_kng_comments_msg_click_star'/* 请点击星星进行评分 */),
            type: 'error'
          });
        }
      }
      this.loading = true;
      await postComment(params).finally(() => (this.loading = false));
      this.$message.success(this.$t('pc_kng_comments_msg_create_success'/* 评论发表成功 */));
      this.$root.$emit('UPDATE_COMMENT_COUNT'); // 更新评论数量
      this.text = '';
      if (this.dimensionResult.score === 0) {
        // 第一次评分，需要更新课程评分
        this.$root.$emit('UPDATE_COURSE_SCORE');
      }
      this.refresh();
    },
    refresh() {
      this.setMultiDimension();
      this.setMultiDimensionResult();
      this.search();
    },
    getCommentList() {
      this.page++;
      const limit = 10;
      const offset = (this.page - 1) * limit;
      this.listLoading = true;
      getCommentList(this.detail.id, {limit, offset})
        .then(({datas, paging}) => {
          this.listFinished = this.page >= paging.pages;
          if (this.page === 1) this.commentList = datas;
          else this.commentList = this.commentList.concat(datas);
        })
        .finally(() => (this.listLoading = false));
    },
    search() {
      this.page = 0;
      this.listFinished = false;
      this.getCommentList();
    },
    async toShare(commentData) {
      const data = this.getScanQueryData();
      data.locateshare = getQueryString('locateshare') || '';
      const str = qs.stringify(data);
      const pcPath = `/kng/#/scan?${str}`;
      const h5Path = `/#/kng/scan?${str}`;
      const url = await commonUtil.getShortUrl(pcPath, h5Path, 0);
      const { id, creatorName, createTime, imgUrl, filtered } = commentData;
      const { coverUrl, title } = this.detail;
      this.$refs.commentShare.init({ id, username: creatorName || '', imgUrl, time: createTime, content: filtered, coverUrl, title, url });
    }
  }
};
</script>
