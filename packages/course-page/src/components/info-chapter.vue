<!-- 创建时间2023/02/27 09:22:26 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：大纲 -->
<template>
  <div v-if="chapters.length" class="yxtulcdsdk-course-page__chapter">
    <div class="standard-size-18 yxt-weight-5 mb24">{{ $t('pc_kng_detail_nav_title2'/** 课程大纲 */) }}</div>
    <div v-for="item in chapters" :key="item.id" class="mb24">
      <div v-if="item.name" class="yxt-weight-5 mb12">{{ item._name }}</div>
      <div
        v-for="child in item.child"
        :key="child.id"
        class="yxtulcdsdk-course-page__chapter-item hover-bg-primary-1"
        :class="{'yxtulcdsdk-course-page__chapter-item--hover':hoverId === child.id}"
        @mouseover="hoverId = child.id"
        @mouseleave="hoverId = ''"
        @click="toPlay(child)"
      >
        <yxtf-svg
          class="yxtulcdsdk-flex-shrink-0"
          :remote-url="kngFileTypeUrl"
          :icon-class="getKngFileTypeIcon(child.bizAtt.fileType, child.type)"
          width="16px"
          height="20px"
        />
        <div class="yxtulcdsdk-flex-1 ml12 ellipsis" :class="child.id === kngId ? 'color-primary-6' : ''">{{ child.name }}</div>
        <div class="yxtulcdsdk-course-page__chapter-content color-gray-9 over-hidden">
          <!-- <yxtf-progress
            class="ml24"
            line-width="80px"
            :percentage="child.bizAtt.studySchedule || 0"
          /> -->
          <template v-if="getShowScoreAndHour(child.type)">
            <div class="ml36 ellipsis">{{ $t('pc_kng_detail_lbl_score_detail'/**学分：*/) }}{{ numberScore(child.bizAtt.studyScores) }}</div>
            <div class="ml36 ellipsis">{{ $t('pc_kng_detail_lbl_hour_detail'/** 学时：*/) }}{{ getMinuteFormat(child.bizAtt.studyHours) }}</div>
          </template>
          <template v-if="canStudy(child) && mode === 'normal'">
            <yxtf-button type="primary" class="ml36">{{ child.studyStatus === 0 ? $t('pc_kng_detail_btn_start_learned'/**开始学习 */):$t('pc_kng_detail_btn_continue_learn'/** 继续学习*/) }}</yxtf-button>
            <div v-if="isShowDownload(child)" class="p4 hover-bg-primary-2 ml24 yxtulcdsdk-flex-center" @click.stop="toDownload(child)">
              <yxtf-svg
                class="color-primary-6 "
                width="20px"
                height="20px"
                icon-class="download"
                :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/pc/svg`"
              />
            </div>
          </template>
        </div>
        <div class="yxtulcdsdk-course-page__chapter-lock hand ml24">
          <yxt-ulcd-sdk-svg
            class="color-gray-6"
            width="20px"
            height="20px"
            :icon-class="getChapterIcon(child)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { commonUtil as Utils } from 'yxt-biz-pc';
import { KNG_TYPE, ENV_FLAG } from 'yxt-ulcd-sdk/packages/course-player/enum';
import { cacheDownloadingKng, continueDownload, downloadUrlName, getMinuteFormat, numberScore, removeDownloadingKng } from 'yxt-ulcd-sdk/packages/course-player/utils';
import { kngFileTypeUrl, getKngFileTypeIcon } from '../../utils';
import Svg from 'packages/_components/svg.vue';
import { getNewDownloadUrl } from 'yxt-ulcd-sdk/packages/course-player/service';
import workerTimer from 'yxt-ulcd-sdk/packages/course-player/timer';
export default {
  name: 'InfoChapter',
  inject: ['getChapterList', 'getBossDownloadConfig', 'getCourseId', 'getKngId', 'getWatermarkConfig', 'getAllKngList', 'getMode'],
  components: {
    [Svg.name]: Svg
  },
  data() {
    return {
      kngFileTypeUrl,
      hoverId: '',
      KNG_TYPE
    };
  },
  computed: {
    mode() {
      return this.getMode();
    },
    chapterList() {
      return this.getChapterList();
    },
    allKngList() {
      return this.getAllKngList();
    },
    kngId() {
      return this.getKngId();
    },
    courseId() {
      return this.getCourseId();
    },
    bossDownloadConfig() {
      if (this.mode === 'normal') {
        return this.getBossDownloadConfig() || {};
      }
      return {};
    },
    chapters() {
      if (this.chapterList.length && this.chapterList[0].type !== 92) {// 课程章节
        return [{
          id: -1,
          child: this.chapterList
        }];
      }
      return this.chapterList;
    }
  },
  watch: {
    allKngList(val) {
      if (val.length > 0 && this.mode === 'normal') {
        this.checkDownloadingContinue();
      }
    }
  },
  methods: {
    numberScore,
    getMinuteFormat,
    getKngFileTypeIcon,
    getShowScoreAndHour(type) {
      return ![KNG_TYPE.ZIP, KNG_TYPE.EXAM, KNG_TYPE.PRACTICE, KNG_TYPE.SURVEY, KNG_TYPE.DISCUSS].includes(type);
    },
    checkDownloadingContinue() {
      this.allKngList.forEach(item => {
        continueDownload(item.id, () => this.toDownload(item));
      });
    },
    // 下载
    async toDownload(item) {
      if (item.downloading) return this.$message.warning(this.$t('pc_ulcdsdk_downloading'/** 下载中... */));
      try {
        item.downloading = true;
        // 真水印逻辑 课件设置：文档类型，水印跟随平台，下载真水印 & 平台设置：开启水印，固定水印
        // downloadType  0:下载源文件，1:下载真水印文档
        const watermarkConfig = this.getWatermarkConfig();
        if (item.type === KNG_TYPE.DOC &&
          item.bizAtt.watermarkFlag === 0 &&
          item.downloadType === 1 &&
          watermarkConfig.enabled &&
          watermarkConfig.type === 1) {
          return this.doDownload(true, item);
        }
        this.doDownload(false, item);
      } catch (e) {
        // 下载失败
        this.stopDownload(item, e.message);
      }
    },
    async doDownload(trueWatermark, item) {
      const params = {
        knowledgeId: item.id,
        courseId: this.courseId
      };
      // 企微isv下 前端获取用户名传给后端打水印
      if (localStorage.sourceCode === '100' && item.type === KNG_TYPE.DOC) {
        try {
          params.fullname = await Utils.getUsername(localStorage.fullname);
        } catch (error) { }
      }
      try {
        const res = await getNewDownloadUrl(params);
        const {state, downloadUrl} = res;
        // 0-未下载过 1-水印生成中 2-水印生成完成,可以下载 3-水印生成失败
        if (state === 1) {
          return this.setDownloadTimer(trueWatermark, item);
        } else if ((state === 2 || state === 0) && downloadUrl) {
          if (item.envFlag === ENV_FLAG.PRIVATE && window.mixKngDown) {
            downloadUrlName(downloadUrl, item.name);
          } else {
            window.open(downloadUrl);
          }
          this.stopDownload(item, this.$t('pc_kng_btn_download_success'/** 已成功加入下载 */), 'success');
        } else {
          this.stopDownload(item, this.$t('pc_kng_downloadlist_msg_error'/** 下载失败 */));
        }
        removeDownloadingKng(item.id);
      } catch (e) {
        // 下载失败
        this.stopDownload(item, e.message);
      }
    },
    setDownloadTimer(trueWatermark, item) {
      if (item.downloadTimer) return;
      this.$fmessage({
        type: 'info',
        dangerouslyUseHTMLString: true,
        message: this.$t('pc_kng_courseware_download_msg_watermark'/** 请耐心等待，课件处理加水印中.. */) + this.$t('pc_kng_courseware_download_msg_base'/* 若无法正常下载课件，请检查浏览器是否已拦截 */)
      });
      cacheDownloadingKng(item.id);
      item.downloadTimer = workerTimer.setInterval(() => this.doDownload(trueWatermark, item), 10 * 1000);
    },
    stopDownload(item, msg, type = 'error') {
      item.downloading = false;
      if (item.downloadTimer) {
        workerTimer.clearInterval(item.downloadTimer);
        item.downloadTimer = null;
      }
      this.$message[type](msg || this.$t('pc_kng_downloadlist_msg_error'/** 下载失败 */));
    },

    toPlay(item) {
      this.$root.$emit('CHAPTER_ITEM_PLAY', item);
      document.querySelector('.yxtulcdsdk-course-page__top').scrollIntoView(true);
    },
    canStudy(item) {
      return item.lockStatus === 1 && item.bizAtt.needToBuy === 0;
    },
    isShowDownload(item) {
      const {isOpen, fileTypes} = this.bossDownloadConfig;
      return isOpen && fileTypes.includes(item.type) && item.bizAtt.downloadStatus === 1;
    },
    getChapterIcon(item) {
      if (!this.canStudy(item)) {
        return 'knglock';
      }
      const map = {
        0: 'kngnotstarted',
        1: 'kngprogress',
        2: 'kngcompleted'
      };
      return map[item.studyStatus] || 'kngnotstarted';
    }
  }
};
</script>
