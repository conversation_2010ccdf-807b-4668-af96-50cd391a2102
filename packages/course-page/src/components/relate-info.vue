<!-- 创建时间2023/02/24 15:24:57 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：播放页下方的组件 -->
<template>
  <div class="yxtulcdsdk-course-page__info">
    <div v-if="tabs.length" class="yxtulcdsdk-course-page__info-tab yxtulcdsdk-flex-center-center">
      <yxtf-tabs
        v-model="activeTab"
        class="mt12"
        :bottom-line="false"
      >
        <yxtf-tab-pane
          v-for="item in tabs"
          :key="item.value"
          :label="item.label"
          :name="item.value"
        />
      </yxtf-tabs>
    </div>
    <div ref="info" class="yxtulcdsdk-flex-mid ph40 pv24">
      <div class="yxtulcdsdk-course-page__info-left pv24 ph32 yxtbizf-br-8">
        <!-- 介绍 -->
        <info-intro v-if="activeTab === '0'" />
        <!-- 大纲 -->
        <info-chapter v-if="activeTab === '0'" />
        <!-- 评论 -->
        <info-comment v-if="activeTab==='1'" ref="infoComment" v-model="commentCount" />
        <!-- 课程笔记 -->
        <info-note v-if="activeTab==='2'" v-model="noteCount" />
      </div>
      <div class="yxtulcdsdk-course-page__info-right ml24">
        <info-other />
      </div>
    </div>
    <yxtf-dialog
      padding-size="medium"
      :visible.sync="showCommentDialog"
      width="520px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div>
        <div class="text-center standard-size-24">{{$t('pc_kng_project_tit_kngComments'/** 课程评论 */)}}</div>
        <div class="text-center standard-size-14 mt12 color-gray-9">{{$t('pc_kng_course_lbl_please_comment'/** 恭喜您学完所有内容，请留下您的评价吧～ */)}}</div>
        <comment-textarea class="mt20" v-model="text" :placeholder="commentAuthConfig.placeholder" :dimensionList="dimensionList"/>

        <!-- 多维度评分 -->
        <div v-if="dimensionList.length" class="mt20">
          <div class="color-gray-9">{{ $t('pc_kng_course_tip_please_comment'/** 请对课程进行评分 */) }}</div>
          <div class="yxtulcdsdk-flex yxtulcdsdk-flex-wrap yxtulcdsdk-mh-10">
            <div class="wp50 mt12" v-for="dimension in dimensionList" :key="dimension.id">
              <div class="yxtulcdsdk-flex-center w162">
                <yxtf-tooltip :content="dimension.dmnName" open-filter placement="top">
                  <div class="mw140 ellipsis color-gray-9">{{ dimension.dmnName }}</div>
                </yxtf-tooltip>
                <yxtf-tooltip
                  v-if="dimension.des"
                  placement="top"
                  :max-width="500"
                  :content="dimension.des"
                >
                  <yxtf-svg
                    class="color-gray-6 hand ml4"
                    width="16px"
                    height="16px"
                    icon-class="prompt-0"
                  />
                </yxtf-tooltip>
              </div>
              <yxtf-rate
                v-model="dimension.score"
                class="mt8"
                show-text
                text-color="#757575"
                :texts="rateTexts"
              />
            </div>
          </div>
        </div>
        <div v-else  class="mt20 yxtulcdsdk-flex-center">
          <span class="color-gray-9">{{ $t('pc_kng_course_tip_please_comment'/** 请对课程进行评分 */) }}</span>
          <yxtf-rate
            class="ml20"
            v-model="singleScore"
            text-color="#757575"
            show-text
            :texts="rateTexts"
          />
        </div>
        
        <div class="text-center">
          <yxtf-button 
            class="mt24"
            type="primary"
            :disabled="confirmDisabled"
            :loading="loading"
            @click="confirm"
          >
            {{ $t('pc_kng_course_lbl_publish'/* 发表 */) }}
          </yxtf-button>
        </div>
      </div>
    </yxtf-dialog>

  </div>
</template>

<script>
import { getCommentCount, getMultiDimension, getMultiDimensionResult, getMultiDimensionSwitch, getNoteCount, postComment } from '../../service';
import { postCommentTipState } from 'yxt-ulcd-sdk/packages/course-player/service';
import InfoChapter from './info-chapter.vue';
import InfoIntro from './info-intro.vue';
import InfoComment from './info-comment.vue';
import InfoNote from './info-note.vue';
import InfoOther from './info-other.vue';
import commentTextarea from './widgets/comment-textarea.vue';
import DimensionRate from './widgets/dimension-rate.vue';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import Svg from 'packages/_components/svg.vue';
import { htmlEncode } from 'yxt-ulcd-sdk/packages/examing/src/core/utils';
export default {
  name: 'RelateInfo',
  inject: ['getDetail', 'getFactorConfig', 'getCommentAuthConfig', 'getGlobalData', 'getIsUsedByKng'],
  components: {
    InfoIntro,
    InfoChapter,
    InfoComment,
    InfoNote,
    InfoOther,
    commentTextarea,
    DimensionRate,
    [Svg.name]: Svg
  },
  data() {
    return {
      activeTab: '0',
      isSticky: false,
      commentCount: 0,
      noteCount: 0,
      loading: false,
      singleScore: 0,
      text: '',
      dimensionList: [],
      rateTexts: [
        this.$t('pc_kng_course_lbl_disappointment'/* 失望 */),
        this.$t('pc_kng_course_lbl_poor'/* 较差 */),
        this.$t('pc_kng_course_lbl_commonly'/* 一般 */),
        this.$t('pc_kng_course_lbl_very_good'/* 很好 */),
        this.$t('pc_kng_course_lbl_great'/* 超赞 */)
      ],
      showCommentDialog: false
    };
  },
  computed: {
    isUsedByKng() {
      return this.getIsUsedByKng();
    },
    factorConfig() {
      return this.getFactorConfig();
    },
    globalData() {
      return this.getGlobalData();
    },
    tabs() {
      const commentLabel = this.commentCount ? this.$t('pc_kng_project_tit_kngComments'/** 课程评论 */) + `(${this.commentCount})` : this.$t('pc_kng_project_tit_kngComments'/** 课程评论 */);
      const list = [
        {label: this.$t('pc_kng_detail_nav_title1'/** 课程介绍 */), value: '0'},
        {label: commentLabel, value: '1'}
      ];
      if (this.factorConfig.showNote && this.detail.enableNote) {
        const noteLabel = this.noteCount ? this.$t('pc_kng_note_lbl_courseNote'/** 课程笔记 */) + `(${this.noteCount})` : this.$t('pc_kng_note_lbl_courseNote'/** 课程笔记 */);
        list.push({label: noteLabel, value: '2'});
      }
      return list;
    },
    detail() {
      return this.getDetail();
    },
    orgId() {
      return this.detail.sourceOrgId || localStorage.orgId;
    },
    // 禁止发布：内容填写合格
    confirmDisabled() {
      return !this.text.trim();
    },
    commentAuthConfig() {
      return this.getCommentAuthConfig();
    }
  },
  created() {
    this.getNoteCommentCount();
    this.$root.$on('SHOW_COMPLETE_COMMENT', this.showCompleteComment);
    this.$root.$on('UPDATE_NOTE_COUNT', this.updateNoteCount); // 更新笔记数量
    this.$root.$on('UPDATE_COMMENT_COUNT', this.updateCommentCount); // 更新评论数量
  },
  beforeDestroy() {
    this.$root.$off('SHOW_COMPLETE_COMMENT', this.showCompleteComment);
    this.$root.$off('UPDATE_NOTE_COUNT', this.updateNoteCount); // 更新笔记数量
    this.$root.$off('UPDATE_COMMENT_COUNT', this.updateCommentCount); // 更新评论数量
  },
  methods: {
    getNoteCommentCount() {
      this.updateNoteCount();
      this.updateCommentCount();
    },
    updateCommentCount(){
      getCommentCount(this.detail.id)
        .then(({data}) => {
          this.commentCount = data || 0;
        });
    },
    updateNoteCount(){
      const {type, sourceKngId, id} = this.detail;
      const isCourse = type === KNG_TYPE.COURSE;
      const data = {
        queryType: this.isCourse ? 0 : 1, // 0.课程中笔记、1.课件中笔记
        courseId: isCourse ? sourceKngId || id : '', // 课程id
        kngId: this.isCourse ? '' : sourceKngId || id, // 课件id
        qryFeatured: 0, // 只查精选
        qryMine: 0, // 只查我的
        onlyCurr: 0
      };
      getNoteCount(data)
        .then(({ data }) => {
          this.noteCount = data || 0;
        });
    },
    async showCompleteComment() {
      // 培训沉浸式中不弹
      if (this.commentAuthConfig.useable && !this.globalData.hadShowCommentTip && !this.showCommentDialog && this.isUsedByKng) {
        await this.setMultiDimensionResult()
        if (this.dimensionResult.score === 0) {
          await this.setMultiDimension();
          this.$root.$emit('EXIT_FULLSCREEN');
          this.showCommentDialog = true;
        }
        postCommentTipState(this.detail.id)
          .then(() => (this.globalData.hadShowCommentTip = true));
      }
    },
    async setMultiDimension() {
      const state = await getMultiDimensionSwitch(this.orgId);
      if (state.switch) {
        const res = await getMultiDimension(this.orgId);
        this.dimensionList = res.filter(item => item.enabled === 1);
      }
    },
    async setMultiDimensionResult() {
      const {score, itemResultBeans} = await getMultiDimensionResult(this.detail.id);
      this.dimensionResult = {
        score,
        itemResultBeans: itemResultBeans || []
      };
    },
    async confirm () {
      const params = {
        kngId: this.detail.id,
        comment: htmlEncode(this.text)
      };
      // 多维度参数
      if (this.dimensionList.length > 0) {
        const unscoredNumber = this.dimensionList.filter(item => item.score === 0).length;
        if (unscoredNumber > 0) {
          return this.$message({
            showClose: false,
            message: this.$t('pc_kng_comments_msg_click_star'/* 请点击星星进行评分 */),
            type: 'error'
          });
        }
        params.addBeans = this.dimensionList.map(item => ({ id: item.id, score: item.score }));
      } else {
        params.score = this.singleScore
        if (params.score === 0) {
          return this.$message({
            showClose: false,
            message: this.$t('pc_kng_comments_msg_click_star'/* 请点击星星进行评分 */),
            type: 'error'
          });
        }
      }
      this.loading = true;
      await postComment(params).finally(() => (this.loading = false));
      this.$message.success(this.$t('pc_kng_comments_msg_create_success'/* 评论发表成功 */));
      this.updateCommentCount();      
      this.text = '';
      this.$root.$emit('UPDATE_COURSE_SCORE'); // 第一次评分，需要更新课程评分
      this.$root.$emit('UPDATE_COMMENT')
      this.showCommentDialog = false;
    }
  }
};
</script>
