<!-- 创建时间2023/04/26 15:13:43 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：外部使用的进度展示 -->
<template>
  <complete-view
    v-if="info.show && info.percentage < 100"
    :light="true"
    :is-mixture-standard="info.isMixtureStandard"
    :standard-content="info.standardContent"
    :content-complete-flag="info.contentCompleteFlag"
    :time-complete-flag="info.timeCompleteFlag"
    :time-complete-standard="info.timeCompleteStandard"
    :percentage="info.percentage"
    :percentage-text="info.percentageText"
  />
</template>

<script>
import CompleteView from 'yxt-ulcd-sdk/packages/course-player/src/components/summary/complete-view.vue';
export default {
  name: 'OutsideSchedule',
  components: {
    CompleteView
  },
  data() {
    return {
      info: {}
    };
  },
  created() {
    this.$root.$on('SCHEDULE_UPDATE', this.scheduleUpdate);
  },
  beforeD<PERSON>roy() {
    this.$root.$off('SCHEDULE_UPDATE', this.scheduleUpdate);
  },
  methods: {
    scheduleUpdate(data) {
      if (data) {
        this.info = data;
      }
    }
  }
};
</script>
