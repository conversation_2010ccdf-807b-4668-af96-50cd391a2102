<!-- 创建时间2023/02/24 17:42:31 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：可以展开的文字 -->
<template>
  <div class="yxtulcdsdk-course-page__intro-text">
    <div class="standard-size-14 color-gray-9 lh24 pre-break" :class="{'yxtulcdsdk-course-page__intro-text--close':!expand && showMore}" v-html="text"></div>
    <div v-if="showMore" class="color-gray-9 yxtulcdsdk-flex-center-center">
      <div class="yxtulcdsdk-flex-center hand" @click="expand = !expand">
        {{ expand ? $t('pc_kng_commentlist_btn_hide'/**收起 */) :$t('pc_kng_commentlist_btn_show'/**展开 */) }}
        <yxtf-svg
          class="color-gray-6 ml8"
          width="16px"
          height="16px"
          :icon-class="expand?'up':'down'"
        />
      </div>
    </div>

  </div>
</template>

<script>
export default {
  name: 'ExpandText',
  props: {
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showMore: false,
      expand: false
    };
  },
  mounted() {
    this.calculate();
  },
  methods: {
    calculate() {
      const defaultHeight = 800;
      const height = this.$el.scrollHeight;
      this.showMore = height > defaultHeight;
    }
  }
};
</script>
