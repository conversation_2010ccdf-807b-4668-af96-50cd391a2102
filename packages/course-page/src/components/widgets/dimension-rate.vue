<!-- 创建时间2023/02/27 15:33:23 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：多维度评分小组件 -->
<template>
  <div class="yxtulcdsdk-flex-center ph10 mt12">
    <div class="yxtulcdsdk-flex-center w162">
      <yxtf-tooltip :content="dimension.dmnName" open-filter placement="top">
        <div class="mw140 ellipsis color-gray-9">{{ dimension.dmnName }}</div>
      </yxtf-tooltip>
      <yxtf-tooltip
        v-if="dimension.des"
        placement="top"
        :max-width="500"
        :content="dimension.des"
      >
        <yxtf-svg
          class="color-gray-6 hand ml4"
          width="16px"
          height="16px"
          icon-class="prompt-0"
        />
      </yxtf-tooltip>
    </div>
    <yxtf-rate
      v-model="dimension.score"
      class="ml12"
      show-text
      text-color="#757575"
      :texts="rateTexts"
      :disabled="disabled"
    />

  </div>
</template>

<script>
export default {
  name: 'DimensionRate',
  props: {
    dimension: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rateTexts: [this.$t('pc_kng_course_lbl_disappointment'/* 失望 */), this.$t('pc_kng_course_lbl_poor'/* 较差 */), this.$t('pc_kng_course_lbl_commonly'/* 一般 */), this.$t('pc_kng_course_lbl_very_good'/* 很好 */), this.$t('pc_kng_course_lbl_great'/* 超赞 */)]
    };
  }
};
</script>
