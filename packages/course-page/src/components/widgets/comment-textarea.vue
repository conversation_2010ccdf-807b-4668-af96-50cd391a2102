<template>
  <div class="yxtulcdsdk-course-page__comment-input" :class="{ 'input-focus': focus }">
    <div v-if="dimensionList.length && !disabled"
      class="yxtulcdsdk-course-page__comment-dmn yxtulcdsdk-flex color-gray-9">
      <div class="flex-shrink-0">{{ $t('pc_kng_course_lbl_comment_dimension'/** 评论维度 */) }}：</div>
      <div class="yxtulcdsdk-flex yxtulcdsdk-flex-wrap">
        <span v-for="item in dimensionList" :key="item.id" class="yxtulcdsdk-course-page__comment-dmn-node"
          @click="nodeClick(item.dmnName)">{{ item.dmnName }}</span>
      </div>
    </div>
    <yxtf-input ref="commentTextarea" v-model="text" type="textarea" :placeholder="placeholder" maxlength="500"
      show-word-limit :disabled="disabled" resize="none" @focus="focus = true" @blur="focus = false" @input="areaInput" />
  </div>
</template>

<script>
export default {
  name: 'YxtUlcdSdkCommentTextarea',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    dimensionList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      focus: false,
      text: ''
    };
  },
  watch: {
    value(val){
      this.text = val
    }
  },
  methods: {
    areaInput (value) {
      this.$emit('input', value)
    },
    nodeClick (text = '') {
      const textarea = this.$refs.commentTextarea.$el.querySelector('textarea');
      const { selectionEnd: endPos, value: content } = textarea;
      const isNoLF = content[endPos - 1] === '\n' || endPos === 0;
      const newText = `${isNoLF ? '' : '\n'}【${text}】`;
      const newStartPos = endPos + newText.length;
      if (this.text.length + newText.length > 500) {
        textarea.focus();
        return;
      }
      this.text = `${content.substring(0, endPos)}${newText}${content.substring(endPos)}`;
      textarea.focus();
      this.$nextTick(() => {
        textarea.setSelectionRange(newStartPos, newStartPos);
      });

      this.$emit('input', this.text)
    }
  },
};
</script>