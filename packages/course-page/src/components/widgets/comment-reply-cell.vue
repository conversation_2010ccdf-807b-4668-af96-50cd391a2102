<!-- 创建时间2023/02/28 16:11:09 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：回复评论的cell -->
<template>
  <div class="yxtulcdsdk-flex yxtulcdsdk-course-page__comment-reply-cell">
    <div class="yxtulcdsdk-portrait">
      <yxtf-portrait
        size="48px"
        :img-url="cellData.creatorImageUrl || cellData.imgUrl"
        :username="cellData.creatorName"
      />
      <yxtbiz-user-medal-tag
        v-if="cellData.level"
        class="portrait-tag"
        :user-level="cellData.level"
      />
    </div>
    <div class="ml16 yxtulcdsdk-flex-1 w0">
      <div class="yxtulcdsdk-flex">
        <yxtbiz-user-name class="yxt-weight-5 yxtulcdsdk-flex-1 color-gray-10" :name="cellData.creatorName" />
        <span v-if="statusText" class="standard-size-12 color-gray-7">{{ statusText }}</span>
        <div v-else-if="operateConfig && showOperation" class="hand color-gray-7 yxtulcdsdk-flex-center ml24 yxtulcdsdk-course-page__reply-hide" @click="toOperate">
          <yxtf-svg
            :icon-class="operateConfig.icon"
            width="18px"
            height="18px"
          />
          <span class="ml4">{{ operateConfig.label }}</span>
        </div>
      </div>
      <div class="mt12 color-gray-9">
        <span v-if="!hideReply">{{ $t('pc_kng_comments_lbl_reply'/** 回复*/) }}</span>
        <span v-if="!hideReply" class="color-gray-10 ml4 ">
          <yxtbiz-user-name class="yxt-weight-5 " :name="cellData.parentCreatorName || parentCreatorName" />：
        </span>
        <span class="break" v-html="cellData.filtered"></span>
      </div>
      <div class="mt12 color-gray-7 standard-size-14">{{ cellData.createTime }}</div>
    </div>
  </div>
</template>

<script>
import { deleteComment } from 'yxt-ulcd-sdk/packages/course-page/service';
export default {
  name: 'CommentReplyCell',
  inject: ['getSilenceConfigs'],
  props: {
    cellData: {
      type: Object,
      default: () =>({})
    },
    showOperation: {
      type: Boolean,
      default: false
    },
    hideReply: {
      type: Boolean,
      default: false
    },
    // 数据中没返回，上级传递过来
    parentCreatorName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  computed: {
    silenceConfigs() {
      return this.getSilenceConfigs();
    },
    operateConfig() {
      if (this.cellData.creator === localStorage.userId) {
        return {type: 'delete', icon: 'delete', label: this.$t('pc_kng_common_btn_delete')};
      } else if (this.silenceConfigs.complainOnlineclassComment) {
        return {type: 'report', icon: 'tip-off', label: this.$t('pc_kng_common_btn_report')};
      }
      return undefined;
    },
    statusText() {
      const {status, auditConclusion} = this.cellData;
      if (status !== 1) return this.$t('pc_kng_comments_lbl_audit_shield');
      else if (auditConclusion === 0) return this.$t('pc_kng_comments_lbl_audit_not');
      else if (auditConclusion === 2) return this.$t('pc_kng_comments_lbl_audit_failed');
      return '';
    }
  },
  methods: {
    toOperate() {
      const {type} = this.operateConfig;
      if (type === 'report') this.$emit('report', this.cellData);
      else if (type === 'delete') this.toDelete();
    },
    async toDelete() {
      await this.$confirm('', this.$t('pc_kng_comments_msg_isdelete'), {
        type: 'warning',
        showCancelButton: true
      });
      await deleteComment(this.cellData.id);
      this.$message.success(this.$t('pc_kng_comments_msg_delete_success'));
      this.$emit('search');
    }
  }
};
</script>
