<!-- 创建时间2023/02/28 17:44:28 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：笔记cell -->
<template>
  <div class="yxtulcdsdk-course-page__note-cell yxtulcdsdk-flex">
    <div class="yxtulcdsdk-portrait">
      <yxtf-portrait
        size="48px"
        :img-url="cellData.userUrl"
        :username="cellData.fullName"
      />
      <yxtbiz-user-medal-tag
        v-if="cellData.level"
        class="portrait-tag"
        :user-level="cellData.level"
      />
    </div>
    <div class="ml16 yxtulcdsdk-flex-1 w0">
      <div class="yxt-weight-5">{{ cellData.fullName }}</div>
      <div class="yxtulcdsdk-flex-center">
        <span class="color-gray-7 mt12 yxtulcdsdk-flex-1">{{ handleDateFormat(cellData.updateTime) }}</span>
        <div
          v-for="item in operateList"
          :key="item.icon"
          class="hand color-gray-7 yxtulcdsdk-flex-center ml24"
          :class="item.className"
          @click="toOperate(item)"
        >
          <yxtf-svg
            :remote-url="item.remoteUrl"
            :icon-class="item.icon"
            width="18px"
            height="18px"
          />
          <span class="ml4">{{ item.label }}</span>
        </div>
      </div>

      <note-item-view class="mt16" :note="cellData" effect="light" />
    </div>

    <!-- 举报组件 -->
    <yxtbiz-complain ref="complain" :target="complainTarget">
      <template slot="targetInfo">
        <yxtbiz-user-name v-if="complainTarget.targetInfo" :name="complainTarget.targetInfo" />
      </template>
    </yxtbiz-complain>
  </div>
</template>

<script>
import { deleteNote, doNotePraise } from 'yxt-ulcd-sdk/packages/course-page/service';
import noteItemView from 'yxt-ulcd-sdk/packages/course-player/src/components/note/note-item-view.vue';
import { handleDateFormat, numberUnit } from 'yxt-ulcd-sdk/packages/course-player/utils';
export default {
  components: { noteItemView },
  inject: ['getSilenceConfigs', 'getDetail'],
  name: 'NoteCell',
  props: {
    cellData: {
      type: Object,
      default: () =>({})
    }
  },
  data() {
    return {
      complainTarget: {}
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    silenceConfigs() {
      return this.getSilenceConfigs();
    },
    operateList() {
      let list = [];
      if (this.cellData.createUserId === localStorage.userId) {
        list.push({type: 'delete', icon: 'delete', label: this.$t('pc_kng_common_btn_delete'), className: 'yxtulcdsdk-course-page__note-hide'});
      } else if (this.silenceConfigs['MK0001-GN0007-2']) {
        list.push({type: 'report', icon: 'tip-off', label: this.$t('pc_kng_common_btn_report'), className: 'yxtulcdsdk-course-page__note-hide'});
      }
      list.push({type: 'praise', icon: this.cellData.supported ? 'praise_s' : 'praise', label: numberUnit(this.cellData.supportCount), remoteUrl: `${this.$staticBaseUrl}ufd/55a3e0/kng/pc/svg`});
      return list;
    }
  },
  methods: {
    handleDateFormat,
    toOperate(item) {
      switch (item.type) {
        case 'delete':
          this.toDelete();
          break;
        case 'report':
          this.toReport();
          break;
        case 'praise':
          this.toPraise();
          break;
      }
    },
    toReport() {
      this.complainTarget = {
        configCode: 'MK0001-GN0007-2',
        masterId: this.detail.id, // 课程/课件的id
        masterType: 1,
        targetId: this.cellData.id, // 笔记id
        targetInfo: `${this.cellData.fullName || this.$t('pc_kng_lbl_anonymous_note' /* 匿名用户的笔记 */)}`, // 举报对象简单描述,xxx（被投诉人）的举报对象类型{小明的评论/xxx的语音}
        targetName: this.cellData.plaintext, // 举报对象内容：知识，帖子，问题（类型123）：传标题；（类型456）：传内容；其他（类型7）：传链接
        targetType: 6, // 举报对象类型(1-知识 2-帖子 3-问题 4-时刻 5-知识/帖子/时刻的评论，问题的回答 6-笔记 7-其他)
        audioUrl: '',
        audioLength: 0,
        logoUrl: '',
        targetCreator: this.cellData.createUserId
      };
      this.$refs.complain.visible = true;
    },
    async toDelete() {
      await this.$confirm('', this.$t('pc_kng_note_tip_deleteNote'), {
        confirmButtonText: this.$t('pc_kng_course_btn_confirm'),
        type: 'warning'
      });
      await deleteNote(this.cellData.id);
      this.$emit('search');
      this.$root.$emit('UPDATE_MY_NOTE'); // 更新上方我的笔记内容
    },
    async toPraise() {
      const supported = this.cellData.supported ? 0 : 1;
      await doNotePraise(this.cellData.id, supported);
      this.cellData.supportCount = supported
        ? this.cellData.supportCount + 1
        : this.cellData.supportCount - 1;
      this.cellData.supported = supported;
    }
  }
};
</script>
