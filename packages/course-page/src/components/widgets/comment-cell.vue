<!-- 创建时间2023/02/28 11:30:52 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述： -->
<template>
  <div class="yxtulcdsdk-course-page__comment-cell" :class="{'yxtulcdsdk-course-page__comment-cell--model':inDialog}">
    <div class="yxtulcdsdk-flex yxtulcdsdk-flex-align-start">
      <div class="yxtulcdsdk-portrait">
        <yxtf-portrait
          size="48px"
          :img-url="cellData.imgUrl"
          :username="cellData.creatorName"
        />
        <yxtbiz-user-medal-tag
          v-if="cellData.level"
          class="portrait-tag"
          :user-level="cellData.level"
        />
      </div>
      <div class="yxtulcdsdk-flex-1 ml16 min-width0">
        <!-- 姓名 -->
        <div class="yxtulcdsdk-flex-space-between">
          <div class="yxtulcdsdk-course-page__comment-name">
            <div>
              <yxtbiz-user-name class="yxt-weight-5 color-gray-10" :name="cellData.creatorName" />
            </div>
            <div class="mt2">
              <yxtf-tooltip :content="cellData.directDept" :open-filter="true" placement="top">
                <div class="ellipsis"><yxtbiz-dept-name class="standard-size-12 color-gray-6" :name="cellData.directDept" /></div>
              </yxtf-tooltip>
              <yxtf-tag
                v-if="cellData.targetOrgName"
                class="ml8"
                type="info"
                size="mini"
              >{{ cellData.targetOrgName }}</yxtf-tag>
            </div>
          </div>
          <yxtf-rate
            v-model="cellData.dScore"
            class="flex-shrink-0"
            icon-size="small"
            disabled
          />
        </div>
        <!-- 评论内容 -->
        <div class="pre-break mt12 color-gray-9">
          <yxtf-tag v-if="cellData.sticky" type="warning" size="mini">{{ $t('pc_kng_comments_lbl_excellent_comment'/**精选 */) }}</yxtf-tag>
          <span v-html="cellData.filtered"></span>
        </div>
        <!-- 操作 -->
        <div class="mt16 yxtulcdsdk-flex">
          <span class="color-gray-7 standard-size-14 yxtulcdsdk-flex-1">{{ cellData.createTime }}</span>
          <span v-if="statusText" class="standard-size-12 color-gray-7">{{ statusText }}</span>
          <div
            v-for="item in operateList"
            :key="item.icon"
            class="hand color-gray-7 yxtulcdsdk-flex-center ml24"
            :class="item.className"
            @click="toOperate(item)"
          >
            <yxtf-svg
              :remote-url="item.remoteUrl"
              :icon-class="item.icon"
              width="18px"
              height="18px"
            />
            <span class="ml4">{{ item.label }}</span>
          </div>
        </div>
        <!-- 回复 -->
        <template v-if="showReplay">
          <yxtf-input
            v-model="text"
            class="mt16"
            autofocus
            tabindex="9"
            show-word-limit
            maxlength="500"
            :placeholder="$t('pc_kng_comments_lbl_reply'/** 回复 */)"
          />
          <div class="mt22 yxtulcdsdk-flex-right">
            <yxtf-button plain @click="showReplay=false">{{ $t('pc_kng_common_btn_cancel_no_blank'/**取消 */) }}</yxtf-button>
            <yxtf-button
              type="primary"
              :disabled="!text"
              :loading="replyLoading"
              @click="doReply"
            >{{ $t('pc_kng_course_lbl_publish'/**发表 */) }}</yxtf-button>
          </div>
        </template>
        <!-- 回复的评论--列表中 -->
        <div v-if="!inDialog && cellData.replyList && cellData.replyList.length" class="yxtulcdsdk-course-page__comment-reply-wrap" @click="toShowMore">
          <comment-reply-cell
            v-for="item in cellData.replyList"
            :key="item.id"
            :cell-data="item"
            :parent-creator-name="cellData.creatorName"
          />
          <div v-if="cellData.replyCount>2" class="pv16 color-gray-8 hand yxtulcdsdk-course-page__comment-reply-more">{{ $t('pc_kng_lbl_view_replies',{num:cellData.replyCount}) }}</div>
        </div>
      </div>
    </div>
    <!-- 举报组件 -->
    <yxtbiz-complain ref="complain" :target="complainTarget">
      <template slot="targetInfo">
        <yxtbiz-user-name v-if="complainTarget.targetInfo" :name="complainTarget.targetInfo" />
      </template>
    </yxtbiz-complain>
    <!-- 查看更多 -->
    <yxtf-dialog
      v-if="!inDialog"
      custom-class="replay-dialog"
      :visible.sync="showMore"
      :title="$t('pc_kng_lbl_reply_comments'/** 评论回复 */)"
      destroy-on-close
    >
      <comment-cell
        :cell-data="cellData"
        :in-dialog="true"
        @delete="toDelete"
        @share="toShare"
      />
    </yxtf-dialog>

    <!-- 回复的评论--弹窗中 -->
    <div v-if="inDialog">
      <div class="yxtulcdsdk-course-page__comment-reply-count font-bold">{{ $t('pc_kng_comments_lbl_reply'/** 回复 */) }} {{ cellData.replyCount }}</div>
      <yxtf-infinite-list
        tag="div"
        :loading="listLoading"
        :finished="listFinished"
        @load="getCommentList"
      >
        <comment-reply-cell
          v-for="item in commentList"
          :key="item.id"
          :cell-data="item"
          :parent-creator-name="cellData.creatorName"
          show-operation
          hide-reply
          @report="toReport"
          @search="search"
        />
      </yxtf-infinite-list>
    </div>
  </div>
</template>

<script>
import { deleteComment, getReplyCommentList, postReplyComment } from 'yxt-ulcd-sdk/packages/course-page/service';
import { cancelLike, doLike } from 'yxt-ulcd-sdk/packages/course-player/service';
import { numberUnit } from 'yxt-ulcd-sdk/packages/course-player/utils';
import { htmlEncode } from 'yxt-ulcd-sdk/packages/examing/src/core/utils';
import commentReplyCell from './comment-reply-cell.vue';
export default {
  components: { commentReplyCell },
  name: 'CommentCell',
  inject: ['getSilenceConfigs', 'getDetail'],
  props: {
    cellData: {
      type: Object,
      default: () =>({})
    },
    inDialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showReplay: false,
      text: '',
      complainTarget: {},

      showMore: false,
      listLoading: false,
      listFinished: false,
      commentList: [],
      page: 0,
      replyLoading: false
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    statusText() {
      const {status, auditConclusion} = this.cellData;
      if (status !== 1) return this.$t('pc_kng_comments_lbl_audit_shield');
      else if (auditConclusion === 0) return this.$t('pc_kng_comments_lbl_audit_not');
      else if (auditConclusion === 2) return this.$t('pc_kng_comments_lbl_audit_failed');
      return '';
    },
    operateList() {
      if (this.statusText) return [];
      let list = [];
      if (this.cellData.creator === localStorage.userId) {
        list.push({type: 'delete', icon: 'delete', label: this.$t('pc_kng_common_btn_delete'/** 删除 */), className: 'yxtulcdsdk-course-page__comment-hide'});
      } else if (this.silenceConfigs.complainOnlineclassComment) {
        list.push({type: 'report', icon: 'tip-off', label: this.$t('pc_kng_common_btn_report'/** 举报 */), className: 'yxtulcdsdk-course-page__comment-hide'});
      }
      list.push({type: 'reply', icon: 'comment', label: this.$t('pc_kng_detail_btn_reply'/** 回复 */)});
      list.push({type: 'praise', icon: this.cellData.praised ? 'praise_s' : 'praise', label: numberUnit(this.cellData.praiseCount), remoteUrl: `${this.$staticBaseUrl}ufd/55a3e0/kng/pc/svg`});
      if (this.detail.shareEnabled === 1) {
        list.push({type: 'share', icon: 'icon_share', label: this.$t('pc_kng_detail_btn_share'), remoteUrl: `${this.$staticBaseUrl}ufd/55a3e0/svg`});
      }
      return list;
    },
    silenceConfigs() {
      return this.getSilenceConfigs();
    }
  },
  methods: {
    toOperate(item) {
      switch (item.type) {
        case 'delete':
          this.toDelete();
          break;
        case 'report':
          this.toReport(this.cellData);
          break;
        case 'reply':
          this.showReplay = true;
          break;
        case 'praise':
          this.toPraise();
          break;
        case 'share':
          this.toShare();
          break;
      }
    },
    toReport(data) {
      this.complainTarget = {
        masterId: data.targetId,
        masterType: 1,
        targetId: data.id, // 举报对象id
        targetInfo: `${data.creatorName || this.$t('pc_kng_lbl_anonymous_comments'/* 匿名用户的评论 */)}`, // 举报对象简单描述,xxx（被投诉人）的举报对象类型{小明的评论/xxx的语音}
        targetName: data.filtered, // 举报对象内容：知识，帖子，问题（类型123）：传标题；（类型456）：传内容；其他（类型7）：传链接
        targetType: 5, // 举报对象类型(1-知识 2-帖子 3-问题 4-时刻 5-知识/帖子/时刻的评论，问题的回答 6-笔记 7-其他)
        audioUrl: '',
        audioLength: 0,
        logoUrl: '',
        targetCreator: data.creator
      };

      this.$refs.complain.visible = true;
    },
    async toDelete() {
      if (this.inDialog) return this.$emit('delete');
      await this.$confirm('', this.$t('pc_kng_comments_msg_isdelete'), {
        type: 'warning',
        showCancelButton: true
      });
      await deleteComment(this.cellData.id);
      this.$message.success(this.$t('pc_kng_comments_msg_delete_success'));
      this.$emit('search');
      this.$root.$emit('UPDATE_COMMENT_COUNT'); // 更新评论数量
      this.showMore = false;
    },
    doReply() {
      const kngId = this.detail.id;
      this.replyLoading = true;
      postReplyComment({reply: htmlEncode(this.text), commentId: this.cellData.id, kngId})
        .then(() => {
          this.showReplay = false;
          this.text = '';
          this.$message.success(this.$t('pc_kng_comments_msg_reply_success'/* 回复成功 */));
          this.$emit('search');
          if (this.inDialog) {
            this.search();
          }
        }).catch(err => {
          err.message && this.$message.error(err.message);
        }).finally(() => (this.replyLoading = false));
    },
    toPraise() {
      const params = {
        targetId: this.cellData.id,
        targetType: 7, // 点赞对象类型(1-用户,2-知识[包括课程],3-笔记,4-学习商城,5-问题回答,6-帖子,7-评论,8-微课,9-作业/心得/实操,10-混合式小组成员)
        praiseType: this.cellData.praised ? 0 : 1,
        remark: ''
      };
      if (this.detail.shareFlag === 2) params.targetOrgId = this.detail.sourceOrgId;
      const api = this.cellData.praised ? cancelLike(this.cellData.id) : doLike(params);
      api.then(() => {
        this.cellData.praiseCount = this.cellData.praised ? this.cellData.praiseCount - 1 : this.cellData.praiseCount + 1;
        this.cellData.praised = !this.cellData.praised;
      });
    },
    toShare() {
      if (this.inDialog) return this.$emit('share');
      this.$emit('share', this.cellData);
    },
    // 展示回复更多的信息
    toShowMore() {
      if (this.inDialog) return;
      this.showMore = true;

    },

    search() {
      this.page = 0;
      this.getCommentList();
    },

    getCommentList() {
      this.page++;
      const limit = 10;
      const offset = (this.page - 1) * limit;
      this.listLoading = true;
      getReplyCommentList(this.detail.id, this.cellData.id, {limit, offset})
        .then(({datas, paging}) => {
          this.listFinished = this.page >= paging.pages;
          if (this.page === 1) this.commentList = datas;
          else this.commentList = this.commentList.concat(datas);
          this.cellData.replyCount = paging.count;
          this.cellData.replyList = this.commentList.slice(0, 2);
        })
        .finally(() => (this.listLoading = false));
    }
  }
};
</script>
