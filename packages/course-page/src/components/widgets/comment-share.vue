<!-- 创建时间2023/10/09 14:35:42 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：评论分享 -->
<template>
  <yxtf-dialog
    :visible.sync="isShowShare"
    padding-size="empty"
    width="100%"
    :fullscreen="true"
    append-to-body
    destroy-on-close
    :show-close="false"
    custom-class="yxtulcdsdk-course-page__comment-share"
  >
    <div class="yxtulcdsdk-course-page__share-wrap">
      <div ref="main" class="yxtulcdsdk-course-page__share-content">
        <div class="yxtulcdsdk-course-page__share-top">
          <div class="yxtulcdsdk-flex-center">
            <yxtf-portrait
              class="yxtulcdsdk-flex-shrink-0"
              size="32px"
              :img-url="headerUrl"
              :username="info.username"
            />
            <div class="ml8 over-hidden yxtulcdsdk-flex-1">
              <div class="standard-size-14 ellipsis">{{ info.username }}</div>
              <div class="yxtulcdsdk-flex-center">
                <div class="yxtulcdsdk-flex-1 ellipsis color-gray-7 standard-size-12 ">{{ $t('kng_lbl_commenttime', [info.time]) }}</div>
                <yxtf-svg class="size-content-section" :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/svg/`" icon-class="content_section" />
              </div>
            </div>
          </div>
          <div class="mt8 pre-break standard-size-16 share-lh28" v-html="info.content"></div>
          <div class="mt8 yxtulcdsdk-flex-center yxtulcdsdk-course-page__share-info">
            <yxtf-image
              class="yxtulcdsdk-flex-shrink-0"
              :src="info.coverUrl"
              fit="cover"
            />
            <div class="ml8 ellipsis-2">{{ info.title }}</div>
          </div>
        </div>
        <div class="mt24 yxtulcdsdk-flex-center ph20 mb12 yxtulcdsdk-course-page__share-bottom">
          <div class="yxtulcdsdk-flex-1 mr14">
            <div class="standard-size-16 yxt-weight-5">{{ orgName }}</div>
            <div class="standard-size-12 color-gray-7">{{ $t('kng_lbl_onpressopencourse',/** 长按识别二维码，打开课程 */) }}</div>
          </div>
          <div class="yxtulcdsdk-course-page__share-qrcode">
            <img v-if="info.url" :src="qrCodeUrl(info.url)" alt="">
          </div>
        </div>
        <!-- 使用背景图，html2Canvas生成canvas图，页面上会有横线，所以换成image -->
        <img class="yxtulcdsdk-course-page__share-bg" :src="`${$staticBaseUrl}ufd/55a3e0/kng/img/share_bg.png`" alt="">
      </div>
      <common-sticky class="w344">
        <div class="yxtulcdsdk-flex-center-center w344 pt24 pb24">
          <yxtf-button class="w140 share-button-shadow share-border-white" size="others" @click="isShowShare = false">{{ $t('pc_ulcdsdk_cancel') }}</yxtf-button>
          <yxtf-button
            class="w168 share-button-shadow"
            size="others"
            type="primary"
            :loading="loading"
            @click="toDownload"
          >{{ $t('pc_ulcdsdk_lbl_saveimage') }}</yxtf-button>
        </div>
      </common-sticky>

    </div>
    <yxtbiz-upload
      ref="upload"
      class="d-none"
      app-code="kng"
      config-key="ImageConfigKey"
      module-name="comment"
      function-name="share"
      filters=".png"
      :convert="false"
      :auto-upload="false"
      :on-uploaded="uploaded"
      :on-error="error"
    />
  </yxtf-dialog>

</template>

<script>
import { Browser } from 'yxt-ulcd-sdk/packages/api/const';
import { downloadUrl } from 'yxt-ulcd-sdk/packages/course-page/utils';
import CommonSticky from 'yxt-ulcd-sdk/packages/examing/src/pages/components/CommonSticky.vue';
import { loadScript } from 'yxt-ulcd-sdk/packages/_utils/core/utils';
// 生态下
const isIsvBrowser = Browser.dingtalk || Browser.weixin;
export default {
  name: 'CommentShare',
  components: {
    CommonSticky
  },
  data() {
    return {
      info: {},
      isShowShare: false,
      shareImageUrl: '',
      isShow: false,
      cache: {},
      base64Cache: {},
      loading: false,
      headerUrl: '',
      orgName: localStorage.getItem('orgName') || this.$t('kng_lbl_platformname'/** 绚星云学堂学习平台 */)
    };
  },
  methods: {
    qrCodeUrl(text) {
      if (!text) return '';
      return `${window.feConfig.common.exportPdfTool}generate-qrcode?text=${text}`;
    },
    /**
     * @param { username, imgUrl, time, content, coverUrl, title, url } data
     */
    init(data) {
      this.loading = false;
      this.info = data;
      this.setHeadUrl();
      this.isShowShare = true;
    },
    toDownload() {
      const url = isIsvBrowser ? this.cache[this.info.id] : this.base64Cache[this.info.id];
      url ? this.doDownload() : this.generateImageUrl();
    },
    async generateImageUrl() {
      this.loading = true;
      try {
        if (!window.html2canvas) {
          await Promise.all([
            loadScript(this.$imagesBaseUrl + 'npm/html2canvas/1.4.1/html2canvas.min.js')
          ]);
        }
        const container = this.$refs.main;
        if (!container || !window.html2canvas) return;
        const canvas = await window.html2canvas(container, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#e6eeff'
        });
        const dataUrl = canvas.toDataURL('image/png');
        this.base64Cache[this.info.id] = dataUrl;
        // 生态下先上传再下载，因为base64的图片无法下载，会直接查看图片
        if (isIsvBrowser) {
          const blob = this.dataURLToBlob(dataUrl);
          const file = new File([blob], this.$t('kng_lbl_sharecomment') + '.png');
          const upload = this.$refs.upload;
          upload.addFile(file);
          upload.start();
        } else {
          this.doDownload();
        }
      } catch (e) {
        this.loading = false;
        return Promise.reject(Error(''));
      }
    },
    async setHeadUrl() {
      const imgUrl = this.info.imgUrl || '';
      if (imgUrl.indexOf('dingtalk.com') > -1) {
        this.headerUrl = `${window.feConfig.common.apiBaseUrl}exporttoolpdf/generate-image?url=${encodeURIComponent(imgUrl)}&width=400&height=400&fullpage=0`;
      } else {
        this.headerUrl = imgUrl;
      }
    },
    async loadImages() {
      return new Promise(resolve => {
        let loadedNum = 0;
        const img = new Image();
        img.src = this.info.coverUrl;
        img.onload = () => {
          loadedNum++;
          completed();
        };
        img.onerror = () => {
          loadedNum++;
          completed();
        };
        const img1 = new Image();
        img1.src = this.headerUrl;
        img1.onload = () => {
          loadedNum++;
          completed();
        };
        img1.onerror = () => {
          loadedNum++;
          completed();
        };
        const img2 = new Image();
        img2.src = this.qrCodeUrl(this.info.url);
        img2.onload = () => {
          loadedNum++;
          completed();
        };
        img2.onerror = () => {
          loadedNum++;
          completed();
        };
        const completed = () => {
          if (loadedNum === 3) {
            // 图片加载完全后再上传
            setTimeout(resolve, 500);
          }
        };
      });
    },
    // 图片格式转换方法
    dataURLToBlob(dataurl) {
      const arr = dataurl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    uploaded(file) {
      this.cache[this.info.id] = file.fullUrl;
      this.doDownload();
    },
    error() {
      this.$message.error(this.$t('pc_kng_downloadlist_msg_error'/** 下载失败 */));
      this.loading = false;
    },
    async doDownload() {
      if (!this.isShowShare) return;
      this.loading = false;
      const url = isIsvBrowser ? this.cache[this.info.id] : this.base64Cache[this.info.id];
      if (!url) return this.$message.error(this.$t('pc_kng_downloadlist_msg_error'/** 下载失败 */));
      // base64去下载可设置名称
      downloadUrl(url, this.$t('kng_lbl_sharecomment') + new Date().getTime());
    }
  }
};
</script>
