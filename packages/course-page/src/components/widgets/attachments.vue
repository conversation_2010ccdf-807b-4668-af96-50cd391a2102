<!-- 创建时间2022/07/29 13:41:14 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：附件 -->
<template>
  <div class="yxtulcdsdk-course-page__attachments yxtulcdsdk-flex yxtulcdsdk-flex-wrap">
    <div v-for="item in attachments" :key="item.id" class="yxtulcdsdk-course-page__attachments-cell yxtulcdsdk-flex-center">
      <yxtf-svg
        class="yxtulcdsdk-flex-shrink-0"
        width="24px"
        height="24px"
        :icon-class="getIconClass(item)"
      />
      <div class="yxtulcdsdk-flex-1 ml8 over-hidden">
        <yxtf-tooltip :content="item.fileName" open-filter placement="top">
          <div class="color-gray-9 ellipsis">{{ item.fileName }}</div>
        </yxtf-tooltip>
        <div class="color-gray-6 standard-size-12 ellipsis">{{ handleFileSize(item.fileSize)[0] }}</div>
      </div>
      <yxtf-tooltip :content="$t('pc_kng_mgmt_lbl_opt_download'/** 下载 */)" placement="top">
        <div class="yxtulcdsdk-course-page__attachments-down yxtulcdsdk-flex-center-center ml12" @click="download(item)">
          <yxtf-svg
            class="color-gray-6"
            width="16px"
            height="16px"
            icon-class="download"
          />
        </div>
      </yxtf-tooltip>
    </div>
  </div>
</template>

<script>
import { handleFileSize } from '../../../utils';
import {getKngAttachmentUrl} from '../../../service';
const iconClass = {
  video: 'icons/f_kng-video',
  image: 'icons/f_kng-img',
  audio: 'icons/f_kng-audio',
  zip: 'icons/f_kng-zip',
  excel: 'icons/f_kng-excel',
  pdf: 'icons/f_kng-pdf',
  word: 'icons/f_kng-word',
  ppt: 'icons/f_kng-ppt',
  other: 'icons/f_kng-other'
};
export default {
  name: 'Attachment',
  props: {
    // 附件列表
    attachments: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {
    handleFileSize,
    download(item) {
      getKngAttachmentUrl(item.id)
        .then(res => {
          res.downloadUrl && window.open(res.downloadUrl);
        });
    },
    getIconClass(item) {
      if (item.fileType === 'doc') {
        if (['.doc', '.docx'].includes(item.extension)) {
          return iconClass.word;
        } else if (['.ppt', '.pptx'].includes(item.extension)) {
          return iconClass.ppt;
        } else if (['.xls', '.xlsx'].includes(item.extension)) {
          return iconClass.excel;
        } else if (['.pps', '.pdf'].includes(item.extension)) {
          return iconClass.pdf;
        }
        return iconClass.other;
      }
      return iconClass[item.fileType] || iconClass.other;
    }
  }
};
</script>
