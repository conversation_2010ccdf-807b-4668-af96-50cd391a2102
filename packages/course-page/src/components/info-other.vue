<!-- 创建时间2023/03/01 08:58:56 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：底部信息区，右侧的其他项目 -->
<template>
  <div class="yxtulcdsdk-course-page__other">

    <course-operation v-if="!isUsedByKng" class="mb24" type="action" />
    <div v-if="factorConfig.buyBBS && detail.enableBbs" class="hand yxtulcdsdk-flex-center mb24" @click="openBBS">
      <yxtf-svg
        :remote-url="`${$staticBaseUrl}ufd/407a24/kng/pc/svg/pcsvg/bbs`"
        width="36px"
        height="36px"
        icon-class="openBbs"
      />
      <div class="ml12">
        <div class="yxt-weight-5 standard-size-18">{{ $t('pc_kng_bbs_tit_title'/** 课程讨论专区 */) }}</div>
        <div class="color-gray-7 mt8">{{ $t('pc_kng_bbs_msg_tips'/**沟通切磋，课程学习更有趣 */) }}</div>
      </div>
    </div>
    <!-- 大家都在学 -->
    <div v-if="studyList.length">
      <div class="yxt-weight-5 standard-size-18">{{ $t('pc_kng_related_study_title'/**大家都在学 */) }}</div>
      <div
        v-for="item in studyList"
        :key="item.id"
        class="yxtulcdsdk-flex hand mt16"
        @click="change(item)"
      >
        <div class="yxtulcdsdk-course-page__other-img">
          <img
            :src="item.coverUrl"
            alt=""
            width="90"
            height="50"
          >
          <span class="yxtulcdsdk-course-page__other-type">{{ getKngTypeName(item.type) }}</span>
        </div>
        <div class="ml12 yxtulcdsdk-flex-1 ellipsis">
          <yxtf-tooltip open-filter :content="item.title">
            <div class="ellipsis hover-primary-6">{{ item.title }}</div>
          </yxtf-tooltip>
          <div class="standard-size-12 color-gray-7">
            <!-- 课程评分 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.COURSE_GRADING)" class="yxtulcdsdk-course-page__other-item">
              <template v-if="item.avgCommentScore">
                <yxtf-rate
                  :value="item.avgCommentScore"
                  disabled
                  icon-size="small"
                  :show-score="false"
                />
                <span class="ml4">{{ item.avgCommentScore.toFixed(1) }}</span>
              </template>
              <span v-else>{{ $t('pc_kng_detail_tools_no_score'/** 暂无评分 */) }}</span>
            </div>
            <!-- 课程学分 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.COURSE_CREDIT)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_credit_show'/** {0}学分 */,[item.studyScore]) }}
            </div>
            <!-- 学习人数 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.LEARNERS_NUMBER)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_learner_num'/** {0}人学习 */,[item.studyCount || 0]) }}
            </div>
            <!-- 浏览人数 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.VISITORS)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_read_num'/** {0}人浏览 */,[item.readCount || 0]) }}
            </div>
            <!-- 浏览次数 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.VIEWS_NUMBER)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_view_num'/** {0}次浏览 */,[item.viewCount || 0]) }}
            </div>
            <!-- 课程时长 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.COURSE_DURATION)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_courseduration'/** 课程时长{0} */, [getMinuteFormat(item.studyHours)]) }}
            </div>
            <!-- 讲师 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.LECTURER)" class="yxtulcdsdk-course-page__other-item">
              <template v-if="item.lectureName">
                <yxtf-tooltip
                  open-filter
                  placement="top"
                  :content="item.lectureName"
                >
                  <yxtbiz-user-name class="yxtulcdsdk-course-page__other-ellipsis" :title="item.lectureName" :name="item.lectureName" />
                </yxtf-tooltip>
                <span class="ml4">{{ $t('pc_kng_lbl_custom_lecturesname'/** 主讲 */) }}</span>
              </template>
              <span v-else>{{ $t('pc_kng_common_lbl_no_lecture'/** 暂无讲师 */) }}</span>
            </div>
            <!-- 贡献者 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.CONTRIBUTOR)" class="yxtulcdsdk-course-page__other-item">
              <template v-if="item.contributorName">
                <yxtf-tooltip
                  open-filter
                  placement="top"
                  :content="item.contributorName"
                >
                  <yxtbiz-user-name class="yxtulcdsdk-course-page__other-ellipsis" :name="item.contributorName" />
                </yxtf-tooltip>
                <span class="ml4">{{ $t('pc_kng_common_lbl_contribute'/** 贡献 */) }}</span>
              </template>
              <span v-else>{{ $t('pc_kng_common_lbl_no_contribute'/** 暂无贡献者 */) }}</span>
            </div>
            <!-- 创建时间 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.CREATE_TIME)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_create_time'/** {0} 创建 */, [formatDate(item.createTime, 'YYYY-MM-DD')]) }}
            </div>
            <!-- 更新时间 -->
            <div v-if="showAttr(COURSE_LIST_DISPLAY.UPDATE_TIME)" class="yxtulcdsdk-course-page__other-item">
              {{ $t('pc_kng_common_lbl_update_time'/** {0} 更新 */, [formatDate(item.updateTime, 'YYYY-MM-DD')]) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import courseOperation from 'yxt-ulcd-sdk/packages/course-player/src/components/course-operation.vue';

import { getCourseListInfo, getStudyTogetherList } from '../../service';

import {COURSE_LIST_DISPLAY} from '../../enum';
import { getKngTypeName, getMinuteFormat} from 'yxt-ulcd-sdk/packages/course-player/utils';
import { formatDate} from 'yxt-ulcd-sdk/packages/develop-view-plan/formateDate';

export default {
  components: { courseOperation },
  name: 'InfoOther',
  inject: ['getDetail', 'getScanQueryData', 'getFactorConfig', 'getIsUsedByKng'],
  data() {
    return {
      COURSE_LIST_DISPLAY,
      studyList: [],
      attrList: [3]
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    scanQueryData() {
      return this.getScanQueryData();
    },
    factorConfig() {
      return this.getFactorConfig();
    },
    hideStudyTogether() {
      const {targetCode} = this.scanQueryData;
      return targetCode !== 'kng';
    },
    isUsedByKng() {
      return this.getIsUsedByKng();
    }
  },
  async created() {
    await Promise.all([
      this.getCustomCourseListInfo(),
      this.getStudyList()
    ]);
  },
  methods: {
    getKngTypeName,
    getMinuteFormat,
    formatDate,
    // 查询课程列表展示信息
    async getCustomCourseListInfo() {
      try {
        const res = await getCourseListInfo();
        this.attrList = res.courseAttList.length ? res.courseAttList : [3];
      } catch (error) { }
    },
    showAttr(attr) {
      return this.attrList && this.attrList.includes(attr);
    },
    getOrgNameTag(item) {
      if (item.orgId !== localStorage.orgId) return item.orgName || '';
      return '';
    },
    openBBS() {
      const {id, sourceKngId} = this.detail;
      const {targetCode, targetId, flipId} = this.scanQueryData;
      const href = `${location.protocol}//${location.host}/bbs/#/bbsdiscuss/${sourceKngId || id}?targetCode=${targetCode}&projectId=${targetId}&flipId=${flipId}`;
      window.open(href, '_blank');
    },
    // 获取一起学人员
    async getStudyList() {
      if (this.hideStudyTogether) return;
      const {catalogAllPathId, id} = this.detail;
      const catalogId = catalogAllPathId && catalogAllPathId.length && catalogAllPathId[this.detail.catalogAllPathId.length - 1];
      try {
        const res = await getStudyTogetherList({kngId: id, count: 3, catalogId: catalogId || ''});
        this.studyList = res.map(item => ({
          ...item,
          ...item.knowledgeAttrDTO
        }));
      } catch (error) {

      }
    },
    change(item) {
      if (this.$router) {
        this.$router.push({name: 'Scan', query: {kngId: item.id}});
      }
    }
  }
};
</script>
