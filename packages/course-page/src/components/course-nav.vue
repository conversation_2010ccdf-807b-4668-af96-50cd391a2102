<!-- 创建时间2023/08/16 14:22:47 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课程里的课件上一个下一个导航 -->
<template>
  <div class="yxtulcdsdk-course-player__kng-operate">
    <action-icon
      :title="$t('pc_ulcdsdk_lbl_previous'/**上一个 */)"
      icon-class="icon_prev"
      :disabled="getIsFirstKng() || disabled"
      @click="toPrev"
    />
    <action-icon
      :title="$t('pc_ulcdsdk_lbl_next'/**下一个 */)"
      icon-class="icon_next"
      :disabled="getIsLastKng() || disabled"
      @click="toNext"
    />
  </div>
</template>

<script>
import actionIcon from 'yxt-ulcd-sdk/packages/course-player/src/components/action-icon.vue';
export default {
  components: { actionIcon },
  inject: ['getIsFirstKng', 'getIsLastKng'],
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  name: 'CourseNav',
  data() {
    return {};
  },
  methods: {
    toPrev() {
      this.$root.$emit('CHAPTER_PREV');
    },
    toNext() {
      this.$root.$emit('CHAPTER_NEXT');
    }
  }
};
</script>
