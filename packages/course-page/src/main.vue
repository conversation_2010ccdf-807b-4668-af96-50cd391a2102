<!-- 创建时间2023/02/07 15:39:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课程/课件页面组件 -->
<!-- 通信方式：1.单向流数据：prop + provide 2.兄弟通信/子通知父：订阅发布 -->
<template>
  <div v-loading="loading" class="yxtulcdsdk-course-page" :class="inner?'yxtulcdsdk-course-page--short':''">
    <error-empty v-if="globalData.errorText" class="yxtulcdsdk-empty" :text="globalData.errorText" />
    <template v-else-if="detail.id">
      <!-- 播放器 -->
      <div class="yxtulcdsdk-course-page__top">
        <!-- 沉浸式播放组件 -->
        <yxt-ulcd-sdk-playframe
          ref="frame"
          :course-id="courseId"
          :kng-id="kngId"
          :course-detail="detail"
          :catalog-lists="chapterList"
          @changeKng="changeKng"
        />
      </div>
      <relate-info />
    </template>
    <error-handler ref="error" />
    <div v-if="showFaceRecognition" class="div-face-recognition">
      <yxtbiz-face-recognition
        ref="face"
        :master-type="faceRecognitionData.masterType"
        :is-record-face="true"
        :master-id="faceRecognitionData.masterId"
        :session-id="faceRecognitionData.sessionId"
        :ext-type="faceRecognitionData.extType"
        :ext-id1="faceRecognitionData.extId1"
        :ext-id2="faceRecognitionData.extId2"
        :subject-id="faceRecognitionData.subjectId"
        :is-small-face="faceRecognitionData.isSmallFace"
        :life-check="faceRecognitionData.lifeCheck"
        @matchSuccess="faceMatchSuccess"
        @errorGoBack="faceErrorGoBack"
      />
    </div>
  </div>
</template>

<script>
import '../compatible';
import config from 'packages/course-player/mixins/config';
import log from '../mixins/log';
import channelProvide from '../mixins/channel/provide';
import { checkVersion, deleteStudying, getChapters, getCommentTipState, getCourseSetting, getKngDetail, postSyncScheduleNotice } from '../../course-player/service';
import { getQueryString, initKngTypeName, getKngTypeName, yxtReportLog } from 'yxt-ulcd-sdk/packages/course-player/utils';
import ErrorHandler from 'yxt-ulcd-sdk/packages/course-player/src/components/error/handler.vue';
import ErrorEmpty from 'yxt-ulcd-sdk/packages/course-player/src/components/error/empty.vue';
import RelateInfo from './components/relate-info.vue';
import { getAttributeInfo, getOrgParameter, postUserViewRecord, preloadKngInfo } from '../service';
import { setDocumentTitle } from '../utils';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
export default {
  components: { ErrorHandler, ErrorEmpty, RelateInfo },
  name: 'YxtUlcdSdkCoursePage',
  mixins: [config, log, channelProvide],
  provide() {
    return {
      getDetail: () => this.detail,
      getScanQueryData: () => this.scanQueryData,
      getIsLastKng: () => this.isLastKng,
      getIsFirstKng: () => this.isFirstKng,
      getCourseId: () => this.courseId,
      getKngId: () => {
        if (this.isCourse) return this.kngId;
        return this.id;
      },
      getCommonParam: () => this.commonParam,
      getGlobalData: () => this.globalData,
      getChapterList: () => this.chapterList,
      getAllKngList: () => this.allKngList,
      getCommentAuthConfig: () => this.commentAuthConfig,
      getIsUsedByKng: () => !this.inner,
      getAttributeInfo: () => this.attributeInfo,
      getMode: () => 'normal',
      doFaceRecognition: (ctype, callback) => this.doFaceRecognition(ctype, callback),
      setPlayingDetail: (cplayingDetail) => {this.playingDetail = cplayingDetail;}
    };
  },
  props: {
    id: { // 课程id
      type: String,
      default: '',
      required: true
    },
    isCourse: { // 是否是课程包
      type: Boolean,
      default: false
    },
    target: { // 原地址上的业务参数：targetId、targetCode、taskId 等等
      type: Object,
      default: () => ({})
    },
    inner: { // 是否是内嵌使用
      type: Boolean,
      default: false
    },
    autoPlay: { // 是否自动播放
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      detail: {},
      chapterList: [],
      attributeInfo: {},
      kngId: '',
      showChapter: false,
      showPlayError: false,
      globalData: {
        startWithAirplaneMode: false, // 无网络学习
        hadShowCommentTip: false, // 是否打开过完课评论
        autoPlay: this.autoPlay,
        notePosition: -1, // 笔记要跳转的位置
        playErrorText: '', // 播放器级别错误
        errorText: '', // 全局级别错误
        aiLanguage: 'zh' // ai字幕语言
      },
      playingDetail: {},
      showFaceRecognition: false,
      faceRecognitionData: {
        isRecordFace: true, // 照片是否记录
        masterType: 10, // 使用场景类型 使用场景类型(1-考试,2-学习计划,10-知识,11-项目,12-人才发展),示例值(1)
        masterId: '', // 使用场景类型 使用场景的主id
        sessionId: '', // 课程包的场景，传课程包id
        type: 0,
        lifeCheck: 0, // 是否活体（0:否,1:是）
        extType: '', // 课件类型
        extId1: 0, // 扩至字段（第一位:是否开启活体（0:否,1:是）
        extId2: 80, // 比对分数 下期可能会改为接口
        subjectId: '', // 放任务id(项目，人才发展场景)
        isSmallFace: false
      },
      faceRecognitionCallBack: ()=>{}
    };
  },
  computed: {
    // 走中转页所有query参数集合,命名未转化
    scanQueryData() {
      // 学习相关接口使用转化后的targetId，其他使用地址上的targetId
      // 4.3整改
      const coursewareId = this.target.coursewareId || getQueryString('coursewareId') || '';
      const targetCode = this.target.targetCode || getQueryString('targetCode') || 'kng';
      const targetId = this.target.targetId || getQueryString('targetId') || '';
      const taskId = this.target.taskId || getQueryString('taskId') || '';
      const projectId = this.target.projectId || getQueryString('projectId') || '';
      const flipId = this.target.flipId || getQueryString('flipId') || '';
      const originOrgId = this.target.originOrgId || getQueryString('originOrgId') || '';
      const btid = this.target.batchId || getQueryString('btid') || '';
      const trackId = this.target.trackId || getQueryString('trackId') || '';
      const locateshare = this.target.locateshare || getQueryString('locateshare') || '';
      const gwnlUrl = this.target.gwnlUrl || getQueryString('gwnlUrl') || '';
      const {lastTrackId} = this.detail;
      // 任意端的全量参数
      return {
        kngId: this.id,
        flipId,
        targetId,
        coursewareId,
        trackId: lastTrackId || trackId,
        clientCode: 'stu',
        targetCode,
        projectId,
        kngFlipId: flipId,
        originOrgId,
        btid,
        taskId,
        locateshare,
        gwnlUrl
      };
    },
    // 接口通用参数，学时提交使用接口
    commonParam() {
      let {originOrgId, flipId, btid, targetId, targetCode, projectId, taskId} = this.scanQueryData;
      targetId = targetCode === 'flip' ? flipId : (btid || targetId || projectId);
      const customFunctionCode = getQueryString('customcode') || '';
      return {
        studyParam: {
          originOrgId,
          previewType: 0 // 0不预览，1学员端版本预览 2草稿版本预览
        },
        targetCode,
        targetId,
        targetParam: {
          taskId,
          projectId: projectId || targetId,
          flipId,
          batchId: btid
        },
        customFunctionCode
      };
    },
    courseId() {
      return this.isCourse ? this.id : '';
    },
    allKngList() {
      if (this.chapterList.length && this.chapterList[0].type === 92) {
        return this.chapterList.map(item => item.child).flat();
      }
      return this.chapterList;
    },
    // 是否是课程大纲中的最后一个
    isLastKng() {
      if (this.allKngList.length === 0) return false;
      return this.allKngList.findIndex(item => item.id === this.kngId) === this.allKngList.length - 1;
    },
    isFirstKng() {
      if (this.allKngList.length === 0) return false;
      return this.allKngList.findIndex(item => item.id === this.kngId) === 0;
    },
    // 是否可以评论
    commentAuthConfig() {
      const silenceAuth = this.silenceConfigs.speechCommentOnlineclass === 1; // 合规评论权限
      const speedAuth = !this.userInfo.forbiddenFlag; // 未禁言
      return {
        useable: silenceAuth && speedAuth,
        placeholder: silenceAuth ? (speedAuth ? this.$t('pc_kng_comments_lbl_share_your_mood') : this.$t('pc_kng_comments_msg_forbidden')) : this.$t('pc_kng_comments_btn_no_auth'/** 评论功能已被关闭，如需使用，请与管理员联系 */)
      };
    }
  },
  watch: {
    kngId(val) {
      this.cacheCurrentKng();
      val && this.onKngChangeProvide(val, this.courseId);
    }
  },
  created() {
    this.init();
    this.$root.$on('NOTE_JUMP', this.noteJump);
    this.$root.$on('ERROR_HANDLER', this.errorHandler);
    this.$root.$on('SCHEDULE_UPDATE', this.scheduleUpdate);
    this.$root.$on('CLOSE_CATALOG', this.closeCatalog);
  },
  beforeDestroy() {
    this.$root.$off('NOTE_JUMP', this.noteJump);
    this.$root.$off('ERROR_HANDLER', this.errorHandler);
    this.$root.$off('SCHEDULE_UPDATE', this.scheduleUpdate);
    this.$root.$off('CLOSE_CATALOG', this.closeCatalog);
    this.faceCloseCamera();
  },
  methods: {
    async init() {

      if (!this.id) return;
      yxtReportLog({ category: 501, msg: '用户进入学习' });
      this.onChapterDataStartProvide(this.courseId);
      this.loading = true;
      await this.checkVersion();
      await Promise.all([
        initKngTypeName(),
        this.setCommentTipState(),
        this._deleteStudying(),
        this.initAutoPlay(),
        this.initVideoVersion()
      ]);
      this.setAttributeInfo();
      // 查询详情
      this.detail = await this.getDetail();
      // 相关方配置
      this.setDetailConfig();
      // 记录学习
      this.postUserViewRecord();
      let kngId = this.id;
      let kngType = this.detail.type;
      if (this.isCourse) {
        // 查询章节
        this.chapterList = await this.getChapterList();
        this.handleChapterData();
        const curKng = this.getCurrentKng();
        kngId = curKng.id;
        kngType = curKng.type;
        this.onChapterDataLoadedProvide(this.chapterList, this.courseId);
      }
      if (window.kngAutoPlay && kngType !== KNG_TYPE.NEWLINK) { // 外链不支持自动播放
        this.globalData.autoPlay = !!window.kngAutoPlay;
      }
      if (localStorage.kng_note_tag_item) {
        const note = JSON.parse(localStorage.kng_note_tag_item);
        localStorage.removeItem('kng_note_tag_item');
        if (!this.isCourse || this.allKngList.some(item => item.id === note.kngId)) {
          this.globalData.autoPlay = true;
          this.globalData.notePosition = note.position;
          kngId = note.kngId;
        }
      }
      this.loading = false;
      this.$nextTick(() => this.$refs.frame && this.$refs.frame.setKng(kngId, this.globalData.autoPlay, this.globalData.notePosition));
      yxtReportLog({ category: 501, msg: '用户进入学习', w_succ: 1 });
    },
    async setAttributeInfo() {
      const res = await getAttributeInfo(this.id);
      this.attributeInfo = res;
    },
    // 笔记
    noteJump(obj) {
      const kngId = obj.kngId;
      if (kngId === this.kngId) {
        // 跳转的笔记是当前课件,调用组件方法去定位
        this.$root.$emit('PLAY_APPOINT', obj.position);
      } else {
        // 跳转的笔记不是当前课件,先切换课件
        this.$refs.frame.setKng(kngId, true, obj.position);
      }
    },
    async checkVersion() {
      const params = {
        kngId: this.id,
        targetCode: this.commonParam.targetCode,
        targetId: this.commonParam.targetId
      };
      const [, error] = await checkVersion(params);
      if (error) {
        this.loading = false;
        this.errorHandler(error);
        this.onChapterDataErrorProvide(this.courseId);
        return Promise.reject(error);
      }
    },
    // 获取课件详情
    async getDetail() {
      const params = {
        kngId: this.id,
        ...this.commonParam
      };
      const [, perror] = await preloadKngInfo(params);
      if (perror) {
        this.loading = false;
        this.errorHandler(perror);
        this.onChapterDataErrorProvide(this.courseId);
        return Promise.reject(perror);
      }
      const [res, error] = await getKngDetail(params);
      if (error || res.lastName) { // 理论上不会有lastName了，因为调用了删除正在学
        this.loading = false;
        this.onChapterDataErrorProvide(this.courseId);
        this.errorHandler(error || {key: 'apis.kng.study.needChange'});
        return Promise.reject(error);
      }
      // 项目特殊逻辑，项目任务未完成(newsTag === 0),课程完成了，同步一下进度
      if (this.commonParam.targetCode === 'o2o' && sessionStorage.newsTag === '0' && res.schedule === 100) {
        postSyncScheduleNotice({kngId: this.id, targetCode: 'o2o', targetId: this.scanQueryData.targetId})
          .then(() => sessionStorage.removeItem('newsTag'));
      }
      return res;
    },
    async _deleteStudying() {
      if (!this.isCourse) {
        try {
          await deleteStudying();
        } catch (e) {}
      }
    },
    postUserViewRecord() {
      const {sourceKngId} = this.detail;
      const params = {
        kngId: sourceKngId || this.id,
        targetCode: this.commonParam.targetCode,
        targetId: this.commonParam.targetId || this.id
      };
      postUserViewRecord(params);
    },
    async setDetailConfig() {
      const res = await getCourseSetting({id: this.id});
      Object.keys(res).forEach(key => {
        this.$set(this.detail, key, res[key]);
      });
      // this.detail = {...this.detail, ...res};
    },
    async setCommentTipState() {
      const [res, error] = await getCommentTipState(this.id);
      if (error) {
        this.loading = false;
        this.onChapterDataErrorProvide(this.courseId);
        this.errorHandler(error);
        return Promise.reject(error);
      }
      this.globalData.hadShowCommentTip = !!res.popUpStatus;
    },
    async getChapterList() {
      const params = {
        courseId: this.id,
        ...this.commonParam
      };
      const [res, error] = await getChapters(params);
      if (error) {
        this.loading = false;
        this.errorHandler(error);
        this.onChapterDataErrorProvide(this.courseId);
        return Promise.reject(error);
      }
      return res || [];
    },
    handleChapterData() {
      this.allKngList.forEach(item => {
        item._typeName = getKngTypeName(item.type); // 如果文案需要不同颜色备用
        item._name = `${item._typeName}｜${item.name}`;
      });
      if (this.chapterList !== this.allKngList) {
        this.chapterList.forEach((item, index) => {
          item._prefix = this.detail.distributeSourceType > 0 ? '' : this.$t('pc_kng_detail_lbl_index_chapter', { num: index + 1 }); // 如果文案需要不同颜色备用
          item._name = this.detail.distributeSourceType > 0 ? item.name : this.$t('kng_lbl_index_chapter', { num: index + 1, title: item.name });
        });
      }
    },
    changeKng(kngId, autoPlay = true, position = -1) {
      // 切换时清空播放器级别的错误信息
      this.globalData.playErrorText = '';
      this.globalData.notePosition = position;
      this.globalData.autoPlay = autoPlay;
      this.kngId = kngId;
      if (this.isCourse) {
        const {name, params, query} = this.$route;
        this.$router.replace({name, params, query: {...query, locateshare: this.kngId}});
      }
      // 受locateshare的replace影响，会进入router.afterEach,所以这边加了延迟
      setTimeout(() => setDocumentTitle(this.detail.title));
    },
    // 缓存课程播放到哪个课件
    cacheCurrentKng() {
      if (!this.isCourse || !this.kngId) return;
      const cacheData = JSON.parse(localStorage.CURRENT_COURSE_PLAYING || '{}');
      const key = `${localStorage.userId}|${this.commonParam.targetCode}|${this.courseId}`;
      cacheData[key] = this.kngId;
      localStorage.CURRENT_COURSE_PLAYING = JSON.stringify(cacheData);
    },
    // 获取课程播放到哪个课件
    getCurrentKng() {
      // 返回地址指定的
      const locateShare = getQueryString('locateshare');
      const locateShareItem = this.allKngList.find(item => item.id === locateShare);
      if (locateShareItem) return locateShareItem;

      // 返回缓存的
      const cacheData = JSON.parse(localStorage.CURRENT_COURSE_PLAYING || '{}');
      const key = `${localStorage.userId}|${this.commonParam.targetCode}|${this.courseId}`;
      const cacheKngId = cacheData[key];
      const cacheKngItem = this.allKngList.find(item => item.id === cacheKngId);
      if (cacheKngItem) return cacheKngItem;

      // 返回第一个
      return this.allKngList[0] || {};
    },
    /**
     *
     * @param {*} error 错误
     * @param {*} isInner 是否是播放器内的错误
     * @param {*} catchUnknown 是否要处理未catch的错误码
     * @param {*} callback 调用者的回调方法
     */
    errorHandler(error, {isInner = false, catchUnknown = true, callback} = {}) {
      this.$refs.error.handleCourseError(error, catchUnknown)
        .then(command => {
          if (!this.isCourse && command === 'restudy') {
            this.globalData.autoPlay = true;
            this.init();
          } else if (!this.isCourse && command === 'continue') {
            this.$root.$emit('START_PLAY');
          } else if (command === 'onHookFaceError') {
            this.globalData.autoPlay = false;
            this.init();
          } else {
            callback && callback(command);
          }
        })
        .catch((text) => {
          if (isInner && this.isCourse) {
            this.globalData.playErrorText = text;
          } else {
            this.globalData.errorText = text;
          }
        });
    },
    scheduleUpdate(data = {}, autoNext = false) {
      if ((data && data.percentage === 100) || autoNext) {
        this.$emit('updateProgress', 2, autoNext);
      }
    },
    closeCatalog() {
      this.$emit('needCloseCatalog');
    },
    async initAutoPlay() {
      try {
        if (window.kngAutoPlay === undefined) {
          const res = await getOrgParameter('autoplay');
          window.kngAutoPlay = res && res.value === '1';
        }
      } catch (e) {
        window.kngAutoPlay = false;
      }
    },
    async initVideoVersion() {
      try {
        if (window.isV2VideoPlayer === undefined) {
          const res = await getOrgParameter('isV2VideoPlayer');
          if (res && res.value !== '0') {
            window.isV2VideoPlayer = true;
            window.tokenDomainName = res.value === '1' ? '' : res.value;
          }
        }
      } catch (e) {
        window.isV2VideoPlayer = false;
      }
    },
    setPlayingDetail(detail) {
      this.playingDetail = detail;
    },
    async doFaceRecognition(ctype, callback) {
      let needCheck = 0;
      const { appPlay, faceRecognitionConfigEnabled, studyBeforeRecognition, studyMiddleRecognition, id, recognitionType, type, schedule } = this.playingDetail;

      // 没开pc/h5人脸机构参
      if (!this.showCourseFacialRecognition) {
        if (appPlay) {
          callback && callback(0);
        } else {
          callback && callback(1);
        }
        return;
      }
      let checkType = 0;
      if (ctype === 'studyBefore') {
        checkType = studyBeforeRecognition;
      } else {
        checkType = studyMiddleRecognition;
      }
      if (faceRecognitionConfigEnabled && checkType && schedule !== 100) {
        needCheck = 1;
      }
      if (needCheck) {
        let masterType = 10;
        // targetCode:'o2o', 'flip', 'gwnl'
        switch (this.commonParam.targetCode) {
          case 'o2o':
            masterType = 11;
            break;
          case 'gwnl':
            masterType = 12;
            break;
          case 'flip':
            masterType = 13;
            break;
        }
        this.faceRecognitionData = {
          isRecordFace: true, // 照片是否记录
          masterType: masterType, // 使用场景类型 使用场景类型(1-考试,2-学习计划,10-知识,11-项目,12-人才发展),示例值(1)
          masterId: id, // 使用场景类型 使用场景的主id
          sessionId: this.courseId, // 课程包的场景，传课程包id
          type: 0,
          lifeCheck: !!recognitionType, // 是否活体（0:否,1:是）
          extType: type, // 课件类型
          extId1: recognitionType ? '1' : '0', // 扩至字段（第一位:是否开启活体（0:否,1:是）
          extId2: '80', // 比对分数 下期可能会改为接口
          subjectId: this.commonParam.targetParam.taskId // 放任务id(项目，人才发展场景)
          // isSmallFace: ctype !== 'studyBefore'
        };
        this.showFaceRecognition = true;
        document.body.style.overflow = 'hidden';
        this.faceRecognitionCallBack = callback;
      } else {
        this.showFaceRecognition = false;
        document.body.style.overflow = 'auto';
        callback && callback(1);
      }
    },
    faceMatchSuccess() {
      this.faceCloseCamera();
      this.$nextTick(()=>{
        this.faceRecognitionCallBack && this.faceRecognitionCallBack(1);
        this.showFaceRecognition = false;
        this.faceRecognitionCallBack = undefined;
      });
    },
    faceErrorGoBack() {
      this.faceCloseCamera();
      this.$nextTick(()=>{
        this.faceRecognitionCallBack && this.faceRecognitionCallBack(0);
        this.showFaceRecognition = false;
        this.faceRecognitionCallBack = undefined;
      });

    },
    faceCloseCamera() {
      try {
        document.body.style.overflow = 'auto';
        this.$refs['face'] && this.$refs['face'].closeFaceCamera();
      } catch (e) {

      }
    }
  }
};
</script>
