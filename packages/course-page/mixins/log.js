import { createUuid } from '../utils';
import { LOG_TYPE } from '../enum';
import { studyLog } from '../service';

export default {
  data() {
    return {
      uuid: '',
      submitCount: 0 // 进度提交次数
    };
  },
  created() {
    this.uuid = createUuid();
    this.$root.$on('LOG', this.log);
  },
  mounted() {
    this.log({type: LOG_TYPE.NAV, ext1: '进入播放页'});
  },
  beforeDestroy() {
    this.$root.$off('LOG', this.log);
  },
  methods: {
    /**
     *
     * @param {*} data {ext1,trackId,params,res}
     */
    log(data) {
      const { type, ext1, ext2, ext3, ext4, res, params, trackId} = data;
      let logData = {
        courseId: this.courseId || '',
        kngId: this.kngId || '',
        ext1: ext1 || '',
        ext2: ext2 || '',
        ext3: ext3 || '',
        ext4: ext4 || ''
      };
      switch (type) {
        case LOG_TYPE.SUBMIT:
          if (!res) this.submitCount++;
          logData = {
            ...logData,
            requestMethod: 'post',
            requestJson: {param: {trackId: trackId || ''}, body: params},
            responseJson: res || {}
          };
          if (data.isRollback) {
            logData.requestUrl = 'study/rollBack';
          } else {
            logData.requestUrl = 'study/submit/second';
            logData.ext2 = this.submitCount;
          }
          this.studyLog('002', logData);
          break;
        case LOG_TYPE.NAV:
          this.studyLog('001', logData);
          break;
        case LOG_TYPE.BEHAVIOR:
          this.studyLog('003', logData);
          break;
        case LOG_TYPE.HANGUP:
          this.studyLog('004', logData);
          break;
        case LOG_TYPE.INFO:
          this.studyLog('005', logData);
          break;
      }
    },

    // 进度提交日志记录
    studyLog(logTypeCode, logData) {
      try {
        const bodyParams = {
          logTypeCode, // 日志类型编号logTypeCode
          uuId: this.uuid, // 每次进入页面的唯一id
          pageUrl: location.href, // 页面地址
          requestHeaderJson: { source: 501 }, // 需记录日志的请求接口的头部信息(token不存)
          ext5: (new Date()).toLocaleString(),
          ...logData
        };
        studyLog(bodyParams);
      } catch (error) {
      }
    }
  }
};
