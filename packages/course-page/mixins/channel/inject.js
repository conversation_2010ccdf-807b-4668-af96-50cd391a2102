/**
 * 与provide.js配合使用，用于外部交互
 */
export default {
  methods: {
    // 课程组件开始初始化
    onChapterDataStart(courseId) {},
    // 课程大纲数据加载成功，使用方重写即可
    onChapterDataLoaded(data, courseId) {},
    // 课程大纲数据加载失败，使用方重写即可
    onChapterDataError(courseId) {},
    // 课件变化，使用方重写即可
    onKngChange(kngId, courseId) {},

    // 切换课件的方法
    clickKng(item) {
      this.$root.$emit('onClickKng', item);
    },
    // 上一个课件, then切换成功，catch切换失败
    nextKng() {
      return new Promise((resolve, reject) => {
        this.$root.$emit('onNextKng', res => {
          if (res) resolve();
          else reject();
        });
      });
    },
    // 下一个课件, then切换成功，catch切换失败
    prevKng() {
      return new Promise((resolve, reject) => {
        this.$root.$emit('onPrevKng', res => {
          if (res) resolve();
          else reject();
        });
      });
    }
  },
  created() {
    this.$root.$on('onChapterDataLoaded', this.onChapterDataLoaded);
    this.$root.$on('onChapterDataError', this.onChapterDataError);
    this.$root.$on('onKngChange', this.onKngChange);
    this.$root.$on('onChapterDataStart', this.onChapterDataStart);
  },
  beforeDestroy() {
    this.$root.$off('onChapterDataLoaded', this.onChapterDataLoaded);
    this.$root.$off('onChapterDataError', this.onChapterDataError);
    this.$root.$off('onKngChange', this.onKngChange);
    this.$root.$off('onChapterDataStart', this.onChapterDataStart);
  }
};
