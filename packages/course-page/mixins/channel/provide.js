/**
 * 与inject.js配合使用，用于外部交互
 */
export default {
  methods: {
    onClickKng(item) {
      this.$root.$emit('CHAPTER_ITEM_PLAY', item);
    },
    onNextKng(cb) {
      if (this.isLastKng) cb(false);
      else {
        this.$root.$emit('CHAPTER_NEXT');
        cb(true);
      }
    },
    onPrevKng(cb) {
      if (this.isFirstKng) cb(false);
      else {
        this.$root.$emit('CHAPTER_PREV');
        cb(true);
      }
    },
    onChapterDataStartProvide(courseId) {
      if (courseId) this.$root.$emit('onChapterDataStart', courseId);
    },
    onChapterDataLoadedProvide(data = [], courseId) {
      if (courseId) this.$root.$emit('onChapterDataLoaded', data, courseId);
    },
    onChapterDataErrorProvide(courseId) {
      if (courseId) this.$root.$emit('onChapterDataError', courseId);
    },
    onKngChangeProvide(val, courseId) {
      if (courseId) this.$root.$emit('onKngChange', val, courseId);
    }
  },
  created() {
    this.$root.$on('onClickKng', this.onClickKng);
    this.$root.$on('onNextKng', this.onNextKng);
    this.$root.$on('onPrevKng', this.onPrevKng);
  },
  beforeDestroy() {
    this.$root.$off('onClickKng', this.onClickKng);
    this.$root.$off('onNextKng', this.onNextKng);
    this.$root.$off('onPrevKng', this.onPrevKng);
  }
};
