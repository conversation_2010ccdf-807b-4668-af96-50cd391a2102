<template>
  <div v-loading="loading" class="yxtulcdsdk-simple-course-page  yxtulcdsdk-course-page yxtulcdsdk-ulcdsdk">
    <error-empty v-if="globalData.errorText" class="yxtulcdsdk-empty" :text="globalData.errorText" />
    <template v-else>
      <div class="yxtulcdsdk-course-page__top">
        <!-- 沉浸式播放组件 -->
        <div class="yxtulcdsdk-playframe yxtulcdsdk-ulcdsdk">
          <span class="gray">
            <header class="yxtulcdsdk-header bgheader">
              <div class="yxtulcdsdk-flex-center title pr24 pl10">
                <play-go-back v-if="isGoBack" @setConfirmgoBack="back" />
                <div class="yxtulcdsdk-flex-1 w0 yxtulcdsdk-simple-course-page__title standard-size-16 ellipsis">
                  <div>{{ detail.title }}</div>
                  <div v-if="timeText.length" class="opacity6 standard-size-12 mt4">
                    <span v-for="(text,index) in timeText" :key="index" class="yxtulcdsdk-simple-course-page__split">{{ text }}</span>
                  </div>
                </div>
              </div>
            </header>
            <div class="yxtulcdsdk-container pr bg">
              <template v-if="isCourse">
                <transition
                  name="width-in"
                >
                  <yxtf-scrollbar
                    v-if="showChapter"
                    class="yxtulcdsdk-flex-shrink-0 w320"
                    :fit-height="true"
                    bar-size="6px"
                  >
                    <aside class="yxtulcdsdk-aside pr">
                      <div class="pr">
                        <div class="mt24 yxtulcdsdk-flex-center mr4 ml16 " :class="hasChapter?'mb24':'mb16'">
                          <yxt-ulcd-sdk-svg
                            class="mr4 textcolorop8"
                            width="16px"
                            height="16px"
                            icon-class="ulcdoutline2"
                          />
                          <span class="font-bolder textcolorop8 font-size-14 lh22">{{ $t('pc_ulcdsdk_lbl_curriculum') }}</span>
                        </div>
                        <div class="yxtulcdsdk-catalog mb20">
                          <ul>
                            <li v-for="(item, index) in chapterList" :key="index">
                              <!-- 章节标题 -->
                              <template v-if="item.type === 92">
                                <div class="font-bolder flex textcolorop7 mr28 ellipsis mt20 hand font-size-14 ml16 mb12 lh22" @click="clickKngChapter(item)">
                                  <span><yxt-ulcd-sdk-svg
                                    class="mr12  hand"
                                    width="12px"
                                    height="12px"
                                    :icon-class="item.closed?'putaway':'expand'"
                                  />
                                  </span>
                                  <yxtf-tooltip
                                    class="item"
                                    :open-filter="true"
                                    :content="item.name"
                                    :open-delay="2000"
                                    placement="top"
                                  >
                                    <span class="ulcdsdk-ellipsis-2 ulcdsdk-break">{{ item._name }}</span>
                                  </yxtf-tooltip>
                                </div>
                                <ul v-if="!item.closed">
                                  <li
                                    v-for="(chapter, chapterIndex) in item.child"
                                    :key="chapterIndex"
                                    class="hand font-w400 textcolorop6 linozj mb4 font-size-14 lh22 ml16 mr16"
                                    :class="currentKng.id === chapter.id ? 'liactive' : ''"
                                    @click="setKng(chapter.id)"
                                  >
                                    <!-- 左侧标题 -->
                                    <div class="ml24 yxtulcdsdk-flex-center flex-space-between">
                                      <yxtf-tooltip
                                        class="item"
                                        :open-filter="true"
                                        :content="chapter.name"
                                        :open-delay="2000"
                                        placement="top"
                                      >
                                        <span class="ulcdsdk-ellipsis-2 ulcdsdk-break"><span class="tag-type mr6">{{ chapter._typeName }}</span>{{
                                          chapter.name
                                        }}</span>
                                      </yxtf-tooltip>
                                      <yxt-ulcd-sdk-svg
                                        class="mr14 ml14 hand"
                                        width="20px"
                                        height="20px"
                                        icon-class="kngnotstarted"
                                      />
                                    </div>
                                  </li>
                                </ul>
                              </template>
                              <template v-else>
                                <div
                                  class="hand font-w400 linozj mb4 font-size-14 textcolorop6 lh22 ml16 mr16"
                                  :class="currentKng.id === item.id ? 'liactive' : ''"
                                  @click="setKng(item.id)"
                                >
                                  <div class="ml20 yxtulcdsdk-flex-center flex-space-between">
                                    <yxtf-tooltip
                                      class="item"
                                      :open-filter="true"
                                      :content="item.name"
                                      :open-delay="2000"
                                      placement="top"
                                    >
                                      <span class="ulcdsdk-ellipsis-2 ulcdsdk-break"><span class="tag-type mr6">{{ item._typeName }}</span>{{
                                        item.name
                                      }}</span>
                                    </yxtf-tooltip>
                                    <yxt-ulcd-sdk-svg
                                      class="mr14 ml14 hand"
                                      width="20px"
                                      height="20px"
                                      icon-class="kngnotstarted"
                                    />
                                  </div>
                                </div>
                              </template>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </aside>
                  </yxtf-scrollbar>
                </transition>

                <div class="pr dividerbg">
                  <span
                    class="hand yxtulcdsdk-flex-center-center left-expand-icon leftico"
                    :class="showChapter?'leftico-show':'leftico-hide'"
                    @click="showChapter = !showChapter"
                  >
                    <yxt-ulcd-sdk-svg
                      class="ml2"
                      width="12px"
                      height="12px"
                      icon-class="arrowhead"
                    /></span>
                </div>
              </template>

              <main v-if="currentKng.id" class="yxtulcdsdk-main pr bg">
                <div v-if="currentKng.type === KNG_TYPE.PRACTICE || currentKng.type === KNG_TYPE.EXAM" class="yxtulcdsdk-main__cantsee">
                  <iframe
                    title="iframe-player"
                    class="yxtulcdsdk-course-player__iframe"
                    allowfullscreen="true"
                    webkitallowfullscreen="true"
                    mozallowfullscreen="true"
                    :src="iframeUrl"
                  ></iframe>
                </div>
                <div v-else-if="noPreview" class="yxtulcdsdk-main__cantsee">
                  <div class="yxtulcdsdk-flex-center-center yxtulcdsdk-flex-vertical">
                    <yxtf-svg
                      :remote-url="`${$staticBaseUrl}/ufd/55a3e0/kng/pc/svg`"
                      width="150px"
                      height="125px"
                      icon-class="cannotsee"
                    />
                    <span class="standard-size-16 color-white mt16">{{ noPreviewText }} </span>
                  </div>
                </div>
                <course-player v-else :id="currentKng.id" />
              </main>
            </div>
          </span>
        </div>
      </div>
      <relate-info v-if="detail.id" />
    </template>
    <error-handler ref="error" />
  </div>
</template>

<script>
import { getChapters, getKngDetail } from 'yxt-ulcd-sdk/packages/course-player/service';
import { getMinuteFormat, getQueryString, numberScore } from 'yxt-ulcd-sdk/packages/course-player/utils';
import playGoBack from 'yxt-ulcd-sdk/packages/playframe/src/components/playGoBack.vue';
import CoursePlayer from './components/course-player.vue';
import RelateInfo from './components/relate-info.vue';
import ErrorHandler from 'yxt-ulcd-sdk/packages/course-player/src/components/error/handler.vue';
import ErrorEmpty from 'yxt-ulcd-sdk/packages/course-player/src/components/error/empty.vue';
import Svg from 'packages/_components/svg.vue';
import { getKngTypeName } from '../../course-player/utils';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import { getAttributeInfo, getOrgParameter } from 'yxt-ulcd-sdk/packages/course-page/service';
import { COURSE_ATTRIBUTE } from 'yxt-ulcd-sdk/packages/course-page/enum';
import {commonUtil} from 'yxt-biz-pc';

const factorList = [
  'Internal_network_learning'
];

export default {
  components: {
    playGoBack,
    [Svg.name]: Svg,
    CoursePlayer,
    RelateInfo,
    ErrorHandler,
    ErrorEmpty
  },
  name: 'YxtUlcdSdkSimpleCoursePage',
  props: {
    id: {
      type: String,
      default: '',
      required: true
    },
    isCourse: {
      type: Boolean,
      default: false
    },
    target: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'preview' // preview | visitor
    },
    previewType: {
      type: Number,
      default: 1 // 1学员端版本预览 2草稿版本预览
    }
  },
  provide() {
    return {
      getDetail: () => this.detail,
      getCourseId: () => this.courseId,
      getKngId: () => {
        if (this.isCourse) return this.kngId;
        return this.id;
      },
      getCommonParam: () => this.commonParam,
      getGlobalData: () => this.globalData,
      getChapterList: () => this.chapterList,
      getAllKngList: () => this.allKngList,
      getMode: () => this.mode,
      getScanQueryData: () => this.scanQueryData,
      getAttributeInfo: () => this.attributeInfo,
      getSilenceConfigs: () => {},
      getBossDownloadConfig: () => {},
      getWatermarkConfig: () => {},
      getCheatInfo: () => {},
      getIsUsedByKng: () => true,
      getFactorConfig: () => ({ showMixNetwork: this.showMixNetwork})
    };
  },
  data() {
    return {
      KNG_TYPE,
      loading: false,
      detail: {},
      chapterList: [],
      attributeInfo: {},
      kngId: '',
      showChapter: true,
      globalData: {
        autoPlay: false,
        notePosition: -1, // 笔记要跳转的位置
        playErrorText: '', // 播放器级别错误
        errorText: '' // 全局级别错误
      },
      currentKng: {},
      iframeUrl: '',
      showMixNetwork: false // 混部播放提示
    };
  },
  computed: {
    scanQueryData() {
      const targetCode = getQueryString('targetCode') || 'kng';
      const targetId = getQueryString('targetId') || '';
      const btid = getQueryString('btid') || '';
      const taskId = getQueryString('taskId') || '';
      const projectId = getQueryString('projectId') || '';
      const flipId = getQueryString('flipId') || '';
      const originOrgId = getQueryString('originOrgId') || '';
      // 任意端的全量参数
      return {
        kngId: this.id,
        targetId,
        targetCode,
        btid,
        taskId,
        projectId,
        flipId,
        originOrgId,
        previewType: this.previewType
      };
    },
    // 接口通用参数，学时提交使用接口
    commonParam() {
      let {originOrgId, flipId, btid, targetId, targetCode, projectId, taskId, previewType} = this.scanQueryData;
      const customFunctionCode = getQueryString('customcode') || '';
      return {
        studyParam: {
          originOrgId,
          previewType
        },
        targetCode,
        targetId: btid || targetId,
        targetParam: {
          taskId,
          projectId: projectId || targetId,
          flipId,
          batchId: btid
        },
        customFunctionCode
      };
    },
    // 是否存在章节
    hasChapter() {
      return this.chapterList && this.chapterList.length && this.chapterList[0].type === 92;
    },
    isGoBack() {
      return window.history.length > 1;
    },
    courseId() {
      return this.isCourse ? this.id : '';
    },
    allKngList() {
      if (this.chapterList.length && this.chapterList[0].type === 92) {
        return this.chapterList.map(item => item.child).flat();
      }
      return this.chapterList;
    },
    otherAttrList() {
      return (this.attributeInfo.otherAttributeInfoResponse || []).filter(item => item.attrShow);
    },
    timeText() {
      // 外链课，zip不显示
      const { studyHours, type, studyScore } = this.detail;
      const infoList = [];
      const otherInfo = this.attributeInfo.otherAttributeInfo4RealAttribute || {};

      if (type !== KNG_TYPE.ZIP) {
        infoList.push(this.$t('pc_ulcdsdk_lbl_courseduration'/** 课程总时长{0} */, [getMinuteFormat(studyHours)]));
        studyScore && infoList.push(this.$t('pc_ulcdsdk_lbl_getscore'/** 可获得{0}学分 */, [numberScore(studyScore)]));
      }
      if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.STUDY_NUM)) {
        infoList.push(this.$t('pc_kng_common_lbl_learner_num'/** {0}人学习 */, [otherInfo.studyCount || 0]));
      }
      if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.VIEW_NUM)) {
        infoList.push(this.$t('pc_kng_common_lbl_read_num'/** {0}人浏览 */, [otherInfo.readCount || 0]));
      }
      if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.VIEW_COUNT)) {
        infoList.push(this.$t('pc_kng_common_lbl_view_num'/** {0}次浏览 */, [otherInfo.viewCount || 0]));
      }

      return infoList;
    },
    noPreview() {
      return [KNG_TYPE.PRACTICE, KNG_TYPE.EXAM, KNG_TYPE.SURVEY, KNG_TYPE.DISCUSS].includes(this.currentKng.type);
    },
    noPreviewText() {
      return this.$t('pc_kng_lbl_nopreview', [this.currentKng._typeName]/** {0}不支持预览 */);
    }
  },
  created() {
    this.init();
    this.$root.$on('CHAPTER_ITEM_PLAY', this.clickKng);
    this.$root.$on('ERROR_HANDLER', this.errorHandler);
    this.$root.$on('CLOSE_CATALOG', this.closeCatalog);
  },
  beforeDestroy() {
    this.$root.$off('CHAPTER_ITEM_PLAY', this.clickKng);
    this.$root.$off('ERROR_HANDLER', this.errorHandler);
    this.$root.$off('CLOSE_CATALOG', this.closeCatalog);
  },
  methods: {
    async init() {
      if (!this.id) return;
      this.loading = true;
      await Promise.all([
        this.initAutoPlay(),
        this.initVideoVersion(),
        this.preCheckFactor()
      ]);
      // 查询详情
      this.detail = await this.getDetail();
      let kngId = this.id;
      let kngType = this.detail.type;
      if (this.isCourse) {
        // 查询章节
        this.chapterList = await this.getChapterList();
        this.handleChapterData();
        const curKng = this.getCurrentKng();
        kngId = curKng.id;
        kngType = curKng.type;
      }
      if (window.kngAutoPlay && kngType !== KNG_TYPE.NEWLINK) { // 外链不支持自动播放
        this.globalData.autoPlay = !!window.kngAutoPlay;
      }
      this.setAttributeInfo();
      this.loading = false;
      this.$nextTick(() => this.setKng(kngId, this.globalData.autoPlay));
    },
    async preCheckFactor() {
      await commonUtil.preCheckFunctions(factorList);
      const mixNetwork = commonUtil.checkTimeOutFnc(factorList[0]);
      this.showMixNetwork = mixNetwork >= 2 && mixNetwork <= 4;
    },
    async setAttributeInfo() {
      const params = {};
      if (this.scanQueryData.previewType === 2) params.draft = 1;
      const res = await getAttributeInfo(this.detail.id, params);
      this.attributeInfo = res;
    },
    async getDetail() {
      const params = {
        kngId: this.id,
        ...this.commonParam
      };
      const [res, error] = await getKngDetail(params);
      if (error) {
        this.loading = false;
        this.errorHandler(error);
        return Promise.reject(error);
      }
      return res;
    },
    async getChapterList() {
      const params = {
        courseId: this.id,
        ...this.commonParam
      };
      const [res, error] = await getChapters(params);
      if (error) {
        this.loading = false;
        this.errorHandler(error);
        return Promise.reject(error);
      }
      return res || [];
    },
    handleChapterData() {
      this.allKngList.forEach(item => {
        item._typeName = getKngTypeName(item.type); // 如果文案需要不同颜色备用
        item._name = `${item._typeName}｜${item.name}`;
      });
      if (this.chapterList !== this.allKngList) {
        this.chapterList.forEach((item, index) => {
          item._prefix = this.detail.distributeSourceType > 0 ? '' : this.$t('pc_kng_detail_lbl_index_chapter', { num: index + 1 }); // 如果文案需要不同颜色备用
          item._name = this.detail.distributeSourceType > 0 ? item.name : this.$t('kng_lbl_index_chapter', { num: index + 1, title: item.name });
        });
      }
    },
    getCurrentKng() {
      // 返回第一个
      return (this.allKngList.length && this.allKngList[0]) || {};
    },
    back() {
      if (window.history.length > 1) {
        window.history.back(-1);
      } else if (window.opener) {
        window.location.href = opener.location.href;
      } else {
        window.location.href = `${window.location.origin}/main/#/course/mgmt`;
      }
    },
    //
    setKng(kngId, autoPlay = true, position = -1) {
      this.currentKng = {};
      // 切换时清空播放器级别的错误信息
      this.globalData.playErrorText = '';
      this.globalData.notePosition = position;
      this.globalData.autoPlay = autoPlay;
      this.kngId = kngId;
      this.$nextTick(() => {
        if (this.isCourse) {
          this.currentKng = this.allKngList.find(item => item.id === this.kngId);
        } else {
          this.currentKng = {...this.detail};
        }
        this.setExam(this.currentKng);
        console.log(this.currentKng);
      });
    },
    setExam(knginfo) {
      this.iframeUrl = '';
      if (knginfo.type === KNG_TYPE.EXAM || knginfo.type === KNG_TYPE.PRACTICE) { // 考试预览
        // type	类型 1-考试 2-练习
        const type = knginfo.type === KNG_TYPE.EXAM ? '1' : '2';
        this.iframeUrl = `${location.origin}/ote/#/userExamAuditPreview?arrangeId=${knginfo.id}&type=${type}&previewType=${this.previewType}`;
      }
    },
    clickKng(item) {
      this.setKng(item.id);
    },
    clickKngChapter(item) {
      this.$set(item, 'closed', !item.closed);
    },
    /**
     *
     * @param {*} error 错误
     * @param {*} isInner 是否是播放器内的错误
     * @param {*} catchUnknown 是否要处理未catch的错误码
     * @param {*} callback 调用者的回调方法
     */
    errorHandler(error, {isInner = false, catchUnknown = true, callback} = {}) {
      this.$refs.error && this.$refs.error.handleCourseError(error, catchUnknown)
        .then(command => {
          callback && callback(command);
        })
        .catch((text) => {
          if (isInner && this.isCourse) {
            this.globalData.playErrorText = text;
          } else {
            this.globalData.errorText = text;
          }
        });
    },
    closeCatalog() {
      this.showChapter = false;
    },
    async initAutoPlay() {
      try {
        if (window.kngAutoPlay === undefined) {
          const res = await getOrgParameter('autoplay');
          window.kngAutoPlay = res && res.value === '1';
        }
      } catch (e) {
        window.kngAutoPlay = false;
      }
    },
    async initVideoVersion() {
      try {
        if (window.isV2VideoPlayer === undefined) {
          const res = await getOrgParameter('isV2VideoPlayer');
          if (res && res.value !== '0') {
            window.isV2VideoPlayer = true;
            window.tokenDomainName = res.value === '1' ? '' : res.value;
          }
        }
      } catch (e) {
        window.isV2VideoPlayer = false;
      }
    }
  }
};
</script>
