<!-- 创建时间2023/02/07 15:39:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课件播放器 -->
<template>
  <div v-loading="loading" class="yxtulcdsdk-course-player color-white">
    <div v-if="globalData.playErrorText" class="yxtulcdsdk-fullsize p20 box-border">
      <error-empty :text="globalData.playErrorText" />
    </div>
    <!-- 播放信息 -->
    <div v-else-if="kngDetail.id && !showError" class="yxtulcdsdk-course-player__inner yxtulcdsdk-flex">
      <div class="flex-1 p-rlt yxtulcdsdk-course-player__inner-left w0">
        <!-- 课件播放：音视频、文档、scorm、微课、外链课、html、压缩文件、（三方课） ；组件加载就播放 -->
        <player
          v-if="isPlaying"
          ref="mainPlayer"
          :appoint-view-loc="appointViewLoc"
          :handouts-list.sync="handoutsList"
          :srt-data.sync="srtData"
          :mark-list="markList"
          :kng-detail="kngDetail"
        />
        <template v-else>
          <!-- 封面 -->
          <img
            v-if="kngDetail.coverUrl || detail.coverUrl"
            class="yxtulcdsdk-course-player__cover"
            :src="kngDetail.coverUrl || detail.coverUrl"
          >
          <div class="yxtulcdsdk-flex-center-center yxtulcdsdk-course-player__back">
            <button-view :kng-detail.sync="kngDetail" />
          </div>
        </template>
      </div>
      <div v-if="!showFunctional" key="inner-mid" class="yxtulcdsdk-course-player__inner-mid">
        <div class="yxtulcdsdk-course-player__kng-operate">
          <yxtf-popover
            v-if="playSourceList.length"
            ref="popover"
            v-model="courseSourceVisible"
            width="120"
            popper-class="course-source-list"
            trigger="hover"
            :visible-arrow="false"
            placement="left-start"
          >
            <action-icon
              class="mt0-i"
              slot="reference"
              :title="$t('kng_lbl_language'/** 语言 */)"
              icon-class="lang"
            />
            <div
              v-for="item in playSourceList"
              :key="item.value"
              :class="item.value === kngDetail.playSourceLang ? 'color-primary-6-i' : ''"
              @click="toChangePlaySource(item)"
            >{{ item.name }}</div>
          </yxtf-popover>

          <action-icon
            v-for="(item,index) in kngOperationConfigs"
            :key="index"
            :title="item.title"
            :icon-class="item.icon"
            @click.native="toOperate(item)"
          />
        </div>
      </div>
      <div v-else-if="showFunctional" key="inner-right" class="yxtulcdsdk-course-player__inner-right">
        <!-- 侧边内容 -->
        <key-point v-if="operateType === 'key_point'" :list="markList" @playAppoint="toPlayAppoint" />
        <handouts v-else-if="operateType === 'handouts'" :list="handoutsList" />
        <subtitle-text
          v-else-if="operateType === 'text'"
          :srt-data="srtData"
          :view-loc="kngDetail.viewLoc"
          :srt-lan="kngDetail.srtLan"
        />
        <yxtf-svg
          class="yxtulcdsdk-course-player__right-close"
          width="24px"
          height="24px"
          icon-class="delete-1"
          @click.native="closeFunctional"
        />
      </div>
    </div>
    <div v-if="kngDetail.envFlag === 0" id="private-cloud"></div>
  </div>
</template>

<script>
import emitter from 'yxt-ulcd-sdk/src/mixins/emitter';
import ActionIcon from 'yxt-ulcd-sdk/packages/course-player/src/components/action-icon.vue';
import KeyPoint from 'yxt-ulcd-sdk/packages/course-player/src/components/key-point.vue';
import Handouts from 'yxt-ulcd-sdk/packages/course-player/src/components/handouts.vue';
import SubtitleText from 'yxt-ulcd-sdk/packages/course-player/src/components/subtitle-text';
import ErrorEmpty from 'yxt-ulcd-sdk/packages/course-player/src/components/error/empty.vue';
import ButtonView from './button-view.vue';
import Player from './player.vue';
import { getDraftMarkList, getKngDetail, getMarkList } from 'yxt-ulcd-sdk/packages/course-player/service';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
export default {
  name: 'CoursePlayer',
  components: {
    SubtitleText,
    ActionIcon,
    KeyPoint,
    Handouts,
    Player,
    ButtonView,
    ErrorEmpty
  },
  mixins: [emitter],
  inject: ['getDetail', 'getCourseId', 'getCommonParam', 'getGlobalData'],
  props: {
    id: {} // 课件id
  },
  data() {
    return {
      isPlaying: false,
      loading: false,
      kngDetail: {
        viewLoc: 0,
        srtLan: 'zh'
      },
      showFunctional: false,
      markList: [], // 关键点列表
      handoutsList: [], // 讲义列表
      srtData: [], // 音视频文稿
      appointViewLoc: -1,
      showError: false,
      courseSourceVisible: false
    };
  },
  computed: {
    globalData() {
      return this.getGlobalData();
    },
    detail() {
      return this.getDetail();
    },
    playSourceList() {
      const { multiFileList } = this.kngDetail;
      if (multiFileList && multiFileList.length > 1) {
        return multiFileList.map(item => ({ name: item.langName, value: item.i18nCode, fileId: item.fileId }));
      }
      return [];
    },
    commonParam() {
      return this.getCommonParam();
    },
    courseId() {
      return this.getCourseId();
    },
    isInCourse() {
      return !!this.courseId;
    },
    kngOperationConfigs() {
      const list = [];
      if (this.srtData.length) {
        list.push({ title: this.$t('pc_kng_detail_audio_text'/** 文稿 */), icon: 'text' });
      }
      if (this.handoutsList.length) {
        list.push({title: this.$t('pc_kng_detail_side_title1'/** 讲义*/), icon: 'handouts'});
      }
      if (this.markList.length > 0) {
        list.push({title: this.$t('pc_kng_mark_lbl_key'/** 关键点*/), icon: 'key_point'});
      }
      return list;
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.init();
      }
    }
  },
  created() {
    this.$root.$on('PLAY_APPOINT', this.toPlayAppoint);
  },
  beforeDestroy() {
    this.$root.$off('PLAY_APPOINT', this.toPlayAppoint);
  },
  methods: {
    reset() {
      this.isPlaying = false;
      this.kngDetail = {
        viewLoc: 0
      };
      this.showFunctional = false;
      this.markList = [];
      this.handoutsList = [];
      this.srtData = [];
      this.appointViewLoc = -1;
      this.showError = false;
    },
    /** 请求 */
    async init() {
      this.reset();
      try {
        if (!this.id) return;
        this.loading = true;
        this.markList = await this._getMarkList();
        if (this.detail.type !== KNG_TYPE.COURSE) {
          // 单课件直接使用详情对象
          this.readyPlay(this.detail, this.globalData.autoPlay);
        } else {
          const res = await this._getKngDetail(this.id);
          this.readyPlay(res, this.globalData.autoPlay);
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.loading = false;
      }
    },
    // 获取课件详情
    async _getKngDetail(id) {
      const params = {
        courseId: this.courseId,
        kngId: id,
        ...this.commonParam
      };
      const [res, error] = await getKngDetail(params);
      if (error || res.lastName) { // 理论上不会有lastName了，因为调用了删除正在学
        this._errorHandler(error || {key: 'apis.kng.study.needChange'});
        return Promise.reject(error);
      }
      return res;
    },
    _errorHandler(error) {
      this.$root.$emit('ERROR_HANDLER', error, {
        isInner: true,
        callback: (command) => {
          if (command === 'restudy') {
            this.restudy();
          } else if (command === 'continue') {
            this.$root.$emit('START_PLAY');
          }
        }
      });
    },
    restudy() {
      this.globalData.autoPlay = true;
      this.init();
    },
    // 获取打点
    async _getMarkList() {
      try {
        const req = this.commonParam.studyParam.previewType === 1 ? getMarkList : getDraftMarkList;
        const res = await req(this.id);
        return res;
      } catch (e) {}
      return [];
    },

    // 准备播放
    readyPlay(obj, autoPlay) {
      this.kngDetail = Object.assign(obj, { srtLan: 'zh', playSourceLang: '' });
      const {type} = obj;
      if (type === KNG_TYPE.ZIP) return;
      this.$nextTick(() => {
      // 有默认笔记位置直接播放笔记位置
        if (this.globalData.notePosition > -1) {
          this.toPlayAppoint(this.globalData.notePosition);
        } else if (autoPlay) {
          this.play();
        }
      });
    },
    // 开始播放，isPlaying
    play() {
      // 是否需要兑换
      // 是否按顺序学习
      this.isPlaying = true;
    },

    toOperate(item) {
      switch (item.icon) {
        case 'handouts':
        case 'key_point':
        case 'text':
          this.operateType = item.icon;
          this.showFunctional = true;
          break;
      }
    },
    toChangePlaySource(item) {
      this.courseSourceVisible = false;
      if (this.kngDetail.playSourceLang === item.value) return;
      this.$root.$emit('CHANGE_PLAY_SOURCE', item);
    },
    closeFunctional() {
      this.showFunctional = false;
    },
    // 视频关键点跳转指定位置；笔记跳转指定位置
    toPlayAppoint(time = 0) {
      // 视频未播放，先播放，再执行方法
      if (!this.isPlaying) {
        this.appointViewLoc = time;
        this.play();
      } else {
        this.$refs.mainPlayer && this.$refs.mainPlayer.playViewLoc(time);
      }
    }
  }
};
</script>
