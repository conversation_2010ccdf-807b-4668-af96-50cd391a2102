<!-- 创建时间2023/02/24 15:24:57 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：播放页下方的组件 -->
<template>
  <div class="yxtulcdsdk-course-page__info">
    <div v-if="tabs.length" class="yxtulcdsdk-course-page__info-tab yxtulcdsdk-flex-center-center">
      <yxtf-tabs
        v-model="activeTab"
        class="mt12"
        :bottom-line="false"
      >
        <yxtf-tab-pane
          v-for="item in tabs"
          :key="item.value"
          :label="item.label"
          :name="item.value"
        />
      </yxtf-tabs>
    </div>
    <div ref="info" class="yxtulcdsdk-flex-mid ph40 pv24">
      <div class="yxtulcdsdk-course-page__info-left pv24 ph32 yxtbizf-br-8">
        <!-- 介绍 -->
        <info-intro v-if="activeTab === '0'" />
        <!-- 大纲 -->
        <info-chapter v-if="activeTab === '0'" />
        <!-- 评论 -->
        <info-comment v-if="activeTab==='1' && mode ==='visitor'" v-model="commentCount" readonly />
        <!-- 课程笔记 -->
        <!-- <info-note v-if="activeTab==='2'" v-model="noteCount" /> -->
      </div>
    </div>
  </div>
</template>

<script>
import InfoChapter from 'yxt-ulcd-sdk/packages/course-page/src/components/info-chapter.vue';
import InfoIntro from 'yxt-ulcd-sdk/packages/course-page/src/components/info-intro.vue';
import InfoComment from 'yxt-ulcd-sdk/packages/course-page/src/components/info-comment.vue';
import Svg from 'packages/_components/svg.vue';
import { getCommentList } from 'yxt-ulcd-sdk/packages/course-page/service';
export default {
  name: 'RelateInfo',
  inject: ['getDetail', 'getGlobalData', 'getMode', 'getScanQueryData'],
  components: {
    InfoIntro,
    InfoChapter,
    InfoComment,
    [Svg.name]: Svg
  },
  data() {
    return {
      activeTab: '0',
      attributeInfo: {},
      isSticky: false,
      commentCount: 0,
      noteCount: 0,
      showCommentDialog: false
    };
  },
  computed: {
    globalData() {
      return this.getGlobalData();
    },
    mode() {
      return this.getMode();
    },
    tabs() {
      const list = [
        {label: this.$t('pc_kng_detail_nav_title1'/** 课程介绍 */), value: '0'}
      ];
      if (this.mode === 'visitor') {
        const commentLabel = this.commentCount ? this.$t('pc_kng_project_tit_kngComments'/** 课程评论 */) + `(${this.commentCount})` : this.$t('pc_kng_project_tit_kngComments'/** 课程评论 */);
        list.push({label: commentLabel, value: '1'});
      }
      return list;
    },
    detail() {
      return this.getDetail();
    }
  },
  created() {
    if (this.mode === 'visitor') {
      this.setNoteCommentCount();
    }
  },
  methods: {
    setNoteCommentCount() {
      getCommentList(this.detail.id, {limit: 1, offset: 0})
        .then(({paging}) => {
          this.commentCount = paging.count;
        });
    }
  }
};
</script>
