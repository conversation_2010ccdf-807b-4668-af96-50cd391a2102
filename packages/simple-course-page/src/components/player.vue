<!-- 创建时间2023/02/03 18:59:05 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：组件切换走销毁播放器 -->
<template>
  <div class="yxtulcdsdk-fullsize" :class="{'yxtulcdsdk-web-full': webFull}">
    <div v-if="webFull" class="yxtulcdsdk-course-player__web-header">
      <div class="yxtulcdsdk-flex-center yxtulcdsdk-play-goback mr12 hand" @click="webFull = false">
        <yxt-ulcd-sdk-svg
          class="color-white"
          width="16px"
          height="16px"
          icon-class="returnarrow"
        />
      </div>
      <div class="standard-size-16 ellipsis">{{ kngDetail.title }}</div>
    </div>
    <div class="yxtulcdsdk-course-player__playeare">
      <!-- 讲义、随堂练习、笔记、关键点 -->
      <!-- 讲义可调整尺寸 -->
      <!-- 文档 -->
      <template v-if="playMode === PLAYER_MODE.DOC">
        <yxtbiz-doc-player
          ref="player"
          v-bind="playerProps"
        />
      </template>
      <!-- 音视频 -->
      <!-- 需要打点引导提示 -->
      <yxtbiz-video
        v-else-if="playMode === PLAYER_MODE.VIDEO"
        v-bind="playerProps"
        ref="player"
        width="100%"
        height="100%"
        app-code="kng"
        version="v2"
        @onReady="onReadyVideo"
        @onMeta="onMetaVideo"
        @markJump="toMarkJump"
        @onTime="updateViewLoc"
        @ai-lan-change="onAiLanChange"
        @onPlaybackRate="e => onRateChange(e && e.playbackRate)"
        @onSrtData="onSrtData"
      />
      <yxtbiz-kng-scorm-player
        v-else-if="playMode === PLAYER_MODE.SCORM"
        v-bind="playerProps"
      />
      <!-- 微课：pangu播放器-->
      <PCPlayer
        v-else-if="playMode === PLAYER_MODE.WEIKE"
        ref="player"
        type="WK"
        is-course-lib
        v-bind="playerProps"
        @videoReady="onReady"
      >
        <template slot="watermark">
          <yxtbiz-watermark
            v-if="watermarkOption.enabled"
            :option="watermarkOption"
            app-code="kng"
            version="v2"
            class="z-999"
          />
        </template>
      </PCPlayer>
      <!-- iframe类的（scorm，html） -->
      <iframe
        v-else-if="playMode === PLAYER_MODE.IFRAME"
        ref="player"
        title="iframe-player"
        class="yxtulcdsdk-course-player__iframe"
        allowfullscreen="true"
        webkitallowfullscreen="true"
        mozallowfullscreen="true"
        v-bind="playerProps"
        @load="setAllTimer"
      ></iframe>
      <!-- 跳出的提示&按钮 -->
      <div v-else-if="playMode === PLAYER_MODE.OTHER" class="yxtulcdsdk-course-player__other">
        <img v-if="kngDetail.coverUrl" :src="kngDetail.coverUrl" alt="">
        <div class="yxtulcdsdk-course-player__other-cover break">
          <div class="standard-size-20 mw700">【{{ getKngTypeName(kngDetail.type) }}】{{ kngDetail.title }}</div>
          <div class="standard-size-16 mt12 mw700">{{ playerProps.message }}</div>
          <yxtf-button class="mt24" type="primary" @click="goOut">{{ $t('pc_ulcdsdk_btn_goimmediate'/**立即前往 */) }}</yxtf-button>
        </div>
      </div>
      <!-- 水印：播放组件不支持水印，需要手动添加 -->
      <yxtbiz-watermark
        v-if="manualWatermark && watermarkOption.enabled"
        :option="watermarkOption"
        app-code="kng"
        version="v2"
        class="z-999"
      />
    </div>
  </div>
</template>

<script>
import { addResizeListener, removeResizeListener } from 'yxt-pc';
import { commonUtil as Utils } from 'yxt-biz-pc';
import commonUtil from 'packages/common-util/index';
import { getKngPlayDetail, getO2oProjectName, checkConnection } from 'yxt-ulcd-sdk/packages/course-player/service';
import { ENV_FLAG, KNG_TYPE, THIRD_COURSE_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import { getKngTypeName, openThirdCourse } from 'yxt-ulcd-sdk/packages/course-player/utils';
import specialmixin from 'packages/_mixins/360Special';
import qs from 'qs';

const PLAYER_MODE = {
  DOC: 1,
  VIDEO: 2,
  WEIKE: 3,
  IFRAME: 4,
  OTHER: 5,
  SCORM: 6
};
export default {
  name: 'Player',
  components: {
    PCPlayer: commonUtil.AsyncPCplayer
  },
  mixins: [specialmixin],
  inject: [ 'getCourseId', 'getDetail', 'getCommonParam', 'getScanQueryData', 'getGlobalData', 'getFactorConfig'],
  props: {
    kngDetail: { // 课件详情
      type: Object,
      default: () =>({})
    },
    markList: {
      type: Array,
      default: []
    },
    handoutsList: {
      type: Array,
      default: []
    },
    // 指定的播放时间点，比如笔记以及 关键点
    appointViewLoc: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      KNG_TYPE,
      PLAYER_MODE,

      playMode: 0,
      kngPlayDetail: {},
      playerProps: {},
      manualWatermark: false,

      countdown: 0, // 倒计时时间，单位秒
      rate: 1, // 倍率
      cycleTime: 0, // 播放周期时间，满20秒提交，提交之后重置，等待下一次20s
      unsavedActualTime: 0, // 未提交的实际学时 ，不关联倍速
      unsavedTime: 0, // 未提交的学时， 有可能关联倍速
      hangUpTime: 0, // 挂机时间，鼠标未操作时间
      hangUpStudyTime: 0, // 挂机期间，提交的学时
      hangUpLoc: 0, // 挂机开始的位置点
      viewLoc: 0, // 观看地址
      unknownCount: 0, // 学习提交连续失败次数
      timeNum: 5,
      webFull: false,
      currentPlaySourceLang: ''
    };
  },
  computed: {
    factorConfig() {
      return this.getFactorConfig();
    },
    scanQueryData() {
      return this.getScanQueryData();
    },
    globalData() {
      return this.getGlobalData();
    },
    detail() {
      return this.getDetail();
    },
    courseId() {
      return this.getCourseId();
    },
    commonParam() {
      return this.getCommonParam();
    },
    watermarkOption() {
      const { displayMode, distributeSourceType } = this.kngDetail;
      const { enabled, type } = this.watermarkConfig;
      return (displayMode === 1 || distributeSourceType) ? this.watermarkConfig : {enabled, type};
    },
    watermarkConfig() {
      // watermarkFlag 0跟随平台 1不开启
      let {enabled, type, watermarkContent, otherColor, otherAlpha, otherFontSize, speed, density} = this.kngPlayDetail.watermarkConfig || {};
      let {envFlag, displayMode} = this.kngDetail;
      if (window.feConfig && window.feConfig.orgCode) {// 私有化都打开假水印
        enabled = 1;
      } else if (this.kngDetail.type === KNG_TYPE.DOC && this.kngDetail.watermarkFlag === 0 && enabled && type === 1 && envFlag !== ENV_FLAG.PRIVATE && displayMode !== 1) { // 真水印 关闭假
        enabled = 0;
      } else if (this.kngDetail.watermarkFlag === 1) { // 课件关闭水印 关闭假水印
        enabled = 0;
      }
      return {
        enabled,
        type,
        text: Utils.common.getOpenData(watermarkContent),
        color: `#${otherColor}`,
        opacity: parseInt(otherAlpha),
        fontSize: otherFontSize,
        speed,
        density
      };
    },
    // 新微课播放老微课
    isNewWeiKePlayOldWeiKe() {
      const {type, weikeType, fileId} = this.kngDetail;
      const {oldXuanyesPlayDetails} = this.kngPlayDetail;
      return type === KNG_TYPE.WEIKE && weikeType === 1 && (!oldXuanyesPlayDetails || oldXuanyesPlayDetails.length === 0) && fileId;
    }
  },
  watch: {
    // 记录播放数据 -- 课件切换了, 缓存上一个数据
    // 初始化播放数据
    kngDetail: {
      immediate: true,
      handler(val, old) {
        this.init();
      }
    },
    webFull(val) {
      document.body.style.overflow = val ? 'hidden' : '';
    }
  },
  mounted() {
    addResizeListener(this.$el, this.resize);
    window.addEventListener('keydown', this.keydown);
    this.$root.$on('CHANGE_PLAY_SOURCE', this.changePlaySource);
  },
  beforeDestroy() {
    removeResizeListener(this.$el, this.resize);
    window.removeEventListener('keydown', this.keydown);
    this.$root.$off('CHANGE_PLAY_SOURCE', this.changePlaySource);
    Utils.globalwaterInit.init({ show: true });
  },
  methods: {
    getKngTypeName,
    resize() {
      // html模式播放文档，需要触发resize方法去重置页面布局
      if (this.kngDetail.displayMode === 1) {
        const ev = document.createEvent('Events');
        ev.initEvent('resize', true, false);
        window.dispatchEvent(ev);
      }
    },
    keydown(e) {
      if (e.code === 'Escape') this.webFull = false;
    },
    onReadyVideo() {
      this.yxtPreScreenCap({'playrefs': this.$refs.player});
    },
    onMetaVideo() {
      // 禁止画中画
      document.querySelector('video').setAttribute('disablePictureInPicture', '');

      const { rate } = this.$route.query;
      const { enableShowMultiple } = this.detail;
      if (rate > 1 && rate <= 2) {
        if (enableShowMultiple === 1) {
          const player = this.$refs.player && this.$refs.player.getPlayer();
          player && player.setPlaybackRate(rate);
        } else if (enableShowMultiple !== 1) {
          const { name, params, query } = this.$route;
          this.$router.replace({ name, params, query: { ...query, rate: 1 } });
        }
      }
    },
    async changePlaySource(item) {
      this.kngPlayDetail.fileId = item.fileId;
      // 重置播放属性
      this.playMode = 0;
      this.$nextTick(async() => {
        this.currentPlaySourceLang = item.value;
        this.kngDetail.playSourceLang = this.currentPlaySourceLang;
        // 查询新的播放地址
        const res = await this._getKngPlayDetail();
        this.kngPlayDetail = res;
        await this._initPlayerPropsData();
      });
    },
    /** **************** 初始方法 ***************/
    async init() {
      // 清空播放器倍速缓存，下次进来1倍速
      localStorage.removeItem('baiducyberplayer.playbackRate');
      const res = await this._getKngPlayDetail();
      this.kngPlayDetail = res;
      this.kngDetail.playSourceLang = this.kngPlayDetail.lang;
      this.$emit('update:handoutsList', this.kngPlayDetail.attList || []);
      // 播放器的相关属性
      this.viewLoc = this.getSuitableTime(this.appointViewLoc, this.kngPlayDetail.viewLoc);
      await this._initPlayerPropsData();
      // 新微课、scorm课件带大纲且在课程包或者项目中时自动网页全屏
      if (this.detail.type === KNG_TYPE.COURSE) {
        if (this.playMode === PLAYER_MODE.WEIKE && [7, 8].includes(this.kngDetail.courseSource)) {
          this.webFull = true;
        } else if (this.playMode === PLAYER_MODE.SCORM && this.kngDetail.scormChapter === 1) {
          this.webFull = true;
        }
      }

    },
    // 获取播放详情
    async _getKngPlayDetail() {
      let fullname = '';
      const { id, envFlag, type } = this.kngDetail;
      // 企微isv下 前端获取用户名传给后端打水印
      if (localStorage.sourceCode === '100' && type === KNG_TYPE.DOC) {
        try {
          fullname = await Utils.getUsername(localStorage.fullname);
        } catch (error) { }
      }
      const params = {
        kngId: id,
        courseId: this.courseId,
        fullname,
        lang: this.currentPlaySourceLang,
        ...this.commonParam
      };
      const [res, error] = await getKngPlayDetail(params);
      if (error) {
        this.$message.error(error.message);
        return Promise.reject(params);
      }
      // 校验私有服务器文件的联通性
      if (this.factorConfig.showMixNetwork && envFlag === ENV_FLAG.PRIVATE && [KNG_TYPE.DOC, KNG_TYPE.AUDIO, KNG_TYPE.VIDEO].includes(type) && res.playDetails && res.playDetails.length) {
        const url = res.playDetails[0].url;
        if (url) {
          try {
            await checkConnection(url);
          } catch (e) {
            this.$message.error(this.$t('pc_ulcdsdk_courseplayerror'));
            return Promise.reject(e);
          }
        }
      }
      return res;
    },
    // 设置组件属性数据
    async _initPlayerPropsData() {
      this.playMode = 0;
      this.manualWatermark = false;
      const { type, displayMode, watermarkFlag, distributeSourceType, specialMode, weikeType, envFlag, title, id, fileType} = this.kngDetail;
      const { videoStream, indexPath, playDetails, oldXuanyesPlayDetails, fileId, subtitlesFlag, contentDetails, serverGroupId, watermarkConfig } = this.kngPlayDetail;
      if (type === KNG_TYPE.VIDEO || type === KNG_TYPE.AUDIO || (type === KNG_TYPE.HTML && !!specialMode && videoStream === THIRD_COURSE_TYPE.VIDEO)) {
        const options = playDetails.map(item => {
          return {
            fileFullUrl: item.url,
            resolution: item.desc,
            fileId
          };
        });
        let isNewSdk = true;
        let enableAi = false;
        let srtUrl;
        if (subtitlesFlag === 1 && contentDetails) {
          srtUrl = contentDetails
            .filter(({ desc, url }) => desc.includes('srt') && url)
            .map(({ desc, url }) => ({
              lan: desc === 'srt' ? 'zh' : desc.replace('_srt', ''),
              url
            }));
          if (srtUrl.length > 0) {
            enableAi = true;
          }
        }

        // 音视频+三方课视频流
        this.playerProps = {
          autoStart: true,
          fileId,
          type: type === KNG_TYPE.AUDIO ? 'audio' : 'video',
          isNeedToken: envFlag !== ENV_FLAG.PRIVATE && !specialMode, // 非三方课的公有云音视频需要解密
          isShowWatermark: true,
          playRate: true,
          controlbarDrag: true,
          marks: this.markList, // 关键点
          options: type === KNG_TYPE.AUDIO ? options.slice(0, 1) : options, // 音频取一个
          starttime: this.viewLoc,
          watermarkObj: this.watermarkOption.enabled === 1 ? this.watermarkOption : {},
          enableAi,
          srtUrl,
          lan: Utils.getLanguage(),
          isNewSdk,
          tokenDomainName: window.tokenDomainName
        };
        this.$set(this.kngDetail, 'srtLan', this.playerProps.lan);
        this.playMode = PLAYER_MODE.VIDEO;
      } else if (type === KNG_TYPE.DOC) {
        this.playerProps = {
          mode: displayMode === 1 ? 'html' : 'picture',
          fileId,
          usePPT: fileType === 'ppt',
          start: Math.max(1, this.viewLoc),
          watermarkOrgId: distributeSourceType > 0 ? '7f75434f-4d4d-f507-b67c-bd7d8af1324f' : '',
          showWatermark: watermarkFlag !== 1,
          watermarkCode: 'kng',
          watermarkConfig: this.watermarkConfig,
          serverGroupId,
          virtual: 'auto'
        };
        this.playMode = PLAYER_MODE.DOC;
      } else if (type === KNG_TYPE.SCORM) {
        this.playerProps = {
          query: {
            ...this.scanQueryData,
            kngId: id,
            courseId: this.courseId
          },
          title,
          preview: 1,
          playInfo: this.kngPlayDetail,
          watermarkConfig: this.watermarkOption
        };
        this.playMode = PLAYER_MODE.SCORM;
      } else if (type === KNG_TYPE.WEIKE && weikeType === 2 || this.isNewWeiKePlayOldWeiKe) {
        // 新微课（weikeType） + 新微课播放老微课（fileId）
        this.playerProps = {
          contentId: fileId,
          rightAreaWidth: 256,
          speedChangeIsShow: true,
          playerConfigs: { playbackRate: [1, 1.25, 1.5, 1.75, 2] }
        };
        this.playMode = PLAYER_MODE.WEIKE;
      } else if ((type === KNG_TYPE.HTML && !specialMode) ||
      (type === KNG_TYPE.HTML && !!specialMode && (videoStream === THIRD_COURSE_TYPE.INNER || videoStream === THIRD_COURSE_TYPE.DEFAULT)) ||
      type === KNG_TYPE.WEIKE) {
        let src = playDetails && playDetails.length ? playDetails[0].url : '';
        if (type === KNG_TYPE.HTML && specialMode === 1 && videoStream === THIRD_COURSE_TYPE.DEFAULT) {
          // 三方课默认
          const projectName = await this._getProjectName();
          const token = localStorage.token || '';
          const {orginalKngId, code, orderNum, orderId, customParam} = this.kngPlayDetail;
          const { enabled, text, type, fontSize, color, opacity, speed, density } = this.watermarkConfig;
          const query = {
            token,
            platformVersion: 2,
            pid: code,
            order: orderNum || orderId || '',
            show: enabled,
            marquee: text,
            type,
            fontsize: fontSize,
            color,
            opacity,
            density,
            speed,
            projectName,
            customParam
          };
          src = `${window.feConfig.common.kngThird}#/play/${orginalKngId || ''}?${qs.stringify(query)}`;
        } else if (type === KNG_TYPE.HTML && !!specialMode && videoStream === THIRD_COURSE_TYPE.INNER) {
          // 三方课内嵌
          src = `${src}&token=${localStorage.token}`;
        } else if (type === KNG_TYPE.WEIKE) {
          // 老微课内嵌
          src = oldXuanyesPlayDetails && oldXuanyesPlayDetails.length ? oldXuanyesPlayDetails[0].url : '';
        }
        this.playerProps = {src};
        this.manualWatermark = true;
        this.playMode = PLAYER_MODE.IFRAME;
      } else {
        let src = playDetails && playDetails.length ? playDetails[0].url : '';
        let message = '';
        if (type === KNG_TYPE.HTML && !!specialMode && videoStream === THIRD_COURSE_TYPE.OUTER) {
          // 三方课外跳
          message = this.$t('pc_kng_thired_msg_jump'/** 课程来源于第三方供应商，需进行跳转播放。 */);
        } else if (type === KNG_TYPE.NEWLINK) { // 立即完成
          src = indexPath;
          message = this.$t('pc_kng_newlink_msg_tip'/** 该课程来源于第三方供应商，将会跳转到新页面学习该课程*/);
        }
        this.playerProps = {src, message};
        // 展示文案，跳出去
        this.playMode = PLAYER_MODE.OTHER;
        this.showGoOutWarning();
      }
      // 在线课堂水印开启 关闭全局水印
      if (this.playMode !== PLAYER_MODE.OTHER && watermarkConfig && watermarkConfig.enabled) {
        Utils.globalwaterInit.init({ show: false });
      }
    },
    showGoOutWarning() {
      this.$msgbox({
        type: 'warning',
        title: this.$t('pc_kng_common_msg_title'),
        message: this.playerProps.message,
        showCancelButton: true,
        closeOnClickModal: false
      }).then(() => {
        this.goOut();
      });
    },
    goOut() {
      const { src} = this.playerProps;
      src && openThirdCourse(src);
    },
    // 获取项目名称,项目中的三方课
    async _getProjectName() {
      if (!this.scanQueryData.targetId) return '';
      const [res, error] = await getO2oProjectName(this.scanQueryData.targetId);
      if (error) {
        return '';
      }
      return res.name;
    },

    /** ************ 播放器的回调方法 *****************/
    onReady() {
      this.$refs.player.player && this.$refs.player.player.togglePlay();
    },
    // 指定播放位置
    playViewLoc(time) {
      time = this.getSuitableTime(time, this.viewLoc);
      const {type} = this.kngDetail;
      if (type === KNG_TYPE.VIDEO || type === KNG_TYPE.AUDIO) {
        this.$refs.player.seek(time);
      } else if (type === KNG_TYPE.DOC) {
        this.$refs.player.scrollToPage(time);
      }
    },
    // 获取合适的跳转位置
    getSuitableTime(targetTime, viewLoc) {
      if (targetTime < 0) return viewLoc;
      return targetTime;
    },
    toMarkJump(time) {
      this.$root.$emit('PLAY_APPOINT', time);
    },
    updateViewLoc(e) {
      const loc = Math.floor(e.position);
      this.kngDetail.viewLoc = loc;
    },
    onAiLanChange(e) {
      this.$set(this.kngDetail, 'srtLan', e);
    },
    onSrtData(e) {
      this.$emit('update:srtData', e || []);
    },
    onRateChange(rate) {
      if (this.playMode === PLAYER_MODE.VIDEO) { // 音视频
        const { name, params, query } = this.$route;
        if (query.rate !== rate) {
          this.$router.replace({ name, params, query: { ...query, rate } });
        }
      }
    }
  }
};
</script>
