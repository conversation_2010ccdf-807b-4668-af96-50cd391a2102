<!-- 创建时间2023/02/15 18:14:55 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：打开页面中间按钮显示, 兼容跳出去的，人脸的-->
<template>
  <div class="yxtulcdsdk-flex-center yxtulcdsdk-flex-vertical max-w-560 ulcdsdk-break-word">
    <template v-if="isShowDownload">
      <yxtf-svg
        :remote-url="`${$staticBaseUrl}ufd/407a24/kng/pc/svg/pcsvg`"
        width="120px"
        height="120px"
        icon-class="zip_down"
      />
      <div class="standard-size-20 mt12">{{ title }}</div>
      <yxtf-button
        class="mt16"
        type="primary"
        size="larger"
        @click="toDownload"
      >
        {{ $t('pc_kng_detail_btn_download_zip'/**下载压缩包 */) }}
      </yxtf-button>
    </template>
    <template v-else>
      <!-- 最近学习,您已完成最后一个学习任务，类型 -->
      <div class="standard-size-20">{{ title }}</div>
      <div class="mt16 yxtulcdsdk-flex-center">
        <yxtf-button
          type="primary"
          size="larger"
          @click="play"
        >
          <span class="yxtulcdsdk-flex-center-center">
            <yxtf-svg
              v-if="showPlayIcon"
              :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/pc/svg`"
              class="v-mid mr8"
              width="13px"
              height="16px"
              :icon-class="showPlayIcon?'play-icon':''"
            />
            <span @click="play">{{ $t('pc_kng_detail_start_learn'/** 开始学习*/) }}</span>
          </span>
        </yxtf-button>
      </div>
    </template>
  </div>
</template>

<script>
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import { getManageZipUrl } from 'yxt-ulcd-sdk/packages/course-player/service';
import { getKngTypeName } from 'yxt-ulcd-sdk/packages/course-player/utils';
export default {
  name: 'ButtonView',
  inject: ['getDetail', 'getCourseId', 'getCommonParam', 'getAllKngList'],
  props: {
    kngDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {qrCodeUrl: ''};
  },
  computed: {
    isShowDownload() {
      return this.kngDetail.type === KNG_TYPE.ZIP;
    },
    detail() {
      return this.getDetail();
    },
    courseId() {
      return this.getCourseId();
    },
    commonParam() {
      return this.getCommonParam();
    },
    showPlayIcon() {
      const {type, weikeType, fileId} = this.kngDetail;
      return type === KNG_TYPE.VIDEO ||
      type === KNG_TYPE.AUDIO ||
      (type === KNG_TYPE.WEIKE && weikeType === 2 && !!fileId);
    },
    chapterTitle() {
      if (this.courseId) {
        const list = this.getAllKngList();
        const c = list.find(item => item.id === this.kngDetail.id) || {};
        return c.name || '';
      }
      return '';
    },
    title() {
      const { title, type} = this.kngDetail;
      return `【${getKngTypeName(type)}】${this.chapterTitle || title}`;
    }
  },
  methods: {
    toDownload() {
      const {studyParam} = this.commonParam;
      getManageZipUrl(this.kngDetail.id, studyParam.previewType === 2 ? {idType: 1} : {})
        .then(res => {
          res.downloadUrl && window.open(res.downloadUrl);
        })
        .catch(e => {
          if (e.key === 'apis.kng.knowledge.withoutDownloadPermission') {
            this.$message.error(this.$t('pc_kng_detail_msg_no_permission_download'));
          }
        });
    },
    play() {
      this.$parent.play();
    }
  }
};
</script>
