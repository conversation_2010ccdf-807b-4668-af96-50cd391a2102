<template>
  <div class="yxtulcdsdk-multi-task o2oplayframe-task-container yxtulcdsdk-ulcdsdk">
    <div class="card ph24 pv40">
      <div class="color-26 font-size-24 lh36 weight-bold">{{ headDetail.taskName }}</div>
      <div class="mt24 font-size-14 lh22 flex-mid">
        <span class="color-8c">{{ $t('pc_pd_lbl_classcount') }}</span>
        <span class="ml12">{{ $t('pc_pd_count', [pager.count]) }}</span>
        <span class="color-8c ml48 mr12">{{ $t('pc_o2o_lbl_finish_standard' /* 完成标准 */) }}</span>
        <span>{{ headDetail.finishStand?finishStandard[headDetail.finishStand-1]:'--' }}</span>
      </div>
    </div>

    <div class="card p24 mt24">
      <div class="color-26 font-size-20 lh32 weight-bold">{{ $t('pc_o2o_lbl_multitasklist' /* 班次列表 */) }}</div>
      <div class="yxtulcdsdk-ulcdsdk">
        <yxtf-empty
          v-if="!tableData.length"
          style="top: calc(50% - 133px);"
          :empty-text="$t('pc_ulcdsdk_no_scene'/**暂无班次 */)"
        />
        <div class="yxtulcdsdk-multi-list">
          <!-- 班次列表 -->
          <div
            v-for="(item,i) in tableData"
            :key="item.id"
            class="ph24 pt20 yxtulcdsdk-multi-list-item mt16"
            :class="{'mr16': (i+1)%3!==0}"
          >
            <div>
              <div class="color-26 lh24 font-size-16 min-width0 flex align-items-center">
                <yxtf-tooltip
                  :content="item.subTaskName"
                  :open-filter="true"
                  placement="top"
                >
                  <div class="ellipsis font-weight-500">{{ item.subTaskName||'--' }}
                  </div>
                </yxtf-tooltip>
                <yxtf-tag
                  v-if="item.currentFlag"
                  size="small"
                  effect="dark"
                  class="align-unset ml8 flex-shrink-0"
                >
                  {{ $t('pc_o2o_lbl_present' /*当前*/) }}
                </yxtf-tag>
                <yxtf-tag
                  size="small"
                  :type="item.status !==2 ? 'info' : ''"
                  class="align-unset flex-shrink-0"
                  :class="{ml8: !item.currentFlag}"
                >
                  {{ statusFormat(item.status,1) }}
                </yxtf-tag>
              </div>
              <div class="color-8c lh20 font-size-12 mt8">
                <div>{{ `${$t('pc_o2o_lbl_class_no')}：${item.orderIndex}` }}</div>
                <div class="mt4">{{ `${$t('pc_pd_lbl_starttime')}：${dateFormat(item.startTime)}` }}</div>
                <div class="mt4">{{ `${$t('pc_pd_lbl_endtime')}：${dateFormat(item.endTime)}` }}</div>
                <div
                  v-if="item.inSubTask === 1"
                  class="mt4"
                >{{ `${$t('pc_o2o_lbl_completionStatus')}：${statusFormat(item.resultStatus,2)}` }}
                  <yxtf-tooltip
                    v-if="item.handCompleted === 1"
                    :content="$t('pc_o2o_handleCompleted_tip'/* 管理员手动标记完成 */)"
                    placement="top"
                  >
                    <o2o-svg
                      :module="'o2o'"
                      type="other/svg"
                      icon-class="info-cirlce-o"
                      width="16px"
                      height="16px"
                      class="color-gray-6 hover-primary-6 hand ml4"
                    />
                  </yxtf-tooltip>
                </div>
                <div
                  v-if="(item.auditStatus && item.auditStatus !== 2) || (item.auditStatus ===2 && item.inSubTask ===1)"
                  class="mt4 hand"
                  :style="{color: ['#FA8C16','#59C623','#F5222D' , '#59C623'][item.auditStatus-1]}"
                  @click="toAudit(item.formId)"
                >{{ `${$t('pc_o2o_lbl_auditstatus')}：` }}
                  {{ $t(['pc_o2o_lbl_auditing','pc_o2o_tip_audit_success','pc_o2o_tip_audit_failed' , 'pc_o2o_tip_audit_revoked'][item.auditStatus-1]) }}
                  <i class="yxt-icon-arrow-right ml4"></i>
                </div>
                <!-- 开启自动加入，同时有人数限制 -->
                <div v-if="item.showRemainingSpots" class="mt4">
                  {{ $t('pc_pd_rest_allowed_num' /* 剩余名额 */) }}：{{ $t('pc_o2o_lbl_countofpeople' /* 0人 */, [item.remainingSpots]) }}
                </div>
              </div>
            </div>
            <div class="pv8 yxtulcdsdk-multi-list-item__footer flex align-items-center justify-center mt8">
              <!-- inSubTask 学员是否在班次 0:不在 1:在 -->
              <yxtf-button
                v-if="item.inSubTask===1"
                type="text"
                @click="$emit('multiClick', item)"
              >
                {{ $t('pc_o2o_lbl_checkdetail') }}
              </yxtf-button>
              <ButtonTooltip
                v-else
                type="primary-text"
                :visible-arrow="true"
                v-bind="getJoinButtonStatusAndTip(item)"
                :loading="item.joinClassBtnLoading"
                @click="handleJoinin(item)"
              >
                {{ $t('pc_o2o_lbl_joinIn') }}
              </ButtonTooltip>
            </div>
          </div>
        </div>
        <!-- 分页控件 -->
        <yxtf-pagination
          v-if="pager.count > 0"
          class="mt16 text-right"
          layout="total, prev, pager, next, sizes, jumper"
          :page-size="pager.limit"
          :total="pager.count"
          :current-page="pager.current"
          simple-total
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <yxtf-dialog
      padding-size="small"
      :visible.sync="auditTipShow"
      :cutline="false"
      :show-close="false"
      width="440px"
    >
      <div class="flex align-items-center">
        <yxtf-svg
          class="d-icon"
          width="20px"
          height="20px"
          icon-class="icons/f_feedback-success"
        />
        <span class="font-size-16 ml12 color-26 lh24">{{ $t('pc_pd_joinin_title') }}</span>
      </div>
      <div class="d-detail ml32 mt8 font-size-14 lh22">
        <div>{{ $t('pc_pd_joinin_content') }}</div>
        <!-- <div>{{ $t('pc_pd_joinin_auditor') }}：{{ auditors || '--' }}</div> -->
      </div>
      <div class="flex justify-end mt24">
        <yxtf-button type="primary" @click="auditTipShow = false">{{ $t('pc_pd_lbl_iknow') }}</yxtf-button>
      </div>
    </yxtf-dialog>
  </div>
</template>

<script>
import {fetchAllowedRestClassNum, getSubTaskHead, joinSubTask, getSubTaskList } from '../service';
import ButtonTooltip from 'packages/_components/buttonWithTooltip';
import dayjs from 'dayjs';
import O2oSvg from 'packages/_components/svg.vue';
import { commonUtil, SponsorChoose } from 'yxt-biz-pc';

export default {
  name: 'YxtUlcdSdkMultiTask',
  components: {
    ButtonTooltip,
    O2oSvg
  },
  props: {
    projectId: String,
    taskId: String
  },
  data() {
    return {
      auditors: '',
      auditTipShow: false,
      pager: {
        limit: 10,
        count: 0,
        pages: 0,
        current: 1
      },
      statusList: [{ value: 1, label: this.$t('pc_o2o_lbl_notyetgegun' /* 未开始 */) }, { value: 2, label: this.$t('pc_o2o_lbl_ongoing' /* 进行中 */) }, { value: 3, label: this.$t('pc_o2o_lbl_projectover' /* 已结束 */) }],
      resultList: [{ value: 0, label: this.$t('pc_o2o_lbl_uncompleted' /* 未完成 */) }, { value: 2, label: this.$t('pc_o2o_lbl_finished' /* 已完成 */) }, { value: 3, label: this.$t('pc_o2o_lbl_delaycomplete' /* 延期完成 */) }],
      tableData: [],
      dialogVisible: false,
      headDetail: {},
      finishStandard: [this.$t('pc_o2o_lbl_finishstandard_one' /* 完成下列任意班次即算完成任务 */), this.$t('pc_o2o_lbl_finishstandard_two'/* 完成带有“当前”标签的班次即算完成任务 */)],
      joinClassBtnLoading: false
    };
  },
  mounted() {
    const { classId, targetId, trackId } = this.$route.query;
    // 具体班次二维码直接跳转到相应班次
    if (classId) {
      this.$emit('multiClick', {subTaskId: classId, targetId, trackId});
    } else {
      this.getInitInfo();
      this.getPages(1);
    }
  },
  methods: {
    async checkJoinedClassNum(item) {
      const restNum = await this.getAllowedRestClassNum(item);
      if (restNum === -1) return Promise.resolve(); // -1 表示不限制班次数量，存在学员端页面没刷新，调用了改接口
      if (restNum === 0) { // 剩余次数为零的时候直接提示
        this.$fmessage.error(this.$t('pc_pd_joined_class_num_overflow' /* 加入的班次数已超上限 */));
        return Promise.reject();
      }
      return this.confirmForClassNumLimited(item, restNum);
    },
    /**
     * 获取可以加入的剩余班次数量
     */
    async getAllowedRestClassNum(item) {
      try {
        const params = {
          orgId: window.localStorage.getItem('orgId'),
          projectId: this.projectId,
          taskId: this.taskId
        };
        this.$set(item, 'joinClassBtnLoading', true);
        return await fetchAllowedRestClassNum(params);
      } finally {
        this.$set(item, 'joinClassBtnLoading', false);
      }
    },
    /**
     * 限制加入班次数量 confirm
     */
    confirmForClassNumLimited(item, restNum) {
      const {auditOpened, subTaskName} = item;
      const {joinSubCount} = this.headDetail;
      const joinedNum = joinSubCount - restNum; // 已加入班次数量
      let msg = this.$t('pc_pd_joined_class_consume_time' /* 你可以加入{0}个（后台配置的值）班次，已加入{1}个，是否继续加入【{2}】？ */, [joinSubCount, joinedNum, subTaskName]);
      if (auditOpened) msg += `<div>${this.$t('pc_pd_audit_failed_tip' /* 若审核不通过，不计入加入次数 */)}</div>`;
      const title = this.$t('pc_o2o_lbl_tip' /* 提示 */);
      return this.$confirm(msg, title, {
        type: 'warning',
        dangerouslyUseHTMLString: true
      });
    },
    /**
     * 获取班次 能否加入和提示
     */
    getJoinButtonStatusAndTip(item) {
      //  auditStatus 审核状态  1:审核中 2：审核通过 3:审核未通过 4:审核撤销
      const {auditStatus, status, showRemainingSpots, remainingSpots} = item;
      const learnerNumFull = showRemainingSpots && remainingSpots === 0; // 人数已满，是否开启自主加入 + 是否开启限制 来判断
      let contentKey = 'pc_pd_tip_projectover';/* 此班次已结束，不可加入 */
      if (item.auditStatus === 1) {
        contentKey = 'pc_pd_tip_auditing';/* 审核中，不可重复加入 */
      } else if (learnerNumFull) {
        contentKey = 'pc_pd_multi_class_limited_num_tip'; /*  人数已满，请选择其他班次 */
      }
      return {
        disabled: auditStatus === 1 || status === 3 || learnerNumFull,
        content: this.$t(contentKey)
      };
    },
    dateFormat(date) {
      return date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '--';
    },
    toAudit(planId) {
      this.openAuditPage({
        formId: planId,
        formType: 22
      });
    },
    /**
     * 跳转审核详情
    */
    async openAuditPage({ formId, formType, orgId, userId, name = '_blank' }) {
      try {
        await commonUtil.goAuditDetail(formId, formType, orgId, userId, name);
      } catch (error) {
        this.$message.error(this.$t('pc_o2o_lbl_permisson_plan_audit').d('您暂无权限查看此审核单'));
      }
    },

    // 获取列表页信息
    getPages(pageIndex) {
      const params = {
        projectId: this.projectId,
        taskId: this.taskId,
        userId: localStorage.getItem('userId') || ''
      };
      if (pageIndex) {
        this.pager.current = 1;
      }

      this.pager.offset = (this.pager.current - 1) * this.pager.limit;
      getSubTaskList({ ...params, ...this.pager }).then(res => {
        if (res) {
          this.tableData = res.records;
          this.pager.pages = res.pages;
          this.pager.count = res.total;
        }
      });
    },

    handleCurrentChange(val) {
      this.pager.current = val;
      this.getPages();
    },

    handleSizeChange(val) {
      this.pager.limit = val;
      this.getPages(true);
    },
    // type-1:班次状态，2：完成状态
    statusFormat(id, type) {
      let list;
      if (type === 1) {
        list = id !== null ? this.statusList.filter(item => item.value === id) : [];
      } else {
        list = id !== null ? this.resultList.filter(item => item.value === id) : [];
      }
      return list && list.length > 0 ? list[0].label : '--';
    },
    async handleJoinin(item) {
      const {subTaskId, auditOpened} = item;
      // 开启自主加入（能进来就是开启了自主加入)  // 设置了班次数量限制
      if (this.headDetail.joinSubCount && this.headDetail.joinSubCount !== -1) {
      // 班次数量是否已满check
        await this.checkJoinedClassNum(item);
      }

      if (auditOpened) {
        SponsorChoose.open({
          props: { // 传递的值
            code: 'Multiple_shifts',
            projId: subTaskId
          },
          onSuccess: (val) => {
            const optionalBeans = JSON.stringify(val);
            this.join(subTaskId, true, optionalBeans);
          },
          onError: (val) => {
            console.log('error', val);
            this.$message({
              type: 'error',
              message: this.$t('pc_o2o_msg_submitfail' /* 提交失败 */)
            });
          }
        });
      } else {
        this.join(subTaskId, false);
      }
    },
    join(subTaskId, needAudit, optionalBeans) {
      joinSubTask({ projectId: this.projectId, subTaskId, fromType: 22, optionalBeans }).then(res => {
        this.getPages();
        if (needAudit) {
          this.auditTipShow = true;
          this.auditors = res.auditorNames;
        } else {
          this.$message.success(this.$t('pc_pd_tip_joinin_success' /* 加入成功，你已是此班次的学员 */));
        }
      }).catch(e => {
        if (e && e.code === 2110025) {
          this.$message.error(e.message);
          this.getPages(); // 刷新当前页
        }
      });
    },
    // 获取头部信息
    getInitInfo() {
      const params = {
        projectId: this.projectId,
        taskId: this.taskId,
        userId: localStorage.getItem('userId') || ''
      };
      getSubTaskHead(params).then(res => {
        if (res) {
          this.headDetail = res;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.yxtulcdsdk-multi-task {
  min-height: 857px;
  margin: 0 auto;
  background-color: transparent !important;
}

.card {
  background: #fff;
  border-radius: 8px;
}

.pv40 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.yxtulcdsdk-multi-list {
  display: flex;
  flex-wrap: wrap;
}

.yxtulcdsdk-multi-list-item {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  width: calc((100% - 32px) / 3);
  border: 1px solid #e9e9e9;
  border-radius: 4px;
}

.yxtulcdsdk-multi-list-item__footer {
  border-top: 1px solid #f6f7fa;
}

.justify-end {
  justify-content: flex-end;
}
</style>
