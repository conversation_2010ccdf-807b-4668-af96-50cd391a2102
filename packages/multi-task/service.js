import {o2oApi} from 'packages/api';
import qs from 'qs';

// 学员端查看多班次任务
export const getSubTaskList = params => {
  return o2oApi.get(`/study/project/sub/task/list?${qs.stringify(params)}`);
};

// 学员端查看多班次任务头详情
export const getSubTaskHead = params => {
  return o2oApi.get(`/study/project/sub/task/head?${qs.stringify(params)}`);
};

// 学员加入多班次任务
export const joinSubTask = params => {
  return o2oApi.post('/subTask/audit/apply/join/sub', params);
};

// 查询学员加入的多班次任务的审核模版
export const getAuditId = (subTaskId) => {
  return o2oApi.get(`/subTask/audit/auditTemp/info/${subTaskId}`);
};

/**
 *  获取学员可以加入的剩余班次数量 
 * @param {Object} params 
 * @returns Promise<number>
 */
export const fetchAllowedRestClassNum = params => o2oApi.get('/subTask/audit/apply/join/sub/count', params)