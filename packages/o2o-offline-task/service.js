import { flipApi } from 'packages/api';
import qs from 'qs';

// 面授详情
export const getFlipDetail = (id, params) => {
  return flipApi.get(`/flip/${id}?${qs.stringify(params)}`);
};

// 查询讲师列表
export const getFlipTeachers = (flipId) => {
  return flipApi.get(`/app/flipUser/getInstructors/${flipId}`);
};

// 课前预习列表
export const getPreviewList = (flipDetailId, params) => {
  return flipApi.get(`/userPush/${flipDetailId}/list?${qs.stringify(params)}`);
};

// 学员未读变已读（小红点）
export const postCourseRead = (id) => {
  return flipApi.post(`/userPush/${id}/atom/read`);
};

/**
   * 调查局查看地址
   * pageType 1：个人答题详细，2：个人答题统计（仅支持登录用户查看，不支持userId指定查看）
   * userId 对应 pageType 为1：需要查看的用户Id，不传查看登录人的答卷，2：不传
   */
export const getSurveyUrl = (taskId, userId) => {
  return flipApi.get(`/flipSurvey/answerurl?pageType=1&taskId=${taskId}&userId=${userId}&targetId=`);
};

// 下载文件
export const putThirdUrl = fileId => {
  return flipApi.put(`/fileController/file/${fileId}`);
};

/**
   * 获取文件当前转码状态
   * @param {String} flipId 面授id
   * @param {String} fileId 文件id
   * @returns
   */
export const checkTranscodeStatus = (flipId, fileId) => flipApi.get(`flip/trans/${flipId}/${fileId}`);

// 获取学员有无访问权限
export const checkMemberAuth = (params) => {
  return flipApi.post('/flipAuth/checkMemberAuth', params);
};

// 外部游客查询ulcd配置
export const getUlcdConfig = (orgId, flipId) => {
  return flipApi.get(`flipExternalUserConfig/get/${orgId}/${flipId}`);
};

// 获取当前用户信息
export const postUserInfo = (data) => {
  return flipApi.post('/FlipGroup/getGroupMemberDetail', data);
};

// 获取分组下学员列表
export const postGroupMembers = (data) => {
  return flipApi.post('flipGroup/getFlipGroupMemberList', data);
};

// 面授项目分组基本信息
export const getFlipGroupBase = (params) => {
  return flipApi.get(`/flipGroup/getFlipProjectGroupBase?${qs.stringify(params)}`);
};

// 获取小组白板id和点评数据
export const getBoardInfo = (groupId) => {
  return flipApi.get(`flipGroup/get/group/board/info?groupId=${groupId}`);
};

// 白板小组人员权限校验
export const checkBoardPermission = (boardId, userId, type = 0) => {
  return flipApi.get(`flipGroup/check/permission?userId=${userId}&boardId=${boardId}&type=${type}`);
};

// 分组下人员列表-固定长度
export const getFlipGroupMemberListFixLimit = (params) => {
  return flipApi.get(`/flipGroup/getFlipGroupMemberListFixLimit?${qs.stringify(params)}`);
};

// 分组下人员列表-page
export const getFlipGroupMemberPageList = (params) => {
  return flipApi.get(`/flipGroup/getFlipGroupMemberPageList?${qs.stringify(params)}`);
};
