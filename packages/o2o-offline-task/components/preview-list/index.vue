<template>
  <div class="p24 bg-white radius4">
    <label class="lh26 font-size-18 font-weight-500 color-26 mb16">{{ $t('pc_flip_lbl_preview_before') }}</label>
    <div v-if="list.length">
      <div
        v-for="(item, index) of list"
        :key="item.id"
        class="flip-stu-preview-item pl32 pr"
        :class="{pb24: index !== list.length - 1}"
      >
        <flip-svg
          module="flip"
          front
          icon-class="time"
          width="16px"
          height="16px"
          class="icon-history pa c-43"
        />
        <label class="d-block text-8c f-s-12 lh18">{{ item.releaseTimeStr }} {{ $t('pc_flip_lbl_receive') }}{{ item.typeName }}</label>
        <div class="content mt8 radius8 layout-flex layout-align-center layout-justify-between hand" @click="preview(item)">
          <div class="flex-1 mr24 min-width-0">
            <!-- 未读、并且未销毁 -->
            <i class="pr" :class="{'is-new': item.studyState === 1 && item.destroyState !== 1}">
            </i>
            <div class="layout-flex color-26 font-size-14 lh22 min-width-0">
              <div class="flex-shrink-0">【{{ item.typeName }}】</div>
              <div class="ellipsis">{{ item.arrangeName }}</div>
            </div>
            <div class="ml8 mt4 flex-item font-size-12 lh20 color-8c">
              <span class="mr24">{{ item.progressStr }}</span>
              <span v-if="item.preNum">{{ $t('pc_flip_lbl_previewed_num', [item.preNum || 0]) }}</span>
            </div>
          </div>
          <!-- 课件设置了焚毁 1 已焚毁，2 正常状态(没有设置焚毁)，3 设置了焚毁 -->
          <div
            v-if="item.destroyState !== 2"
            class="color-8c font-size-12 lh20 mr16"
          >{{ item.timeStr }}
          </div>
          <ButtonWidthTooltip
            v-if="enablePublishOperation"
            :type="item.progressState === 3 ? 'info' : 'primary'"
            :disabled="item.destroyState === 1"
            :content="$t('pc_ulcdsdk_task_destory')"
          >{{ $t(taskBtn[item.type] || 'pc_o2o_lbl_tostudy') }}</ButtonWidthTooltip>
        </div>
      </div>
    </div>
    <yxtf-empty v-else-if="!loading" size="small" />
  </div>
</template>

<script>
import { getPreviewList, postCourseRead } from '../../service';
import { prefix0, getTypeName, formatFriendDate } from '../../utils';
import FlipSvg from 'packages/_components/svg';
import ButtonWidthTooltip from 'packages/_components/buttonWithTooltip';
export default {
  components: {FlipSvg, ButtonWidthTooltip},
  props: {
    flipId: [String, Number],
    trackId: String,
    projectId: String,
    hasPermission: Boolean, // 是否有权限
    enablePublishOperation: Boolean // 是否有权限
  },
  data() {
    return {
      loading: true,
      list: [],
      taskBtn: // 按钮上文字
        {
          4: this.$t('pc_o2o_lbl_tostudy'), // 去学习
          6: this.$t('pc_o2o_lbl_toexaming'), // 去考试
          8: this.$t('pc_o2o_lbl_toevaluate') // 去评价
        }
    };
  },
  watch: {
    hasPermission: {
      immediate: true,
      handler(v) {
        v && this.loadData();
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timeId);
  },
  mounted() {
    const _this = this;
    const hiddenProperty = 'hidden' in document ? 'hidden' : 'webkitHidden' in document ? 'webkitHidden' : 'mozHidden' in document ? 'mozHidden' : null;
    const visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange');
    const onVisibilityChange = function() {
      if (document[hiddenProperty]) {
        // 页面离开后，需要关闭计时器，记录离开时间
        clearInterval(_this.timeId);
        _this.leaveTime = (new Date()).getTime();
      } else {
        // 页面激活后，记录激活时间，课件数据计时要减去相差的时间，并重新启动计时器
        _this.enterTime = (new Date()).getTime();
        const _timeDiff = Math.round((_this.enterTime - _this.leaveTime) / 1000);
        _this.updatePerviewList(_timeDiff);
        _this.setTime();
      }
    };
    document.addEventListener(visibilityChangeEvent, onVisibilityChange);
  },
  methods: {
    loadData() {
      this.loading = true;
      clearInterval(this.timeId);
      const getProgressStr = (satte) => {
        if (satte === 3) return this.$t('pc_flip_lbl_completed');
        if (satte === 2) return this.$t('pc_flip_lbl_ongoing');
        else return this.$t('pc_flip_lbl_not_involved');
      };
      getPreviewList(this.flipId, { page: 1, pageSize: 1000, pushType: 1, trackId: this.trackId }).then(res => {
        // 课前预习列表
        this.list = res.flipUserPushVos ? res.flipUserPushVos.map(item => ({
          ...item,
          releaseTimeStr: formatFriendDate(item.createTime),
          typeName: getTypeName(item),
          progressStr: getProgressStr(item.progressState), // 1未开始,2进行中,3已完成
          // studyState 1未读 2进行中 3已完成
          timeStr: ''
        })) : [];
        // 启用定时器
        if (this.list.length) this.setTime();
      }).catch(e => {
        this.$message.error(e);
      }).finally(() => {
        this.loading = false;
      });
    },
    formatTime(s) {
      const h = parseInt(s / (60 * 60)); // 总小时
      const d = parseInt(h / 24) || 0; // 天
      const _h = h % 24; // 剩余小时
      return (d ? `${d}${this.$t('pc_flip_lbl_day')} ` : '') + `${prefix0(_h)}:${prefix0(parseInt(s / 60 - h * 60))}:${prefix0(s % 60)}`;
    },
    // 倒计时
    setTime() {
      const format = () => {
        this.updatePerviewList();
      };
      format();
      this.timeId = setInterval(() => {
        format();
      }, 1000);
    },
    // 课前预习数据更新
    updatePerviewList(timeDiff) {
      const _this = this;
      _this.list.forEach(item => {
        // 课件没有设置焚毁 1 已焚毁，2 正常状态(没有设置焚毁)，3 设置了焚毁
        if (item.destroyState === 2) return;
        // 课件没有焚毁
        if (item.destroyState === 3) {
          item.remainTime = timeDiff ? (item.remainTime - timeDiff) : (item.remainTime - 1);
          if (item.remainTime <= 0) {
            item.destroyState = 1;
          } else {
            item.timeStr = _this.$t('pc_flip_lbl_destroyed_time', [_this.formatTime(item.remainTime)]);
          }
        }
        item.destroyState === 1 && (item.timeStr = _this.$t('pc_flip_lbl_destroyed'));
      });
    },
    // 打开课前预习课件
    preview(item) {
      // 已结束、已归档、已删除
      if (!this.enablePublishOperation) {
        return this.$message(this.$t('pc_pd_f2f_stu_tip_limit').d('已删除、结束、归档的任务不支持该操作'));
      }
      // 已焚毁
      if (item.destroyState === 1) {
        return this.$message(this.$t('pc_ulcdsdk_task_destory'));
      }
      // 游客访问权限拦截
      if (localStorage.getItem('isVisitor') === '1') {
        return this.$message(this.$t('pc_flip_tip_novisitoraccess'));
      }
      if (item.loading) return;
      item.loading = true;
      // 标为已读
      postCourseRead(item.id).then(res => {
        // 已删除（0：否；1：是）
        if (res.deleted === 1) {
          this.$message.error(this.$t('pc_flip_tip_deleted'));
          this.loadData();
          return;
        }
        // 已焚毁
        if (res.destroyState === 1) {
          this.$message(this.$t('pc_ulcdsdk_task_destory'));
          item.destroyState = 1;
          return;
        }
        const { trackId, targetId } = item;
        const pid = this.projectId;
        if (item.type === 4) { // 练习
          const params = {
            trackId,
            praId: targetId
          };
          this.$emit('toTask', 19, 'yxt-ulcd-sdk-practicing', params);
          // window.open(`/ote/#/practicepreview?${qs.stringify(params)}`);
        } else if (item.type === 6) { // 考试
          const params = {
            arrangeId: targetId,
            trackId
          };

          this.$emit('toTask', 2, 'yxt-ulcd-sdk-examing', params);
          // window.open(`/ote/#/exampreview?${qs.stringify(params)}`);
        } else if (item.type === 8) { // 问卷
          // if (item.studyState !== 3) {
          const params = {
            type: 2,
            allowResult: 1,
            id: item.arrangeId,
            tid: this.flipId,
            tname: '',
            success: '',
            step: item.progressState, // 1未开始,2进行中,3已完成
            trackId,
            hideBack: true
          };

          this.$emit('toTask', 3, 'yxt-ulcd-sdk-surveying', params, item.typeName);
        } else { // 知识
          const params = {
            targetId: pid, // 项目id
            flipId: this.flipId, // 面授 id
            kngId: targetId, // 知识id
            coursewareId: item.arrangeId, // 知识对应面授中主键id
            clientCode: 'stu',
            targetCode: 'flip', // 来源标识，对应 kng 中 appCode
            trackId,
            projectId: item.projectId,
            id: item.targetId, // 知识ID
            kngFlipId: item.arrangeId, // 知识对应面授中主键id
            entry: 0
          };
          this.$emit('toTask', item.type, 'yxt-ulcd-sdk-course-page', params, item.typeName);
        }
      }).catch(e => {
        this.$message.error(e);
      }).then(() => {
        item.loading = false;
      });
    }
  }
};

</script>
<style lang='scss' scoped>

.flip-stu-preview-item {
  width: calc(100% - 32px);

  &::before {
    position: absolute;
    top: 14px;
    bottom: 0;
    left: 7px;
    width: 1px;
    background: linear-gradient(to bottom, transparent 0, transparent 4px, #e7ebfa 4px, #e7ebfa 8px);
    background-size: 1px 8px;
    content: '';
  }

  &:last-child {
    &::before {
      display: none;
    }
  }

  & > svg {
    color: var(--color-primary) !important;
  }

  .is-new {
    &::before {
      position: absolute;
      top: 7px;
      right: -4px;
      width: 8px;
      height: 8px;
      background-color: #f5222d;
      border-radius: 50%;
      content: '';
    }
  }

  .content {
    padding: 16px 24px 16px 8px;
    border: 1px solid #E9E9E9;

    &:hover {
      box-shadow: 0 2px 6px 0 rgba(102, 107, 128, 0.12);
    }
  }

  .yxtf-tag {
    margin: 0;
    color: #8c8c8c;
  }

  .icon-history {
    top: 2px;
    left: 0;
  }
}
</style>
