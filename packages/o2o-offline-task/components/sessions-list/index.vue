<template>
  <div class="flip-stu-sessions flex flex-wrap">
    <div
      v-for="(item, index) of list"
      :key="item.id"
      :span="8"
      class="sessions-item mt16 mr16 p20 radius8 over-hidden bg-white"
    >
      <yxtf-tooltip :open-filter="true" :content="item.sceneName" placement="top">
        <label class="pl20 d-block lh24 font-size-16 font-weight-500 nowrap ellipsis pr color-26">{{ $t('pc_flip_lbl_sessions', [index + 1]) }}  {{ item.sceneName }}</label>
      </yxtf-tooltip>
      <ul class="mt12 c-59 font-size-14">
        <!--        面授场次时间      -->
        <li class="mt8 lh20 pr flex align-items-center">
          <yxt-svg
            icon-class="common_time"
            width="16px"
            height="16px"
            class="mr3 c-59"
          />{{ formatDate(item) }}
        </li>
        <li class="mt8 lh20 pr nowrap flex align-items-center">
          <flip-svg
            module="flip"
            flip
            icon-class="address"
            width="16px"
            height="16px"
            class="mr3 c-59 flex-shrink-0"
          />
          <yxtf-tooltip :open-filter="true" :content="formatAddress(item)" placement="top">
            <span class="mr3 ellipsis">{{ formatAddress(item) || $t('pc_ulcdsdk_no_address') }}</span>
          </yxtf-tooltip>
        </li>
        <li
          v-if="(item.enableSignIn && formatSignDate(item, true)) || (item.enableSignOut && formatSignDate(item, false))"
          class="mt16 pv12 pl16 pr24 session-item-sign flex align-items-center justify-between hand"
          @click="toSign(item)"
        >
          <div class="font-size-12 lh20 color-59">
            <div v-if="item.enableSignIn && formatSignDate(item, true)">{{ `${$t('pc_ulcdsdk_clock_in')}：${formatSignDate(item, true)}` }}</div>
            <div v-if="item.enableSignOut && formatSignDate(item, false)" :class="{'mt8': item.enableSignIn}">{{ `${$t('pc_ulcdsdk_clock_out')}：${formatSignDate(item, false)}` }}</div>
          </div>
          <yxtf-button
            v-if="enablePublishOperation"
            type="primary"
            class="flex-0"
            @click.stop="toSign(item)"
          >{{ $t('pc_ulcdsdk_lbl_sign_in') }}</yxtf-button>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import FlipSvg from 'packages/_components/svg';
import { formatFriendDate } from '../../utils';
export default {
  components: {FlipSvg},
  props: {
    appCode: String,
    list: {
      type: Array,
      default: function() { return []; }
    },
    projectId: String,
    enablePublishOperation: Boolean
  },
  data() {
    return {
    };
  },
  methods: {
    formatAddress(item) {
      if (item.locationName) {
        return item.locationName + (item.address ? '-' + item.address : '');
      } else {
        return item.address;
      }
    },
    formatSignDate(date, signin = true) {
      const {attendanceDetailResp } = date;
      const { signInBeginTime, signInEndTime, signOutBeginTime, signOutEndTime} = attendanceDetailResp;
      if (signin) {
        return signInBeginTime ? `${formatFriendDate(signInBeginTime, 'HH:mm')} ~ ${formatFriendDate(signInEndTime, 'HH:mm')}` : '';
      } else {
        return signOutBeginTime ? `${formatFriendDate(signOutBeginTime, 'HH:mm')} ~ ${formatFriendDate(signOutEndTime, 'HH:mm')}` : '';
      }
    },
    getAttendanceIdBySharedStatus({ generateRule, shareAttendId, id }) { // 通过共享考勤状态获取考勤id
      const attendanceShared = generateRule === 1; // 共享考勤
      return attendanceShared ? shareAttendId : id;
    },
    toSign(scene) {
      if (!this.enablePublishOperation) {
        // 已结束、已归档、已删除
        this.$message(this.$t('pc_pd_f2f_stu_tip_limit').d('已删除、结束、归档的任务不支持该操作'));
        return;
      }
      const params = {
        id: this.getAttendanceIdBySharedStatus(scene),
        appCode: this.appCode,
        bizId: this.projectId
      };
      this.$emit('toTask', 0, 'yxt-ulcd-sdk-attend', params);
    },
    // 格式化日期
    formatDate(item) {
      const startStr = dayjs(item.startTime).format('YYYY-MM-DD HH:mm');
      const endStr = dayjs(item.endTime).format('YYYY-MM-DD HH:mm');
      return `${startStr} ~ ${endStr}`;
    }
  }
};

</script>
<style lang='scss' scoped>

.flip-stu-sessions {
  margin-right: -16px;

  label {
    margin-left: -20px;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 4px;
      height: 16px;
      margin-top: -8px;
      background-color: var(--color-primary);
      border-radius: 2px;
      content: '';
    }
  }

  .sessions-item {
    box-sizing: border-box;
    width: calc(50% - 16px);
    border: 1px solid #e9e9e9;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 20px 0 rgba(102, 107, 128, 0.12);

      .address {
        white-space: normal;
        word-break: break-all;
      }
    }
  }

  .session-item-sign {
    background: #fafafa;
    border-radius: 4px;
  }
}
</style>
