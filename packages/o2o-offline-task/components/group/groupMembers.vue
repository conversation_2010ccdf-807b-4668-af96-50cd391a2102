<template>
  <div>
    <div
      class="live-course-preview-group-name lh22 font-size-14 color-26"
      :class="{mb4: showAll}"
    >
      <span
        v-if="showAll"
        class="font-size-14 lh22 color-8c font-weight-500"
      >{{ $t('pc_o2o_lbl_belong_group' /*所属小组*/) }}：</span>
      {{ groupName }}
    </div>
    <div class="live-course-preview-group-body">
      <div
        v-for="item in list"
        :key="item.id"
        class="live-course-preview-group-body-item mt16 hand"
        @click="goUserIndex(item.userId)"
      >
        <yxtf-portrait
          :img-url="item.imgUrl"
          :username="item.fullname"
          size="40px"
          class="flex-shrink-0"
        />
        <div class="ml8 min-width0 flex layout-flex-vertical">
          <yxtf-tooltip
            :open-filter="true"
            :content="item.fullname"
            placement="top"
          >
            <yxtbiz-user-name
              class="font-size-14 lh22 color-26 ellipsis avatar-name"
              :name="item.fullname"
            />
          </yxtf-tooltip>
          <div
            v-if="item.canOwner===1"
            class="font-size-12 lh20 color-8c"
          >{{ $t('pc_pd_lbl_leader') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    groupMembers: {
      type: Array,
      default: () => {
        return [];
      }
    },
    groupName: {
      type: String
    },
    showAll: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    list() {
      return this.groupMembers.slice(0, this.showAll ? this.groupMembers.length : 5);
    }
  },
  methods: {
    goUserIndex(userId) {
      window.open(`${window.location.origin}/#/userindex?userId=${userId}`);
    }
  }
};
</script>

<style lang="scss" scoped>
.live-course-preview-group-body-item {
  display: flex;
  align-items: center;

  &:hover {
    .avatar-name {
      color: var(--color-primary);
    }
  }
}

.avatar-name {
  display: inline-block;
  max-width: 100%;
}
</style>
