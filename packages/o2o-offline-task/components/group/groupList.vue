<template>
  <div>
    <div
      class="live-course-preview-group-name mb4 lh22 font-size-14 color-26"
    >
      <span
        class="font-size-14 lh22 color-8c font-weight-500"
      >{{ $t('pc_o2o_lbl_belong_group' /*所属小组*/) }}：</span>
      {{ groupName }}
    </div>
    <!--学员列表-->
    <div>
      <yxtf-infinite-list
        :loading="loading"
        :error="isLoadError"
        :finished="noMore"
        :is-page="false"
        style="height: 500px;"
        :finished-text="$t('pc_o2o_tip_nomore')"
        @load="loadList"
      >
        <div class="live-course-preview-group-body">
          <div
            v-for="item in list"
            :key="item.id"
            class="live-course-preview-group-body-item mt16 hand"
            @click="goUserIndex(item.userId)"
          >
            <yxtf-portrait
              :img-url="item.imgUrl"
              :username="item.fullname"
              size="40px"
              class="flex-shrink-0"
            />
            <div class="ml8 min-width0 flex layout-flex-vertical">
              <yxtf-tooltip
                :open-filter="true"
                :content="item.fullname"
                placement="top"
              >
                <yxtbiz-user-name
                  class="font-size-14 lh22 color-26 ellipsis avatar-name"
                  :name="item.fullname"
                />
              </yxtf-tooltip>
              <div
                v-if="item.canOwner===1"
                class="font-size-12 lh20 color-8c"
              >{{ $t('pc_pd_lbl_leader') }}</div>
            </div>
          </div>
        </div>
      </yxtf-infinite-list>
    </div>
  </div>
</template>

<script>

import { getFlipGroupMemberPageList } from '../../service';
export default {
  name: 'Yxtgrouplist',
  data() {
    return {
      isLoadError: false,
      list: [],
      loading: true, // 阻止初始化时无限滚动回调
      noMore: false,
      empty: false,
      current: 1,
      // 分页数据
      limit: 10,
      page: 1,
      info: []
    };
  },
  props: {
    flipDetailId: String,
    groupName: {
      type: String
    },
    groupId: String
  },
  created() {
    this.reload();
  },
  computed: {
  },
  watch: {
  },
  methods: {
    // 学员排名列表
    loadList() {
      this.loading = true;
      this.empty = false;
      const params = {
        flipDetailId: this.flipDetailId,
        groupId: this.groupId,
        limit: this.limit,
        offset: (this.current - 1) * this.limit
      };
      getFlipGroupMemberPageList(params).then((res) => {
        // 用户的已获学分 积分 学时
        // const { ranks, ...rankInfo } = res.data;
        this.list.push(...res.datas);

        // 数据总数
        const count = res.paging.count;
        this.empty = count === 0;
        if (this.list.length >= count) {
          this.noMore = true;
        } else {
          this.current = this.current + 1;
          this.noMore = false;
        }
      })
        .catch(() => (this.isLoadError = true))
        .finally(() => {
          this.loading = false;
        });
    },
    reload() {
      this.page = 1;
      this.limit = 10;
      this.current = 1;
      this.list = [];
      this.loadList();
    },
    goUserIndex(userId) {
      window.open(`${window.location.origin}/#/userindex?userId=${userId}`);
    }
  }
};
</script>

<style lang="scss" scoped>
.live-course-preview-group-body-item {
  display: flex;
  align-items: center;

  &:hover {
    .avatar-name {
      color: var(--color-primary);
    }
  }
}

.avatar-name {
  display: inline-block;
  max-width: 100%;
}
</style>

