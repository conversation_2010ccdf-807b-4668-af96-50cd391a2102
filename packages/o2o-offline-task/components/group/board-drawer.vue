<template>
  <!-- 小组白板 -->
  <yxt-dialog
    :visible.sync="visible"
    :title="groupName"
    custom-class="yxtulcdsdk-o2o-offline__board"
    fullscreen
    @close="clear"
  >
    <iframe
      id="boardIframe"
      :src="boardUrl"
      title="White Board"
    ></iframe>
  </yxt-dialog>
</template>

<script>
let boardIframe = null;
export default {
  props: {
    // 小组名称，默认值保留空格，空字符串dialog title不显示样式错乱
    groupName: {
      type: String,
      default: ' '
    },
    // 小组id
    groupId: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      boardUrl: '',
      visible: false,
      boardId: ''
    };
  },
  methods: {
    clear() {
      this.boardUrl = '';
      this.boardId = '';
    },
    sendWbMsg(msg) {
      boardIframe.contentWindow.postMessage(JSON.stringify(msg), '*');
    },
    openBoard() {
      const userId = localStorage.userId;
      const name = localStorage.fullname;
      const meetDomain = window.feConfig.common.meet;
      // 获取小组白板id
      this.boardId = this.id;
      this.boardUrl = `${meetDomain}/ewb3/index.html?room=${this.boardId}&client=o2o&name=${name}`;
      this.visible = true;
      this.$nextTick(() => {
        boardIframe = boardIframe || document.getElementById('boardIframe');
        boardIframe.addEventListener('load', () => {
          // 发送login消息给白板，否则无法加载
          this.sendWbMsg({ action: 'login', value: userId });
        });
      });
    }
  }
};
</script>
<style lang="scss">
// 小组白板dialog
.yxtulcdsdk-o2o-offline__board {
  .yxt-scrollbar__view,
  .yxt-dialog__body {
    width: 100%;
    height: 100%;
    padding: 0 !important;
    display: flex;

    iframe {
      flex: 1;
      width: 100%;
      height: 100%;
      border: none;
    }
  }
}
</style>
