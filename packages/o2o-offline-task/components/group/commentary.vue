<!-- 查看点评 -->
<template>
  <yxtf-dialog
    width="480px"
    :visible.sync="dialogVisible"
    :title="$t('pc_ulcdsdk_viewCommentary').d('查看点评')"
  >
    <div class="yxtulcdsdk-flex-center">
      <span class="color-gray-7">{{ $t('pc_ulcdsdk_lbl_groupName').d('小组名称：') }}</span>
      <span class="font-bolder ellipsis flex-1">{{ groupName }}</span>
    </div>
    <div class="mt16 color-gray-10 pb8">
      <pre
        class="commentary-pre"
        v-html="commentary"
      ></pre>
    </div>
  </yxtf-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 小组名称
    groupName: {
      type: String,
      default: ''
    },
    // 点评内容
    commentary: {
      type: String,
      default: ''
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    }
  },
  data() {
    return {
      dialogVisible: false
    };
  }
};
</script>
<style lang="scss">
.commentary-pre {
  white-space: pre-line;
}
</style>
