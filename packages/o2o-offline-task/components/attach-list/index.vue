<template>
  <div>
    <ul class="flip-attachments pv24 pl24 pr32 bg-white radius4">
      <li class="mb8 lh24 font-size-18 lh26 color-26 font-weight-500">{{ $t('pc_pd_lbl_attachments' /* 附件 */) }}</li>
      <li v-for="(file,index) in files" :key="file.fileId" :class="['flip-attachments__item layout-flex layout-align-center yxtbizf-br-4',{mt4: index!== 0 }]">
        <div class="h64 text-no-select hand flex-1 over-hidden layout-flex layout-align-center flip-attachments__item-left" @click="handlePreview(file)">
          <yxtf-svg
            class="flex-shrink-0"
            :icon-class="`icons/f_kng-${file.iconType}`"
            width="24px"
            height="24px"
          />
          <div class="flex-1 ml8 mr12 over-hidden">
            <yxtf-tooltip :content="file.name" placement="top" open-filter>
              <div class="ellipsis color-gray-9 nowrap flip-attachments__item-name">{{ file.name }}</div>
            </yxtf-tooltip>
            <div class="f-s-12 lh18 color-gray-6">
              <!-- status: 0 未转码 1 转码中 2 转码成功 3 转码失败 -->
              <div v-if="isTranscodeSuccess(file)"><span>{{ file.fileSize | formatFileSize }}</span></div>
              <div v-else-if="file.status === 1">{{ $t('pc_flip_lbl_transcoding' /* 转码中 */) }}</div>
              <div v-else-if="file.status === 3">{{ $t('pc_flip_tip_transcodefail' /* 转码失败 */) }}</div>
            </div>
          </div>
        </div>
        <div class="flex-shrink-0 layout-flex layout-align-center">
          <yxtf-tooltip
            :content="$t('pc_flip__download' /* 下载 */)"
            placement="top"
            effect="light"
            :visible-arrow="false"
          >
            <div class="w20 h20 yxtbizf-br-2 hover-bg-e9 layout-flex layout-align-center layout-justify-center hand text-no-select" @click="handleListDownload(file)">
              <yxtf-svg
                icon-class="download"
                width="16px"
                height="16px"
                class="color-gray-7"
              />
            </div>
          </yxtf-tooltip>
          <div v-if="isTranscodeSuccess(file)" class="ml4 w20 h20 yxtbizf-br-2 hover-bg-e9 layout-flex layout-align-center layout-justify-center hand text-no-select">
            <yxtbiz-file-join-knglib
              :file-id="file.fileId || ''"
              :file-name="getFilenameAndExtname(file.name).prefix"
              :file-size="file.fileSize"
              :file-ext="getFilenameAndExtname(file.name).suffix"
              width="16px"
              height="16px"
              class="color-gray-7"
            />
          </div>
        </div>
      </li>
      <yxtf-empty v-if="files.length === 0" size="small" />
    </ul>
    <yxtbiz-course-player
      v-if="fileIdList.length > 0"
      :download-cb="downloadFile"
      :add-to-kng-store-cb="addToKngStoreCb"
      :visible.sync="showCoursePlayer"
      :file-id-list="fileIdList"
      :start="coursePlayerStart"
    />
    <yxtbiz-file-join-knglib
      v-if="files.length !== 0"
      ref="knglib"
      :file-id="kngLibFile.fileId"
      :file-name="kngLibFile.fileName"
      :file-size="kngLibFile.fileSize"
      :file-ext="kngLibFile.fileExt"
    >
      <span></span>
    </yxtbiz-file-join-knglib>
  </div>
</template>

<script>

import { formatFileSize, getFilenameAndExtname } from '../../utils';
import previewDownload from './preview-download';
export default {
  mixins: [previewDownload],
  props: {
    flipId: String
  },
  data() {
    return {
      kngLibFile: {
        fileId: '',
        fileName: '',
        fileSize: 0,
        fileExt: ''
      }
    };
  },
  filters: {
    formatFileSize: value => formatFileSize(value)
  },
  methods: {
    isTranscodeSuccess: ({ fileType, status }) => ['image', 'zip'].includes(fileType) || status === 2, // 图片和zip文件直接视为转码成功
    handleListDownload(file) {
      const { fileId, downloading } = file;
      if (downloading) return;
      file.downloading = true;
      this.downloadFile(fileId)
        .finally(() => (file.downloading = false));
    },
    handlePreview(file) {
      this.previewFile({ file, flipId: this.flipId });
    },
    getFilenameAndExtname: filename => getFilenameAndExtname(filename),
    addToKngStoreCb(fileId) {
      const { name, fileSize } = this.files.find(file => file.fileId === fileId);
      const { prefix, suffix } = getFilenameAndExtname(name);
      this.kngLibFile = {
        fileId,
        fileName: prefix,
        fileSize,
        fileExt: suffix
      };
      this.$refs.knglib.showDialog();
    }
  }
};

</script>
<style lang='scss' scoped>
.flip-attachments {

  &__item-left {
    &:hover {
      .flip-attachments__item-name {
        color: var(--color-primary-6);
      }
    }
  }
}
</style>
