import { putThirdUrl, checkTranscodeStatus } from '../../service';

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      coursePlayerStart: 0,
      showCoursePlayer: false
    };
  },
  computed: {
    fileIdList: ({ list }) => list.map(file => file.fileId),
    files() {
      this.list.forEach(file => {
        this.addCustomProperty(file);
      });
      return this.list;
    }
  },

  methods: {
    addCustomProperty(file) {
      const anObject = {
        downloading: false, // 下载中
        canPreview: false, // 弱网多次触发的时候, 只有最后一次点击的元素可以 preview
        transcodeChecking: false // 正在校验转码状态
      };
      for (let key in anObject) {
        file[key] = anObject[key];
      }
    },
    async previewFile({ file, flipId }) {
      //  file.status (0:未转码 1:转码中,2: 转码成功, 3:转码失败;
      if (file.fileType === 'zip') return this.$fmessage.warning(this.$t('pc_flip_tip_no_support_preview')); // 该文件不支持预览，请下载该文件
      if (file.fileType !== 'image') { // 非图片文件进行转码校验
        if (file.transcodeChecking) return;
        if (file.status === 1) {
          try {
            file.transcodeChecking = true;
            const status = await this.checkTranscodeStatus(flipId, file.fileId);
            file.status = status;
          } finally {
            file.transcodeChecking = false;
          }
        }

        if (file.status === 1) { // 转码中
          return this.$fmessage.warning(this.$t('pc_flip_transcoding_no_preview' /* 转码中，暂不支持预览 */));
        }

        if (file.status === 3) { // 转码失败
          return this.$fmessage.warning(this.$t('pc_flip_transcode_fail_pls_download' /* 文件转码失败，请下载后查看 */));
        }
      }

      this.files.forEach(file => (file.canPreview = false));
      file.canPreview = true; // 当前可以展示
      this.previewDocAndImg(file.fileId);
    },
    checkTranscodeStatus(flipId, fileId) {
      return checkTranscodeStatus(flipId, fileId)
        .then(({ data: { status } }) => status);
    },
    previewDocAndImg(fileId) {
      const startIndex = this.fileIdList.findIndex(id => id === fileId);
      Object.assign(this, {
        coursePlayerStart: startIndex > -1 ? startIndex : 0,
        showCoursePlayer: true
      });
    },
    downloadFile(fileId) {
      return putThirdUrl(fileId).then(res => {
        this.downloadFileByHref(res.downloadUrl);
      });
    },
    downloadFileByHref(url) {(window.location.href = url);}
  }
};
