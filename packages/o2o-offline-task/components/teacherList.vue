<template>
  <div v-if="list.length" class="bg-white radius4 pl24">
    <div class="font-size-18 lh26 mb-8 font-weight-500">{{ $t('pc_pd_lbl_offlineinstructor') }}</div>
    <div class="flex flex-wrap">
      <div
        v-for="teacher of list"
        :key="teacher.id"
        class="mt24 mr24 layout-flex layout-align-center cursor-p teacher-item"
        @click="openUserInfo(teacher.id)"
      >
        <yxtf-portrait
          size="48px"
          :img-url="teacher.avatar"
          :username="teacher.fullname"
          class="hand"
        />
        <div class="ml12 flex-1 flex-item">
          <yxtf-tooltip open-filter placement="top" :content="teacher.fullname">
            <yxtbiz-user-name class="d-block nowrap ellipsis font-size-16 lh24 color-26 user-name" :name="teacher.fullname" />
          </yxtf-tooltip>
          <yxtf-tooltip open-filter placement="top" :content="teacher.deptName">
            <yxtbiz-dept-name class="d-block c-75 nowrap ellipsis mt2 font-size-14 lh22 color-75" :name="teacher.deptName" />
          </yxtf-tooltip>
        </div>
      </div>
    </div>
    <div v-if="loading" v-floading="loading" class="pv32"></div>
  </div>
</template>

<script>

import { getFlipTeachers } from '../service.js';
export default {
  props: {
    flipId: [String, Number],
    hasPermission: Boolean
  },
  data() {
    return {
      loading: false,
      list: []
    };
  },
  watch: {
    hasPermission: {
      immediate: true,
      handler(v) {
        v && this.loadData();
      }
    }
  },
  methods: {
    openUserInfo(userId) {
      // 游客身份
      if (localStorage.getItem('isVisitor') === '1') {
        return this.$message(this.$t('pc_flip_tip_novisitoraccess'));
      }
      userId && window.open(`${window.location.origin}/#/userindex?userId=${userId}`);
    },
    loadData() {
      this.loading = true;
      getFlipTeachers(this.flipId).then(res => {
        this.list = res;
      }).catch(e => {
        this.$message.error(e);
      }).then(() => {
        this.loading = false;
      });
    }
  }
};

</script>
<style lang='scss' scoped>
.mb-8 {
  margin-top: -8px;
}
.flex-item {
  max-width: 140px;
}
// 鼠标样式
.cursor-p {
  cursor: pointer;
}
.teacher-item:hover {
  .user-name {
    color: var(--color-primary) !important;
  }
}
</style>
