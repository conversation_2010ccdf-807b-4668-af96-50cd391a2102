<!--
 * @FileDescription:
 * @Author: 陈瑞军
 * @Date: 2021-09-14 16:19:23
 * @LastEditors: 陈瑞军
 * @LastEditTime: 2021-10-19 15:15:56
-->
<template>
  <div class="desc-text-section" :class="{'unfold':!fold}">
    <!-- 显示富文本 -->
    <div
      ref="htmlContent"
      class="dcss h-auto"
      :class="{'doc-hide':fold}"
      v-html="html"
    ></div>
    <div v-if="fold" class="footer lh22 mt16  layout-align-center layout-justify-center">
      <div class="mask"></div>
      <div class="footer-content mt2">
        <span class="hand color-59 font-size-14 lh22" @click="toggle">
          <span>{{ !fold ? $t('pc_flip_lbl_packup') : $t('biz_pc_expand_more') }}</span>
          <yxtf-svg
            :icon-class="!fold ? 'up' : 'down'"
            width="16px"
            height="16px"
            class="ml4 icon v-mid"
          />
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import FlipSvg from 'packages/_components/svg';
export default {
  comments: {
    FlipSvg
  },
  props: {
    html: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fold: false,
      myText: ''
    };
  },
  mounted() {
    // 是否显示底部操作按钮
    this.$nextTick(() => {
      if ((this.$refs['htmlContent'] && this.$refs['htmlContent'].clientHeight > 80) || this.isHasImg) {
        this.fold = true;
      }
    });
  },
  computed: {
    isHasImg() {
      // eslint-disable-next-line
      return /\<img/.test(this.html)
    }
  },
  watch: {
    html(val) {
      if (!val) return;
      const dom = document.createElement('div');
      dom.innerHTML = val;
      this.myText = dom.innerText;
    }
  },
  methods: {
    toggle() {
      this.fold = !this.fold;
    }
  }
};

</script>
<style scoped lang='scss'>
//
.desc-text-section {
  position: relative;
  margin-top: 20px;

  img {
    max-width: 100%;
  }

  .dcss.doc-hide {
    max-height: 80px;
    overflow: hidden;
  }

  .footer {
    margin-top: -56px;
    text-align: right;

    .mask {
      width: 100%;
      height: 56px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%);
      opacity: 0.8;
    }

    .footer-content {
      & > span:hover {
        color: var(--color-primary);
      }
    }
  }

  &.unfold {
    .dcss {
      height: auto;
    }

    .footer {
      margin-top: 0;

      .mask {
        display: none;
      }
    }
  }
}
</style>
