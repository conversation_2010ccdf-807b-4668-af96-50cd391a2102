<template>
  <div
    :class="[{'p24 yxtulcdsdk-o2o-offline-task':!['yxt-ulcd-sdk-examing', 'yxt-ulcd-sdk-course-page','yxt-ulcd-sdk-practicing','yxt-ulcd-sdk-o2o-offline-task'].includes(targetTask.name)},{'h100p': !!targetTask.name, 'max-width-100': fullScreenStatue }]"
    class="yxtulcdsdk-ulcdsdk minh100"
  >
    <components
      :is="targetTask.name"
      v-if="targetTask.name && targetTask.type !== 3"
      :id="targetTask.params.kngId"
      :ref="targetTask.name"
      :params="targetTask.params"
      :target="targetTask.params"
      :task-id="targetTask.id"
      :project-id="bizId"
      :inner="true"
      :is-course="targetTask.type===9"
      :radius="false"
      :auto-play="true"
      @updateProgress="updateProgress"
      @fullScreen="handleFullScreen"
    />
    <yxt-ulcd-sdk-surveying
      v-else-if="targetTask.type === 3"
      :params="targetTask.params"
      :full-screen="false"
      class="bg-white"
      :scroll-inner="false"
      :radius="false"
      @fullScreen="param => $emit('fullScreen',param)"
      @updateProgress="surveyUpdateProgress"
    />
    <div v-else-if="!notExist">
      <div class="offline-task-head pt32 pb32 border-radius4 bg-white">
        <div class="font-size-24 lh28 font-weight-500 color-26 ph24">{{ detail.title }}</div>
        <div
          v-if="detail.desc"
          class="ph24 mt24"
        >
          <div class="font-size-18 lh26 mb8 font-weight-500">{{ $t('pc_o2o_lbl_offlinedesc') }}</div>
          <!-- 面授说明 -->
          <desc-text :html="detail.desc" />
        </div>
        <!-- 授课讲师 -->
        <TeacherList
          ref=""
          :flip-id="flipId"
          :has-permission="hasPermission"
          class="mt24"
        />
      </div>
      <div class="border-radius4 mt24 bg-white p24">
        <div class="font-size-18 lh26 flex align-center font-weight-500">{{ $t('pc_flip_lbl_facesession') }} <span class="ml14 font-size-14 lh22 color-lightgrey font-weight-400">{{ $t('pc_ulcdsdk_tip_interactive') }}</span></div>
        <SessionsList
          :list="sessionsList"
          :project-id="bizId"
          :app-code="params.appCode"
          :enable-publish-operation="enablePublishOperation"
          @toTask="handleToTask"
        />
      </div>
      <div class="mt24 flex">
        <div class="flex-1 min-width-0">
          <div class="border-radius4 bg-white pb24">
            <!-- 课前预习 没有课前预习不显示出来 -->
            <PreviewList
              :flip-id="flipId"
              :project-id="bizId"
              :has-permission="hasPermission"
              :track-id="params.trackId"
              :enable-publish-operation="enablePublishOperation"
              @toTask="handleToTask"
            />
            <!-- 面授成绩 -->
            <div class="mt8 ph24 bg-white radius4">
              <label class="d-block lh24 font-size-18 font-weight-500">{{ $t('pc_flip_lbl_face_score') }}</label>
              <div
                class="flip-stu-score mt16 ph16 pv13 font-size-14 color-8c radius4 bg-fa flex align-items-center"
                :class="scoreCls.cls"
              >
                <div
                  v-if="scoreCls.passed >= 0"
                  class="color-26 font-size-16 lh22"
                >{{ detail.totalScore }} </div>
                <div
                  v-if="scoreCls.passed >= 0"
                  class="color-26 font-size-12 lh20"
                >{{ $t('pc_flip_lbl_point') }}</div>
                <div class="ml2 lh22">{{ scoreCls.lbl }}</div>
              </div>
              <!-- 面授评价 -->
              <template v-if="detail.targetId">
                <label class="d-block mt32 lh24 font-size-18 font-weight-500">{{ $t('pc_flip_lbl_face_eval') }}</label>
                <div class="pv16 pl8 pr24 flex align-items-center offline-task-eval justify-between mt16">
                  <div class="color-8c font-size-12 lh20">
                    <div class="color-26 font-size-14 lh22 ellipsis min-width0">{{ `【${$t('pc_flip_type_questionnaire') }】${detail.targetName}` }}</div>
                    <div
                      v-if="detail.stuStartTime"
                      class="mt4 ml8"
                    >{{ `${formatTime(detail.stuStartTime)} ~ ${formatTime(detail.stuEndTime)}` }}</div>
                    <div class="mt4 ml8">{{ $t(hasEvaled ? 'pc_ulcdsdk_lbl_completestatus' : evalStateLbl[evalState]) }}</div>
                  </div>
                  <ButtonWidthTooltip
                    v-if="enablePublishOperation"
                    :loading="loadingUrl"
                    class="ml24 flex-shrink-0"
                    :plain="hasEvaled"
                    :type="hasEvaled ? undefined : 'primary'"
                    :disabled="!hasEvaled && (evalState === 0 || evalState === 2)"
                    :content="$t(evalStateLbl[evalState])"
                    @click="toEvaluate"
                  >
                    {{ $t(`pc_flip_lbl${hasEvaled ? '_view' : ''}_eval`) }}
                  </ButtonWidthTooltip>
                </div>
              </template>
            </div>
          </div>
          <!-- 线下活动精彩瞬间 -->
          <div class="mt24 border-radius4 bg-white p24">
            <div class="font-size-18 lh26 mb8 flex align-center font-weight-500">{{ $t('pc_ulcdsdk_lbl_moments' /*线下活动精彩瞬间*/) }}</div>
            <!-- 线下活动精彩瞬间 -->
            <MomentList
              v-if="momentList.length"
              :list="momentList"
              :is-edit="false"
            />
            <yxtf-empty
              v-else
              size="small"
            />
          </div>
        </div>
        <div class="ml24 flex-shrink-0">
          <div
            v-if="groupMembers.length && hasGroup"
            class="mb24 bg-white border-radius4 pt24 width312"
          >
            <div
              class="ph24"
              :class="isOpenBoard ? 'pb16' : 'pb24'"
            >
              <!-- 团队成员 -->
              <div class="font-size-18 lh26 mb16 flex align-center justify-between font-weight-500">{{ $t('pc_o2o_lbl_belong_group' /*所属小组*/) }}
                <yxt-button
                  v-if="groupMembers.length > 5"
                  type="text"
                  @click="showAllGroupMembers = true"
                >
                  {{ $t('pc_ulcdsdk_lbl_all_members' /*全部成员*/) }}
                </yxt-button>
              </div>
              <group-members
                :group-members="groupMembers"
                :group-name="groupName"
              />
            </div>
            <div
              v-if="isOpenBoard"
              class="yxtulcd-offline-group__buttom"
            >
              <yxt-button @click="openGroupBoard">
                {{ $t('pc_ulcdsdk_groupBoard'/** 小组白板 */) }}
              </yxt-button>
              <yxt-button @click="viewCommentary">
                {{ $t('pc_ulcdsdk_viewCommentary'/** 查看点评 */) }}
              </yxt-button>
            </div>
          </div>
          <!-- 附件 -->
          <AttachList
            :flip-id="flipId"
            :list="attachList"
            class="width312"
          />
        </div>
      </div>
    </div>
    <yxtf-empty
      v-else
      :empty-text="errorTip"
      style=" position: absolute; top: 0; right: 0; bottom: 0; left: 0; margin: auto;"
    />
    <yxt-dialog
      :title="$t('pc_o2o_lbl_groupdetail')"
      :visible.sync="showAllGroupMembers"
      width="480px"
      custom-class="yxtulcdsdk-ulcdsdk"
      append-to-body
    >
      <group-list
        :flip-detail-id="flipId"
        :group-id="groupId"
        :group-name="groupName"
        class="mt-4"
        show-all
      />
    </yxt-dialog>
    <!-- 小组白板 -->
    <group-board
      :id="boardId"
      ref="board"
      :group-id="groupId"
      :group-name="groupName"
    />
    <!-- 查看点评 -->
    <commentary
      :visible.sync="commentaryVisible"
      :group-name="groupName"
      :commentary="commentary"
    />
  </div>
</template>

<script>
import { checkImgExists } from 'packages/o2o-homework-task/src/utils';
import SessionsList from '../components/sessions-list/index.vue';
import TeacherList from '../components/teacherList';
import AttachList from '../components/attach-list/index.vue';
import PreviewList from '../components/preview-list/index.vue';
import DescText from '../components/desc-text/index.vue';
import { getFlipDetail, checkMemberAuth, postUserInfo, getFlipGroupMemberListFixLimit, getFlipGroupBase, checkBoardPermission, getBoardInfo } from '../service';
import { getAttachIconType, getTaskType } from 'packages/_utils/core/utils';
import groupMembers from '../components/group/groupMembers.vue';
import groupList from '../components/group/groupList.vue';
import MomentList from '../../o2o-homework-task/src/components/attachment-list.vue';
import dayjs from 'dayjs';
import ButtonWidthTooltip from 'packages/_components/buttonWithTooltip';
import GroupBoard from '../components/group/board-drawer';
import Commentary from '../components/group/commentary';
import qs from 'qs';

export default {
  name: 'YxtUlcdSdkO2oOfflineTask',
  components: {
    ButtonWidthTooltip,
    groupMembers,
    groupList,
    SessionsList,
    TeacherList,
    AttachList,
    PreviewList,
    DescText,
    MomentList,
    GroupBoard,
    Commentary
  },
  props: {
    source: String, // flipstu 面授详情独立页面
    params: {
      type: Object,
      default: () => ({
        tid: '',
        targetId: '',
        trackId: '',
        appCode: ''
      })
    },
    projectId: String
  },
  data() {
    return {
      targetTask: {
        params: {}
      },
      hasGroup: false,
      errorTip: '',
      notExist: false,
      momentList: [],
      groupMembers: [],
      showAllGroupMembers: false,
      groupName: '',
      loading: false,
      loadingUrl: false,
      evalState: '', // 评价状态
      evalStateLbl: [
        'pc_flip_lbl_not_involved', // 未开始
        '', // 进行中
        'pc_ulcdsdk_lbl_ended', // 已结束
        'pc_ulcdsdk_lbl_uncompleted' // 未完成
      ],
      detail: {
        title: '',
        desc: '',
        totalScore: null, // 是否显示面授成绩
        targetId: '', // 评价模板 id
        passed: null // 是否评分
      },
      fullScreenStatue: false, // 全屏
      hasEvaled: false, // 是否评价过
      attachList: [],
      sessionsList: [],
      expanded: false, // 简介是否展开
      hasPermission: false, // 是否有权限
      isVisitor: false, // 是否是游客
      isSubTask: 0, // 是否是多班次 0不是，1是
      isOpenBoard: false, // 小组是否开启白板
      boardId: '', // 白板id
      groupId: '', // 小组id
      commentaryVisible: false, // 点评组件显隐
      commentary: ''// 点评内容
    };
  },
  computed: {
    flipId() {
      return this.params.targetId;
    },
    bizId() {
      return this.params.bizId || this.projectId;
    },
    getTaskLabel() {
      return this.targetTask.label || getTaskType(this.targetTask.type).name;
    },
    hasSceneStarted() {
      return this.sessionsList.some(item => new Date(item.startTime) < new Date());
    },
    // 格式化分数
    scoreCls() {
      let passed = this.detail.passed;
      if (typeof passed !== 'number') return { lbl: this.$t('pc_ulcdsdk_waiting_for_evla'), cls: 'c-bf' };
      let data = [
        { passed: 3, lbl: this.$t('pc_flip_lbl_excellent'), cls: 'score-1' }, // 优秀
        { passed: 2, lbl: this.$t('pc_flip_lbl_good'), cls: 'score-2' }, // 良好
        { passed: 1, lbl: this.$t('pc_flip_lbl_qualified'), cls: 'score-3' }, // 合格
        { passed: 0, lbl: this.$t('pc_flip_lbl_unqualified'), cls: 'score-4' }, // 不合格
        { passed: -1, lbl: this.$t('pc_ulcdsdk_waiting_for_evla'), cls: 'c-bf' } // 待评分
      ];
      return data.find(item => passed === item.passed);
    },
    // 面授任务是否属于已删除、归档、结束，限制一些功能操作
    enablePublishOperation() {
      if (!this.detail) {
        return false;
      }
      if ([3, 4, 5, 25, 28].indexOf(this.detail.publishState) !== -1) {
        return false;
      }
      return true;
    }
  },
  mounted() {
    this.isVisitor = !!parseInt(localStorage.getItem('isVisitor'));
    this.checkMemberAuth().then(() => {
      this.getDetail();
      this.getMembers();
      this.getFlipGroupBase();
    }).catch(code => {
      this.notExist = true;
      this.errorTip = code === '0001' ? this.$t('pc_flip_tip_error_0001') : this.$t('pc_flip_lbl_noaccess');
    });
  },
  created() {
    this.$root.$on('GOBACK', this.goBack);
  },
  beforeDestroy() {
    this.setBreadcrumbUpdate(false);
    this.$root.$off('GOBACK', this.goBack);
  },
  methods: {
    goBack() {
      this.targetTask = {};
      this.setBreadcrumbUpdate(false);
    },
    setBreadcrumbUpdate(v) {
      let breadcrumb = {
        show: v,
        list: []
      };
      if (v) {
        breadcrumb.list.push({ name: this.$t('pc_ulcdsdk_lbl_offline_task'), eventName: 'GOBACK' }, { name: this.getTaskLabel });
      } else {
        breadcrumb.list.push({ name: this.$t('pc_ulcdsdk_lbl_offline_task') });
      }
      this.$root.$emit('SETBREADCRUMBUPDATE', breadcrumb);
    },
    handleFullScreen(open) {
      this.$emit('fullScreen', open);
      this.fullScreenStatue = open;
    },
    formatTime(val) {
      return dayjs(val).format('YYYY-MM-DD HH:mm');
    },
    updateProgress(param) {
      if (this.targetTask.type === 0) { // 签到之后回调进度
        this.$emit('updateProgress', param);
      }
    },
    // 获取小组学员列表
    async getMembers() {
      const { groupId, groupName, hasGroup } = await postUserInfo({ flipDetailId: this.flipId });
      this.hasGroup = hasGroup;
      this.groupId = groupId;
      // 学员所属小组
      this.groupName = groupName;
      getFlipGroupMemberListFixLimit({
        fixLimit: 6,
        flipDetailId: this.flipId,
        groupId: groupId
      }).then(res => {
        this.groupMembers = res || [];
      });
    },
    // 获取小组分组基本信息
    getFlipGroupBase() {
      getFlipGroupBase({ flipId: this.flipId }).then((res) => {
        this.isOpenBoard = res.boardOpen;
      });
    },
    // 打开小组白板
    openGroupBoard() {
      if (!this.boardId) {
        getBoardInfo(this.groupId).then((res) => {
          this.boardId = res.boardId;
          this.checkBoardPermission();
        }).catch((err) => {
          this.$message.error(err.message);
        });
      } else {
        this.checkBoardPermission();
      }
    },
    // 校验小组权限
    checkBoardPermission() {
      const userId = window.localStorage.userId;
      checkBoardPermission(this.boardId, userId).then((res) => {
        if (res === true) {
          this.$refs.board.openBoard();
        }
      }).catch((err) => {
        this.$message.error(err.message);
      });
    },
    // 查看点评
    viewCommentary() {
      getBoardInfo(this.groupId).then((res) => {
        this.commentary = res.tips;
        if (!this.commentary) {
          this.$message.warning(this.$t('pc_ulcdsdk_no_commentary'/** 暂无点评 */));
        } else {
          this.commentaryVisible = true;
        }
      }).catch((err) => {
        this.$message.error(err.message);
      });
    },
    // 获取课件列表
    checkMemberAuth() {
      return new Promise((resolve, reject) => {
        checkMemberAuth({ flipDetailId: this.flipId }).then(res => {
          if (res && res.code === '0000') {
            resolve();
          } else {
            reject(res.code);
          }
        }).catch(e => {

        });
      });
    },
    getDetail() {
      this.loading = true;
      getFlipDetail(this.flipId, { bizId: this.bizId }).then(res => {
        this.hasPermission = true;
        let data = res;
        this.isSubTask = data.isSubTask;
        this.detail = {
          title: data.flipName,
          desc: data.description,
          textDesc: data.textDesc,
          totalScore: data.totalScore, // 面授成绩
          targetId: data.targetId, // 评价 id
          targetName: data.targetName, // 评价名
          stuStartTime: data.stuStartTime, // 开始时间
          stuEndTime: data.stuEndTime, // 结束时间
          passed: data.passed, // 评分
          publishState: data.publishState // 面授任务发布状态（:0未发布；1已发布，2撤回 3删除 4结束 5归档）
        };
        const { stuStartTime, stuEndTime } = data;
        if (stuStartTime) {
          if (dayjs(stuStartTime).isAfter(dayjs())) {
            this.evalState = 0; // 未开始
          } else if (dayjs(stuEndTime).isAfter(dayjs())) {
            this.evalState = 1; // 进行中
          } else {
            this.evalState = 2; // 已结束
          }
        } else {
          this.evalState = 1; // 进行中
        }
        // 是否评价过, canAppraise 1已评价 2未评价 默认0
        this.hasEvaled = data.canAppraise === 1;
        if (this.evalState === 1 && !this.hasEvaled) {
          this.evalState = 3; // 未完成
        }

        // 场次
        this.sessionsList = data.flipSceneDtos;
        // 附件
        this.attachList = (data.attachments || []).map(item => ({
          iconType: getAttachIconType(item.name),
          ...item
        }));
        this.momentList = (data.instructorAttachment || []).map(item => {
          return { ...item, viewUrl: item.fileType === 'image' ? (item.viewUrl || item.url) : '' };
        });
      }).catch(e => {
        this.$message.error(e);
      }).then(() => {
        this.loading = false;
      });
    },
    setErrorImgUrl(file) {
      checkImgExists(file.viewUrl).then(() => { }).catch(() => {
        checkImgExists(file.url).then(() => {
          this.$set(file, 'viewUrl', file.url);
        }).catch(() => {

        }).finally(() => {
          return file;
        });
      });
    },
    openExamPage(params) {
      window.open(`/ote/#/exampreview?${qs.stringify(params)}`);
    },
    openPracticePage(params) {
      window.open(`/ote/#/practicepreview?${qs.stringify(params)}`);
    },
    openPage(type, params) {
      switch (type) {
        case 2:
          this.openExamPage(params);
          break;
        case 19:
          this.openPracticePage(params);
          break;
      }
    },
    // 跳转任务
    handleToTask(type, name, params, label) {
      if (this.source === 'flipstu' && [2, 19].includes(type)) {
        // 直接跳转考试、练习页面
        this.openPage(type, params);
        return;
      }
      this.targetTask = {
        name,
        label,
        type,
        params
      };
      this.setBreadcrumbUpdate(true);
    },
    surveyUpdateProgress(param) {
      // 学员评价
      if (!this.targetTask.tid) {
        this.getDetail();
        this.$emit('updateProgress', param);
      }
    },
    // 跳转到调查问卷
    toEvaluate() {
      if (!this.hasSceneStarted && !this.detail.stuStartTime) {
        this.$message.warning(this.$t('pc_flip_lbl_face_evaluateTip'));
        return;
      }
      const supportMerge = this.params.appCode === 'o2o' && this.isSubTask !== 1;
      this.targetTask = {
        name: 'yxt-ulcd-sdk-surveying',
        type: 3,
        label: this.$t('pc_flip_type_questionnaire'),
        params: {
          type: 2,
          allowResult: 1,
          id: this.flipId,
          tid: '',
          mergeEvalProjectId: supportMerge ? this.bizId : '',
          mergeEvalTaskId: this.params.tid,
          showResult: 1,
          tname: '',
          success: '',
          hideBack: true
        }
      };
      this.setBreadcrumbUpdate(true);
    }
  }
};
</script>
<style lang="scss">
.yxtulcd-offline-group__buttom {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 72px;
  border-top: 1px solid #f5f5f5;
}
</style>
