
import { commonUtil } from 'yxt-biz-pc';

export const i18n = commonUtil.i18n;
// 课件类型
export const getCourse = function(type) {
  switch (type) {
    case 4: // 练习
      return { name: '', typeName: i18n.tc('pc_o2o_lbl_practice') };
    case 6: // 考试
      return { name: '', typeName: i18n.tc('pc_o2o_lbl_exam') };
    case 8: // 问卷
      return { name: '', typeName: i18n.tc('pc_flip_type_questionnaire') };
    case 101: // 文档
      return { name: 'doc', typeName: i18n.tc('pc_o2o_lbl_doc') };
    case 104: // 微课
      return { name: 'weike', typeName: i18n.tc('pc_kng_common_lbl_weike') };
    case 102: // 视频
      return { name: 'video', typeName: i18n.tc('pc_kng_common_lbl_video') };
    case 103: // 音频
      return { name: 'audio', typeName: i18n.tc('pc_kng_common_lbl_audio') };
    case 105: // Scorm
      return { name: 'scorm', typeName: 'Scorm' };
    case 106: // html
      return { name: 'html', typeName: 'html' };
    case 108: // html
      return { name: 'newlink', typeName: i18n.tc('pc_o2o_lbl_kngouterlink') };
    default:
      return { name: '', typeName: '-' };
  }
};

// 获取类型名称
export const getTypeName = function(typeName, type) {
  if (typeof typeName === 'object') {
    type = typeName.type;
    typeName = typeName.typeName;
  }
  return typeName || getCourse(type).typeName;
};
export const prefix0 = value => {
  return value >= 10 ? value : (`0${value}`);
};
export const formatFriendDate = (date, format = 'HH:mm:ss') => {
  if (!date) return '';
  date = getDate(date);
  const now = new Date(); // 当前时间对象
  const [nowYear, nowMonth, nowDay] = [now.getFullYear(), now.getMonth() + 1, now.getDate()];
  const [year, month, day] = [date.getFullYear(), date.getMonth() + 1, date.getDate()];
  // 跨年
  if (nowYear !== year) return dateFormat(date, `yyyy-MM-dd ${format}`);
  // 当天
  if (nowMonth === month && nowDay === day) return dateFormat(date, format);
  // 昨天
  if (dateFormat(date, 'yyyy-MM-dd') === dateFormat(now.setDate(nowDay - 1), 'yyyy-MM-dd')) return `${i18n.t('pc_flip_lbl_yesterday')} ${dateFormat(date, format)}`;
  return dateFormat(date, `MM-dd ${format}`);
};
  // 获取日期对象
export function getDate(arg, tag = '') {
  if (!arg) return tag;
  if (typeof arg === 'string') arg = arg.replace(/-/g, '/');
  return new Date(arg);
}

// 对Date的扩展，将 Date 转化为指定格式的String
// 月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
// 年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
// 例子：
// (new Date()).Format("yyyy-MM-dd HH:mm:ss.S") ==> 2006-07-02 08:09:04.423
// (new Date()).Format("yyyy-M-d H:m:s.S")      ==> 2006-7-2 8:9:4.18
// eslint-disable-next-line no-extend-native
export function dateFormat(date, fmt = 'yyyy-MM-dd HH:mm') {
  if (!date) return '';
  date = getDate(date);
  const obj = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substring(4 - RegExp.$1.length));
  }
  for (const key in obj) {
    if (new RegExp('(' + key + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? obj[key] : ('00' + obj[key]).substring(('' + obj[key]).length));
    }
  }
  return fmt;
}

/**
   * 格式化 文件尺寸
   * 用于上传文件 size Bytes 转换成 ' Bytes', ' KB', ' MB', ' GB', ' TB' 单位
   * @param {Number} value  单位：Byte 字节
   * @returns
   */
export const formatFileSize = value => commonUtil.fileUtils.getFileSize(value);

/**
   * 获取文件名 和 扩展名
   * @param {String} filename
   * @returns
   */
export const getFilenameAndExtname = filename => {
  if (typeof filename !== 'string') throw new Error('filename must be a string!');
  const lastDotIndex = filename.lastIndexOf('.');
  let prefix = '';
  let suffix = '';
  if (lastDotIndex > -1) {
    prefix = filename.substring(0, lastDotIndex);
    suffix = filename.substring(lastDotIndex + 1).toLowerCase();
  } else {
    prefix = filename;
  }

  return {
    prefix,
    suffix
  };
};
