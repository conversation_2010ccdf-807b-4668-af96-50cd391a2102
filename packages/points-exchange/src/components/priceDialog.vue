<template>
  <div class="">
    <yxt-dialog
      :title="$t('pc_kng_detail_lbl_exchange_dialog_title')"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="z-999999"
      append-to-body
      destroy-on-close
      width="520px"
      @close="close"
    >
      <!-- 兑换成功 -->
      <div v-if="status === 'success'" class="text-center">
        <yxt-svg
          width="73px"
          height="73px"
          icon-class="biz/result-success-icon"
        />
        <h2 class="font-size-20 font-bolder text-26 mt25">{{ $t('pc_kng_detail_tit_exchange_success') }}</h2>
        <p class="mt10 mb20">{{ $t('pc_kng_detail_msg_exchange_success') }}：{{ orderCode }}</p>
        <yxtf-button size="other" type="primary" @click="closeDialog">{{ $t('pc_kng_detail_btn_exchange_success') }}</yxtf-button>
      </div>

      <!-- 兑换失败 -->
      <div v-else-if="status === 'fail'" class="text-center">
        <yxt-svg
          width="73px"
          height="73px"
          icon-class="biz/mobile-result-fail"
        />
        <h2 class="font-size-20 font-bolder text-26 mt25">{{ $t('pc_kng_detail_tit_exchange_fail') }}</h2>
        <p class="mt10 mb20">{{ $t('pc_kng_detail_msg_exchange_fail') }}</p>
        <yxtf-button size="other" type="primary" @click="closeDialog">{{ $t('pc_kng_detail_btn_exchange_fail') }}</yxtf-button>
      </div>
      <template v-else>
        <div class="exchange-credit__center">
          <img :src="courseInfo.coverUrl" alt="" class="exchange-credit__center--left mr10">
          <div class="exchange-credit__center--right">
            <h2 class="font-size-16 text-26 yxtulcdsdkmp0 ellipsis-2">{{ courseInfo.name }}</h2>
            <p class="text-75 yxtulcdsdkmp0">{{ $t('pc_kng_detail_lbl_exchange_dialog_txt') }}</p>
          </div>
        </div>
        <div class="exchange-credit__footer">
          <span class="text-26 font-size-14">{{ $t('pc_kng_detail_lbl_exchange_dialog_payable') }}：</span>
          <span class="font-size-32 yxt-color-warning fw-600 mr5">{{ courseInfo.payablePoint }}</span>
          <span class="font-size-16 font-weight500 yxt-color-warning mr30">{{ $t('pc_kng_detail_lbl_credit') }}</span>
          <span class="text-75">（{{ $t('pc_kng_detail_lbl_exchange_dialog_user') }}{{ courseInfo.userPoint }}）</span>
          <yxt-button
            v-if="!showIsAblePay && showIsOpenPay && deleteGoPay"
            type="text"
            class="ml10"
            @click="onJumpPay"
          >
            {{ $t('pc_kng_detail_lbl_exchange_dialog_topay') }}
          </yxt-button>
        </div>
        <span slot="footer" class="dialog-footer">
          <yxtf-button
            type="primary"
            :disabled="!showIsAblePay"
            :loading="loading"
            @click="onExchangeCredit"
          >{{ $t('pc_kng_detail_btn_exchange_confirm') }}</yxtf-button>
        </span>
      </template>

    </yxt-dialog>
  </div>
</template>

<script>
import { getExchangeInfo, getPayAuth, postExchangePay} from '../../service';
import _gte from 'lodash/gte';

export default {
  name: 'ExchangeCredit',
  props: {
    price: {
      type: Number
    },
    visible: {
      type: Boolean,
      default: false
    },
    kngId: { // 单个课件的ID
      type: String
    }
  },
  data() {
    return {
      courseId: this.kngId || this.$route.query.courseId || this.$route.query.id,
      courseInfo: {},
      showIsOpenPay: false, // 机构是否开通了积分充值
      showIsAblePay: false, // 是否积分能够支付
      orderCode: '',
      status: '', // 支付结果

      deleteGoPay: false, // 默认取消去充值
      loading: false
    };
  },
  async mounted() {
    this.courseId = this.kngId || (this.$route.query.courseId || this.$route.query.id);
    await this.getExchangeInfo();
    await this.showPayHandler();
  },
  watch: {
    visible: {
      handler: function(val) {
        this.$emit('update:visible', val);
      },
      immediate: true
    }
  },
  methods: {
    async getExchangeInfo() {
      const res = await getExchangeInfo(this.courseId);
      this.courseInfo = res;
      this.showIsAblePay = _gte(res.userPoint, res.payablePoint);
    },

    // 用于判断机构是否开通了积分充值，1-开通，0-未开通",
    async showPayHandler() {
      const id = window.getLocalStorage('orgId');
      const { value } = await getPayAuth(id);
      this.showIsOpenPay = value === '1';
    },
    onExchangeCredit() {
      this.loading = true;
      postExchangePay(this.courseId).then(({ orderCode }) => {
        this.orderCode = orderCode;
        this.status = 'success';
      }).catch(() => {
        this.status = 'fail';
      }).finally(() => {
        this.loading = false;
      });
    },
    closeDialog() {
      this.$emit('update:visible', false);
    },
    close() {
      if (this.status === 'success') {
        this.$emit('success');
      }
    },
    // 去充值
    onJumpPay() {
      window.open(window.location.origin + '#/my/point', '_blank');
    }
  }
};
</script>

