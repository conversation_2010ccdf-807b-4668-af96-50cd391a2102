
<template>
  <div class="yxtulcdsdk-points-exchange yxtulcdsdk-ulcdsdk">
    <div class="yxtulcdsdk-points-exchange__inner yxtulcdsdk-flex">
      <div class="flex-1 pr yxtulcdsdk-points-exchange__innerback">
        <img :src="coverUrl" class="yxtulcdsdk-points-exchange__cover">
        <div class="yxtulcdsdk-flex-center-center yxtulcdsdk-points-exchange__back">
          <div class="yxtulcdsdk-flex-center yxtulcdsdk-flex-vertical">
            <div class="standard-size-20 color-white">{{ $t('pc_ulcdsdk_lbl_toberedeemedbeforelearning', [price]) }}</div>
            <div class="mt16 yxtulcdsdk-flex-center"><yxtf-button size="larger" type="primary" @click="dialogPaddingDefault = true">{{ $t('pc_ulcdsdk_lbl_redeemnow') }}</yxtf-button></div>
          </div>
        </div>
      </div>
    </div>
    <!-- 课件课程兑换弹框 -->
    <price-dialog
      :key="kngId"
      :visible.sync="dialogPaddingDefault"
      :kng-id="kngId"
      @success="$emit('pointExchangeCompleted')"
    />
  </div>
</template>

<script>
import priceDialog from './components/priceDialog';

export default {
  name: 'YxtUlcdSdkPointsExchange',
  components: {
    priceDialog
  },
  props: {
    kngId: {
      type: String,
      default: ''
    },
    coverUrl: {
      type: String,
      default: ''
    },
    price: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      dialogPaddingDefault: false
    };
  },
  async mounted() {
  }
};
</script>

