import {kngApi, coreApi} from 'packages/api';

// 积分兑换商品信息
export const getExchangeInfo = (courseId) => {
  return kngApi.get(`/exchange/info/${courseId}`);
};

// 是否展示去充值
export const getPayAuth = (orgId) => {
  return coreApi.get(`/orgparsetting/code?code=OpenPointRecharge&orgId=${orgId}`);
};

// 积分兑换商品信息
export const postExchangePay = (courseId) => {
  return kngApi.post(`/exchange/pay/${courseId}`);
};
