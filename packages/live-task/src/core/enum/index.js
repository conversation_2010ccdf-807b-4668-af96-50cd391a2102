/*
 * @FileDescription:
 * @Author: 曹韧
 * @Date: 2021-02-26 17:32:24
 * @LastEditors: yuyangw
 * @LastEditTime: 2022-06-06 14:27:03
 */
// 学员类型 人员属性， userType=3则为外部人员
export const USERTYPE = {
  EXTERNAL: 3,
  INSIDER: 1
};

export const SIGN_ENROLL_TYPE = {
  ALL: '',
  AIM: 0
};

export const ROOM_STATUS = {
  NOSTART: 0,
  LIVING: 1,
  ENDED: 2
};

export const USER_ROLE = {
  MAIN: 0,
  STU: 1,
  GUEST: 2
};

export const ENROLL_TYPE = {
  ALL: 0,
  AIM: 1,
  NOTAIM: 2
};

// 显示LOGO
export const LOGO_STATE = {
  OFF: 0,
  ON: 1
};

// 分享类型
export const SHARE_TYPE = {
  NEITHER: '00',
  INSIDE: '10',
  OUTSIDE: '01',
  ALL: '11'
};

export const TASK_STATUS = {
  // 未开始
  NOSTART: -1,
  // 排队中
  QUENEING: 0,
  // 录制中
  RECORDING: 1,
  // 录制完成
  RECORDED: 2,
  // 录制失败
  RECORDFAILD: 3,
  // 正在转码
  TRANSCODING: 4,
  // 转码失败
  TRANSCODEFAILD: 5,
  // 转码完成
  TRANSCODED: 6,
  // 失败
  FAILD: 7
};

/**/
export const THIRD_PROJECT_ID = {
  SELFTLIVE: null,
  TRAINCENTER: 2,
  TALENTDEVELOP: 3
};
// 当前回放 0 是 1 不是
export const RECORD_TYPE = {
  ON: 0,
  OFF: 1
};

// 录制未开始 / 正在录制中 / 正在转码中 / 录制失败 / 录制成功 / 正在转码中（私有化）
export const RECORD_STATUS = {
  NOTSTART: 0,
  RECORDING: 1,
  TRANSCODING: 2,
  RECORDFAILED: 3,
  RECORDSUCCESS: 4,
  PRIVATE_TRANSCODING: 5
};

export const TAB_NAME = {
  ANALYSE: '0',
  MEMBER: '1',
  CHAT: '3',
  SIGN: '4'
};
// 直播类型
export const LIVE_TYPE = {
  MOBIE: 1,
  PC: 0,
  ALL: ''
};

export const TEACHER_TYPE = {
  ISTEACHER: 1
};

// 当前机构是否开启集团化
export const ENABLE_GROUP_CORP = {
  SUPPORT: 'true',
  UNSUPPORTED: 'false'
};

// 弱管理 / 强管理
export const MANAGE_MODE = {
  WEAK: '1',
  STRONG: '2'
};

// 非集团版 / 集团版
export const ORG_TYPE = {
  NORORG: 0,
  ISORG: 1
};

// 开播方式：支持客户端/仅web开播
export const BEGIN_TYPE = {
  NORMAL: 1,
  ONLY_WEB: 2
};

// 用户性别
export const SEX_TYPE = {
  UNCERTAIN: 0,
  MALE: 1,
  FEMALE: 2
};

// 消息模板类型
export const MESSAGE_TYPE = {
  ALL: 0,
  START_NOTICE: 1,
  ADD_STUDENT_NOTICE: 2,
  CHANGE_TEACHER_NOTICE: 3,
  CHANGE_TIME_NOTICE: 4,
  DELETE_LIVE_NOTICE: 5
};
