/*
 * @Description: Description
 * @Author: yhwang
 * @Date: 2022-04-14 16:21:58
 * @LastEditors: yuyangw
 * @LastEditTime: 2022-06-06 14:28:19
 */
export const isDd = () => {
  // 判断是否是钉钉
  const sourceCode = localStorage.getItem('sourceCode');
  return sourceCode === '104';
};

export const isWx = () => {
  const sourceCode = localStorage.getItem('sourceCode');
  // 100 是企业微信，104 是钉钉
  return sourceCode === '100';
};

// 判断是否在pc端微信浏览器中（不包含企业微信）
export const isBrowserInWeixin = () => {
  const ua = navigator.userAgent.toLowerCase();

  return (
    (ua.indexOf('macwechat') > -1 || ua.indexOf('windowswechat') > -1) &&
    ua.indexOf('wxwork') === -1
  );
};

export const isIEBrowser = () =>
  !!window.ActiveXObject || 'ActiveXObject' in window;
