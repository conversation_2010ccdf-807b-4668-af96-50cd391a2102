/**
 * 过滤xss攻击代码
 * @param {String} text 文本
 */
export const filterXss = (text) => {
  if (!text) {
    return '';
  }

  // 去掉script标签
  text = text.replace(/<script[\s\S]*?(<\/script>|\/>)/ig, '');
  // 去除on事件
  const onTagReg = /<[^>]*?( |\/)(on[\W\w]*?=([""'])?([^'""]+)([""'])?)[\S\s]*?>/ig;
  const onReg = /( |\/)on[\W\w]*?=([""'])?([^'""]+)([""'])?/ig;
  let onMatches = onTagReg.exec(text);
  while (onMatches) {
    text = text.replace(onMatches[0], onMatches[0].replace(onReg, ''));
    onMatches = onTagReg.exec(text);
  }
  // 去除非链接href
  const hrefReg = /<a[^>]*(href=([""']?([^'""]+)([""'])?))[\S\s]*?>/ig;
  let hrefMatches = hrefReg.exec(text);
  while (hrefMatches) {
    hrefMatches[3].indexOf('http') !== 0 && (text = text.replace(hrefMatches[0], hrefMatches[0].replace(hrefMatches[1], '')));
    hrefMatches = hrefReg.exec(text);
  }
  // 去除eval(...)和expression(...)
  text = text.replace(/(eval|expression)\(.*?\)/ig, '');

  return text;
};

