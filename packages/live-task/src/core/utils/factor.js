import { commonUtil } from 'yxt-biz-pc';

export async function checkFactor(factor) {
  await commonUtil.preCheckFunctions([factor]);
  return commonUtil.checkTimeOutFnc(factor);
}

export const FUNC_CODE_STATE = {
  DEFAULT: -1, // 未知。异常数据
  OFF_SHELF: 0, // 要素下架中-直接隐藏
  NOT_PURCHASED: 1, // 要素上架中-机构未购买-直接隐藏
  PURCHASED_SHOW: 2, // 要素上架中-机构已购买未过期（走启用控制逻辑）-显示
  PURCHASED_HIDE: 3, // 要素上架中-机构已购买未过期（走启用控制逻辑）-隐藏"
  PURCHASED_CUSTOM: 4, // 要素上架中-机构已购买未过期（走启用控制逻辑）-自定义
  EXPIRED_HIDE: 5, // 要素上架中-机构已购买已过期（走禁用控制逻辑）-隐藏
  EXPIRED_DISABLED: 6, // 要素上架中-机构已购买已过期（走禁用控制逻辑）-置灰
  EXPIRED_CUSTOM: 7 // 要素上架中-机构已购买已过期（走禁用控制逻辑）-自定义
};
