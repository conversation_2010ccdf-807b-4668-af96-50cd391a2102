/*
 * @FileDescription:
 * @Author: 曹韧
 * @Date: 2021-02-26 17:32:24
 * @LastEditors: yuyangw
 * @LastEditTime: 2022-09-14 14:47:16
 */
import { Message } from 'yxt-pc';
import { tliveApi } from '../../../../api';

tliveApi.interceptors.response.use(
  undefined,
  error => {
    if (error && error.message) {
      Message.error(error.message);
    }

    return Promise.reject(error);
  }
);

export function getLiveDetail(roomId, params) {
  return tliveApi.get(`liveroom/pc/${roomId}`, { params });
}

// 验证直播间权限
export function getPermissions(roomId, params) {
  return tliveApi.get(`liveroom/common/${roomId}/livePermissions`, { params });
}

// 学员观看回放的权限
export function getRecordedPermissions(roomId, params) {
  return tliveApi.get(`liveroom/pc/${roomId}/recordPermissions`, { params });
}

// 学员端获取直播间分享地址
export function getLiveroomWatchUrl(roomId) {
  return tliveApi.get(`liveroom/common/${roomId}/shareUrl`);
}

// 获取讲师评价链接
export function getEvalutionLink(roomId) {
  return tliveApi.get(`liveroom/common/${roomId}/answerUrl`);
}
