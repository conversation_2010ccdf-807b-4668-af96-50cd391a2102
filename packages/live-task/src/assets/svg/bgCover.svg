<?xml version="1.0" encoding="UTF-8"?>
<svg width="452px" height="255px" viewBox="0 0 452 255" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 62 (91390) - https://sketch.com -->
    <title>编组 11备份</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="0%" y1="33.9080991%" x2="100%" y2="66.0919009%" id="linearGradient-1">
            <stop stop-color="#7388FE" offset="0%"></stop>
            <stop stop-color="#9F96F2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="52.6143221%" y1="50%" x2="100%" y2="66.0919009%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="linearGradient-3">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#999AF6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="直播详情未开始-452" transform="translate(-331.000000, -174.000000)">
            <g id="1备份-3" transform="translate(331.000000, 174.000000)">
                <g id="位图">
                    <g id="编组-11备份">
                        <g>
                            <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="452" height="256.423077"></rect>
                            <path d="M452,0 C434.617573,42.492967 404.159448,77.415348 360.625625,104.767143 C314.283902,133.883108 255.545293,129.52977 202.848676,128.211538 C111.538472,125.927369 43.922246,168.664548 0,256.423077 L452,256.423077 L452,0 Z" id="路径-47备份" fill="url(#linearGradient-2)" transform="translate(226.000000, 128.211538) scale(-1, -1) translate(-226.000000, -128.211538) "></path>
                            <g id="编组-7" transform="translate(170.948718, 75.333333)">
                                <rect id="矩形" fill="url(#linearGradient-3)" x="19.2220138" y="86.5850851" width="76.888055" height="19.1161876" rx="3.4"></rect>
                                <path d="M108.532083,0.562240812 C112.287619,0.562240812 115.332083,3.60670451 115.332083,7.36224081 L115.332083,89.3431789 C115.332083,93.0987152 112.287619,96.1431789 108.532083,96.1431789 L6.8,96.1431789 C3.0444637,96.1431789 -5.36775993e-13,93.0987152 -5.34571378e-13,89.3431789 L-5.34571378e-13,7.36224081 C-5.35031299e-13,3.60670451 3.0444637,0.562240812 6.8,0.562240812 L108.532083,0.562240812 Z M51.4550344,32.1701701 C49.5772662,32.1701701 48.0550344,33.692402 48.0550344,35.5701701 L48.0550344,35.5701701 L48.0550344,61.1354051 C48.0550344,61.8036906 48.2519771,62.4571575 48.621253,63.0141501 C49.6588552,64.5792044 51.7687252,65.0067887 53.3337794,63.9691865 L53.3337794,63.9691865 L72.6138514,51.1868529 C72.9928406,50.93559 73.3175788,50.6108585 73.5688494,50.2318744 C74.6064836,48.6668414 74.1789424,46.5569627 72.6139094,45.5193285 L72.6139094,45.5193285 L53.3338374,32.7364271 C52.7768326,32.3671267 52.1233436,32.1701701 51.4550344,32.1701701 Z" id="形状结合" fill="#FFFFFF"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>