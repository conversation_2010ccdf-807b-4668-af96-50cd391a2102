<template>
  <span :class="[waveClass, bigger]">
    <i v-for="i in 3" :key="i" :class="[dotClass, dotType]"></i>
  </span>
</template>

<script>
export default {
  name: 'Wave',
  props: {
    type: {
      type: String,
      default: ''
    },
    isPause: {
      type: Boolean,
      default: false
    },
    isBigger: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {
    waveClass() {
      return this.isPause ? 'yxtulcdsdk-live-task-wave-pause' : 'yxtulcdsdk-live-task-wave';
    },
    bigger() {
      return this.isBigger ? 'yxtulcdsdk-live-task-wave--bigger' : '';
    },
    dotClass() {
      return this.isPause ? 'yxtulcdsdk-live-task-wave-pause__dot' : 'yxtulcdsdk-live-task-wave__dot';
    },
    dotType() {
      return !this.isPause && this.type ? `yxtulcdsdk-live-task-wave__dot--${this.type}` : '';
    }
  }
};
</script>
