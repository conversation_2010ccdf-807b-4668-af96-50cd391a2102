<!--
 * @Date: 2022-06-06 14:23:21
 * @LastEditors: yuyangw
 * @LastEditTime: 2022-06-06 14:31:57
 * @FilePath: \tlive-pc\src\views\student\components\CornerMark.vue
-->
<template>
  <div :class="['yxtulcdsdk-live-task-corner-mark', animationClass]">
    <Wave v-if="isLiving" type="corner" />
    {{ studioStatus(roomInfo.roomStatus) }}
  </div>
</template>

<script>
import { ROOM_STATUS } from '../../../core/enum';
import Wave from '../../../views/student/components/Wave.vue';

export default {
  name: 'CornerMark',
  components: { Wave },
  props: {
    roomInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    // 角标class
    animationClass() {
      const status = this.roomInfo.roomStatus;
      return status === ROOM_STATUS.ENDED
        ? this.roomInfo.recorded
          ? 'yxtulcdsdk-live-task-corner-mark--ended'
          : 'yxtulcdsdk-live-task-corner-mark--replay'
        : status === ROOM_STATUS.LIVING
          ? 'yxtulcdsdk-live-task-corner-mark--living'
          : status === ROOM_STATUS.NOSTART
            ? 'yxtulcdsdk-live-task-corner-mark--notStart'
            : '';
    },
    // 是否在直播中的状态
    isLiving() {
      return this.roomInfo.roomStatus === ROOM_STATUS.LIVING;
    }
  },
  methods: {
    // 直播室状态集合
    studioStatus(status) {
      if (status === ROOM_STATUS.LIVING) {
        return this.$t('pc_tlive_liveUnderway' /* 直播中 */);
      }
      if (status === ROOM_STATUS.NOSTART) {
        return this.$t('pc_tlive_hasNotStarted' /* 未开始 */);
      }

      if (status === ROOM_STATUS.ENDED && this.roomInfo.recorded) {
        return this.$t('pc_tlive_over' /* 已结束 */);
      }

      return this.$t('pc_tlive_replayable' /* 可回放 */);
    }
  }
};
</script>
