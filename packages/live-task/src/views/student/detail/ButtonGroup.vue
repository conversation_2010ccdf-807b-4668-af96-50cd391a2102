<template>
  <div class="yxtulcdsdk-live-task-detail__button-group">
    <div v-if="roomInfo.liveType && roomInfo.roomStatus !== roomStatus.ENDED">
      <yxt-button
        class="yxtulcdsdk-live-task-detail__btn"
        size="others"
        type="primary"
        :loading="watchLoading"
        @click="goLiveRoom('offline')"
      >
        {{ $t('pc_tlive_fromLive' /* 从现场进入 */) }}
      </yxt-button>
      <yxt-button
        class="yxtulcdsdk-live-task-detail__btn"
        size="others"
        type="primary"
        :loading="watchLoading"
        @click="goLiveRoom('online')"
      >
        {{ $t('pc_tlive_fromRemote' /* 从远程接入 */) }}
      </yxt-button>
    </div>
    <template v-for="(item, i) in operation">
      <yxtf-button
        v-if="item.show"
        :key="i"
        class="yxtulcdsdk-live-task-detail__btn"
        size="others"
        type="primary"
        :disabled="item.disable"
        :loading="item.loading"
        @click="item.clickFn(item.clickParams ? item.clickParams : '')"
      >
        {{ item.name }}
      </yxtf-button>
    </template>
    <yxt-popover
      v-if="roomInfo.mobile === 1"
      placement="bottom"
      trigger="click"
      max-width="344"
      max-height="344"
    >
      <yxtf-button
        class="yxtulcdsdk-live-task-detail__qrcodeBtn"
        @click="getLiveroomWatchUrl"
        slot="reference"
      >
        <div class="yxtulcdsdk-live-task-detail__qrcodeBtn-qrcode"></div>
      </yxtf-button>
      <div class="yxtulcdsdk-live-task-detail__qrcode">
        <yxtbiz-qrcode
          v-if="isMobile"
          ref="shareQrcode"
          :url="currentAddress"
          hide-link="true"
          hide-download="true"
          :padding="10"
          :size="180"
          slot=""
        />
        <div>
          {{ $t('pc_tlive_adviseWatchOnMobile' /* 建议使用手机扫码观看 */) }}
        </div>
      </div>
    </yxt-popover>
    <div
      v-if="
        roomInfo.squareAudienceCountInfo &&
          roomInfo.squareAudienceCountInfo.audienceCountShowLiveSquare &&
          roomInfo.roomStatus === roomStatus.LIVING
      "
      class="yxtulcdsdk-live-task-detail__audience"
    >
      {{
        $t('pc_tlive_watching' /* {0}人在线 */, [
          filterNumber(roomInfo.squareAudienceCountInfo.audienceCount),
        ])
      }}
    </div>
  </div>
</template>

<script>
import qs from 'qs';
import { ROOM_STATUS, RECORD_STATUS } from '../../../core/enum';
import {
  getLiveroomWatchUrl,
  getRecordedPermissions,
  getPermissions
} from '../../../core/api/student';
import {
  isBrowserInWeixin,
  isIEBrowser
} from '../../../core/utils';

export default {
  name: 'ButtonGroup',
  props: {
    roomInfo: {
      type: Object,
      default: () => {}
    },
    roomId: {
      type: String,
      default: ''
    },
    taskId: {
      type: String,
      default: ''
    },
    trackId: {
      type: String,
      default: ''
    },
    isStudent: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      roomStatus: ROOM_STATUS,
      isMobile: false,
      currentAddress: '',
      watchLoading: false
    };
  },
  computed: {
    recordStatus() {
      return this.roomInfo.recordStatus === RECORD_STATUS.RECORDSUCCESS
        ? this.roomInfo.recordVersion
          ? this.$t('pc_tlive_watchreviewClip' /* 观看精编回放 */)
          : this.$t('pc_tlive_watchreview' /* 观看回放 */)
        : this.$t('pc_tlive_playbackGeneration' /* 回放生成中 */);
    },
    operation() {
      if (!this.isStudent) {
        return [{
          show: true,
          clickFn: () => {
            const query = {
              id: this.roomId
            };
            this.taskId && (query.taskId = this.taskId);
            this.trackId && (query.trackId = this.trackId);
            this.$emit('jump', `${window.location.origin}/tlive/#/livecenter/detail?${qs.stringify(query, {
              encode: false
            })}`);
          },
          disable: false,
          loading: false,
          name: this.$t('pc_tlive_management' /* 管理 */)
        }];
      }

      let operateArr = [];
      if (
        this.roomInfo.roomStatus === this.roomStatus.ENDED &&
        !this.roomInfo.recorded
      ) {
        operateArr.push({
          show: true,
          clickFn: this.goRecords,
          disable: this.roomInfo.recordStatus !== RECORD_STATUS.RECORDSUCCESS,
          loading: this.watchLoading,
          name: this.recordStatus
        });
      }
      const btnProps = {
        show: true,
        clickFn: '',
        loading: false,
        disable: false
      };
      if (
        this.roomInfo.roomStatus === this.roomStatus.ENDED &&
        this.roomInfo.recorded
      ) {
        btnProps.disable = true;
        btnProps.name = this.$t('pc_tlive_liveEnded' /* 直播已结束 */);
      } else if (
        !this.roomInfo.liveType &&
        this.roomInfo.roomStatus !== this.roomStatus.ENDED
      ) {
        btnProps.clickFn = this.goLiveRoom;
        btnProps.clickParams = 'live';
        btnProps.loading = this.watchLoading;
        btnProps.name = this.$t('pc_tlive_watchLive' /* 观看直播 */);
      } else {
        btnProps.show = false;
      }
      operateArr.push(btnProps);
      return operateArr;
    }
  },
  methods: {
    // 预约人数的计算规则
    filterNumber(number) {
      if (number >= 9999999) {
        return '999w+';
      } else if (number >= 10000) {
        return parseInt(number / 10000) + 'w';
      } else if (number >= 1000) {
        return (number / 1000).toFixed(1) + 'k';
      }
      return number || 0;
    },
    // 获取手机观看二维码
    async getLiveroomWatchUrl() {
      try {
        const res = await getLiveroomWatchUrl(this.roomId);
        this.currentAddress = res;
        this.isMobile = true;
      } catch (e) {
        this.$message({
          duration: 1000,
          message: this.$t('pc_tlive_getQrcodeFail' /* 获取二维码失败 */),
          type: 'error'
        });
      }
    },
    // 点击观看回放
    async goRecords() {
      if (this.roomInfo.recordStatus !== RECORD_STATUS.RECORDSUCCESS) {
        const recordStatusTitle = {
          [RECORD_STATUS.NOTSTART]: this.$t(
            'pc_tlive_recordingNotStart' /* 录制未开始 */
          ),
          [RECORD_STATUS.RECORDING]: this.$t('pc_tlive_recording' /* 正在录制中 */),
          [RECORD_STATUS.TRANSCODING]: this.$t(
            'pc_tlive_transcoding' /* 正在转码中 */
          ),
          [RECORD_STATUS.RECORDFAILED]: this.$t(
            'pc_tlive_recordingFailed' /* 录制失败 */
          ),
          [RECORD_STATUS.PRIVATE_TRANSCODING]: this.$t(
            'pc_tlive_transcoding' /* 正在转码中 */
          )
        };
        const message = recordStatusTitle[this.roomInfo.recordStatus];
        this.$message({
          duration: 1000,
          message: message,
          type: 'error'
        });
        return;
      }

      try {
        const params = {
          roomId: this.roomId,
          taskId: this.taskId || '',
          trackId: this.trackId || ''
        };
        await getRecordedPermissions(this.roomId, {
          roomId: this.roomId,
          taskId: this.taskId || ''
        });

        this.$emit('jump', `${window.location.origin}/tlive-room/#/review?${qs.stringify(params, {
          encode: false
        })}`);
      } catch (error) {
        console.log(error);
      }
    },
    // 点击观看直播
    async goLiveRoom(type) {
      if (isBrowserInWeixin() || isIEBrowser()) {
        this.$emit('jump', `${window.location.origin}/tlive/#/browser?origin=${encodeURIComponent(window.location.href)}`);
        return;
      }

      this.watchLoading = true;
      const params = {
        deviceType: 0, // pc 0
        enterMethod: 0, // 判断是否为分享链接进入
        omoWatchType: type === 'offline' ? 0 : 1
      };
      this.taskId && (params.taskId = this.taskId);

      const query = {
        id: this.roomId,
        liveType: type
      };
      this.taskId && (query.taskId = this.taskId);
      this.trackId && (query.trackId = this.trackId);
      try {
        await getPermissions(this.roomId, params);
        this.$emit('jump', `${window.location.origin}/tlive-hybird/?${qs.stringify(query, {
          encode: false
        })}`);
        this.watchLoading = false;
      } catch (error) {
        if (
          error && error.error && error.error.key ===
          'apis.tlive.room.validation.room.StudentUser.NotMatch'
        ) {
          this.$emit('jump', `${window.location.origin}/tlive/#/livecenter/detail?${qs.stringify(query, {
            encode: false
          })}`);
        }
        this.watchLoading = false;
      }
    }
  }
};
</script>
