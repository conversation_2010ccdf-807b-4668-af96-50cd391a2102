<template>
  <div v-floading.lock="loading" class="yxtulcdsdk-live-task-detail">
    <div>
      <div>
        <div class="yxtulcdsdk-live-task-detail__info">
          <div class="yxtulcdsdk-live-task-detail__info-left">
            <yxtf-image
              class="yxtulcdsdk-live-task-detail__info-image"
              :src="roomInfo.cover || defaultCover"
              fit="cover"
              lazy
            >
              <div slot="error" class="image-slot">
                <i class="yxt-icon-picture-outline"></i>
              </div>
            </yxtf-image>
            <corner-mark :room-info="roomInfo" />
          </div>
          <div class="yxtulcdsdk-live-task-detail__info-right">
            <h3 class="yxtulcdsdk-live-task-detail__info-title">
              {{ roomInfo.roomName }}
            </h3>
            <p class="yxtulcdsdk-live-task-detail__info-date">
              <span>{{ $t('pc_tlive_startTime2' /* 开始时间 */) }}:&nbsp;</span>
              <span>{{ i18nTime(roomInfo.beginTime) }}</span>
              <span class="yxtulcdsdk-live-task-detail__info-duration">
                {{ liveDuration }}
              </span>
            </p>
            <yxt-popover
              v-if="roomInfo.liveType"
              placement="top"
              width="500"
              trigger="hover"
              :open-filter="true"
              :content="roomInfo.liveLocation"
            >
              <p class="yxtulcdsdk-live-task-detail__info-location" slot="reference">
                {{ $t('pc_tlive_address' /* 地址 */) }}:
                {{ roomInfo.liveLocation }}
              </p>
            </yxt-popover>

            <div class="yxtulcdsdk-live-task-detail__info-runtime">
              <div v-if="roomInfo.roomStatus === ROOM_STATUS.NOSTART" class="yxtulcdsdk-live-task-detail__noStart">
                <div v-if="!deadLine" class="yxtulcdsdk-live-task-detail__count-down">
                  <i18n path="pc_tlive_beginLiveTips" tag="span" class="yxtulcdsdk-live-task-detail__count-txt">
                    <span>
                      <i18n
                        v-for="(item, i) in timesFormat"
                        :key="i"
                        :path="item.unit"
                        tag="span"
                        class="yxtulcdsdk-live-task-detail__count-unit"
                      >
                        <span class="yxtulcdsdk-live-task-detail__count-number">{{
                          item.time
                        }}</span>
                      </i18n>
                    </span>
                  </i18n>
                </div>
                <p v-else class="yxtulcdsdk-live-task-detail__count-down">
                  {{ $t('pc_tlive_aboutToStart' /* 即将开始 */) }}
                </p>
              </div>
              <div v-else-if="roomInfo.roomStatus === ROOM_STATUS.LIVING" class="yxtulcdsdk-live-task-detail__living">
                <div class="yxtulcdsdk-live-task-detail__living-icon">
                  <Wave is-bigger="true" />
                </div>
                <div class="yxtulcdsdk-live-task-detail__living-msg">
                  <p class="yxtulcdsdk-live-task-detail__living-desc">
                    {{ $t('pc_tlive_living' /* 直播进行中 */) }}
                  </p>
                  <!-- <p class="yxtulcdsdk-live-task-detail__living-data">
                    {{
                      $t('pc_tlive_watching' /* {0}人正在观看 */, [
                        roomInfo.counttl,
                      ])
                    }}
                  </p> -->
                </div>
              </div>
              <div v-else-if="roomInfo.roomStatus === ROOM_STATUS.ENDED" class="yxtulcdsdk-live-task-detail__ended">
                <div class="yxtulcdsdk-live-task-detail__ended-icon">
                  <Wave is-pause="false" />
                </div>
                <div class="yxtulcdsdk-live-task-detail__ended-msg">
                  <p class="yxtulcdsdk-live-task-detail__ended-msg--status">
                    {{
                      $t('pc_tlive_liveStreamWatched' /* {0}人已观看直播 */, [
                        roomInfo.squareAudienceCountInfo.audienceCount
                      ])
                    }}
                  </p>
                </div>
              </div>
            </div>
            <button-group
              v-if="!loading"
              :room-info="roomInfo"
              :is-student="isStudent"
              :room-id="roomId"
              :task-id="taskId"
              :track-id="trackId"
              @jump="jumpExternal"
            />
          </div>
        </div>
        <div
          v-if="roomInfo.appraiseFlag &&
            roomInfo.roomStatus === ROOM_STATUS.ENDED &&
            (showEvalutionState === FUNC_CODE_STATE.PURCHASED_SHOW || showEvalutionState === FUNC_CODE_STATE.PURCHASED_CUSTOM || showEvalutionState === FUNC_CODE_STATE.EXPIRED_DISABLED)
          "
          class="yxtulcdsdk-live-task-detail__evalution"
        >
          <span>
            {{ $t('pc_tlive_evalution' /* 讲师评价 */) }}
            <span class="yxtulcdsdk-live-task-detail__evalution-tips">{{
              $t('pc_tlive_please_evaluation' /* 请对本场直播讲师进行评价 */)
            }}</span>
          </span>
          <yxt-button
            type="second"
            :disable="showEvalutionState === FUNC_CODE_STATE.EXPIRED_DISABLED"
            @click="jumpEvalution"
          >{{
            $t('pc_tlive_immediately_evaluation' /* 立即评价 */)
          }}</yxt-button>
        </div>
        <div class="yxtulcdsdk-live-task-detail__cont">
          <h3 class="yxtulcdsdk-live-task-detail__cont-title">
            {{ $t('pc_tlive_teacher' /* 讲师 */) }}
          </h3>
          <teacher-group :room-info="roomInfo" :host-teacher="hostTeacher" />
          <h3 class="yxtulcdsdk-live-task-detail__cont-title">
            {{ $t('pc_tlive_liveIntroduction' /* 直播介绍 */) }}
          </h3>
          <div v-if="roomInfo.remark" class="yxtulcdsdk-live-task-detail__intro" v-html="filterXss(roomInfo.remark)"></div>
          <yxtf-empty
            v-else
            class="yxtulcdsdk-live-task-detail__delete"
            :empty-text="$t('pc_tlive_noLiveIntroduction' /* 暂无直播介绍 */)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CornerMark from '../../../views/student/components/CornerMark.vue';
import Wave from '../../../views/student/components/Wave.vue';
import ButtonGroup from '../../../views/student/detail/ButtonGroup.vue';
import TeacherGroup from '../../../views/student/detail/TeacherGroup.vue';
import { getEvalutionLink, getLiveDetail } from '../../../core/api/student';
import { ROOM_STATUS, USER_ROLE } from '../../../core/enum';
import coverImg from '../../../assets/svg/bgCover.svg';
import { i18nTime, filterXss, checkFactor, FUNC_CODE_STATE } from '../../../core/utils';

export default {
  components: {
    ButtonGroup,
    Wave,
    CornerMark,
    TeacherGroup
  },
  props: [
    'taskId',
    'trackId',
    'roomId'
  ],
  data() {
    return {
      ROOM_STATUS,
      FUNC_CODE_STATE,
      loading: true,
      // 静态数据
      // 默认封面图
      defaultCover: coverImg,
      // 视图数据
      // 存储倒计时时间
      times: {},
      // 业务逻辑数据
      // 存储直播室信息
      roomInfo: {},
      // 过了时间仍未开播的判定
      deadLine: false,
      hostTeacher: {},
      showEvalutionState: FUNC_CODE_STATE.DEFAULT,
      timer: null
    };
  },
  computed: {
    liveDuration() {
      return `${this.$t(
        'pc_tlive_liveDuration' /* 直播时长 */
      )} ：${this.$t('pc_tlive_anyMinute' /* {0}分钟 */, [
        this.roomInfo.duration ? Math.ceil(this.roomInfo.duration / 60) : 0
      ])} `;
    },
    timesFormat() {
      let timesFormat = [];
      if (this.times.days > 0) {
        timesFormat.push({
          time: this.times.days,
          unit: 'pc_tlive_shortDay' /* {0}天 */
        });
      }
      if (this.times.days <= 0 && this.times.hours > 0) {
        timesFormat.push({
          time: this.times.hours,
          unit: 'pc_tlive_hour' /* {0}时 */
        });
      }
      if (this.times.days <= 0 && this.times.minutes > 0) {
        timesFormat.push({
          time: this.times.minutes,
          unit: 'pc_tlive_shortMinute' /* {0}分 */
        });
      }
      return timesFormat;
    },
    isStudent() {
      return this.roomInfo.userRole === USER_ROLE.STU;
    }
  },
  async created() {
    try {
      await this.getCurDetail();
      await this.getFactors();
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }

  },
  methods: {
    i18nTime,
    filterXss,
    async getCurDetail() {
      this.roomInfo = await getLiveDetail(
        this.roomId,
        {}
      );

      this.hostTeacher = this.roomInfo.masterInfo;
      if (this.roomInfo.roomStatus === ROOM_STATUS.NOSTART) {
        let time =
            new Date(this.roomInfo.beginTime.replace(/-/g, '/')).getTime() -
            Date.now();
        time < 0 && (this.deadLine = true);
        time > 0 && this.countDownFun(time);
      }
    },
    async getFactors() {
      this.showEvalutionState = await checkFactor('tools_appraise');
    },
    // 如果有权限去管理后台
    jumpExternal(url) {
      // TODO 跳出提示
      window.open(url, '_blank');
    },
    // 处理时间
    formatTime(time) {
      let days = (time / 1000 / 60 / 60 / 24) | 0;
      let hours = (time / 1000 / 60 / 60) % 24 | 0;
      const minutes =
        ((time / 1000 / 60) % 60 | 0) + ((time / 1000) % 60 > 0 ? 1 : 0);
      hours = minutes === 60 ? hours + 1 : hours;
      days = hours === 24 ? days + 1 : days;
      this.times = {
        days: days,
        hours: hours,
        minutes: minutes === 60 ? 0 : minutes
      };
    },
    // 计算距离开始时间还有多久
    countDownFun(time) {
      if (this.timer) clearInterval(this.timer);
      this.deadLine = false;
      this.formatTime(time);
      this.timer = setInterval(() => {
        time = time - 1000;
        this.formatTime(time);
        if (time <= 0) {
          this.deadLine = true;
          clearInterval(this.timer);
        }
      }, 1000);
    },
    async jumpEvalution() {
      try {
        const res = await getEvalutionLink(this.roomId);
        this.jumpExternal(res);
      } catch (e) {
        console.error(e);
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timer);
  }
};
</script>
