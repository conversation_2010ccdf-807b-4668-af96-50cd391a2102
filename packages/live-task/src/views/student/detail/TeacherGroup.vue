<template>
  <div class="yxtulcdsdk-live-task-detail__teach-cont">
    <!--头像组-->
    <div
      ref="teachGroup"
      class="yxtulcdsdk-live-task-detail__avatar-group"
      :class="{
        'yxtulcdsdk-live-task-detail__avatar-group--expand ':
          isShowtoggle && !isMultiLine,
      }"
    >
      <div
        v-for="i in teachersList"
        :key="i.id"
        class="yxtulcdsdk-live-task-detail__avatar"
      >
        <yxtf-portrait
          size="72px"
          :img-url="i.imgUrl || ''"
          :username="i.viewName || ''"
        />
        <span class="yxtulcdsdk-live-task-detail__teacher">
          <yxtbiz-user-name
            :name="i.viewName || '--'"
            class="yxtulcdsdk-live-task-detail__teacher-name"
          />
          <i
            class="yxtulcdsdk-live-task-detail__name-tag"
            :class="{
              'yxtulcdsdk-live-task-detail__name-tag--guest':
                i.teacherType !== TEACHER_TYPE.ISTEACHER,
            }"
          >{{ getTagName(i) }}</i>
        </span>
      </div>
    </div>
    <!--展开收起按钮 -->
    <div
      v-if="isShowtoggle"
      class="yxtulcdsdk-live-task-detail__toggle-exhibit"
      :class="{ 'is-expand': isMultiLine }"
      @click="toggleClick"
    >
      <div>
        {{
          isMultiLine
            ? $t('pc_tlive_putAway' /* 收起 */)
            : $t('pc_tlive_unfold' /* 展开 */)
        }}
      </div>
      <em class="yxtulcdsdk-live-task-detail__toggle-icon"></em>
    </div>
  </div>
</template>

<script>
import { TEACHER_TYPE } from '../../../core/enum';

export default {
  name: 'TeacherGroup',
  props: {
    roomInfo: {
      type: Object,
      default: () => {}
    },
    hostTeacher: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isShowtoggle: false,
      isMultiLine: false,
      teachersList: [],
      TEACHER_TYPE: TEACHER_TYPE
    };
  },

  watch: {
    roomInfo() {
      this.roomInfo.teachers.forEach(item => {
        if (item.teacherType === TEACHER_TYPE.ISTEACHER) {
          this.teachersList.unshift(item);
        } else {
          this.teachersList.push(item);
        }
      });
      this.determineExpand();
    }
  },
  mounted() {
    this.determineExpand();
  },
  methods: {
    // 判断是否需要展开
    determineExpand() {
      this.$nextTick(() => {
        if (this.$refs.teachGroup.offsetHeight > 120) {
          this.isShowtoggle = true;
          this.isMultiLine = false;
        }
      });
    },

    // 展开收起
    toggleClick() {
      this.isMultiLine = !this.isMultiLine;
    },

    getTagName(element) {
      return element.teacherType === TEACHER_TYPE.ISTEACHER
        ? this.$t('pc_tlive_presenter')
        : this.$t('pc_tlive_guest');
    }
  }
};
</script>
