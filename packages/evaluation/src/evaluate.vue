<template>
  <div class="eval_assess_wrapper">
    <div class="assess_topic mt24">
      <div class="flex-1">
        <!-- 顶部 title -->
        <div class="model_name medium mr24 mb24">{{ topicData.modelName }}</div>
        <!-- 各个能力的描述 -->
        <div class="assess_main mr24">
          <div
            v-for="item in topicData.skillList"
            :key="item.skillId"
          >
            <div class="main_head">
              <span class="menu">{{ item.index }}</span>
              <span class="medium">{{ item.skillName }}</span>
            </div>
            <!-- 单人评估 -->
            <template v-if="self">
              <div v-for="user in item.behaUserList" :key="user.id">
                <!-- 能力描述 -->
                <div v-if="item.skillDescription" class="c26 mt6 color-gray-7">{{ item.skillDescription }}</div>
                <div v-if="item.skillBehavior" class="c26 mt6 color-gray-7">{{ item.skillBehavior }}</div>
                <!-- 请选择一个选项 -->
                <div class="hint mt4" :class="isDone ? 'visibility-hide' : 'visibility-show'">{{ $t('pc_gwnl_eval_selectOption') }}</div>
                <div class="mt2">
                  <yxtf-radio-group v-model="user.skillOptionId" direction="row" @change="changeAnswerAndNextQues">
                    <yxtf-radio
                      v-for="(skill, sIdx) in user.skillLevelList"
                      :key="skill.id"
                      :label="skill.id"
                      :disabled="user.finished && skill.levelTrue === 0"
                    >
                      <span class="medium">{{ skill.name }}</span>
                      <!-- 显示标准的标签 -->
                      <span v-if="item.standardLevel === (sIdx + 1) && isShowStandard" class="standardTag">{{ $t('pc_gwnl_chart_legend_standardLevel') }}</span>
                      <div class="ml22 break lh22 color-gray-7">
                        <div>{{ skill.description }}</div>
                        <div>{{ skill.behavior }}</div>
                      </div>
                    </yxtf-radio>
                  </yxtf-radio-group>
                </div>
              </div>
            </template>
            <!-- 批量评估 -->
            <div v-else>
              <div class="topic_intro">
                <div v-if="item.skillDescription" class="mt4 color-gray-7">{{ item.skillDescription }}</div>
                <!-- 描述信息 -->
                <div v-if="showTopicIntro" class="hidebox mt12">
                  <div v-if="item.skillBehavior" class="color-gray-7">{{ item.skillBehavior }}</div>
                  <div class="ability_grade mt16">
                    <div class="medium">{{ $t('pc_eval_web_levelStandard') }}</div>
                    <div
                      v-for="(ability, aIndex) in item.behaUserList[0].skillLevelList"
                      :key="ability.skillId"
                      class="mt12 ability_grade_item"
                    >
                      <span class="menu mr8">{{ aIndex + 1 }}</span>
                      <span class="medium">{{ ability.name }}</span>
                      <span v-if="item.standardLevel === ability.level" class="standardTag">
                        {{ $t('pc_gwnl_chart_legend_standardLevel') }}
                      </span>
                      <div v-if="ability.description" class="color-gray-7 mt4">{{ ability.description }}</div>
                      <div v-if="ability.behavior" class="color-gray-7 mt4">{{ ability.behavior }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="show_more mt6 hand" @click="showMore">
                <!-- 请选择一个选项 -->
                <div v-if="!isDone" class="hint">{{ $t('pc_gwnl_eval_selectOption') }}</div>
                <div v-else></div>
                <div class="color-primary-6">
                  <span>{{ $t(showTopicIntro ? 'pc_common_lbl_slideup' : 'pc_eval_web_up_more') }}</span>
                  <yxtf-svg
                    width="16px"
                    height="16px"
                    :icon-class="showTopicIntro ? 'up' : 'down'"
                  />
                </div>
              </div>
              <!-- 答题信息 -->
              <div class="main_list mt16">
                <div class="head">
                  <div class="column_first ml24 mr44">
                    {{ $t('pc_gwnl_eval_lbl_assessee') }}
                  </div>
                  <div
                    v-for="(skill, sIdx) in item.behaUserList[0].skillLevelList"
                    :key="skill.id"
                    class="ability_term"
                  >
                    <span class="ability_term_name">{{ skill.name }}</span>
                    <span v-if="item.standardLevel === (sIdx + 1)" class="standardTag">{{ $t('pc_gwnl_chart_legend_standardLevel') }}</span>
                  </div>
                </div>
                <div
                  v-for="user in item.behaUserList"
                  :key="user.id"
                  class="assessor_list"
                >
                  <yxtf-portrait
                    :img-url="user.imgUrl"
                    :username="user.fullName"
                    size="small"
                    background-color="#436bff"
                    color="#fff"
                    class="ml24 mr8"
                  />
                  <yxt-tooltip :content="user.fullName" placement="top" open-filter>
                    <div class="column_first ellipsis-2line-text">
                      {{ user.fullName }}
                    </div>
                  </yxt-tooltip>

                  <yxtf-radio-group
                    v-for="skill in user.skillLevelList"
                    :key="skill.id"
                    v-model="user.skillOptionId"
                    class="ability_radio"
                  >
                    <yxtf-radio
                      :label="skill.id"
                      :disabled="user.finished && skill.levelTrue === 0"
                    />
                  </yxtf-radio-group>
                </div>
              </div>
            </div>
          </div>
          <div class="main_footer">
            <!-- 如果只有一题的情况 -->
            <template v-if="topicData.total === 1">
              <yxtf-button
                size="larger"
                type="primary"
                class="w240"
                @click="quesBtnClick"
              >
                {{ $t(buttonText) }}
              </yxtf-button>
            </template>
            <div v-else class="btn_box">
              <yxtf-button
                v-if="!isCompleted"
                size="larger"
                plain
                :loading="saveBtnLoading"
                @click="saveQuest"
              >
                {{ $t('pc_gwnl_global_btn_save') }}
              </yxtf-button>
              <yxtf-button
                :disabled="params.offset === 1"
                size="larger"
                @click="prevQuest"
              >
                {{ $t('pc_gwnl_web_lbl_survey_btn_last') }}
              </yxtf-button>
              <yxtf-button
                v-if="!isLastQues"
                size="larger"
                type="primary"
                :loading="nextBtnLoading"
                @click="nextQuest"
              >
                {{ $t('pc_gwnl_web_lbl_survey_btn_next') }}
              </yxtf-button>
              <yxtf-button
                v-else-if="isCompleted || isLastQues"
                size="larger"
                type="primary"
                @click="quesBtnClick"
              >
                {{ $t(buttonText) }}
              </yxtf-button>
            </div>
          </div>
        </div>
      </div>
      <div class="w280 flex-shrink-0">
        <progressBehavior
          ref="progressBehavior"
          :user-info="userInfo"
          :user-info-all="userInfoAll"
          :params="params"
          :topic-data="topicData"
          :multiple="multiple"
          :evaluation-type="evaluationType"
          :answer-overview="answerOverview"
          :self="self"
          :skill-list="skillList"
          :is-show-standard="isShowStandard"
          @check="handleToCheck"
          @save-evaluation="clickEvaluateInfo"
        />
      </div>
    </div>
  </div>
</template>

<script>
import progressBehavior from './components/progressBehavior';
import {
  postBehaviorAnswerBackView, postBehaviorCommitRecord,
  postSubmitBehavior
} from 'yxt-ulcd-sdk/packages/evaluation/src/service/eval.service';

export default {
  components: {
    progressBehavior
  },
  props: {
    queryData: {
      type: Object,
      default: ({})
    }
  },
  data() {
    return {
      modelId: this.queryData.modelId,
      evaluationId: this.queryData.evaluationId,
      relationType: this.queryData.relationType,
      JumpOffset: +this.queryData.offset,
      type: +this.queryData.type || 0,
      showTopicIntro: true,
      radio: 0,
      params: {
        offset: 1,
        limit: 1
      },
      topicData: {},
      isDone: true,
      self: false,
      firstCall: true,
      relationIds: this.queryData.relationIds ? [this.queryData.relationIds] : null,
      multiple: this.queryData.multiple,
      currentSkill: {}, // 当前第几题
      userInfo: {},
      evaluationType: this.queryData.evaluationType,
      answerOverview: [],
      saveBtnLoading: false,
      nextBtnLoading: false, // 下一页的按钮 loading
      switchBtnLoading: false, // 一题一页按钮 loading
      userInfoAll: [],
      skillList: {}, // 整个能力的 list
      skillListAll: [], // 获取所有能力时的赋值，用于查看答卷后不请求接口
      isShowStandard: 1,
      isSupportOptionNext: false // 是否支持点击选项后跳转下一题
    };
  },

  computed: {
    isCompleted() {
      return this.topicData.modelStatus === 1;
    },

    // 是否最后一题
    isLastQues() {
      // return this.params.offset === this.topicData.total

      const hasLength = Array.isArray(this.skillListAll) && this.skillListAll.length > 0;
      return hasLength &&
        this.currentSkill &&
        this.currentSkill.skillId === this.skillListAll[this.skillListAll.length - 1].skillId;
    },

    buttonText() {
      return this.isCompleted
        ? 'pc_gwnl_global_return'
        : 'pc_gwnl_web_btn_submitBtn';
    },

    isTrain() {
      return ~~this.queryData.isTrain === 1;
    }
  },

  mounted() {
    if (this.JumpOffset) {
      this.params.offset = this.JumpOffset + 1;
    }
    this.getData();
  },
  methods: {
    setIsShowStandard(isShowStandard) {
      if (![null, undefined].includes(isShowStandard)) {
        this.isShowStandard = isShowStandard;
      }
    },
    setIsSupportOptionNext(isSupportOptionNext) {
      this.isSupportOptionNext = isSupportOptionNext;
    },
    // 设置 topicData数据
    setTopicData(res) {
      this.topicData = res;
      // 判断是否是自评
      this.setIsShowStandard(this.relationType === '1' ? res.selfSwitch : 1);
      // 是否支持点击选项后跳转下一题
      this.setIsSupportOptionNext(!!res.autoFlipSwitch);
      this.currentSkill = (this.topicData.skillList && this.topicData.skillList[0]) || {};
      let userArr = [];
      this.topicData.skillList && this.topicData.skillList.forEach(skill => {
        if (skill.behaUserList && skill.behaUserList.length === 1) this.self = true;
        userArr = skill.behaUserList.map((user, idx) => {
          if (idx < 3) {
            return user.fullName;
          }

          return '';
        });
      });
      if (this.type === 1 && this.firstCall) {
        this.firstCall = false;
        this.$message.info({
          duration: 4000,
          message: `${this.topicData.userName}${this.$t('pc_eval_web_choose_truthfully_confidential', { userArr })}`
        });
      }
      this.userInfoAll = this.currentSkill.behaUserList || [];
      this.userInfo = this.userInfoAll[0];
    },

    getData() {
      // 第一次请求，这边是拿不到topicData数据的
      if (this.isCompleted) {
        this.handleCompleted();

        // 获取当前能力对应的index
        const skillIndex = this.params.offset - 1;
        if (this.skillListAll[skillIndex]) {
          this.$set(this.topicData, 'skillList', [this.skillListAll[skillIndex]]);
          this.setTopicData(this.topicData);
        }
        return;
      }
      // evaluatorType 评估人类型（1-自评, 2-上级，3-平级，4-下级）
      postBehaviorAnswerBackView(
        {
          limit: this.params.limit,
          modelId: this.modelId,
          offset: this.params.offset,
          evaluationId: this.evaluationId,
          evaluatorType: this.relationType,
          relationIds: this.relationIds
        })
        .then(res => { this.setTopicData(res); })
        .catch(err => this.$handleError(err));

      this.getAllBehaviorView();
    },

    handleCompleted() {
      this.saveBtnLoading = false;
      this.nextBtnLoading = false;
      this.switchBtnLoading = false;
    },

    // 获取所有评估的详情
    getAllBehaviorView(callback) {
      // 答题概览
      postBehaviorAnswerBackView(
        {
          limit: 999,
          modelId: this.modelId,
          offset: 1,
          evaluationId: this.evaluationId,
          evaluatorType: this.relationType,
          relationIds: this.relationIds
        }).then(res => {
        this.skillListAll = res.skillList;
        this.answerOverview = (res.skillList || []).map((item, itemIndex) => {
          item.behaUserList.forEach(user => {
            if (!this.skillList[user.id]) {
              this.skillList[user.id] = [];
            }

            const index = this.skillList[user.id].findIndex(skill => skill.skillId === item.skillId);

            // 当前判定用户的等级
            const currentUserLever = (user.skillLevelList || []).find(skill => skill.levelTrue);

            const userObj = {
              ...item,
              userLeval: currentUserLever || {},
              sort: itemIndex + 1,
              userId: user.id,
              fullName: user.fullName,
              isAnswer: user.skillOptionId ? 1 : 0,
              isPass: user.isPass
            };

            if (index !== -1) {
              // 如果已存在，则更新
              this.skillList[user.id][index] = userObj;
            } else {
              this.skillList[user.id].push(userObj);
            }
          });

          const isAllAnswer = item.behaUserList.every(user => user.skillOptionId);
          // 所有作答的情况
          const curSkillOptionId = item.behaUserList.map(user => user.skillOptionId);
          // isPass字段只针对单个评估有效，批量时候多人判断不出来是否通过，这个意义不大
          return {
            sort: itemIndex,
            skillName: item.skillName,
            skillId: item.skillId,
            skillOptionList: curSkillOptionId,
            isAnswer: isAllAnswer ? 1 : 0,
            isPass: item.behaUserList[0].isPass
          };
        });

        setTimeout(() => {
          callback && callback();
        }, 0);
      })
        .catch(err => this.$handleError(err))
        .finally(() => this.handleCompleted());
    },

    showMore() {
      this.showTopicIntro = !this.showTopicIntro;
    },
    nextQuest() {
      const nextAndGet = () => {
        this.params.offset = this.params.offset + 1;
        this.getData();
      };

      // 如果一页一题正在提交中，则点击直接return
      if (this.switchBtnLoading) return;

      this.nextBtnLoading = true;
      if (this.isCompleted) {
        nextAndGet();
        return;
      }

      this.saveDataCommitRecord(nextAndGet, '', 'pc_gwnl_web_lbl_survey_submit_failure');
    },
    selectAll(isSave) {
      const isSaveOper = typeof isSave === 'boolean' ? isSave : false;

      this.topicData.skillList.forEach(item => {
        // 是否全部选择
        this.isDone = item.behaUserList && item.behaUserList[isSaveOper ? 'some' : 'every'](level => level.skillOptionId);
      });
    },
    saveDataHandler() {
      const questionList = [];
      this.topicData.skillList.forEach(item => {
        // 数据处理
        const questionObj = {
          skillId: '',
          optionAnswers: []
        };
        questionObj.skillId = item.skillId;
        item.behaUserList.forEach(user => {
          const optionObj = {
            evalLevel: '',
            optionId: '',
            priority: 1,
            targetId: '',
            order: 0
          };
          const levelOption = user.skillLevelList.filter(skill => skill.id === user.skillOptionId);
          optionObj.targetId = user.id;
          optionObj.evalLevel = levelOption[0].level || '';
          optionObj.optionId = levelOption[0].id || '';
          questionObj.optionAnswers.push(optionObj);
        });
        questionList.push(questionObj);
      });

      const data = {
        evaluationId: this.evaluationId,
        evaluatorType: this.relationType,
        modelId: this.modelId,
        questionList
      };
      return data;
    },

    onSuccess() {
      this.$fmessage({
        message: this.$t('pc_gwnl_web_homework_tip_savesuccess'),
        type: 'success'
      });
    },

    saveQuest() {
      // 如果一页一题正在提交中，则点击直接return
      if (this.switchBtnLoading) return;

      if (this.isSupportOptionNext) {
        this.saveDataCommitRecord(this.onSuccess, '', true);
        return;
      }

      this.saveBtnLoading = true;
      this.saveDataCommitRecord(() => {
        // 获取所有能力list
        this.getAllBehaviorView();
        this.onSuccess();
      }, '', true);
    },
    prevQuest() {
      this.params.offset = this.params.offset - 1;
      // 重置报错的状态
      this.isDone = true;
      this.getData();
    },
    returnSubmit(type) {
      if (type === 0) {
        // 返回
        // this.$router.push({
        //   name: this.isTrain ? 'web.assess.train' : 'web.assess.questionnaire',
        //   query: {
        //     evaluationId: this.evaluationId
        //   }
        // });
        this.$emit('goQuestionnaire');
      } else {
        // 提交
        this.saveDataCommitRecord(() => {
          this.$confirm(this.$t('pc_h5_eval_web_cannot_submission'), this.$t('pc_h5_eval_web_submit_now'), {
            confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
            cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
            type: 'warning'
          }).then(() => {
            postSubmitBehavior(
              {
                evaluationId: this.evaluationId,
                evaluatorType: this.relationType,
                modelId: this.modelId,
                totalQuesNum: this.topicData.total,
                relationIds: this.relationIds
              }
            ).then(res => {
              this.$fmessage({
                message: this.$t('pc_gwnl_web_msg_submitSuccess'),
                type: 'success'
              });
              this.$router.push({
                name: this.isTrain ? 'web.assess.train' : 'web.assess.questionnaire',
                query: {
                  evaluationId: this.evaluationId
                }
              });
            }).catch(err => {
              this.$fmessage({
                message: `${this.$t('pc_gwnl_web_lbl_survey_submit_failure')}，${err}`,
                type: 'error'
              });
            });
          });
        });
      }
    },
    handleToCheck(index) {
      this.params.offset = index + 2;
      this.prevQuest();
    },

    async saveInterface(callback, catchKey) {
      try {
        const data = this.saveDataHandler();
        await postBehaviorCommitRecord(data);
        callback && callback();
      } catch (err) {
        this.$fmessage({
          message: `${this.$t(catchKey || 'pc_gwnl_setting_msg_saveFailed')}，${err}`,
          type: 'error'
        });
        this.handleCompleted();
      }
    },

    // 公共的保存操作
    async saveDataCommitRecord(callback, callback2, catchKey) {
      this.selectAll(catchKey);

      if (this.isDone) {
        this.saveInterface(callback, catchKey);
        return;
      }

      if (!callback2) {
        this.$message.error(this.$t('pc_gwnl_eval_selectOption' /* 请选择一个选项 */));
      }

      this.handleCompleted();
      callback2 && typeof callback2 === 'function' && callback2();
    },

    // 打开评估详情
    openVisibleDetail() {
      this.$refs.progressBehavior.openVisibleDetail();
    },

    // 5.3 需求要求点击评估详情的时候先进行保存
    // 单个行为评估保存单人作答
    // 批量行为评估保存多人作答，只要有人作答即保存
    async clickEvaluateInfo() {
      const curList = this.topicData.skillList[0];
      // 获取当前的skillId
      const currentSkillId = curList.skillId || '';
      // 获取当前作答的题目的选项
      const currentSkillOption = (curList.behaUserList || []).map(item => item.skillOptionId);
      // 根据是否作答和当前作答是否和接口返回一致判断是否需要调用保存接口
      const { isAnswer, skillOptionList } = this.answerOverview.find(item => item.skillId === currentSkillId);

      // 无论单次还是批量，已作答的题目，直接打开弹框
      if (isAnswer && JSON.stringify(skillOptionList) === JSON.stringify(currentSkillOption)) {
        this.isDone = true;
        this.openVisibleDetail();
        return;
      }

      this.saveDataCommitRecord(() => {
        // 获取所有能力list
        this.getAllBehaviorView(() => {
          this.openVisibleDetail();
        });
      }, () => {
        this.isDone = true;
        this.openVisibleDetail();
      }, !this.self);
    },

    quesBtnClick() {
      this.returnSubmit(this.isCompleted ? 0 : 1);
    },

    // 支持点击单个选项即保存答案跳转下一题
    changeAnswerAndNextQues() {
      if (!this.isSupportOptionNext || this.switchBtnLoading) return;

      this.switchBtnLoading = true;
      this.saveDataCommitRecord(() => {
        // 判断是否是最后一题
        if (this.params.offset < this.topicData.total) {
          this.params.offset = this.params.offset + 1;
        }

        this.getData();
      }, '', 'pc_gwnl_web_lbl_survey_submit_failure');
    }
  }
};
</script>

<style lang="scss" scoped>
.eval_assess_wrapper {
  height: 100%;
  margin: 24px auto;
  color: #8c8c8c;
  font-weight: 400;
  font-size: 14px;

  .medium {
    color: #262626;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
  }

  .c26 {
    color: #8c8c8c;
  }

  .f14 {
    font-size: 14px;
  }

  .model_name {
    height: 72px;
    line-height: 72px;
    text-align: center;
    background: #fff;
  }

  .hint {
    color: #f5222d;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }

  .standardTag {
    display: inline-block;
    width: 40px;
    height: 20px;
    margin-left: 4px;
    color: #436bff;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    vertical-align: text-top;
    background: #f0f6ff;
    border-radius: 2px;
  }

  .assess_topic {
    display: flex;
    height: calc(100% - 120px);
    padding-bottom: 40px;

    .assess_main {
      position: relative;
      flex: 1;
      padding: 40px 80px 100px 80px;
      background: #fff;

      .main_head {
        position: relative;

        .menu {
          position: absolute;
          left: -21px;
          width: 20px;
          text-align: center;
        }

        .tag {
          display: inline-block;
          height: 16px;
          padding: 0 4px;
          color: #436bff;
          font-size: 12px;
          line-height: 16px;
          text-align: center;
          background: rgb(54 105 255 / 10%);
          border-radius: 2px;
        }

        span {
          vertical-align: middle;
        }
      }

      .topic_intro {
        .hidebox {
          .ability_grade {
            padding: 24px;
            background: #faf9fa;
            border-radius: 4px;

            .menu {
              display: inline-block;
              width: 18px;
              height: 18px;
              color: #fff;
              font-size: 12px;
              line-height: 18px;
              text-align: center;
              vertical-align: text-top;
              background: #4570ff;
              border-radius: 4px;
            }

            .ability_grade_item {
              padding-bottom: 16px;
              border-bottom: 1px solid #e4e6e9;

              &:last-child {
                padding-bottom: 0;
                border-bottom: none;
              }
            }
          }
        }
      }

      .show_more {
        display: flex;
        align-items: center;
        justify-content: space-between;

        svg {
          vertical-align: sub;
        }
      }

      .main_list {
        .head {
          min-height: 50px;

          .ability_term {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            color: #262626;
            font-weight: 500;

            .ability_term_name {
              display: block;
              max-width: 70px;
              overflow: hidden;
              text-align: center;
              word-break: break-all;
            }
          }
        }

        .assessor_list {
          height: 64px;
          cursor: pointer;

          &:hover {
            background: #f0f6ff;
          }
        }

        & > div {
          display: flex;
          align-items: center;
          border-bottom: 1px solid #e9e9e9;
        }

        .column_first {
          width: 116px;
        }

        .ability_radio {
          display: flex;
          flex: 1;

          label {
            flex: 1;
            text-align: center;
          }

          ::v-deep .yxtf-radio__label {
            display: none;
          }
        }
      }

      .main_footer {
        position: absolute;
        bottom: 40px;
        left: 50%;
        text-align: center;
        transform: translate(-192px);

        .btn_box {
          display: flex;
          justify-content: center;
          width: 384px;

          button {
            flex: 1;
            max-width: 200px;
          }
        }
      }
    }

    .assess_aside {
      height: 122px;
      padding: 20px;
      color: #262626;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      background: #fff;

      .topicnum {
        :first-child {
          font-weight: 500;
          font-size: 22px;
          line-height: 30px;
        }

        :last-child {
          color: #8c8c8c;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }

  .userInfo-box {
    min-height: 208px;
    padding: 20px;
    background-color: white;
    border-radius: 4px;

    .tips-p {
      color: #262626;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
    }

    .img-box {
      margin-top: 4px;
      margin-bottom: 11px;
      text-align: center;

      .user-img {
        width: 64px;
        height: 64px;
        border-radius: 50%;

        &.img-deflut {
          display: inline-block;
          color: #fff;
          font-weight: 400;
          font-size: 20px;
          line-height: 64px;
          text-align: center;
          background: #b0b7d2;
        }
      }
    }

    .user-name {
      overflow: hidden;
      color: #262626;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
    }

    .user-des {
      display: -webkit-box;
      overflow: hidden;
      color: #8c8c8c;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      text-align: center;
      word-wrap: wrap;
      word-break: break-all;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }
}
</style>
