<template>
  <yxt-collapse class="pwidth100">
    <yxt-collapse-item
      v-for="(group, gIndex) in data"
      :key="gIndex"
      class="collapse-others"
      :show-item-icon="group.type !== 'self'"
    >
      <!-- title信息 -->
      <template slot="title">
        <div :class="['collapse-demo-custom-title', group.type === 'self' ? 'ml44' : '']">
          <div class="title-left pwidth50 flex">
            <yxt-tooltip
              theme-mode="dark"
              :content="group.title"
              placement="top"
              open-filter
            >
              <span class="title ellipsis inline-block width100">{{ group.title }}</span>
            </yxt-tooltip>
          </div>
          <div class="align-items-center">
            <yxt-progress
              :percentage="percentProcess(group)"
              size="small"
              class="pwidth-100"
              :status="percentProcess(group) === 100 ? 'success' : ''"
            />
            <span class="font14 color-gray-7 minW82 ml12 ellipsis">{{ $t('pc_eval_lbl_leftEvaluate',[group.processNumber]).d('0人待评估') }}</span>
          </div>
          <div class="title-right minW100 text-right">
            <btns
              v-if="group.type==='self'"
              :type="group.type"
              :type-code="group.typeCode"
              :is-only-self="isOnlySelf()"
              :data="group.data[0]"
              :base-data="baseData"
              @openiselect="handleEmitISelect"
            />
            <!-- 批量评估 -->
            <yxt-tooltip
              v-else
              effect="dark"
              :content="tooltipBatchEvaluate(group)"
              placement="top"
              :disabled="!disableBatchEvaluate(group)"
            >
              <span>
                <yxtf-button
                  :size="size"
                  :disabled="disableBatchEvaluate(group)"
                  type="primary-text"
                  @click="handleBatchEvaluate(group.data)"
                >
                  {{ $t('pc_eval_lbl_batchEvaluate').d('批量评估') }}
                </yxtf-button>
              </span>
            </yxt-tooltip>
          </div>
        </div>
      </template>
      <!-- 展开的内容项 -->
      <template v-if="group.type!=='self'">
        <div v-for="(person,pIndex) in group.data" :key="pIndex" class="lh50 border-top-gray pl48 straight-line bg-light">
          <div class="flex-row-between align-items-center">
            <div>
              <yxtf-portrait
                class="pos-relative top4"
                size="20px"
                :img-url="person.imgUrl"
                :username="person.fullname"
              />
              <span class="ml8 color-gray-10"><yxtbiz-user-name :name="person.fullname" /></span>
              <span class="ml8 mr8">({{ person.username }})</span>
              <yxtf-svg
                v-show="person.commitStatus===COMMIT_STATUS.completed"
                width="10px"
                height="10px"
                icon-class="icons/f_feedback-success"
              />
            </div>
            <div class="right-box mr4">
              <btns
                :type="group.type"
                :type-code="group.typeCode"
                :is-only-self="isOnlySelf()"
                :data="person"
                :base-data="baseData"
                :collapse="true"
                @openiselect="handleEmitISelect"
              />
            </div>
          </div>
        </div>
      </template>
    </yxt-collapse-item>
  </yxt-collapse>
</template>

<script>
import Btns from './btns.vue';
// 答题状态 0-未答题 1-已答题，暂存 2-已提交
const COMMIT_STATUS = {
  noStart: 0,
  started: 1,
  completed: 2
};

export default {
  name: 'TaskDetailTaskListItem',
  components: {
    Btns
  },
  inject: {
    isOnlySelf: {
      from: 'isOnlySelf',
      default: false
    },
    inTimeRange: {
      from: 'inTimeRange',
      default: false
    },
    isBeforeTime: {
      from: 'isBeforeTime',
      default: false
    },
    isAfterTime: {
      from: 'isAfterTime',
      default: false
    }
  },
  props: {
    baseData: {
      type: Object
    },
    type: {
      type: String
    },
    typeCode: {
      type: Number
    },
    data: {
      type: Array
    },
    index: {
      type: Number
    },
    typeOrigin: {
      type: [Number, String]
    }
  },
  data: function() {
    return {
      COMMIT_STATUS,
      titleOptions: [this.$t('pc_h5_eval_evaluate_self'), this.$t('pc_h5_eval_evaluate_up'), this.$t('pc_h5_eval_evaluate_same'), this.$t('pc_h5_eval_evaluate_down')],
      relationList: window.sessionStorage.getItem('relationList') ? JSON.parse(window.sessionStorage.getItem('relationList')) : [],
      isVisit: this.$route.query.isVisit
    };
  },
  computed: {
    title: function() {
      let tit = '';

      switch (this.type) {
        case 'self':
          tit = this.$t('pc_h5_eval_evaluate_self');
          break;
        case 'upLevel':
          tit = this.$t('pc_h5_eval_evaluate_up');
          break;
        case 'sameLevel':
          tit = this.$t('pc_h5_eval_evaluate_same');
          break;
        case 'downLevel':
          tit = this.$t('pc_h5_eval_evaluate_down');
          break;
        default:
          return '';
      }

      return tit;
    }
  },
  methods: {
    handleEmitISelect(data, type) {
      this.$emit('openiselect', data, type);
    },
    // 批量评估
    handleBatchEvaluate(batchAll) {
      // 批量一次最多处理300人
      // let batch = batchAll.length > 300 ? batchAll.splice(0, 300) : batchAll
      const batch = batchAll.splice(0, 300);
      const { relationId, evaluatedUserId: userId } = this.data;
      const { evaluationType, surveyId } = this.baseData;

      const evaluationId = batch[0].evaluationId;
      const evaluatorType = batch[0].relationType;

      // 不用vuex因为页面强刷以后会丢失数据
      const relationTrain = this.relationList.filter(item => item.evaluationId === evaluationId && item.relationType === evaluatorType);
      if (relationTrain && relationTrain.length) {
        relationTrain[0].relationIds = batch.map(item => item.relationId);
      } else {
        this.relationList.push({
          evaluationId,
          relationType: evaluatorType,
          relationIds: batch.map(item => item.relationId)
        });
      }
      window.sessionStorage.removeItem(`warnInfo_${evaluationId}_${evaluatorType}`);
      window.sessionStorage.setItem('relationList', JSON.stringify(this.relationList));

      // this.$router.push({
      //   name: 'web.assess.multi',
      //   query: {
      //     evaluationId,
      //     evaluationType,
      //     surveyId,
      //     evaluatorType,
      //     userId,
      //     relationId,
      //     isVisit: this.isVisit
      //   }
      // });
      this.handleEmitISelect({
        evaluationId,
        evaluationType,
        surveyId,
        evaluatorType,
        userId,
        relationId,
        isVisit: this.isVisit
      }, 'goMulti');
    },
    disableBatchEvaluate(group) {
      const isAllCompleteSelf = (group.data || []).every(item => item.canComment === 1);
      // 启用条件：项目进行中&有员工未完成&在起止时间范围内
      return !((!this.baseData.finished && this.baseData.evaluationStatus !== 2) && group.processNumber && this.inTimeRange()) || !isAllCompleteSelf;
    },

    tooltipBatchEvaluate(group) {
      let toolMsg = '';
      const isAllCompleteSelf = (group.data || []).every(item => item.canComment === 1);
      if (!(!this.baseData.finished && this.baseData.evaluationStatus !== 2)) {
        toolMsg = this.$t('pc_eval_msg_batch_over').d('测评项目已结束，不能进入答题');
      } else if (this.isBeforeTime()) {
        toolMsg = this.$t('pc_eval_msg_batch_beforeTime').d('还未到测评时间，不能进入答题');
      } else if (group.processNumber === 0) {
        toolMsg = this.$t('pc_eval_msg_batch_students').d('学员已经全部答完，不需要重复答题');
      } else if (!isAllCompleteSelf) {
        toolMsg = this.$t('pc_eval_tips_all_self_finished' /* 评估关系中所有评估人完成自评，才允许批量评估 */);
      } else if (this.isAfterTime()) {
        toolMsg = this.$t('pc_eval_msg_batch_expire').d('测评项目已过期，不能进入答题');
      }
      return toolMsg;
    },

    percentProcess(group) {
      return Math.round(((group.data.length - group.processNumber) / group.data.length) * 100);
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;

  .self_ui_wrap {
    width: 100%;
  }

  .flex-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    background: #fff;
    box-shadow: inset 0 -1px 0 0 #e9e9e9;

    .left-title {
      margin-left: 32px;
      color: #262626;
      font-weight: 500;
      font-size: 14px;
    }

    .left-name {
      margin-left: 48px;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
    }

    .right-box {
      margin-right: 4px;
    }
  }
}

.collapse-demo-custom-title {
  display: flex;
  justify-content: space-between;
  width: calc(100% - 44px);
  padding-right: 4px;
}

.collapse-others {
  ::v-deep .yxt-collapse-item__content {
    padding: 0;
  }
}
</style>
