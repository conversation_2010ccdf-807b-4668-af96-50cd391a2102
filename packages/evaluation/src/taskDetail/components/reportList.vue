<template>
  <div class="wrap">
    <h3
      class="title"
      :style="{ 'marginTop': top }"
    >
      {{ $t('pc_eval_web_start_report') }}
    </h3>
    <div
      v-for="report in reports"
      :key="report.id"
      class="report-item"
    >
      <div class="name">
        <yxtf-svg
          width="16px"
          height="18px"
          icon-class="icons/f_kng-pdf"
        />
        <div>{{ report.reportName }}</div>
      </div>
      <yxt-button
        class="right-btn"
        type="text"
        @click="handleClickViewReport(report)"
      >
        {{ $t('pc_gwnl_global_btn_view') }}
      </yxt-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskDetailReport',

  props: {
    reports: {
      type: Array
    },
    top: {
      type: String,
      default: '40px'
    },
    baseData: {
      type: Object
    }
  },
  methods: {
    handleClickViewReport(report) {
      const { joinType, evaluationId, evaluationType, userRelationMap } = this.baseData;

      if (evaluationType === 3 && joinType === 1) {
        const userId = userRelationMap['1'][0].evaluatedUserId;

        this.$http.beizhi.sendReportViewInfo({
          evaluationId,
          userId,
          name: report.reportName
        });
      }

      setTimeout(() => {
        window.open(report.reportLink, '_blank');
      }, 0);
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;

  .title {
    height: 24px;
    margin-bottom: 12px;
    color: #262626;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;

    &::before {
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 12px;
      vertical-align: -3px;
      background: url("https://stc.yxt.com/ufd/b0174a/evaluation/pc/svg/ico_eval_report.svg") left top no-repeat;
      content: "";
    }
  }

  .report-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding: 0 24px;
    background: #fff;
    box-shadow: inset 0 -1px 0 0 #e9e9e9;

    &:hover {
      background: #f0f6ff;
      box-shadow: inset 0 -1px 0 0 #e9e9e9;
    }

    .name {
      display: flex;
      flex-direction: row;
      align-items: center;

      div {
        width: 650px;
        margin-left: 8px;
        overflow: hidden;
        color: #262626;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .right-btn {
      flex-grow: 0;
      flex-shrink: 0;
      margin-right: 4px;
    }
  }
}
</style>>
