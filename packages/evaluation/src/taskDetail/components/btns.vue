<template>
  <div :class="{'wrap': true, 'web-btn-transparent' : collapse}">
    <!-- 已过期 -->
    <yxt-tooltip
      v-if="!data.finished && isAfterTime() && baseData.evaluationStatus !== 2"
      effect="dark"
      :content="$t('pc_eval_ask_btn_expired_tips_a')"
      placement="top"
    >
      <span>
        <yxtf-button
          :size="size"
          :type="btnType"
          :plain="btnPlan"
          :disabled="true"
        >
          {{ $t('pc_gwnl_web_lbl_survey_btn_outdated') }}
        </yxtf-button>
      </span>
    </yxt-tooltip>
    <!-- 未完成&已结束 === 已结束 -->
    <yxt-tooltip
      v-if="!data.finished && baseData.evaluationStatus === 2"
      effect="dark"
      :content="$t('pc_eval_tip_ask_btn_end').d('测评已经结束，不能进入答题')"
      placement="top"
    >
      <span>
        <yxtf-button
          :size="size"
          :type="btnType"
          :plain="btnPlan"
          :disabled="true"
        >
          {{ $t('pc_gwnl_btn_end_status').d('已结束') }}
        </yxtf-button>
      </span>
    </yxt-tooltip>
    <!-- 未完成查看答卷 -->
    <yxt-tooltip
      v-else-if="!data.finished && data.commitStatus === COMMIT_STATUS.completed"
      effect="dark"
      :content="$t('pc_eval_ask_btn_system_questions_tips')"
      placement="top"
    >
      <span>
        <yxtf-button
          :size="size"
          :type="btnTypeStd2"
          :plain="btnPlan"
          :disabled="true"
        >
          {{ $t('pc_gwnl_web_lbl_survey_btn_view') }}
        </yxtf-button>
      </span>
    </yxt-tooltip>
    <!-- 继续评估 -->
    <yxt-tooltip
      v-else-if="!data.finished && inTimeRange() && data.commitStatus === COMMIT_STATUS.started"
      effect="dark"
      :content="$t('pc_eval_tips_self_finished')"
      placement="top"
      :disabled="data.canComment"
    >
      <yxtf-button
        :disabled="!data.canComment"
        :size="size"
        :type="btnType"
        @click="handleContinueEval"
      >
        {{ $t('pc_eval_ask_btn_continue') }}
      </yxtf-button>
    </yxt-tooltip>
    <!-- 分发中 -->
    <yxtf-button
      v-else-if="data.evaluationUserStatus === -1"
      :size="size"
      :type="btnType"
      disabled
    >
      {{ $t('pc_eval_fenfazhong_doing') }}
    </yxtf-button>
    <!-- 立即评估 -->
    <yxt-tooltip
      v-else-if="!data.finished && (inTimeRange() || isBeforeTime())"
      effect="dark"
      :content="$t('pc_eval_tips_self_finished')"
      placement="top"
      :disabled="data.canComment"
    >
      <yxtf-button
        :disabled="(!data.finished && isBeforeTime()) || !data.canComment"
        :size="size"
        :type="btnType"
        @click="handleStartEval"
      >
        {{ $t('pc_eval_ask_btn_now_stat' /* 立即评估 */) }}
      </yxtf-button>
    </yxt-tooltip>
    <!-- 已完成查看答卷 -->
    <yxtf-button
      v-if="data.finished && data.canViewed"
      :type="btnTypeStd2"
      :size="size"
      :plain="btnPlan"
      @click="handleViewSurveyResult"
    >
      {{ $t('pc_gwnl_web_lbl_survey_btn_view') }}
    </yxtf-button>
    <!-- 已完成不允许查看答卷 -->
    <yxt-tooltip
      v-if="data.finished && !data.canViewed"
      effect="dark"
      :content="$t('pc_gwnl_web_lbl_survey_btn_not_check')"
      placement="top"
    >
      <span>
        <yxtf-button
          :disabled="true"
          :type="btnTypeStd2"
          :plain="btnPlan"
          :size="size"
        >
          {{ $t('pc_gwnl_web_lbl_survey_btn_view') }}
        </yxtf-button>
      </span>
    </yxt-tooltip>
    <!-- 要求多少分内答完  -->
    <yxt-dialog
      :key="true"
      :visible.sync="dialogVisible"
      width="520px"
      :append-to-body="true"
      :cutline="false"
      class="highLayer"
    >
      <div class="layer-content">
        <div>{{ $t('pc_gwnl_warmTips') }}</div>
        <p>
          {{ $t('pc_eval_web_evaluation_tips_left') }}<span>{{ data.timeLimit }}{{ $t('pc_gwnl_jq_min') }}</span>{{
            $t('pc_eval_web_evaluation_tips_right')
          }}
        </p>
        <span
          slot="footer"
          class="dialog-footer"
        >
          <yxtf-button
            plain
            @click="dialogVisible = false"
          >{{ $t('pc_eval_web_answernexttime') }}</yxtf-button>
          <yxtf-button
            type="primary"
            @click="goAskSurvey(false)"
          >{{ $t('pc_eval_web_nowanswerthequestion') }}
          </yxtf-button>
        </span>
      </div>
    </yxt-dialog>
  </div>
</template>

<script>
import { clusterAll, clusterProduct } from '../../configs/behavior';

// 答题状态 0-未答题 1-已答题，暂存 2-已提交
const COMMIT_STATUS = {
  noStart: 0,
  started: 1,
  completed: 2
};

export default {
  name: 'TaskDetailBtns',
  inject: {
    inTimeRange: {
      from: 'inTimeRange',
      default: false
    },
    isBeforeTime: {
      from: 'isBeforeTime',
      default: false
    },
    isAfterTime: {
      from: 'isAfterTime',
      default: false
    }
  },
  props: {
    type: {
      type: String
    },
    typeCode: {
      type: Number
    },
    isOnlySelf: {
      type: Boolean
    },
    data: {
      type: Object
    },
    baseData: {
      type: Object
    },
    collapse: {
      type: Boolean,
      default: false
    }
  },
  data: function() {
    return {
      dialogVisible: false,
      COMMIT_STATUS,
      isVisit: this.$route.query.isVisit,
      clusterAll,
      clusterProduct
    };
  },
  computed: {
    size() {
      return this.isOnlySelf ? 'larger2' : '';
    },
    btnType() {
      return this.isOnlySelf ? 'primary' : 'primary-text';
    },
    btnTypeStd2() {
      return this.isOnlySelf ? '' : 'primary-text';
    },
    btnPlan() {
      return this.isOnlySelf;
    }
  },
  methods: {
    goBack() {
      let weburl = this.$route.query.weburl;
      if (weburl) {
        weburl = weburl.indexOf('http') === -1 ? `https://${weburl}` : weburl;
        window.location.href = decodeURIComponent(weburl);
      } else {
        const href = '/spgwnl/#/web/eval';
        window.location.href = href;
      }
    },
    handleViewSurveyResult() {
      // 查看问卷和去答题逻辑一致
      const { evaluationType: type } = this.baseData;
      const { evaluationUrl } = this.data;
      if (type === 3) {
        window.open(evaluationUrl, '_blank');
      } else if (type === 2) {
        this.goAskSurvey();
      } else {
        // 行为评估应该去行为页面
        this.goActionEval();
      }
    },
    openISelectedDialog() {
      this.$emit('openiselect', this.data);
    },
    goAskSurvey(needWarning = true) {
      const { evaluationId, relationId, evaluatedUserId: userId } = this.data;
      const { evaluationType, surveyId } = this.baseData;

      if (needWarning && this.data.timeLimit && this.data.commitStatus === COMMIT_STATUS.noStart) {
        this.dialogVisible = true;
        return;
      }

      const evaluatorType = this.typeCode;
      // this.$router.push({
      //   name: 'web.assess.survey',
      //   query: {
      //     evaluationId,
      //     evaluationType,
      //     surveyId,
      //     userId,
      //     relationId,
      //     evaluatorType,
      //     isVisit: this.isVisit
      //   }
      // });
      this.$emit('openiselect', {
        evaluationId,
        evaluationType,
        surveyId,
        userId,
        relationId,
        evaluatorType,
        isVisit: this.isVisit
      }, 'goAskSurvey');
    },
    goThirdEval() {
      const { joinType } = this.baseData;
      const { evaluationUrl } = this.data;
      if (joinType === 1) {
        const paramDomain = encodeURI(JSON.stringify({ 'Yxt-Orgdomain': localStorage.getItem('domain'), product: clusterProduct[window.feConfig.appName], cluster: this.clusterAll[window.feConfig.apiEnv] || window.feConfig.apiEnv }));
        const urlDomain = evaluationUrl + '&channelId=' + paramDomain;
        window.open(urlDomain, '_blank');
      } else {
        this.openISelectedDialog();
      }
    },
    goActionEval() {
      const { evaluationId, positionId, evaluatedUserId: userId, relationType, finished } = this.data;
      const operate = finished === 1 ? 0 : 1;
      const assess = finished === 1 ? 1 : 0;

      // this.$toBlankPage({
      //   name: 'web.assess.evaluate',
      //   query: {
      //     evaluationId,
      //     positionId,
      //     userId,
      //     evaluatorType: relationType,
      //     operate,
      //     assess
      //   }
      // });

      this.$emit('openiselect', {
        evaluationId,
        positionId,
        userId,
        evaluatorType: relationType,
        operate,
        assess
      }, 'goEvaluate');
    },
    handleStartEval() {
      const { evaluationType: type } = this.baseData;
      const { evaluationId, relationType } = this.data;
      if (type === 3) {
        this.goThirdEval();
      } else if (type === 2) {
        sessionStorage.removeItem(`warnInfo_${evaluationId}_${relationType}`);
        this.goAskSurvey();
      } else if (type === 1) {
        // 行为评估处理
        this.goActionEval();
      }
    },

    handleContinueEval() {
      this.handleStartEval();
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;

  .highLayer {
    z-index: 9999;
  }
}

.layer-content {
  padding: 20px 32px;
  text-align: center;

  div {
    margin-bottom: 20px;
    color: #262626;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }

  p {
    margin-bottom: 50px;
    color: #262626;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;

    span {
      color: #ff8c43;
    }
  }
}

.web-btn-transparent {
  ::v-deep .yxtf-button.is-disabled,
  .yxtf-button.is-disabled:hover {
    background-color: transparent;
  }
}
</style>
