<template>
  <div class="wrap">
    <template v-if="isOnlySelf()">
      <div class="flex-row-center self_ui_wrap">
        <btns
          :type="type"
          :type-code="typeCode"
          :class="{ 'flex-row-center': isOnlySelf() }"
          :is-only-self="isOnlySelf()"
          :data="data"
          :base-data="baseData"
          @openiselect="handleEmitISelect"
        />
      </div>
    </template>
    <!-- 仅自评结束， 他评开始 -->
    <template v-else>
      <div
        v-if="type === 'self'"
        class="flex-box"
      >
        <div class="left-title">
          {{ title }}
        </div>
        <div class="right-box">
          <btns
            :type="type"
            :type-code="typeCode"
            :is-only-self="isOnlySelf()"
            :data="data"
            :base-data="baseData"
            @openiselect="handleEmitISelect"
          />
        </div>
      </div>
      <template v-else>
        <div
          v-if="index === 0"
          class="flex-box"
        >
          <div class="left-title">
            {{ title }}
          </div>
        </div>
        <div class="flex-box">
          <div class="left-name">
            {{ data.fullname }}
          </div>
          <div class="right-box">
            <btns
              :type="type"
              :type-code="typeCode"
              :is-only-self="isOnlySelf()"
              :data="data"
              :base-data="baseData"
              @openiselect="handleEmitISelect"
            />
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import Btns from './btns.vue';
// 答题状态 0-未答题 1-已答题，暂存 2-已提交
const COMMIT_STATUS = {
  noStart: 0,
  started: 1,
  completed: 2
};

export default {
  name: 'TaskDetailTaskListItem',
  components: {
    Btns
  },
  inject: {
    isOnlySelf: {
      from: 'isOnlySelf',
      default: false
    }
  },
  props: {
    baseData: {
      type: Object
    },
    type: {
      type: String
    },
    typeCode: {
      type: Number
    },
    data: {
      type: Object
    },
    index: {
      type: Number
    },
    typeOrigin: {
      type: [Number, String]
    }
  },
  data: function() {
    return {
      COMMIT_STATUS,
      xxx: 1,
      titleOptions: [this.$t('pc_h5_eval_evaluate_self'), this.$t('pc_h5_eval_evaluate_up'), this.$t('pc_h5_eval_evaluate_same'), this.$t('pc_h5_eval_evaluate_down')]
    };
  },
  computed: {
    title: function() {
      let tit = '';

      switch (this.type) {
        case 'self':
          tit = this.$t('pc_h5_eval_evaluate_self');
          break;
        case 'upLevel':
          tit = this.$t('pc_h5_eval_evaluate_up');
          break;
        case 'sameLevel':
          tit = this.$t('pc_h5_eval_evaluate_same');
          break;
        case 'downLevel':
          tit = this.$t('pc_h5_eval_evaluate_down');
          break;
        default:
          return '';
      }

      return tit;
    }
  },

  methods: {
    handleEmitISelect(data, type) {
      this.$emit('openiselect', data, type);
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;

  .self_ui_wrap {
    width: 100%;
  }

  .flex-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    background: #fff;
    box-shadow: inset 0 -1px 0 0 #e9e9e9;

    .left-title {
      margin-left: 32px;
      color: #262626;
      font-weight: 500;
      font-size: 14px;
    }

    .left-name {
      margin-left: 48px;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
    }

    .right-box {
      margin-right: 4px;
    }
  }
}

.collapse-demo-custom-title {
  display: flex;
  justify-content: space-between;
  width: calc(100% - 44px);
  padding-right: 4px;
}

.collapse-others {
  ::v-deep .yxt-collapse-item__content {
    padding: 0;
  }
}
</style>
