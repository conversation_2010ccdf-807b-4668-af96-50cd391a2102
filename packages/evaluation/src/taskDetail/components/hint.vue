<template>
  <div v-if="content.length" class="wrap" :style="{'marginTop': top}">
    <h3 class="title">
      {{ $t('pc_gwnl_web_lbl_survey_information_hint') }}
    </h3>
    <div
      ref="content"
      v-dompurify-html="contentFormated"
      class="hint-text word-break"
      :class="{ expanded: isExpanded }"
    ></div>

    <div v-if="isContentOverflow" class="align-items-center-inline color-primary-6 mt8 hand" @click="toggleExpand">
      <span>{{ isExpanded ? $t('pc_gwnl_clamp_retract' /* 收起 */) : $t('pc_gwnl_clamp_open' /* 展开 */) }}</span>

      <yxt-svg
        width="14px"
        height="14px"
        class="ml4"
        :icon-class="isExpanded ? 'arrow-up' : 'arrow-down'"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskDetailHint',
  props: {
    top: {
      type: String,
      default: '60px'
    },
    content: {
      type: String,
      default: '--'
    }
  },
  data() {
    return {
      isExpanded: false, // 控制内容是否展开
      isContentOverflow: false
    };
  },
  computed: {
    contentFormated() {
      const a = this.content;
      return a;
    }
  },

  watch: {
    content: {
      immediate: true,
      handler(v) {
        v && this.$nextTick(() => {
          if (this.$refs.content) {
            this.isContentOverflow = this.$refs.content.scrollHeight > this.$refs.content.clientHeight;
          }
        });
      }
    }
  },

  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    }
  }
};
</script>

<style lang="scss" scoped>
  .wrap {
    width: 100%;

    .word-break {
      white-space: pre-wrap;
      word-break: break-all;
    }

    .hint-text {
      max-height: 88px; /* 4 行 x 22px 行高 */
      margin-top: 12px;
      overflow: hidden;
      color: #595959;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      transition: max-height 0.3s ease;

      &.expanded {
        max-height: none;
      }
    }

    .title {
      height: 24px;
      color: #262626;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;

      &::before {
        display: inline-block;
        width: 18px;
        height: 18px;
        margin-right: 12px;
        vertical-align: -3px;
        background: url("https://stc.yxt.com/ufd/b0174a/evaluation/pc/svg/ico_exam.svg") left top no-repeat;
        content: "";
      }
    }
  }

</style>>
