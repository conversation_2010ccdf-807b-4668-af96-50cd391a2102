<template>
  <div
    class="wrap"
    :style="{ 'marginTop': isOnlySelf ? '107px' : top }"
  >
    <h3 v-if="!isOnlySelf" class="title">
      {{ $t('pc_eval_lbl_answerInfo').d('答题信息') }}
    </h3>
    <template v-if="isOnlySelf">
      <taskItem
        v-for="(item, index) in tasks"
        :key="index"
        :index="item.index"
        :base-data="baseData"
        :data="item"
        :type-code="item.relationType"
        :type-origin="tasks[0].relationType"
        :type="relationOption[item.relationType].typeName"
        @openiselect="(data, type) => type ? relationOption[item.relationType].openiselect(data, type) : relationOption[item.relationType].openiselect(item)"
      />
    </template>
    <template v-else>
      <taskCollapse
        :base-data="baseData"
        :data="multiGroup"
        @openiselect="handleEmitISelect"
      />
    </template>
  </div>
</template>

<script>
import taskItem from './taskItem.vue';
import taskCollapse from './taskCollapse.vue';
export default {
  name: 'TaskDetailTaskList',
  components: {
    taskItem, taskCollapse
  },
  props: {
    isOnlySelf: {
      type: Boolean
    },
    top: {
      type: String,
      default: '40px'
    },
    task: {
      type: Object,
      default: () => {
        return {};
      }
    },
    baseData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      relationOption: {
        1: {
          typeName: 'self',
          openiselect: this.handleEmitISelect
        },
        2: {
          typeName: 'upLevel'
        },
        3: {
          typeName: 'sameLevel'
        },
        4: {
          typeName: 'downLevel'
        }
      }
    };
  },
  computed: {
    tasks() {
      // 解决IE11下flat不兼容的问题
      const list = Object.entries(this.task).map(([relationType, task]) => {
        return task.map((d, index) => ({ ...d, relationType, orderIndex: 0, index }));
      });
      return this.flatDeep(list, Infinity);
    },
    multiGroup: function() {
      const multiPersons = Object.entries(this.task).map(([relationType, task]) => {
        return {
          type: (this.relationOption[relationType] && this.relationOption[relationType].typeName) || `${relationType}Leval`,
          title: this.getRelationName(relationType),
          typeCode: relationType,
          processNumber: task.filter(item => item.commitStatus !== 2).length,
          data: task.map((d, index) => ({ ...d, relationType, orderIndex: 0, index }))
        };
      });
      return multiPersons;
    }
  },
  methods: {
    handleEmitISelect(data, type) {
      this.$emit('openiselect', data, type);
    },
    flatDeep(arr, d = 1) {
      return d > 0
        ? arr.reduce((acc, val) => acc.concat(Array.isArray(val) ? this.flatDeep(val, d - 1) : val), [])
        : arr.slice();
    },

    getRelationName(relationType) {
      const { dimensionInfoBeans } = this.baseData;
      const dimension = (dimensionInfoBeans || []).find(item => item.type === relationType);
      const name = dimension ? dimension.dimensionName : '';
      return this.$t('pc_eval_lbl_evaluation_role', [name]);
    },

    getAllRelationSuccess() {
      return this.multiGroup.every(group => {
        return Math.round(((group.data.length - group.processNumber) / group.data.length) * 100) === 100;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;

  .hint-text {
    margin-top: 12px;
    color: #595959;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }

  .title {
    height: 24px;
    margin-bottom: 12px;
    color: #262626;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;

    &::before {
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 12px;
      vertical-align: -3px;
      background: url("https://stc.yxt.com/ufd/b0174a/evaluation/pc/svg/ico_exam_start.svg") left top no-repeat;
      content: "";
    }
  }
}
</style>>
