<template>
  <div class="wrap">
    <h2 class="eval_name">
      {{ title }}
    </h2>
    <div class="eval_time">
      {{ $t('pc_eval_ask_text_time_range') }}{{ startTime | filterTime }} {{ $t('pc_biz_te_lbl_to' /* 至 */) }} {{ endTime | filterTime }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskDetailHeader',

  filters: {
    filterTime(time) {
      if (!time) return '';
      return time.substring(0, time.length - 3);
    }
  },
  props: {
    title: {
      type: String
    },
    startTime: {
      type: String
    },
    endTime: {
      type: String
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  display: flex;
  flex-direction: column;
  width: 100%;

  .eval_name {
    // height: 32px;
    color: #262626;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    text-align: center;
  }

  .eval_time {
    height: 22px;
    margin-top: 7px;
    color: #595959;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    text-align: center;
  }
}
</style>>
