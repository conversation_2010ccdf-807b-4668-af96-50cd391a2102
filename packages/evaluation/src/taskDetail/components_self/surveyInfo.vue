<template>
  <div class="wrap" :style="{marginTop: top}">
    <div class="questionsCount">
      <yxt-svg
        :remote-url="'https://stc.yxt.com/ufd/b0174a/evaluation/pc/svg/eval'"
        class="color-white vertical-align--middle mr9"
        width="20px"
        height="20px"
        icon-class="eval-counts"
      />
      <div class="font1">
        {{ $t('pc_gwnl_web_lbl_survey_information_counts') }}：
      </div>
      <div class="font2">
        <span v-if="questionsCount > 0">{{ questionsCount }}{{ $t('pc_eval_question') }}</span>
        <span v-else>--</span>
      </div>
    </div>
    <div class="timeLimt">
      <yxt-svg
        :remote-url="'https://stc.yxt.com/ufd/b0174a/evaluation/pc/svg/eval'"
        class="color-white vertical-align--middle mr9"
        width="20px"
        height="20px"
        icon-class="eval-time"
      />
      <div class="font1">
        {{ $t('pc_eval_timelit_title') }}：
      </div>
      <div class="font2">
        <span v-if="timeLimt">{{ timeLimt }}{{ $t('pc_eval_minute') }}</span>
        <span v-else-if="timeLimt === 0">{{ $t('pc_gwnl_web_lbl_survey_information_no_time') }}</span>
        <span v-else>--</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TaskDetailSurveyInfo',
  filters: {
    filterTime(time) {
      if (!time) return '';
      return time.substring(5);
    }
  },
  props: {
    top: {
      type: String,
      default: '28px'
    },
    questionsCount: {
      type: String
    },
    timeLimt: {
      type: String
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 85px;
  background: #fafafa;
  border-radius: 10px;

  &::before {
    position: absolute;
    left: 50%;
    width: 1px;
    height: 45px;
    font-size: 0;
    line-height: 0;
    background: #e9e9e9;
    content: "";
  }

  .questionsCount {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    width: 50%;
    padding-left: 70px;
  }

  .timeLimt {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    width: 50%;
    padding-left: 80px;
  }

  .font1 {
    color: #757575;
    font-weight: 400;
    font-size: 18px;
  }

  .font2 {
    color: #262626;
    font-weight: 500;
    font-size: 18px;
  }
}
</style>>
