<template>
  <div class="survey__description">
    <div class="survey__description-container">
      <div
        v-loading="loading"
        class="survey__description-content"
      >
        <!-- 测评 title -->
        <div class="color-gray-10 font-size-18 lh26 font-weight-500">{{ baseData.evaluationName }}</div>
        <!-- 测评有效时间 -->
        <div class="color-gray-7 font-size-12 mt4">
          {{ $t('pc_eval_ask_text_time_range') }}{{ baseData.startTime | filterTime }} {{ $t('pc_biz_te_lbl_to' /* 至 */) }} {{ baseData.endTime | filterTime }}
        </div>

        <div v-if="url" class="survey__description-qrcode mt32">
          <yxtbiz-qrcode
            :url="url"
            :hide-link="true"
            :hide-download="true"
            :padding="12"
            :size="248"
          />
        </div>

        <div class="font-size-14 mt26">
          {{ $t('pc_eval_lbl_take_part_in_mobile_evaluation' /* 仅支持通过手机端参加测评，请打开绚星APP扫描二维码 */) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { evalTaskStartPage } from '../service/eval.service.js';
import {generateShortUrl} from 'yxt-ulcd-sdk/packages/_utils/core/operate';
import dayjs from 'dayjs';

export default {
  name: 'BEIAI',
  data() {
    return {
      loading: false,
      baseData: {},
      url: ''
    };
  },
  filters: {
    filterTime(time) {
      return dayjs(time).format('YYYY-MM-DD HH:mm');
    }
  },
  computed: {
    evaluationId() {
      return this.$route.query.evaluationId;
    }
  },

  created() {
    this.getData();
  },

  mounted() {
    this.$nextTick(() => {
      this.getShortUrl();
    });
  },

  methods: {
    async getData() {
      try {
        this.loading = true;
        const result = await evalTaskStartPage({ evaluationId: this.evaluationId });
        this.baseData = result;
      } catch (error) {
        this.$handleError(error);
      } finally {
        this.loading = false;
      }
    },

    getShortUrl() {
      const pcUrl = `web/assess/beiai?evaluationId=${this.evaluationId}`;
      const h5Url = `eval/beiai?evaluationId=${this.evaluationId}`;

      generateShortUrl(h5Url, pcUrl).then(res => {
        if (res && res.url) {
          this.url = res.url;
        }
      }).catch(this.$handleError);
    }
  }
};
</script>

<style lang="scss" scoped>
.survey__description {
  height: 100%;
  background: url("https://stc.yxt.com/ufd/b0174a/evaluation/pc/img/questionnaire-bg.png") no-repeat top center;
  background-color: #fdfdfd;
  background-size: 1920px auto;

  &-container {
    width: 1200px;
    min-width: 1200px;
    margin: 0 auto;
  }

  &-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 1200px;
    min-height: calc(100vh - 70px);
    padding: 15vh 210px 56px;
    background: #fff;
  }

  &-qrcode {
    border: 1px solid #e9e9e9;
    border-radius: 8px;
  }
}
</style>
