<template>
  <div class="behavior_page mt40">
    <div
      v-for="(item, index) in behaviorDetail"
      :key="index"
      class="behavior_item mb16"
    >
      <div class="item_left">
        <h2>{{ $t(item.title) }}</h2>
        <div v-if="item.userList" class="img_box ml8">
          <template
            v-for="(user, i) in item.userList"
          >
            <yxtf-portrait
              v-if="i < 3"
              :key="user.id"
              :img-url="user.imgUrl"
              :class="'img' + i"
              class="aimage"
              size="small"
              :username="user.fullName"
            />
          </template>
          <span class="ml8">{{ item.userList.length }}{{ $t('pc_h5_eval_web_place') }}{{ $t('pc_gwnl_web_toBeEvaluated') }}</span>
        </div>
      </div>
      <div class="item_right">
        <yxtf-tag size="mini" :type="tagStatus(item).tagtype">
          {{ $t(tagStatus(item).tagname) }}
        </yxtf-tag>
        <yxtf-progress :percentage="parseInt(item.finishedRate * 100)" />
        <div class="ml24">
          <btngroup
            :val="item"
            :detail="detail"
            :btn-type="true"
            @selectModel="selectModel"
          />
          <div v-if="item.modelList && item.modelList.length > 1" class="mode_brief">
            {{ $t('pc_h5_eval_web_total_models', { count: item.modelList.length }) }}
          </div>
        </div>
      </div>
    </div>
    <yxtf-dialog
      :title="$t('pc_gwnl_global_pleaseSelect')"
      :visible.sync="dialogShow"
      :before-close="handleClose"
      destroy-on-close
      :modal-append-to-body="false"
    >
      <div>
        <div
          v-for="model in modelList"
          :key="model.modelId"
          class="model_dialog mb12"
        >
          <h1 class="mb12 title">
            {{ model.modelName }}
          </h1>
          <div class="model_item">
            <div class="item_left">
              <template
                v-for="(user, i) in model.userList"
              >
                <yxtf-portrait
                  v-if="i < 3"
                  :key="user.id"
                  :img-url="user.imgUrl"
                  :class="'img' + i"
                  class="aimage"
                  size="small"
                  :username="user.fullName"
                />
              </template>
              <div class="img_box ml8">
                {{ $t('pc_gwnl_web_toBeEvaluated') }}：{{ model.userNum }}{{ $t('pc_h5_eval_web_person') }}
              </div>
            </div>
            <div class="item_right">
              <yxtf-progress :percentage="parseInt(model.finishedRate * 100)" />
              <div class="ml24">
                <btngroup
                  :val="model"
                  :detail="detail"
                  :btn-type="true"
                  @selectModel="selectModel"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </yxtf-dialog>
  </div>
</template>

<script>
import btngroup from './btngroup.vue';
export default {
  components: {
    btngroup
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dialogShow: false,
      dialogPaddingEmpty: false,
      behaviorDetail: [],
      modelList: []
    };
  },
  mounted() {
    this.evalDimension(this.detail);
  },
  methods: {
    selectModel(mode) {
      this.modelList = mode;
      this.dialogShow = true;
    },
    evalDimension(val) {
      let arr = [];
      for (const k in val.userRelationMap) {
        if (val.userRelationMap[k].length > 0) {
          switch (k) {
            case '1':
              val.userRelationMap[k][0].title = 'pc_h5_eval_evaluate_self';
              break;
            case '2':
              val.userRelationMap[k][0].title = 'pc_h5_eval_evaluate_up';
              break;
            case '3':
              val.userRelationMap[k][0].title = 'pc_h5_eval_evaluate_same';
              break;
            case '4':
              val.userRelationMap[k][0].title = 'pc_h5_eval_evaluate_down';
              break;
          }

          arr = arr.concat(val.userRelationMap[k]);
        }
      }
      this.behaviorDetail = arr;
    },
    tagStatus(tagV) {
      const tagObj = [
        { tagname: 'pc_gwnl_global_notStarted', tagtype: 'info' },
        { tagname: 'pc_gwnl_global_processing', tagtype: 'primary' },
        { tagname: 'pc_gwnl_global_completed', tagtype: 'success' }
      ];
      // 已完成 添加人 列表状态
      if (tagV.finishedRate > 0 && tagV.finishedRate < 1) {
        return tagObj[1];
      }
      // 未开始
      if (tagV.commitStatus === 0) {
        return tagObj[0];
      }
      // 进行中
      if (tagV.commitStatus === 1 && tagV.finished === 0) {
        return tagObj[1];
      }
      // 已完成
      if (tagV.finished === 1 || tagV.finished === -1) {
        return tagObj[2];
      }
      return tagObj[0];
    }
  }
};
</script>

<style lang="scss" scoped>
.behavior_page {
  width: 100%;

  .behavior_item {
    display: flex;
    justify-content: space-between;
    padding: 14px 16px 10px 20px;
    background: #fafafa;
    border-radius: 8px;
  }

  .item_left {
    display: flex;
    align-items: center;

    h2 {
      color: #262626;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }

    .img_box {
      display: flex;
      align-items: center;
      color: #8c8c8c;
      font-size: 12px;

      .aimage {
        border: 2px solid #fff;
      }

      .img1 {
        margin-left: -12px;
      }

      .img2 {
        margin-left: -12px;
      }
    }
  }

  .item_right {
    display: flex;
    align-items: center;

    .mode_brief {
      color: #8c8c8c;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
    }
  }

  .model_dialog {
    padding: 24px;
    background: #fafafa;

    .title {
      display: -webkit-box;
      overflow: hidden;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      //text-overflow: -o-ellipsis-lastline;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
    }

    .model_item {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
