<template>
  <div class="behavior_page mt40">
    <yxt-collapse class="pwidth100">
      <yxt-collapse-item
        v-for="(group, gIndex) in behaviorDetail"
        :key="gIndex"
        class="collapse-others"
        :show-item-icon="group.type !== 'self'"
      >
        <!-- title信息 -->
        <template slot="title">
          <div :class="['collapse-demo-custom-title', group.type === 'self' ? 'ml44' : '']">
            <!-- 左侧自定义评估 title -->
            <yxt-tooltip
              theme-mode="dark"
              :content="group.title"
              placement="top"
              open-filter
            >
              <span class="title ellipsis inline-block width100">{{ group.title }}</span>
            </yxt-tooltip>
            <!-- 进度 + {0}人待评估 -->
            <div class="align-items-center ml8">
              <yxt-progress
                :percentage="percentProcess(group)"
                size="small"
                class="pwidth-100"
                :status="percentProcess(group) === 100 ? 'success' : ''"
              />
              <span class="font14 color-gray-7 minW82 ml12 ellipsis">
                {{ $t('pc_eval_lbl_leftEvaluate', [group.processNumber]).d('0人待评估') }}
              </span>
            </div>

            <div class="title-right minW100 text-right">
              <!-- 自评-立即评估 -->
              <btngroup
                v-if="group.type === 'self'"
                :val="group"
                :detail="detail"
                @selectModel="selectModel"
                @goEvaluate="goEvaluate"
              />

              <!-- 单次行为-批量评估 -->
              <btngroup
                v-else-if="detail.evaluationType === 1"
                :val="group"
                :detail="detail"
                :multi-btn="true"
                :multi-disabled="disableBatchEvaluate(group)"
                @selectModel="selectModel"
                @goEvaluate="goEvaluate"
              />

              <!-- 多次行为-批量评估-多次行为评估不支持批量评估 -->
              <yxt-tooltip v-else-if="detail.evaluationType === 4" :content="$t('pc_eval_tip_multiple_behavior_not_support_batch_evaluate')" placement="top">
                <yxtf-button :disabled="true" type="primary-text">
                  {{ $t('pc_eval_lbl_batchEvaluate').d('批量评估') }}
                </yxtf-button>
              </yxt-tooltip>
            </div>
          </div>
        </template>
        <!-- 展开的内容项, 不是自评才会有展开按钮 -->
        <template v-if="group.type !== 'self'">
          <div v-for="(person, pIndex) in group.userList" :key="pIndex" class="lh50 border-top-gray pl48 straight-line bg-light">
            <div class="flex-row-between align-items-center">
              <div>
                <yxtf-portrait
                  class="pos-relative top4"
                  size="20px"
                  :img-url="person.imgUrl"
                  :username="person.fullName"
                />
                <span class="ml8 color-gray-10">
                  <yxtbiz-user-name :name="person.fullName" />
                </span>
                <span class="ml8 mr8">({{ person.userName }})</span>
                <yxtf-svg
                  v-show="person.finished === 1"
                  width="12px"
                  height="12px"
                  icon-class="icons/f_feedback-success"
                />
              </div>
              <div class="right-box mr4">
                <btngroup
                  :val="btnChildVal(group, person)"
                  :detail="detail"
                  @selectModel="selectModel"
                  @goEvaluate="goEvaluate"
                />
              </div>
            </div>
          </div>
        </template>
      </yxt-collapse-item>
    </yxt-collapse>
    <yxtf-dialog
      :title="$t('pc_gwnl_global_pleaseSelect')"
      :visible.sync="dialogShow"
      :before-close="handleClose"
      destroy-on-close
      :modal-append-to-body="false"
    >
      <div>
        <div
          v-for="model in modelList"
          :key="model.modelId"
          class="model_dialog mb12"
        >
          <h1 class="mb12 title">
            {{ model.modelName }}
          </h1>
          <div class="model_item">
            <div class="item_left">
              <template
                v-for="(user, i) in model.userList"
              >
                <yxtf-portrait
                  v-if="i < 3"
                  :key="user.id"
                  :img-url="user.imgUrl"
                  :class="'img' + i"
                  class="aimage"
                  size="small"
                  :username="user.fullName"
                />
              </template>
              <div class="img_box ml8">
                {{ $t('pc_gwnl_web_toBeEvaluated') }}：{{ model.userNum }}{{ $t('pc_h5_eval_web_person') }}
              </div>
            </div>
            <div class="item_right">
              <yxtf-progress :percentage="parseInt(model.finishedRate * 100)" />
              <div class="ml24">
                <btngroup
                  :val="model"
                  :multi-btn="true"
                  :detail="detail"
                  @selectModel="selectModel"
                  @goEvaluate="goEvaluate"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </yxtf-dialog>
  </div>
</template>

<script>
import btngroup from './btngroup.vue';
export default {
  components: {
    btngroup
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  inject: {
    inTimeRange: {
      from: 'inTimeRange',
      default: false
    }
  },
  data() {
    return {
      dialogShow: false,
      dialogPaddingEmpty: false,
      behaviorDetail: [],
      modelList: [],
      relationOption: {
        1: {
          typeName: 'self',
          openiselect: this.handleEmitISelect
        },
        2: {
          typeName: 'upLevel'
        },
        3: {
          typeName: 'sameLevel'
        },
        4: {
          typeName: 'downLevel'
        }
      }
    };
  },
  mounted() {
    this.evalDimension(this.detail);
  },
  methods: {
    selectModel(mode) {
      this.modelList = mode;
      this.dialogShow = true;
    },

    getRelationName(relationType) {
      // 评价自己
      if (relationType === '1') return this.$t('pc_h5_eval_evaluate_self');

      const { dimensionInfoBeans } = this.detail;
      const dimension = (dimensionInfoBeans || []).find(item => item.type === relationType);
      const name = dimension ? dimension.dimensionName : '';
      return this.$t('pc_eval_lbl_evaluation_role', [name]);
    },

    evalDimension(detail) {
      this.behaviorDetail = Object.entries(detail.userRelationMap || {}).map(([relationType, task]) => {
        return {
          ...task[0],
          title: this.getRelationName(relationType),
          processNumber: task[0].userList.filter(item => item.commitStatus !== 2).length,
          type: (this.relationOption[relationType] && this.relationOption[relationType].typeName) || `${relationType}Leval`
        };
      });
    },
    tagStatus(tagV) {
      const tagObj = [
        { tagname: 'pc_gwnl_global_notStarted', tagtype: 'info' },
        { tagname: 'pc_gwnl_global_processing', tagtype: 'primary' },
        { tagname: 'pc_gwnl_global_completed', tagtype: 'success' }
      ];
      // 已完成 添加人 列表状态
      if (tagV.finishedRate > 0 && tagV.finishedRate < 1) {
        return tagObj[1];
      }
      // 未开始
      if (tagV.commitStatus === 0) {
        return tagObj[0];
      }
      // 进行中
      if (tagV.commitStatus === 1 && tagV.finished === 0) {
        return tagObj[1];
      }
      // 已完成
      if (tagV.finished === 1 || tagV.finished === -1) {
        return tagObj[2];
      }
      return tagObj[0];
    },
    percentProcess(group) {
      return Math.round(((group.userList.length - group.processNumber) / group.userList.length) * 100);
    },
    disableBatchEvaluate(group) {
      // 启用条件：项目进行中&有员工未完成&在起止时间范围内
      return !((!this.detail.finished && this.detail.evaluationStatus !== 2) && group.processNumber && this.inTimeRange(this.detail));
    },
    handleClose() {
      this.dialogShow = false;
    },

    // 控制传递子组件按钮的 val
    btnChildVal(group, person) {
      return {
        ...group,
        relationIds: [person.id],
        finished: person.finished,
        commitStatus: person.commitStatus
      };
    },

    getAllRelationSuccess() {
      return this.behaviorDetail.every(group => {
        return Math.round(((group.userList.length - group.processNumber) / group.userList.length) * 100) === 100;
      });
    },
    goEvaluate(data) {
      this.$emit('goEvaluate', data);
    }
  }
};
</script>

<style lang="scss" scoped>
.behavior_page {
  width: 100%;

  .behavior_item {
    display: flex;
    justify-content: space-between;
    padding: 14px 16px 10px 20px;
    background: #fafafa;
    border-radius: 8px;
  }

  .item_left {
    display: flex;
    align-items: center;

    h2 {
      color: #262626;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }

    .img_box {
      display: flex;
      align-items: center;
      color: #8c8c8c;
      font-size: 12px;

      .aimage {
        border: 2px solid #fff;
      }

      .img1 {
        margin-left: -12px;
      }

      .img2 {
        margin-left: -12px;
      }
    }
  }

  .item_right {
    display: flex;
    align-items: center;

    .mode_brief {
      color: #8c8c8c;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
    }
  }

  .model_dialog {
    padding: 24px;
    background: #fafafa;

    .title {
      display: -webkit-box;
      overflow: hidden;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      //text-overflow: -o-ellipsis-lastline;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
    }

    .model_item {
      display: flex;
      justify-content: space-between;
    }
  }
}

.wrap {
  width: 100%;

  .self_ui_wrap {
    width: 100%;
  }

  .flex-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    background: #fff;
    box-shadow: inset 0 -1px 0 0 #e9e9e9;

    .left-title {
      margin-left: 32px;
      color: #262626;
      font-weight: 500;
      font-size: 14px;
    }

    .left-name {
      margin-left: 48px;
      color: #262626;
      font-weight: 400;
      font-size: 14px;
    }

    .right-box {
      margin-right: 4px;
    }
  }
}

.collapse-demo-custom-title {
  display: flex;
  justify-content: space-between;
  width: calc(100% - 44px);
  padding-right: 4px;
}

.collapse-others {
  ::v-deep .yxt-collapse-item__content {
    padding: 0;
  }
}
</style>
