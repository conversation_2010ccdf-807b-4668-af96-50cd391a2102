<template>
  <div class="btn_box">
    <template v-if="multiBtn">
      <span>
        <yxtf-button
          :size="size"
          :disabled="multiDisabled"
          type="primary-text"
          @click="clickbtn({...val,multiple: '1'})"
        >
          {{ $t('pc_eval_lbl_batchEvaluate').d('批量评估') }}
        </yxtf-button>
      </span>
    </template>
    <template v-else>
      <yxtf-button
        v-if="continueEval(val)"
        :type="btnType ? 'primary' : 'primary-text'"
        size="medium"
        @click="clickbtn(val)"
      >
        {{ $t('pc_eval_ask_btn_continue') }}
      </yxtf-button>
      <yxtf-button
        v-else-if="startEval(val)"
        :disabled="timeStatus === 2"
        :type="btnType ? 'primary' : 'primary-text'"
        size="medium"
        @click="clickbtn(val, 1)"
      >
        {{ $t('pc_eval_ask_btn_now_stat') }}
      </yxtf-button>
      <yxtf-button
        v-else-if="expired(val)"
        disabled
        :type="btnType ? 'info' : 'primary-text'"
        size="medium"
      >
        {{ $t('pc_gwnl_web_lbl_survey_btn_outdated') }}
      </yxtf-button>
      <yxtf-button
        v-else-if="judGment(val)"
        disabled
        :type="btnType ? '' : 'primary-text'"
        size="medium"
      >
        {{ $t('pc_h5_eval_web_judgment') }}
      </yxtf-button>
      <yxtf-button
        v-else-if="viewSheet(val)"
        :type="btnType ? '' : 'primary-text'"
        :disabled="val.finishedRate === 0"
        size="medium"
        @click="clickbtn(val, 2)"
      >
        {{ $t('pc_gwnl_web_lbl_survey_btn_view') }}
      </yxtf-button>
    </template>
  </div>
</template>

<script>
import dayjs from 'dayjs';

const COMMIT_STATUS = {
  noStart: 0,
  started: 1,
  completed: 2
};

export default {
  props: {
    val: {
      type: Object,
      default: () => {}
    },
    detail: {
      type: Object,
      default: () => {}
    },
    btnType: {
      type: Boolean,
      default: false
    },
    multiBtn: {
      type: Boolean,
      default: false
    },
    multiDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      surveyDetail: {},
      timeStatus: 1,
      offset: 0,
      COMMIT_STATUS
    };
  },
  computed: {
    // 开始测评
    // 继续测评
    // 已过期
    // 查看答卷
    // 正在分发中
    startEval() {
      return (val) => {
        return this.surveyDetail.evaluationStatus === 1 && !val.finished && (this.timeStatus === 1 || this.timeStatus === 2);
      };
    },
    continueEval() {
      return (val) => {
        return this.surveyDetail.evaluationStatus === 1 && !val.finished && this.timeStatus === 1 && val.commitStatus === COMMIT_STATUS.started;
      };
    },
    expired() {
      return (val) => {
        return !val.finished && ((this.surveyDetail.evaluationStatus === 1 && this.timeStatus === 3) || this.surveyDetail.evaluationStatus === 2);
      };
    },
    viewSheet() {
      return (val) => {
        return val.finished;
      };
    },
    judGment() {
      return (val) => {
        return val.finished === -1;
      };
    },
    // 是否是培训的页面
    isTrainPage() {
      return this.$route.name === 'web.assess.train';
    }
  },
  mounted() {
    this.surveyDetail = this.detail;
    this.timeStatus = this.getTimeStatus(this.detail);
  },
  methods: {
    async clickbtn(val, type) {
      let { modelId, relationType } = val;
      const { evaluationId } = this.detail;
      // 多岗多模型-多次行为评估不含
      if (val.modelList && val.modelList.length > 1) {
        val.modelList.forEach(item => {
          item.relationType = val.relationType;
          item.quesNum = item.skillNum;
        });
        // 矩阵评估
        if (this.multiBtn) {
          this.$emit('selectModel', val.modelList);
        } else {
          const relationIdInfo = val.relationIds[0];
          const modelListInfo = val.modelList.map(item => item.relationIds);
          let infoNum = 0;
          modelListInfo.forEach((info, index) => {
            if (info.includes(relationIdInfo)) {
              infoNum = index;
            }
          });
          modelId = val.modelList[infoNum].modelId;
          const data = {
            relationIds: val.relationIds,
            modelId
          };
          if (type !== 2) {
            this.$http.web.postAnswerUse(data).then(res => {});
            await this.$http.web.getOffsetPlan(data).then(res => {
              this.offset = val.quesNum === res ? res - 1 : res;
            });
          }
          // await this.$router.push({
          //   name: 'web.assess.evaluate',
          //   query: {
          //     evaluationId,
          //     modelId,
          //     relationType,
          //     offset: this.offset,
          //     type,
          //     multiple: val.multiple === '1' ? '1' : '0',
          //     evaluationType: this.detail.evaluationType,
          //     relationIds: val.multiple === '1' ? '' : val.relationIds[0],
          //     isTrain: ~~this.isTrainPage
          //   }
          // });
          this.$emit('goEvaluate', {
            evaluationId,
            modelId,
            relationType,
            offset: this.offset,
            type,
            multiple: val.multiple === '1' ? '1' : '0',
            evaluationType: this.detail.evaluationType,
            relationIds: val.multiple === '1' ? '' : val.relationIds[0],
            isTrain: ~~this.isTrainPage
          });
        }
      } else {
        if (val.modelList && val.modelList.length === 1) {
          modelId = val.modelList[0].modelId;
        }
        const data = {
          relationIds: val.relationIds,
          modelId
        };
        if (type !== 2) {
          // 行为评估开始答题
          this.$http.web.postAnswerUse(data).then(res => {});
          // 行为评估获取答题进度
          await this.$http.web.getOffsetPlan(data).then(res => {
            let offsetQues;
            // 多次行为评估
            if (this.detail.evaluationType === 4) {
              offsetQues = val.userList.filter(item => item.id === val.relationIds[0])[0].quesNum;
            } else {
              offsetQues = val.quesNum;
            }
            this.offset = offsetQues === res ? res - 1 : res;
          });
        }
        // await this.$router.push({
        //   name: 'web.assess.evaluate',
        //   query: {
        //     evaluationId,
        //     modelId,
        //     relationType,
        //     offset: this.offset,
        //     type,
        //     multiple: val.multiple === '1' ? '1' : '0',
        //     evaluationType: this.detail.evaluationType,
        //     relationIds: val.multiple === '1' ? '' : val.relationIds[0],
        //     isTrain: ~~this.isTrainPage
        //   }
        // });
        this.$emit('goEvaluate', {
          evaluationId,
          modelId,
          relationType,
          offset: this.offset,
          type,
          multiple: val.multiple === '1' ? '1' : '0',
          evaluationType: this.detail.evaluationType,
          relationIds: val.multiple === '1' ? '' : val.relationIds[0],
          isTrain: ~~this.isTrainPage
        });
      }
    },
    getTimeStatus(val) {
      // 1进行中 2未开始 3已过期
      let status = 1;
      const { startTime, endTime, currentTime } = val;

      if (!startTime || !endTime || !currentTime) return 1;

      if (dayjs(currentTime).isBefore(startTime, 'time')) status = 2;
      if (dayjs(currentTime).isAfter(endTime, 'time')) status = 3;

      return status;
    }
  }
};
</script>
