<template>
  <div v-loading="loading" class="survey__description" :class="{'h-100vh-impt': isShowErrorPage}">
    <div v-if="!loading" class="survey__description-container hline">
      <ErrorPage
        v-if="isShowErrorPage"
        :error-message="evalDetail.status === 0 ? $t('pc_eval_lbl_eval_not_publish' /* 测评未发布 */) : $t('pc_eval_tips_quota_over_time').d('测评可用次数不足，请联系项目负责人或平台管理员。')"
        :is-no-per="!quotaEnabled"
        :is-empty="evalDetail.status === 0"
        @customClick="returnList"
      />

      <template v-else>
        <div
          class="survey__description-content"
        >
          <EvalHeader
            :title="baseData.evaluationName"
            :start-time="baseData.startTime"
            :end-time="baseData.endTime"
          />
          <SurveyInfo
            v-if="isOnlySelf && baseData.evaluationType === 2"
            :questions-count="baseData.questionCount"
            :time-limt="selfTimeLimit"
          />
          <ReportList
            v-if="baseData.reports && baseData.reports.length"
            :base-data="baseData"
            :reports="baseData && baseData.reports"
          />
          <Hint
            v-else
            :content="baseData.description"
            :top="isOnlySelf && baseData.evaluationType === 2 ? '40px' : '60px'"
          />
          <!-- 行为评估使用新组件，1代表单次评估，4代表多次评估 -->
          <taskBehaviorNew
            v-if="baseData.evaluationType === 1 || baseData.evaluationType === 4"
            ref="taskList"
            :detail="baseData"
            @goEvaluate="goEvaluate"
          />
          <TaskList
            v-else
            ref="taskList"
            :is-only-self="isOnlySelf"
            :task="baseData.userRelationMap"
            :base-data="baseData"
            @openiselect="handleOpenISelect"
          />
        </div>
      </template>
    </div>
    <!-- 善泽提示 -->
    <eval-dialog
      ref="evalDialog"
      :info="info"
      :is-view="isView"
      :self-info="selfInfo"
    />
  </div>
</template>

<script>
import { SURVEY_STATUS } from '../enum';
import { checkOpenPlatform } from '../utils';
import evalDialog from '../components/evalDialog.vue';
import Hint from './components/hint.vue';
import TaskList from './components/taskList.vue';
import ReportList from './components/reportList.vue';
import SurveyInfo from './components_self/surveyInfo.vue';
import taskBehaviorNew from './components_behavior/taskBehaviorNew.vue';
import ErrorPage from '../components/error-page.vue';
import Header from './components/header.vue';
import dayjs from 'dayjs';
import {
  getEvalDetail,
  getEvaluationsPersonalitySelfInfo,
  postBehaviorAnswerList
} from 'yxt-ulcd-sdk/packages/evaluation/src/service/eval.service';
import { Message } from 'yxt-pc';
export default {
  name: 'TaskDetail',
  components: {
    'EvalHeader': Header,
    evalDialog,
    Hint,
    TaskList,
    ReportList,
    SurveyInfo,
    taskBehaviorNew,
    ErrorPage
  },
  provide() {
    return {
      inTimeRange: () => this.inTimeRange,
      isBeforeTime: () => this.isBeforeTime,
      isAfterTime: () => this.isAfterTime,
      isOnlySelf: () => this.isOnlySelf
    };
  },
  props: {
    baseData: {
      type: Object,
      default: ({
        userRelationMap: {}
      })
    }
  },
  data() {
    return {
      hint: '',
      isStart: false,
      dialogVisible: false,
      isOpenEnv: false,
      isView: 0,
      info: {},
      selfInfo: {},
      loading: false,
      surveyStatus: SURVEY_STATUS,
      timer: null,
      value: '',
      detail: {},
      behaviorType: false,
      isFromAnswerPage: false, // 是否从答题页过来
      quotaEnabled: true,
      evalDetail: {} // 测评详情
    };
  },
  computed: {
    isShowErrorPage() {
      return this.evalDetail.status === 0 || !this.quotaEnabled;
    },
    isOnlySelf() {
      const userRelationMap = this.baseData.userRelationMap;
      if (!userRelationMap) return 'peding';

      const otherEval = Object.keys(userRelationMap).filter((item) => item !== '1');
      if (otherEval.length > 0) {
        return false;
      }
      return userRelationMap['1'] ? true : 'peding';
    },
    selfTimeLimit() {
      if (!this.isOnlySelf) return '';
      // 加兜底判断不然会一直loading
      return Object.keys(this.baseData.userRelationMap).length !== 0 ? this.baseData.userRelationMap['1'][0].timeLimit : 0;
    },
    inTimeRange() {
      const { startTime, endTime, currentTime } = this.baseData;

      if (!startTime || !endTime || !currentTime) return '';

      const a = dayjs(currentTime).isAfter(startTime, 'time');
      const b = dayjs(currentTime).isBefore(endTime, 'time');

      if (a && b) {
        return true;
      }

      return false;
    },
    isBeforeTime() {
      const { startTime, endTime, currentTime } = this.baseData;

      if (!startTime || !endTime || !currentTime) return '';

      const a = dayjs(currentTime).isBefore(startTime, 'time');

      if (a) {
        return true;
      }

      return false;
    },
    isAfterTime() {
      const { startTime, endTime, currentTime } = this.baseData;

      if (!startTime || !endTime || !currentTime) return '';

      const a = dayjs(currentTime).isAfter(endTime, 'time');

      if (a) {
        return true;
      }

      return false;
    }

    // 是否是培训的页面 TODO
    // isTrainPage() {
    //   return this.$route.name === 'web.assess.train';
    // }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (['web.assess.evaluate', 'web.assess.multi', 'web.assess.survey'].includes(from.name)) {
        vm.isFromAnswerPage = true;
      }
    });
  },

  created() {
    console.log('task detail enter');
    this.isOpenEnv = checkOpenPlatform();
    this.getData();
  },
  methods: {
    goAskSurvey(data) {
      this.$emit('goAskSurvey', data);
    },
    goEvaluate(data) {
      this.$emit('goEvaluate', data);
    },
    async getData() {
      this.loading = true;
      const { evaluationId } = this.baseData;
      await getEvalDetail({ evaluationId }).then(res => {
        this.evalDetail = res;
        this.behaviorType = (res.type === 1 || res.type === 4) && !res.targetId;
      });
      if (this.behaviorType) {
        await postBehaviorAnswerList({ evalId: evaluationId }).then(res => {
          this.loading = false;
          this.baseData = res;
        }).catch((err) => {
          this.loading = false;
          if (err.error.key === 'api.eval.behavior.no.modelId.error') {
            this.$confirm(this.$t('pc_h5_eval_web_no_qualification_configured'), this.$t('pc_gwnl_tips'), {
              type: 'warning',
              showCancelButton: false,
              confirmButtonText: this.$t('pc_gwnl_global_msg_known')
            }).then(() => {
              const href = '/spgwnl/#/web/eval';
              window.location.href = href;
            });
            return;
          }
          this.$handleError(err);
        }).finally(() => {
          this.loading = false;
        });
      } else {
        // 说明未占用份额
        if ('quotaEnabled' in this.baseData && ~~this.baseData.quotaEnabled === 0) {
          this.quotaEnabled = false;
          return;
        }

        this.baseData.userRelationMap = this.baseData.userRelationMap || {};
        // fix:stnl-2572 学员端，答题起始页，试题总数不显示
        if (Object.keys(this.baseData.userRelationMap).length !== 0) {
          this.baseData.questionCount = this.baseData.userRelationMap['1'] && this.baseData.userRelationMap['1'][0].questionCount;
        }
        this.loading = false;
      }

      // 完成所有评估发送消息给项目，从答题页过来，让它刷新对应页面，并且关闭页面
      this.$nextTick(() => {
        const isAllSuccess = this.$refs.taskList && this.$refs.taskList.getAllRelationSuccess();
        if (isAllSuccess && this.isFromAnswerPage && this.isTrainPage && window.opener) {
          window.opener.postMessage({ message: 'completed' });
          setTimeout(() => { window.close(); }, 1000);
        }
      });
    },
    async handleOpenISelect(data, action) { // action: goAskSurvey|goActionEval
      if (action === 'goAskSurvey') {
        this.goAskSurvey(data);
        return;
      }
      if (action === 'goEvaluate') {
        this.goEvaluate(data);
        return;
      }
      if (action === 'goMulti') {
        this.$emit('goMulti', data);
      }
      const { evaluationName, evaluationId } = this.baseData;
      const { evaluationUrl, fullname } = data;

      this.info = {
        ...this.info,
        evaluationName,
        evaluationId,
        userName: fullname,
        evaluationUrl
      };

      await getEvaluationsPersonalitySelfInfo().then((res) => {
        this.selfInfo = res;
      }).catch((err) => {
        this.$handleError(err);
      });

      this.$refs.evalDialog && this.$refs.evalDialog.show();
    },
    evalReturn() {
      let weburl = this.$route.query.weburl;
      if (weburl) {
        weburl = weburl.indexOf('http') === -1 ? `https://${weburl}` : weburl;
        window.location.href = decodeURIComponent(weburl);
      } else {
        const href = '/spgwnl/#/web/eval';
        window.location.href = href;
      }
    },

    // 回到测评列表
    returnList() {
      location.href = `${location.origin}/spgwnl/#/web/eval`;
    },
    // 处理普通接口报错
    $handleError(error) {
      if (error && error.error) {
        // 兼容处理
        error = error.error;
      }
      if (error) {
        if (error.key && error.key.indexOf('global.token') >= 0) {
          return; // 不弹出token错误
        }
        const msg = error.message;
        if (typeof (msg) !== 'string') return;
        Message({
          message: msg,
          type: 'error'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.survey__description {
  height: 100%;
  background: url("https://stc.yxt.com/ufd/b0174a/evaluation/pc/img/questionnaire-bg.png") no-repeat top center;
  background-color: #fdfdfd;
  background-size: 1920px auto;
}

.layer-content {
  padding: 20px 32px;
  text-align: center;

  div {
    margin-bottom: 20px;
    color: #262626;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }

  p {
    margin-bottom: 50px;
    color: #262626;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;

    span {
      color: #ff8c43;
    }
  }
}

.survey__description-container {
  //width: 1200px;
  //min-width: 1200px;
  margin: 0 auto;
}

.survey__description-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  //width: 1200px;
  min-height: calc(100vh - 70px);
  padding: 46px 210px 56px;
  background: #fff;

  .word-break {
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.web_cont_btn {
  display: flex;
  margin-top: 40px;
}

.footer {
  margin-top: 64px;
}
</style>
