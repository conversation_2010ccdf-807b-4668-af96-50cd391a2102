<template>
  <div>
    <div class="">
      <div v-if="question.quStyle==='A03.01.04'" class="ml150 mr20">
        <div ref="scaleTextList" class="flex over-hidden">
          <div v-for="(title,tIndex) in question.prjQuOptions" :key="tIndex" class="mr6 flex-space-between flex-1">
            <div class="color-gray-9 lh22">
              {{ title.optionTip || $t('pc_eval_input_text').d('请输入描述文字') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      ref="scaleContent"
      :class="{'over-scroll': question.quStyle === 'A03.01.04'}"
      :style="{'height': scaleHeight}"
    >
      <div v-for="(person, pIndex) in question.answerBackViewUsers" :key="pIndex">
        <div class="mb24 mt14 pos-relative" :class="[question.quStyle === 'A03.01.04' ? 'flex-row-end' : 'flex-row-center']">
          <!-- 学员资料 -->
          <profile
            :data="person"
            :choose-tip="chooseTip"
            :class="{'mt20': pIndex === 0, 'mb4': question.quStyle === 'A03.01.04'}"
          />
          <div>
            <div v-if="question.quStyle!=='A03.01.04' && pIndex===0 && question.quTip" class="gwnl-flex justify-content-between mb30 color-gray-9 standard-size-14">
              <span class="tip-wrapper">{{ question.quTip.split(';')[0] }}</span>
              <span class="tip-wrapper">{{ question.quTip.split(';')[1] }}</span>
            </div>
            <component
              :is="question.quStyle | scaleType"
              :question="question"
              :person="person"
              :p-index="pIndex"
              :disabled="!!person.finished"
              :changed="change"
              :class="['w570', pIndex === 0 ? 'mb12' : 'mb0']"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import starScale from './starRate';
import slideScale from './sliderRate';
import NPSScale from './rateNps';
import rateRadio from './rateRadio';
import profile from './components/profile.vue';

const NPS_SCALE = 'A01.09.01';
const STAR_SCALE = 'A01.09.02';
const SLIDE_SCALE = 'A01.09.07';
const RADIO_SCALE = 'A03.01.04';
const SCALEMAP = {
  [NPS_SCALE]: 'NPSScale',
  [STAR_SCALE]: 'starScale',
  [SLIDE_SCALE]: 'slideScale',
  [RADIO_SCALE]: 'rateRadio'
};
export default {
  components: {
    NPSScale,
    starScale,
    slideScale,
    rateRadio,
    profile
  },
  filters: {
    scaleType(type) {
      return SCALEMAP[type];
    }
  },
  data() {
    return {
      titleShow: true,
      hasPassed: 0,
      scaleHeight: ''
    };
  },
  props: {
    question: Object,
    disabled: Boolean,
    chooseTip: {
      type: Boolean,
      default: false
    },
    changed: {
      type: Function,
      default: () => {}
    }
  },

  methods: {
    change() {
      this.changed(...arguments);
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.question.quStyle === 'A03.01.04') {
        const originH = this.$refs.scaleContent.scrollHeight;
        if (originH > 480) {
          this.scaleHeight = '480px';
        }
      }
    });
  }
};

</script>

<style lang="scss" scoped>
.rate-content {
  width: 100%;
}
</style>
