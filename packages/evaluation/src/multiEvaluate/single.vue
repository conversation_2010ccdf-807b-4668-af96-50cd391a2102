<template>
  <div>
    <!-- 选项展示 -->
    <div class="mb8">
      <ul>
        <li v-for="(item,index) in question.prjQuOptions" :key="index" class="flex lh34">
          <span>{{ convertASCIIForNum(index) }}、</span>
          <span
            v-dompurify-html="item.optionTitleFull"
            class="question-item-label"
          ></span>
        </li>
      </ul>
    </div>

    <!-- 学员答题 -->
    <div v-for="(person,pId) in question.answerBackViewUsers" :key="pId">
      <div class="flex-row-center mb24 pos-relative">
        <!-- 学员资料 -->
        <profile :data="person" :choose-tip="chooseTip" />
        <!-- 答题区域 -->
        <yxtf-radio-group
          v-model="person.optionId"
          direction="column"
          :disabled="person.finished"
          @change="handleClick"
        >
          <yxtf-radio
            v-for="(option,index) in question.prjQuOptions"
            :key="option.id"
            :label="option.id"
            class="minW36"
            :class="{ 'mb12': question.prjQuOptions.length > 8 }"
          >
            {{ convertASCIIForNum(index) }}
          </yxtf-radio>
        </yxtf-radio-group>
      </div>
    </div>
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
import profile from './components/profile.vue';
import { convertASCIIForNum } from '../utils';

export default {
  mixins: [Emmiter],
  components: { profile },
  props: {
    question: Object,
    disabled: Boolean,
    chooseTip: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },

  methods: {
    convertASCIIForNum,
    handleClick(pid) {
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.question-item-label {
  display: inline-block;
  word-wrap: break-word;
}
</style>
