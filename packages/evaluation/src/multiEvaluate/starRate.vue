<template>
  <div class="gwnl-flex justify-content-between">
    <template v-for="(item, index) in question.prjQuOptions">
      <scale-option-tips-tooltip
        :key="index"
        :disabled="!item.optionTip"
        :value="index === activeindex"
        popper-class="gwnl-square-item-tips"
        effect="dark"
        max-width="500"
        :options="question.prjQuOptions"
        :content="item.optionTip"
        :index="index"
        :manual="true"
        :active-index="activeindex"
      >
        <yxt-svg
          :key="index"
          width="32px"
          height="32px"
          :disabled="disabled"
          :remote-url="'https://stc.yxt.com/ufd/b0174a/evaluation/pc/svg/eval'"
          icon-class="star"
          class="color-gray-4 hand"
          :class="{
            stratDisable: disabled,
            'a-link': hoverIndex >= index,
            'color-gray-4':
              hoverOption.optionNum === undefined ||
              hoverOption.optionNum < index
          }"
          @mouseenter.native="optionEnter(item)"
          @click.native="select(item, index)"
        />
      </scale-option-tips-tooltip>
    </template>
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
import ScaleOptionTipsTooltip from '../components/scaleOptionTipsTooltip';
export default {
  components: {
    ScaleOptionTipsTooltip
  },
  mixins: [Emmiter],
  props: {
    question: Object,
    person: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      changeindex: this.person.optionId ? 1 : 0,
      selectedIndex: '',
      activeindex: '',
      value: null,
      hoverOption: {}
    };
  },
  computed: {
    hoverIndex() {
      return this.question.prjQuOptions.findIndex(
        (index) => index === this.selectedIndex
      );
    }
  },
  watch: {
    person: {
      immediate: true,
      deep: true,
      handler: function(newVal) {
        const matched = this.question.prjQuOptions.findIndex(
          (option) => option.id === newVal.optionId
        );
        this.activeindex = matched > -1 ? matched : null;
      }
    }
  },
  mounted() {
    this.getSelected();
  },

  methods: {
    getSelected() {
      console.log(this.activeindex);
      if (!this.activeindex && this.activeindex !== 0) return;
      this.hoverOption = this.question.prjQuOptions.find(
        (item) => item.id === this.person.optionId
      );
      this.selectedIndex = this.hoverOption;
      console.log(this.hoverOption);
      console.log(this.selectedIndex);
    },
    select(item, index) {
      if (this.disabled) return;
      this.selectedIndex = item;
      this.activeindex = index;
      this.change(index + 1);
    },
    optionEnter(item) {
      if (this.hasSelected || this.disabled) return;
      this.hoverOption = item;
    },
    getOptionIdByValue(value) {
      return this.question.prjQuOptions[value - 1].id;
    },
    change(val) {
      this.person.optionId = this.getOptionIdByValue(val);
      this.handleClick(this.getOptionIdByValue(val));
    },
    handleClick(pid) {
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.star-rate {
  ::v-deep .yxt-rate__icon {
    font-size: 25px;
  }
}

.stratDisable {
  cursor: not-allowed;
  pointer-events: auto;
}
</style>
