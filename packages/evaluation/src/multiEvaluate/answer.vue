<template>
  <div class="answer-item">
    <div v-for="(person,pId) in question.answerBackViewUsers" :key="pId" class="mb16 mt14">
      <div class="align-items-start">
        <profile :data="person" :choose-tip="chooseTip" />
        <yxt-input
          v-model="person.optionId"
          type="textarea"
          :placeholder="$t('pc_gwnl_web_pleaseEnter')"
          maxlength="2000"
          show-word-limit
          :disabled="person.finished"
          class="gwnl-input-none-border ml14"
          @blur="handleClick(person)"
        />
      </div>
    </div>
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
import profile from './components/profile.vue';
export default {
  mixins: [Emmiter],
  components: { profile },
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    },
    chooseTip: Boolean
  },

  methods: {
    handleClick(person) {
      person.optionId = person.optionId.trim();
      this.changed(this.question.userOptionId, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.blur');
    }
  }
};
</script>
<style lang="scss" scoped>
.question-item-label {
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
}

.answer-item {
  width: 100%;
}
</style>
