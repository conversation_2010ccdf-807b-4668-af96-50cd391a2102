<template>
  <div class="gwnl-flex justify-content-between mb12">
    <template v-for="(item, index) in question.prjQuOptions">
      <scale-option-tips-tooltip
        :key="index"
        :disabled="!item.optionTip"
        :value="index === activeindex"
        popper-class="gwnl-square-item-tips"
        effect="dark"
        max-width="500"
        :options="question.prjQuOptions"
        :content="item.optionTip"
        :index="index"
        :manual="true"
        :active-index="activeindex"
      >
        <div
          :key="index"
          class="gwnl-square-item hand"
          :class="{ active: hoverIndex === index, disabled: disabled }"
          @click="select(item, index)"
        >
          {{ index + question.optionStart }}
        </div>
      </scale-option-tips-tooltip>
    </template>
  </div>
</template>

<script>
import Emmiter from '../mixins/emmiter';
import ScaleOptionTipsTooltip from '../components/scaleOptionTipsTooltip';
export default {
  components: {
    ScaleOptionTipsTooltip
  },
  mixins: [Emmiter],
  props: {
    question: Object,
    person: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      hasSelected: false,
      hoverOption: {},
      hasPassed: 0,
      activeindex: ''
    };
  },
  computed: {
    hoverIndex() {
      return this.question.prjQuOptions.findIndex(
        (option) => option.id === this.hoverOption.id
      );
    }
  },

  mounted() {
    this.getSelected();
  },

  methods: {
    getSelected() {
      if (!this.person.optionId) return;
      this.hoverOption = this.question.prjQuOptions.find(
        (item) => item.id === this.person.optionId
      );
      this.activeindex = this.hoverIndex;
      this.hasSelected = true;
    },
    optionEnter(item) {
      if (this.hasSelected || this.disabled) return;
      this.hoverOption = item;
    },
    optionLeave() {
      if (this.hasSelected || this.disabled) return;
      this.hoverOption = {};
    },
    select(item, index) {
      if (this.disabled) return;
      this.activeindex = index;
      this.hasSelected = true;
      this.hoverOption = item;
      this.person.optionId = item.id;
      this.handleClick(item.id);
    },
    handleClick(pid) {
      console.log(pid, this.question.prop);
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.a-link {
  color: var(--color-primary-6) !important;
}

.gwnl-square-item {
  &:hover {
    color: #fff;
    background: var(--color-primary-6);
  }

  /*
  &.disabled:hover {
    color: #595959;
    background-color: #f0f0f0;
  } */
  &.disabled {
    cursor: not-allowed;
  }
}
</style>
