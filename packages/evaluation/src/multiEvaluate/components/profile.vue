<template>
  <div class="align-items-center mr16 minW120">
    <yxtf-portrait
      size="24px"
      class="mr8"
      :img-url="data.imgUrl"
      :username="data.fullname"
    />
    <div>
      <yxtf-tooltip :content="data.fullname" placement="top" :open-filter="true">
        <p class="lh22 maxWidth88 ellipsis">
          <yxtbiz-user-name :name="data.fullname" />
        </p>
      </yxtf-tooltip>
      <p class="lh22 maxWidth88 ellipsis font12 color-gray-7">
        {{ data.username }}
      </p>
      <span v-show="tipEvaluate && !upload" class="pos-absolute lh22 color-red-fa font-size-12">
        {{ $t('pc_eval_tip_personEvaluate').d('请对员工进行评价') }}
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    chooseTip: {
      type: Boolean,
      default: false
    },
    data: Object,
    upload: {
      type: Boolean,
      default: false
    }
  },

  computed: {
    tipEvaluate: function() {
      return !!((this.chooseTip && (!this.data.optionId || this.data.optionId.length === 0)));
    }
  }
};
</script>
