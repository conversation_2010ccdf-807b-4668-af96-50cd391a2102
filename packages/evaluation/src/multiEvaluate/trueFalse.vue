<template>
  <div>
    <!-- 学员资料 -->
    <div v-for="(person,pId) in question.answerBackViewUsers" :key="pId">
      <div class="flex-row-center pos-relative mb24 mt6">
        <profile :data="person" :choose-tip="chooseTip" />
        <!-- 答题区域 -->
        <yxtf-radio-group
          v-model="person.optionId"
          direction="column"
          :disabled="person.finished"
          @change="handleClick"
        >
          <yxtf-radio
            v-for="option in question.prjQuOptions"
            :key="option.id"
            :label="option.id"
          >
            <span
              v-dompurify-html="option.optionTitleFull"
              class="inline-block wordbreak"
            ></span>
          </yxtf-radio>
        </yxtf-radio-group>
      </div>
    </div>
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
import profile from './components/profile.vue';

export default {
  mixins: [Emmiter],
  components: { profile },
  props: {
    question: Object,
    disabled: Boolean,
    chooseTip: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },

  methods: {
    handleClick(pid) {
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
