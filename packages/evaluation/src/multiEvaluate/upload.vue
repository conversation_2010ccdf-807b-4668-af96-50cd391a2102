<template>
  <div>
    <!-- 学员答题 -->
    <div v-for="(person,pId) in question.answerBackViewUsers" :key="pId">
      <div class="mb24 pos-relative">
        <!-- 学员资料 -->
        <profile :data="person" :choose-tip="chooseTip" upload />
        <div class="mt8">
          <!-- 答题区域 -->
          <upload-single
            :ref="`answer${person.relationId}`"
            :attach-list="person.uploadAnswer || []"
            :is-view-mode="disabled || person.finished"
            is-multiple
            @setAttachList="setAttachList($event, person)"
          />
        </div>

        <span v-show="showErrorTips(person)" class="pos-absolute lh22 color-red-fa font-size-12">
          {{ $t('pc_eval_msg_upload_required_materials').d('请上传相应材料') }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
import profile from './components/profile.vue';
import UploadSingle from '../components/upload';

export default {
  mixins: [Emmiter],
  components: { profile, UploadSingle },
  props: {
    question: Object,
    disabled: Boolean,
    chooseTip: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },

  methods: {
    setAttachList(attachList, person) {
      this.$set(person, 'uploadAnswer', attachList);
      // 更新form-item校验
      this.changed(attachList, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    },

    showErrorTips(person) {
      return this.chooseTip && !person.uploadAnswer.length;
    },

    checkUploadQuesIsUploading() {
      const isUploadingList = [];
      const isErrorList = [];
      this.question.answerBackViewUsers.forEach(person => {
        const perRelationRef = this.$refs[`answer${person.relationId}`][0];
        const fileLoadingList = perRelationRef.progressIds;
        isUploadingList.push(!!fileLoadingList.length);
        const errList = perRelationRef.errorList;
        isErrorList.push(!!errList.length);
      });

      return {
        uploadingList: isUploadingList.some(item => item),
        errList: isErrorList.some(item => item)
      };
    }
  }
};
</script>
<style lang="scss" scoped>
.question-item-label {
  display: inline-block;
  word-wrap: break-word;
}
</style>
