<template>
  <div class="gwnl-questionnaire-slider-bar-box">
    <div class="gwnl-flex justify-content-between mb12">
      <div v-for="(title,tIndex) in question.prjQuOptions" :key="tIndex" class="flex-space-between flex-1 mr8">
        <div class="">
          <yxt-radio-group v-model="person.optionId" @change="change(value)">
            <yxt-radio :label="title.id" :disabled="disabled" hide-label />
          </yxt-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Emmiter from '../mixins/emmiter';
export default {

  mixins: [Emmiter],
  props: {
    question: Object,
    person: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    },
    pIndex: Number
  },
  data() {
    return {
      value: null
    };
  },
  watch: {
    value: function(val) {
      this.person.optionId = this.getOptionIdByValue(val);
    }
  },
  mounted() {
    if (this.person.optionId) {
      const matched = this.question.prjQuOptions.find(
        (option) => option.id === this.person.optionId
      );
      this.value = matched.optionNum + this.question.optionStart;
      console.log(this.value);
    }
  },
  methods: {
    getOptionIdByValue(value) {
      value = value - this.question.optionStart;
      const matched = this.question.prjQuOptions.find(
        (option) => option.optionNum === value
      );
      return matched.id;
    },
    change(value) {
      this.value = value;
      this.changed(this.getOptionIdByValue(value), this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.gwnl-questionnaire-slider-bar-box {
  position: relative;

  // width: 100%;
  padding: 0 15px;

  .tips-box {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    width: 100%;

    .gwnl-questionnaire-slider-bar-item {
      position: relative;
      top: 0;
      z-index: -1;
      width: 30px;
      height: 5px;
      background: red;
      opacity: 0;
    }
  }
}
</style>
