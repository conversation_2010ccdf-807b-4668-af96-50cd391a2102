<template>
  <div class="yxtulcdsdk-evaluation-questionnaire">
    <div class="custom-sticky-header" ref="stickyHeader">
      <div class="yxtulcdsdk-exam-header">
        <div
          class="flex flex-mid hand hover-primary-6"
          @click="backToPreview()"
        >
          <yxt-svg icon-class="quit" width="16px" height="16px" />
          <span class="ml8">{{ $t("pc_ote_btn_exit" /** 退出 */) }}</span>
        </div>
      </div>
    </div>
    <ErrorPage
      v-if="isDelEval"
      :error-message="$t('pc_eval_msg_survey_al_delete')"
      @customClick="returnList"
    />

    <template v-else>
      <div v-if="isDetail" class="eval-ques-detail">
        <span class="font-size-16 font-weight-500">{{
          quesInfo.evaluationName
        }}</span>
      </div>

      <div
        v-loading="loading"
        class="gwnl-questionnaire-preview gwnl-questionnaire-previewsubmit mt16 mb24"
        :class="isDetail ? 'mt64 eval-ques-detail__preview' : ''"
      >
        <yxtbiz-watermark :option="option" iframe-id="main" />
        <div class="gwnl-questionnaire-preview-container">
          <div class="gwnl-flex width100 ph24">
            <div
              id="playercontainer"
              class="gwnl-questionnaire-preview-container__pc"
            >
              <div id="main" ref="themeContainer">
                <yxt-form
                  ref="form"
                  :rules="rules"
                  :model="form"
                  :inline-message="true"
                  class="gwnl-questionnaire-preview-questions"
                >
                  <template v-for="item in questions">
                    <yxt-form-item
                      v-if="isQuestion(item)"
                      :key="item.id"
                      :prop="item.prop"
                      label-width="0px"
                      class="gwnl-questionnaire-preview-questions__item"
                    >
                      <div class="gwnl-question-index color-gray-7">
                        {{ item.quNum }}
                      </div>
                      <div class="gwnl-question-content gwnl-flex-1 pr">
                        <div
                          class="standard-size-14 mb20 wordbreak layout-flex"
                        >
                          <span
                            class="gwnl-question-title-item gwnl-question-content_type"
                          >
                            [{{
                              $t(QUESTION_TYPE_WORD.get(item.quType))
                            }}]</span
                          >
                          <span
                            v-dompurify-html="item.quTitleFull"
                            class="gwnl-question-title-item gwnl-question-title-html ml4"
                          ></span>
                        </div>

                        <div
                          v-if="
                            surveyId && item.showIndicator && item.skillType
                          "
                          class="gwnl-question-indicator__detail standard-size-14 color-primary-6"
                          @click="getIndicatorDetail(item)"
                        >
                          {{
                            $t(
                              "pc_eval_lbl_indicator_detail" /* 所属指标详情 */
                            )
                          }}
                          <yxt-svg
                            width="16px"
                            height="16px"
                            icon-class="arrow-right"
                          />
                        </div>

                        <div class="gwnl-question-content-item">
                          <component
                            :is="showComponent(item)"
                            :ref="`question${item.id}`"
                            :question="item"
                            :attach-list.sync="item.uploadAnswer"
                            :disabled="submited"
                            :is-view-mode="submited"
                            :changed="changeOptionMap"
                            @statusChanged="reCountFinished"
                            @setAttachList="setAttachList"
                          />
                        </div>
                        <analysis
                          v-show="submited && item.explainText"
                          :analyze-text="item.explainText"
                        />
                      </div>
                    </yxt-form-item>
                    <!--  备注  -->
                    <div
                      v-else-if="item.quType === 'A04.01'"
                      :key="item.id + 'A04.01'"
                    >
                      <div
                        v-dompurify-html="item.quTitleFull"
                        class="gwnl-questionnaire-question__remark"
                      ></div>
                      <analysis
                        v-show="submited && item.explainText"
                        :analyze-text="item.explainText"
                      />
                    </div>
                    <!-- 分割线 -->
                    <div
                      v-else-if="item.quType === 'A04.02'"
                      :key="item.id + 'A04.02'"
                    >
                      <div class="gwnl-questionnaire-question__divider"></div>
                      <analysis
                        v-show="submited && item.explainText"
                        :analyze-text="item.explainText"
                      />
                    </div>
                  </template>
                </yxt-form>

                <survey-footer
                  class="eval-answer-footer"
                  :pager="pager"
                  :submited="submited"
                  :answering="!submited"
                  :disable-submit="submitting"
                  @goPre="uploadCheckAndSkip('prev')"
                  @goNext="uploadCheckAndSkip('next')"
                  @submit="submit"
                  @goSave="goSavePage"
                />
              </div>
            </div>
            <div :class="{ 'card-hidde': isShowCard }">
              <progress-card
                v-if="!submited || isDetail"
                :user-info="userInfo_a"
                :time-limit="timeLimit"
                :total-count="totalCount"
                :finished-count="finishedCount"
                :ques-info="quesInfo"
                @submit="submit"
                @get-user-detail="goUserInfoDetail"
              />
            </div>
            <div v-if="!submited || isDetail" class="fled">
              <div class="content-center">
                <div v-show="isShowCard" class="card-box">
                  <progress-card
                    :user-info="userInfo_a"
                    :time-limit="timeLimit"
                    :total-count="totalCount"
                    :finished-count="finishedCount"
                    :ques-info="quesInfo"
                    @get-user-detail="goUserInfoDetail"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <abnormal-dialog
        ref="abnormalDialog"
        :type="1"
        :time-ctrl-count="timeCtrlCount"
        @stopAlarm="handleStopAlarm"
      />

      <yxtbiz-indicator-detail
        ref="indicatorDetail"
        :is-client="true"
        :title="indicatorTitle"
      />
    </template>
  </div>
</template>

<script>
import { Message } from "yxt-pc";
import {
  getSurveyBasicInfo,
  getQuestionsList,
  saveSurveyRecord,
  submitSurvey,
  getUserRelationInfo,
  evalAnswerCheck,
} from "./service/eval.service.js";
import Rate from "./components/rate";
import Single from "./components/single";
import multiple from "./components/multiple";
import answer from "./components/answer";
import trueFalse from "./components/trueFalse";
import ProgressCard from "./components/progressCard.vue";
import surveyFooter from "./components/surveyFooter";
import abnormalDialog from "./components/abnormalDialog.vue";
import {
  SURVEY_STATUS,
  QUESTION_TYPE_ENUM,
  QUESTION_TYPE_WORD,
  SURVEY_TYPE,
} from "./enum";
// import { mapState } from 'vuex';
import analysis from "./components/analysis";
import upload from "./components/upload";
import ErrorPage from "./components/error-page.vue";
import question from "./mixins/question";
import { sendErrMsg } from "yxt-ulcd-sdk/packages/evaluation/src/utils";
const isIE = () => {
  return !!(!!window.ActiveXObject || "ActiveXObject" in window);
};
export default {
  mixins: [question],
  components: {
    Rate,
    // crumb,
    Single,
    ProgressCard,
    surveyFooter,
    multiple,
    trueFalse,
    answer,
    abnormalDialog,
    analysis,
    upload,
    ErrorPage,
  },
  props: {
    queryData: {
      type: Object,
      defalut: {},
    },
  },
  data() {
    return {
      stickyHeaderTop: 0, // sticky header 的初始位置
      isShowCard: false,
      questions: [],
      userInfo_a: {},
      surveyId: this.queryData.surveyId,
      relationId: this.queryData.relationId,
      evaluationId: this.queryData.evaluationId,
      qstnId: "",
      evaluatorType: this.queryData.evaluatorType,
      type: this.queryData.type,
      pageId: this.queryData.pageId,
      evaluationUserId: "",
      loading: false,
      finishedCount: 0,
      timeLimit: 0,
      totalCount: 0,
      submited: false,
      startTime: 0,
      submitTime: 0,
      pager: {
        pages: 0,
        pageSize: 10,
        current: 1,
        offset: 0,
        count: 0,
      },
      isPageSymbol: false,
      form: {},
      // 数据校验规则
      rules: {},
      submitting: false,
      surveyStatus: 2,
      QUESTION_TYPE_WORD,
      SURVEY_TYPE,
      QUESTION_TYPE_ENUM,
      isVisitorKey: this.queryData.isVisit,
      invalidPercent: 0,
      invalidSetting: 0,
      invalidSwitch: 0,
      invalidAction: 0,
      warnCount: 0,
      warnSwitch: 0,
      warnInfo: { stopAlarm: false },
      warnSaveData: [],
      timeCtrlSwitch: 0,
      timeCtrlAction: 0,
      timeCtrlCount: 0,
      quesInfo: {},
      indicatorTitle: "",
      isDelEval: false,
      userInfo: {},
    };
  },
  computed: {
    // 是否是管理端查看页
    isDetail() {
      return !!this.mgmtVal;
    },

    // 0或者空代表不是管理端查看页，1管理端查看页，2其他评估关系查看页
    mgmtVal() {
      return ~~this.queryData.mgmt; // TODO 检查用法是否正确
    },
  },
  watch: {
    questions: {
      handler(newVal, oldVal) {
        this.warnInfo = sessionStorage.getItem(this.sessionVal)
          ? JSON.parse(sessionStorage.getItem(this.sessionVal))
          : { stopAlarm: false };
        const postCommitData = this.getFormatedCommitedData();
        const commitData = postCommitData.quAns;

        if (this.warnInfo.users) {
          this.warnSaveData = this.warnInfo.users[0].question || [];
          commitData.forEach((item) => {
            const quIndex = this.warnSaveData
              .map((item) => item.quId)
              .indexOf(item.quId);
            if (quIndex === -1) {
              this.warnSaveData.push(item);
            } else {
              this.warnSaveData.splice(quIndex, 1, item);
            }
          });
        } else {
          this.warnSaveData = postCommitData.quAns;
        }
        const warnNum = this.maxPower(this.warnSaveData);
        // 触发提示条件：warnSwitch开关打开+连续题数大于设置数+没有关闭“不再提示”+非查看问卷(限单人答题）
        if (
          this.warnSwitch &&
          this.warnCount &&
          warnNum >= this.warnCount &&
          !this.warnInfo.stopAlarm &&
          !this.submited
        ) {
          this.$refs.abnormalDialog.handleShowAbnormal();
        }
      },
      deep: true,
    },
  },
  created() {
    this.isVisitor();
    this.getSurveyDetail();
    window.addEventListener("scroll", this.handleScroll);
    // 兼容IE 皮肤切换处理
    if (isIE()) {
      this.themeContainer();
    }
  },
  mounted() {
    this.initStickyHeader();
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleStickyScroll);
  },
  methods: {
    // 初始化 sticky header
    initStickyHeader() {
      if (this.$refs.stickyHeader) {
        this.stickyHeaderTop = this.$refs.stickyHeader.offsetTop;
        window.addEventListener("scroll", this.handleStickyScroll);
      }
    },
    // 处理滚动事件
    handleStickyScroll() {
      if (!this.$refs.stickyHeader) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const header = this.$refs.stickyHeader;

      if (scrollTop >= this.stickyHeaderTop) {
        header.classList.add("sticky-fixed");
      } else {
        header.classList.remove("sticky-fixed");
      }
    },
    backToPreview() {
      this.$emit("confirmLeave", true);
    },
    confirmLeave() {
      // 如果有答题进度且未提交，则不允许离开
      if (this.finishedCount > 0 && !this.submited) {
        return true;
      }
      // 如果没有答题进度或已提交，则允许离开
      return false;
    },
    // 根据relationId获取信息
    getUserRelationInfo() {
      getUserRelationInfo(this.relationId)
        .then((res) => {
          this.userInfo_a = res;
        })
        .finally(() => {
          this.userInfo = {};
        });
    },
    reCountFinished(num) {
      // 解决特殊情形下已答题大于总数的问题
      this.finishedCount = this.finishedCount + num;
      if (this.finishedCount > this.totalCount) {
        this.finishedCount = this.totalCount;
      }
      if (this.finishedCount <= 0) {
        this.finishedCount = 0;
      }
    },
    getSurveyDetail() {
      if (!this.surveyId) return;

      this.loading = true;
      getSurveyBasicInfo(this.relationId)
        .then(async (res) => {
          this.loading = false;
          this.quesInfo = res;

          // 说明该问卷已删除
          if (!res.evaluationId && !res.surveyId) {
            this.isDelEval = true;
            return;
          }

          // 1-开始答题禁用 2-开始答题启用 3-已经提交查看答卷 4-已经提交不能查看答卷 5-已过期
          this.surveyStatus = res.status;
          this.evaluationUserId = res.evaluationUserId;
          this.qstnId = res.qstnId;
          this.submited = this.surveyStatus !== SURVEY_STATUS.start;
          this.invalidPercent = res.invalidPercent;
          this.invalidSetting = res.invalidSetting;
          this.invalidSwitch = res.invalidSwitch;
          this.invalidAction = res.invalidAction;
          this.warnCount = res.warnCount;
          this.warnSwitch = res.warnSwitch;
          this.timeCtrlSwitch = res.timeCtrlSwitch;
          this.timeCtrlAction = res.timeCtrlAction;
          this.timeCtrlCount = res.timeCtrlCount;
          this.isDetail ? this.getUserRelationInfo() : this.getUserInfo();
          // 开始答题时计算倒计时
          if (!this.submited) {
            const usedTime = await this.calculateUsedTime();
            this.timeLimit = res.timeLimit * 60 - usedTime; // 单位分钟转换为秒
          }
          this.pager.pageSize = this.getSizeOfPage(res.pageQumNum);
          this.getQuestions();
        })
        .catch((err) => {
          this.$handleError(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getSizeOfPage(pageQumNum) {
      let pageSize = 0;
      switch (pageQumNum) {
        // 不分页，一页展示全部
        case -1:
          pageSize = 9999;
          break;
        // 分页符展示
        case -99:
          pageSize = -99;
          this.isPageSymbol = true;
          break;
        default:
          pageSize = pageQumNum;
      }
      return pageSize;
    },
    getQuestions(offset = 0) {
      // 说明该问卷已删除
      if (!this.surveyId) {
        this.isDelEval = true;
        return;
      }

      this.loading = true;
      this.questions = [];
      const p = {
        relationId: this.relationId,
        offset,
        limit: this.pager.pageSize,
        qstnId: this.qstnId,
        ia: this.mgmtVal === 2 ? 0 : 1,
        fromDimensionType: this.evaluatorType,
      };

      if (this.pager.pageSize === -99) {
        if (offset === 0) {
          offset = 1;
        }
        p.current = offset;
        delete p.offset;
      }

      getQuestionsList(this.surveyId, p)
        .then((res) => {
          this.pager.pages = res.paging.pages;
          this.pager.offset = this.isPageSymbol ? offset : res.paging.offset;
          this.pager.count = res.paging.count;
          this.pager.current =
            this.isPageSymbol || this.pager.pageSize === 9999
              ? offset + 1
              : this.pager.offset / res.paging.limit + 1;
          this.finishedCount = res.datas[0]
            ? res.datas[0].answerNum > res.paging.count
              ? res.paging.count
              : res.datas[0].answerNum
            : 0;
          this.totalCount = res.paging.count;
          this.formatData(res.datas);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    formatData(list) {
      const params = {};
      const ruleObj = {};
      this.questions = list.map((item, index) => {
        // 只有题才有check
        if (this.isQuestion(item)) {
          item.userOptionId = this.formatQuestionRecord(item);
          if (this.isQuestionAnswer(item)) {
            item.userOptionId = item.titleAnswer || "";
          }
          const value = `value${index}`;
          item.prop = value;
          params[value] = this.isQuestionUpload(item)
            ? (item.titleAnswer && JSON.parse(item.titleAnswer)) || []
            : item.userOptionId;
          ruleObj[value] = [];

          if (this.isQuestionAnswer(item)) {
            if (item.quRequire) {
              ruleObj[value].push({
                required: true,
                type: "string",
                message: this.$t("pc_gwnl_eval_pleaseEnterAnswer"),
                trigger: "blur",
              });
            }
          } else if (this.isQuestionUpload(item)) {
            item.uploadAnswer =
              (item.titleAnswer && JSON.parse(item.titleAnswer)) || [];
            if (item.quRequire) {
              ruleObj[value].push({
                required: true,
                type: "array",
                validator: this.validateUpload,
                trigger: "change",
              });
            }
          } else if (this.isQuestionButNotMultiple(item)) {
            ruleObj[value].push({
              required: true,
              type: "string",
              message: this.$t("pc_gwnl_eval_selectOption"),
              trigger: "change",
            });
          } else {
            ruleObj[value].push({
              required: true,
              type: "array",
              message: this.$t("pc_gwnl_eval_selectOption"),
              trigger: "change",
            });
          }
        }

        return item;
      });
      this.rules = ruleObj;
      this.form = params;
    },

    formatQuestionRecord(item) {
      const isQuestionType = this.isQuestion(item);
      const isMultipleType =
        item.quType === QUESTION_TYPE_ENUM.multiple ||
        item.quType === QUESTION_TYPE_ENUM.upload;
      if (item.optionId) {
        return isMultipleType ? item.optionId.split(",") : item.optionId;
      } else {
        // init
        return isQuestionType ? (isMultipleType ? [] : "") : undefined;
      }
    },

    // 提交回答的答案
    commitAnswerData(resolve, reject) {
      const data = this.getFormatedCommitedData();
      this.loading = true;
      saveSurveyRecord(data)
        .then(() => {
          const info = {
            stopAlarm: this.warnInfo.stopAlarm,
            users: [{ ...{ question: this.warnSaveData } }],
          };
          sessionStorage.setItem(this.sessionVal, JSON.stringify(info));
          resolve();
        })
        .catch((err) => {
          reject(new Error());
          sendErrMsg(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    saveRecord(validateRequired = true) {
      return new Promise((resolve, reject) => {
        if (!validateRequired) {
          this.commitAnswerData(resolve, reject);
          return;
        }

        this.$refs.form.validate((valid) => {
          if (valid) {
            this.commitAnswerData(resolve, reject);
          } else {
            this.$refs.form.$children
              .find((item) => item.validateState === "error")
              .$el.scrollIntoView({ block: "center" });
            reject(new Error("validate error"));
          }
        });
      });
    },
    getFormatedCommitedData() {
      const params = {
        quAns: [],
        surveyId: this.surveyId,
        targetId: this.relationId,
        evaluationId: this.evaluationId,
        evaluatorType: this.evaluatorType,
        qstnId: this.qstnId,
        userId: this.userId,
      };
      const questions = this.questions.filter((question) =>
        this.isQuestion(question)
      );
      params.quAns = questions.map((question) => {
        let answerValue = "";
        let titleName = "";
        let list;
        if (question.userOptionId) {
          const arrayOptions = Array.isArray(question.userOptionId)
            ? question.userOptionId
            : [question.userOptionId];
          list = arrayOptions.map((selectedOptionId) => {
            const matchedOption = question.prjQuOptions.find((option) => {
              return option.id === selectedOptionId;
            });
            return {
              optionId: selectedOptionId,
              order: matchedOption ? matchedOption.optionNum : undefined,
              score: matchedOption ? matchedOption.optionScore : undefined,
              scoreType: matchedOption ? matchedOption.scoreType : undefined,
            };
          });
        }
        if (this.isQuestionAnswer(question)) {
          list = [];
          answerValue = question.userOptionId || "";
          titleName = question.quTitleFull || "";
        }
        return {
          quRequire: question.quRequire,
          titleName,
          quId: question.id,
          quType: question.quType,
          titleAnswer: answerValue,
          optionWarn: this.optionWarnValue(question, list),
          optionAnswers: list,
          uploadFile: question.uploadAnswer,
        };
      });
      return params;
    },

    // 检查文件上传题是否有文件正在上传
    checkUploadQuesIsUploading() {
      const fileList = this.questions.filter(
        (question) =>
          this.isQuestion(question) &&
          question.quType === QUESTION_TYPE_ENUM.upload
      );

      const isUploadingList = [];
      const isErrorList = [];
      if (fileList && fileList.length) {
        fileList.forEach((item) => {
          const fileLoadingList =
            this.$refs[`question${item.id}`][0].progressIds;
          isUploadingList.push(!!fileLoadingList.length);

          const errList = this.$refs[`question${item.id}`][0].errorList;
          isErrorList.push(!!errList.length);
        });
        return {
          isUploadingFile: isUploadingList.some((item) => item),
          isErrorFile: isErrorList.some((item) => item),
        };
      }
      return { isUploadingFile: false, isErrorFile: false };
    },

    submit(needConfirm = true, isForceSubmit = false) {
      const { isUploadingFile, isErrorFile } =
        this.checkUploadQuesIsUploading();
      if (isUploadingFile) {
        // 有文件正在上传中，无法提交！
        this.$message.error(this.$t("pc_kng_upload_msg_file_uploading4"));
        return;
      }
      if (isErrorFile) {
        // 有文件上传失败，请检查
        this.$message.error(this.$t("pc_eval_msg_upload_fail"));
        return;
      }
      if (needConfirm) {
        this.$confirm(
          this.$t("pc_gwnl_web_lbl_survey_submit_hint"),
          this.$t("pc_gwnl_global_tips"),
          {
            confirmButtonText: this.$t("pc_gwnl_global_msg_determine"),
            cancelButtonText: this.$t("pc_gwnl_global_msg_cancel"),
            type: "success",
          }
        ).then(() => {
          this.submitSurveyCard();
        });
      } else {
        this.submitSurveyCard(false, isForceSubmit);
      }
    },

    async submitSurveyCard(needCheckRequired = true, isForceSubmit = false) {
      try {
        // 是否是强制提交
        const forceSubmit =
          typeof isForceSubmit === "boolean" ? ~~isForceSubmit : 0;
        this.submitting = true;
        this.submitTime = new Date().getTime();
        await this.saveRecord(needCheckRequired);

        if (!isForceSubmit && !(await this.handleAnswerCheck())) {
          return;
        }

        const data = {
          surveyId: this.surveyId,
          targetId: this.relationId,
          evaluationId: this.evaluationId,
          evaluatorType: this.evaluatorType,
          qstnId: this.qstnId,
          userId: this.userId,
          submitTime: this.submitTime,
          startTime: this.startTime,
          forceSubmit,
        };

        await submitSurvey(data);
        // this.$router.go(-1);
        this.$emit("on-complete-change", true);
      } catch (error) {
        this.$handleError(error);
      } finally {
        this.submitting = false;
      }
    },

    // 答题行为检测-无效问卷判断
    async handleAnswerCheck() {
      const data = { qstnId: this.qstnId, relationIds: [this.relationId] };

      try {
        const res = await evalAnswerCheck(data);
        if (res.success) return true;

        const userInfo = res.userInfo || [];
        const timeUserInfo = res.timeUserInfo || [];

        if (userInfo.length) {
          this.$refs.abnormalDialog.handleShowUseless([]);
        } else if (timeUserInfo.length) {
          this.$refs.abnormalDialog.handleShowUseless([], true);
        }
        return false;
      } catch (error) {
        return false;
      }
    },
    // 答题行为检测-警示-本次评估不再提醒
    handleStopAlarm(val) {
      // 如果“本次评估不再提醒”发生变化以后缓存数据
      if (val !== this.warnInfo.stopAlarm) {
        this.warnInfo.stopAlarm = val;
        sessionStorage.setItem(
          this.sessionVal,
          JSON.stringify({
            stopAlarm: val,
            users: [{ ...{ question: this.warnSaveData } }],
          })
        );
      }
    },

    // 不同子组件的展示
    showComponent(item) {
      return this.SURVEY_TYPE[item.quType];
    },

    // 保存操作新增
    goSavePage() {
      const { isUploadingFile, isErrorFile } =
        this.checkUploadQuesIsUploading();
      if (isUploadingFile) {
        // 有文件正在上传中，无法保存！
        this.$message.error(this.$t("pc_eval_upload_file_loading"));
        return;
      }
      if (isErrorFile) {
        // 有文件上传失败，请检查
        this.$message.error(this.$t("pc_eval_msg_upload_fail"));
        return;
      }
      this.saveRecord(false)
        .then(() => {
          this.$message.success(
            this.$t("pc_eval_save_successfully").d("保存成功")
          );
          this.$emit("on-complete-change", false);
        })
        .catch(() => {
          this.$message.error(
            this.$t("pc_gwnl_setting_msg_saveFailed").d("保存失败")
          );
        });
    },

    setAttachList(item, attachList) {
      this.$set(item, "uploadAnswer", attachList);
    },

    // 前往查看被评估人答题页面
    goUserInfoDetail(userInfo) {
      this.goDetail(userInfo, this.quesInfo.userSelfRelationId || "");
    },

    // 上一步/下一步跳转检查
    uploadCheckAndSkip(key) {
      const { isUploadingFile, isErrorFile } =
        this.checkUploadQuesIsUploading();
      if (isUploadingFile) {
        // 文件正在上传中，请等待上传完成
        this.$message.error(this.$t("pc_eval_msg_file_uploading_wait"));
        return;
      }
      if (isErrorFile) {
        // 有文件上传失败，请检查
        this.$message.error(this.$t("pc_eval_msg_upload_fail"));
        return;
      }

      if (key === "prev") {
        this.gotoPrePage();
        return;
      }
      this.gotoNextPage();
    },
    // 处理普通接口报错
    $handleError(error) {
      if (error && error.error) {
        // 兼容处理
        error = error.error;
      }
      if (error) {
        if (error.key && error.key.indexOf("global.token") >= 0) {
          return; // 不弹出token错误
        }
        const msg = error.message;
        if (typeof msg !== "string") return;
        Message({
          message: msg,
          type: "error",
        });
      }
    },
  },
};
</script>

<style lang="scss">
@import "./questionnaire";
.eval-ques-detail {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 12px 24px 12px 16px;
  line-height: 24px;
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
}

.gwnl-questionnaire-preview {
  padding-top: 0;

  .wordbreak {
    .gwnl-question-title-item {
      display: inline-block;
      min-width: 55px;
      vertical-align: top;
    }
  }
}

.gwnl-questionnaire-previewsubmit {
  .gwnl-questionnaire-preview-container {
    .gwnl-question-content-item {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .yxtf-radio-group.yxtf-radio-group--row,
    .yxtf-checkbox-group.yxtf-checkbox-group--row {
      width: 100%;

      .yxtf-radio__label,
      .yxtf-checkbox__label {
        display: inline-flex;
        width: 90%;
        vertical-align: middle;
      }

      .yxtf-radio__label {
        vertical-align: top;
      }
    }
  }
}

.gwnl-questionnaire-preview-container {
  display: flex;
  align-content: center;
  justify-content: center;
}

.gwnl-questionnaire-preview-questions {
  .select {
    .option {
      cursor: pointer;

      &.square:hover {
        color: #fff;
        background: var(--color-primary-6);
      }

      &.star:hover {
        color: var(--color-primary-6);
      }
    }
  }

  .yxtf-radio,
  .yxtf-radio__input {
    white-space: normal !important;
  }

  ::v-deep .yxt-form-item:last-child {
    margin-bottom: 0;
  }
}

.gwnl-questionnaire-toolbar {
  margin-top: 68px;
  text-align: center;
}

.gwnl-question-content_type {
  color: #262626;
  font-weight: 500;
}

.gwnl-question-index {
  width: 20px;
  margin-right: 8px;
  padding: 0;
  line-height: 22px;
  text-align: center;
}

/* Evaluation questionnaire container */
.yxtulcdsdk-evaluation-questionnaire {
  height: 100%;
  overflow-y: auto;
}

/* Custom sticky header */
.custom-sticky-header {
  position: relative;
  z-index: 999;

  &.sticky-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
<style lang="scss" scoped>
.card-hidde {
  opacity: 0;
}

.fled {
  position: fixed;
  top: 70px;
  left: 0;
  width: 100%;

  .content-center {
    position: relative;
    width: 1200px;
    margin: 0 auto;

    .card-box {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.gwnl-questionnaire-preview-questions__item {
  ::v-deep .yxt-form-item__content {
    display: flex;
    width: 100%;
  }
}
</style>
