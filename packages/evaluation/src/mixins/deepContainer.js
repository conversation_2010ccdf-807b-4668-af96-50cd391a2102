import { addResizeListener, removeResizeListener } from 'yxt-pc';
import ErrorPage from '../components/error-page.vue';

export default {
  components: {
    ErrorPage
  },

  eventList: [{
    type: 'fullScreen',
    handler: 'changeFullScreen',
    label: '更改是否全屏'
  }, {
    type: 'updateProgress',
    handler: 'updateProgress',
    label: '更新当前的进度'
  }, {
    type: 'back',
    handler: 'back',
    label: '返回到上一级'
  }],

  props: {
    // 原先页面的参数
    params: {
      type: Object,
      default: () => {
      },
      desc: '调查原先页面的参数',
      setter: 'JsonSetter'
    },
    // 当前的步骤，调查默认走原先的中转页
    step: {
      type: Number,
      default: 0,
      desc: '当前的步骤，调查默认走原先的中转页',
      setter: 'NumberSetter'
    },
    // 是否沉浸式
    deepStudy: {
      type: Boolean,
      default: true,
      desc: '是否沉浸式',
      setter: 'BooleanSetter'
    },
    // 是否显示全屏按钮
    fullScreen: {
      type: Boolean,
      default: true,
      desc: '是否显示全屏按钮',
      setter: 'BooleanSetter'
    },
    // 是否使用圆角
    radius: {
      type: Boolean,
      default: true,
      desc: '是否使用圆角',
      setter: 'BooleanSetter'
    },
    // 直接撑满容器，去除内部自带的边距
    fullPage: {
      type: Boolean,
      default: undefined,
      desc: '直接撑满容器，去除内部自带的边距',
      setter: 'BooleanSetter'
    },
    // 内部滚动的模式
    scrollInner: {
      type: Boolean,
      default: true,
      desc: '内部滚动的模式',
      setter: 'BooleanSetter'
    },
    // 是否单独的成功页面
    isSuccessPage: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      // 处理css不好计算的高宽问题
      getHeight: () => this.height, // 组件的容器大小
      getWidth: () => this.width, // 组件的容器大小
      getFSWidth: () => this.fsWisth // 浏览器可见区域大小
    };
  },
  data() {
    return {
      exporting: this.$route.query.exporting,
      queryDatas: {},
      height: 0,
      heightOuter: 0,
      width: 0,
      fsWisth: 72,
      isFullscreen: false,
      keyIndex: 1,
      componentMap: [],
      componentName: '',
      routerMap: [],
      pageType: 1,
      isShowErrorPage: false,
      errorMessage: '',
      errorCode: ''
    };
  },
  watch: {
    step: {
      handler: function() {
        this.changeStep(this.step);
      }
    },
    params: {
      handler: function() {
        this.init();
      },
      deep: true
    }
  },
  created() {
  },

  methods: {
    init(cm, rm, pageType = 1) {
      if (cm) this.componentMap = cm;
      if (rm) this.routerMap = rm;
      this.queryDatas = {
        ...this.params
      };
      this.currentStep = this.step;
      this.componentName = this.componentMap[this.currentStep];
      this.keyIndex++;
      this.pageType = pageType;
      this.isShowErrorPage = false;

      this.$nextTick(() => {
        if (this.deepStudy) {
          addResizeListener(this.$el, this.setSize);
          this.setSize();
        } else {
          window.addEventListener('resize', this.setSizeInPage);
          this.setSizeInPage();
        }
      });
    },
    changeStep(step, params, replace, newPage) {
      if (step === this.currentStep && this.deepStudy) {
        this.keyIndex++;
      }
      if (this.deepStudy) {
        // 沉浸式学习组件内切换
        this.currentStep = step;
        this.componentName = this.componentMap[this.currentStep];
        this.queryDatas = params;
      } else {
        // 调研自己使用还是正常页面的跳转
        const url = this.$router.resolve({
          name: this.routerMap[step],
          query: {
            ...params
          }
        }).href;
        if (newPage) {
          window.open(url);
        } else if (replace) {
          window.location.replace(url);
        } else {
          window.location.href = url;
        }
      }
    },
    setSize() {
      this.height = this.$el && this.$el.clientHeight || 0;
      !this.heightOuter && (this.heightOuter = this.height);
      this.width = this.$el && this.$el.clientWidth || 0;
      const rfFS = this.$refs.fullScreen;
      this.fsWisth = rfFS && rfFS.clientWidth || 72;
    },
    setSizeInPage() {
      const header = document.getElementsByClassName('yxtbiz-nav-top-stu');
      const footer = document.getElementsByClassName('yxtbiz-nav-footer');

      this.height = window.innerHeight -
        (footer && footer[0] && footer[0].offsetHeight || 0) -
        ((header && header[0] && header[0].offsetHeight + 56) || 0);
      !this.heightOuter && (this.heightOuter = this.height);
      this.width = window.innerWidth;

    },
    changeFullScreen() {
      this.isFullscreen = !this.isFullscreen;
      this.$emit('fullScreen', this.isFullscreen);

      this.$nextTick(() => {
        this.setSize();
      });
    },
    updateProgress(type) {
      this.$emit('updateProgress', type);
    },
    goSuccessPage(survey) {
      this.$emit('goSuccessPage', survey);
    },
    back() {
      this.$emit('back');
    },
    // 提供给使用方，在离开页面时做组件内的二次确认
    confirmLeave(cb) {
      try {
        const pager = this.$refs.refPage && this.$refs.refPage[0] || this.$refs.refPage;
        if (pager && pager.confirmLeaveStudy) {
          pager.confirmLeaveStudy(cb);
        } else {
          cb && cb(true);
        }
      } catch (error) {
        cb && cb(true);
      }
    },

    errorPublic(code, msg) {
      this.isShowErrorPage = true;
      this.errorCode = code;
      this.errorMessage = msg;
    },

    renderErrorPage(h) {
      return h('ErrorPage', {
        class: {
          'yxtulcdsdk-surveying': true,
          'hline': !this.deepStudy
        },
        style: {
          minHeight: (this.scrollInner ? this.height : this.heightOuter) + 'px'
        },
        props: {
          'code': this.errorCode,
          'msg': this.errorMessage
        }
      });
    },

    renderPage(h) {
      return h(this.componentName, {
        props: {
          'deep-study': this.deepStudy,
          'query-datas': this.queryDatas,
          'full-page': this.fullPage,
          'is-success-page': this.isSuccessPage
        },
        class: {
          'yxtulcdsdk-surveying': true,
          'hline': !this.deepStudy
        },
        style: {
          minHeight: (this.scrollInner ? this.height : this.heightOuter) + 'px',
          minWidth: (this.deepStudy && this.isFullscreen) ? '860px' : undefined
        },
        on: {
          changeStep: this.changeStep,
          updateProgress: this.updateProgress,
          goSuccessPage: this.goSuccessPage,
          errorPublic: this.errorPublic,
          back: this.back
        },
        ref: 'refPage'
      });
    }
  },
  render(h) {
    return <div
      key={this.keyIndex}
      class={{
        'yxtulcdsdk-ulcdsdk yxtulcdsdk-survey yxtulcdsdk-survey-container pr': true,
        'yxtulcdsdk-survey--deep': this.deepStudy,
        'yxtulcdsdk-survey-container--page': !this.deepStudy,
        'yxtulcdsdk-survey-container--no-radius': !this.radius,
        'yxtulcdsdk-survey-container--no-scroll': !this.scrollInner,
        'yxtulcdsdk-survey-container--fs': this.isFullscreen
      }}
    >
      { this.isShowErrorPage ? this.renderErrorPage(h) : (this.deepStudy && this.scrollInner ? <yxt-scrollbar is-auto-overscroll={true} fit-height={true}>{ this.renderPage(h) }</yxt-scrollbar> : this.renderPage(h)) }

      {
        // 全屏/退出全屏
        this.deepStudy && this.fullScreen && <div
          ref="fullScreen"
          class="yxtulcdsdk-full-screen"
          onClick={this.changeFullScreen}
        >
          <media-svg
            isStc
            icon-class={this.isFullscreen ? 'fullscreen-back' : 'fullscreen'}
            width="16px"
            height="16px"
          />
          <span class="ml8">{ this.$t(this.isFullscreen ? 'pc_ote_btn_exitfullscreen' : 'pc_ote_btn_fullscreen') }</span>
        </div>
      }
    </div>;
  },
  beforeDestroy() {
    if (this.deepStudy) {
      removeResizeListener(this.$el, this.setSize);
    } else {
      window.removeEventListener('resize', this.setSizeInPage);
    }
  }
};
