import { getUserInfo, checkSurveyLeftTime } from '../service/eval.service.js';
import { SURVEY_STATUS, QUESTION_TYPE_ENUM } from '../enum';

export default {
  computed: {
    option() {
      return {
        enabled: 1,
        type: 1,
        text: this.userInfo.username,
        color: 'rgb(153,153,153)',
        opacity: 18
      };
    },
    userId() {
      return this.queryData.userId;
    },

    sessionVal() {
      return `warnInfo_${this.evaluationId}_${this.evaluatorType}`;
    }
  },

  methods: {
    // 是否是游客登陆
    isVisitor() {
      const userId = localStorage.getItem('TY_USER_ID');
      const isVisitor = localStorage.getItem('isVisitor') === '1';
      if (isVisitor || this.isVisitorKey) {
        const vToken = localStorage.getItem('vToken');
        // 如果是游客登陆没有token 重新跳转游客登陆
        if (!vToken) {
          this.$router.push({
            name: 'visitor'
          });
        } else {
          // 培训过来的话用默认的userId,不需要变
          if (Number(this.isVisitorKey) !== 2) {
            this.userId = userId;
          }
        }
      }
    },

    // 连续题数的最大值
    maxPower(s) {
      if (!s) return 1;

      let ans = 1; let cnt = 1;
      for (let i = 1; i < s.length; ++i) {
        if (s[i].optionWarn) {
          if (s[i].optionWarn === s[i - 1].optionWarn) {
            ++cnt;
            ans = Math.max(ans, cnt);
          } else {
            cnt = 1;
          }
        }
      }
      return ans;
    },

    themeContainer() {
      const themeColor = window.localStorage.getItem('theme') || '#436bff';
      this.$changeTheme(themeColor, {
        container: this.$refs.themeContainer
      });
    },

    handleScroll() {
      // 窗口滚要做的操作写这里
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      this.isShowCard = scrollTop > 140;
    },

    getUserInfo() {
      getUserInfo(this.evaluationUserId).then(async(res) => {
        this.userInfo_a = res;
      }).finally(() => {
        this.userInfo = {};
      });
    },

    changeOptionMap(id, index) {
      this.form[index] = id;
    },

    refreshQuestions(offset, needValidate = true) {
      if (this.surveyStatus === SURVEY_STATUS.start) {
        this.saveRecord(needValidate).then(() => {
          this.getQuestions(offset);
        });
      } else {
        this.getQuestions(offset);
      }
    },

    gotoPrePage() {
      if (this.pager.pageSize === -99) {
        this.refreshQuestions(this.pager.current - 2, false);
        return;
      }

      if (this.pager.current > 1) {
        this.refreshQuestions((this.pager.current - 2) * this.pager.pageSize, false);
      }
    },
    gotoNextPage() {
      if (this.pager.pageSize === -99) {
        this.refreshQuestions(this.pager.current);
        return;
      }

      if (this.pager.pages > this.pager.current) {
        this.refreshQuestions(this.pager.current * this.pager.pageSize);
      }
    },

    optionWarnValue(question, list) {
      let warnVal;
      // 单选题(无唯一标准答案)或者量表题才校验
      if ((question.quType === 'A01.01' && !question.quScoreType) || question.quType === 'A01.09') {
        warnVal = list && list.length ? question.quType + (list[0].scoreType ? '-' : list[0].score) : null;
      }
      return warnVal;
    },

    isQuestion(question) {
      return Object.keys(QUESTION_TYPE_ENUM).map(key => QUESTION_TYPE_ENUM[key]).indexOf(question.quType) !== -1;
    },

    isQuestionAnswer(item) {
      return this.isQuestion(item) && item.quType === QUESTION_TYPE_ENUM.answer;
    },

    isQuestionButNotMultiple(question) {
      return this.isQuestion(question) && question.quType !== QUESTION_TYPE_ENUM.multiple;
    },

    // 是否是文件上传题
    isQuestionUpload(question) {
      return this.isQuestion(question) && question.quType === QUESTION_TYPE_ENUM.upload;
    },

    validateUpload(rule, value, callback) {
      if (!value || value.length === 0) {
        callback(new Error(this.$t('pc_eval_msg_upload_required_materials' /* 请上传相应材料 */)));
        return;
      }
      callback();
    },

    async calculateUsedTime() {
      const data = {
        surveyId: this.surveyId,
        evaluationUserRelationId: this.relationId,
        qstnId: this.qstnId
      };
      const res = await checkSurveyLeftTime(data).catch(() => {});
      if (res && res.firstTime) {
        this.startTime = res.currentTime;
        return parseInt((res.currentTime - res.firstTime) / 1000);
      } else {
        return 0;
      }
    },

    // 前往查看被评估人答题页面
    goDetail(userInfo, relationId) {
      this.$toBlankPage({
        name: 'eval.assess.survey',
        query: {
          evaluationId: this.evaluationId,
          evaluationType: 2,
          surveyId: this.surveyId,
          userId: userInfo.userId || '',
          relationId,
          evaluatorType: this.evaluatorType,
          mgmt: 2
        }
      });
    },

    getIndicatorDetail(item) {
      if (!item.skillType) return;
      this.indicatorTitle = item.skillName;
      this.$refs.indicatorDetail.open(item.skillId, item.skillType);
    }
  }
};
