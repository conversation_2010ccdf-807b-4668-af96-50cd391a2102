import { commonUtil } from 'yxt-biz-pc';
import Vue from 'vue';
export const i18n = commonUtil.i18n;

// 判断钉钉环境下
export const checkOpenPlatform = function() {
  const SOURCE_CODES = {
    weixin: '100',
    dingding: '104'
  };

  const isOpenPlatform = [SOURCE_CODES.weixin, SOURCE_CODES.dingding].includes(
    localStorage.sourceCode
  );

  return isOpenPlatform;
};
/**
 * 数字转字母
 * @param {string} num 数字
 */
export const convertASCIIForNum = (num) => {
  let itemCode = '';
  num = parseInt(num) + 1;

  while (num > 0) {
    const remainder = (num - 1) % 26;
    itemCode = String.fromCharCode(remainder + 65) + itemCode;
    num = Math.floor((num - 1) / 26);
  }

  return itemCode;
};
// 计算文本需要的行数
export const calculateTextLines = (text, containerWidth, font = '14px PingFang SC') => {
  if (!text || !containerWidth) return 1;

  try {
    // 创建一个用于测量文本宽度的canvas上下文
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    // 设置字体样式
    context.font = font;
    // 按空格或换行符拆分文本，逐一测量
    const words = text.split(/\s+/);
    let lines = 1;
    let currentLineWidth = 0;
    // 遍历所有单词，计算每个单词的宽度并判断是否需要换行
    words.forEach(word => {
      const wordWidth = context.measureText(word + ' ').width;
      if (currentLineWidth + wordWidth > containerWidth) {
        // 如果当前行宽度加上单词的宽度超过了容器宽度，需要换行
        lines++;
        currentLineWidth = wordWidth;
      } else {
        // 否则将单词添加到当前行
        currentLineWidth += wordWidth;
      }
    });

    return lines;
  } catch (error) {
    console.log(error);
    return 1;
  }
};
export const errKeyMsg = {
  'apis.eval.question.maxsize': 'pc_eval_apis_eval_question_maxsize',
  'apis.eval.evaluationUser.relation.not.existed': 'apis_eval_evaluationUser_relation_not_existed',
  'apis.skill.catalog.exists.skills': 'pc_gwnl_error_skill_tip_catalogExistsSkills',
  'apis.skill.catalog.validation.name.duplication': 'pc_gwnl_error_skill_tip_catalogDuplication',
  'apis.skill.validation.name.duplication': 'pc_gwnl_error_skill_tip_nameDuplication',
  'apis.skill.jq.existed': 'pc_gwnl_error_skill_jqExisted',
  'apis.talent.jqCatalog.name.duplicated': 'pc_gwnl_error_jqCatalog_nameDuplication',
  'apis.talent.jqCatalog.parent.not.existed': 'pc_gwnl_error_jqCatalog_parentNotExisted',
  'apis.talent.jqCatalog.notFound': 'pc_gwnl_error_jqCatalog_notFound',
  'apis.talent.jqCatalog.subCatalog.exists': 'pc_gwnl_error_jqCatalog_subCatalogExists',
  'apis.talent.jqCatalog.reference.exists': 'pc_gwnl_error_jqCatalog_referenceExists',

  'apis.talent.jobQualification.notFound': 'pc_gwnl_error_jobQualification_notFound',
  'apis.talent.jobQualification.name.existed': 'pc_gwnl_error_jobQualification_nameExisted',
  'apis.talent.jobQualification.cannot.disable.for.study.config': 'pc_gwnl_error_jobQualification_cannotDisable',
  'apis.talent.jq.cannot.disable.for.study.config': 'pc_gwnl_error_jobQualification_cannotDisable',
  'apis.talent.jobQualification.validation.name.notBlank': 'pc_gwnl_error_jobQualification_nameNotBlank',
  'apis.talent.function.notFound': 'pc_gwnl_error_jobQualification_functionNotFound',
  'apis.talent.orgSetting.imporantce.notFound': 'pc_gwnl_error_jobQualification_imporantceNotFound',
  'apis.talent.skill.notFound': 'pc_gwnl_error_jobQualification_skillNotFound',
  'apis.talent.skill.level.notFound': 'pc_gwnl_error_jobQualification_skillLevelNotFound',
  'apis.talent.skill.maxLevel.notFound': 'pc_gwnl_error_jobQualification_skillmaxLevelNotFound',
  'apis.talent.skill.standardLevel.notFound': 'pc_gwnl_error_jobQualification_skillstandardLevelNotFound',
  'apis.talent.jobQualification.prioritysum.cannot.be.zero': 'pc_gwnl_error_jobQualification_skillCannotBeZero',
  'apis.talent.knowledge.notFound': 'pc_gwnl_error_jobQualification_knowledgeNotFound',
  'apis.talent.JqCatalog.notFound': 'pc_gwnl_error_jobQualification_jqCatalogNotFound',
  'apis.talent.jobQualification.function.disable': 'pc_gwnl_error_jobQualification_functionDisable',
  'apis.talent.jobQualification.function.change': 'pc_gwnl_error_jobQualification_functionChange',
  'apis.eval.evaluation.name.existed': 'pc_gwnl_error_eval_nameExisted',
  'apis.eval.evaluation.notExisted': 'pc_gwnl_eval_notExisted',
  'apis.eval.evaluation.is.deleted': 'apis_eval_deleted',
  'apis.eval.evaluation.deleted': 'apis_eval_deleted',
  'apis.eval.evaluation.expired': 'apis_eval_expired',
  'apis.eval.evaluationUser.relation.validation.evaluatorId.notBlank': 'pc_gwnl_error_eval_evaluatorIdNotBlank',
  'apis.eval.evaluationUser.relation.selfNotAllow': 'pc_gwnl_error_eval_selfNotAllow',
  'apis.eval.evaluationUser.relation.existed': 'pc_gwnl_error_eval_relationExisted',
  'apis.eval.evaluationUser.needPosition': 'pc_gwnl_error_eval_needPosition',
  'apis.eval.evaluationUser.needValidPosition': 'pc_gwnl_error_eval_needValidPosition',
  'apis.talent.studyProgressUser.notExisted': 'pc_gwnl_error_study_userDetailNotExisted',
  'apis.talent.studyConfig.notPublish': 'pc_gwnl_error_study_notPublish',
  'global.no.permission': 'pc_gwnl_error_tip_noPermission',
  'apis.study.config.validation.status.is.published': 'pc_gwnl_error_study_configValidationStatusIsPublish',
  'apis.study.phase.task.validation.result.exist': 'pc_gwnl_error_study_phaseTaskExist',
  'apis.study.phase.task.not.exist': 'pc_gwnl_error_study_phaseTaskNotExist',
  'apis.study.config.status.already.finish': 'pc_gwnl_error_study_alreadyFinish',
  'apis.study.phase.validation.id.notEmpty': 'pc_gwnl_error_study_phaseIdNotEmpty',
  'apis.study.config.validation.result.notFound': 'pc_gwnl_error_study_notFound',
  'apis.study.config.validation.currentPositionIdNotEmpty': 'pc_gwnl_error_study_currentPositionIdNotEmpty',
  'apis.series.catalog.name.existed': 'pc_gwnl_error_series_catalogNameExisted',
  'apis.udp.position.id.validation.notFound': 'pc_gwnl_error_position_notFound',
  'apis.eval.evaluationConfig.notExisted': 'pc_gwnl_error_eval_notExisted',
  'apis.eval.arrange.validation.evaluation.notEnough': 'pc_gwnl_error_eval_notEnough',
  'apis.eval.evaluationNotify.notExisted': 'pc_gwnl_error_eval_NotifyNotExisted',
  'gwnl.apis.studyConfig.not.ready.to.publish': 'pc_gwnl_error_study_notReadyToPublish',
  'apis.eval.evaluationUser.notEmpty': 'pc_gwnl_error_eval_userNotEmpty',
  'gwnl.apis.studyConfig.evaluation.inprogress.exists': 'pc_gwnl_error_study_evaluationInProgress',
  'apis.catalog.existed.reference': 'pc_gwnl_error_series_catalogExistReference',
  'apis.series.grade.validation.name.same': 'pc_gwnl_error_series_gradeNameSame',
  'apis.series.grade.existed.reference': 'pc_gwnl_error_series_gradeExistReference',
  'apis.polestar.series.existed.reference': 'pc_gwnl_error_series_existedReference',
  'apis.series.name.existed': 'pc_gwnl_error_series_nameExisted',
  'apis.series.catalog.name.notBlank': 'pc_gwnl_error_series_catalogNameNotBlank',
  'apis.series.name.notBlank': 'pc_gwnl_error_series_nameNotBlank',
  'apis.eval.evaluationUser.validation.jq.notBlank': 'pc_gwnl_error_eval_jqNotBlank',
  'apis.study.config.nextPosition.alreadyExistedInUserSeries': 'pc_gwnl_error_study_alreadyExistedInUserSeries.',
  'apis.study.phase.validation.name.notEmpty': 'pc_gwnl_error_study_phaseNameNotEmtpy',
  'apis.study.config.nextPosition.same': 'pc_gwnl_error_study_nextPositionSame',
  'apis.kng.knowledge.NotExist': 'pc_gwnl_error_study_kngNotExist',
  'apis.talent.seriesGrade.validation.name.notBlank': 'pc_gwnl_error_series_gradeNameNotBlank',
  'apis.talent.org.setting.validation.name.same': 'pc_gwnl_error_setting_nameSame',
  'apis.talent.orgSetting.priority.is.used': 'pc_gwnl_error_setting_used',
  'apis.talent.PositionCatalog.notFound': 'pc_gwnl_error_position_catalogNotFound',
  'apis.skill.catalog.is.deleted': 'pc_gwnl_error_skill_catalogDeleted',
  'apis.eval.survey.notFound': 'pc_gwnl_error_survey_notFound',
  'apis.series.notFound': 'pc_gwnl_error_series_notFound',
  'apis.eval.evaluationResult.skill.export.nodata': 'pc_gwnl_error_eval_resultExportNoData',
  'apis.study.task.knowledge.repeat.validation.exists': 'pc_gwnl_error_study_kngExist',
  'apis.talent.evaluation.personality.user.notEmpty': 'pc_gwnl_error_eval_personalityUserNotEmpty',
  'apis.series.catalog.existed.reference': 'pc_gwnl_error_series_catalogExistReference',
  'apis.polestar.question.validate.name.existed': 'pc_gwnl_eval_import_msg_question_questionNameAlreadyExist',
  'apis.talent.skill.name.notFound': 'pc_gwnl_eval_import_msg_question_skillNotExist',
  'apis.talent.skill.cloud.enable': 'pc_gwnl_error_skillDisabled',
  'apis.talent.knowledge.cloud.notFound': 'pc_gwnl_error_kngDisabled',
  'apis.talent.duplicated.name.skill.found': 'pc_gwnl_eval_import_msg_question_skillNameDuplicated',
  'apis.eval.survey.question.name.duplicated': 'pc_gwnl_eval_import_msg_question_questionDuplicated',
  'apis.talent.user.email.notBlank': 'pc_gwnl_error_sendReportNoEmail',
  'apis.talent.evaluation.available.num.not.enough': 'pc_gwnl_eval_available_num_not_enough',

  'apis.talent.studyConfigModel.name.exist': 'pc_gwnl_study_plan_template_msg_nameExist',
  'apis.talent.studyConfigModel.task.notFound': 'pc_gwnl_study_plan_template_msg_planNoTask',
  'apis.talent.studyConfigModel.phase.notFound': 'pc_gwnl_study_plan_template_msg_planNoPhase',
  'apis.eng.qu.validation.edit.status.Illegal': 'pc_gwnl_eval_error_question_published',
  'apis.eval.survey.name.existed': 'pc_gwnl_eval_questionnaire_nameExist',
  'apis.talent.studyConfigModel.phaseName.illegal': 'pc_gwnl_course_msg_checkStageName',
  'apis.talent.skillModel.name.existed': 'pc_gwnl_model_template_msg_repeatName',
  'apis.talent.skillModel.status.noReleased': 'pc_gwnl_model_template_msg_notReleased',
  'apis.talent.skillModel.status.deleted': 'pc_gwnl_model_template_msg_deleted',
  'apis.talent.skillModel.status.deletedOrReleased': 'pc_gwnl_model_template_msg_deletedOrReleased',
  'apis.talent.studyConfigModel.notFound': 'pc_gwnl_error_study_notFound',
  'apis.talent.studyConfigModel.not.ready.to.publish': 'pc_gwnl_study_plan_template_msg_publishUnsuccessfully',
  'apis.ote.exam.NotFound': 'pc_gwnl_study_plan_template_msg_examNotFound',
  'apis.talent.exam.has.deleted': 'pc_gwnl_study_exam_template_msg_deleted',
  'api.talent.practice.publish.fail': 'pc_gwnl_practice_publish_fail',
  'apis.talent.position.studyconfig.is.published': 'pc_gwnl_position_studyconfig_published',
  'apis.talent.category.name.conflict': 'pc_gwnl_error_skill_tip_catalogDuplication',
  'api.talent.training.validation.endDate.illegal': 'pc_gwnl_error_training_endDate_illegal',
  'apis.exam.export.sendMail.msg': 'pc_gwnl_eval_end_generateReport',
  'api.talent.series.permission.group.existed': 'pc_gwnl_permission_msg_groupNameExist',
  'apis.eval.evaluation.parentid.notBlank': 'apis_eval_evaluation_parentid_notBlank',
  'apis.eval.evaluation.save.failed': 'apis_eval_evaluation_save_failed',
  'apis.eval.evaluation.arrange.depth.overrun': 'apis_eval_evaluation_arrange_depth_overrun',
  'apis.eval.evaluation.notFound': 'pc_gwnl_eval_apis_eval_evaluation_notFound',
  'apis.eval.evaluationConfig.notNull': 'pc_gwnl_eval_apis_eval_evaluationConfig_notNul',
  'apis.eval.survey.surveyId.notEmpty': 'pc_gwnl_eval_apis_eval_survey_surveyId_notEmpty',
  'apis.eval.evaluation.expired.deferred': 'pc_gwnl_eval_apis_eval_evaluation_expired_deferred',
  'apis.eval.personality.avaliable.not.abundant': 'pc_gwnl_eval_apis_eval_personality_quota_Insufficient',
  'api.eval.before.error': 'pc_eval_anteroposterior_test_again_later',
  'apis.eval.publish.schedule.error': 'eval_apis_eval_publish_schedule_error',
  'apis.eval.survey.publish.error': 'pc_eval_questionnaire_publishing_failed',
  'apis.eval.survey.skill.relation.empty': 'pc_eval_capability_item_release',
  'apis.eval.eng.duplicate.request': 'pc_eval_eng_duplicate_request', // 27612201;400;已经提交成功，无需重复提交
  'apis.eval.eng.service.internal.error': 'pc_eval_eng_service_internal_error', // 27612202;400;服务内部错误.
  'apis.eval.eng.productGroup.validation.productName.blank': 'pc_eval_eng_productGroup_validation_productName_blank', // 27612204;400;产品名称不能为空.
  'apis.eval.eng.productGroup.validation.orgId.blank': 'pc_eval_eng_productGroup_validation_orgId_blank', // 27612205;400;机构不能为空.
  'apis.eval.eng.productGroup.validation.levelId.null': 'pc_eval_eng_productGroup_validation_levelId_null', // 27612206;400;层级不能为空.
  'apis.eval.eng.productGroup.validation.sceneId.null': 'pc_eval_eng_productGroup_validation_sceneId_null', // 27612207;400;场景不能为空.
  'apis.eval.eng.productGroup.validation.toolIdList.empty': 'pc_eval_eng_productGroup_validation_toolIdList_empty', // 27612208;400;工具不能为空.
  'apis.eval.eng.productGroup.validation.reportIdList.empty': 'pc_eval_eng_productGroup_validation_reportIdList_empty', // 27612209;400;报告不能为空.
  'apis.eval.eng.productGroup.product.exist': 'pc_eval_productGroup_product_exist', // 27612210;400;此测评产品已存在，名称为：{0}  ，无需再组合，请直接使用.
  'apis.eval.eng.productGroup.package.not.exist.or.offline': 'pc_eval_productGroup_package_not_exist_or_offline', // 27612211;400;本配置对应的产品不存在或已下线.
  'apis.eval.eng.productGroup.buyinfo.not.exist': 'pc_eval_productGroup_buyinfo_not_exist', // 27612212;400;未查询到购买信息，请先购买后重试.
  'apis.eval.eng.productGroup.quantity.not.enough': 'pc_eval_productGroup_quantity_not_enough', // 27612213;400;份额不足.
  'apis.eval.eng.tas.execute.failure': 'pc_eval_tas_execute_failure', // 27612203;400;服务调用异常.
  // zx
  'apis.eval.product.group.surveyId.notEmpty': 'pc_eval_product_group_surveyId_notEmpty', // 27612100;400;组合产品编号不能为空.
  'apis.eval.product.group.tool.notEmpty': 'pc_eval_product_group_tool_notEmpty', // 27612101;400;组合产品测评工具不能为空.
  'apis.eval.product.group.report.notEmpty': 'pc_eval_product_group_report_notEmpty', // 27612102;400;组合产品测评报告不能为空.
  'apis.eval.product.group.tool.notExisted': 'pc_eval_product_group_tool_notExisted', // 27612103;400;测评工具不存在.
  'apis.eval.product.group.report.notExisted': 'pc_eval_product_group_report_notExisted', // 27612104;400;测评报告不存在.
  'apis.eval.product.group.validation.name.maxsize': 'pc_eval_product_group_validation_name_maxsize', // 27612106;400;组合产品名称长度最大不能超过50.
  'apis.eval.product.group.validation.name.minsize': 'pc_eval_product_group_validation_name_minsize', // 27612107;400;组合产品名称长度最小不能小于5.
  'apis.eval.product.group.validation.name.notEmpty': 'pc_eval_product_group_validation_name_notEmpty', // 27612108;400;组合产品名称不能为空.
  'apis.eval.product.group.validation.purpose.notEmpty': 'pc_eval_product_group_validation_purpose_notEmpty', // 27612109;400;组合产品场景不能为空.
  'apis.eval.product.group.validation.level.notEmpty': 'pc_eval_product_group_validation_level_notEmpty', // 27612110;400;组合产品层级不能为空.
  'apis.eval.product.group.tool.report.relation.notEmpty': 'pc_eval_product_group_tool_report_relation_notEmpty', // 7612111;400;{0},未选择对应的测评报告，请选择对应的测评报告.
  'apis.eval.product.group.tool.amount.too.small': 'pc_eval_product_group_tool_amount_too_small', // 7612112;400;{0} 可用额度为0，请购买后重试.
  'apis.eval.product.group.tool.report.amount.too.small': 'pc_eval_product_group_tool_amount_too_small', // 7612113;400;{0} 可用额度为0，请购买后重试.
  'apis.eval.product.group.tool.amount.notEnough': 'pc_eval_product_group_tool_amount_notEnough', // 7612114;400;测评工具份额不足，请购买后重试.
  'apis.eval.product.group.report.amount.notEnough': 'pc_eval_product_group_report_amount_notEnough', // 7612115;400;测评报告份额不足，请购买后重试.
  'apis.eval.survey.amount.notEnough': 'pc_eval_survey_amount_notEnough', // 7612116;400;{0} 组合产品份额不足.
  'apis.eval.survey.amount.notExisted': 'pc_eval_survey_amount_notExisted', // 7612117;400;组合产品份额不存在，禁止调整份额.
  'apis.eval.survey.status.published': 'pc_eval_survey_status_published', // 7612118;400;组合产品已发布，禁止重复组合.
  'apis.eval.product.group.validation.purpose.notMapping': 'pc_eval_product_group_validation_purpose_notMapping', // 27612119;400;组合产品场景未配置.
  'apis.eval.product.group.validation.level.notMapping': 'pc_eval_product_group_validation_level_notMapping', // 27612120;400;组合产品层级未配置.
  'apis.eval.product.group.validation.oper.notSupport': 'pc_eval_product_group_validation_oper_notSupport', // 27612121;400;组合产品操作不支持.
  'apis.eval.jobQualification.skillMap.notFound': 'pc_eval_product_group_validation_unbound_qualification'
};

export const sendErrMsg = (error, merge = {}) => {
  if (!error || !error.error || !error.error.key) {
    return;
  }
  const key = error.error.key;
  let msg = merge ? merge[key] || errKeyMsg[key] : errKeyMsg[key];
  const apiMsg = error.error.message;
  if (!msg && merge.default) {
    msg = merge.default;
  }
  if (msg || apiMsg) {
    const hasKey = i18n && i18n.t(msg) && i18n.t(msg) !== msg; // 判断是否有key
    Vue.prototype.$message.error(hasKey ? i18n.t(msg) : apiMsg);
  } else {
    Vue.prototype.$message.error(i18n('pc_gwnl_error_tip_operationFailure'));
  }
};
