import {evalApi} from '../../../api';

// 问卷基本信息
export const getSurveyBasicInfo = (userRelationId) => {
  return evalApi.get(`client/survey/answerbasic/${userRelationId}`);
};

// 问卷答题分页
export const getQuestionsList = (surveyId, params) => {
  return evalApi.post(
    `client/survey/answerbackview/${surveyId}`,
    {},
    { params }
  );
};
// 分页保存答卷记录
export const saveSurveyRecord = (data) => {
  return evalApi.post('client/survey/answercommit', data);
};
// 提交问卷
export const submitSurvey = (data) => {
  return evalApi.post('client/survey/answer', data);
};
// 查看答卷获取答卷人的账号信息
export const getUserRelationInfo = (evalUserRelationId) => {
  return evalApi.get(`/client/survey/userrelation/${evalUserRelationId}`);
};

// 答案校验
export const evalAnswerCheck = (data) => {
  return evalApi.post('/client/survey/answercheck', data);
};

// 批量答题未完成第几题
export const evalBatchAnswerCount = (data) => {
  return evalApi.post('/client/survey/batch/answercount', data);
};

// 批量获取答题信息
export const evalBatchAnswerBackview = (data) => {
  return evalApi.post('/client/survey/batch/answerbackview', data);
};

// 批量分页提交答案
export const evalBatchAnswerCommit = (data) => {
  return evalApi.post('/client/survey/batch/answercommit', data);
};

// 批量交卷
export const evalBatchAnswer = (data) => {
  return evalApi.post('/client/survey/batch/answer', data);
};

export const evalTaskStartPage = (data) => {
  return evalApi.post('/client/evaluations/evalTaskStartPage', data);
};
// 答题页用户基本信息
export const getUserInfo = (evaluationUserId) => {
  return evalApi.get(`client/survey/answeruse/${evaluationUserId}`);
};
// 开始答卷
export const checkSurveyLeftTime = (data) => {
  return evalApi.post('client/survey/answeruse', data);
};
// 获取评估类型
export const getEvalDetail = (data) => {
  return evalApi.post('/client/evaluations/getEvalDetail', data);
};
// 行为评估开始答题页
export const postBehaviorAnswerList = (data) => {
  return evalApi.post('/client/behavior/answerList', data);
};
// 获取善则测评个人信息
export const getEvaluationsPersonalitySelfInfo = () => {
  return evalApi.get('client/evaluations/personalityselfinfo');
};
// 填写善则测评个人信息
export const putEvaluationsPersonalityUserInfo = (evaluationId, data) => {
  return evalApi.put(`client/evaluations/${evaluationId}/userinfo`, data);
};
// 行为评估答题详情页面接口
export const postBehaviorAnswerBackView = (data) => {
  return evalApi.post('/client/behavior/answerBackView', data);
};
// 行为评估-开始答题接口
export const postSubmitBehavior = (data) => {
  return evalApi.post('/client/behavior/submitBehavior', data);
};
// 行为评估分页提交答题记录
export const postBehaviorCommitRecord = (data) => {
  return evalApi.post('/client/behavior/commitRecord', data);
};
