<template>
  <div class="pwidth100">
    <yxt-popover
      trigger="hover"
      placement="top"
      effect="dark"
      :disabled="deptName === '' || deptName === null"
      show-scroll
      max-width="300"
      max-height="200"
      popper-class="gwnl-eval-dept-info"
      class="eval-dept-info__popover"
      :class="{'eval-dept-info__popover-center': isCenter}"
    >
      <div
        slot="reference"
        :class="['ellipsis','inline-block','maxW100p','top14',cellPosition==='static'?'pos-static':'pos-absolute']"
      >
        <yxtbiz-dept-name :name="deptName | deptNameFormatter | emptyFormatter" />
      </div>
      <yxtbiz-dept-name :name="deptName | deptNameFormatter | emptyFormatter" />
    </yxt-popover>
  </div>
</template>

<script>
export default {
  filters: {
    deptNameFormatter(v) {
      const deptNameArr = v ? v.split('->') : [];
      return deptNameArr[deptNameArr.length - 1] || '';
    },
    emptyFormatter(v) {
      return v === '' || v === null || v === undefined ? '--' : v;
    }
  },
  props: {
    deptName: String,
    cellPosition: {
      type: String,
      default: 'absolute'
    },
    isCenter: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style lang="scss">
.gwnl-eval-dept-info {
  padding: 6px 8px !important;
}

.eval-dept-info__popover {
  .el-popover__reference-wrapper {
    display: flex;
    justify-content: flex-start;
  }

  &-center {
    .el-popover__reference-wrapper {
      justify-content: center;
    }
  }
}
</style>
