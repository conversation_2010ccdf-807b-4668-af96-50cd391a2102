<template>
  <div
    class="survey__card"
    :class="{'survey_card-multiple': type !== 'single'}"
  >
    <div v-show="type === 'single'" class="userInfo-box mb16">
      <p class="tips-p">
        {{ isDetail ? $t('pc_eval_lbl_answer' /* 答卷人 */) : $t('pc_gwnl_eval_lbl_assessee').d('被评估人') }}
      </p>
      <div class="img-box">
        <img
          v-if="userInfo.imgUrl"
          alt
          :src="userInfo.imgUrl"
          class="user-img"
        >
        <div
          v-else
          class="img-deflut user-img"
        >
          {{ userInfo.fullname | userName }}
        </div>
      </div>
      <p class="user-name">
        <yxtbiz-user-name :name="userInfo.fullname" />
      </p>
      <p class="user-des">
        <dept-cell :dept-name="userInfo.deptName" cell-position="static" is-center />
        <span v-if="userInfo.positionName" class="mt4 inline-block">{{ userInfo.positionName }}</span>
      </p>

      <!-- 管理端设置是否允许查看自评答卷配置 -->
      <div v-if="!isDetail && !isSelfAssess && quesInfo.selfAssess && quesInfo.userSelfRelationId" class="mt24 text-center">
        <yxt-tooltip
          :content="$t('pc_eval_msg_self_not_complete' /* 自评未完成 */)"
          placement="top"
          :disabled="!!quesInfo.userSelfFinished"
        >
          <yxt-button :disabled="!quesInfo.userSelfFinished" @click="getSelfDetail(userInfo)">{{ $t('pc_eval_lbl_get_detail_answer' /* 查看自评答卷 */) }}</yxt-button>
        </yxt-tooltip>
      </div>
    </div>
    <div
      v-if="!isDetail"
      class="survey__card-progress flex-space-between"
      :class="{'mb16': timeLimit > 0}"
    >
      <span>{{ $t('pc_gwnl_web_lbl_survey_progress') }}</span>
      <span><i>{{ finishedCount }}</i>/{{ $t('pc_gwnl_web_lbl_survey_count',{totalCount}) }}</span>
      <yxtf-progress
        :percentage="answeredPercent"
        :stroke-width="8"
        color="#52C41A"
      />
    </div>
    <div
      v-if="timeLimit > 0 && !isDetail"
      class="survey__card-time flex-space-between"
    >
      <span>{{ leftTime }}</span>
      <span>{{ $t('pc_gwnl_web_lbl_survey_counting_down') }}</span>
    </div>

    <!-- 批量作答时右侧的人员信息 -->
    <div v-if="type !== 'single' && quesInfo.selfAssess && pageUserList[0] && pageUserList[0].userSelfRelationId" class="eval-card-user-list">
      <div v-for="user in pageUserList" :key="user.userId" class="align-items-center justify-content-between mb24">
        <div class="align-items-center flex-1">
          <yxt-avatar
            class="flex-shrink-0"
            size="24px"
            bg-type="gray"
            :img-url="user.imgUrl"
            :username="user.fullname"
          />

          <div class="ml8 flex-1-width0">
            <yxt-tooltip :content="user.fullname" placement="top" open-filter>
              <div class="ellipsis width100">{{ user.fullname || '-' }}</div>
            </yxt-tooltip>

            <yxt-tooltip :content="user.username" placement="top" open-filter>
              <div class="font-size-12 color-gray-7 ellipsis width100">{{ user.username || '-' }}</div>
            </yxt-tooltip>
          </div>
        </div>

        <yxt-tooltip
          :content="$t('pc_eval_msg_self_not_complete' /* 自评未完成 */)"
          placement="top"
          :disabled="!!user.userSelfFinished"
        >
          <yxt-button
            class="ml8"
            size="small"
            :disabled="!user.userSelfFinished"
            @click="getSelfDetail(user, true)"
          >
            {{ $t('pc_eval_lbl_get_detail_answer' /* 查看自评答卷 */) }}
          </yxt-button>
        </yxt-tooltip>
      </div>

      <div class="text-center">
        <yxt-pagination
          prev-next-no-border
          jumper-no-text
          simple-total-page
          :current-page.sync="pager.current"
          :page-size="pager.pageSize"
          layout="prev, jumper, total, next"
          :total="userList.length"
        />
      </div>
    </div>
    <yxt-dialog
      :visible.sync="showNotify"
      width="400px"
      :title="$t('pc_eval_show_tip' /* 提示 */)"
      :cutline="true"
      :show-close="false"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div>{{ $t('pc_gwnl_web_lbl_survey_timeout_tip') }}</div>
      <div class="mt20 count-down-num">{{ countDown }}</div>
    </yxt-dialog>
  </div>
</template>
<script>
import deptCell from './deptCell';

export default {
  components: {
    deptCell
  },
  filters: {
    userName(val) {
      if (!val) {
        return '';
      }
      let str = '';
      str = val.substr(val.length - 1, 1);
      return str;
    }
  },
  props: {
    userInfo: {
      type: Object
    },
    timeLimit: {
      type: Number
    },
    totalCount: {
      type: Number
    },
    finishedCount: {
      type: Number
    },
    type: {
      type: String,
      default: 'single'
    },
    quesInfo: {
      type: Object,
      default: () => {}
    },
    userList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showNotify: false,
      limitTimer: null,
      countTimer: null,
      countDown: 5,
      countingTime: 0,
      pager: {
        current: 1,
        pageSize: 6
      }
    };
  },
  computed: {
    leftTime() {
      return this.formatTime(this.countingTime);
    },
    answeredPercent() {
      return this.totalCount ? Math.floor(((this.finishedCount) / this.totalCount) * 100) : 0;
    },
    // 是否是管理端查看页
    isDetail() {
      return !!this.$route.query.mgmt;
    },
    // 是否是自评
    isSelfAssess() {
      return this.$route.query.evaluatorType === '1';
    },

    pageUserList() {
      return this.userList.slice((this.pager.current - 1) * this.pager.pageSize, this.pager.current * this.pager.pageSize);
    }
  },
  watch: {
    timeLimit(val) {
      if (val > 0) {
        this.countingTime = val;
        this.countingDown();
      }
      val = val + '';
      return val.substr(val.length - 1, 1);
    }
  },
  beforeDestroy() {
    this.limitTimer && clearInterval(this.limitTimer);
    this.limitTimer = null;
    this.countTimer && clearInterval(this.countTimer);
    this.countTimer = null;
  },
  methods: {
    formatTime(time) {
      const minute = Math.floor(time / 60);
      const second = time - minute * 60;
      return this.$t('pc_gwnl_web_lbl_survey_format_time', { minute, second });
    },
    countingDown() {
      this.limitTimer && clearInterval(this.limitTimer);
      this.limitTimer = setInterval(() => {
        if (this.countingTime) {
          this.countingTime--;
        } else {
          clearInterval(this.limitTimer);
          this.handleTimeOut();
        }
      }, 1000);
    },
    handleTimeOut() {
      this.showNotify = true;
      this.countTimer && clearInterval(this.countTimer);
      this.countTimer = setInterval(() => {
        if (this.countDown) {
          this.countDown--;
        } else {
          clearInterval(this.countTimer);
          this.showNotify = false;
          this.$emit('submit', false, true);
        }
      }, 1000);
    },

    // 查看自评答卷
    getSelfDetail(userInfo, isBatch = false) {
      this.$emit('get-user-detail', { ...userInfo, isBatch });
    }
  }
};
</script>

<style lang="scss" scoped>
.userInfo-box {
  min-height: 208px;
  padding: 20px;
  background-color: white;
  border-radius: 4px;

  .tips-p {
    color: #262626;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
  }

  .img-box {
    margin-top: 4px;
    margin-bottom: 11px;
    text-align: center;

    .user-img {
      width: 64px;
      height: 64px;
      border-radius: 50%;

      &.img-deflut {
        display: inline-block;
        color: #fff;
        font-weight: 400;
        font-size: 20px;
        line-height: 64px;
        text-align: center;
        background: #b0b7d2;
      }
    }
  }

  .user-name {
    overflow: hidden;
    color: #262626;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
  }

  .user-des {
    display: -webkit-box;
    margin-top: 4px;
    overflow: hidden;
    color: #8c8c8c;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    word-wrap: wrap;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}

.survey_card-multiple {
  width: 282px !important;
}

.survey__card {
  width: 220px;
  margin-left: 24px;
  font-size: 14px;

  .survey__card-time {
    height: 90px;
    padding: 16px 0;
    background-color: white;
    border-top: 3px solid #fa8c16;
    border-radius: 4px;

    span:nth-child(1) {
      color: #fa8c16;
      font-size: 24px;
    }

    span:nth-child(2) {
      color: #595959;
    }
  }

  .survey__card-progress {
    align-items: flex-start;
    height: 136px;
    padding: 16px 20px 24px;
    background-color: white;

    span:nth-child(1) {
      color: #262626;
      font-size: 12px;
    }

    span:nth-child(2) {
      color: #8c8c8c;

      i {
        color: #262626;
        font-size: 22px;
      }
    }
  }

  .eval-card-user-list {
    width: 100%;
    margin-top: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 4px;
  }
}

.count-down-num {
  font-size: 18px;
  text-align: center;
}
</style>
