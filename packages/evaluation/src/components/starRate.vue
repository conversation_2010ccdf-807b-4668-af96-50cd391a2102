<template>
  <div class="gwnl-flex justify-content-between mb12">
    <template v-for="(item, index) in question.prjQuOptions">
      <scale-option-tips-tooltip
        :key="index"
        :disabled="!item.optionTip"
        :value="index === activeindex"
        popper-class="gwnl-square-item-tips"
        effect="dark"
        max-width="500"
        :options="question.prjQuOptions"
        :content="item.optionTip"
        :index="index"
        :manual="true"
        :active-index="activeindex"
      >
        <yxt-svg
          :key="index"
          width="32px"
          height="32px"
          :disabled="disabled"
          :remote-url="remoteUrl"
          icon-class="star"
          class="color-gray-4 hand"
          :class="{
            stratDisable: disabled,
            'a-link': hoverIndex >= index,
            'color-gray-4':
              hoverOption.optionNum === undefined ||
              hoverOption.optionNum < index
          }"
          @mouseenter.native="optionEnter(item)"
          @click.native="select(item, index)"
        />
      </scale-option-tips-tooltip>
    </template>
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
import ScaleOptionTipsTooltip from './scaleOptionTipsTooltip';
import {staticBaseUrl} from '../../../examing/src/configs/const';
export default {
  components: {
    ScaleOptionTipsTooltip
  },
  mixins: [Emmiter],
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      changeindex: this.question.userOptionId ? 1 : 0,
      selectedIndex: '',
      activeindex: '',
      value: null,
      hoverOption: {}
    };
  },
  computed: {
    hoverIndex() {
      return this.question.prjQuOptions.findIndex(
        (index) => index === this.selectedIndex
      );
    },
    remoteUrl() {
      return staticBaseUrl + '/ufd/b0174a/evaluation/pc/svg/eval';
    }
  },
  watch: {
    'question.userOptionId': {
      handler: function(newVal, oldVal) {
        if (newVal) {
          this.changeindex += 1;
          if (this.changeindex === 1) {
            this.$emit('statusChanged', 1);
          }
        } else {
          this.changeindex = 0;
          this.$emit('statusChanged', -1);
        }
      }
    },
    question: {
      immediate: true,
      deep: true,
      handler: function(newVal) {
        const matched = newVal.prjQuOptions.findIndex(
          (option) => option.id === newVal.userOptionId
        );
        this.activeindex = matched > -1 ? matched : null;
      }
    },
    activeindex: function(val) {
    }
  },
  mounted() {
    this.getSelected();
  },

  methods: {
    getSelected() {
      if (!this.activeindex && this.activeindex !== 0) return;
      this.hoverOption = this.question.prjQuOptions.find(
        (item) => item.id === this.question.userOptionId
      );
      this.selectedIndex = this.hoverOption;
    },
    select(item, index) {
      if (this.disabled) return;
      this.selectedIndex = item;
      this.activeindex = index;
      this.change(index + 1);
    },
    optionEnter(item) {
      if (this.hasSelected || this.disabled) return;
      this.hoverOption = item;
    },
    getOptionIdByValue(value) {
      return this.question.prjQuOptions[value - 1].id;
    },
    change(val) {
      this.question.userOptionId = this.getOptionIdByValue(val);
      this.handleClick(this.getOptionIdByValue(val));
    },
    handleClick(pid) {
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.star-rate {
  ::v-deep .yxt-rate__icon {
    font-size: 25px;
  }
}

.stratDisable {
  pointer-events: auto;
  cursor: not-allowed;
}
</style>
