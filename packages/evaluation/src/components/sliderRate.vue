<template>
  <div class="gwnl-questionnaire-slider-bar-box">
    <yxt-slider
      v-model="value"
      :show-tooltip="false"
      :disabled="disabled"
      :min="question.optionStart"
      :step="1"
      :max="question.prjQuOptions.length + question.optionStart - 1"
      class="w570"
      @change="change(value)"
    />
    <div class="tips-box">
      <template v-for="(item, index) in question.prjQuOptions">
        <scale-option-tips-tooltip
          :key="index"
          :disabled="!item.optionTip"
          :value="index === value - question.optionStart"
          popper-class="gwnl-square-item-tips"
          effect="dark"
          max-width="500"
          :options="question.prjQuOptions"
          :content="item.optionTip"
          :index="index"
          :manual="true"
          :active-index="value - 1"
        >
          <div
            class="gwnl-questionnaire-slider-bar-item"
            :class="{ active: index === value - 1 }"
          >
            {{ item.optionTip }}
            {{ index === value - 1 }} sliderValue:{{ sliderValue }} index:{{
              index
            }}
          </div>
        </scale-option-tips-tooltip>
      </template>
    </div>
  </div>
</template>

<script>
import Emmiter from '../mixins/emmiter';
import ScaleOptionTipsTooltip from './scaleOptionTipsTooltip';
export default {
  components: {
    ScaleOptionTipsTooltip
  },
  mixins: [Emmiter],
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      value: null,
      sliderValue: null
    };
  },
  watch: {
    'question.userOptionId': {
      handler: function(newVal, oldVal) {
        if (oldVal === '' && newVal !== '') {
          this.$emit('statusChanged', 1);
        }
      }
    },
    question: {
      immediate: true,
      deep: true,
      handler: function(newVal) {
      }
    },
    value: function(val) {
      this.question.userOptionId = this.getOptionIdByValue(val);
    }
  },
  mounted() {
    if (this.question.userOptionId) {
      const matched = this.question.prjQuOptions.find(
        (option) => option.id === this.question.userOptionId
      );
      this.value = matched.optionNum + this.question.optionStart;
    } else {
      this.change(this.question.optionStart);
    }
  },
  methods: {
    getOptionIdByValue(value) {
      value = value - this.question.optionStart;
      const matched = this.question.prjQuOptions.find(
        (option) => option.optionNum === value
      );
      return matched.id;
    },
    change(value) {
      this.value = value;
      this.changed(this.getOptionIdByValue(value), this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.gwnl-questionnaire-slider-bar-box {
  position: relative;
  width: 100%;
  padding: 0 15px;

  .tips-box {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    width: 100%;

    .gwnl-questionnaire-slider-bar-item {
      position: relative;
      top: 0;
      z-index: -1;
      width: 30px;
      height: 5px;
      background: red;
      opacity: 0;
    }
  }
}
</style>
