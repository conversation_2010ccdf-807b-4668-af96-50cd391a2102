<template>
  <div>
    <yxt-dialog
      :title="$t('pc_eval_tip_abnormalOperation_title').d('异常操作提示')"
      :visible.sync="abnormalOperateVisible"
      width="480px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div>
        <p>
          {{ $t('pc_eval_tip_abnormalOperation_content').d('系统检测您可能存在评估异常操作（连续选择相同得分的选项），是否进行检查修改？') }}
        </p>
        <div v-if="answerPersons && answerPersons.length" class="min-pheight-16 mt12">
          <p class="font-weight-500 color-gray-10 max-height-132 over-auto">
            {{ $t('pc_eval_lbl_involvePeople').d('涉及人员') }}：{{ answerPersons.join('、') }}
          </p>
        </div>
      </div>
      <span slot="prefixFooter">
        <yxtf-checkbox v-model="stopAlarm">{{ $t('pc_eval_tip_stopAlarm_again').d('本次评估不再提示') }}</yxtf-checkbox>
      </span>
      <span slot="footer" class="dialog-footer">
        <yxtf-button type="primary" size="medium" @click="handleCloseTip">{{ $t('pc_gwnl_global_msg_IKnow').d('我知道了') }}</yxtf-button>
      </span>
    </yxt-dialog>
  </div>
</template>
<script>
export default {
  props: {
    type: Number, // 1:单人评价 2：多人评价
    timeCtrlCount: Number
  },
  data() {
    return {
      abnormalOperateVisible: false,
      stopAlarm: false,
      answerPersons: []
    };
  },

  methods: {
    // 警示判定提示
    handleShowAbnormal(persons) {
      this.stopAlarm = false;
      this.answerPersons = persons;
      this.abnormalOperateVisible = true;
    },

    // 无效问卷判定提示
    handleShowUseless(answerPersons, isDuration) {
      const _this = this;
      const h = this.$createElement;
      this.$msgbox({
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false,
        title: this.$t('pc_eval_tip_abnormalOperation_title').d('异常操作提示'),
        message: isDuration
          ? h('div', [
            // 您本次评估的作答时长过短，问卷被判断为无效问卷。该问卷要求作答时长不能少于{0}分钟，请重新认真评估后，再进行提交。
            h('p', {}, _this.$t('pc_eval_invalid_duration', [this.timeCtrlCount]))
          ])
          : h('div', [
            h('p', {}, _this.$t('pc_eval_tip_useless_content').d('经系统检测，您本次评估存在大量答案选项一致的题目，问卷被判断为无效问卷，请重新评估后再进行提交')),
            h('p', { class: ['font-weight-500', 'color-gray-10', 'max-height-140', 'over-auto', 'mt-12_imp', { 'd-none': !answerPersons.length }] },
              _this.$t('pC_eval_ 1b1_involvepeople').d('涉及人员') + '：' + answerPersons.join('、'))
          ]),
        showCancelButton: false,
        confirmButtonText: this.$t('pc_gwnl_global_msg_IKnow').d('我知道了')
      });
    },

    handleCloseTip() {
      this.abnormalOperateVisible = false;
      this.$emit('stopAlarm', this.stopAlarm);
    }
  }
};
</script>
