<template>
  <div class="mb12">
    <yxt-radio-group v-model="question.userOptionId" class="gwnl-flex font14" @change="handleClickOption">
      <div v-for="(title,tIndex) in question.prjQuOptions" :key="tIndex" class="flex-space-between flex-1 mr8">
        <div class="color-gray-9 lh22">
          {{ title.optionTip || $t('pc_eval_input_text').d('请输入描述文字') }}
        </div>
        <div class="mt24">
          <yxt-radio :disabled="disabled" :label="title.id" hide-label />
        </div>
      </div>
    </yxt-radio-group>
  </div>
</template>

<script>
import Emmiter from '../mixins/emmiter';
export default {

  mixins: [Emmiter],
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      hasSelected: false,
      hoverOption: {},
      activeindex: 0,
      value: null
    };
  },
  computed: {
    hoverIndex() {
      return this.question.prjQuOptions.findIndex(
        (option) => option.id === this.hoverOption.id
      );
    }
  },
  watch: {
    'question.userOptionId': {
      handler: function(newVal, oldVal) {
        if (oldVal === '' && newVal !== '') {
          this.$emit('statusChanged', 1);
        }
      }
    },
    value: function(val) {
      this.question.userOptionId = this.getOptionIdByValue(val);
    }
  },

  mounted() {
    this.getOptionIdByIndex();
  },

  methods: {
    getSelected() {
      if (this.question.userOptionId) {
        const matched = this.question.prjQuOptions.find(
          (option) => option.id === this.question.userOptionId
        );
        this.value = matched.optionNum + this.question.optionStart;
      } else {
        this.change(this.question.optionStart);
      }
    },
    getOptionIdByIndex(i) {
      const matched = this.question.prjQuOptions.findIndex(
        (option) => option.id === this.question.userOptionId
      );
      this.activeindex = matched > -1 ? matched : null;
    },
    handleClickOption(pid) {
      console.log(pid);
      this.getOptionIdByIndex(pid);
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.a-link {
  color: var(--color-primary-6) !important;
}

.gwnl-square-item {
  &:hover {
    color: #fff;
    background: var(--color-primary-6);
  }

  &.disabled {
    cursor: not-allowed;
  }
}
</style>
