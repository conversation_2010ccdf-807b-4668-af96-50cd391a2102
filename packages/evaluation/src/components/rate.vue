<template>
  <div class="rate-content mt32">
    <component
      :is="question.quStyle | scaleType"
      :question="question"
      :disabled="disabled"
      :changed="change"
      @statusChanged="statusChanged"
    />
    <div v-if="question.quStyle!=='A03.01.04' && question.quTip" class="gwnl-flex justify-content-between color-gray-9 standard-size-14">
      <span class="tip-wrapper">{{ question.quTip.split(';')[0] }}</span>
      <span class="tip-wrapper">{{ question.quTip.split(';')[1] }}</span>
    </div>
  </div>
</template>

<script>
import starScale from './starRate';
import slideScale from './sliderRate';
import NPSScale from './rateNps';
import rateScale from './rateRadio';

const NPS_SCALE = 'A01.09.01';
const STAR_SCALE = 'A01.09.02';
const SLIDE_SCALE = 'A01.09.07';
const RADIO_SCALE = 'A03.01.04';
const SCALEMAP = {
  [NPS_SCALE]: 'NPSScale',
  [STAR_SCALE]: 'starScale',
  [SLIDE_SCALE]: 'slideScale',
  [RADIO_SCALE]: 'rateScale'
};
export default {
  components: {
    NPSScale,
    starScale,
    slideScale,
    rateScale
  },
  filters: {
    scaleType(type) {
      return SCALEMAP[type];
    }
  },
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  methods: {
    statusChanged(status) {
      this.$emit('statusChanged', status);
    },
    change() {
      this.changed(...arguments);
    }
  }
};

</script>
<style lang="scss" scoped>
  .rate-content {
    width: 100%;
  }
</style>
