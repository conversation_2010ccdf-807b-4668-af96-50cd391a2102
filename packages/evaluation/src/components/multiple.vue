<template>
  <yxtf-checkbox-group
    v-model="question.userOptionId"
    direction="row"
    :disabled="disabled"
    @change="handleClick"
  >
    <yxtf-checkbox
      v-for="option in question.prjQuOptions"
      :key="option.id"
      :label="option.id"
    >
      <span
        v-dompurify-html="option.optionTitleFull"
        class="question-item-label"
      ></span>
    </yxtf-checkbox>
  </yxtf-checkbox-group>
</template>
<script>
import Emmiter from '../mixins/emmiter';

export default {
  mixins: [Emmiter],
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      changeindex: this.question.userOptionId.length === 0 ? 0 : 1
    };
  },
  watch: {
    'question.userOptionId': {
      handler: function(newVal, oldVal) {
        if (newVal.length && newVal.length > 0) {
          this.changeindex += 1;
          if (this.changeindex === 1) {
            this.$emit('statusChanged', 1);
          }
        } else {
          this.changeindex = 0;
          this.$emit('statusChanged', -1);
        }
      }
    }
  },
  methods: {
    handleClick(pid) {
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.question-item-label {
  flex: 1;
  word-wrap: break-word;
}
</style>
