<template>
  <div :class="['flex-row-center', 'mt64',multi?'survey-multi__footer':'survey__footer']">
    <template v-if="pager.pageSize !== -99">
      <yxt-button
        v-show="pager.current !== 1"
        @click="goPre"
      >
        {{ $t('pc_gwnl_web_lbl_survey_btn_last') }}
      </yxt-button>
      <yxt-button v-show="multi || answering" @click="goSave">
        {{ $t('pc_gwnl_global_btn_save') }}
      </yxt-button>
      <yxt-button
        v-show="pager.pages > pager.current"
        type="primary"
        @click="goNext"
      >
        {{ $t('pc_gwnl_web_lbl_survey_btn_next') }}
      </yxt-button>
      <yxt-button
        v-show="pager.pages === pager.current && !submited"
        type="primary"
        :loading="disableSubmit"
        @click="submit"
      >
        {{ $t('pc_gwnl_web_btn_submitBtn') }}
      </yxt-button>
    </template>
    <template v-else>
      <yxt-button
        v-show="pager.current !== 2"
        @click="goPre"
      >
        {{ $t('pc_gwnl_web_lbl_survey_btn_last') }}
      </yxt-button>
      <yxt-button
        v-show="pager.pages >= pager.current"
        type="primary"
        @click="goNext"
      >
        {{ $t('pc_gwnl_web_lbl_survey_btn_next') }}
      </yxt-button>
      <yxt-button
        v-show="pager.pages < pager.current && !submited"
        type="primary"
        :loading="disableSubmit"
        @click="submit"
      >
        {{ $t('pc_gwnl_web_btn_submitBtn') }}
      </yxt-button>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    submited: {
      type: Boolean
    },
    pager: {
      pages: {
        default: 0,
        type: Number
      },
      current: {
        default: 0,
        type: Number
      },
      pageSize: {
        type: Number
      }
    },
    disableSubmit: {
      default: false,
      type: Boolean
    },
    // 多人答题
    multi: {
      type: Boolean,
      default: false
    },
    answering: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    goPre() {
      this.$emit('goPre');
    },
    goNext() {
      this.$emit('goNext');
    },
    goSave() {
      this.$emit('goSave');
    },
    submit() {
      this.$emit('submit', true, 2);
    }
  }
};
</script>

<style lang='scss' scoped>
.survey__footer {
  .yxt-button {
    width: 180px;
    height: 40px;
  }
}

.survey-multi__footer {
  .yxt-button {
    width: 132px;
    height: 40px;
  }
}
</style>
