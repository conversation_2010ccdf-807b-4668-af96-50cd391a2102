<!-- 评估详情 -->
<template>
  <div class="eval-evaluation-detail">
    <!-- 左侧人员区域 -->
    <div v-if="showLeftUserInfo" class="eval-evaluation-detail__left">
      <div class="ml8 mr24 mb16">
        <yxt-input
          v-model="searchName"
          size="small"
          :placeholder="$t('pc_gwnl_eval_pleaseEnterNameSearch' /* 请输入姓名搜索 */)"
          clearable
          searchable
          @blur="blurSearch"
          @search="searchUser"
          @clear="clearUser"
        />
      </div>

      <yxt-scrollbar class="eval-detail-dialog__scrollbar">
        <div class="eval-detail-dialog__scrollbar">
          <div
            v-for="user in userInfoList"
            :key="user.id"
            class="align-items-center eval-evaluation-detail__left--user"
            :class="{'bg-gray-2': user.id === currentUserId}"
            @click="clickUserInfo(user)"
          >
            <yxt-avatar
              size="small"
              :img-url="user.imgUrl"
              :username="user.fullName"
              class="shrink0"
            />

            <yxtf-tooltip :content="user.fullName" placement="top" open-filter>
              <span class="ml12 color-gray-10 lh22 ellipsis">{{ user.fullName }}</span>
            </yxtf-tooltip>
          </div>
        </div>
      </yxt-scrollbar>
    </div>

    <div v-if="showLeftUserInfo" class="eval-evaluation-detail__divider"></div>

    <!-- 右侧指标区域 -->
    <div class="eval-evaluation-detail__right" :class="{'ml24': showLeftUserInfo}">
      <yxt-scrollbar class="eval-evaluation-detail__right--scrollbar">
        <yxtf-table :data="userTableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)" class="eval-evaluation-detail__right--table">
          <!-- 题号 -->
          <yxtf-table-column
            prop="sort"
            :label="$t('pc_eval_lbl_subject_index')"
            width="80"
          />
          <!-- 指标名称 -->
          <yxtf-table-column
            prop="skillName"
            :label="$t('pc_eval_lbl_metric_name')"
            min-width="120"
          >
            <template slot-scope="{row}">
              <div class="eval-table-row">
                <yxt-tooltip
                  :content="row.skillName"
                  open-filter
                  class="eval-table-row__name"
                  placement="top"
                  :max-width="500"
                >
                  <span class="ellipsis-3">{{ row.skillName }}</span>
                </yxt-tooltip>
              </div>
            </template>
          </yxtf-table-column>
          <!-- 标准 -->
          <yxtf-table-column
            v-if="isShowStandard"
            prop="standardName"
            :label="$t('pc_gwnl_chart_legend_standardLevel')"
            width="160"
          />
          <!-- 当前评级 -->
          <yxtf-table-column
            prop="isAnswer"
            :label="$t('pc_eval_lbl_current_rating')"
            width="180"
          >
            <template slot-scope="{ row }">
              <span>{{ row.userLeval.name || $t('pc_gwnl_pos_lbl_not_assess' /* 未评估 */) }}</span>
              <!-- 匹配 ｜ 未匹配 -->
              <yxt-tag
                v-if="row.isAnswer && isShowStandard"
                size="mini"
                :type="row.isPass ? 'success' : 'danger'"
                class="ml8"
                effect="light"
              >
                {{ $t(row.isPass ? 'pc_gwnl_pos_lbl_match' : 'pc_gwnl_pos_lbl_not_match') }}
              </yxt-tag>
            </template>
          </yxtf-table-column>
        </yxtf-table>
      </yxt-scrollbar>

      <div class="eval-evaluation-detail__right--pager">
        <yxt-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="userTableData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'EvaluationDetail',
  props: {
    userInfoAll: {
      type: Object,
      default: () => {}
    },
    skillList: {
      type: Array,
      default: () => {}
    },
    isShowStandard: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      searchName: '',
      currentUserId: '', // 当前正选中的学员
      userTableData: [],

      userInfoList: [],
      currentPage: 1,
      pageSize: 10
    };
  },

  computed: {
    // 是否展示左侧人员信息
    showLeftUserInfo() {
      return this.userInfoAll.length > 1;
    }
  },

  created() {
    this.userInfoList = this.userInfoAll;
    this.setCurrentUser(this.userInfoAll[0]);
  },

  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
    },
    handleCurrentChange(current) {
      this.currentPage = current;
    },

    // 设置公共的user方法
    setCurrentUser(user) {
      this.currentUserId = user.id;
      this.userTableData = this.skillList[this.currentUserId];
    },

    // 点击每个user信息
    clickUserInfo(user) {
      this.currentPage = 1;
      this.pageSize = 10;
      this.setCurrentUser(user);
    },

    blurSearch() {
      this.searchName = this.searchName.trim();
    },

    // 搜索学员名称
    searchUser() {
      this.blurSearch();

      if (!this.searchName) {
        this.clearUser();
        return;
      }

      this.userInfoList = this.userInfoAll.filter(item => {
        return item.fullName.includes(this.searchName);
      });
    },

    // 清空学员搜索
    clearUser() {
      this.userInfoList = this.userInfoAll;
    }
  }
};
</script>
