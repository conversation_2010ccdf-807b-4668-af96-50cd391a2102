<template>
  <div>
    <yxtf-dialog
      :title="$t('pc_gwnl_web_pleaseEnterBasicInfo')"
      :visible.sync="dialogVisible"
      width="640px"
      append-to-body
      :before-close="cancel"
      class="gwnl-evaldialog"
    >
      <div>
        <div class="mb24 gwnl-evaldialog-info">
          <div class="gwnl-evaldialog-info__left">
            <span class="color-gray-7">{{ $t('pc_gwnl_web_assessor') }}：</span>
            <span class="color-gray-10 ellipsis">{{ info.userName }}</span>
          </div>
          <div class="ml40 gwnl-evaldialog-info__right">
            <span class="color-gray-7">{{ $t('pc_gwnl_web_evalName') }}：</span>
            <span class="color-gray-10 ellipsis gwnl-evaldialog-name">{{
              info.evaluationName
            }}</span>
          </div>
        </div>
        <yxtf-form
          ref="form"
          :model="form"
          label-width="80px"
          :rules="rules"
        >
          <yxtf-form-item :label="$t('pc_gwnl_web_lbl_gender')" prop="gender">
            <yxtf-select
              v-model="form.gender"
              :placeholder="$t('pc_gwnl_web_pleaseChooseGender')"

              class="w240"
            >
              <yxtf-option
                :label="$t('pc_gwnl_web_male')"
                :value="$t('pc_gwnl_web_male')"
              />
              <yxtf-option
                :label="$t('pc_gwnl_web_female')"
                :value="$t('pc_gwnl_web_female')"
              />
            </yxtf-select>
          </yxtf-form-item>
          <yxtf-form-item :label="$t('pc_gwnl_web_age')" prop="age">
            <yxtf-select
              v-model="form.age"
              :placeholder="$t('pc_gwnl_web_pleaseChooseAge')"

              class="w240"
            >
              <yxtf-option
                v-for="item in 38"
                :key="item + 17"
                :label="item + 17"
                :value="item + 17"
              />
            </yxtf-select>
          </yxtf-form-item>
          <yxtf-form-item
            :label="$t('pc_gwnl_jq_lbl_function')"
            prop="functionName"
          >
            <yxtf-select
              v-model="form.functionName"
              :placeholder="$t('pc_gwnl_global_msg_selectFunction')"

              class="w240"
            >
              <yxtf-option
                v-for="(item, index) in functions"
                :key="index"
                :label="item.name"
                :value="item.name"
              />
            </yxtf-select>
          </yxtf-form-item>
          <yxtf-form-item :label="$t('pc_gwnl_web_lbl_post')" prop="postName">
            <yxtf-input
              v-model="form.postName"
              maxlength="20"
              :placeholder="$t('pc_gwnl_web_pleaseEnterPost')"

              class="w240"
            />
          </yxtf-form-item>
          <yxtf-form-item
            :label="$t('pc_gwnl_web_lbl_education')"
            prop="education"
          >
            <yxtf-select
              v-model="form.education"
              :placeholder="$t('pc_gwnl_web_pleaseSelectEducation')"

              class="w240"
            >
              <yxtf-option
                v-for="(item, index) in educations"
                :key="index"
                :label="item.name"
                :value="item.name"
              />
            </yxtf-select>
          </yxtf-form-item>
          <yxtf-form-item
            :label="$t('pc_gwnl_web_lbl_currentTenure')"
            prop="workingTime"
          >
            <yxtf-select
              v-model="form.workingTime"
              :placeholder="$t('pc_gwnl_web_pleaseSelectCurrentTenure')"
              class="w240"
            >
              <yxtf-option
                v-for="(item, index) in workingTimes"
                :key="index"
                :label="item.name"
                :value="item.name"
              />
            </yxtf-select>
          </yxtf-form-item>
        </yxtf-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <yxtf-button @click="cancel">
          {{ $t('pc_gwnl_global_btn_cancel') }}
        </yxtf-button>
        <yxtf-button type="primary" @click="submit">
          {{ $t('pc_gwnl_web_btn_startEval') }}
        </yxtf-button>
      </div>
    </yxtf-dialog>
  </div>
</template>

<script>
import {putEvaluationsPersonalityUserInfo} from 'yxt-ulcd-sdk/packages/evaluation/src/service/eval.service';

export default {
  name: '',
  props: {
    isView: {
      type: Number,
      default: 0
    },
    info: {
      type: Object,
      default: () => {
        return {};
      }
    },
    selfInfo: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    const checkGender = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('pc_gwnl_web_pleaseChooseGender')));
      } else {
        callback();
      }
    };
    const checkAge = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('pc_gwnl_web_pleaseChooseAge')));
      } else {
        callback();
      }
    };
    const checkPostName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('pc_gwnl_web_pleaseEnterPost')));
      } else {
        callback();
      }
    };
    const checkFunctionName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('pc_gwnl_global_msg_selectFunction')));
      } else {
        callback();
      }
    };
    const checkEducation = (rule, value, callback) => {
      if (!value) {
        return callback(new Error(this.$t('pc_gwnl_web_pleaseSelectEducation')));
      } else {
        callback();
      }
    };
    const checkWorkingTime = (rule, value, callback) => {
      if (!value) {
        return callback(
          new Error(this.$t('pc_gwnl_web_pleaseSelectCurrentTenure'))
        );
      } else {
        callback();
      }
    };

    return {
      dialogVisible: false,
      form: {
        gender: '',
        age: '',
        functionName: '',
        postName: '',
        education: '',
        workingTime: ''
      },
      rules: {
        gender: [{ validator: checkGender, trigger: 'change' }],
        age: [{ validator: checkAge, trigger: 'change' }],
        functionName: [{ validator: checkFunctionName, trigger: 'change' }],
        postName: [{ validator: checkPostName, trigger: 'blur' }],
        education: [{ validator: checkEducation, trigger: 'change' }],
        workingTime: [{ validator: checkWorkingTime, trigger: 'change' }]
      },
      functions: [
        { name: this.$t('pc_gwnl_web_sale') },
        { name: this.$t('pc_gwnl_web_accounting') },
        { name: this.$t('pc_gwnl_web_personnelMatters') },
        { name: this.$t('pc_gwnl_web_administration') },
        { name: this.$t('pc_gwnl_web_customerService') },
        { name: this.$t('pc_gwnl_web_manager') },
        { name: this.$t('pc_gwnl_web_market') },
        { name: this.$t('pc_gwnl_web_production') },
        { name: this.$t('pc_gwnl_web_purchase') },
        { name: this.$t('pc_gwnl_web_engineering') },
        { name: 'IT' },
        { name: this.$t('pc_gwnl_web_consultingService') },
        { name: this.$t('pc_gwnl_web_notApplicable') },
        { name: this.$t('pc_gwnl_jq_other') }
      ],
      educations: [
        { name: this.$t('pc_gwnl_web_highSchool') },
        { name: this.$t('pc_gwnl_web_juniorCollege') },
        { name: this.$t('pc_gwnl_web_undergraduate') },
        { name: this.$t('pc_gwnl_web_bachelor') },
        { name: this.$t('pc_gwnl_web_master') },
        { name: this.$t('pc_gwnl_web_doctor') }
      ],
      workingTimes: [
        { name: this.$t('pc_gwnl_web_lessThanOneYear') },
        { name: this.$t('pc_gwnl_web_oneToThreeYear') },
        { name: this.$t('pc_gwnl_web_moreThanThreeYear') }
      ]
    };
  },
  methods: {
    show() {
      if (this.isView) {
        // 预留查看善泽问卷逻辑
      } else {
        this.dialogVisible = true;
        if (this.selfInfo.age !== 0) {
          this.form = this.selfInfo;
        }
      }
    },
    submit() {
      this.form.postName = this.form.postName.trim();
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = {
            age: this.form.age,
            education: this.form.education,
            functionName: this.form.functionName,
            gender: this.form.gender,
            leaderFlag: 0,
            leaderTime: '',
            postName: this.form.postName,
            workingTime: this.form.workingTime
          };

          putEvaluationsPersonalityUserInfo(this.info.evaluationId, data)
            .then(() => {
              window.open(this.info.evaluationUrl, '_blank');
              this.dialogVisible = false;
            })
            .catch((err) => {
              this.$handleError(err);
            });
        } else {
          return false;
        }
      });
    },
    cancel() {
      this.dialogVisible = false;
      this.$refs.form.resetFields();
    }
  }
};
</script>
<style lang="scss" scoped>
.gwnl-evaldialog {
  ::v-deep .yxtf-dialog__title {
    font-weight: 500;
  }

  .gwnl-evaldialog-info {
    height: 22px;
  }

  span {
    display: inline-block;
    vertical-align: middle;
  }

  .gwnl-evaldialog-info__left {
    display: inline-block;
    max-width: 154px;
    line-height: 22px;
  }

  .gwnl-evaldialog-info__left > span:last-child {
    max-width: 98px;
  }

  .gwnl-evaldialog-info__right {
    display: inline-block;
    width: calc(100% - 194px);
    line-height: 22px;
  }

  .gwnl-evaldialog-name {
    display: inline-block;
    width: calc(100% - 70px);
    vertical-align: middle;
  }
}
</style>
