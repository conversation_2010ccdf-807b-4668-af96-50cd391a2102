<template>
  <yxtf-radio-group
    v-model="question.userOptionId"
    direction="row"
    :disabled="disabled"
    @change="handleClick"
  >
    <yxtf-radio
      v-for="option in question.prjQuOptions"
      :key="option.id"
      :label="option.id"
    >
      <span
        v-dompurify-html="option.optionTitleFull"
        class="question-item-label"
      ></span>
      <!-- {{ option.optionTitleFull }} -->
    </yxtf-radio>
  </yxtf-radio-group>
</template>
<script>
import Emmiter from '../mixins/emmiter';
export default {
  mixins: [Emmiter],
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },

  watch: {
    'question.userOptionId': {
      handler: function(newVal, oldVal) {
        if (oldVal === '' && newVal !== '') {
          this.$emit('statusChanged', 1);
        }
      }
    }
  },
  methods: {
    handleClick(pid) {
      this.changed(pid, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.change');
    }
  }
};
</script>
<style lang="scss" scoped>
.question-item-label {
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
}
</style>
