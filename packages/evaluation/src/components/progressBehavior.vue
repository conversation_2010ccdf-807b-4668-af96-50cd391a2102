<template>
  <div>
    <div v-if="progressType===1">
      <!-- 被评估人 -->
      <div>
        <div v-if="showUserAndNewProgress && userInfo" class="userInfo-box mb16">
          <p class="tips-p">
            {{ $t('pc_gwnl_eval_lbl_assessee').d('被评估人') }}
          </p>
          <div class="img-box">
            <img
              v-if="userInfo.imgUrl"
              alt
              :src="userInfo.imgUrl"
              class="user-img"
            >
            <div
              v-else
              class="img-deflut user-img"
            >
              {{ userInfo.fullName | userName }}
            </div>
          </div>
          <p class="user-name">
            <yxtbiz-user-name :name="userInfo.fullName" />
          </p>
          <p class="user-des">
            <dept-cell :dept-name="userInfo.deptName" cell-position="static" is-center />
            <span v-if="userInfo.positionName" class="mt4 inline-block">{{ userInfo.positionName }}</span>
          </p>
        </div>
      </div>
      <!-- 原来是只有多次行为评估才展示新的评估样式 -->
      <!--  5.3 添加单次行为评估也使用新的评估样式 -->
      <div v-if="['1', '4'].includes(String(evaluationType))" class="w282 color-gray-10 bg-white">
        <div class="align-items-center pt20 pb20 pl16">
          <!-- 评估进度 -->
          <span class="mr12 font14">{{ $t('pc_eval_lbl_evaluation_progress').d('评估进度') }}</span>
          <yxtf-progress :percentage="100 / topicData.total * getPageNum">
            <span slot="content" class="color-gray-8">{{ getPageNum }}/{{ $t('pc_gwnl_web_lbl_survey_count', { totalCount: topicData.total }) }}</span>
          </yxtf-progress>
        </div>
        <div class="pos-relative">
          <div class="left-circle"></div>
          <div class="order-line"></div>
          <div class="right-circle"></div>
        </div>
        <!-- 评估概览 -->
        <div>
          <div class="pt16 pb16 pl16 pr16 border-bottom-gray-4">
            <p class="font18 font-weight-500 mb12">
              {{ $t('pc_eval_lbl_evaluation_overview').d('评估概览') }}
            </p>

            <!-- 单人评估 -->
            <template v-if="showUserStyle">
              <div class="align-items-center">
                <div class="w12 h12 yxt-br-2 mr8 bg-color-d7f7c1"></div>
                <span>{{ $t('pc_gwnl_pos_lbl_match').d('匹配') }}</span>
                <div class="w12 h12 yxt-br-2 mr8 ml24 bg-color-fef2f0"></div>
                <span>{{ $t('pc_gwnl_pos_lbl_not_match').d('未匹配') }}</span>
                <div class="w12 h12 yxt-br-2 mr8 ml24 border-d9"></div>
                <span>{{ $t('pc_gwnl_pos_lbl_not_assess').d('未评估') }}</span>
              </div>
            </template>
            <!-- 批量评估 -->
            <template v-else>
              <div class="align-items-center">
                <div class="w12 h12 yxt-br-2 mr8 bg-color-c7d9ff"></div>
                <span>{{ $t('pc_gwnl_web_evaluated').d('已评估') }}</span>
                <div class="w12 h12 yxt-br-2 mr8 ml24 bg-white border-d9"></div>
                <span>{{ $t('pc_gwnl_pos_lbl_not_assess').d('未评估') }}</span>
              </div>
            </template>
          </div>

          <div class="pt24 pb8 pl24 pr24">
            <div class="align-items-center layout-flex-wrap">
              <div
                v-for="(item,index) in answerOverview"
                :key="index"
                class="mb16 eval-answer-card__item"
                :style="getBackStyle(item)"
                @click="handleToCheck(item)"
              >
                <span :class="['lh24', 'font12', item.isAnswer ? '' : 'color-gray-9']">{{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="order-line"></div>
        <!-- 评估详情 -->
        <div class="pv16 ph16">
          <yxt-button
            type="second"
            class="width100"
            :loading="loadingDetail"
            @click.native="showEvaluationDetail"
          >
            {{ $t('pc_eval_lbl_evaluation_detail' /* 评估详情 */) }}
          </yxt-button>
        </div>
      </div>
      <!-- 答题进度-原 -->
      <div v-else class="assess_aside mb16">
        <span class="font14">{{ $t('pc_eval_lbl_evaluation_progress').d('评估进度') }}</span>
        <div class="topicnum mt8 mb4">
          <span>{{ params.offset }}</span>
          <span> /{{ $t('pc_gwnl_web_lbl_survey_count', { totalCount: topicData.total }) }}</span>
        </div>
        <yxtf-progress
          :stroke-width="8"
          :percentage="100 / topicData.total * params.offset"
          :show-text="false"
          class="width100"
          status="success"
        />
      </div>
    </div>

    <!-- 评估详情组件 -->
    <yxt-dialog
      width="960px"
      :visible.sync="visibleDetail"
      custom-class="eval-detail-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- title -->
      <div slot="title" class="eval-evaluation-detail__title">{{ $t('pc_eval_tit_view_evaluation_details' /* 查看评估详情 */) }}</div>
      <!-- content -->
      <EvaluationDetail v-if="visibleDetail" :user-info-all="userInfoAll" :skill-list="skillList" :isShowStandard="isShowStandard" />
    </yxt-dialog>
  </div>
</template>

<script>
import deptCell from './deptCell';
import EvaluationDetail from './evaluationDetail';

export default {
  data() {
    return {
      progressType: 1,
      visibleDetail: false,
      loadingDetail: false
    };
  },
  components: {
    deptCell,
    EvaluationDetail
  },
  computed: {
    getPageNum() {
      const offset = this.params.offset;
      const viewPage = this.answerOverview.length ? this.answerOverview.filter(item => item.isAnswer === 1).length : 0;
      return offset >= viewPage ? offset : viewPage;
    },

    // 展示评估人和新的评估进度样式
    showUserAndNewProgress() {
      return (String(this.multiple) === '0' || this.self);
    },

    // 判断最大允许跳转为未作答的第一题
    maxStepQues() {
      return this.answerOverview.findIndex(answer => !answer.isAnswer);
    },

    showUserStyle() {
      return this.showUserAndNewProgress && this.isShowStandard;
    }
  },
  filters: {
    userName(val) {
      if (!val) {
        return '';
      }
      let str = '';
      str = val.substr(val.length - 1, 1);
      return str;
    }
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    },
    userInfoAll: {
      type: Object,
      default: () => {}
    },
    topicData: {
      type: Object,
      default: () => {}
    },
    params: {
      type: Object,
      default: () => {}
    },
    multiple: {
      type: String,
      default: ''
    },
    evaluationType: {
      type: String,
      default: '1'
    },
    answerOverview: {
      type: Array,
      default: () => []
    },
    self: {
      type: Boolean,
      default: false
    },
    skillList: {
      type: Array,
      default: () => {}
    },
    isShowStandard: {
      type: Number,
      default: 1
    }
  },

  methods: {
    handleToCheck(item) {
      if (item.isAnswer || item.sort <= this.maxStepQues) {
        this.$emit('check', item.sort);
        return;
      }

      this.$message.warning(this.$t('pc_eval_tip_answer_jump').d('未评估题目无法跳转，请逐题评估'));
    },
    getBackStyle(item) {
      // 未作答的展示
      if (item.isAnswer === 0) return 'border: 1px solid #d9d9d9';
      // 已作答&批量评估的展示
      if (item.isAnswer && !this.showUserStyle) return 'background-color: #c7d9ff;color: #436bff;';
      // 已通过｜未通过的展示
      return item.isPass === 1
        ? 'backgroundColor: #d7f7c1;color: #52c41b;'
        : 'background: #fef2f0;color: #fa5353;';
    },

    showEvaluationDetail() {
      this.loadingDetail = true;
      this.$emit('save-evaluation');
    },

    openVisibleDetail() {
      this.loadingDetail = false;
      this.visibleDetail = true;
    }
  }
};
</script>

<style lang="scss" scoped>
  .assess_aside {
    height: 122px;
    padding: 20px;
    color: #262626;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    background: #fff;

    .topicnum {
      :first-child {
        font-weight: 500;
        font-size: 22px;
        line-height: 30px;
      }

      :last-child {
        color: #8c8c8c;
        font-size: 14px;
        line-height: 22px;
      }
    }
  }

  .userInfo-box {
    min-height: 208px;
    padding: 20px;
    background-color: white;
    border-radius: 4px;

    .tips-p {
      color: #262626;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
    }

    .img-box {
      margin-top: 4px;
      margin-bottom: 11px;
      text-align: center;

      .user-img {
        width: 64px;
        height: 64px;
        border-radius: 50%;

        &.img-deflut {
          display: inline-block;
          color: #fff;
          font-weight: 400;
          font-size: 20px;
          line-height: 64px;
          text-align: center;
          background: #b0b7d2;
        }
      }
    }

    .user-name {
      overflow: hidden;
      color: #262626;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
    }

    .user-des {
      display: -webkit-box;
      margin-top: 4px;
      overflow: hidden;
      color: #8c8c8c;
      font-weight: 400;
      font-size: 12px;
      line-height: 18px;
      text-align: center;
      word-wrap: wrap;
      word-break: break-all;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }

  // bg为有圆角的白布
  .order-bg {
    position: relative;
    width: 100%;
    height: auto;
    background-color: #fff;
    border-radius: 50px;
  }

  // 用页面背景色的两个圆形直接定位到bg画布上 一左一右实现凹圆角
  .left-circle {
    position: absolute;
    top: -4px;
    left: -4px;
    width: 8px;
    height: 8px;
    background-color: #f5f5f5;
    border-radius: 50%;
  }

  .right-circle {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background-color: #f5f5f5;
    border-radius: 50%;
  }

  // 圆形距top的高度+圆形半径就是虚线所在的位置了
  .order-line {
    width: 100%;
    border: 1px dashed #bfbfbf;
  }

  .eval-answer-card__item {
    width: 24px;
    height: 24px;
    margin-right: 28.5px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;

    &:nth-of-type(5n) {
      margin-right: 0;
    }
  }
</style>
