<template>
  <div class="eval-error-page">
    <yxtf-svg
      v-if="isNoPer"
      width="714px"
      height="422px"
      icon-class="f_permission"
    />
    <yxtf-svg
      v-else-if="isEmpty"
      width="550px"
      height="401px"
      icon-class="f_empty"
    />
    <yxt-svg
      v-else
      :remote-url="mediaUrl"
      icon-class="exam-error"
      width="428px"
      height="253px"
    />

    <div class="font-size-24 mt24 lh32 color-gray-9 font-bolder">{{ errorMessage }}</div>

    <yxtf-button
      size="larger"
      plain
      class="mt32"
      @click="$emit('customClick')"
    >
      {{ $t('pc_eval_btn_return_eval_list' /* 返回测评列表 */) }}
    </yxtf-button>
  </div>
</template>

<script>
export default {
  name: 'ErrorPage',
  props: {
    errorMessage: String,
    isNoPer: {
      type: Boolean,
      default: false
    },
    isEmpty: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      mediaUrl: `${this.$staticBaseUrl}ufd/55a3e0/ote/pc/svg`
    };
  }
};
</script>

<style scoped>
.eval-error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding-top: 15vh;
  background-color: #fff;
}
</style>
