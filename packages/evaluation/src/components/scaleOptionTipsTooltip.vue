<template>
  <div class="tooltip-layer">
    <div class="tooltip-cont">
      <div
        v-if="value"
        class="tooltip-box"
        :class="countTipsseat(index)"
        :style="boxLineStyle"
      >
        <div v-if="!disabled" class="abc">
          {{ content }}
        </div>
      </div>
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { calculateTextLines } from '../utils';
export default {
  props: {
    activeIndex: {
      type: [Number, String],
      default: ''
    },
    options: {
      type: Array,
      default() {
        return [];
      }
    },
    disabled: {
      type: Boolean,
      default: true
    },
    value: {
      type: Boolean,
      default: true
    },
    content: {
      type: String,
      default: ''
    },
    index: {
      type: Number
    }
  },

  computed: {
    evalSvg() {
      return this.$evalSvg;
    },

    boxLineStyle() {
      const line = calculateTextLines(this.content, 440);
      return { top: `-${line * 44}px` };
    }
  },
  methods: {
    sliderchange(val) {
      let index = 0;
      const lenStep = (100 / (this.options.length - 1)).toFixed(3);
      if (val) {
        index = val / +lenStep;
      }
      this.changeprjQuOptions(Math.round(index));
      console.log(val, index, +lenStep);
    },
    countLeft(index) {
      if (!this.options.length) {
        return 1;
      }
      const lenStep = (510 / (this.options.length - 1)).toFixed(3);
      return lenStep * index;
    },
    countstep() {
      if (!this.options.length) {
        return 1;
      }
      const lenStep = (100 / (this.options.length - 1)).toFixed(3);
      return lenStep;
    },
    marksObject() {
      if (!this.options.length) {
        return { 0: '0' };
      }
      const marks = {};
      const len = this.options.length;
      const lenStep = (100 / (this.options.length - 1)).toFixed(3);
      console.log(this.options);
      this.options.forEach(function(item, index) {
        let key = lenStep * index;
        if (index === len - 1) {
          key = 100;
        }
        marks[key] = this.$t('pc_eval_lbl_score', [item.optionScore]);
      });
      console.log(marks, '[][][][]');
      return marks;
    },
    countTipsseat(index) {
      if (!this.options.length) {
        return 'top';
      }
      const len = this.options.length;
      const max = Math.round(len * 0.3) - 1;
      const min = Math.round(len * 0.6) - 1;
      // console.log(index, max, min)
      if (len <= 3) {
        if (index) {
          return 'top';
        } else {
          return 'top-start';
        }
      }
      if (index <= max) {
        return 'top-start';
      } else if (index > max && index < min) {
        return 'top';
      } else {
        return 'top-end';
      }
      // }
    },
    changeprjQuOptions(index) {
      this.activeIndex = index;
    }
  }
};
</script>

<style lang="scss" scoped>
.tooltip-cont {
  position: relative;

  .tooltip-box {
    position: absolute;
    width: 440px;

    .abc {
      position: relative;
      display: inline-block;
      width: auto;
      padding: 0 8px;
      color: #fff;
      line-height: 32px;
      background: #436bff;
      border-radius: 4px;

      &::before {
        position: absolute;
        bottom: -5px;
        left: 10px;
        display: block;
        border-color: transparent;
        border-style: solid;
        border-width: 5px;
        border-top-color: #436bff;
        border-bottom-width: 0;
        content: "";
      }
    }

    &.top {
      left: 50%;
      transform: translate(-50%, 0);

      .abc {
        left: 50%;
        transform: translate(-50%, 0);

        &::before {
          left: 50%;
          transform: translate(-50%, 0);
        }
      }
    }

    &.top-end {
      right: 0;
      left: unset;
      text-align: right;

      .abc {
        right: 0;
        left: unset;

        &::before {
          right: 12px;
          left: unset;
        }
      }
    }
  }
}

.gwnl-square-item-tips {
  z-index: 10 !important;
  background: #436bff !important;

  .popper__arrow::after {
    border-top-color: #436bff !important;
  }
}

.gwnl-questionnaire-slider-bar-box {
  position: relative;

  .tips-box {
    position: absolute;
    top: 0;
    left: -10px;
    width: 510px;

    .gwnl-questionnaire-slider-bar-item {
      position: absolute;
      top: 0;
      z-index: -1;
      width: 5px;
      opacity: 0;
    }
  }
}
</style>
