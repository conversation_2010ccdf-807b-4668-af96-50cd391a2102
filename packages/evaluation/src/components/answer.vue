<template>
  <div
    class="answer-item"
  >
    <yxt-input
      v-model="question.userOptionId"
      type="textarea"
      :placeholder="$t('pc_gwnl_web_pleaseEnter')"
      maxlength="2000"
      show-word-limit
      rows="4"
      :disabled="disabled"
      class="gwnl-input-none-border"
      @blur="handleClick"
    />
  </div>
</template>
<script>
import Emmiter from '../mixins/emmiter';
export default {
  mixins: [Emmiter],
  props: {
    question: Object,
    disabled: Boolean,
    changed: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      changeindex: this.question.userOptionId === '' ? 0 : 1
    };
  },
  watch: {
    'question.userOptionId': {
      handler: function(newVal, oldVal) {
        if (newVal.length && newVal.length > 0) {
          this.changeindex += 1;
          if (this.changeindex === 1) {
            this.$emit('statusChanged', 1);
          }
        } else {
          this.changeindex = 0;
          this.$emit('statusChanged', -1);
        }
      }
    }
  },
  methods: {
    handleClick(pid) {
      this.question.userOptionId = this.question.userOptionId.trim();
      this.changed(this.question.userOptionId, this.question.prop);
      this.dispatch('YxtFormItem', 'yxt.form.blur');
    }
  }
};
</script>

<style lang="scss" scoped>
.question-item-label {
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
}

.answer-item {
  width: 100%;
}
</style>
