<template>
  <div class="justify-content-left mt14 radius4 bg-f8f pt20 pb20 pl26 pr26 lh20">
    <div>
      <yxtf-tag size="mini">
        {{ $t('pc_gwnl_jq_analysis').d('解析') }}
      </yxtf-tag>
    </div>
    <div class="color-gray-9 ml10">
      {{ analyzeText }}
    </div>
  </div>
</template>
<script>
export default {
  props: {
    analyzeText: {
      type: String,
      default: ''
    }
  }
};
</script>
