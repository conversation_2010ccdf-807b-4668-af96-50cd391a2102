/* 题目 */
// NPS style
.gwnl-square-item {
  width: 32px;
  height: 32px;
  color: #595959;
  line-height: 32px;
  text-align: center;
  background-color: #f0f0f0;
  border-radius: 4px;

  &.active {
    color: #fff;
    background: var(--color-primary-6);
  }
}

.gwnl-questionnaire-question__divider {
  width: 100%;
  height: 0;
  border-bottom: 1px dashed #bfbfbf;
}

.gwnl-question-wrapper {
  position: relative;
  display: flex;
  margin-top: 12px;
  padding: 32px;
  overflow: hidden;
  background-color: #fff;
  cursor: move;

  &.disabled {
    cursor: pointer;
  }

  .yxt-input__inner {
    height: auto;
    padding: 0 !important;
    border: 1px solid transparent;

    &:focus {
      box-shadow: none;
    }
  }

  &.active {
    &::before {
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      height: 100%;
      background-color: #436bff;
      content: "";
    }
  }

  &:hover {
    box-shadow: 0 0 12px 0 rgb(0 0 0 / 12%);

    .gwnl-question-operation {
      right: 0;
    }
  }

  // 分割线wrapper
  &.gwnl-question-wrapper--divider {
    padding: 24px;

    &:hover {
      padding-right: 80px;
    }
  }

  // 备注说明wrapper
  &.gwnl-question-wrapper--remark {
    padding: 29px 64px;
  }

  .gwnl-question-num {
    min-width: 48px;
    margin-right: 20px;
    padding-right: 4px;
    color: #8c8c8c;
    font-weight: 500;
    text-align: right;
  }

  .gwnl-question-content {
    // min-width: 548px;
    max-width: 944px;
    margin-right: 100px;
  }

  .gwnl-question-margin {
    margin-right: -50px;
  }

  // 右侧操作栏
  .gwnl-question-operation {
    position: absolute;
    top: 0;
    right: -60px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 100%;
    background: rgb(250 250 250 / 50%);
    box-shadow: -1px 0 0 0 #e9e9e9;
    transition: right 0.5s;

    .color-gray-7 {
      cursor: pointer;

      &:hover {
        color: #436bff;
      }
    }
  }

  // 单选题
  .gwnl-question-item--single {
    margin-left: -52px;
    padding-right: 50px;
    padding-left: 40px;

    .drag-sort {
      position: absolute;
      right: -22px;
      display: none !important;
    }

    .gwnl-question-item-title {
      position: relative;
      padding: 9px 12px;
      border: 1px solid transparent;
      border-radius: 4px;
    }

    .yxt-input {
      width: auto;
    }
  }

  &:not(.disabled) .gwnl-question-item--single {
    &:hover {
      .drag-sort {
        left: -30px;
        display: inline-block !important;
      }

      .gwnl-question-item-title {
        border-color: #d9d9d9;
      }

      .gwnl-option-delete {
        display: block !important;
      }
    }

    .gwnl-question-item-title {
      position: relative;
      padding: 9px 12px;
      border: 1px solid transparent;
      border-radius: 4px;
    }

    .yxt-input {
      width: auto;
    }
  }

  .gwnl-option-delete {
    position: absolute;
    top: 10px;
    right: -32px;
    display: none !important;
  }

  .gwnl-question-item-score {
    position: relative;

    // width: 68px;
    height: 40px;
    padding-right: 12px;
    line-height: 40px;
    text-align: right;
    border: 1px solid transparent;
    border-radius: 4px;

    .yxt-input-number {
      width: 26px;
    }
  }

  .gwnl-tip-wrapper {
    display: inline-block;
    max-width: 200px;
    line-height: 22px;
  }

  .yxt-radio {
    margin-right: 4px;
  }

  .yxt-input {
    &.input--warning {
      ::v-deep .yxt-input__inner {
        border-color: red;
      }
    }
  }
}

// 问卷量表题进度条
.gwnl-questionnaire-slider-bar {
  position: relative;
  width: 100%;
  height: 8px;
  background-color: #e9e9e9;
  border-radius: 4px;

  .gwnl-questionnaire-slider-bar__inner {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: #436bff;
    border-radius: 4px;

    &::after {
      position: absolute;
      top: -6px;
      right: -8px;
      width: 16px;
      height: 16px;
      background-color: #436bff;
      border: 3px solid #fff;
      border-radius: 50%;
      box-shadow: -2px 1px 6px 0 rgb(0 0 0 / 27%);
      content: "";
    }
  }
}

// input 无边框
.gwnl-input-none-border {
  ::v-deep .yxt-input__inner {
    height: auto;
    padding: 0 !important;
    border: 1px solid transparent;

    &:focus {
      box-shadow: none;
    }
  }
}

// preview
.eval-ques-detail__preview {
  background: none !important;
}

.gwnl-questionnaire-preview {
  width: 100%;
  padding-top: 104px;
  background: url("https://stc.yxt.com/ufd/b0174a/evaluation/pc/img/questionnaire-bg.png") no-repeat top center;
  background-color: #fdfdfd;
  background-size: 1920px auto;

  &.gwnl-questionnaire-preview--noheader {
    padding-top: 48px;
  }

  .gwnl-questionnaire-preview-header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 56px;
    background-color: #fff;
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 4%);

    .tab-item {
      color: #595959;

      .yxt-svg-icon {
        color: #8c8c8c;
      }

      &.active {
        color: #435bff;

        .yxt-svg-icon {
          color: #435bff;
        }
      }

      & + .tab-item {
        margin-left: 45px;
      }
    }

    .gwnl-questionnaire-preview-header__right {
      position: absolute;
      top: 12px;
      right: 32px;
    }
  }

  .gwnl-questionnaire-preview-container__pc {
    position: relative;
    width: 660px;
    min-height: calc(100vh - 184px);
    padding: 64px 126px 168px 80px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 24px 0 rgb(166 168 175 / 16%);

    &.gwnl-questionnaire-preview-onepage {
      padding-bottom: 68px;
    }

    .gwnl-questionnaire-preview__empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .gwnl-questionnaire-preview-top {
      padding-bottom: 48px;
      border-bottom: 1px solid #e9e9e9;
    }

    .gwnl-questionnaire-preview-title {
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }

    .gwnl-questionnaire-preview-subtitle {
      margin-top: 24px;
    }

    .gwnl-questionnaire-preview-questions {
      .gwnl-questionnaire-preview-questions__item {
        display: flex;

        &:not(:first-child) {
          margin-top: 40px;
        }

        .gwnl-question-num {
          min-width: 20px;
          text-align: center;
        }

        .gwnl-question-content {
          width: 100%;
          padding-right: 100px;

          .gwnl-question-title-item {
            display: inline-block;
            flex-shrink: 0;
            min-width: 55px;
            vertical-align: top;

            &.gwnl-question-title-html {
              flex: 1;
            }

            &.gwnl-question-content_type {
              color: #262626;
              font-weight: 500;
            }
          }
        }

        .gwnl-questionnaire-question__remark {
          width: 100%;
          padding: 0 40px;
        }

        .yxt-radio {
          margin-right: 4px;
        }

        .gwnl-questionnaire-preview-option-title {
          margin-top: 16px;
        }

        .gwnl-question-margin {
          .yxt-radio__label {
            white-space: normal;
            word-break: break-all;
          }
        }

        .questionnaire-item_column {
          &:not(:last-child) {
            padding-bottom: 18px;
          }
        }

        .yxt-radio-group--row > label {
          line-height: 22px;
        }
      }

      .btn-area {
        position: absolute;
        bottom: 64px;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .gwnl-question-indicator__detail {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
  }

  .gwnl-questionnaire-scale-content {
    max-width: 592px;
  }
}
