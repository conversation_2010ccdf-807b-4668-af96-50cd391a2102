<template>
  <div class="width100">
    <div
      v-loading="loading"
      class="gwnl-questionnaire-preview gwnl-questionnaire-previewsubmit"
    >
      <yxtbiz-watermark
        :option="option"
        iframe-id="main"
      />
      <div
        class="gwnl-questionnaire-preview-container"
      >
        <div>
          <div class="gwnl-flex">
            <div
              id="playercontainer"
              class="gwnl-questionnaire-preview-container__pc"
            >
              <div
                id="main"
                ref="themeContainer"
              >
                <yxt-form
                  ref="form"
                  :model="form"
                  :inline-message="true"
                  class="gwnl-questionnaire-preview-questions"
                >
                  <template
                    v-for="item in questions"
                  >
                    <yxt-form-item
                      v-if="isQuestion(item)"
                      :key="item.id"
                      :prop="item.prop"
                      label-width="0px"
                      class="gwnl-questionnaire-preview-questions__item"
                    >

                      <div class="gwnl-question-index color-gray-7">
                        {{ item.quNum }}
                      </div>
                      <div class="gwnl-question-content gwnl-flex-1 pr">
                        <div class="standard-size-14 mb10 wordbreak">
                          <span
                            class="gwnl-question-title-item gwnl-question-content_type"
                          >
                            [{{ $t(QUESTION_TYPE_WORD.get(item.quType)) }}]</span>
                          <span
                            v-dompurify-html="item.quTitleFull"
                            class="gwnl-question-title-item gwnl-question-title-html ml4"
                          ></span>
                        </div>

                        <div v-if="surveyId && item.showIndicator && item.skillType" class="gwnl-question-indicator__detail standard-size-14 color-primary-6" @click="getIndicatorDetail(item)">
                          {{ $t('pc_eval_lbl_indicator_detail' /* 所属指标详情 */) }}
                          <yxt-svg width="16px" height="16px" icon-class="arrow-right" />
                        </div>

                        <div class="gwnl-question-content-item">
                          <component
                            :is="SURVEY_TYPE[item['quType']]"
                            :ref="`question${item.id}`"
                            :question="item"
                            :choose-tip="choosePersonTip"
                            :disabled="submited"
                            :changed="changeOptionMap"
                          />
                        </div>
                      </div>

                    </yxt-form-item>
                    <!--  备注  -->
                    <div
                      v-else-if="item.quType === 'A04.01'"
                      :key="item.id + 'A04.01'"
                      v-dompurify-html="item.quTitleFull"
                      class="gwnl-questionnaire-question__remark"
                    ></div>
                    <!-- 分割线 -->
                    <div
                      v-else-if="item.quType === 'A04.02'"
                      :key="item.id + 'A04.02'"
                      class="gwnl-questionnaire-question__divider"
                    ></div>
                  </template>
                </yxt-form>

                <survey-footer
                  class="eval-answer-footer"
                  :pager="pager"
                  :submited="submited"
                  :disable-submit="submitting"
                  :multi="true"
                  @goPre="uploadCheckAndSkip('prev')"
                  @goNext="uploadCheckAndSkip('next')"
                  @submit="submit"
                  @goSave="handleSavePage"
                />
              </div>
            </div>
            <div :class="{'card-hidde': isShowCard}">
              <progress-card
                v-if="!submited"
                type="multi"
                :user-info="userInfo_a"
                :time-limit="timeLimit"
                :total-count="totalCount"
                :finished-count="finishedCount"
                :user-list="userList"
                :ques-info="quesInfo"
                @submit="submit"
                @get-user-detail="goUserInfoDetail"
              />
            </div>
            <div
              v-if="!submited"
              class="fled"
            >
              <div class="content-center">
                <div
                  v-show="isShowCard"
                  class="card-box"
                >
                  <progress-card
                    type="multi"
                    :user-info="userInfo_a"
                    :time-limit="timeLimit"
                    :total-count="totalCount"
                    :finished-count="finishedCount"
                    :ques-info="quesInfo"
                    @get-user-detail="goUserInfoDetail"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <abnormal-dialog ref="abnormalDialog" :time-ctrl-count="timeCtrlCount" @stopAlarm="handleStopAlarm" />

    <yxtbiz-indicator-detail ref="indicatorDetail" :is-client="true" :title="indicatorTitle" />
  </div>
</template>

<script>
import { getSurveyBasicInfo, evalBatchAnswerCount, evalBatchAnswerBackview, evalBatchAnswerCommit, evalBatchAnswer, evalAnswerCheck } from './service/eval.service.js';
import Rate from './multiEvaluate/rate';
import Single from './multiEvaluate/single';
import multiple from './multiEvaluate/multiple';
import answer from './multiEvaluate/answer';
import trueFalse from './multiEvaluate/trueFalse';
import ProgressCard from './components/progressCard.vue';
import surveyFooter from './components/surveyFooter';
import abnormalDialog from './components/abnormalDialog.vue';
import { QUESTION_TYPE_ENUM, QUESTION_TYPE_WORD, SURVEY_TYPE } from './enum';
import upload from './multiEvaluate/upload';
import question from './mixins/question';
import {sendErrMsg} from 'yxt-ulcd-sdk/packages/evaluation/src/utils';
const isIE = () => {
  return !!((!!window.ActiveXObject || 'ActiveXObject' in window));
};
export default {
  mixins: [question],
  components: {
    Rate,
    Single,
    ProgressCard,
    surveyFooter,
    multiple,
    trueFalse,
    answer,
    abnormalDialog,
    upload
  },
  props: {
    queryData: {
      type: Object,
      defalut: ({})
    }
  },
  data() {
    return {
      isShowCard: false,
      questions: [],
      userInfo_a: {},
      surveyId: this.queryData.surveyId,
      relationId: this.queryData.relationId,
      evaluationId: this.queryData.evaluationId,
      qstnId: '',
      evaluatorType: this.queryData.evaluatorType,
      type: this.queryData.type,
      pageId: this.queryData.pageId,
      evaluationUserId: '',
      loading: false,
      finishedCount: 0,
      timeLimit: 0,
      totalCount: 0,
      submited: false,
      startTime: 0,
      submitTime: 0,
      pager: {
        pages: 0,
        pageSize: 1,
        current: 1,
        offset: 0,
        count: 0
      },
      isPageSymbol: false,
      form: {},
      // 数据校验规则
      rules: {},
      submitting: false,
      surveyStatus: 2,
      QUESTION_TYPE_WORD,
      SURVEY_TYPE,
      QUESTION_TYPE_ENUM,
      isVisitorKey: this.queryData.isVisit,
      relationList: JSON.parse(window.sessionStorage.getItem('relationList')) || [],
      relationIds: [],
      choosePersonTip: false,
      invalidPercent: 0,
      invalidSetting: 0,
      invalidSwitch: 0,
      invalidAction: 0,
      warnCount: 0,
      warnSwitch: 0,
      timeCtrlSwitch: 0,
      timeCtrlAction: 0,
      timeCtrlCount: 0,
      evaluationName: '',
      quesInfo: {},
      userList: [], // 批量作答时每个人的答题信息
      indicatorTitle: '',
      userInfo: {}
    };
  },
  created() {
    this.isVisitor();
    this.getSurveyDetail();
    window.addEventListener('scroll', this.handleScroll);
    // 兼容IE 皮肤切换处理
    if (isIE()) {
      this.themeContainer();
    }
  },
  watch: {
    // 监听用户答题行为进行答题行为检测
    questions: {
      handler(newVal, oldVal) {
        // 上下页切换时不警示
        // if (newVal !== oldVal) { return false }
        this.warnInfo = sessionStorage.getItem(this.sessionVal) ? JSON.parse(sessionStorage.getItem(this.sessionVal)) : { stopAlarm: false };
        const postCommitData = this.getFormatedCommitedData();
        const commitData = postCommitData.quesAnswers;
        // 如果有存储的上一页的人员数据
        if (this.warnInfo.users) {
          this.warnSaveData = this.warnInfo.users;
          if (commitData && commitData.length) {
            commitData.forEach((person) => {
              const data = this.warnSaveData.filter(item => item.relationId === person.relationId)[0].quAns;
              person.quAns.forEach(item => {
                const quIndex = data.map(item => item.quId).indexOf(item.quId);
                quIndex === -1 ? data.push(item) : data.splice(quIndex, 1, item);
              });
            });
          }
        } else {
          this.warnSaveData = postCommitData.quesAnswers;
        }
        if (this.warnSaveData && this.warnSaveData.length) {
          const answerPersons = [];
          this.warnSaveData.forEach(person => {
            const warnNum = this.maxPower(person.quAns);
            // 触发提示条件：warnSwitch开关打开+连续题数大于设置数+没有关闭“不再提示”
            if (this.warnSwitch && (this.warnCount && warnNum >= this.warnCount) && !this.warnInfo.stopAlarm) {
              answerPersons.push(`${person.fullname}(${person.username})`);
            }
          });

          // 如果answerPersons大于0，证明存在有异常的数据，则弹出弹框
          if (answerPersons.length) {
            this.$refs.abnormalDialog.handleShowAbnormal(answerPersons);
          }
        }
      },
      deep: true
    }
  },
  methods: {
    getSurveyDetail() {
      if (this.relationList && !this.relationList.length) {
        window.location.replace('/default-pages/403.html');
        return;
      }
      this.relationIds = this.relationList.filter(item => item.evaluationId === this.evaluationId && item.relationType === this.evaluatorType)[0].relationIds;

      if (!this.surveyId || !this.relationIds[0]) {
        window.location.replace('/default-pages/403.html');
        return;
      }

      this.loading = true;
      getSurveyBasicInfo(this.relationIds[0]).then(async(res) => {
        this.loading = false;
        this.quesInfo = res;
        this.surveyStatus = res.status;
        this.evaluationUserId = res.evaluationUserId;
        this.qstnId = res.qstnId;
        this.invalidPercent = res.invalidPercent;
        this.invalidSetting = res.invalidSetting;
        this.invalidSwitch = res.invalidSwitch;
        this.invalidAction = res.invalidAction;
        this.warnCount = res.warnCount;
        this.warnSwitch = res.warnSwitch;
        this.timeCtrlSwitch = res.timeCtrlSwitch;
        this.timeCtrlAction = res.timeCtrlAction;
        this.timeCtrlCount = res.timeCtrlCount;
        this.evaluationName = res.evaluationName;
        this.getUserInfo();
        const data = {
          evaluationId: this.evaluationId,
          qstnId: this.qstnId,
          relationIds: this.relationIds,
          relationType: this.evaluatorType,
          surveyId: this.surveyId
        };
        evalBatchAnswerCount(data).then(res => {
          if (res) {
            this.getQuestions(res.minUnsolvedNum || 0);
          }
        }).catch(error => {
          console.log(error);
        });
      }).catch(err => {
        this.$handleError(err);
      }).finally(() => {
        this.loading = false;
      });
    },
    getQuestions(offset = 0) {
      this.loading = true;
      this.questions = [];
      const p = {
        evaluationId: this.evaluationId,
        qstnId: this.qstnId,
        surveyId: this.surveyId,
        relationIds: this.relationIds,
        relationType: this.evaluatorType,
        offset,
        limit: this.pager.pageSize
      };

      if (this.pager.pageSize === -99) {
        if (offset === 0) {
          offset = 1;
        }
        p.current = offset;
        delete p.offset;
      }
      evalBatchAnswerBackview(p).then((res) => {
        this.pager.pages = res.paging.pages;
        this.pager.offset = this.isPageSymbol ? offset : res.paging.offset;
        this.pager.count = res.paging.count;
        this.pager.current = this.isPageSymbol || this.pager.pageSize === 9999 ? offset + 1 : this.pager.offset / res.paging.limit + 1;
        this.finishedCount = res.paging.offset + 1;
        this.totalCount = res.paging.count;
        this.userList = res.datas[0].answerBackViewUsers || [];
        this.formatData(res.datas);
      }).finally(() => {
        this.loading = false;
      });
    },
    formatData(list) {
      const params = {};
      const ruleObj = {};
      this.questions = list.map((item, index) => {
        // 只有题才有check
        // id: 'fe3d3e0b04d44ee694b0350efb7c5d53', quType: 'A01.01', quRequire: 0, quJudge: 1, score: 0
        if (this.isQuestion(item)) {
          item.answerBackViewUsers.forEach(user => {
            user.uploadAnswer = this.isQuestionUpload(item) ? ((user.titleAnswer && JSON.parse(user.titleAnswer)) || []) : [];
            user.optionId = this.formatQuestionRecord(item, user);
          });
          if (this.isQuestionAnswer(item)) {
            item.userOptionId = item.titleAnswer || '';
          }
          const value = `value${index}`;
          item.prop = value;
          params[value] = this.isQuestionUpload(item) ? item.answerBackViewUsers.map(item => item.uploadAnswer || []).flat() : item.userOptionId;
          ruleObj[value] = [];

          if (this.isQuestionAnswer(item)) {
            if (item.quRequire) {
              ruleObj[value].push({ required: true, type: 'string', message: this.$t('pc_gwnl_eval_pleaseEnterAnswer'), trigger: 'blur' });
            }
          } else if (this.isQuestionUpload(item) && item.quRequire) {
            ruleObj[value].push({ required: true, type: 'array', validator: this.validateUpload, trigger: 'change' });
          } else if (this.isQuestionButNotMultiple(item)) {
            ruleObj[value].push({ required: true, type: 'string', message: this.$t('pc_gwnl_eval_selectOption'), trigger: 'change' });
          } else {
            ruleObj[value].push({ required: true, type: 'array', message: this.$t('pc_gwnl_eval_selectOption'), trigger: 'change' });
          }
        }

        return item;
      });
      this.rules = ruleObj;
      this.form = params;
    },

    formatQuestionRecord(item, user) {
      const isQuestionType = this.isQuestion(item);
      const isMultipleType = item.quType === QUESTION_TYPE_ENUM.multiple || item.quType === QUESTION_TYPE_ENUM.upload;
      const isAnswerType = item.quType === QUESTION_TYPE_ENUM.answer;
      if (isAnswerType && user.titleAnswer) {
        return user.titleAnswer;
      } else {
        if (user.optionId) {
          return isMultipleType ? user.optionId.split(',') : user.optionId;
        } else {
          return isQuestionType ? (isMultipleType ? [] : '') : undefined;
        }
      }
    },

    handleCheckRequire(data) {
      const answerTrue = data.quesAnswers.map(item => {
        return item.quAns.map(ans => ans.needShowTip);
      }).flat();
      return !answerTrue.includes(true);
    },

    saveRecord(validateRequired = true) {
      return new Promise((resolve, reject) => {
        const data = this.getFormatedCommitedData();
        data.relationIds = data.quesAnswers && data.quesAnswers.length ? data.quesAnswers.map(item => item.relationId) : []; // 231218传参值修改-答题页不展示的人员不传
        if (validateRequired) {
          const isValid = this.handleCheckRequire(data);
          if (!isValid) {
            this.choosePersonTip = true;
            reject(new Error());
            return;
          } else {
            this.choosePersonTip = false;
          }
        }
        this.loading = true;
        evalBatchAnswerCommit(data).then(() => {
          resolve();
          const info = { stopAlarm: this.warnInfo.stopAlarm, users: this.warnSaveData };
          sessionStorage.setItem(this.sessionVal, JSON.stringify(info));
        }).catch(err => {
          reject(new Error());
          sendErrMsg(err);
        }).finally(() => {
          this.loading = false;
        });
      });
    },
    getFormatedCommitedData() {
      const params = {
        quAns: [],
        quesAnswers: [],
        surveyId: this.surveyId,
        targetId: this.relationId,
        evaluationId: this.evaluationId,
        evaluatorType: this.evaluatorType,
        qstnId: this.qstnId,
        userId: this.userId,
        evaluatorId: '',
        fbiPrjId: '',
        orgId: '',
        pageId: '',
        quRequire: 0,
        questionnaireId: '',
        relationIds: this.relationIds,
        relationType: this.evaluatorType,
        startTime: '',
        submitTime: ''
      };
      const questions = this.questions.filter(question => this.isQuestion(question));
      console.log(questions);
      params.quesAnswers = questions[0] && questions[0].answerBackViewUsers.map(user => {
        const quAns = questions.map(question => {
          const arrayOptions = Array.isArray(user.optionId) ? user.optionId : [user.optionId];
          let answerValue = '';
          let titleName = '';
          let list;
          if (arrayOptions && arrayOptions[0]) {
            list = arrayOptions.map(selectedOptionId => {
              const matchedOption = question.prjQuOptions.find(option => {
                return option.id === selectedOptionId;
              });
              return {
                optionId: selectedOptionId,
                order: matchedOption ? matchedOption.optionNum : undefined,
                score: matchedOption ? matchedOption.optionScore : undefined,
                scoreType: matchedOption ? matchedOption.scoreType : undefined
              };
            });
          }

          if (this.isQuestionAnswer(question)) {
            list = [];
            answerValue = user.optionId || '';
            titleName = question.quTitleFull || '';
          }
          return {
            quRequire: question.quRequire,
            quId: question.id,
            quType: question.quType,
            titleName,
            titleAnswer: answerValue,
            optionAnswers: list,
            optionWarn: this.optionWarnValue(question, list),
            needShowTip: question.quRequire && (this.isQuestionUpload(question) ? !user.uploadAnswer.length : (!user.optionId || user.optionId.length === 0)),
            uploadFile: this.isQuestionUpload(question) ? user.uploadAnswer : null
          };
        });
        return {
          quAns,
          relationId: user.relationId,
          fullname: user.fullname,
          username: user.username
        };
      });
      return params;
    },

    checkIsUploading() {
      const fileList = this.questions.filter(question => this.isQuestion(question) && question.quType === QUESTION_TYPE_ENUM.upload);

      const isUploadingList = [];
      const isErrorList = [];
      if (fileList && fileList.length) {
        fileList.forEach(item => {
          const { uploadingList, errList } = this.$refs[`question${item.id}`][0].checkUploadQuesIsUploading();
          isUploadingList.push(uploadingList);
          isErrorList.push(errList);
        });
        return {
          isUploadingFile: isUploadingList.some(item => item),
          isErrorFile: isErrorList.some(item => item)
        };
      }
      return {
        isUploadingFile: false,
        isErrorFile: false
      };
    },

    submit(needConfirm = true, isForceSubmit = false) {
      const { isUploadingFile, isErrorFile } = this.checkIsUploading();
      if (isUploadingFile) {
        // 有文件正在上传中，无法提交！
        this.$message.error(this.$t('pc_kng_upload_msg_file_uploading4'));
        return;
      }
      if (isErrorFile) {
        // 有文件上传失败，请检查
        this.$message.error(this.$t('pc_eval_msg_upload_fail'));
        return;
      }
      if (needConfirm) {
        this.$confirm(this.$t('pc_eval_tip_submitHint').d('提交后不能撤回修改'), this.$t('pc_eval_tip_submitTitle', [this.evaluationName]).d('确定要提交吗'), {
          confirmButtonText: this.$t('pc_gwnl_global_msg_determine'),
          cancelButtonText: this.$t('pc_gwnl_global_msg_cancel'),
          type: 'warning'
        }).then(() => {
          this.submitSurveyCard();
        });
      } else {
        this.submitSurveyCard(false, isForceSubmit);
      }
    },
    async submitSurveyCard(needCheckRequired = true, isForceSubmit = false) {
      try {
        // 是否是强制提交
        const forceSubmit = typeof isForceSubmit === 'boolean' ? ~~isForceSubmit : 0;
        this.submitting = true;
        this.submitTime = new Date().getTime();
        await this.saveRecord(needCheckRequired);

        if (!isForceSubmit && !(await this.handleAnswerCheck())) {
          return;
        }

        const data = {
          surveyId: this.surveyId,
          targetId: this.relationId,
          evaluationId: this.evaluationId,
          evaluatorType: this.evaluatorType,
          qstnId: this.qstnId,
          userId: this.userId,
          submitTime: this.submitTime,
          startTime: this.startTime,
          evaluatorId: '',
          fbiPrjId: '',
          orgId: '',
          pageId: '',
          quRequire: 0,
          questionnaireId: '',
          relationIds: this.relationIds,
          relationType: this.evaluatorType,
          forceSubmit
        };

        await evalBatchAnswer(data);
        this.$router.go(-1);
      } catch (error) {
        this.$handleError(error);
      } finally {
        this.submitting = false;
      }
    },

    // 保存问卷
    handleSavePage() {
      const { isUploadingFile, isErrorFile } = this.checkIsUploading();
      if (isUploadingFile) {
        // 有文件正在上传中，无法保存！
        this.$message.error(this.$t('pc_eval_upload_file_loading'));
        return;
      }
      if (isErrorFile) {
        // 有文件上传失败，请检查
        this.$message.error(this.$t('pc_eval_msg_upload_fail'));
        return;
      }
      this.saveRecord(false).then((res) => {
        this.$message.success(this.$t('pc_eval_save_successfully').d('保存成功'));
      }).catch(() => {
        this.$message.error(this.$t('pc_gwnl_setting_msg_saveFailed').d('保存失败'));
      });
    },

    // 答题行为检测-无效问卷判断
    async handleAnswerCheck() {
      const data = { qstnId: this.qstnId, relationIds: this.relationIds };

      try {
        const res = await evalAnswerCheck(data);
        if (res.success) return true;

        const userInfo = res.userInfo || [];
        const timeUserInfo = res.timeUserInfo || [];
        const answerPersons = userInfo.map(({ fullname, username }) => `${fullname}(${username})`);

        if (userInfo.length) {
          this.$refs.abnormalDialog.handleShowUseless(answerPersons);
        } else if (timeUserInfo.length) {
          this.$refs.abnormalDialog.handleShowUseless(answerPersons, true);
        }
        return false;
      } catch (error) {
        return false;
      }
    },
    // 答题行为检测-警示-本次评估不再提醒
    handleStopAlarm(val) {
      if (val !== this.warnInfo.stopAlarm) {
        this.warnInfo.stopAlarm = val;
        sessionStorage.setItem(this.sessionVal, JSON.stringify({
          stopAlarm: val,
          users: this.warnSaveData
        }));
      }
    },

    // 前往查看被评估人答题页面
    goUserInfoDetail(userInfo) {
      this.goDetail(userInfo, userInfo.userSelfRelationId || '');
    },

    // 上一步/下一步跳转检查
    uploadCheckAndSkip(key) {
      const { isUploadingFile, isErrorFile } = this.checkIsUploading();
      if (isUploadingFile) {
        // 文件正在上传中，请等待上传完成
        this.$message.error(this.$t('pc_eval_msg_file_uploading_wait'));
        return;
      }
      if (isErrorFile) {
        // 有文件上传失败，请检查
        this.$message.error(this.$t('pc_eval_msg_upload_fail'));
        return;
      }

      // 上一步下一步的时候清除提示
      this.choosePersonTip = false;
      if (key === 'prev') {
        this.gotoPrePage();
        return;
      }
      this.gotoNextPage();
    }
  }
};

</script>

<style lang="scss">
@import "./questionnaire";

.gwnl-questionnaire-preview {
  padding-top: 0;

  .wordbreak {
    .gwnl-question-title-item {
      display: inline-block;
      min-width: 55px;
      vertical-align: top;
    }
  }
}

.gwnl-questionnaire-previewsubmit {
  .gwnl-questionnaire-preview-container {
    .gwnl-question-content-item {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .yxtf-radio-group.yxtf-radio-group--row,
    .yxtf-checkbox-group.yxtf-checkbox-group--row {
      width: 100%;

      .yxtf-radio__label,
      .yxtf-checkbox__label {
        display: inline-block;
        width: 90%;
        vertical-align: middle;
      }

      .yxtf-radio__label {
        vertical-align: top;
      }
    }
  }
}

.gwnl-questionnaire-preview-container {
  display: flex;
  align-content: center;
  justify-content: center;
}

.gwnl-questionnaire-preview-questions {
  .select {
    .option {
      cursor: pointer;

      &.square:hover {
        color: #fff;
        background: var(--color-primary-6);
      }

      &.star:hover {
        color: var(--color-primary-6);
      }
    }
  }

  .yxtf-radio,
  .yxtf-radio__input {
    white-space: normal !important;
  }

  ::v-deep .yxt-form-item:last-child {
    margin-bottom: 0;
  }
}

.gwnl-questionnaire-toolbar {
  margin-top: 68px;
  text-align: center;
}

.gwnl-question-content_type {
  color: #262626;
  font-weight: 500;
}

.gwnl-question-index {
  width: 20px;
  margin-right: 8px;
  padding: 0;
  line-height: 22px;
  text-align: center;
}
</style>
<style lang="scss" scoped>
.card-hidde {
  opacity: 0;
}

.fled {
  position: fixed;
  top: 70px;
  left: 0;
  width: 100%;

  .content-center {
    position: relative;
    width: 1200px;
    margin: 0 auto;

    .card-box {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}

.gwnl-questionnaire-preview-questions__item {
  ::v-deep .yxt-form-item__content {
    display: flex;
    width: 100%;
  }
}
</style>
