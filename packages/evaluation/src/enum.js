export const QUESTION_TYPE_WORD = new Map([
  ['A01.01', 'pc_gwnl_global_singleChoice'],
  ['A01.09', 'pc_gwnl_eval_scaleQuestion'],
  ['A01.02', 'pc_gwnl_jq_multipleChoice'],
  ['A01.10', 'pc_gwnl_jq_judgmentQuestion'],
  ['A02.03', 'pc_gwnl_eval_questionAndAnswer'],
  ['A06.01', 'pc_eval_lbl_file_upload']
]);
export const QUESTION_TYPE_ENUM = {
  single: 'A01.01',
  scale: 'A01.09',
  multiple: 'A01.02',
  trueFalse: 'A01.10',
  answer: 'A02.03',
  upload: 'A06.01'
  // 'multiple': 13,
  // 'trueFalse': 14
};
export const SURVEY_STATUS = {
  notStart: 1,
  start: 2,
  submitted: 3,
  submittedCannotCheck: 4,
  outdated: 5
};

/*
  题目类型(A04.01：备注说明，A04.02：分割线，A01.01：单选题, A01.09: 量表题，'A02.03：问答题)
 */
export const SURVEY_TYPE = {
  'A04.01': 'remark',
  'A04.02': 'divide',
  'A01.01': 'Single',
  'A01.02': 'multiple',
  'A01.10': 'trueFalse',
  'A01.09': 'Rate',
  'A02.03': 'answer',
  'A06.01': 'upload'
};
/*
  量表类型(A01.09.07：滑块，A01.09.01：点选，A01.09.02：星评分)
 */
export const RATE_TYPE = {
  slider: 'A01.09.07',
  nps: 'A01.09.01',
  star: 'A01.09.02'
};
