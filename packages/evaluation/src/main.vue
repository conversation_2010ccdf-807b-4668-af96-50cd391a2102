<template>
  <div class="yxtulcdsdk-evaluation yxtulcdsdk-ulcdsdk minh100 pr" :class="{'yxtulcdsdk-evaluation--radius': radius, 'yxtulcdsdk-evaluation-wrap': inner}">
    <div v-if="baseData !== undefined" class="center-block pr hline" :class="{'w1200': !inner}">
      <Questionnaire
        v-if="componentName==='Questionnaire'"
        :base-data="baseData"
        @goAskSurvey="goAskSurvey"
        @goEvaluate="goEvaluate"
        @goMulti="goMulti"
      />
      <BeiAi v-if="componentName==='BeiAi'" :base-data="baseData" />
      <Survey
        v-if="componentName==='Survey'"
        :query-data="surveyData"
        @on-complete-change="hasCompleted"
        @goEvaluate="goEvaluate"
      />
      <Evaluate v-if="componentName==='Evaluate'" :query-data="evaluateData" @goQuestionnaire="goQuestionnaire" />
      <Multi v-if="componentName==='Multi'" :query-data="multiData" />
    </div>
  </div>
</template>

<script>
import Questionnaire from './taskDetail/taskDetail.vue'; // 开始结果页
import BeiAi from './taskDetail/ai.vue'; // BEIAI预览页
import Evaluate from './evaluate.vue'; // 行为详情页面
import Survey from './questionnaire.vue'; // 答题页
import Multi from './questionnaireMulti.vue'; // 批量答题页
import { evalTaskStartPage } from './service/eval.service.js';
import VueDOMPurifyHTML from 'vue-dompurify-html';
import Vue from 'vue';

Vue.use(VueDOMPurifyHTML);
export default {
  name: 'YxtUlcdSdkEvaluation',
  name_zh: '沉浸式测评',
  components: {
    Questionnaire,
    BeiAi,
    Evaluate,
    Survey,
    Multi
  },
  props: {
    params: {
      type: Object,
      default: () => ({
        evaluationId: ''
      })
    },
    inner: {
      type: Boolean,
      default: true
    },
    // 是否需要圆角效果
    radius: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      componentName: 'Questionnaire',
      baseData: undefined,
      surveyData: undefined,
      evaluateData: undefined,
      multiData: undefined
    };
  },
  created() {
    this.getEvalBaseData();
  },
  methods: {
    getEvalBaseData() {
      console.log('getEvalBaseData', this.params);
      evalTaskStartPage({ evaluationId: this.params.evaluationId }).then(res => {
        console.log(res);
        this.baseData = res;
        if (this.baseData.aiEval) {
          this.componentName = 'BeiAi';
        } else {
          this.componentName = 'Questionnaire';
        }
      });
    },
    goAskSurvey(data) {
      this.surveyData = data;
      this.componentName = 'Survey';
    },
    goEvaluate(data) {
      this.evaluateData = data;
      this.componentName = 'Evaluate';
    },
    goMulti(data) {
      this.multiData = data;
      this.componentName = 'Multi';
    },
    goQuestionnaire() {
      this.componentName = 'Questionnaire';
    },
    hasCompleted(flag) {
      console.log('on complete change ~~~~~~~');
      if (flag) {
        this.goQuestionnaire();
      }
      this.$emit('updateProgress', flag ? 2 : 1);
    },
    confirmLeave(cb = (() => {})) {
      try {
        this.leaveCB = cb;
        this.leaveDialog();
      } catch (error) {
        cb && cb(true);
      }
    },
    leaveDialog() {
      if (!this.$refs.contentInput.confirmLeave()) {
        return this.confirmLeaved(true);
      }

      this.$confirm(this.$t('pc_o2o_lbl_cancel_describe').d('当前操作尚未保存'), this.$t('pc_o2o_give_edit_confirm').d('确定放弃此次编辑吗？'), {
        confirmButtonText: this.$t('pc_o2o_btn_done'),
        cancelButtonText: this.$t('pc_o2o_btn_cancel'),
        type: 'warning'
      }).then(() => {
        this.confirmLeaved(true);
      }).catch(() => {
        this.confirmLeaved(false);
      });
    },
    // 离开前的确认后
    confirmLeaved(leave) {
      if (!this.leaveCB) return;
      // 沉浸式的时候告知播放器是否离开
      this.leaveCB(leave);
      this.leaveCB = undefined;

    }
  }
};
</script>
