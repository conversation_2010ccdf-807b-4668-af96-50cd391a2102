<template>
  <div class="minh100 yxtulcdsdk-o2o-evaluation-task yxtulcdsdk-ulcdsdk" :class="{'h0 yxtulcdsdk-o2o-evaluation-task-wrap': inner}">
    <yxt-ulcd-sdk-surveying
      v-if="checkSurvey"
      :params="surveyParams"
      :full-screen="false"
      style="min-height: calc(100% - 38px);"
      :scroll-inner="false"
      :radius="false"
      @updateProgress="updatesProgress"
    />
    <div v-show="!checkSurvey" class="o2o-task-details hline">
      <div
        v-loading="workLoading"
        :class="{'w1200 mt24': !inner}"
        class="color-lightgrey  o2o-page-loading hline"
      >
        <div class="hline layout-flex layout-flex-vertical">
          <div class="bg-white radius4 text-center o2o-task-topName">
            <Task-header :task="task" :inner="inner" />
          </div>
          <div class="o2o-task-content bg-white mt24 radius4 pb94 flex-1" :class="{'pl94 pr142': !inner}">
            <div :class="{'center-wrap': inner}">
              <div v-if="task.id">
                <div class="pt56 break">
                  <div class="font-size-20 weight-bold lh36 text-26">{{ $t('pc_o2o_lbl_taskdesc') }}</div>
                  <div
                    v-if="task.description"
                    class="font-size-18 lh34 mt24 break text-pre"
                    v-html="task.description"
                  >
                  </div>
                  <div
                    v-else
                    class="font-size-18 lh22 mt24"
                  >
                    {{ $t('pc_o2o_tip_nodatas') }}
                  </div>
                </div>
                <div class="mt24">
                  <yxtf-button
                    size="larger"
                    type="primary"
                    @click="toSurvey"
                  >
                    {{ getBtnName }}
                  </yxtf-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import mixin from 'packages/_mixins/task';
import TaskHeader from './components/taskHeader.vue';
import { getTaskTypeName } from 'packages/_utils/core/utils.js';

export default {
  name: 'YxtUlcdSdkO2oEvaluationTask',
  mixins: [mixin],
  components: {
    TaskHeader
  },
  props: {
    params: {
      type: Object,
      default: ()=>({
        projectId: '',
        trackId: '',
        teid: '',
        btid: '',
        taskId: ''
      })
    },
    inner: Boolean
  },
  data() {
    return {
      surveyParams: {},
      checkSurvey: false,
      checkStatusSurvey: false,
      bthid: ''
    };
  },
  watch: {
    params: {
      handler(data) {
        this.taskId = data.taskId;
        this.checkSurvey = false;
        this.bthid = data.btid || '';

        this.init();
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    getTaskLabel() {
      return getTaskTypeName(this.task.typeName, this.task.type);
    },
    getBtnName() {
      let str = '';
      if ((this.task.taskResult && this.task.taskResult.length > 0 && this.task.taskResult[0].replyStatus > 0)) {
        str = this.$t('pc_o2o_lbl_checkdetail');
      } else {
        str = this.$t('pc_o2o_lbl_immediatejoin');
      }
      return str;
    },
    useEvalCfgId() {
      return this.task.taskOwner === 1 || this.task.taskOwner === 2;
    }
  },
  created() {
    this.$root.$on('GOBACK', this.goBack);
  },
  beforeDestroy() {
    this.setBreadcrumbUpdate(false);
    this.$root.$off('GOBACK', this.goBack);
  },
  methods: {
    goBack() {
      this.init(); // 合并评价 状态变化处理
      this.checkSurvey = false;
      this.setBreadcrumbUpdate(false);
    },
    updatesProgress(v) {
      this.$emit('updateProgress', v);
    },
    setBreadcrumbUpdate(v) {
      const breadcrumb = {
        show: v,
        list: [{name: this.getTaskLabel, eventName: 'GOBACK'}, {name: this.$t('pc_o2o_lbl_questionnaire')}]
      };
      this.$root.$emit('SETBREADCRUMBUPDATE', breadcrumb);
    },
    // 获取问卷地址
    toSurvey() {
      if (this.task.taskOwner === 2 && !this.task.targetId) {
        this.$message.warning(this.$t('pc_o2o_lbl_contactadmintosetquestionnaire'));
        return;
      }
      // taskOwner ==1  1是学员评价导师 2是学员自评
      // 如果task对象中没有teacherId的值，已页面传过来的值为准
      const targetId = this.task.taskOwner === 1 ? (this.task.teacherId || this.params.teid) : window.localStorage.getItem('userId');
      this.surveyParams = {
        type: 2,
        id: this.task.type === 4 && this.useEvalCfgId ? this.task.evalCfgId : this.taskId,
        tname: '',
        tid: targetId,
        success: '',
        trackId: this.params.trackId,
        mergeEvalProjectId: this.params.projectId,
        mergeEvalTaskId: this.taskId,
        showResult: 1,
        btid: this.params.btid,
        batchId: this.params.btid || '',
        hideBack: true
      };
      this.checkSurvey = true;
      this.setBreadcrumbUpdate(true);
    }
  }
};
</script>
