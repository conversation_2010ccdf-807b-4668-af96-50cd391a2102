import { commonUtil } from 'yxt-biz-pc';

const i18n = commonUtil.i18n;

/**
 * 将秒转换成天 时 分 秒
 * @param {value} // 秒数
 * @returns
 */
export const formatSeconds = value => {
  let timeStr = '0';

  if (!value) return timeStr;

  const time = parseInt(value);// 需要转换的时间秒
  if (time < 1) {
    timeStr = 0;
  } else if (time < 60) {
    timeStr = time + i18n.t('pc_o2o_lbl_second' /* 秒  */);
  } else if (time < 3600) {
    timeStr = (time / 60).toFixed(1) + i18n.t('pc_o2o_lbl_minute_h5' /* 分  */);
  } else if (time < 86400) {
    timeStr = (time / 3600).toFixed(1) + i18n.t('pc_o2o_lbl_hour' /* 小时  */);
  } else if (time < 31536000) {
    timeStr = (time / 86400).toFixed(1) + i18n.t('pc_o2o_lbl_day' /* 天  */);
  }
  return timeStr;
};
