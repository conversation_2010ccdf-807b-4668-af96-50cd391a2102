<template>
  <div style="padding-top: 54px; padding-bottom: 55px;" :class="{'center-wrap' : inner}">
    <div class="break weight-bold text-26  v-mid font-size-24" :class="{'ph94': !inner}">
      <yxt-popover
        placement="bottom"
        trigger="hover"
        show-scroll
        max-width="560"
        max-height="168"
        :content="task.name"
        open-filter
      >
        <span slot="reference" class="ellipsis-2">
          <yxtf-tag
            v-if="task.type === 7"
            class="v-mid"
            type="info"
            size="small"
          >
            {{ typeItem[task.activityType] }}
          </yxtf-tag>
          <span class="v-mid lh36">{{ task.name }}</span>
        </span>
      </yxt-popover>
    </div>

    <div v-if="task.startTime || task.endTime" class="font-size-18 lh22 mt12">
      {{ $t('pc_o2o_lbl_activitytime') }}：{{ task.startTime ? dateFormat(task.startTime, 'yyyy-MM-dd hh:mm'): '' }} &nbsp;{{ $t('pc_o2o_lbl_to') }}&nbsp; {{ task.endTime ? dateFormat(task.endTime, 'yyyy-MM-dd hh:mm'): '' }}
    </div>

    <div v-if="task.duration > 0" class="font-size-18 lh22 mt12">
      {{ $t('pc_o2o_lbl_activityduration') }}：{{ task.duration | filterFormatSeconds }}
    </div>
  </div>
</template>

<script>
import { formatSeconds } from '../utils';
import { dateFormat } from 'packages/_utils/core/date-format';

export default {
  data() {
    return {
      typeItem: [this.$t('pc_o2o_lbl_exam'), this.$t('pc_o2o_lbl_program'), this.$t('pc_o2o_lbl_activity'), this.$t('pc_o2o_lbl_other')] // 任务类型
    };
  },
  props: {
    task: [Object],
    inner: Boolean
  },
  filters: {
    filterFormatSeconds(value) {
      return formatSeconds(value);
    }
  },
  computed: {
    timeLabel() {
      let str = '';
      // 任务类型:0考勤,1面授,2考试,3调查,4评价,5投票,6作业,7活动,8知识文档,9知识课程,10知识微课,11知识视频,12知识音频,13知识scorm,14其他任务15任务组16HTML17直播
      switch (this.task.type) {
        case 1:
          str = this.$t('pc_o2o_lbl_traintime');
          break;
        case 3:
        case 4:
        case 5:
          str = this.$t('pc_o2o_lbl_tasktime');
          break;
        case 7:
          str = this.$t('pc_o2o_lbl_activitytime');
          break;
        default:
          break;
      }
      return str;
    },
    durationLabel() {
      let str = '';
      // 任务类型:0考勤,1面授,2考试,3调查,4评价,5投票,6作业,7活动,8知识文档,9知识课程,10知识微课,11知识视频,12知识音频,13知识scorm,14其他任务15任务组16HTML17直播
      switch (this.task.type) {
        case 1:
          str = this.$t('pc_o2o_lbl_trainduration');
          break;
        case 7:
          str = this.$t('pc_o2o_lbl_activitytime');
          break;
        default:
          break;
      }
      return str;
    }
  },
  methods: {
    dateFormat
  }
};
</script>
