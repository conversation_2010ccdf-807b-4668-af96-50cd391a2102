import {o2oApi} from 'packages/api';
import qs from 'qs';

// 项目权限校验
export const checkPermission = (taskId, config) => {
  return o2oApi.put(`study/task/${taskId}/check`, undefined, config);
};
// 作业详情
/**
 * @param {*} taskId
 * @param {*} userId
 * @param {String} end 代表管理端1,0学员端
 * @returns
 */
export const getHomeworkDetail = (taskId, userId, end = '0', batchId='') => {
  // return o2oApi.get(`study/homework/${taskId}/${userId}?end=${end}`)
  if (!taskId) {
    return Promise.resolve();
  }
  return o2oApi.get(
    `study/homework/${taskId}/${userId}`,
    {
      end,
      limitCurrentUser: true,
      batchId
    }
  );
};
/**
 * 获取协同小作业
 * @param {Object} param0
 * @returns
 */
export const getCooperationHwk = ({ homeworkId, userId }) =>
  o2oApi.get(`study/project/team/homework/${homeworkId}/${userId}`);

// 检验任务是否已删除
export const getTaskHasDeleted = (projectId, taskId, type) => {
  if (!projectId) {
    return Promise.resolve();
  }
  return o2oApi.get(`tasks/checkTaskExist/${projectId}/${taskId}?${qs.stringify({ type })}`);
};
