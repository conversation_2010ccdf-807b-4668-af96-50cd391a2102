import { kngApi, utilityApi, ccApi, fileApi, o2oApi, coreApi } from 'packages/api';
import axios from 'axios';
const catchTools = async(rq) => {
  let res, error;
  try {
    res = await rq();
  } catch (e) {
    error = e;
  }
  return Promise.resolve([res, error]);
};

// 删除正在学

/** --------- kngApi --------- */
// 获取章节数据
export const getChapters = data => catchTools(() => kngApi.post('/study/tree', data));
// 获取课件/课程详情
export const getKngDetail = data => catchTools(() => kngApi.post('/study/kngInfo', data));
// 获取播放详情
export const getKngPlayDetail = data => catchTools(() => kngApi.post('/study/kngPlay', data));
// 获取boss平台下载开关状态
export const getBossDownloadSwitch = data => kngApi.post('kv/conf/query/switch', data);
// 获取boss平台下载文件类型
export const getBossDownloadFileType = () => kngApi.get('download/config');
// 获取课程综合评分
export const getCourseAvgScore = id => kngApi.get(`/multi-eval/group/info/${id}`);
// 获取下载地址
export const getDownloadUrl = (id, params) => kngApi.get(`knowledge/url/download/${id}`, { params });
// 获取下载地址
export const getNewDownloadUrl = (data) => kngApi.post('knowledge/downloadurl', data);
// 学时提交
export const submitStudyTime = (data, trackId = '') => catchTools(() => kngApi.post(`/study/submit/second?trackId=${trackId || ''}`, data));
// 学时扣除
export const subtractStudyTime = (data, trackId = '') => kngApi.post(`/study/rollBack?trackId=${trackId || ''}`, data);
// 进度提交
export const submitSchedule = (data) => catchTools(() => kngApi.post('/study/schedule/submit', data));
// 防作弊配置
export const getCheatInfo = () => kngApi.get('/kngConf/cheat');
// 获取打点设置
export const getMarkList = id => kngApi.get(`/mark/byKng/${id}`);
// 获取草稿的打点设置
export const getDraftMarkList = id => kngApi.get(`/mark/manage/key/point/${id}`);
// 获取课程包中课件的随堂练习
export const getKngExercise = data => kngApi.post('/study/exercise/list', data);
// 获取讲义列表
export const getHandouts = data => kngApi.post('/play/study', data);
// 获取课程统计信息
export const getKngSummary = data => kngApi.post('/study/summary', data);
// 加入自学
export const addSelfStudy = id => kngApi.put(`/kngSelfStudy/addStudy/${id}`);
// 移除自学
export const removeSelfStudy = id => kngApi.delete(`/kngSelfStudy/deleteByKngId/${id}`);
// 生成trackId
export const generateTrackId = data => kngApi.post('/common/buildTrackId', data);
// 写笔记
export const addNote = data => kngApi.post('studynote/web/add', data);
// 编辑笔记
export const editNote = data => kngApi.post('studynote/web/edit', data);
// 查询我的笔记列表
export const getMyNoteList = (params, data) => kngApi.post('/studynote/web/my/pageList', data, { params });
// 删除笔记
export const delNote = id => kngApi.put(`/studynote/web/del/${id}`);
// 切换正在学的知识
export const changeStudying = data => kngApi.post('study/change/studying', data);
// 删除正在学
export const deleteStudying = (data = {}) => kngApi.post('study/del/studying', data);
// 检测是否需要更新版本
export const checkVersion = data => catchTools(() => kngApi.post('/kngVersion/check', data));
// 知识版本更新校验后修改版本信息
export const changeVersion = data => kngApi.put('/kngVersion/alert', data);
export const updateVersion = data => kngApi.put('/kngVersion/update', data);
// 引导页
export const closeGuideByType = type => kngApi.put('beginnerguide/compeletd', { guideType: type });
// 获取引导页
export const getGuideByType = type => kngApi.get('beginnerguide/state', { params: { guideType: type } });
// 获取是否打开过完课评论
export const getCommentTipState = id => catchTools(() => kngApi.get(`study/popupAfterFinishStudy/${id}`));
// 添加完课评论打开记录
export const postCommentTipState = id => kngApi.get(`study/addPopupAfterFinishStudy/${id}`);
// 下载压缩包
export const getZipUrl = data => kngApi.post('study/zip/submit', data);
// 下载压缩包-管理端预览使用
export const getManageZipUrl = (id, params) => kngApi.get(`knowledge/getZipDownloadUrl/${id}`, { params });
// 同步进度给项目
export const postSyncScheduleNotice = data => kngApi.post('study/userStudyCompleteNoSyncNotify', data);
/** --------- utilityApi --------- */
// 课程/课件点赞状态
export const getKngLikeStatus = data => utilityApi.post('praises/findpraisedtargets', data, { allowRepeat: true });
// 添加点赞
export const doLike = data => utilityApi.post('praises?usebody=true', data);
// 取消点赞
export const cancelLike = id => utilityApi.delete(`praisetargets/${id}`);
// 获取收藏状态
export const getCollectStatus = id => utilityApi.get(`favoritetargets/${id}/favorited`);
// 收藏
export const doCollect = data => utilityApi.post('favorites?usebody=true', data);
// 取消收藏
export const cancelCollect = id => utilityApi.delete(`favoritetargets/${id}`);
// 课程相关配置，从课程详情接口剥离的一些信息
export const getCourseSetting = data => kngApi.post('play/detail/config', data);
/** --------- ccApi --------- */
// 获取舆论管理配置
export const getSilenceConfigs = params => ccApi.get('silenceconfigs', { params });

/** --------- fileApi --------- */
// 获取水印配置
export const getWatermarkConfig = () => fileApi.get('watermark/config');

/** --------- o2oApi --------- */
// 获取项目详情
export const getO2oProjectName = id => catchTools(() => o2oApi.get(`external/project/baseInfo?projectId=${id}`));

// 查询团队自学计划学员列表
export const getPlanUserList = (bodyParams, params) => kngApi.post('kngSelfStudy/plan/user/list', bodyParams, { params });
// 获取我的团队导航
export const getNavs = () => coreApi.get('user/nav/self?type=2');

// 获取自定义设置课件名称配置
export const getCustomKngTypeNameConfig = () => catchTools(() => kngApi.get('/kv/current/knowledge/name'));

export const getSrt = (url) => axios.get(url);

export const getCourseAi = data => kngApi.post('study/otherInfo', data);

export const checkConnection = url => axios.get(url);
