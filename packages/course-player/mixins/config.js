// 公共配置信息查询
import { getUserInfo } from '../../course-page/service';
import { getSilenceConfigs, getBossDownloadSwitch, getBossDownloadFileType, getCheatInfo, getWatermarkConfig } from '../service';
import {commonUtil} from 'yxt-biz-pc';
const factorList = [
  'learning_note', // 笔记
  'discussion_enable', // 讨论区
  'Internal_network_learning',
  'Course_Facial_Recognition', // pc/h5人脸识别
  'Self_selected_course_pop-up_window'
];
export default {
  provide() {
    return {
      getCheatInfo: () => this.cheatInfo,
      getSilenceConfigs: () => this.silenceConfigs,
      getBossDownloadConfig: () => this.bossDownloadConfig,
      getWatermarkConfig: () => this.watermarkConfig,
      getFactorConfig: () => ({
        showNote: this.showNote,
        buyBBS: this.buyBBS,
        showMixNetwork: this.showMixNetwork,
        showCourseFacialRecognition: this.showCourseFacialRecognition,
        showLinkTip: this.showLinkTip
      })
    };
  },
  data() {
    return {
      bossDownloadConfig: { // boss平台下载设置
        isOpen: false,
        fileTypes: []
      },
      silenceConfigs: {},
      watermarkConfig: {},
      cheatInfo: {},
      userInfo: {},
      showNote: false, // 学习笔记
      buyBBS: false, // 购买bbs
      showMixNetwork: false, // 混部播放提示
      showCourseFacialRecognition: false, //  pc/h5人脸识别
      showLinkTip: false // 三方外链课开启提示
    };
  },
  created() {
    this.preCheckFactor();
    this.setSilenceConfigs();
    this.setBossDownloadConfig();
    this.setCheatInfo();
    this.setWatermarkConfig();
    this.setUserInfo();
  },
  methods: {
    async preCheckFactor() {
      await commonUtil.preCheckFunctions(factorList);
      const noteState = commonUtil.checkTimeOutFnc(factorList[0]);
      this.showNote = noteState === 2;
      const bbsState = commonUtil.checkTimeOutFnc(factorList[1]);
      this.buyBBS = bbsState >= 2 && bbsState <= 4;
      const mixNetwork = commonUtil.checkTimeOutFnc(factorList[2]);
      this.showMixNetwork = mixNetwork >= 2 && mixNetwork <= 4;
      const facialRecognition = commonUtil.checkTimeOutFnc(factorList[3]);
      this.showCourseFacialRecognition = facialRecognition === 2;
      const linkTip = commonUtil.checkTimeOutFnc(factorList[4]);
      this.showLinkTip = linkTip >= 2 && linkTip <= 4;
    },
    // 舆论管理配置
    async setSilenceConfigs() {
      const res = await getSilenceConfigs({orgId: localStorage.orgId, userId: localStorage.userId});
      this.silenceConfigs = res;
    },
    // boss平台下载配置
    async setBossDownloadConfig() {
      const params = {
        group: 'DOWNLOAD_GROUP',
        key: 'DOWNLOAD_SWITCH',
        orgId: localStorage.orgId || ''
      };
      const [status, typeList] = await Promise.all([getBossDownloadSwitch(params), getBossDownloadFileType()]);
      this.bossDownloadConfig.isOpen = status && status.config === '1';
      this.bossDownloadConfig.fileTypes = typeList.map(item => item.fileType);
    },
    // 防作弊配置
    async setCheatInfo() {
      const res = await getCheatInfo();
      this.cheatInfo = res;
    },

    // 平台水印设置
    async setWatermarkConfig() {
      const res = await getWatermarkConfig();
      this.watermarkConfig = res;
    },
    async setUserInfo() {
      const res = await getUserInfo();
      this.userInfo = res;
    }
  }
};
