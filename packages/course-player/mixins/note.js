import { getMediaTimeStr } from 'yxt-ulcd-sdk/packages/course-player/utils';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
export default {
  methods: {
    getPositionText(item) {
      const position = parseInt(item.position || 0);
      if (position < 0) return '';
      else if (item.kngType === KNG_TYPE.DOC) return this.$t('pc_kng_note_lbl_marke_page', [position]);
      else if (item.kngType === KNG_TYPE.VIDEO || item.kngType === KNG_TYPE.AUDIO) return getMediaTimeStr(position);
      return '';
    },
    // 播放中的视频文档记录位置，其他的不显示位置
    getIcon(item, light = false) {
      // 知识类型 0.课程、1.文档、2.视频、3.音频、4.微课、5.SCORM、6.HTML、7.压缩包、8.外链
      if (item.position < 0) return 'kng' + item.kngType;
      else if (item.kngType === KNG_TYPE.AUDIO || item.kngType === KNG_TYPE.VIDEO) return 'note-circle' + (light ? '-white' : '');
      else if (item.kngType === KNG_TYPE.DOC) return 'note-flag' + (light ? '-white' : '');
      return 'kng' + item.kngType;
    }
  }
};
