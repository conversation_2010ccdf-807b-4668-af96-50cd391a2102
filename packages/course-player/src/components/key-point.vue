<!-- 创建时间2023/02/13 09:24:06 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：关键点功能区 -->
<template>
  <div class="yxtulcdsdk-course-player__functional">
    <div class="mt24 ml24 mr40 standard-size-16">{{ $t('pc_kng_mark_lbl_key'/**关键点 */) }}</div>
    <div class="yxtulcdsdk-flex-1 h0 ph24 pt12 pb24">
      <yxtf-scrollbar :fit-height="true">
        <div
          v-for="item in list"
          :key="item.id"
          class="yxtulcdsdk-course-player__point"
          @click="toJump(item)"
        >
          <div class="yxtulcdsdk-course-player__point-top-line"></div>
          <div class="yxtulcdsdk-course-player__point-bottom-line"></div>
          <div class="yxtulcdsdk-course-player__point-circle"></div>
          <div class="yxtulcdsdk-course-player__point-text">
            <div>{{ getMediaTimeStr(item.markTime) }}</div>
            <div>{{ item.markDesc }}</div>
          </div>
        </div>
      </yxtf-scrollbar>

    </div>
  </div>
</template>

<script>
import { getMediaTimeStr } from '../../utils';
export default {
  name: 'KeyPoint',
  props: {
    list: {
      type: Boolean,
      default: []
    }
  },
  data() {
    return {};
  },
  methods: {
    getMediaTimeStr,
    toJump(item) {
      this.$emit('playAppoint', item.markTime);
    }
  }
};
</script>
