<!-- 创建时间2023/02/03 18:59:05 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：组件切换走销毁播放器 -->
<template>
  <div class="yxtulcdsdk-fullsize" :class="{'yxtulcdsdk-web-full': webFull}">
    <div v-if="webFull" class="yxtulcdsdk-course-player__web-header">
      <div class="yxtulcdsdk-flex-center yxtulcdsdk-play-goback mr12 hand" @click="webFull = false">
        <yxt-ulcd-sdk-svg
          class="color-white"
          width="16px"
          height="16px"
          icon-class="returnarrow"
        />
      </div>
      <div class="standard-size-16 ellipsis">{{ kngDetail.title }}</div>
    </div>
    <div class="yxtulcdsdk-course-player__playeare">
      <!-- 倒计时：计算出countdown后再显示 -->
      <countdown v-if="playMode > 0 && playMode !== PLAYER_MODE.OTHER && !globalData.startWithAirplaneMode" :kng-detail="kngDetail" :second="countdown" />
      <!-- 讲义、随堂练习、笔记、关键点 -->
      <!-- 讲义可调整尺寸 -->
      <!-- 文档 -->
      <template v-if="playMode === PLAYER_MODE.DOC">
        <yxtbiz-doc-player
          ref="player"
          v-bind="playerProps"
          @pageChange="onPageChange"
        />
      </template>
      <!-- 音视频 -->
      <!-- 需要打点引导提示 -->
      <yxtbiz-video
        v-else-if="playMode === PLAYER_MODE.VIDEO"
        v-bind="playerProps"
        ref="player"
        width="100%"
        height="100%"
        app-code="kng"
        version="v2"
        @onReady="onReadyVideo"
        @onMeta="onMetaVideo"
        @markJump="toMarkJump"
        @onPlaybackRate="e => onRateChange(e && e.playbackRate)"
        @onPlay="onPlay"
        @onPause="onPause"
        @onComplete="onEnd"
        @onTime="updateViewLoc"
        @onSeek="onSeek"
        @onBuffer="onBuffer"
        @onQualityChange="onQualityChange"
        @onError="onError"
        @onSrtData="onSrtData"
        @ai-lan-change="onAiLanChange"
      >
        <div v-if="showMarkGuide && markList.length" slot="mark">
          <div v-html="$t('pc_kng_mark_msg_guide')">
          </div>
          <yxtf-button
            type="primary"
            size="small"
            class="pull-right mt8"
            @click="closeGuid"
          >
            {{ $t('pc_kng_detail_lbl_l_know') }}
          </yxtf-button>
        </div>
      </yxtbiz-video>
      <yxtbiz-kng-scorm-player
        v-else-if="playMode === PLAYER_MODE.SCORM"
        ref="player"
        v-bind="playerProps"
        @completed="onContentComplete"
      />
      <!-- 微课：pangu播放器-->
      <PCPlayer
        v-else-if="playMode === PLAYER_MODE.WEIKE"
        ref="player"
        type="WK"
        is-course-lib
        v-bind="playerProps"
        @videoReady="onReady"
        @speedChange="onRateChange"
        @videoPlay="onPlay"
        @videoPause="onPause"
        @videoEnded="onContentComplete"
        @progress="onContentSchedule"
      >
        <template slot="watermark">
          <yxtbiz-watermark
            v-if="watermarkOption.enabled"
            :option="watermarkOption"
            app-code="kng"
            version="v2"
            class="z-999"
          />
        </template>
      </PCPlayer>
      <!-- iframe类的（scorm，html） -->
      <template v-else-if="playMode === PLAYER_MODE.IFRAME">
        <iframe
          ref="player"
          title="iframe-player"
          class="yxtulcdsdk-course-player__iframe"
          allowfullscreen="true"
          webkitallowfullscreen="true"
          mozallowfullscreen="true"
          v-bind="playerProps"
          @load="setAllTimer"
        ></iframe>

        <yxtbiz-watermark
          v-if="watermarkOption.enabled"
          :option="watermarkOption"
          app-code="kng"
          version="v2"
          class="z-999"
        />
      </template>
      <!-- 跳出的提示&按钮 -->
      <!-- 人脸识别，去app学习二维码 -->
      <div v-else-if="playMode === PLAYER_MODE.OTHER" class="yxtulcdsdk-course-player__other">
        <img v-if="kngDetail.coverUrl" :src="kngDetail.coverUrl" alt="">
        <div class="yxtulcdsdk-course-player__other-cover break">
          <div class="standard-size-20 mw700">【{{ getKngTypeName(kngDetail.type) }}】{{ kngDetail.title }}</div>
          <div class="standard-size-16 mt12 mw700">{{ playerProps.message }}</div>
          <yxtf-button class="mt24" type="primary" @click="goOut">{{ $t('pc_ulcdsdk_btn_goimmediate'/**立即前往 */) }}</yxtf-button>
        </div>
      </div>

      <!-- 播放接口的错误处理：防作弊、学时改变、错误提示 -->
      <!-- 完课评论 -->
    </div>
  </div>
</template>

<script>
import { addResizeListener, removeResizeListener } from 'yxt-pc';
import SparkMD5 from 'spark-md5';
import { commonUtil as Utils } from 'yxt-biz-pc';
import commonUtil from 'packages/common-util/index';
import { getGuideByType, closeGuideByType, getKngPlayDetail, getO2oProjectName, submitStudyTime, subtractStudyTime, submitSchedule, checkConnection} from '../../service';
import { ENV_FLAG, KNG_TYPE, THIRD_COURSE_TYPE } from '../../enum';
import Countdown from './countdown.vue';
import { activeDurationSubmit, getKngTypeName, getMediaTimeStr, openThirdCourse, yxtReportLog } from '../../utils';
import workerTimer from '../../timer';
import Message from './message';
import { LOG_TYPE } from 'yxt-ulcd-sdk/packages/course-page/enum';
import specialmixin from 'packages/_mixins/360Special';
import { postUserViewRecord, preloadSubmitStudy } from 'yxt-ulcd-sdk/packages/course-page/service';
import qs from 'qs';
import { throttle } from 'lodash';
const PLAYER_MODE = {
  DOC: 1,
  VIDEO: 2,
  WEIKE: 3,
  IFRAME: 4,
  OTHER: 5,
  SCORM: 6
};
export default {
  name: 'Player',
  mixins: [specialmixin],
  components: {
    PCPlayer: commonUtil.AsyncPCplayer,
    Countdown
  },
  inject: ['getCheatInfo', 'getCourseId', 'getDetail', 'getCommonParam', 'getScanQueryData', 'getIsLastKng', 'getGlobalData', 'getIsUsedByKng', 'getFactorConfig'],
  props: {
    kngDetail: { // 课件详情
      type: Object,
      default: () =>({})
    },
    markList: {
      type: Array,
      default: []
    },
    handoutsList: {
      type: Array,
      default: []
    },
    exerciseList: {
      type: Array,
      default: []
    },
    // 指定的播放时间点，比如笔记以及 关键点
    appointViewLoc: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      KNG_TYPE,
      PLAYER_MODE,

      playMode: 0,
      kngPlayDetail: {},
      playerProps: {},
      showMarkGuide: false, // 打点引导

      countdown: 0, // 倒计时时间，单位秒
      rate: 1, // 倍率
      cycleTime: 0, // 播放周期时间，满20秒提交，提交之后重置，等待下一次20s
      unsavedActualTime: 0, // 未提交的实际学时 ，不关联倍速
      unsavedTime: 0, // 未提交的学时， 有可能关联倍速
      hangUpTime: 0, // 挂机时间，鼠标未操作时间
      hangUpStudyTime: 0, // 挂机期间，提交的学时
      hangUpLoc: 0, // 挂机开始的位置点
      viewLoc: 0, // 观看地址
      maxViewLoc: 0, // 最大观看地址
      unknownCount: 0, // 学习提交连续失败次数
      timeNum: 5,
      oddTime: 0,
      webFull: false,
      currentPlaySourceLang: ''
    };
  },
  computed: {
    factorConfig() {
      return this.getFactorConfig();
    },
    scanQueryData() {
      return this.getScanQueryData();
    },
    globalData() {
      return this.getGlobalData();
    },
    detail() {
      return this.getDetail();
    },
    courseId() {
      return this.getCourseId();
    },
    commonParam() {
      return this.getCommonParam();
    },
    watermarkOption() {
      const { displayMode, distributeSourceType } = this.kngDetail;
      const { enabled, type } = this.watermarkConfig;
      return (displayMode === 1 || distributeSourceType) ? this.watermarkConfig : {enabled, type};
    },
    watermarkConfig() {
      // watermarkFlag 0跟随平台 1不开启
      let {enabled, type, watermarkContent, otherColor, otherAlpha, otherFontSize, speed, density} = this.kngPlayDetail.watermarkConfig || {};
      let {envFlag, displayMode} = this.kngDetail;
      if (window.feConfig && window.feConfig.orgCode) {// 私有化都打开假水印
        enabled = 1;
      } else if (this.kngDetail.type === KNG_TYPE.DOC && this.kngDetail.watermarkFlag === 0 && enabled && type === 1 && envFlag !== ENV_FLAG.PRIVATE && displayMode !== 1) { // 非私有云的图片模式的文档 真水印 关闭假
        enabled = 0;
      } else if (this.kngDetail.watermarkFlag === 1) { // 课件关闭水印 关闭假水印
        enabled = 0;
      }
      return {
        enabled,
        type,
        text: Utils.common.getOpenData(watermarkContent),
        color: `#${otherColor}`,
        opacity: parseInt(otherAlpha),
        fontSize: otherFontSize,
        speed,
        density
      };
    },
    isLastKng() {
      return this.getIsLastKng();
    },
    // 新微课播放老微课
    isNewWeiKePlayOldWeiKe() {
      const {type, weikeType, fileId} = this.kngDetail;
      const {oldXuanyesPlayDetails} = this.kngPlayDetail;
      return type === KNG_TYPE.WEIKE && weikeType === 1 && (!oldXuanyesPlayDetails || oldXuanyesPlayDetails.length === 0) && fileId;
    },
    // 时长完课
    isTimeNotComplete() {
      const {timeCompleteFlag, timeCompleteStandard, schedule} = this.kngDetail;
      return schedule < 100 && timeCompleteStandard > 0 && timeCompleteFlag === 0;
    },
    isFullscreen() {
      const {playMode} = this;
      if (playMode === PLAYER_MODE.VIDEO) {
        const player = this.$refs.player && this.$refs.player.getPlayer();
        return player && player.getFullscreen();
      } else if (playMode === PLAYER_MODE.DOC) {
        const player = this.$refs.player;
        return player && player.fullscreen;
      } else if (playMode === PLAYER_MODE.SCORM) {
        const player = this.$refs.player;
        return player && player.isFullScreen;
      } else if (playMode === PLAYER_MODE.WEIKE) {
        const player = this.$refs.player;
        return player && player.player && player.player.fullscreen;
      }
      return false;
    }
  },
  watch: {
    // 记录播放数据 -- 课件切换了, 缓存上一个数据
    // 初始化播放数据
    kngDetail: {
      immediate: true,
      handler(val, old) {
        if (old && old.id !== val.id) {
          this.cachePlayingData(old);
        }
        this.init();
      }
    },
    viewLoc(val) {
      try {
        const info = this.exerciseList.find(item => item.popSec > 0 && item.popSec === val && !item.isAnswer);
        if (info) {
          this.$root.$emit('EXERCISE_VIEW', info, true);
        }
      } catch (e) {}
    },
    webFull(val) {
      document.body.style.overflow = val ? 'hidden' : '';
    }
  },
  created() {
    this.resize = throttle(this._resize, 500);
    // 记录播放数据 -- 页面关闭/刷新
    window.addEventListener('unload', this.unload);
    window.addEventListener('mousemove', this._resetHangUpTime);
    window.addEventListener('keydown', this.keydown);
    this.$root.$on('ROLLBACK', this._rollback);
    this.$root.$on('START_PLAY', this.start);
    this.$root.$on('EXIT_FULLSCREEN', this.exitFullscreen);
    this.$root.$on('CHANGE_PLAY_SOURCE', this.changePlaySource);
  },
  mounted() {
    addResizeListener(this.$el, this.resize);
  },
  beforeDestroy() {
    window.removeEventListener('unload', this.unload);
    window.removeEventListener('mousemove', this._resetHangUpTime);
    window.removeEventListener('keydown', this.keydown);
    this.$root.$off('ROLLBACK', this._rollback);
    this.$root.$off('START_PLAY', this.start);
    this.$root.$off('EXIT_FULLSCREEN', this.exitFullscreen);
    this.$root.$off('CHANGE_PLAY_SOURCE', this.changePlaySource);
    removeResizeListener(this.$el, this.resize);
    //  组件卸载
    this.unload();
    Utils.globalwaterInit.init({ show: true });
  },
  methods: {
    getKngTypeName,
    _resetHangUpTime() {
      this.hangUpTime = 0;
      if (this.submitTimer) { // 暂停后不重置
        this.hangUpStudyTime = 0;
        this.hangUpLoc = this.viewLoc;
      }
    },
    _resize() {
      // html模式播放文档，需要触发resize方法去重置页面布局
      // if (this.kngDetail.displayMode === 1) {
      const ev = document.createEvent('Events');
      ev.initEvent('resize', true, false);
      window.dispatchEvent(ev);
      // }
    },
    unload() {
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '离开当前播放', ext2: this.viewLoc, ext3: this.kngDetail.id});
      this.cachePlayingData(this.kngDetail);
      this.clearAllTimer();
    },
    keydown(e) {
      if (e.code === 'Escape') this.webFull = false;
    },
    // 音视频，进度条最后3秒内的学时提交，viewLoc记录为0
    getResetPositionState() {
      // 倒计时结束，提交一次
      try {
        if (this.kngDetail.type === KNG_TYPE.VIDEO || this.kngDetail.type === KNG_TYPE.AUDIO) {
          const duration = this.$refs.player && this.$refs.player.getPlayer().getDuration();
          return Math.abs(duration - this.viewLoc) <= 3;
        }
      } catch (e) {}
      return false;
    },
    async changePlaySource(item) {
      this.kngPlayDetail.fileId = item.fileId;
      // 重置播放属性
      this.pause();
      this.playMode = 0;
      this.$nextTick(async() => {
        this.currentPlaySourceLang = item.value;
        this.kngDetail.playSourceLang = this.currentPlaySourceLang;
        // 查询新的播放地址
        const res = await this._getKngPlayDetail();
        this.kngPlayDetail = res;
        await this._initPlayerPropsData();
        this.start();
      });
    },
    /** **************** 初始方法 ***************/
    async init() {
      // 清空播放器倍速缓存，下次进来1倍速
      localStorage.removeItem('baiducyberplayer.playbackRate');
      this.postUserViewRecord();
      this.initSubmitStudy();
      this.currentPlaySourceLang = this.getLocalPlayingData(this.kngDetail).currentPlaySourceLang || '';
      const res = await this._getKngPlayDetail();
      this.kngPlayDetail = res;
      this.kngDetail.playSourceLang = this.kngPlayDetail.lang;
      this.$emit('update:handoutsList', this.kngPlayDetail.attList || []);
      // 本地的相关数据
      this._initPlayData();
      // 播放器的相关属性
      await this._initPlayerPropsData();
      // 有按进度提交的课且为0学时课件 && 非跳出的（跳出的点击之后提交学时），直接提交时长
      if (this.playMode !== PLAYER_MODE.OTHER && this.kngDetail.schedule !== 100 && this.kngDetail.studyHours === 0 && this.kngDetail.timeCompleteStandard > 0) {
        // 提交学时-0学时，直接完课
        this.submitStudyTime();
      }
      // 新微课、scorm课件带大纲且在课程包或者项目中时自动网页全屏
      if (!this.getIsUsedByKng() || this.detail.type === KNG_TYPE.COURSE) {
        if (this.playMode === PLAYER_MODE.WEIKE && [7, 8].includes(this.kngDetail.courseSource)) {
          this.webFull = true;
        } else if (this.playMode === PLAYER_MODE.SCORM && this.kngDetail.scormChapter === 1) {
          this.webFull = true;
        }
      }
      this.$nextTick(() => {
      // 无组件回调的，手动触发开始学习
        if (this.playMode === PLAYER_MODE.DOC || this.playMode === PLAYER_MODE.SCORM || this.isNewWeiKePlayOldWeiKe) {
          this.setAllTimer();
        }
        // 位置续播的提示
        if (this.viewLoc > 0 && [KNG_TYPE.AUDIO, KNG_TYPE.VIDEO].includes(this.kngDetail.type)) {
          this.showPlayerMessage(this.$t('pc_ulcdsdk_lbl_playcontinuelabel', [getMediaTimeStr(this.viewLoc)]));
        } else if (this.viewLoc > 1 && this.kngDetail.type === KNG_TYPE.DOC) {
          this.showPlayerMessage(this.$t('pc_ulcdsdk_lbl_doccontinuemsg', [this.viewLoc]));
        }
      });

      // 未学完记录用户准备提交学习日志
      if (this.kngDetail.schedule !== 100) {
        yxtReportLog({category: 502, msg: '用户学习提交'});
      }
    },
    async initSubmitStudy() {
      const params = {
        kngId: this.kngDetail.id,
        courseId: this.courseId,
        ...this.commonParam
      };
      const [, error] = await preloadSubmitStudy(params);
      if (error) {
        // 错误处理
        this._errorHandler(error);
        return Promise.reject(error);
      }
    },
    // 获取播放详情
    async _getKngPlayDetail() {
      let fullname = '';
      const { id, envFlag, type } = this.kngDetail;
      // 企微isv下 前端获取用户名传给后端打水印
      if (localStorage.sourceCode === '100' && type === KNG_TYPE.DOC) {
        try {
          fullname = await Utils.getUsername(localStorage.fullname);
        } catch (error) { }
      }
      const params = {
        kngId: id,
        courseId: this.courseId,
        fullname,
        lang: this.currentPlaySourceLang,
        ...this.commonParam
      };
      const [res, error] = await getKngPlayDetail(params);
      if (error) {
        // 错误处理
        this._errorHandler(error);
        return Promise.reject(error);
      }
      // 校验私有服务器文件的联通性
      if (this.factorConfig.showMixNetwork && envFlag === ENV_FLAG.PRIVATE && [KNG_TYPE.DOC, KNG_TYPE.AUDIO, KNG_TYPE.VIDEO].includes(type) && res.playDetails && res.playDetails.length) {
        const url = res.playDetails[0].url;
        if (url) {
          try {
            await checkConnection(url);
          } catch (e) {
            this._errorHandler({key: 'apis.kng.ip.error'});
            return Promise.reject(e);
          }
        }
      }
      return res;
    },
    postUserViewRecord() {
      if (this.detail.type === KNG_TYPE.COURSE) {
        const {id, sourceKngId} = this.kngDetail;
        const params = {
          kngId: sourceKngId || id,
          targetCode: this.commonParam.targetCode,
          targetId: this.commonParam.targetId || this.courseId
        };
        postUserViewRecord(params);
      }
    },
    // 处理播放相关数据
    _initPlayData() {
      const {type, learnedHours, studyHours, timeCompleteFlag, accHours, viewLoc, timeCompleteStandard} = this.kngDetail;
      const totalStudyTime = studyHours * (timeCompleteStandard || 100) / 100;
      let data = this.getLocalPlayingData(this.kngDetail) || {};
      const defaultMaxViewLoc = type === KNG_TYPE.DOC ? 1 : 0;
      if (data.accHours !== accHours) {
        // 其他地方学过了或者没有本地记录，以接口数据为准
        data = {
          unsavedTime: 0,
          unsavedActualTime: 0,
          cycleTime: 0,
          viewLoc: viewLoc,
          maxViewLoc: data.maxViewLoc || 0
        };
      }
      this.countdown = timeCompleteFlag === 1 ? 0 : parseInt(totalStudyTime - learnedHours - data.unsavedTime);
      this.unsavedTime = data.unsavedTime;
      this.unsavedActualTime = data.unsavedActualTime;
      this.cycleTime = data.cycleTime;
      this.maxViewLoc = Math.max(data.viewLoc, data.maxViewLoc || defaultMaxViewLoc);
      this.viewLoc = this.getSuitableTime(this.appointViewLoc < 0 ? data.viewLoc : this.appointViewLoc, this.maxViewLoc);
      this.hangUpLoc = this.viewLoc;
      this.updateViewLoc({position: this.viewLoc});
    },
    // 设置组件属性数据
    async _initPlayerPropsData() {
      // 正好是随堂练习位置，不自动播放
      const autoPlay = this.exerciseList.findIndex(item => item.popSec > 0 && item.popSec === this.viewLoc && !item.isAnswer) < 0;
      this.playMode = 0;
      const { enableShowMultiple} = this.detail;
      const { type, displayMode, watermarkFlag, distributeSourceType, specialMode, weikeType, envFlag, contentCompleteStandard, title, id, fileType} = this.kngDetail;
      const { videoStream, indexPath, playDetails, oldXuanyesPlayDetails, subtitlesFlag, contentDetails, serverGroupId, fileId, watermarkConfig } = this.kngPlayDetail;
      if (type === KNG_TYPE.VIDEO || type === KNG_TYPE.AUDIO || (type === KNG_TYPE.HTML && !!specialMode && videoStream === THIRD_COURSE_TYPE.VIDEO)) {
        const options = playDetails.map(item => {
          return {
            fileFullUrl: item.url,
            resolution: item.desc,
            fileId
          };
        });
        let isNewSdk = true;
        let enableAi = false;
        let srtUrl = [];
        if (subtitlesFlag === 1 && contentDetails) {
          srtUrl = contentDetails
            .filter(({ desc, url }) => desc.includes('srt') && url)
            .map(({ desc, url }) => ({
              lan: desc === 'srt' ? 'zh' : desc.replace('_srt', ''),
              url
            }));
          if (srtUrl.length > 0) {
            enableAi = true;
          }
        }

        // 音视频+三方课视频流
        this.playerProps = {
          autoStart: autoPlay,
          fileId,
          type: type === KNG_TYPE.AUDIO ? 'audio' : 'video',
          isNeedToken: envFlag !== ENV_FLAG.PRIVATE && !specialMode, // 非三方课的公有云音视频需要解密
          isShowWatermark: true,
          playRate: enableShowMultiple === 1,
          controlbarDrag: true, // 由seek回调中拖拽位置和最大播放位置来控制拖拽结果
          // controlbarDrag: schedule === 100 || enableDrag === 1,
          marks: this.markList, // 关键点
          options: type === KNG_TYPE.AUDIO ? options.slice(0, 1) : options, // 音频取一个
          starttime: this.viewLoc,
          watermarkObj: this.watermarkOption.enabled === 1 ? this.watermarkOption : {},
          enableAi,
          srtUrl,
          lan: Utils.getLanguage(),
          isNewSdk,
          tokenDomainName: window.tokenDomainName
        };
        this.$set(this.kngDetail, 'srtLan', this.playerProps.lan);
        await this.getKeyPointGuide();
        this.playMode = PLAYER_MODE.VIDEO;
      } else if (type === KNG_TYPE.DOC) {
        this.playerProps = {
          mode: displayMode === 1 ? 'html' : 'picture',
          fileId,
          usePPT: fileType === 'ppt',
          start: Math.max(1, this.viewLoc),
          watermarkOrgId: distributeSourceType > 0 ? '7f75434f-4d4d-f507-b67c-bd7d8af1324f' : '',
          showWatermark: watermarkFlag !== 1,
          watermarkCode: 'kng',
          watermarkConfig: this.watermarkConfig,
          serverGroupId,
          virtual: 'auto'
        };
        this.playMode = PLAYER_MODE.DOC;
      } else if (type === KNG_TYPE.SCORM) {
        this.playerProps = {
          query: {
            ...this.scanQueryData,
            kngId: id,
            courseId: this.courseId
          },
          title,
          playInfo: this.kngPlayDetail,
          watermarkConfig: this.watermarkOption
        };
        this.playMode = PLAYER_MODE.SCORM;
      } else if (type === KNG_TYPE.WEIKE && weikeType === 2 || this.isNewWeiKePlayOldWeiKe) {
        // 新微课（weikeType） + 新微课播放老微课（fileId）
        this.playerProps = {
          contentId: fileId,
          rightAreaWidth: 256,
          speedChangeIsShow: enableShowMultiple === 1,
          playerConfigs: enableShowMultiple === 1 ? { playbackRate: [1, 1.25, 1.5, 1.75, 2] } : {}
        };
        this.playMode = PLAYER_MODE.WEIKE;
      } else if ((type === KNG_TYPE.HTML && !specialMode) ||
      (type === KNG_TYPE.HTML && !!specialMode && (videoStream === THIRD_COURSE_TYPE.INNER || videoStream === THIRD_COURSE_TYPE.DEFAULT)) ||
      type === KNG_TYPE.WEIKE) {
        let src = playDetails && playDetails.length ? playDetails[0].url : '';
        if (type === KNG_TYPE.HTML && specialMode === 1 && videoStream === THIRD_COURSE_TYPE.DEFAULT) {
          // 三方课默认
          const projectName = await this._getProjectName();
          const token = localStorage.token || '';
          const { orginalKngId, code, orderNum, orderId, customParam } = this.kngPlayDetail;
          const { enabled, text, type, fontSize, color, opacity, speed, density } = this.watermarkConfig;
          const query = {
            token,
            platformVersion: 2,
            pid: code,
            order: orderNum || orderId || '',
            show: enabled,
            marquee: text,
            type,
            fontsize: fontSize,
            color,
            opacity,
            density,
            speed,
            projectName,
            customParam
          };
          src = `${window.feConfig.common.kngThird}#/play/${orginalKngId || ''}?${qs.stringify(query)}`;
        } else if (type === KNG_TYPE.HTML && !!specialMode && videoStream === THIRD_COURSE_TYPE.INNER) {
          // 三方课内嵌
          if (contentCompleteStandard === 2) {
            await this.$msgbox({
              type: 'warning',
              title: this.$t('pc_kng_thired_msg_title'),
              message: this.$t('pc_kng_thired_msg_progress'),
              showCancelButton: false,
              closeOnClickModal: false,
              confirmButtonText: this.$t('pc_kng_common_btn_iknow')
            });
          }
          src = `${src}&token=${localStorage.token}`;
        } else if (type === KNG_TYPE.WEIKE) {
          // 老微课内嵌
          src = oldXuanyesPlayDetails && oldXuanyesPlayDetails.length ? oldXuanyesPlayDetails[0].url : '';
        }
        this.playerProps = {src};
        this.playMode = PLAYER_MODE.IFRAME;
      } else {
        let src = playDetails && playDetails.length ? playDetails[0].url : '';
        let message = '';
        if (type === KNG_TYPE.HTML && !!specialMode && videoStream === THIRD_COURSE_TYPE.OUTER) {
          // 三方课外跳
          if (contentCompleteStandard === 2) { // 按进度
            message = this.$t('pc_ulcdsdk_lbl_thiredoutdelaytip'/** 该课程来源于第三方供应商，需进行跳转播放，学习进度将由供应商提供，进度显示存在延迟。 */);
          } else { // 立即完成
            message = this.$t('pc_kng_thired_msg_jump'/** 课程来源于第三方供应商，需进行跳转播放。 */);
          }
        } else if (type === KNG_TYPE.NEWLINK) { // 立即完成
          src = indexPath;
          message = this.$t('pc_kng_newlink_msg_tip'/** 该课程来源于第三方供应商，将会跳转到新页面学习该课程*/);
        }
        this.playerProps = {src, message};
        // 展示文案，跳出去
        this.playMode = PLAYER_MODE.OTHER;
        if (!this.factorConfig.showLinkTip && type !== KNG_TYPE.NEWLINK) {
          this.goOut();
        } else {
          this.showGoOutWarning();
        }
      }
      // 在线课堂水印开启 关闭全局水印
      if (this.playMode !== PLAYER_MODE.OTHER && watermarkConfig && watermarkConfig.enabled) {
        Utils.globalwaterInit.init({ show: false });
      }
    },
    showGoOutWarning() {
      this.$msgbox({
        type: 'warning',
        title: this.$t('pc_kng_common_msg_title'),
        message: this.playerProps.message,
        showCancelButton: true,
        closeOnClickModal: false
      }).then(() => {
        this.goOut();
      });
    },
    goOut() {
      const { src} = this.playerProps;
      // 提交学时-跳出去，更新进度
      this.submitStudyTime();
      src && openThirdCourse(src);
    },
    // 获取项目名称,项目中的三方课
    async _getProjectName() {
      if (!this.scanQueryData.targetId) return '';
      const [res, error] = await getO2oProjectName(this.scanQueryData.targetId);
      if (error) {
        return '';
      }
      return res.name;
    },
    onPageChange(page) {
      this.updateViewLoc({position: page});
    },
    onReadyVideo() {
      this.yxtPreScreenCap({'playrefs': this.$refs.player});
    },
    onMetaVideo() {
      // 禁止画中画
      document.querySelector('video').setAttribute('disablePictureInPicture', '');

      const { rate } = this.$route.query;
      const { enableShowMultiple } = this.detail;
      if (rate > 1 && rate <= 2) {
        if (enableShowMultiple === 1) {
          const player = this.$refs.player && this.$refs.player.getPlayer();
          player && player.setPlaybackRate(rate);
        } else if (enableShowMultiple !== 1) {
          const { name, params, query } = this.$route;
          this.$router.replace({ name, params, query: { ...query, rate: 1 } });
        }
      }
    },
    /** ************ 播放器的回调方法 *****************/
    onReady() {
      this.$refs.player.player && this.$refs.player.player.togglePlay();
    },
    updateViewLoc(e) {
      if (this.seekOverview) {
        // 超出最大值，重置到最大位置
        this.playViewLoc(this.maxViewLoc);
        this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '防拖拽回退', ext2: this.maxViewLoc});
        this.seekOverview = false;
        this.$message.warning(this.$t('pc_sdk_lbl_dragincompleterange'));
        return;
      }
      const loc = Math.floor(e.position);
      this.viewLoc = loc;
      this.maxViewLoc = Math.max(this.viewLoc, this.maxViewLoc);
      this.kngDetail.viewLoc = loc;
      if (this.$refs.player && !this.$refs.player.useCustomVersion && this.$refs.player.player && this.$refs.player.player.setMaxSeekTime && this.detail.enableDrag !== 1) {
        if (this.kngDetail.schedule !== 100) {
          this.$refs.player.player.setMaxSeekTime(this.maxViewLoc);
        } else {
          this.$refs.player.player.setMaxSeekTime();
        }
      }
    },
    onPause() {
      let volume;
      try {
        if (this.playMode === PLAYER_MODE.VIDEO) {
          const player = this.$refs.player && this.$refs.player.getPlayer();
          player && (volume = `volume:${player.getVolume()}`);
        }
      } catch (error) { }
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '暂停', ext2: this.viewLoc, ext3: volume});
      this.clearAllTimer();
    },
    onPlay() {
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '开始播放', ext2: this.viewLoc});
      this.setAllTimer();
    },
    // 音视频播放结束-学时提交
    async onEnd() {
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '播放结束', ext2: this.viewLoc, ext3: this.kngDetail.id});
      this.updateViewLoc({position: 0});
      this.clearAllTimer();
      if (this.kngDetail.schedule === 100 && !this.isLastKng && this.detail.type === KNG_TYPE.COURSE) {
        // 音视频之前已完成，直接下一个
        this.$root.$emit('AUTO_NEXT_MSG', true, true);
      } else if (this.kngDetail.schedule === 100 && (this.detail.type === KNG_TYPE.AUDIO || this.detail.type === KNG_TYPE.VIDEO) && !this.getIsUsedByKng()) {
        this.$root.$emit('SCHEDULE_UPDATE', null, true);
      }
      // 提交学时-播放器结束
      this.submitStudyTime(true);
    },
    onRateChange(rate) {
      if (!rate || this.rate === rate) return;
      this.calculateOddTime();
      this.rate = rate;
      this.resetCountdownTimer(!!this.countdownTimer);
      this.showPlayerMessage(this.$t('pc_ulcdsdk_lbl_changeratelabel', [rate]));
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '倍速切换', ext2: this.viewLoc, ext3: this.rate});
      if (this.playMode === PLAYER_MODE.VIDEO) { // 音视频
        const { name, params, query } = this.$route;
        if (query.rate !== rate) {
          this.$router.replace({ name, params, query: { ...query, rate } });
        }
      }
    },
    // 微课&scorm内容完成
    onContentComplete() {
      // 内容完课
      const {contentCompleteStandard, contentCompleteFlag} = this.kngDetail;
      if (contentCompleteStandard > 0 && contentCompleteFlag === 0) {
        // 提交学时-内容未完成，去内容完课
        this.submitStudyTime(false, {end: 1, studyByEnd: 1});
      }
    },
    // 微课内容进度
    onContentSchedule({ progress }) {
      // 内容完课
      const { contentCompleteStandard, contentCompleteFlag, timeCompleteStandard } = this.kngDetail;
      if (contentCompleteStandard > 0 && contentCompleteFlag === 0) {
        if (timeCompleteStandard > 0) { // 时长+内容完成
          if (progress === 1) {
            this.submitStudyTime(false, { end: 1, studyByEnd: 1 });
          }
        } else { // 内容完成
          this.submitSchedule(Number((progress * 100).toFixed(2)));
        }
      }
    },
    // 进度条拖动
    onSeek(e) {
      if (e.offset > this.maxViewLoc && this.kngDetail.schedule !== 100 && this.detail.enableDrag !== 1) {
        if (!this.$refs.player.useCustomVersion && this.$refs.player.player && this.$refs.player.player.setMaxSeekTime) {
          this.playViewLoc(this.maxViewLoc);
          this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '防拖拽回退', ext2: this.maxViewLoc});
          this.$message.warning(this.$t('pc_sdk_lbl_dragincompleterange'/** 已开启防拖拽，未完成的课程仅支持在已学范围内拖动 */));
        } else {
          // 拖拽的位置超过了最大值
          this.seekOverview = true;
        }
      }
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '拖拽', ext2: JSON.stringify(e)});
    },
    onBuffer() {
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: 'buffer'});
      this.clearAllTimer();
    },
    onQualityChange(e) {
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '切换清晰度', ext2: JSON.stringify(e)});
      this.clearAllTimer();
      const qLabel = e.levels[e.currentQuality].label;
      this.showPlayerMessage(this.$t('pc_ulcdsdk_lbl_changeto', [qLabel]));
    },
    onAiLanChange(e) {
      this.$set(this.kngDetail, 'srtLan', e);
    },
    showPlayerMessage(msg) {
      if (!msg) return;
      if ([KNG_TYPE.AUDIO, KNG_TYPE.VIDEO, KNG_TYPE.DOC].includes(this.kngDetail.type)) {
        Message(msg);
      }
    },
    onError(e) {
      let ext2 = '播放失败';
      try {
        ext2 = JSON.stringify(e);
      } catch (e) { }
      this.$root.$emit('LOG', { type: LOG_TYPE.INFO, ext1: '播放失败', ext2, ext3: JSON.stringify(this.playerProps) });
    },
    onSrtData(e) {
      this.$emit('update:srtData', e || []);
    },
    /** ************* 学习提交逻辑 *****************/
    // 缓存相关数据
    cachePlayingData(kngDetail) {
      if (this.globalData.startWithAirplaneMode) return;
      const key = this.getCacheKey(kngDetail);
      const data = {
        accHours: kngDetail.accHours, // 累计时长
        unsavedTime: this.unsavedTime, // 未提交时间
        unsavedActualTime: this.unsavedActualTime,
        cycleTime: this.cycleTime,
        viewLoc: this.viewLoc,
        maxViewLoc: this.maxViewLoc,
        currentPlaySourceLang: this.currentPlaySourceLang
      };
      const cacheData = JSON.parse(localStorage.KNG_CACHE_DATA || '{}');
      cacheData[key] = data;
      localStorage.KNG_CACHE_DATA = JSON.stringify(cacheData);
    },
    // 获取本地缓存数据
    getLocalPlayingData(kngDetail) {
      const key = this.getCacheKey(kngDetail);
      const cacheData = JSON.parse(localStorage.KNG_CACHE_DATA || '{}');
      return cacheData[key] || {};
    },
    // 获取缓存key
    getCacheKey(kngDetail) {
      const userId = localStorage.userId;
      const targetCode = this.commonParam.targetCode;
      return `${userId}|${targetCode}|${this.courseId || '-'}|${kngDetail.id}`;
    },
    // 触发开始学习
    start() {
      const {type, weikeType} = this.kngDetail;
      if (type === KNG_TYPE.AUDIO || type === KNG_TYPE.VIDEO || (type === KNG_TYPE.WEIKE && weikeType === 2)) {
        this.$refs.player && this.$refs.player.play && this.$refs.player.play();
      } else {
        this.setAllTimer();
      }
      this.expandFullscreen();
    },
    // 触发暂停学习
    pause() {
      const {type, weikeType} = this.kngDetail;
      if (type === KNG_TYPE.AUDIO || type === KNG_TYPE.VIDEO || (type === KNG_TYPE.WEIKE && weikeType === 2)) {
        this.$refs.player && this.$refs.player.pause && this.$refs.player.pause();
      } else {
        this.clearAllTimer();
      }
    },

    resetCountdownTimer(openTimer) {
      if (this.countdownTimer) {
        workerTimer.clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      const interval = this.detail.enableMultiple ? 1000 / this.rate : 1000;
      this.handleOddTime();
      if (openTimer) {
        // 倒计时定时器
        this.prevTime = new Date().getTime();
        this.countdownTimer = workerTimer.setInterval(() => {
          this.prevTime = new Date().getTime();
          if (this.countdown > 0) {
            this.countdown--;
            const { studyHours, type} = this.kngDetail;
            // 音视频每3分钟补1秒学时
            if ([KNG_TYPE.VIDEO, KNG_TYPE.AUDIO].includes(type) && studyHours > 1200 && (studyHours - this.countdown) % 180 === 0) {
              this.countdown--;
              this.unsavedTime++;
            }
          }
          this.unsavedTime++;
        }, interval);
      }
    },
    // 开始定时器
    setAllTimer() {
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '开始定时器', ext2: this.viewLoc, ext3: this.getCheatInfo().enabled});
      if (this.globalData.startWithAirplaneMode) return;
      // 倒计时定时器
      if (!this.countdownTimer) {
        this.resetCountdownTimer(true);
      }

      // 学时提交定时器
      if (!this.submitTimer) {
        const { submitTime } = this.kngDetail;
        this.submitTimer = workerTimer.setInterval(() => {
          this.cycleTime++;
          this.unsavedActualTime++;
          if (this.countdown <= 0 && this.isTimeNotComplete && this.unsavedTime > 0) {
            this.submitStudyTime(true);
          } else if (this.cycleTime >= submitTime && this.unsavedTime > 0) {
            // 提交学时-定时器20s一次，剩余时间小于等于20+5s内，不调用接口，由倒计时触发
            const { studyHours, learnedHours} = this.kngDetail;
            if (this.isTimeNotComplete && (studyHours - learnedHours) <= submitTime + 5) {
              return;
            }
            this.submitStudyTime(true);
          }
        }, 1000);
      }

      // 开启防作弊，开启防作弊定时器
      const {enabled, maxMin, selectType} = this.getCheatInfo();
      if (enabled && !this.hangUpTimer) {
        this.hangUpTimer = workerTimer.setInterval(() => {
          this.hangUpTime++;
          if (this.hangUpTime >= maxMin * 60) {
            // 触发防挂机,弹出弹框
            this.hangUpTime = 0;
            // 纯内容或者时长完课走模式1
            const useTypeOne = (this.kngDetail.timeCompleteStandard === 0 && this.kngDetail.contentCompleteStandard > 0) ||
            this.kngDetail.timeCompleteFlag === 1 ||
            selectType === 1;
            this._errorHandler({key: useTypeOne ? 'complete.study.onHook' : 'study.onHook'});
            this.$root.$emit('LOG', {type: LOG_TYPE.HANGUP, ext1: '防挂机弹窗'});
          }
        }, 1000);
      }
    },
    // 销毁定时器
    clearAllTimer() {
      this.calculateOddTime();
      this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '暂停定时器', ext2: this.viewLoc});
      if (this.countdownTimer) {
        workerTimer.clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
      if (this.submitTimer) {
        workerTimer.clearInterval(this.submitTimer);
        this.submitTimer = null;
      }
      if (this.hangUpTimer) {
        workerTimer.clearInterval(this.hangUpTimer);
        this.hangUpTimer = null;
      }
    },
    // 计算由于定时器暂停导致丢失的零碎时间
    calculateOddTime() {
      try {
        if (this.kngDetail.type !== KNG_TYPE.VIDEO && this.kngDetail.type !== KNG_TYPE.AUDIO) return;
        if (this.prevTime) {
          const oddTime = Math.min(1000, (new Date().getTime() - this.prevTime) * this.rate);
          this.oddTime += oddTime;
          this.prevTime = null;
        }
      } catch (e) {}
    },
    // 处理零碎时间
    handleOddTime() {
      try {
        if (this.oddTime >= 1000) { // 如果之前开启过定时器，再次开始的时候直接消耗一秒，放置不满一个周期（<1s）的时间丢失
          this.countdown--;
          this.unsavedTime++;
          this.oddTime = Math.max(0, this.oddTime - 1000);
        }
      } catch (e) {}
    },
    async sleep(time = 5) {
      return new Promise(resolve => {
        workerTimer.setTimeout(() => {
          resolve();
        }, time);
      });
    },
    /**
     * @param {*} checkTime 是否校验提交的时长，时长为0不提交
     * @param {*} other 提交学时（包括内容完课other:{end:1,studyByEnd:1}）
     */
    async submitStudyTime(checkTime = false, other = {}) {
      if (this.globalData.startWithAirplaneMode) return;
      const onlyContentSubmit = other.end === 1 && other.studyByEnd === 1;// 内容完课提交
      if (this.kngDetail.schedule < 100 && !onlyContentSubmit) {
        await this.sleep(5); // 延迟5ms，磨平倒计时定时器和20s定时器启动的时间差，例如20s视频，倒计时结束时，cycleTime可能还是19
      }
      if (checkTime && this.unsavedTime === 0) return; // 防止学时提交同时触发，过滤掉学时为0的提交
      activeDurationSubmit();
      if (!onlyContentSubmit) this.cycleTime = 0; // 学时提交，cycleTime重置
      const savingTime = onlyContentSubmit ? 0 : this.unsavedTime; // 保存中的提交学时时长
      const savingActualTime = onlyContentSubmit ? 0 : this.unsavedActualTime; // 保存中的实际时长
      this.unsavedTime -= savingTime;
      this.unsavedActualTime -= savingActualTime;
      const {targetId, targetCode, studyParam, targetParam} = this.commonParam;
      const viewLoc = this.getResetPositionState() ? 0 : this.viewLoc;
      const params = {
        acqSecond: savingTime,
        actualSecond: savingActualTime,
        speed: this.rate,
        kngId: this.kngDetail.id,
        courseId: this.courseId,
        viewLoc,
        targetId,
        targetCode,
        originOrgId: studyParam.originOrgId,
        targetParam,
        ...other
      };
      this.$root.$emit('LOG', {type: LOG_TYPE.SUBMIT, ext1: '进度提交前', trackId: this.kngDetail.lastTrackId, params});
      const [res, error] = await submitStudyTime(params, this.kngDetail.lastTrackId);
      if (error) {
        this.$root.$emit('LOG', {type: LOG_TYPE.SUBMIT, ext1: '进度提交失败', trackId: this.kngDetail.lastTrackId, params, res: error});
        // 提交失败，提交中学时放回去
        this.unsavedTime += savingTime;
        this.unsavedActualTime += savingActualTime;
        // 处理错误数据
        this._errorHandler(error, false);
        return;
      }
      this.$root.$emit('LOG', {type: LOG_TYPE.SUBMIT, ext1: '进度提交成功', trackId: this.kngDetail.lastTrackId, params, res});
      this.hangUpStudyTime += res.acqChangeHours; // 防挂机期间提交的acq已后端接收的为主
      this.unknownCount = 0;
      // 内容完课状态同步
      if (other.end === 1 && other.studyByEnd === 1) {
        this.kngDetail.contentCompleteFlag = 1;
      }
      // 音视频完课时是否自动播放下一个标记
      if ((this.kngDetail.type === KNG_TYPE.VIDEO || this.kngDetail.type === KNG_TYPE.AUDIO) && this.kngDetail.schedule < 100 && res.schedule === 100 && viewLoc === 0) {
        if (this.detail.type === KNG_TYPE.COURSE) {
          // 课程包中视频自动下一个
          this.kngDetail.autoNext = true;
        } else if ((this.detail.type === KNG_TYPE.AUDIO || this.detail.type === KNG_TYPE.VIDEO) && !this.getIsUsedByKng()) {
          // 项目大纲中音视频自动下一个
          this.detail.o2oAutoNext = true;
        }
      }
      // 更新数据
      this._syncDetail(res);
    },
    // 微课内容进度提交
    async submitSchedule(schedule = 0) {
      const { targetId, targetCode, studyParam } = this.commonParam;
      const params = {
        kngId: this.kngDetail.id,
        courseId: this.courseId,
        targetId,
        targetCode,
        originOrgId: studyParam.originOrgId,
        sourceType: 0,
        schedule
      };
      this.$root.$emit('LOG', { type: LOG_TYPE.SUBMIT, ext1: '微课进度提交前', params });
      const [res, error] = await submitSchedule(params);
      if (error) {
        this.$root.$emit('LOG', { type: LOG_TYPE.SUBMIT, ext1: '微课进度提交失败', params, res: error });
        return;
      }
      this.$root.$emit('LOG', { type: LOG_TYPE.SUBMIT, ext1: '微课进度提交成功', params, res });
      // 内容完课状态同步
      if (schedule === 100) {
        this.kngDetail.contentCompleteFlag = 1;
      }
      // 更新数据
      this._syncDetail(res);
    },
    _syncDetail(res) {
      const {schedule, timeCompleteFlag, accHours, acqHours} = res;
      if (schedule === 100 && this.kngDetail.schedule !== schedule) {
        yxtReportLog({ category: 502, msg: '用户学习提交', w_succ: 1 });
      }
      this.kngDetail.schedule = schedule;
      this.kngDetail.timeCompleteFlag = timeCompleteFlag;
      this.kngDetail.accHours = accHours;
      this.kngDetail.learnedHours = acqHours;
    },
    _errorHandler(error, catchUnknown = true) {
      this.pause();
      this.$root.$emit('ERROR_HANDLER', error, {
        isInner: true,
        catchUnknown,
        callback: (command) => {
          if (command === 'restudy') {
            this.unknownCount = 0;
            this.$parent.restudy();
          } else if (command === 'continue') {
            this.unknownCount = 0;
            this.start();
          } else if (command === 'unknown') {
            this.unknownCount += 1;
            if (this.unknownCount < 2) {
              this.start();
            } else {
              this.showNetworkError();
            }
          }
        }
      });
    },
    showNetworkError() {
      this.exitFullscreen();
      this.$confirm(this.$t('pc_kng_msg_coursesubmitnetworkerror'), this.$t('pc_kng_tit_networkerror'), {
        type: 'warning',
        showCancelButton: true,
        cancelButtonText: this.$t('pc_kng_detail_btn_continue_learn'),
        confirmButtonText: this.$t('pc_kng_btn_freshreload')
      })
        .then(() => {
          // 刷新重试
          window.location.reload();
        })
        .catch(() => {
          // 继续学习
          this.globalData.startWithAirplaneMode = true;
          this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '无网络学习', ext2: this.viewLoc});
          this.start();
        });
    },
    // 扣除学时
    async _rollback() {
      const {targetId, targetCode, originOrgId, targetParam} = this.commonParam;
      const params = {
        backTime: this.hangUpStudyTime,
        courseId: this.courseId,
        kngId: this.kngDetail.id,
        targetCode,
        targetId,
        originOrgId,
        viewLoc: this.hangUpLoc,
        sign: SparkMD5.hash(`${this.kngDetail.id}-${localStorage.userId}`),
        targetParam
      };
      const res = await subtractStudyTime(params, this.kngDetail.lastTrackId);
      this.$root.$emit('LOG', {type: LOG_TYPE.SUBMIT, ext1: '防作弊减去学时', trackId: this.kngDetail.lastTrackId, params, res});
      this._syncDetail(res);
      this.countdown += this.hangUpStudyTime;
      this.viewLoc = this.hangUpLoc;
      // 设置位置，不需要播放
      this.playViewLoc(this.hangUpLoc);
      setTimeout(() => this.pause());

    },
    // 指定播放位置
    playViewLoc(time) {
      time = this.getSuitableTime(time, this.maxViewLoc);
      const {type} = this.kngDetail;
      if (type === KNG_TYPE.VIDEO || type === KNG_TYPE.AUDIO) {
        this.$refs.player.seek(time);
      } else if (type === KNG_TYPE.DOC) {
        this.$refs.player.scrollToPage(time);
      }
    },
    // 获取合适的跳转位置
    getSuitableTime(targetTime, viewLoc) {
      if (targetTime < 0) return viewLoc;
      if (targetTime > viewLoc && !this.detail.enableDrag && this.kngDetail.schedule < 100) {
        // 禁止拖拽的禁止跳转的情况
        this.$message.warning(this.$t('pc_kng_mark_tip_study_onschedule'));
        return viewLoc;
      }
      return targetTime;
    },
    toMarkJump(time) {
      this.$root.$emit('PLAY_APPOINT', time);
    },
    async getKeyPointGuide() {
      const {state} = await getGuideByType(6);
      this.showMarkGuide = !state;
    },
    async closeGuid() {
      await closeGuideByType(6);
      this.showMarkGuide = false;
    },
    expandFullscreen() {
      if (!this.globalData.autoFullscreen) return;
      this.globalData.autoFullscreen = false;
      const {playMode} = this;
      if (playMode === PLAYER_MODE.VIDEO) {
        const player = this.$refs.player && this.$refs.player.getPlayer();
        player && player.setFullscreen(true);
      } else if (playMode === PLAYER_MODE.DOC) {
        const player = this.$refs.player;
        player && player.intoFullscreen && player.intoFullscreen();
        player && player.toggleFullScreen && player.toggleFullScreen();
      } else if (playMode === PLAYER_MODE.SCORM) {
        const player = this.$refs.player;
        player && player.onFullscreenChange(true, {currentTarget: {}});
      }
    },
    exitFullscreen() {
      this.globalData.autoFullscreen = this.isFullscreen;
      const {playMode} = this;
      if (playMode === PLAYER_MODE.VIDEO) {
        const player = this.$refs.player && this.$refs.player.getPlayer();
        player && player.setFullscreen(false);
      } else if (playMode === PLAYER_MODE.DOC) {
        const player = this.$refs.player;
        player && player.exitFullscreen();
      } else if (playMode === PLAYER_MODE.SCORM) {
        const player = this.$refs.player;
        player && player.onFullscreenChange(false, {currentTarget: {}});
      } else if (playMode === PLAYER_MODE.WEIKE) {
        const player = this.$refs.player;
        player && player.exitFullscreen && player.exitFullscreen();
      }
    }
  }
};
</script>
