<!-- 创建时间2023/02/13 09:24:45 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：练习功能区 -->
<template>
  <div class="yxtulcdsdk-course-player__functional">
    <div class="mt24 ml24 mr40 standard-size-16">{{ $t('pc_ulcdsdk_tit_exerciseinclass'/**随堂练习 */) }}</div>
    <div class="yxtulcdsdk-flex-1 h0 p24">
      <yxtf-scrollbar :fit-height="true">
        <div v-for="item in list" :key="item.id" class="yxtulcdsdk-course-player__exercise yxtulcdsdk-flex-center">
          <yxtf-svg
            :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/pc/svg`"
            width="29px"
            height="35px"
            icon-class="exercise_in_class"
          />
          <div class="ml16 yxtulcdsdk-flex-1 w0">
            <div class="standard-size-14 opacity9 ellipsis">{{ item.trName }}</div>
            <div class="standard-size-12 opacity6 mt4">{{ item.isAnswer ? $t('pc_kng_exercise_answered'/**已作答 */) : $t('pc_kng_exercise_not_answer'/**尚未作答 */) }}</div>
          </div>
          <yxtf-button
            class="ml16"
            type="primary"
            size="small"
            @click="toView(item)"
          >{{ $t('pc_kng_exercise_view'/**查看 */) }}</yxtf-button>
        </div>

      </yxtf-scrollbar>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Exercise',
  props: {
    list: {
      type: Boolean,
      default: []
    }
  },
  data() {
    return {};
  },
  methods: {
    toView(item) {
      this.$root.$emit('EXERCISE_VIEW', item);
    }
  }
};
</script>
