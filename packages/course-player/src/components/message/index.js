import Vue from 'vue';
import main from './index.vue';
import { isVNode } from 'yxt-ulcd-sdk/src/utils/vdom';

const instances = [];
let seed = 1;
let instance ;
const MessageConstructor = Vue.extend(main);
const Message = function(options, container = document.querySelector('.yxtulcdsdk-course-player__inner-left')) {
  options = options || {};
  if (typeof options !== 'object') {
    options = {
      message: options
    };
  }
  instance = new MessageConstructor({data: options});
  const id = 'player_message_' + seed++;
  instance.id = id;
  instance.onClose = function() {
    Message.close(id);
  };
  if (isVNode(instance.message)) {
    instance.$slots.default = [instance.message];
    instance.message = null;
  }
  const findInstance = instances.find(item => item.message === instance.message);
  if (findInstance) {
    findInstance.clearTimer();
    findInstance.startTimer();
    return;
  }
  instance.$mount();
  container && container.appendChild(instance.$el);
  let verticalOffset = options.offset || 50;
  instances.forEach(item => {
    verticalOffset += item.$el.offsetHeight + 6;
  });
  instance.verticalOffset = verticalOffset;
  instance.visible = true;
  instances.push(instance);
  return instance;
};
Message.close = function(id) {
  let len = instances.length;
  let index = -1;
  for (let i = 0; i < len; i++) {
    if (id === instances[i].id) {
      index = i;
      instances.splice(i, 1);
      break;
    }
  }
  if (len <= 1 || index === -1 || index > instances.length - 1) return;
  const removedHeight = instances[index].$el.offsetHeight;
  for (let i = index; i < len - 1 ; i++) {
    let dom = instances[i].$el;
    dom.style['bottom'] =
      parseInt(dom.style['bottom']) - removedHeight - 6 + 'px';
  }

};
export default Message;
