<template>
  <transition name="yxtulcdsdk-player-message-fade" @after-leave="handleAfterLeave">
    <div
      v-show="visible"
      class="yxtulcdsdk-player-message"
      :style="positionStyle"
    >
      <slot>
        <span class="yxtulcdsdk-player-message__content">{{ message }}</span>
      </slot>
    </div>
  </transition>
</template>

<script>

export default {
  data() {
    return {
      visible: false,
      message: '',
      duration: 3000,
      verticalOffset: 50,
      timer: null
    };
  },

  computed: {
    positionStyle() {
      return {
        'bottom': `${ this.verticalOffset }px`
      };
    }
  },
  methods: {
    handleAfterLeave() {
      this.$destroy(true);
      this.$el.parentNode.removeChild(this.$el);
    },

    clearTimer() {
      clearTimeout(this.timer);
    },

    startTimer() {
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          if (this.visible) {
            this.visible = false;
            this.onClose();
          }
        }, this.duration);
      }
    }
  },
  mounted() {
    this.startTimer();
  }
};
</script>
