<!-- 创建时间2023/02/14 14:51:39 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课程包/单课件课程的操作等 -->
<template>
  <div v-if="type === 'action'" class="yxtulcdsdk-flex-center yxtulcdsdk-course-page__other-operation">
    <span v-for="item in courseOperateConfigs" :key="item.type" @click="toOperate(item)">
      <div class="yxtulcdsdk-flex-center-center yxtulcdsdk-flex-vertical">
        <yxtf-svg
          class="color-gray-9 mt2"
          width="18px"
          height="18px"
          :remote-url="remoteUrl"
          :icon-class="item.icon"
        />
        <div class="standard-size-12 mt5 color-gray-7">{{ item.title }}</div>
      </div>
    </span>
    <yxtf-popover
      v-if="moreOperateConfigs.length"
      trigger="hover"
      placement="bottom-end"
      :offset="12"
      :effect="true"
    >
      <div
        v-for="item in moreOperateConfigs"
        :key="item.type"
        class="yxtulcdsdk-flex-center yxtulcdsdk-course-page__other-operation-hover"
        @click="toOperate(item)"
      >
        <yxtf-svg
          class="mr4"
          width="18px"
          height="18px"
          :remote-url="remoteUrl"
          :icon-class="item.icon"
        />
        <span class="standard-size-14 nowrap">{{ item.title }}</span>
      </div>
      <div slot="reference" class="yxtulcdsdk-flex-center-center yxtulcdsdk-flex-vertical">
        <yxtf-svg
          width="18px"
          height="18px"
          :remote-url="remoteUrl"
          icon-class="more"
        />
        <div class="standard-size-12 mt5 color-gray-7">{{ $t('pc_kng_mgmt_lbl_opt_more'/** 更多*/) }}</div>
      </div>
    </yxtf-popover>

    <!-- 举报组件 -->
    <yxtbiz-complain ref="complain" :target="complainTarget" />
    <yxtf-dialog :visible.sync="showShareCode" destroy-on-close width="520px">
      <yxtbiz-qrcode v-bind="shareData" />
    </yxtf-dialog>
  </div>
  <div v-else-if="type === 'score'" class="standard-size-12 color-gray-8">
    <span v-if="!isUsedByKng" class="yxtulcdsdk-course-operation__split">
      <!-- 评分 -->
      <template v-if="!!courseScore.score">
        <yxt-popover placement="bottom-start" trigger="hover" :disabled="courseScore.list.length === 0">
          <div class="standard-size-12">
            <div v-for="(item,index) in courseScore.list" :key="index" :class="index>0?'mt20':''">
              <div class="color-gray-9">{{ item.name }}</div>
              <div class="yxtulcdsdk-flex-center mt5">
                <yxtf-rate :value="item.score" disabled :icon-size="10" />
                <span class="yxtf-color-warning ml8">{{ item.score.toFixed(1) }}</span>
              </div>
            </div>
          </div>
          <div class="yxtulcdsdk-inline-flex yxtulcdsdk-flex-center" slot="reference">
            <yxtf-rate
              class="yxtulcdsdk-flex-center"
              :value="courseScore.score"
              disabled
              :icon-size="10"
            />
            <span class="ml2 color-gray-9">{{ courseScore.score.toFixed(1) }}</span>
            <yxtf-svg
              v-if="courseScore.list.length > 0"
              class="ml4 color-gray-8"
              width="12px"
              height="12px"
              :remote-url="remoteUrl"
              icon-class="down_arrow"
            />
          </div>
        </yxt-popover>
      </template>
      <span v-else>{{ $t('pc_kng_detail_tools_no_score'/** 暂无评分*/) }}</span>
    </span>
    <span v-for="(text,index) in timeTextAll" :key="index" class="yxtulcdsdk-course-operation__split">{{ text }}</span>
    <slot v-if="isUsedByKng && !timeTextAll.length" name="empty"></slot>
  </div>
  <div v-else-if="type === 'all'" class="yxtulcdsdk-course-operation">
    <div class="yxtulcdsdk-flex-center">
      <!-- 课程名、相关操作 -->
      <span class="standard-size-16 ellipsis">{{ detail.title }}</span>
      <yxtf-tooltip
        v-for="item in courseOperateConfigs"
        :key="item.type"
        :disabled="disabled"
        :content="item.title"
      >
        <div
          class="ml18 yxtulcdsdk-flex-center"
          :class="item.highlight ? 'hand':'yxtulcdsdk-course-operation__hover'"
          @click="toOperate(item)"
        >
          <yxtf-svg
            width="18px"
            height="18px"
            :remote-url="remoteUrl"
            :icon-class="item.icon"
          />
          <span v-if="item.label" class="ml6">{{ item.label }}</span>
        </div>
      </yxtf-tooltip>

      <yxtf-popover
        v-if="moreOperateConfigs.length"
        popper-class="yxtulcdsdk-course-operation__more"
        trigger="hover"
        placement="bottom-start"
        :offset="12"
        :effect="true"
      >
        <div
          v-for="item in moreOperateConfigs"
          :key="item.type"
          class="yxtulcdsdk-flex-center yxtulcdsdk-course-operation__hover"
          @click="toOperate(item)"
        >
          <yxtf-svg
            class="mr4"
            width="18px"
            height="18px"
            :remote-url="remoteUrl"
            :icon-class="item.icon"
          />
          <span class="standard-size-14 nowrap">{{ item.title }}</span>
        </div>

        <div slot="reference" class="yxtulcdsdk-flex-center">
          <yxtf-svg
            class=" yxtulcdsdk-course-operation__hover color-white ml18"
            width="18px"
            height="18px"
            :remote-url="remoteUrl"
            icon-class="more"
          />
        </div>
      </yxtf-popover>
    </div>
    <div class="standard-size-12 mt4">
      <!-- 评分 -->
      <template v-if="!!courseScore.score">
        <yxt-popover placement="bottom-start" trigger="hover" :disabled="courseScore.list.length === 0">
          <div class="standard-size-12">
            <div v-for="(item,index) in courseScore.list" :key="index" :class="index>0?'mt20':''">
              <div class="color-gray-9">{{ item.name }}</div>
              <div class="yxtulcdsdk-flex-center mt5">
                <yxtf-rate :value="item.score" disabled :icon-size="10" />
                <span class="yxtf-color-warning ml8">{{ item.score.toFixed(1) }}</span>
              </div>
            </div>
          </div>
          <div class="yxtulcdsdk-inline-flex yxtulcdsdk-flex-center " :class="courseScore.list.length ? 'yxtulcdsdk-course-operation__hover':''" slot="reference">
            <yxtf-rate
              class="yxtulcdsdk-flex-center"
              :value="courseScore.score"
              disabled
              :icon-size="10"
            />
            <span class="ml2">{{ courseScore.score.toFixed(1) }}</span>
            <yxtf-svg
              v-if="courseScore.list.length > 0"
              class="ml4"
              width="12px"
              height="12px"
              :remote-url="remoteUrl"
              icon-class="down_arrow"
            />
          </div>
        </yxt-popover>
      </template>
      <span v-else class="opacity6">{{ $t('pc_kng_detail_tools_no_score'/** 暂无评分*/) }}</span>
      <span v-if="timeTextAll.length" class="opacity6 ml12">
        <span v-for="(text,index) in timeTextAll" :key="index" class="yxtulcdsdk-course-operation__split">{{ text }}</span>
      </span>
    </div>
    <!-- 举报组件 -->
    <yxtbiz-complain ref="complain" :target="complainTarget" />
    <yxtf-dialog :visible.sync="showShareCode" destroy-on-close width="520px">
      <yxtbiz-qrcode v-bind="shareData" />
    </yxtf-dialog>
  </div>
</template>

<script>
import qs from 'qs';
import { commonUtil } from 'yxt-biz-pc';
import { KNG_TYPE } from '../../enum';
import { cancelLike, getCourseAvgScore, doLike, getKngLikeStatus, getCollectStatus, cancelCollect, doCollect } from '../../service';
import { getMinuteFormat, getQueryString } from '../../utils';
import { COURSE_ATTRIBUTE } from 'yxt-ulcd-sdk/packages/course-page/enum';
import { formatDate } from 'yxt-ulcd-sdk/packages/develop-view-plan/formateDate';
export default {
  name: 'CourseOperation',
  props: {
    type: {
      type: String,
      default: 'all' // action | score
    }
  },
  inject: ['getDetail', 'getSilenceConfigs', 'getScanQueryData', 'getAttributeInfo', 'getIsUsedByKng'],
  data() {
    return {
      courseScore: {}, // 课程多维度评分
      isLiked: false, // 当前用户点赞
      isCollected: false, // 收藏
      bossDownloadConfig: {},
      complainTarget: {}, // 举报参数对象

      disabled: true,
      showShareCode: false,
      shareData: {
        name: '',
        url: ''
      }
    };
  },
  computed: {
    scanQueryData() {
      return this.getScanQueryData();
    },
    remoteUrl() {
      return `${this.$staticBaseUrl}ufd/55a3e0/kng/pc/svg`;
    },
    silenceConfigs() {
      return this.getSilenceConfigs && this.getSilenceConfigs();
    },
    detail() {// 课程包/单课件课程详情
      return this.getDetail() || {};
    },
    idOfGroup() {
      return (this.detail.shareFlag === 2 && this.detail.sourceKngId) || this.detail.id;
    },
    // 是否可以举报
    isShowReport() {
      return this.silenceConfigs.complainOnlineclassCourse === 1 && localStorage.userId !== this.detail.contributorsId;
    },
    // 点赞、收藏、举报
    courseOperateConfigs() {
      const list = [
        {type: 'praise', icon: this.isLiked ? 'praise_s' : 'praise', title: this.isLiked ? (this.type === 'action' ? this.detail.supportCount : this.$t('pc_kng_btn_cancelpraised'/** 取消点赞*/)) : this.$t('pc_kng_detail_btn_praised'/** 点赞 */), label: this.detail.supportCount || '', highlight: this.isLiked},
        {type: 'collect', icon: this.isCollected ? 'collect_s' : 'collect', title: this.isCollected ? (this.type === 'action' ? this.$t('pc_kng_detail_lbl_colleted'/** 已收藏 */) : this.$t('pc_kng_detail_cancelcollect'/** 取消收藏 */)) : this.$t('pc_kng_detail_btn_collete'/** 收藏*/), highlight: this.isCollected}

      ];
      if (this.detail.shareEnabled === 1) {
        list.push({type: 'share', icon: 'share', title: this.$t('pc_kng_detail_btn_share'/** 分享 */)});
      }
      return list;
    },
    moreOperateConfigs() {
      let list = [];
      if (this.isShowReport) {
        list.push({type: 'report', icon: 'report', title: this.$t('pc_kng_common_btn_report'/** 举报*/) });
      }
      return list;
    },
    isUsedByKng() {
      return this.getIsUsedByKng();
    },
    attributeInfo() {
      return this.getAttributeInfo();
    },
    otherAttrList() {
      return (this.attributeInfo.otherAttributeInfoResponse || []).filter(item => item.attrShow);
    },
    timeTextAll() {
      // 外链课，zip不显示
      const { studyHours, type, studyScore, expiredTime } = this.detail;
      const infoList = [];
      const otherInfo = this.attributeInfo.otherAttributeInfo4RealAttribute || {};

      if ((this.type === 'score' && !this.isUsedByKng) || this.type === 'all') {
        if (type !== KNG_TYPE.ZIP) {
          infoList.push(this.$t('pc_ulcdsdk_lbl_courseduration'/** 课程总时长{0} */, [getMinuteFormat(studyHours)]));
          studyScore && infoList.push(this.$t('pc_ulcdsdk_lbl_getscore'/** 可获得{0}学分 */, [studyScore]));
        }
        if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.STUDY_NUM)) {
          infoList.push(this.$t('pc_kng_common_lbl_learner_num'/** {0}人学习 */, [otherInfo.studyCount || 0]));
        }
        if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.VIEW_NUM)) {
          infoList.push(this.$t('pc_kng_common_lbl_read_num'/** {0}人浏览 */, [otherInfo.readCount || 0]));
        }
        if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.VIEW_COUNT)) {
          infoList.push(this.$t('pc_kng_common_lbl_view_num'/** {0}次浏览 */, [otherInfo.viewCount || 0]));
        }
        if (expiredTime) {
          infoList.push(this.$t('kng_common_lbl_validdateto'/** 有效期至{0} */, [formatDate(expiredTime, 'YYYY-MM-DD')]));
        }
      }
      if (this.type === 'score') {
        if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.CREATE_TIME)) {
          infoList.push(this.$t('pc_kng_common_lbl_create_time'/** {0} 创建 */, [formatDate(otherInfo.createTime, 'YYYY-MM-DD')]));
        }
        if (this.otherAttrList.some(item => item.attrKey === COURSE_ATTRIBUTE.UPDATE_TIME)) {
          infoList.push(this.$t('pc_kng_common_lbl_update_time'/** {0} 更新 */, [formatDate(otherInfo.updateTime, 'YYYY-MM-DD')]));
        }
      }
      console.log(infoList);
      return infoList;
    }
  },
  watch: {
    courseOperateConfigs() {
      // 为了tooltip显示过程中，文字长度变化（点赞 -> 取消点赞），位置重新计算
      this.disabled = true;
      this.$nextTick(() => {
        this.disabled = false;
      });
    }
  },
  created() {
    this.getCourseScore();
    this.getLikeStatus();
    this.getCollectStatus();
    this.$root.$on('UPDATE_COURSE_SCORE', this.getCourseScore);
  },
  beforeDestroy() {
    this.$root.$off('UPDATE_COURSE_SCORE', this.getCourseScore);
  },
  methods: {
    // 获取用户综合评分
    async getCourseScore() {
      if (!this.idOfGroup) return;
      const res = await getCourseAvgScore(this.idOfGroup);
      const {score = 0, itemResultBeans} = res;
      this.courseScore = {
        score,
        list: (itemResultBeans || []).map(item => ({name: item.dmnName, score: item.avgScore}))
      };

    }, // 获取课程课件点赞状态
    async getLikeStatus() {
      this.isLiked = false;
      const res = await getKngLikeStatus({targetIds: [this.idOfGroup]});
      if (res.datas && res.datas.length) {
        this.isLiked = res.datas[0].praiseType === 1;
      }
    },
    // 获取收藏状态
    async getCollectStatus() {
      const res = await getCollectStatus(this.idOfGroup);
      this.isCollected = !!res.myFavorite;
    },
    toOperate(item) {
      if (item.type === 'praise') this.toLike();
      else if (item.type === 'collect') this.toCollect();
      else if (item.type === 'report') this.toReport();
      else if (item.type === 'share') this.toShare();
    },
    // 点击点赞
    async toLike() {
      if (this.isLiked) {
        await cancelLike(this.idOfGroup);
        this.detail.supportCount--;
      } else {
        await doLike({targetId: this.idOfGroup, targetType: 2, targetOrgId: this.orgIdOfGroup});
        this.detail.supportCount++;
      }
      this.getLikeStatus();
    },
    // 点击收藏
    async toCollect() {
      if (this.isCollected) {
        await cancelCollect(this.idOfGroup);
      } else {
        await doCollect({targetId: this.idOfGroup, targetType: 2, targetOrgId: this.orgIdOfGroup});
      }
      this.getCollectStatus();
    },
    // 举报
    toReport() {
      const {id, contributorsName, title, contributorsId = ''} = this.detail;
      this.complainTarget = {
        masterId: id,
        masterType: 1,
        targetId: id,
        targetInfo: contributorsName || this.$t('pc_kng_lbl_anonymous_knowledge'/* 匿名用户的知识 */),
        targetName: title,
        targetType: 1, // 举报对象类型(1-知识 2-帖子 3-问题 4-时刻 5-知识/帖子/时刻的评论，问题的回答 6-笔记 7-其他)
        audioUrl: '',
        audioLength: 0,
        logoUrl: '',
        targetCreator: contributorsId
      };
      this.$refs.complain.visible = true;
    },
    // 分享
    async toShare() {
      const data = this.scanQueryData;
      data.locateshare = getQueryString('locateshare') || '';
      const str = qs.stringify(this.scanQueryData);
      const pcPath = `/kng/#/scan?${str}`;
      const h5Path = `/#/kng/scan?${str}`;
      const url = await commonUtil.getShortUrl(pcPath, h5Path, 0);
      this.shareData = {
        url,
        name: this.detail.title
      };
      this.showShareCode = true;
    }
  }
};
</script>
