<!-- 创建时间2023/04/26 15:18:07 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述： -->
<template>
  <!-- 培训中展示 -->
  <div v-if="light" class="standard-size-14 color-gray-9 yxtulcdsdk-flex-center yxtulcdsdk-flex-shrink-0">
    <template v-if="isMixtureStandard">
      <span>{{ $t('pc_kng_uplaoder_btn_study_process'/**学习进度 */) }}</span>
      <template v-if="timeCompleteStandard !== 0">
        <span class="ml12">{{ $t('pc_kng_courseware_standard_lbl_duration'/**时长 */) }}</span>
        <yxt-ulcd-sdk-svg
          class="ml4 color-gray-6"
          width="20px"
          height="20px"
          :icon-class="timeCompleteFlag === 1?'kngcompleted':'kngnotstarted'"
        />
      </template>
      <span class="ml12">{{ $t('pc_kng_courseware_standard_lbl_content'/** 内容 */) }}</span>
      <yxt-ulcd-sdk-svg
        class="ml4 color-gray-6"
        width="20px"
        height="20px"
        :icon-class="contentCompleteFlag === 1?'kngcompleted':'kngnotstarted'"
      />
    </template>
    <span v-else>{{ percentageText }}</span>
  </div>
  <!-- 在线课中展示 -->
  <div v-else-if="isMixtureStandard" class="yxtulcdsdk-flex-center yxtulcdsdk-flex-shrink-0">
    <yxtf-tooltip :content="standardContent">
      <yxtf-svg
        class="opacity5 yxtulcdsdk-course-summary__hover mb2"
        width="20px"
        height="20px"
        icon-class="prompt-0"
      />
    </yxtf-tooltip>
    <span class="opacity8 ml4">{{ $t('pc_kng_uplaoder_btn_study_process'/**学习进度 */) }}</span>
    <template v-if="timeCompleteStandard !== 0">
      <span class="ml12 opacity8">{{ $t('pc_kng_courseware_standard_lbl_duration'/**时长 */) }}</span>
      <yxt-ulcd-sdk-svg
        class="ml4 opacity5"
        width="20px"
        height="20px"
        :icon-class="timeCompleteFlag === 1?'kngcompleted':'kngnotstarted'"
      />
    </template>
    <span class="ml12 opacity8">{{ $t('pc_kng_courseware_standard_lbl_content'/** 内容 */) }}</span>
    <yxt-ulcd-sdk-svg
      class="ml4 opacity5"
      width="20px"
      height="20px"
      :icon-class="contentCompleteFlag === 1?'kngcompleted':'kngnotstarted'"
    />
  </div>
  <div v-else class="yxtulcdsdk-flex-center">
    <yxtf-progress
      v-if="percentage < 100"
      color="rgba(255,255,255,0.7)"
      line-width="80px"
      :percentage="percentage"
      :show-text="false"
    />
    <yxtf-svg
      v-else
      class="opacity8"
      width="20px"
      height="20px"
      icon-class="ok-facial"
    />

    <span class="opacity8 ml8">{{ percentageText }}</span>
  </div>
</template>
<script>
import Svg from 'packages/_components/svg.vue';
export default {
  name: 'CompleteView',
  components: {
    [Svg.name]: Svg
  },
  props: ['light', 'isMixtureStandard', 'standardContent', 'timeCompleteStandard', 'timeCompleteFlag', 'contentCompleteFlag', 'percentage', 'percentageText'],
  data() {
    return {};
  }
};
</script>
