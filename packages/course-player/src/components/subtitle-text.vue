<template>
  <div class="yxtulcdsdk-course-player__functional">
    <div class="mt24 ml24 mr40 standard-size-16">{{ $t('pc_kng_detail_audio_text'/** 文稿 */) }}</div>
    <div class="yxtulcdsdk-flex-1 h0 p16">
      <yxtf-scrollbar ref="scroll" :fit-height="true">
        <div
          v-for="(item, index) in textList"
          :ref="'subtitle'+item.index"
          :key="index"
          class="yxtulcdsdk-course-player__text mb8 p8"
          :class="{'text-highlight': highlightIndex === item.index}"
        >
          <div>{{ formateTime(item.startTime) }}</div>
          <div class="mt2">{{ item.text }}</div>
        </div>
      </yxtf-scrollbar>
    </div>
  </div>
</template>

<script>
export default {
  name: 'YxtUlcdSdkSubtitleText',
  props: {
    srtData: {
      type: Array,
      default: () => []
    },
    viewLoc: { // 音视频播放位置
      type: Number,
      default: 0
    },
    srtLan: {
      type: String,
      default: 'zh'
    }
  },
  data() {
    return {
      highlightIndex: -1
    };
  },
  computed: {
    textList() {
      const item = this.srtData.find(item => item.langTag === this.srtLan);
      return item ? item.textList : [];
    }
  },
  methods: {
    formateTime(time) {
      const hours = Math.floor(time / 3600);
      const minutes = Math.floor((time % 3600) / 60);
      const seconds = Math.floor(time % 60);

      const formattedHours = hours.toString().padStart(2, '0');
      const formattedMinutes = minutes.toString().padStart(2, '0');
      const formattedSeconds = seconds.toString().padStart(2, '0');

      if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
      } else if (minutes > 0) {
        return `${formattedMinutes}:${formattedSeconds}`;
      } else {
        return `00:${formattedSeconds}`;
      }
    }
  },
  watch: {
    viewLoc: {
      immediate: true,
      handler(value) {
        this.highlightIndex = this.textList.findIndex(item=> value >= item.startTime && value < item.endTime);
      }
    },
    highlightIndex(val) {
      const el = this.$refs['subtitle' + val];
      if (el && el.length && this.$refs.scroll && this.$refs.scroll.wrap) {
        this.$refs.scroll.wrap.scrollTop = el[0].offsetTop - 300;
      }
    }
  }
};
</script>
