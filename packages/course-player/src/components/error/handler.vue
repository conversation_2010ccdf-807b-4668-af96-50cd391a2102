<!-- 创建时间2023/02/23 13:20:40 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述： -->
<!-- 创建时间2023/02/20 18:37:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：错误提示 -->
<template>
  <yxtf-dialog
    padding-size="larger"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :show-close="false"
    center
    :cutline="false"
    :visible.sync="showModel"
    width="520px"
  >
    <div class="yxtulcdsdk-flex-center-center yxtulcdsdk-flex-vertical">
      <yxt-svg
        :remote-url="`${$staticBaseUrl}ufd/407a24/kng/pc/svg/pcsvg`"
        width="120px"
        height="120px"
        icon-class="tips"
      />
      <div class="mt24 standard-size-20 color-gray-9 yxt-weight-6">{{ $t('pc_kng_common_btn_tips'/** 提示*/) }}</div>
      <div class="mt12 color-gray-9">{{ text }}</div>
      <div class="mt24">
        <yxtf-button
          type="primary"
          size="large"
          @click="confirm"
        >{{ confirmButtonText }}<span v-if="time>0"> ({{ time }})</span></yxtf-button>
      </div>
    </div>
  </yxtf-dialog>
</template>

<script>
import { LOG_TYPE } from 'yxt-ulcd-sdk/packages/course-page/enum';
import { changeStudying, updateVersion } from '../../../service';
import workerTimer from '../../../timer';
export default {
  name: 'Handler',
  inject: ['getCourseId', 'getKngId', 'getCheatInfo', 'getCommonParam', 'doFaceRecognition'],
  data() {
    return {
      showModel: false,
      loading: false,
      error: '',
      time: -1,
      errorKey: '',
      text: ''
    };
  },
  computed: {
    cheatInfo() {
      return this.getCheatInfo();
    },
    kngId() {
      return this.getKngId();
    },
    courseId() {
      return this.getCourseId();
    },
    commonParam() {
      return this.getCommonParam();
    },
    dialog() {
      return ['apis.kng.knowledge.hours.changed',
        'apis.kng.knowledge.version.changed',
        'apis.kng.study.needChange',
        'apis.kng.study.same.needChange',
        'study.onHook',
        'complete.study.onHook'].includes(this.errorKey);
    },
    confirmButtonText() {
      if (this.errorKey === 'apis.kng.knowledge.hours.changed') {
        return this.$t('pc_kng_detail_lbl_l_know'/** 我知道了 */);
      } else if (this.errorKey === 'apis.kng.knowledge.version.changed') {
        return this.$t('pc_kng_btn_versionchange'/** 继续学习 */);
      } else if (this.errorKey === 'apis.kng.study.needChange') {
        return this.$t('pc_kng_detail_btn_switch_btn'/** 切换到新课程*/);
      } else if (this.errorKey === 'study.onHook' && this.time === 0) {
        return this.$t('pc_kng_btn_countdownend'/** 倒计时结束，重新学习 */);
      } else if (this.errorKey === 'study.onHook' || this.errorKey === 'complete.study.onHook') {
        return this.$t('pc_kng_detail_btn_continue_learn'/** 继续学习*/);
      }
      // 'apis.kng.study.same.needChange'
      return this.$t('pc_kng_common_btn_confirm_no_blank'/** 确定 */);
    }
  },
  methods: {
    // 错误类型，1.页面-信息错误 2.dialog-点击刷新页面 3.dialog-点击继续
    // 刷新页面dialog：版本更新，学时更新
    // 点击继续dialog：多课程同时学、同时学同一个课、防挂机、网络异常
    // resolve指令：'continue' 'unknown','restudy'
    handleCourseError(error, catchUnknown = true) {
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        // 课程页面所有错误都要反馈
        this.text = this.getErrorText(error, catchUnknown);
        this.$nextTick(() => {
          if (this.text && !this.dialog) {
            // 文字错误让调用组件处理
            reject(this.text);
          } else if (this.dialog) {
            this.showModel = true;
            this.$root.$emit('EXIT_FULLSCREEN');
          }
        });
      });
    },
    getErrorText(error, catchUnknown) {
      const key = error && error.key || '';
      this.errorKey = key;
      this.$root.$emit('LOG', {type: LOG_TYPE.INFO, ext1: '接口错误', ext2: JSON.stringify({errorKey: this.errorKey, cheatInfo: this.cheatInfo}) });
      let message = '';
      switch (this.errorKey) {
        case 'apis.kng.knowledge.validation.has.not.permission':
        case 'apis.kng.knowledge.NotExist':
        case 'apis.kng.knowledge.validation.is.being.transcoded':
        case 'apis.kng.course.order.validation.not.effective':
          message = error.message;
          break;
        case 'apis.kng.knowledge.validation.is.hidden':
        case 'apis.kng.knowledge.validation.has.remove':
        case 'apis.kng.knowledge.validation.has.not.approved':
          message = this.$t('pc_kng_detail_error_msg1'/** 該課程已下線 */);
          break;
        case 'apis.kng.knowledge.validation.has.expired':
        case 'apis.course.order.validation.has.expired':
          message = this.$t('pc_kng_detail_error_msg3'/** 课程已过期，无法再继续学习 */);
          break;
        case 'apis.kng.freqbuy.order.error':
        case 'apis.kng.freqbuy.order.return':
        case 'apis.kng.freqbuy.order.unoccupy':
        case 'apis.kng.freqbuy.order.noavailable':
          message = this.$t('pc_ulcdsdk_lbl_undercount'/** 课程可用次数不足，请联系管理员 */);
          break;
        case 'apis.kng.knowledge.be.reported':
          message = this.$t('pc_kng_mgmt_msg_del_course'/* 该课程已删除。 */);
          break;
        case 'apis.kng.ip.inValid':
          message = this.$t('pc_kng_courseware_lbl_you_are_not_within_the_ip_range_allowed'/* 您不在此课程允许访问的ip范围内，如需访问请联系管理人员 */);
          break;
        case 'apis.kng.ip.error':
          // 私有云播放失败
          message = this.$t('pc_ulcdsdk_courseplayerror'/* 您不在此课程允许访问的ip范围内，如需访问请联系管理人员 */);
          break;
        case 'apis.kng.knowledge.hours.changed':// dialog
          message = this.$t('pc_kng_detail_lbl_time_change'/** 本课程学时被更新，学习进度将被重置*/);
          break;
        case 'apis.kng.knowledge.version.changed':// dialog
          message = this.$t('pc_kng_detail_lbl_versions_change'/** 本课程更新了新版本，建议您重新学习 */);
          break;
        case 'apis.kng.study.needChange': // dialog
          message = this.$t('pc_kng_detail_lbl_cheat'/** 系统已开启防多课程同时学习功能，您之前已有正在学习中的课程，是否确定学习该新打开的课程？*/);
          break;
        case 'apis.kng.study.same.needChange': // dialog
          message = this.$t('pc_kng_detail_lbl_cheat2'/** 您已经打开过该课程，系统在同一时间同一课程只会记录一个播放页面的学习进度和学分，确认是否仍在此页面学习？ */);
          break;
        case 'study.onHook':// dialog
          message = this.cheatInfo.tips;
          this.initCheatInfo();
          break;
        case 'complete.study.onHook':// dialog
          this.time = 0; // 隐藏倒计时
          message = this.cheatInfo.selectType === 1 ? this.cheatInfo.tips : this.$t('pc_kng_detail_leave_tips'/** 已触发防挂机验证，请点击“继续学习”，否则学习将被停止 */);
          break;
        default:
          if (catchUnknown) {
            message = this.$t('pc_kng_detail_error_msg2'/**	对不起，该课程无法观看 */);
          } else {
            this.resolve('unknown');
          }
          break;
      }
      return message;
    },
    async confirm() {
      this.loading = true;
      try {
        if (this.errorKey === 'apis.kng.knowledge.hours.changed' || this.errorKey === 'apis.kng.knowledge.version.changed') {
          this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '切换版本', ext2: this.errorKey });
          // 版本更新，学时更新
          const params = {
            kngId: this.courseId || this.kngId,
            targetCode: this.commonParam.targetCode,
            targetId: this.commonParam.targetId
          };
          await updateVersion(params);
          window.location.reload();
        } else if (this.errorKey === 'apis.kng.study.needChange') {
        // 切换正在学，继续
          this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '切换新课程', ext2: this.errorKey });
          await changeStudying({kngId: this.kngId});
          this.resolve('continue');
        } else if (this.errorKey === 'apis.kng.study.same.needChange') {
          this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '同时学一门课刷新', ext2: this.errorKey });
          this.resolve('restudy');
        } else if (this.errorKey === 'study.onHook' || this.errorKey === 'complete.study.onHook') {
          this.showModel = false;
          this.clearTimer();
          this.doFaceRecognition && this.doFaceRecognition('studyMiddle', (state) => {
            if (state === 1) {
              // 防挂机，继续学习
              this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '防挂机继续', ext2: this.errorKey });
              this.resolve('continue');
            } else {
              // 防挂机人脸识别失败
              this.$root.$emit('LOG', {type: LOG_TYPE.BEHAVIOR, ext1: '防挂机人脸识别失败', ext2: this.errorKey });
              this.resolve('onHookFaceError');
            }
          });
        }
        if (this.errorKey !== 'study.onHook' && this.errorKey !== 'complete.study.onHook') {
          this.showModel = false;
        }
      } catch (e) {}
      this.loading = false;
    },
    initCheatInfo() {
      const {selectType, countDown} = this.cheatInfo;
      if (selectType === 1) return;
      this.time = countDown;
      !this.timer && (this.timer = workerTimer.setInterval(() => {
        this.time--;
        if (this.time === 0) {
          this.$root.$emit('ROLLBACK');
          this.clearTimer();
        }
      }, 1000));
    },
    clearTimer() {
      if (this.timer) {
        workerTimer.clearInterval(this.timer);
        this.timer = null;
      }
    }
  }
};
</script>
