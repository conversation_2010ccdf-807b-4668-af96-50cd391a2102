<!-- 创建时间2023/02/15 18:14:55 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：打开页面中间按钮显示, 兼容跳出去的，人脸的-->
<template>
  <div class="yxtulcdsdk-flex-center yxtulcdsdk-flex-vertical max-w-560 ulcdsdk-break-word">
    <template v-if="isShowDownload">
      <yxtf-svg
        :remote-url="`${$staticBaseUrl}ufd/407a24/kng/pc/svg/pcsvg`"
        width="120px"
        height="120px"
        icon-class="zip_down"
      />
      <div class="standard-size-20 mt12">{{ title }}</div>
      <yxtf-button
        class="mt16"
        type="primary"
        size="larger"
        @click="toDownload"
      >
        {{ $t('pc_kng_detail_btn_download_zip'/**下载压缩包 */) }}
      </yxtf-button>
    </template>
    <template v-else-if="isShowFace">
      <yxtbiz-qrcode
        v-if="qrCodeUrl"
        :url="qrCodeUrl"
        hide-link
        hide-download
        :padding="8"
        :size="200"
      />
      <div class="standard-size-20 mt24">{{ title }}</div>
      <div class="standard-size-16 mt12">{{ $t('pc_ulcdsdk_lbl_onlystudywithapp'/**该课程已开启人脸识别，仅支持在App中学习，请使用App扫码学习课程 */) }}</div>

    </template>
    <template v-else>
      <!-- 最近学习,您已完成最后一个学习任务，类型 -->
      <div class="standard-size-20">{{ title }}</div>
      <!-- 按钮：开始学习，继续学习，重新学习，重播，查看，下一个-->
      <div class="mt16 yxtulcdsdk-flex-center">
        <yxtf-button
          type="primary"
          size="larger"
          @click="play"
        >
          <span class="yxtulcdsdk-flex-center-center">
            <yxtf-svg
              v-if="playButtonInfo.icon"
              :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/pc/svg`"
              class="v-mid mr8"
              width="13px"
              height="16px"
              :icon-class="playButtonInfo.icon"
            />
            <span @click="play">{{ playButtonInfo.text }}</span>
          </span>
        </yxtf-button>
        <!-- 非最后一个任务展示 -->
        <yxtf-button
          v-if="showNext"
          type="primary"
          size="larger"
          @click="next"
        >
          {{ $t('pc_kng_temp_msg_next'/**下一个 */) }}
        </yxtf-button>
      </div>
    </template>
  </div>
</template>

<script>
import { commonUtil } from 'yxt-biz-pc';
import { KNG_TYPE } from '../../enum';
import { getKngTypeName } from '../../utils';
import qs from 'qs';
import { getZipUrl } from '../../service';
export default {
  name: 'ButtonView',
  inject: ['getDetail', 'getIsLastKng', 'getScanQueryData', 'getCourseId', 'getCommonParam', 'getAllKngList', 'getFactorConfig'],
  props: {
    kngDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {qrCodeUrl: ''};
  },
  computed: {
    isShowDownload() {
      return this.kngDetail.type === KNG_TYPE.ZIP;
    },
    isShowFace() {
      // Course_Facial_Recognition要素，控制人脸识别是否支持PC/H5
      if (!this.getFactorConfig().showCourseFacialRecognition) {
        const {faceRecognitionConfigEnabled, appPlay} = this.kngDetail;
        return faceRecognitionConfigEnabled === 1 && appPlay === 1;
      }
      return false;
    },
    detail() {
      return this.getDetail();
    },
    courseId() {
      return this.getCourseId();
    },
    commonParam() {
      return this.getCommonParam();
    },
    isLastKng() {
      return this.getIsLastKng();
    },
    playButtonInfo() {
      const {schedule} = this.detail;
      let icon = '';
      let text = '';
      let type = '';
      if (schedule === 0) {
        text = this.$t('pc_kng_detail_start_learn'/** 开始学习*/);
        icon = this.showPlayIcon ? 'play-icon' : '';
        type = 'play';
      } else if (schedule === 100 && this.isLastKng) {
        text = this.$t('pc_kng_mgmt_btn_relearning'/** 重新学习*/);
        type = 'restart';
      } else {
        text = this.$t('pc_kng_detail_btn_continue_learn'/** 继续学习*/);
        icon = this.showPlayIcon ? 'play-icon' : '';
        type = 'play';
      }
      return {text, icon, type};
    },
    showPlayIcon() {
      const {type, weikeType, fileId} = this.kngDetail;
      return type === KNG_TYPE.VIDEO ||
      type === KNG_TYPE.AUDIO ||
      (type === KNG_TYPE.WEIKE && weikeType === 2 && !!fileId);
    },
    showNext() {
      const {schedule} = this.kngDetail;
      return this.detail.type === KNG_TYPE.COURSE && schedule === 100 && !this.isLastKng;
    },
    chapterTitle() {
      if (this.courseId) {
        const list = this.getAllKngList();
        const c = list.find(item => item.id === this.kngDetail.id) || {};
        return c.name || '';
      }
      return '';
    },
    title() {
      const { title, type} = this.kngDetail;
      const {schedule} = this.detail;
      if (schedule === 0 || type !== KNG_TYPE.COURSE) {
        return `【${getKngTypeName(type)}】${this.chapterTitle || title}`;
      } else if (schedule === 100 && this.isLastKng) {
        return this.$t('pc_kng_mgmt_msg_completed_last_task'/** 您已完成最后一个学习任务 */);
      } else {
        return this.$t('pc_kng_mgmt_msg_recent_study') + (this.chapterTitle || title);
      }
    }
  },
  created() {
    if (this.isShowFace) {
      this.setQrCodeUrl();
    }
  },
  methods: {
    async setQrCodeUrl() {
      const url = `/#/kng/scan?${qs.stringify(this.getScanQueryData())}`;
      const res = await commonUtil.getShortUrl('', url, 0);
      this.qrCodeUrl = res;
    },
    toDownload() {
      const {targetId, targetCode, studyParam} = this.commonParam;
      const params = {
        kngId: this.kngDetail.id,
        courseId: this.courseId,
        targetId,
        targetCode,
        originOrgId: studyParam.originOrgId,
        sspParameter: this.commonParam.targetParam
      };
      getZipUrl(params)
        .then(res => {
          res.downloadUrl && window.open(res.downloadUrl);
          this.kngDetail.schedule = 100;
        })
        .catch(e => {
          if (e.key === 'apis.kng.knowledge.withoutDownloadPermission') {
            this.$message.error(this.$t('pc_kng_detail_msg_no_permission_download'));
          } else {
            this.$parent._errorHandler(e);
          }
        });
    },
    play() {

      const {type} = this.playButtonInfo;
      if (type === 'restart') {
        // 最后一个，从第一个课件开始学
        this.next();
      } if (type === 'play') {
        this.$parent.play();
      }
    },
    next() {
      this.$root.$emit('CHAPTER_NEXT', true);
    }
  }
};
</script>
