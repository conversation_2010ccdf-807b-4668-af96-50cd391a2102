<template>
  <div v-if="showBtns" class="yxtulcdsdk-study-plan">
    <yxtf-button v-if="showView" class="plan-button" @click="viewPlan">{{ $t('pc_kng_studyplan_view'/** 查看自学计划 */) }}</yxtf-button>
    <yxtf-button
      v-else
      :class="showTeam ? 'plan-button' : ''"
      :type="showTeam ? '' : 'primary'"
      @click="addStudy"
    >{{ $t('pc_kng_detail_btn_add_study'/** 加入自学计划 */) }}</yxtf-button>
    <yxtf-button v-if="showTeam" type="primary" @click="addTeam">{{ $t('pc_kng_studyplan_recommendteam'/** 推荐给团队 */) }}</yxtf-button>

    <develop-view-plan ref="plan" :kng-id="kngId" @isAddStudy="isAddStudy" />

    <yxt-dialog
      :title="firstAdd ? $t('pc_kng_studyplan_recommendteam'/** 推荐给团队 */) : $t('pc_kng_studyplan_addstudents'/** 添加学员 */)"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-back="!firstAdd"
      destroy-on-close
      width="720px"
      @back="backHandler"
    >
      <add-students-to-team ref="addTeam" :kng-id="kngId" :show-tip="firstAdd" />
      <span slot="footer" class="dialog-footer">
        <yxtf-button @click="visible = false">{{ $t('pc_ulcdsdk_cancel' /** 取消 */) }}</yxtf-button>
        <yxtf-button :loading="loading" type="primary" @click="confirm">{{ $t('pc_ulcdsdk_done'/** 确定 */) }}</yxtf-button>
      </span>
    </yxt-dialog>

    <yxt-dialog
      :title="$t('pc_kng_studyplan_recommendteam'/** 推荐给团队 */)"
      :visible.sync="visible2"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="640px"
    >
      <div class="yxtulcdsdk-study-plan__list">
        <div>
          <yxt-button type="primary" @click="addStudents">{{ $t('pc_kng_studyplan_addstudents'/** 添加学员 */) }}</yxt-button>
          <yxt-button @click="teamManage">{{ $t('pc_kng_studyplan_teammanage'/** 跟踪管理 */) }}</yxt-button>
          <div class="pull-right">
            <yxtf-input
              v-model="searchTxt"
              searchable
              clearable
              size="small"
              :placeholder="$t('pc_kng_studyplan_searchtip'/** 请输入姓名搜索 */)"
              @search="search"
              @clear="search"
            />
          </div>
        </div>
        <yxtf-table class="mt12" :data="list">
          <yxtf-table-column prop="fullName" :label="$t('pc_kng_studyplan_name'/** 姓名 */)">
            <template slot-scope="{row}">
              <div class="yxtulcdsdk-flex-center">
                <yxtf-portrait
                  class="flex-shrink-0"
                  size="small"
                  :img-url="row.userImage"
                  :username="row.fullName"
                />
                <yxtf-tooltip open-filter placement="top" :content="row.fullName">
                  <yxtbiz-user-name class="ml12 ellipsis" :name="row.fullName" />
                </yxtf-tooltip>
              </div>
            </template>
          </yxtf-table-column>
          <yxtf-table-column prop="userName" :label="$t('pc_kng_common_lbl_username'/** 账号 */)">
            <template slot-scope="{row}">
              <yxtf-tooltip open-filter placement="top" :content="row.userName">
                <div class="ellipsis">{{ row.userName }}</div>
              </yxtf-tooltip>
            </template>
          </yxtf-table-column>
          <yxtf-table-column prop="planFinishTime" :label="$t('pc_kng_studyplan_deadtime'/** 截止时间 */)">
            <template slot-scope="{row}">
              {{ formatDate(row.planFinishTime,'YYYY-MM-DD HH:mm') }}
            </template>
          </yxtf-table-column>
        </yxtf-table>
        <div class="text-right mt20">
          <yxtf-pagination
            layout="prev, pager, next"
            :current-page="page"
            :page-size="6"
            :page-count="total"
            @current-change="handleSizeChange"
          />
        </div>
      </div>
    </yxt-dialog>
  </div>
</template>

<script>
import { addSelfStudy, getPlanUserList, getNavs } from '../../service';

import developViewPlan from 'yxt-ulcd-sdk/packages/develop-view-plan';
import addStudentsToTeam from 'yxt-ulcd-sdk/packages/add-students-to-team';

import { formatDate} from 'yxt-ulcd-sdk/packages/develop-view-plan/formateDate';

export default {
  name: 'YxtUlcdSdkStudyPlanBtns',
  inject: ['getDetail', 'getScanQueryData'],
  components: {
    developViewPlan,
    addStudentsToTeam
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    kngId() {
      return this.detail.id;
    }
  },
  data() {
    return {
      showBtns: false,
      showView: false, // 查看计划
      showTeam: false, // 推荐给团队
      visible: false,
      visible2: false,
      page: 1,
      list: [],
      total: 0,
      searchTxt: '',
      firstAdd: true,
      loading: false
    };
  },
  async mounted() {
    try {
      // 是否有团队推荐权限
      const {datas} = await getNavs();
      const teamNav = datas.find(item => item.code === 'o2o_myteam');
      this.showTeam = !!teamNav && this.detail.leaderPermission === 0; // 是否依赖上级浏览权限标识 0：否 1：是
    } catch (error) {
      console.log(error);
    }
    this.showView = !!this.detail.addStudy;
    const { targetCode } = this.getScanQueryData();
    this.showBtns = !['o2o', 'gwnl', 'flip'].includes(targetCode);
  },
  methods: {
    formatDate,
    // 加入自学
    async addStudy() {
      await addSelfStudy(this.detail.id);
      this.showView = true;
      const confirmMsg = this.$t('pc_kng_studyplan_confirmmsg'/** 针对此课程制定一份自学计划，帮助你更高效的完成学习 */);
      const options = {
        cancelButtonText: this.$t('pc_kng_studyplan_later'/** 稍后制定 */),
        confirmButtonText: this.$t('pc_kng_viewplan_btn_draw'/** 立即制定 */),
        type: 'success'
      };
      this.$confirm(confirmMsg, this.$t('pc_kng_studyplan_confirmtitle'/** 已加入自学，制定自学计划 */), options).then(() => {
        this.$refs.plan.init('develop', this.kngId);
      });
    },
    // 查看自学计划
    viewPlan() {
      this.$refs.plan.init('view', this.kngId);
    },
    isAddStudy(val) {
      this.showView = val;
    },
    // 推荐给团队
    async addTeam() {
      this.page = 1;
      await this.getList();
      if (this.list.length > 0) {
        this.visible2 = true;
      } else {
        this.firstAdd = true;
        this.visible = true;
      }
    },
    search() {
      this.page = 1;
      this.getList();
    },
    async getList() {
      try {
        const { datas = [], paging } = await getPlanUserList(
          {
            kngId: this.kngId,
            userName: this.searchTxt
          }, {
            limit: 6,
            offset: (this.page - 1) * 6
          });
        this.list = datas;
        this.total = paging.pages;
      } catch (error) { }
    },
    handleSizeChange(val) {
      this.page = val;
      this.getList();
    },
    backHandler() {
      this.visible = false;
      this.visible2 = true;
    },
    // 添加学员
    addStudents() {
      this.firstAdd = false;
      this.visible2 = false;
      this.visible = true;
    },
    // 跟踪管理
    teamManage() {
      // 跳转团队管理
      window.open(`${location.origin}/o2o/#/myteam/teamlearning/course`);
    },
    confirm() {
      this.loading = true;
      this.$refs.addTeam && this.$refs.addTeam.addStudents().then(()=>{
        this.visible = false;
        this.$message.success(this.$t('pc_kng_tip_recommend_success'/** 推荐成功! */));
      }).catch(()=>{}).finally(() => (this.loading = false));
    }
  }
};
</script>

