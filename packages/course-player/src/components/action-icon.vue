<!-- 创建时间2023/02/14 16:22:18 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：上下结构的图标 -->
<template>
  <div class="yxtulcdsdk-flex-center over-hidden yxtulcdsdk-action-icon yxtulcdsdk-flex-vertical" :class="disabled? 'yxtulcdsdk-action-icon--disabled':''" @click="toClick">
    <yxtf-svg
      width="20px"
      height="20px"
      :icon-class="iconClass"
      :remote-url="remoteUrl || `${$staticBaseUrl}ufd/55a3e0/kng/pc/svg`"
    />
    <div class="ulcdsdk-break-word mt2 standard-size-12 text-center max-width-100">
      <autosize-text :text="title" />
    </div>
  </div>
</template>

<script>
import autosizeText from './autosize-text.vue';
export default {
  components: { autosizeText },
  name: 'ActionIcon',
  props: {
    iconClass: {
      type: String,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    remoteUrl: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  methods: {
    toClick() {
      if (this.disabled) return;
      this.$emit('click');
    }
  }
};
</script>
