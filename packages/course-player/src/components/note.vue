<!-- 创建时间2023/02/13 09:22:25 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：笔记功能区 -->
<template>
  <div class="yxtulcdsdk-course-player__functional">
    <yxtf-tabs
      v-model="activeName"
      :bottom-line="false"
    >
      <yxtf-tab-pane :label="$t('pc_kng_note_lbl_writeNote'/** 写笔记*/)" name="first" />
      <yxtf-tab-pane :label="$t('pc_kng_note_lbl_myNote'/**我的笔记 */)" name="second" />
    </yxtf-tabs>

    <note-editor
      v-show="activeName === 'first'"
      ref="editor"
      class="yxtulcdsdk-mt-12 flex-1 ph12 h0"
      hide-close
      @confirm="addNote"
    />
    <template v-if="activeName === 'second'">
      <div class="ph12 mb12 yxtulcdsdk-mt-12">
        <yxt-checkbox
          v-if="isInCourse"
          v-model="onlyCurrent"
          :true-label="1"
          :false-label="0"
        ><span class="color-white">{{ $t('pc_kng_note_lbl_current_courseware_note'/**仅查看当前课件笔记 */) }}</span></yxt-checkbox>

      </div>
      <yxt-scrollbar class="yxtulcdsdk-flex-1" :fit-height="true">
        <div v-infinite-scroll="load">
          <note-item
            v-for="item in noteList"
            :key="item.id"
            :note="item"
            @confirm="editNote"
            @delete="deleteNote"
          />
          <div v-if="loading" class="font-size-14 color-gray-7 text-center pv24">{{ $t('pc_ulcdsdk_lbl_loading'/**加载中... */) }}</div>
          <div v-if="finished && noteList.length" class="font-size-14 color-gray-7 text-center pv24">{{ $t('pc_ulcdsdk_lbl_finished'/**已经到底了 */) }}</div>
        </div>
        <yxtf-empty v-if="loaded && noteList.length === 0" size="small" class="mt100">
          <span class="color-white" slot="text">{{ $t('pc_kng_note_tip_not_has_note'/**暂无相关笔记 */) }}</span>
        </yxtf-empty>
      </yxt-scrollbar>
    </template>

  </div>

</template>

<script>
import { KNG_TYPE } from '../../enum';
import { getMyNoteList } from '../../service';
import NoteEditor from './note/note-editor.vue';
import NoteItem from './note/note-item.vue';
export default {
  name: 'Note',
  components: {NoteItem, NoteEditor},
  inject: ['getDetail'],
  props: {
    kngDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeName: 'first',
      onlyCurrent: 0,
      page: 0,
      noteList: [],
      loading: false,
      finished: false,
      loaded: false
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    isInCourse() {
      return this.detail.type === KNG_TYPE.COURSE;
    }
  },
  watch: {
    onlyCurrent(val) {
      this.search();
    }
  },
  created() {
    this.$root.$on('UPDATE_MY_NOTE', this.search); // 更新下方列表
  },
  beforeDestroy() {
    this.$root.$off('UPDATE_MY_NOTE', this.search); // 更新下方列表
  },
  methods: {
    search() {
      this.page = 1;
      this.finished = false;
      this.getNoteList();
    },
    load() {
      if (this.finished || this.loading) return;
      this.page++;
      this.getNoteList();
    },
    async getNoteList() {
      try {
        this.loading = true;
        const data = {
          queryType: this.isInCourse ? 0 : 1,
          courseId: this.isInCourse ? this.detail.id : '',
          kngId: this.isInCourse ? this.kngDetail.id : this.detail.id,
          onlyCurr: this.onlyCurrent
        };
        const limit = 10;
        const offset = 10 * (this.page - 1);
        const {datas, paging} = await getMyNoteList({limit, offset}, data);
        this.finished = this.page >= paging.pages;
        if (this.page > 1) {
          this.noteList = this.noteList.concat(datas);
        } else {
          this.noteList = datas;
        }
        this.loaded = true;
      } catch (e) {}
      this.loading = false;
    },
    addNote() {
      this.page = 0;
      this.noteList = [];
      this.loading = false;
      this.finished = false;
      this.loaded = false;
      this.activeName = 'second';
      this.$root.$emit('UPDATE_NOTE'); // 更新下方列表
      this.$root.$emit('UPDATE_NOTE_COUNT'); // 更新笔记数量
    },
    editNote(obj) {
      const item = this.noteList.find(item => item.id === obj.id);
      item.content = obj.content;
      this.$root.$emit('UPDATE_NOTE'); // 更新下方列表
    },
    deleteNote(obj) {
      const index = this.noteList.findIndex(item => item.id === obj.id);
      index > -1 && this.noteList.splice(index, 1);
      this.$root.$emit('UPDATE_NOTE'); // 更新下方我的笔记列表
      this.$root.$emit('UPDATE_NOTE_COUNT'); // 更新笔记数量
    }
  }
};
</script>
