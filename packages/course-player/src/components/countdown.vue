<!-- 创建时间2023/02/07 15:39:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：倒计时 -->
<template>
  <div v-if="kngDetail.schedule < 100 && hidden" class="yxtulcdsdk-course-player__countdown-simple">
    <yxt-svg
      class="hover-opacity7"
      :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/pc/svg/`"
      width="32px"
      height="32px"
      icon-class="icon_clock"
      @click.native.stop="hidden = false"
    />
  </div>
  <div
    v-else-if="kngDetail.schedule < 100"
    class="yxtulcdsdk-course-player__countdown standard-size-12 yxtulcdsdk-flex-center"
    @mousedown="mousedown"
  >
    <yxtbiz-language-slot v-if="langKey" :lang-key="langKey">
      <span slot="time" class="yxt-color-warning">{{ timeString }}</span>
      <span slot="number">{{ studyScore }}</span>
    </yxtbiz-language-slot>
    <yxt-svg
      class="hover-opacity7"
      :remote-url="`${$staticBaseUrl}ufd/55a3e0/pc/svg/`"
      width="16px"
      height="16px"
      icon-class="icon_delete"
      @click.native.stop="hidden = true"
    />
  </div>
</template>

<script>
import { commonUtil } from 'yxt-biz-pc';
import { KNG_TYPE } from '../../enum';
export default {
  name: 'Countdown',
  inject: ['getCommonParam', 'getDetail'],
  props: {
    second: {
      type: Number,
      default: 0
    },
    kngDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      hidden: false
    };
  },
  computed: {
    commonParam() {
      return this.getCommonParam();
    },
    /*
    * 所有文案模版：1. 培训进入不展示学分
    * 0学时&&未完课：不展示
    * 时间未达到&&内容已完成&&有学分：还需 {time} 可完成本课程学习并获得{number}学分，加油！
    * 时间未达到&&有学分：共需 {time} 可完成本课程学习并获得{number}学分，加油！
    * 时间未达到&&内容已完成&&无学分：还需 {time} 可完成本课程学习，加油！
    * 时间未达到&&无学分：共需 {time} 可完成本课程学习，加油！
    * 时间已达到&&内容未完成&&有学分：还需完成剩余课程内容，才能获得${score}学分，加油！
    * 时间已达到&&内容未完成&&无学分：还需完成剩余课程内容，加油！
    * 完课&&无学分：恭喜您已完成本课程的学习。
    * 完课&&有学分：恭喜您已完成本课程的学习，获得了{number}学分。
    */
    langKey() {
      // contentCompleteStandard 0：默认 1：按内容完成 2：按进度完成 3：点击立即完成
      const { contentCompleteFlag, contentCompleteStandard, timeCompleteFlag, timeCompleteStandard, schedule } = this.kngDetail;
      let isDistributeScore = this.studyScore > 0; // 是否需要发放学分
      const {scoreRewardType, type} = this.getDetail();
      if (scoreRewardType === 1 && type === KNG_TYPE.COURSE) { // 课程包 学完课程时发放所有课件的学分
        isDistributeScore = false;
      }

      if (schedule === 100) { // 完课
        // 恭喜您已完成本课程的学习，获得了{number}学分。
        // 恭喜您已完成本课程的学习。
        return isDistributeScore ? 'pc_kng_lbl_completecoursegetscore' : 'pc_kng_lbl_completecourse';
      } else if (contentCompleteStandard > 0 && contentCompleteFlag === 1 && timeCompleteStandard !== 0) { // 内容完成、学时未完成
        // 还需 {time} 可完成本课程学习并获得{number}学分，加油！
        // 还需 {time} 可完成本课程学习，加油！
        return isDistributeScore ? 'pc_kng_lbl_needtimecompletecoursegetscore' : 'pc_kng_lbl_needtimecompletecourse';
      } else if (contentCompleteStandard > 0 && timeCompleteFlag === 1 && timeCompleteStandard !== 0) { // 内容未完成 学时完成
        // 还需完成剩余课程内容，才能获得{number}学分，加油！
        // 还需完成剩余课程内容，加油！
        return isDistributeScore ? 'pc_kng_lbl_needremaincompletecoursegetscore' : 'pc_kng_lbl_needremaincompletecourse';
      } else if (contentCompleteStandard > 0 && contentCompleteFlag === 0 && timeCompleteStandard === 0) {// 内容未完成
        // 需完成课程内容，才能获得{number}学分，加油！
        // 需完成课程内容可完成本课程学习，加油！
        return isDistributeScore ? 'pc_kng_lbl_needcontentcompletecoursegetscore' : 'pc_kng_lbl_needcontentcompletecourse';
      } else {// 学时未完成
        // 共需 {time} 可完成本课程学习并获得{number}学分，加油！
        // 共需 {time} 可完成本课程学习，加油！
        return isDistributeScore ? 'pc_kng_lbl_needtimecompletecoursegetscore' : 'pc_kng_lbl_needtimecompletecourse';
      }
    },
    studyScore() {
      return this.commonParam.targetCode === 'o2o' ? 0 : this.kngDetail.studyScore || 0;
    },
    timeString() {
      const s = Math.max(this.second, 0);
      const day = Math.floor(s / (24 * 60 * 60));
      const hour = Math.floor((s - day * 24 * 60 * 60) / (60 * 60));
      const minute = Math.floor((s - day * 24 * 60 * 60 - hour * 60 * 60) / 60);
      const second = s - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60;
      const split = commonUtil.getLanguage() === 'en' ? ' ' : '';
      let str = '';
      if (day) str += ' ' + day + split + this.$t('pc_kng_detail_lbl_day'/** 天*/);
      if (hour) str += ' ' + hour + split + this.$t('pc_kng_detail_lbl_hour'/** 小时 */);
      if (minute) str += ' ' + minute + split + this.$t('pc_kng_detail_lbl_minute'/** 分钟*/);
      if (second) str += ' ' + second + split + this.$t('pc_kng_detail_lbl_second'/** 秒*/);
      return str.trim() || `0${split}${this.$t('pc_kng_detail_lbl_second'/** 秒*/)}`;
    }
  },
  mounted() {
    // this.mouseover();
    window.addEventListener('mousemove', this.mousemove);
    window.addEventListener('mouseup', this.mouseup);
  },
  beforeDestroy() {
    window.removeEventListener('mousemove', this.mousemove);
    window.addEventListener('mouseup', this.mouseup);
  },
  methods: {
    mousedown(e) {
      this.down = true;
      const {top, left, right, bottom} = this.$el.getBoundingClientRect();
      this.start = {x: e.pageX, y: e.pageY, top, left, right, bottom};
      console.log(e);
    },
    mouseup() {
      this.down = false;
    },
    mousemove(e) {
      if (this.down) {
        let x = this.start.x - e.pageX;
        let y = e.pageY - this.start.y;
        const container = document.querySelector('.yxtulcdsdk-fullsize');
        let {top, left, right, bottom} = container.getBoundingClientRect();
        top = top + 4;
        left = left + 4;
        right = right - 4;
        bottom = bottom - 4;
        const verticalRange = [top - this.start.top, bottom - this.start.bottom];
        const horizontalRange = [this.start.right - right, this.start.left - left];
        console.log(verticalRange, horizontalRange);
        if (y < verticalRange[0]) y = verticalRange[0];
        if (y > verticalRange[1]) y = verticalRange[1];
        if (x < horizontalRange[0]) x = horizontalRange[0];
        if (x > horizontalRange[1]) x = horizontalRange[1];
        this.$el.style.right = (right - this.start.right) + x + 4 + 'px';
        this.$el.style.top = (this.start.top - top) + y + 4 + 'px';
      }
    }
  }
};
</script>
