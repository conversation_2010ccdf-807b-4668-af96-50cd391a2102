<!-- 创建时间2024/03/27 09:43:33 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：自定义字体大小 -->
<template>
  <span class="nowrap">{{ text }}</span>
</template>

<script>
export default {
  name: 'AutosizeText',
  props: {
    text: {
      type: String,
      default: ''
    },
    wrapWidth: {
      type: Number,
      default: 0
    },
    zoom: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  mounted() {
    this.calc();
  },
  watch: {
    text(val) {
      if (val) this.calc();
    }
  },
  methods: {
    calc() {
      this.$nextTick(() => {
        const textWidth = this.$el.offsetWidth;
        const wrapWidth = this.wrapWidth || this.$el.parentNode.clientWidth;
        if (this.zoom) {
          this.$el.style.zoom = this.zoom;
        } else if (wrapWidth < textWidth && wrapWidth > 0) {
          this.$el.style.zoom = Math.floor(wrapWidth / textWidth * 100) / 100;
        } else {
          this.$el.style.zoom = 1;
        }
      });
    }
  }
};
</script>
