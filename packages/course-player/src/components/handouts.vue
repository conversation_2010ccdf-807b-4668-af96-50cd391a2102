<!-- 创建时间2023/02/13 09:23:04 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：讲义功能区 -->
<template>
  <div class="yxtulcdsdk-course-player__functional">
    <div class="mt24 ml24 mr40 standard-size-16">{{ $t('pc_kng_detail_side_title1'/** 讲义*/) }}</div>
    <div class="yxtulcdsdk-flex-1 h0 p24">
      <yxtbiz-doc-player
        v-if="fileId"
        mode="picture"
        :file-id="fileId"
        :start="1"
        virtual="auto"
        :toolbars="['zoom', 'fullscreen']"
        :moon="true"
      />
      <yxtbiz-doc-viewer
        v-else
        class="h100p"
        mode="night"
        :show-mode="false"
        :file-info="{list:list}"
      />
    </div>
    <div class="yxtulcdsdk-course-player__handouts-drag" @mousedown="mousedown">
      <div></div>
      <div></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Handouts',
  props: {
    list: {
      type: Array,
      default: []
    },
    fileId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  mounted() {
    window.addEventListener('mousemove', this.mousemove);
    window.addEventListener('mouseup', this.mouseup);
  },
  beforeDestroy() {
    window.removeEventListener('mousemove', this.mousemove);
    window.removeEventListener('mouseup', this.mouseup);
  },
  methods: {
    mousedown(e) {
      this.isStart = true;
      this.startX = e.clientX;
      const dom = document.querySelector('.yxtulcdsdk-course-player__inner-right');
      if (dom) {
        this.startWidth = dom.offsetWidth;
      } else {
        this.startWidth = 360;
      }

    },
    mouseup() {
      this.isStart = false;
    },
    mousemove(e) {
      if (!this.isStart) return;
      const offset = this.startX - e.clientX;
      let width = this.startWidth + offset;
      this.setWidth(width);

    },
    setWidth(width) {
      const dom = document.querySelector('.yxtulcdsdk-course-player__inner-right');
      if (dom) {
        const playerMinWidth = 300;
        const maxWidth = Math.max(window.innerWidth - playerMinWidth - 300, 360);
        const minWidth = 360;
        if (width < minWidth) width = minWidth;
        if (width > maxWidth) width = maxWidth;
        dom.style.width = width + 'px';
      }
    }
  }
};
</script>
