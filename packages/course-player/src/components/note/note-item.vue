<!-- 创建时间2023/02/13 14:13:17 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：笔记cell -->
<template>
  <div class="yxtulcdsdk-course-player__note-item">
    <!-- 播放器右侧的我的笔记展示 -->
    <template v-if="!editing">
      <div class="yxtulcdsdk-flex-center">
        <span class="yxtulcdsdk-flex-1">
          {{ handleDateFormat(note.updateTime) }}
          <yxtf-tag
            v-if="note.privat === 1"
            class="ml8"
            size="mini"
            type="info"
          >{{ $t('pc_kng_mgmt_lbl_status_private'/**私密 */) }}</yxtf-tag>
        </span>

        <yxtf-popover
          trigger="hover"
          placement="bottom-end"
        >
          <div class="yxtulcdsdk-flex-center yxtulcdsdk-course-player__note-item-line  hover-bg-primary-2" @click="edit">
            <yxtf-svg
              class="mr8"
              width="18px"
              height="18px"
              icon-class="edit-1"
            />
            {{ $t('pc_kng_kngcourse_btn_edit'/**编辑 */) }}
          </div>
          <div class="yxtulcdsdk-flex-center yxtulcdsdk-course-player__note-item-line hover-bg-primary-2" @click="del">
            <yxtf-svg
              class="mr8"
              width="18px"
              height="18px"
              icon-class="delete"
            />
            {{ $t('pc_kng_common_btn_delete'/** 删除*/) }}
          </div>
          <div class="yxtulcdsdk-flex" slot="reference">
            <yxtf-svg
              width="24px"
              height="24px"
              icon-class="more"
              class="color-white yxtulcdsdk-course-player__note-item-action"
            />
          </div>
        </yxtf-popover>
      </div>
      <note-item-view class="mt16" :note="note" />
    </template>
    <!-- 编辑 -->
    <template v-if="editing">
      <note-editor
        :note="note"
        class="yxtulcdsdk-course-player__note-item-edit"
        @cancel="editing = false"
        @confirm="confirm"
      />
    </template>

  </div>
</template>

<script>
import { delNote } from 'yxt-ulcd-sdk/packages/course-player/service';
import { handleDateFormat } from 'yxt-ulcd-sdk/packages/course-player/utils';
import NoteEditor from './note-editor.vue';
import NoteItemView from './note-item-view.vue';
export default {
  components: { NoteEditor, NoteItemView },
  name: 'NoteItem',
  props: {
    note: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      editing: false
    };
  },
  methods: {
    handleDateFormat,
    edit() {
      this.editing = true;
    },
    async del() {
      await this.$confirm('', this.$t('pc_kng_note_tip_sure_deleteNote'/** 确认删除笔记吗？*/), {
        type: 'warning',
        showCancelButton: true
      });
      await delNote(this.note.id);
      this.$emit('delete', this.note);
    },
    confirm(obj) {
      this.editing = false;
      this.$emit('edit', obj);
    }
  }
};
</script>
