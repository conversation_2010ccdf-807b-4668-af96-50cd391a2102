<!-- 创建时间2023/02/13 14:50:18 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：笔记编辑功能 -->
<template>
  <div class="yxtulcdsdk-course-player__note-editor ">
    <yxt-scrollbar class="yxtulcdsdk-course-player__note-editor-scroll yxtulcdsdk-flex-1 h0" :fit-height="true">
      <div
        id="note_editor"
        ref="editor"
        class="yxtulcdsdk-flex-1 yxtulcdsdk-course-player__note-input"
        contenteditable
        @click="saveRange"
        @keydown="keydown"
        @keyup="keyup"
        @paste="paste"
        @cut="cut"
      >
      </div>
      <div v-if="textLength === 0" class="yxtulcdsdk-course-player__note-placeholder">{{ $t('pc_kng_note_lbl_editor_placeholder'/**好记性不如烂笔头，记录些什么吧～ */) }}</div>
    </yxt-scrollbar>
    <div class="yxtulcdsdk-flex-center yxtulcdsdk-course-player__note-action">
      <yxtf-popover
        placement="top"
        trigger="hover"
        :content="$t('pc_kng_note_lbl_insert_progress_marker')"
      >
        <span
          slot="reference"
          class="hover-primary-6 mr24 yxtulcdsdk-flex-center"
          @click="inertMark"
        >
          <yxtf-svg
            :remote-url="`${$staticBaseUrl}ufd/407a24/kng/pc/svg`"
            width="24px"
            height="24px"
            icon-class="note-flag-outline"
          />
        </span>
      </yxtf-popover>
      <yxt-checkbox v-model="isOpen" :disabled="disabledOpen"><span :class="disabledOpen?'color-gray-7':'color-white'">{{ $t('pc_ulcdsdk_lbl_opennote'/** 公开笔记*/) }}</span></yxt-checkbox>
      <div class="yxtulcdsdk-flex-1"></div>
      <yxtf-button
        v-if="!hideClose"
        size="small"
        type="text"
        plain
        @click="$emit('cancel')"
      >{{ $t('pc_kng_common_btn_cancel_no_blank'/** 取消*/) }}</yxtf-button>
      <yxtf-button
        size="small"
        type="primary"
        :loading="loading"
        @click="submit"
      >{{ $t('pc_ulcdsdk_btn_submit'/**提交 */) }}</yxtf-button>
    </div>
  </div>
</template>

<script>
import { addNote, editNote } from 'packages/course-player/service';
import note from '../../../mixins/note';
import { getNoteCache, setNoteCacheWithTime } from 'yxt-ulcd-sdk/packages/course-player/utils';
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
export default {
  name: 'NoteEditor',
  inject: ['getNotePositionInfo', 'getDetail', 'getSilenceConfigs'],
  mixins: [note],
  props: {
    hideClose: {
      type: Boolean,
      default: false
    },
    note: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      marksTemp: {},
      textLength: 0,
      isOpen: false,
      loading: false
    };
  },
  computed: {
    detail() {
      return this.getDetail();
    },
    silenceConfigs() {
      return this.getSilenceConfigs();
    },
    disabledOpen() {
      return !(this.detail.enableNote && this.silenceConfigs['MK0001-GN0007-1']) && this.isOpen === false;
    },
    isInCourse() {
      return this.detail.type === KNG_TYPE.COURSE;
    }
  },
  mounted() {
    this.handleData();
    window.addEventListener('unload', this.unload);
  },
  beforeDestroy() {
    window.removeEventListener('unload', this.unload);
    this.unload();
  },
  methods: {
    unload() {
      if (this.note && this.note.id || this.isCheck) return; // 编辑不用缓存
      const datas = this.getNoteData().datas;
      if (datas.some(item => (item.type === 0 && item.content))) { // 输入过文字，缓存数据
        setNoteCacheWithTime(this.detail.id, datas);
      }
    },
    check() {
      this.isCheck = true; // 手动关闭的不需要缓存
      const noteData = this.getNoteData();
      const datas = noteData.datas;
      const notEmpty = datas.some(item => (item.type === 0 && item.content) || item.type > 0);

      let isDefault = false;
      if (notEmpty && datas.length === 2 && noteData.plaintext === '') {
        isDefault = true;
      }

      // const datas = this.getNoteData().datas;
      // const notEmpty = datas.some(item => (item.type === 0 && item.content) || item.type > 0);
      if (!notEmpty || isDefault) return Promise.resolve();
      return new Promise(resolve => {
        this.$confirm('', this.$t('pc_kng_note_tip_saveNote'/** 您还未保存此笔记，是否保存？ */), {
          type: 'warning',
          showCancelButton: true
        }).then(() => {
          this.submit().then(resolve);
        }).catch(() => {
          resolve();
        });
      });
    },
    async handleData() {
      // 是否可以公开
      if (this.note.id) {
        this.isOpen = this.note.privat !== 1;
      } else {
        this.isOpen = !this.disabledOpen; // MK0001-GN0007-1 合规笔记功能
      }
      this.$refs.editor.innerHTML = '';
      let cache = getNoteCache(this.detail.id);
      if (cache) {
        await this.$confirm('', this.$t('pc_kng_note_tip_continue_edit'), {
        // confirmButtonText: '继续',
          confirmButtonText: this.$t('pc_kng_note_lbl_continue'/* 继续 */),
          type: 'warning'
        }).catch(() => {
          cache = null;
        });
      }
      if (cache) {
        this.note.content = JSON.stringify(cache);
      } else if (!this.note.id) {
        const defaultContent = [{type: 0, content: ''}];
        const defaultTag = this.getNotePositionInfo();
        if (defaultTag) {
          defaultContent.unshift(defaultTag);
        }
        this.note.content = JSON.stringify(defaultContent);
      }
      this.marksTemp = {};
      const dataList = JSON.parse(this.note.content || '[]');
      if (dataList.length === 0) return;
      const wrap = document.createDocumentFragment();
      dataList.forEach(item => {
        if (item.type === 0) { // 文本
          const div = document.createElement('div');
          if (item.content) div.innerText = item.content;
          else div.innerHTML = '<br>';
          wrap.appendChild(div);
        } else {
          wrap.appendChild(this.getMarkerNode(item));
        }
      });
      this.$refs.editor.appendChild(wrap);
      this.initContent();
      setTimeout(this.setEditorFocus, 300);
    },
    // 处理非文本dom
    getMarkerNode(marker) {
      const id = `${marker.kngId || ''};${new Date().getTime()}`;
      this.marksTemp[id] = marker;

      const markerNodeWrap = document.createElement('div');
      markerNodeWrap.setAttribute('contenteditable', false);
      markerNodeWrap.setAttribute('data-id', id);

      const markerNode = document.createElement('div');
      markerNode.setAttribute('contenteditable', false);
      markerNode.className = 'yxtulcdsdk-course-player__note-edit-mark';
      markerNodeWrap.appendChild(markerNode);

      const iconNode = document.createElement('img');
      const icon = this.getIcon(marker, true);
      iconNode.className = 'yxtulcdsdk-flex-shrink-0';
      iconNode.setAttribute('width', 16);
      iconNode.setAttribute('height', 16);
      iconNode.src = `${this.$staticBaseUrl}ufd/407a24/kng/pc/svg/${icon}.svg`;
      markerNode.appendChild(iconNode);

      const text = this.getPositionText(marker);
      if (text) {
        const positionNode = document.createElement('div');
        positionNode.className = 'ml7';
        positionNode.innerText = text;
        markerNode.appendChild(positionNode);
      }

      if (marker.kngName && this.isInCourse) {
        const nameNode = document.createElement('div');
        nameNode.className = 'ml8 ellipsis yxtulcdsdk-flex-1';
        nameNode.innerText = marker.kngName;
        markerNode.appendChild(nameNode);
      }

      const closeNode = document.createElement('i');
      closeNode.className = 'yxt-icon-close hover-primary-6 ml12 hand';
      closeNode.onclick = () => {
        markerNodeWrap.remove();
        this.initContent();
      };
      markerNode.appendChild(closeNode);
      return markerNodeWrap;
    },

    inertMark() {
      const info = this.getNotePositionInfo(true);
      if (!info) return;
      const markerNode = this.getMarkerNode(info);
      const range = this.range;
      const targetNode = (range.endContainer.nodeName || '').toLowerCase() === 'div' ? range.endContainer.id === 'note_editor' ? range.endContainer.lastChild : range.endContainer : range.endContainer.parentNode;
      if (range.endOffset === 0) { // 往前一行插入
        this.$refs.editor.insertBefore(markerNode, targetNode);
      } else { // 在目标节点后插入标记
        const isLast = targetNode === this.$refs.editor.lastChild;
        if (isLast) {
          this.$refs.editor.appendChild(markerNode);
          this.addNewLine(); // 在末尾插入标记，则新增一个空行
        } else {
          this.$refs.editor.insertBefore(markerNode, targetNode.nextSibling);

        }
      }
      this.initContent();
      this.setEditorFocus();// 恢复光标位置
    },
    saveRange() {
      const selection = window.getSelection();
      this.range = selection.getRangeAt(0);
    },
    setEditorFocus() { // 编辑区获得焦点
      if (this.range) {
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(this.range);
      } else {
        this.$refs.editor.focus();
        this.saveRange();
      }
      if (!this.moveLast) { // 光标移到尾部
        this.moveLast = true;
        this.moveCursorEnd();
      }
    },
    moveCursorEnd() { // 光标移到尾部
      const range = document.createRange();
      range.selectNodeContents(this.$refs.editor.lastChild.lastChild);
      range.collapse(false);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
      this.saveRange();
    },
    addNewLine() { // 添加一个新的空行
      const newLineNode = document.createElement('div');
      newLineNode.innerHTML = '<br>';
      this.$refs.editor.appendChild(newLineNode);
    },
    keydown(e) {
      if (e.keyCode !== 8 && e.keyCode !== 46) return; // 非 BackSpace、Delete，不作处理
      if (this.$refs.editor.innerHTML.toLowerCase().trim() === '<div><br></div>') {// 最后剩下一个空行，就不再删除了
        e.preventDefault();
      }
    },
    keyup() {
      this.saveRange();
      this.initContent();
    },
    paste(e) {
      e.preventDefault();
      let pastedText;
      if (window.clipboardData && window.clipboardData.getData) {
        // 获取拷贝进剪切板指定格式的数据 (此处用的Text格式)
        pastedText = window.clipboardData.getData('Text');
      } else {
        const clipboardData = e.clipboardData || e.originalEvent.clipboardData;
        if (clipboardData && clipboardData.getData) {
          pastedText = clipboardData.getData('text/plain');
        }
      }
      if (pastedText) {
        document.execCommand('insertText', false, pastedText);
        this.initContent();
      }
    },
    cut() {
      setTimeout(() => { // 鼠标右键剪切有延迟
        this.throttleLength();
        this.saveRange();
      });
    },
    // 计算内容长度 ; 内容只剩“br”标签时替换成“<div><br></div>”
    initContent() {
      // 需要重新计算的场景：
      // keyup
      // 插入、删除标记
      // 清空内容
      // 剪切、粘贴
      this.textLength = this.$refs.editor.innerText.replace(/\n/g, '').length;

      const textHtml = this.$refs.editor.innerHTML.toLowerCase().trim();
      if (!textHtml || textHtml === '<br>') {
        this.$refs.editor.innerHTML = '';
        this.addNewLine();
      }
    },
    getNoteData() {
      const datas = [];
      const contents = [];
      const kngIds = [];
      Array.from(this.$refs.editor.childNodes).forEach(item => {
        if ((item.nodeName || '').toLowerCase() === 'br') return; // 空的换行不要
        if (item.getAttribute) {
          const id = item.getAttribute('data-id');
          const marker = id && this.marksTemp[id];
          if (marker) {
            datas.push(marker);
            kngIds.push(marker.kngId);
            return;
          }
        }
        // nodeName: "#text" 文本节点
        const textContent = item.nodeName === '#text' ? item.textContent : item.innerText.replace('\n', '');
        datas.push({
          type: 0,
          content: textContent
        });
        contents.push(textContent);
      });
      return {
        datas,
        kngIds,
        plaintext: contents.join(' ').trim()
      };
    },
    async submit() {
      this.loading = true;
      const noteData = this.getNoteData();

      if (noteData.plaintext === '') {
        this.$message.error(this.$t('pc_kng_note_tip_need_input_content'));
        this.loading = false;
        return Promise.reject();
      } else if (noteData.plaintext.length > 10000) {
        this.$message.error(this.$t('pc_kng_note_tip_input_note_limit'));
        this.loading = false;
        return Promise.reject();
      }
      const {type, sourceKngId, id} = this.detail;
      const kngId = sourceKngId || id;
      const params = {
        id: this.note.id,
        plaintext: noteData.plaintext,
        privat: this.isOpen ? 0 : 1,
        content: JSON.stringify(noteData.datas),
        kngId,
        kngIds: noteData.kngIds,
        kngType: type
      };
      const api = params.id ? editNote : addNote;
      await api(params).then(() => {
        this.$message.success(this.$t('pc_kng_operate_msg_save_success')/* '保存成功' */);
        this.note.content = JSON.stringify(noteData.datas);
        this.note.privat = this.isOpen ? 0 : 1;
        this.$emit('confirm', this.note);
        this.handleData();
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>
