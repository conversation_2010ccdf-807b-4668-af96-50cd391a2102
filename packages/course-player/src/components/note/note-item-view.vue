<!-- 创建时间2023/02/13 15:21:27 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：笔记数据转化为dom显示 -->
<template>
  <div class="yxtulcdsdk-course-player__note-item-view" :class="{'yxtulcdsdk-course-player__note-item-view--light':effect==='light'}">
    <template v-for="(item,index) in dataList">
      <div v-if="item.type === 0" :key="`text-${index}`">
        {{ item.content }}
      </div>
      <div v-else :key="`mark-${index}`">
        <div
          class="yxtulcdsdk-course-player__note-mark hover-bg-primary-6-i"
          @click="toJump(item)"
        >
          <yxtf-svg
            :remote-url="`${$staticBaseUrl}ufd/407a24/kng/pc/svg`"
            width="16px"
            height="16px"
            :icon-class="getIcon(item)"
            class="color-gray-6 yxtulcdsdk-flex-shrink-0"
          />
          <yxt-popover
            v-if="getPositionText(item)"
            trigger="hover"
            :content="$t('pc_kng_note_tip_marker_jump')"
            placement="top"
          >
            <span slot="reference" class="ml7 yxtulcdsdk-flex-center">
              {{ getPositionText(item) }}
            </span>
          </yxt-popover>
          <div v-if="isInCourse" class="ellipsis ml8">{{ item.kngName }}</div>
        </div>
      </div>

    </template>
  </div>
</template>

<script>
import { KNG_TYPE } from 'yxt-ulcd-sdk/packages/course-player/enum';
import note from '../../../mixins/note';
export default {
  name: 'NoteItemView',
  inject: ['getDetail'],
  mixins: [note],
  props: {
    note: {
      type: Object,
      default: () => ({})
    },
    effect: {
      type: String,
      default: 'night'
    }
  },
  data() {
    return {
      KNG_TYPE
    };
  },
  computed: {
    dataList() {
      try {
        const content = JSON.parse(this.note.content || '[]');
        return content;
      } catch (e) {
        return [];
      }
    },
    isInCourse() {
      return this.getDetail().type === KNG_TYPE.COURSE;
    }
  },
  methods: {
    toJump(obj) {
      document.querySelector('.yxtulcdsdk-course-page__top').scrollIntoView(true);
      this.$root.$emit('NOTE_JUMP', obj);
    }
  }
};
</script>
