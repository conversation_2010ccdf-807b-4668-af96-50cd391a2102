<!-- 创建时间2023/03/03 16:44:34 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：练习弹框 -->
<template>
  <yxtf-dialog
    :visible.sync="show"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="640px"
    destroy-on-close
    padding-size="empty"
    custom-class="yxtulcdsdk-course-player__exercise-model"
    :show-close="false"
  >
    <div slot="title" class="yxtulcdsdk-flex-center">
      <span class="yxt-weight-5 color-gray-10">{{ $t('pc_ulcdsdk_tit_exerciseinclass'/**随堂练习 */) }}</span>
      <span v-if="!closeable" class="ml12 color-gray-7 ellipsis">{{ $t('pc_ulcdsdk_lbl_completeexercisecontinue'/**完成练习后才可以继续学习此课程 */) }}</span>
      <div class="yxtulcdsdk-flex-1"></div>
      <yxtf-svg
        v-if="closeable"
        class="color-gray-6 hand hover-primary-6 ml10"
        icon-class="delete-1"
        width="20px"
        height="20px"
        @click.native="close"
      />
    </div>
    <yxt-ulcd-sdk-practicing
      v-if="params.praId"
      ref="practicing"
      course-practice
      :step="step"
      :params="params"
      @finish="finish"
      @complete="complete"
      @error="close"
    />
  </yxtf-dialog>
</template>

<script>
import { generateTrackId } from '../../service';
export default {
  name: 'ExerciseModel',
  inject: ['getCourseId'],
  props: {
    kngDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: false,
      params: {},
      closeable: true,
      step: 0,
      exerciseInfo: {}
    };
  },
  methods: {
    async todo(obj, immediate) {
      this.exerciseInfo = obj;
      const params = {
        courseId: this.getCourseId(),
        lastTrackId: this.kngDetail.lastTrackId,
        currentKngId: this.kngDetail.id
      };
      const {data} = await generateTrackId(params);
      return new Promise(resolve => {
        this.resolve = resolve;
        // canSkipped 不可跳过
        this.closeable = obj.canSkipped === 0 || obj.isAnswer;
        this.params = {
          praId: obj.targetId,
          trackId: data
        };
        this.step = immediate ? 1 : 0;
        this.show = true;
        this.$root.$emit('EXIT_FULLSCREEN');
      });
    },
    // 跳过
    close() {
      this.show = false;
      this.resolve();
    },
    // 练习题都完成了
    finish() {
      this.exerciseInfo.isAnswer = 1;
    },
    // 完成后退出练习
    complete() {
      this.exerciseInfo.isAnswer = 1;
      this.close();
    }
  }
};
</script>
