<!-- 创建时间2023/02/16 15:48:18 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课程统计 ， 包含加入自学-->
<template>
  <div v-if="!detail.needToBuy" class="yxtulcdsdk-course-summary yxtulcdsdk-inline-flex yxtulcdsdk-flex-center yxtulcdsdk-flex-shrink-0">
    <template v-if="loaded && !globalData.startWithAirplaneMode">
      <complete-view
        :light="false"
        :is-mixture-standard="isMixtureStandard"
        :standard-content="standardContent"
        :content-complete-flag="detail.contentCompleteFlag"
        :time-complete-flag="detail.timeCompleteFlag"
        :time-complete-standard="detail.timeCompleteStandard"
        :percentage="percentage"
        :percentage-text="percentageText"
      />
      <!-- <template v-if="isMixtureStandard">
        <yxtf-tooltip :content="standardContent">
          <yxtf-svg
            class="opacity5 yxtulcdsdk-course-summary__hover mb2"
            width="20px"
            height="20px"
            icon-class="prompt-0"
          />
        </yxtf-tooltip>
        <span class="opacity8 ml4">{{ $t('pc_kng_uplaoder_btn_study_process'/**学习进度 */) }}</span>
        <template v-if="detail.timeCompleteStandard !== 0">
          <span class="ml12 opacity8">{{ $t('pc_kng_courseware_standard_lbl_duration'/**时长 */) }}</span>
          <yxtf-svg
            v-if="detail.timeCompleteFlag === 1"
            class="ml2 opacity5"
            width="20px"
            height="20px"
            icon-class="ok-facial"
          />
          <div v-else class="yxtulcdsdk-course-summary__circle"></div>
        </template>
        <span class="ml14 opacity8">{{ $t('pc_kng_courseware_standard_lbl_content'/** 内容 */) }}</span>
        <yxtf-svg
          v-if="detail.contentCompleteFlag === 1"
          class="ml2 opacity5"
          width="20px"
          height="20px"
          icon-class="ok-facial"
        />
        <div v-else class="yxtulcdsdk-course-summary__circle"></div>
      </template>
      <template v-else>
        <yxtf-progress
          v-if="percentage < 100"
          color="rgba(255,255,255,0.7)"
          line-width="80px"
          :percentage="percentage"
          :show-text="false"
        />
        <yxtf-svg
          v-else
          class="opacity8"
          width="20px"
          height="20px"
          icon-class="ok-facial"
        />

        <span class="opacity8 ml8">{{ $t('pc_ulcdsdk_lbl_completestatus'/**已完成 */) }} {{ percentageText }}</span>
      </template> -->
      <!-- <span v-if="summaryData.studyScores && commonParam.targetCode !=='o2o'" class="opacity8 ml26">{{ $t('pc_ulcdsdk_lbl_gotscore'/**已获学分 */) }} {{ scoreText }}</span> -->
    </template>
  </div>
</template>

<script>
import { KNG_TYPE } from '../../enum';
import { getKngSummary } from '../../service';
import completeView from './summary/complete-view.vue';
export default {
  components: { completeView },
  name: 'CourseSummary',
  inject: ['getDetail', 'getCommonParam', 'getGlobalData', 'getIsUsedByKng'],
  data() {
    return {
      summaryData: {},
      loaded: false
    };
  },
  computed: {
    // 内容+ 时长的完课标准
    isMixtureStandard() {
      const {contentCompleteStandard, type} = this.detail;
      return type !== KNG_TYPE.COURSE && contentCompleteStandard === 1;
    },
    standardContent() {
      if (this.isMixtureStandard && this.detail.timeCompleteStandard !== 0) {
        return this.$t('pc_kng_courseware_standard_tip_and_content_you_can_get_credits_come_on');
      } else {
        return this.$t('pc_ulcdsdk_lbl_completecontentgetscore'/** 完成规定学习内容，即可获得学分，加油 ！ */);
      }
    },
    globalData() {
      return this.getGlobalData();
    },
    detail() {
      return this.getDetail();
    },
    commonParam() {
      return this.getCommonParam();
    },
    percentage() {
      const {type, schedule} = this.detail;
      if (type !== KNG_TYPE.COURSE) {
        return schedule;
      }
      const {finishNum, totalNum} = this.summaryData;
      return finishNum / totalNum * 100;
    },
    percentageText() {
      const {type} = this.detail;
      const {finishNum, totalNum} = this.summaryData;
      if (this.percentage === 100) return this.$t('pc_ulcdsdk_lbl_completestudy'/** 已完成学习*/);
      if (type === KNG_TYPE.COURSE) return `${this.$t('pc_ulcdsdk_lbl_completestatus'/** 已完成 */)} ${finishNum}/${totalNum}`;
      return `${this.$t('pc_ulcdsdk_lbl_completestatus'/** 已完成 */)} ${parseInt(this.percentage)}%`;
    },
    scoreText() {
      const {type} = this.detail;
      const {acqScores, studyScores} = this.summaryData;
      if (type === KNG_TYPE.ZIP || type === KNG_TYPE.NEWLINK) return '';
      return `${acqScores}/${studyScores}`;
    }
  },
  watch: {
    percentage(to) {
      if (to === 100 && this.detail.enableStudyedAlert) {
        this.$root.$emit('SHOW_COMPLETE_COMMENT');
      }
      this.noticeUpdateSchedule();
    },
    'globalData.startWithAirplaneMode'() {
      this.noticeUpdateSchedule();
    }
  },
  created() {
    this.setSummary();
    this.$root.$on('UPDATE_SUMMARY', this.updateSummary);
  },
  beforeDestroy() {
    this.$root.$off('UPDATE_SUMMARY', this.updateSummary);
  },
  methods: {
    async setSummary() {
      const params = {
        kngId: this.detail.id,
        ...this.commonParam
      };
      const res = await getKngSummary(params);
      this.loaded = true;
      const {bizAtt, ...other} = res;
      this.summaryData = {...other, ...bizAtt};
      this.noticeUpdateSchedule();
    },
    /**
     * 课程进度更新
     * @param {*} kngDetail 课件详情
     */
    updateSummary(kngDetail) {
      if (kngDetail) {
        // 单课件更新逻辑 不调用接口
        this.summaryData.studyScores = kngDetail.studyScore;
        // 完课了使用总学分给已得学分
        if (kngDetail.schedule === 100) this.summaryData.acqScores = kngDetail.studyScore;
      } else {
        // 课程包逻辑，调用接口更新进度
        this.setSummary();
      }
    },
    noticeUpdateSchedule() {
      if (!this.getIsUsedByKng()) {
        const data = {
          show: this.loaded && !this.globalData.startWithAirplaneMode,
          percentage: this.percentage,
          isMixtureStandard: this.isMixtureStandard,
          standardContent: this.standardContent,
          timeCompleteStandard: this.detail.timeCompleteStandard,
          timeCompleteFlag: this.detail.timeCompleteFlag,
          contentCompleteFlag: this.detail.contentCompleteFlag,
          percentageText: this.percentageText
        };
        this.$root.$emit('SCHEDULE_UPDATE', data, this.detail.o2oAutoNext);
        this.detail.o2oAutoNext = false;
      }
    }
  }
};
</script>
