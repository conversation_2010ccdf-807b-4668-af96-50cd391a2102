<!-- 创建时间2023/02/07 15:39:08 -->
<!-- 创建人：Zhao bing bing -->
<!-- 组件描述：课件播放器 -->
<template>
  <div v-loading="loading" class="yxtulcdsdk-course-player color-white">
    <!-- 播放信息 -->
    <div class="yxtulcdsdk-course-player__inner yxtulcdsdk-flex">
      <div class="flex-1 p-rlt yxtulcdsdk-course-player__inner-left w0">
        <error-empty v-if="globalData.playErrorText" class="yxtulcdsdk-fullsize" :text="globalData.playErrorText" />
        <template v-else-if="kngDetail.id">
          <!-- 课件播放：音视频、文档、scorm、微课、外链课、html、压缩文件、（三方课） ；组件加载就播放 -->
          <player
            v-if="isPlaying"
            ref="mainPlayer"
            :appoint-view-loc="appointViewLoc"
            :handouts-list.sync="handoutsList"
            :srt-data.sync="srtData"
            :mark-list="markList"
            :exercise-list="exerciseList"
            :kng-detail="kngDetail"
          />
          <template v-else>
            <!-- 封面 -->
            <img
              v-if="kngDetail.coverUrl || detail.coverUrl"
              class="yxtulcdsdk-course-player__cover"
              :src="kngDetail.coverUrl || detail.coverUrl"
            >
            <div class="yxtulcdsdk-flex-center-center yxtulcdsdk-course-player__back">
              <button-view :kng-detail.sync="kngDetail" />
            </div>
          </template>

        </template>
      </div>
      <div v-if="!showFunctional" key="inner-mid" class="yxtulcdsdk-course-player__inner-mid">
        <yxtf-svg
          v-if="showCourseAi"
          class="mb12 hand"
          width="58px"
          height="58px"
          icon-class="course-ai"
          :remote-url="`${$staticBaseUrl}ufd/55a3e0/kng/pc/svg`"
          @click.native="toOperate({icon: 'assistant'})"
        />
        <div class="yxtulcdsdk-course-player__kng-operate">

          <yxtf-popover
            v-if="playSourceList.length"
            ref="popover"
            v-model="courseSourceVisible"
            width="120"
            popper-class="course-source-list"
            trigger="hover"
            :visible-arrow="false"
            placement="left-start"
          >
            <action-icon
              class="mt0-i"
              slot="reference"
              :title="$t('kng_lbl_language'/** 语言 */)"
              icon-class="lang"
            />
            <div
              v-for="item in playSourceList"
              :key="item.value"
              :class="item.value === kngDetail.playSourceLang ? 'color-primary-6-i' : ''"
              @click="toChangePlaySource(item)"
            >{{ item.name }}</div>
          </yxtf-popover>
          <action-icon
            v-for="(item,index) in kngOperationConfigs"
            :key="index"
            :title="item.title"
            :icon-class="item.icon"
            @click.native="toOperate(item)"
          />
        </div>
        <course-nav v-if="isInCourse && isUsedByKng" class="mt12" :disabled="loading" />
      </div>
      <div v-else key="inner-right" class="yxtulcdsdk-course-player__inner-right">
        <!-- 侧边内容 -->
        <note v-if="operateType === 'note'" ref="note" :kng-detail="kngDetail" />
        <key-point v-else-if="operateType === 'key_point'" :list="markList" @playAppoint="toPlayAppoint" />
        <handouts v-else-if="operateType === 'handouts'" :list="handoutsList" :file-id="kngDetail.attId" />
        <exercise v-else-if="operateType === 'exercise'" :list="exerciseList" :kng-detail="kngDetail" />
        <subtitle-text
          v-else-if="operateType === 'text'"
          :srt-data="srtData"
          :view-loc="kngDetail.viewLoc"
          :srt-lan="kngDetail.srtLan"
        />
        <assistant v-else-if="operateType === 'assistant'" :kng-id="id" />
        <yxtf-svg
          class="yxtulcdsdk-course-player__right-close"
          width="24px"
          height="24px"
          icon-class="delete-1"
          @click.native="closeFunctional"
        />
      </div>
    </div>

    <exercise-model ref="exerciseModel" :kng-detail="kngDetail" />
    <yxtbiz-gratuity
      ref="gratuity"
      v-bind="gratuityData"
    />
    <div v-if="kngDetail.envFlag === 0" id="private-cloud"></div>
  </div>
</template>

<script>
import { commonUtil as Utils } from 'yxt-biz-pc';
import SubtitleText from './components/subtitle-text';
import emitter from 'yxt-ulcd-sdk/src/mixins/emitter';
import { getKngDetail, getMarkList, getKngExercise, getNewDownloadUrl, deleteStudying, getCourseAi} from '../service';
import { ENV_FLAG, KNG_TYPE } from '../enum';
import ActionIcon from './components/action-icon.vue';
import KeyPoint from './components/key-point.vue';
import Handouts from './components/handouts.vue';
import Exercise from './components/exercise.vue';
import Note from './components/note.vue';
import Assistant from './components/assistant.vue';
import Player from './components/player.vue';
import ButtonView from './components/button-view.vue';
import ErrorEmpty from './components/error/empty.vue';
import ExerciseModel from './components/exercise-model.vue';
import workerTimer from '../timer';
import { cacheDownloadingKng, continueDownload, downloadUrlName, removeDownloadingKng } from '../utils';
import { preloadKngInfo, getContributors } from 'yxt-ulcd-sdk/packages/course-page/service';
import CourseNav from 'yxt-ulcd-sdk/packages/course-page/src/components/course-nav.vue';
export default {
  name: 'YxtUlcdSdkCoursePlayer',
  components: {
    SubtitleText,
    Note,
    ActionIcon,
    KeyPoint,
    Handouts,
    Exercise,
    Player,
    ButtonView,
    ErrorEmpty,
    ExerciseModel,
    CourseNav,
    Assistant
  },
  mixins: [ emitter],
  provide() {
    return {
      getNotePositionInfo: this.getNotePositionInfo
    };
  },
  inject: ['getDetail', 'getBossDownloadConfig', 'getFactorConfig', 'getWatermarkConfig', 'getCourseId', 'getCommonParam', 'getGlobalData', 'getScanQueryData', 'getIsUsedByKng', 'setPlayingDetail', 'doFaceRecognition'],
  props: {
    id: {} // 课件id
  },
  data() {
    return {
      isPlaying: false,
      loading: false,
      kngDetail: {
        viewLoc: 0,
        srtLan: 'zh'
      },
      showFunctional: false,
      downloading: false, // 下载中，生成水印文件中的标记位
      markList: [], // 关键点列表
      exerciseList: [], // 练习列表
      handoutsList: [], // 讲义列表
      srtData: [], // 音视频文稿
      appointViewLoc: -1,
      gratuityData: {
        // 在线课堂打赏模块code
        moduleCode: 'MK0001',
        // 来源模块的code
        sourceType: '1002',
        // 当前模块的type,既在线课堂的type
        moduleType: '1002',
        // 知识Id或者课程包Id
        serviceId: '',
        // 来源Id, 从哪进来的详情，传那边的业务id，目前组件那边说先传知识的id
        sourceId: '',
        // 被打赏人用户Id
        targetUserId: '',
        // 打赏的知识名称
        serviceName: ''
        // 错误信息
      },
      showCourseAi: false,
      courseSourceVisible: false
    };
  },
  computed: {
    globalData() {
      return this.getGlobalData();
    },
    detail() {
      return this.getDetail();
    },
    playSourceList() {
      const { multiFileList } = this.kngDetail;
      if (multiFileList && multiFileList.length > 1) {
        return multiFileList.map(item => ({ name: item.langName, value: item.i18nCode, fileId: item.fileId }));
      }
      return [];
    },
    commonParam() {
      return this.getCommonParam();
    },
    courseId() {
      return this.getCourseId();
    },
    isInCourse() {
      return !!this.courseId;
    },
    isUsedByKng() {
      return this.getIsUsedByKng();
    },
    isShowDownload() {
      const bossDownloadConfig = this.getBossDownloadConfig && this.getBossDownloadConfig() || {};
      return bossDownloadConfig.isOpen && bossDownloadConfig.fileTypes.includes(this.kngDetail.type);
    },
    canDownload() {
      return this.kngDetail.downloadStatus === 1;
    },
    kngOperationConfigs() {
      if (!this.kngDetail.id || this.globalData.playErrorText) return [];
      const list = [];
      if (this.srtData.length) {
        list.push({ title: this.$t('pc_kng_detail_audio_text'/** 文稿 */), icon: 'text' });
      }
      if (this.handoutsList.length) {
        list.push({title: this.$t('pc_kng_detail_side_title1'/** 讲义*/), icon: 'handouts'});
      }
      if (this.markList.length > 0) {
        list.push({title: this.$t('pc_kng_mark_lbl_key'/** 关键点*/), icon: 'key_point'});
      }
      if (this.exerciseList.length > 0) {
        list.push({title: this.$t('pc_kng_course_lbl_exercise'/** 练习 */), icon: 'exercise'});
      }
      const factorConfig = this.getFactorConfig();
      if (factorConfig.showNote) {
        list.push({title: this.$t('pc_kng_note_lbl_note'/** 笔记 */), icon: 'note'});
      }
      if (this.detail.rewardFlag && this.detail.rewardBossSwitch) {
        list.push({ title: this.$t('pc_kng_note_lbl_reward'/** 打赏 */), icon: 'reward'});
      }
      if (this.isShowDownload && this.canDownload) {
        list.push({title: this.downloading ? this.$t('pc_ulcdsdk_lbl_requesting'/** 请求中 */) : this.$t('pc_kng_mgmt_lbl_opt_download'/** 下载*/), icon: 'download'});
      }
      return list;
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.init();
      }
    },
    'kngDetail.schedule'(to) {
      // 完成状态
      const studyStatus = to > 0 ? (to === 100 ? 2 : 1) : 0;
      // 音视频完课时是否自动播放下一个
      const autoNext = this.kngDetail.autoNext;
      this.$emit('updateProgress', studyStatus, to, autoNext);
      this.kngDetail.autoNext = false;
    }
  },
  created() {
    this.$root.$on('PLAY_APPOINT', this.toPlayAppoint);
    this.$root.$on('EXERCISE_VIEW', this.toExercise);
  },
  beforeDestroy() {
    this.$root.$off('PLAY_APPOINT', this.toPlayAppoint);
    this.$root.$off('EXERCISE_VIEW', this.toExercise);
  },
  methods: {
    reset() {
      this.isPlaying = false;
      this.kngDetail = {
        viewLoc: 0
      };
      this.showFunctional = false;
      this.downloading = false; // 下载中，生成水印文件中的标记位
      this.markList = [];
      this.srtData = [];
      this.exerciseList = [];
      this.handoutsList = [];
      this.appointViewLoc = -1;
      this.showCourseAi = false;
    },
    /** 请求 */
    async init() {
      this.reset();
      try {
        if (!this.id) return;
        this.loading = true;
        this.markList = await this._getMarkList();
        this.exerciseList = await this._getPracticeList();
        if (this.detail.type !== KNG_TYPE.COURSE) {
          // 单课件直接使用详情对象
          this.readyPlay(this.detail, this.globalData.autoPlay);
        } else {
          await this._deleteStudying(); // 删除正在学，防止获取详情接口走入多课程同时学逻辑
          const res = await this._getKngDetail(this.id);
          this.readyPlay(res, this.globalData.autoPlay);
        }
      } catch (e) {
        console.error(e);
      } finally {
        this.loading = false;
      }
    },
    // 是否展示课程助手
    async _setIsShowCurseAi() {
      const {answerFlag} = await getCourseAi({kngId: this.id});
      this.showCourseAi = answerFlag === 1;
    },
    // 获取课件详情
    async _getKngDetail(id) {
      const params = {
        courseId: this.courseId,
        kngId: id,
        ...this.commonParam
      };

      const [, perror] = await preloadKngInfo(params);
      if (perror) {
        this._errorHandler(perror);
        return Promise.reject(perror);
      }
      const [res, error] = await getKngDetail(params);
      if (error || res.lastName) { // 理论上不会有lastName了，因为调用了删除正在学
        this._errorHandler(error || {key: 'apis.kng.study.needChange'});
        return Promise.reject(error);
      }
      return res;
    },
    _errorHandler(error) {
      this.$root.$emit('ERROR_HANDLER', error, {
        isInner: true,
        callback: (command) => {
          if (command === 'restudy') {
            this.restudy();
          } else if (command === 'continue') {
            this.$root.$emit('START_PLAY');
          }
        }
      });
    },
    restudy() {
      this.globalData.autoPlay = true;
      this.init();
    },
    async _deleteStudying() {
      try {
        await deleteStudying();
      } catch (e) {}
    },
    // 获取打点
    async _getMarkList() {
      try {
        const res = await getMarkList(this.id);
        return res;
      } catch (e) {}
      return [];
    },
    async _getPracticeList() {
      try {
        const params = {
          kngId: this.id,
          courseId: this.courseId,
          ...this.commonParam
        };
        const res = await getKngExercise(params);
        return res;
      } catch (e) {}
      return [];
    },

    // 准备播放
    readyPlay(obj, autoPlay) {
      this.kngDetail = Object.assign(obj, { srtLan: 'zh', playSourceLang: '' });
      this.setPlayingDetail(this.kngDetail);
      this.checkDownloadingContinue();
      this._setIsShowCurseAi();
      const {type} = obj;
      if (type === KNG_TYPE.ZIP) return;
      this.$nextTick(() => {
        // 有默认笔记位置直接播放笔记位置
        if (this.globalData.notePosition > -1) {
          this.toPlayAppoint(this.globalData.notePosition);
        } else if (autoPlay) {
          this.play();
        }
      });
    },
    // 开始播放，isPlaying
    play() {
      this.doFaceRecognition && this.doFaceRecognition('studyBefore', (state) => {
        if (state === 1) {
          this.isPlaying = true;
        }
      });
    },
    toOperate(item) {
      switch (item.icon) {
        case 'handouts':
        case 'key_point':
        case 'exercise':
        case 'note':
        case 'text':
        case 'assistant':
          this.operateType = item.icon;
          this.showFunctional = true;
          break;
        case 'download':
          this.toDownload();
          break;
        case 'reward':
          this.toReward();
          break;

      }
    },
    toChangePlaySource(item) {
      this.courseSourceVisible = false;
      if (this.kngDetail.playSourceLang === item.value) return;
      this.$root.$emit('CHANGE_PLAY_SOURCE', item);
    },
    async closeFunctional() {
      if (this.operateType === 'note') {
        await this.$refs.note.$refs.editor.check();
      }
      this.showFunctional = false;
    },
    checkDownloadingContinue() {
      continueDownload(this.kngDetail.id, () => {
        this.toDownload();
      });
    },
    // 下载
    async toDownload() {
      if (this.downloading) return;
      try {
        this.downloading = true;
        // 真水印逻辑 课件设置：文档，水印跟随平台，下载真水印 & 平台设置：开启水印，固定水印
        // downloadType  0:下载源文件，1:下载真水印文档
        const watermarkConfig = this.getWatermarkConfig();
        if (this.kngDetail.type === KNG_TYPE.DOC &&
          this.kngDetail.watermarkFlag === 0 &&
          this.kngDetail.downloadType === 1 &&
          watermarkConfig.enabled &&
          watermarkConfig.type === 1) {
          return this.doDownload(true);
        }
        this.doDownload();
      } catch (e) {
        // 下载失败
        this.stopDownload(e.message);
      }
    },
    async doDownload(trueWatermark = false) {
      const params = {
        knowledgeId: this.kngDetail.id,
        courseId: this.courseId,
        lang: this.kngDetail.playSourceLang
      };
      // 企微isv下 前端获取用户名传给后端打水印
      if (localStorage.sourceCode === '100' && this.kngDetail.type === KNG_TYPE.DOC) {
        try {
          params.fullname = await Utils.getUsername(localStorage.fullname);
        } catch (error) { }
      }
      const res = await getNewDownloadUrl(params);
      const {state, downloadUrl} = res;
      // 0-未下载过 1-水印生成中 2-水印生成完成,可以下载 3-水印生成失败
      if (state === 1) {
        return this.setDownloadTimer(trueWatermark);
      } else if ((state === 2 || state === 0) && downloadUrl) {
        if (this.kngDetail.envFlag === ENV_FLAG.PRIVATE && window.mixKngDown) {
          downloadUrlName(downloadUrl, this.kngDetail.title);
        } else {
          window.open(downloadUrl);
        }
        this.stopDownload(this.$t('pc_kng_btn_download_success'/** 已成功加入下载 */), 'success');
      } else {
        this.stopDownload(this.$t('pc_kng_downloadlist_msg_error'/** 下载失败 */));
      }
      removeDownloadingKng(this.kngDetail.id);
    },
    setDownloadTimer(trueWatermark) {
      if (this.downloadTimer) return;
      this.$fmessage({
        type: 'info',
        dangerouslyUseHTMLString: true,
        message: this.$t('pc_kng_courseware_download_msg_watermark'/** 请耐心等待，课件处理加水印中.. */) + this.$t('pc_kng_courseware_download_msg_base'/* 若无法正常下载课件，请检查浏览器是否已拦截 */)
      });
      cacheDownloadingKng(this.kngDetail.id);
      this.downloadTimer = workerTimer.setInterval(() => this.doDownload(trueWatermark), 10 * 1000);
    },
    stopDownload(msg, type = 'error') {
      this.downloading = false;
      if (this.downloadTimer) {
        workerTimer.clearInterval(this.downloadTimer);
        this.downloadTimer = null;
      }
      this.$message[type](msg || '下载失败');
    },
    // 视频关键点跳转指定位置；笔记跳转指定位置
    toPlayAppoint(time = 0) {
      // 视频未播放，先播放，再执行方法
      if (!this.isPlaying) {
        this.doFaceRecognition && this.doFaceRecognition('studyBefore', (state) => {
          if (state === 1) {

            this.appointViewLoc = time;
            this.isPlaying = true;
          }
        });
      } else {
        this.$refs.mainPlayer && this.$refs.mainPlayer.playViewLoc(time);
      }
    },
    // 打开随堂练习
    // immediate 直接答题
    toExercise(obj, immediate = false) {
      // 音视频播放的时候打开需要暂停，文档直接暂停
      const playerIsPlaying = this.$refs.mainPlayer && (this.kngDetail.type === KNG_TYPE.DOC || (this.$refs.mainPlayer.$refs.player && this.$refs.mainPlayer.$refs.player.isPaused === false));
      playerIsPlaying && this.$refs.mainPlayer && this.$refs.mainPlayer.pause();
      this.$refs.exerciseModel.todo(obj, immediate)
        .then(() => {
          // 之前在播放 或者 播放时间触发练习 关掉后直接播放
          if (playerIsPlaying || immediate) this.$refs.mainPlayer && this.$refs.mainPlayer.start();
        });
    },
    // 打赏
    async toReward() {
      const {id, title, shareFlag, type} = this.detail;

      const contributors = await getContributors(id);
      const filteredContributors = contributors.filter(item => item.contributorsId !== localStorage.userId);
      if (filteredContributors.length === 0) {
        return this.$message.warning(this.$t('pc_ulcdsdk_lbl_nosupportgratuityself'));
      }
      const targetUsers = filteredContributors.map(item => {
        return {
          userId: item.contributorsId,
          username: item.username,
          fullname: item.contributorsName,
          deptName: item.contributorsDeptName,
          imgUrl: item.contributorsImage
        };
      });
      const isMultiple = targetUsers.length > 1;
      if (isMultiple) {
        this.gratuityData.isMultiple = true;
        this.gratuityData.targetUsers = targetUsers;
      } else {
        this.gratuityData.targetUserId = targetUsers[0].userId;
      }
      this.gratuityData.targetOrgId = localStorage.orgId;
      this.gratuityData.serviceId = id;
      this.gratuityData.sourceId = id;
      // this.gratuityData.targetUserId = contributorsId;
      this.gratuityData.serviceName = title;
      this.gratuityData.moduleType = '1002';
      this.gratuityData.sourceType = '1002';
      const {targetCode, targetId, taskId} = this.getScanQueryData();
      if (['o2o', 'flip', 'gwnl'].includes(targetCode)) {
        if (targetCode === 'o2o' || targetCode === 'flip') {
          this.gratuityData.moduleType = type === KNG_TYPE.COURSE ? '1041' : '1040';
          this.gratuityData.sourceType = '1001';
        } else {
          const types = {
            [KNG_TYPE.COURSE]: '1004',
            [KNG_TYPE.DOC]: '1006',
            [KNG_TYPE.VIDEO]: '1007',
            [KNG_TYPE.AUDIO]: '1008',
            [KNG_TYPE.WEIKE]: '1009',
            [KNG_TYPE.SCORM]: '1010',
            [KNG_TYPE.HTML]: '1005',
            [KNG_TYPE.ZIP]: '1050',
            [KNG_TYPE.NEWLINK]: '1075'
          };
          this.gratuityData.moduleType = types[type];
          this.gratuityData.sourceType = '1007';
        }
        this.gratuityData.sourceId = targetId || taskId || '';
      }
      if (shareFlag === 2) {
        this.gratuityData.error = this.$t('pc_kng_mgmt_lbl_group_gratuity_msss3'/** 非当前平台课程不支持打赏*/);
      }
      this.$nextTick(() => {
        this.$refs.gratuity.init({
          error: this.gratuityData.error
        });
      });
    },
    /** **************** provide方法 *******************/
    // 提供provide方法，供子组件-设置笔记
    getNotePositionInfo(isManual = false) {
      // kngDetail 中的viewLoc会实时更新
      const {id, type, courseId, kngVersion, title, viewLoc} = this.kngDetail;
      const data = {kngType: type, kngId: id, kngName: title, kngVersion, courseId: courseId || ''};
      const isAVD = [KNG_TYPE.AUDIO, KNG_TYPE.VIDEO, KNG_TYPE.DOC].includes(type);
      // 课件：只支持音视频文档
      // 课程包：都支持
      if (this.isInCourse) {
        if (this.isPlaying) { // 课程中-播放中
          if (isAVD) {
            const minLoc = type === KNG_TYPE.DOC ? 1 : 0;
            data.position = Math.max(minLoc, viewLoc);
          } else {
            data.position = -1;
          }
        } else { // 课程中未播放
          if (isManual && isAVD) {
            if (type === KNG_TYPE.DOC) data.position = 1;
            else data.position = 0;
          } else {
            data.position = -1;
          }
        }
      } else {
        if (!isManual || !isAVD) {
          if (isManual) {
            this.$message.warning(this.$t('pc_kng_note_tip_not_get_progress'));
          }
          return null;
        }
        if (this.isPlaying) {
          const minLoc = type === KNG_TYPE.DOC ? 1 : 0;
          data.position = Math.max(minLoc, viewLoc);
        } else if (isManual) {
          if (type === KNG_TYPE.DOC) data.position = 1;
          else data.position = 0;
        } else {
          data.position = -1;
        }
      }
      if (data.position > -1) {
        data.type = 1; // 可以设置位置，用于图标区分
      } else {
        data.type = 2; // 不可以设置位置，用于图标区分
      }
      return data;
    }

  }
};
</script>
