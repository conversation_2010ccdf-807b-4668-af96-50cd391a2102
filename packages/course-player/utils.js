
import { commonUtil } from 'yxt-biz-pc';
import { KNG_TYPE } from './enum';
import { getCustomKngTypeNameConfig } from './service';
const i18n = commonUtil.i18n;
let _customKngTypeName = {};
/**
 * 标准学习时长：后台设置标准学习时长
 * 60分钟以下，显示单位为分钟，保留小数点后一位，向下取整，不足0.1分钟显示0.1分钟；
 * 60分钟及以上，显示单位为小时，保留小数点后一位，向下取整；
 * @param {number} seconds 秒
 * @returns {string}
 */
export const getMinuteFormat = (seconds) => {
  const m = Math.round((Math.floor(seconds * 10 / 60))) / 10;
  if (m < 60) {
    return i18n.t('pc_ulcdsdk_lbl_minute'/** 分钟*/, [numberFloor(m)]);
  }
  return i18n.t('pc_ulcdsdk_lbl_hours'/** 小时 */, [numberFloor(m / 60) ]) ;
};
/**
 * @desc numberFloor 处理数字,根据位数向下取整
 * @param {Number} value 传入的数字，用于计算
 * @param {Number} decimals 留几位小数，默认1
 * @returns {String}
 */
export const numberFloor = (value, decimals = 1) => numberUnit(value, decimals, 10, [''], 0, '');
/**
 * @desc 处理数字单位
 * <AUTHOR>
 * @param {Number} value 传入的数字，用于计算
 * @param {Number} decimals 留几位小数，默认0
 * @param {Number} kilo 多少进制，默认10：十进制
 * @param {Array<String>} units 每组进制单位
 * @param {Number} startIndex 从指定索引开始显示单位，默认从千开始处理单位
 * @param {String} overflow 是否溢出处理，溢出的符号，传入'' 表示溢出处理时没有符号，默认+
 * @param {Number} overflowValue 溢出值，默认超出9999999溢出
 * @param {Array<Number>} decimalsRule 保留几位小数的规则(每个单位的小数规则)，长度与units对应，如果传入的decimals>=0，则舍弃规则
 * @return {String} result 返回展示的结果
 */
export const numberUnit = (value, decimals, kilo, units, startIndex, overflow, overflowValue, decimalsRule) => {
  value = Number(value) || 0; // 当前值
  if (decimals >= 0) {
    decimalsRule = null;
  } else {
    decimals = 0; // 保留几位小数，默认0
    decimalsRule = decimalsRule || [0, 0, 0, 1, 1]; // 小数规则(每个单位的小数规则)，长度与units对应，如果传入的decimals>=0，则舍弃规则
  }
  kilo = kilo || 10; // 十进制
  units = units || ['', '', '', 'k', 'w']; // 每组进制单位
  startIndex = (startIndex === undefined || startIndex === null) ? 2 : startIndex; // 从指定索引开始显示单位，默认从千开始处理单位
  overflow = (overflow === undefined || overflow === null) ? '+' : overflow; // 是否溢出处理，溢出的符号，传入'' 表示溢出处理时没有符号，默认+
  overflowValue = (overflowValue === undefined || overflowValue === null) ? 9999999 : overflowValue; // 溢出值，默认超出9999999溢出

  if (value === 0) return value;
  for (let i = units.length; i > startIndex; i--) {
    const unitNumber = Math.pow(kilo, i - 1);
    const surplus = value % unitNumber;
    const lastIndex = i === startIndex + 1;
    if (surplus !== value || lastIndex) {
      // 最终单位处理
      // 如果是最后一次循环，只需要取余
      let num = lastIndex ? value : (value / unitNumber);
      if (overflow && overflowValue && value > overflowValue && i === units.length && num >= kilo) {
        // 如果需要溢出处理，且当前值已溢出，则处理成最大值
        num = parseInt(overflowValue / unitNumber) + units[i - 1] + overflow;
      } else {
        const tepDecimals = (decimalsRule ? decimalsRule[i - 1] : decimals) || 0; // 获取当前保留的小数位数
        num = Math.floor(num * Math.pow(10, tepDecimals)) / Math.pow(10, tepDecimals);
        if (num % 1 > 0) {
          num = num.toFixed(tepDecimals);
        }
        num += units[i - 1];
      }
      return num;
    }
  }
};

/**
 * @desc numberScore 学分规则计算方式
 * @param {Number} value 传入的数字,用于计算
 * @returns {String}
 */
export const numberScore = (value) => {
  return numberUnit(value, value < 1000 ? 2 : 1);
};
// 处理时间显示
export const handleDateFormat = (date) => {
  return date ? date.slice(0, 16).replace(/-/g, '/') : '';
};

/**
 * dom元素全屏
 * @param {*} docElm
 */
export const elementFullscreen = (docElm = {}) => {
  if (docElm.requestFullscreen) {
    docElm.requestFullscreen();
  } else if (docElm.msRequestFullscreen) {
    docElm.msRequestFullscreen();
  } else if (docElm.mozRequestFullScreen) {
    docElm.mozRequestFullScreen();
  } else if (docElm.webkitRequestFullScreen) {
    docElm.webkitRequestFullScreen();
  }
};

export const getQueryString = name => {
  try {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    r = window.location.hash
      .substr(window.location.hash.indexOf('?') + 1)
      .match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return null;
  } catch (e) {
    return null;
  }
};
export const getAllQueryData = () => {
  try {
    const queryData = window.location.href.split('?')[1].split('&');
    let data = {};
    for (let i in queryData) {
      const key = queryData[i].split('=')[0];
      const value = queryData[i].split('=')[1] || '';
      data[key] = value;
    }
    return data;
  } catch (e) {
    console.log(e);
    return {};
  }
};

export const getMediaTimeStr = seconds => {
  const h = parseInt(seconds / (60 * 60)); // 时
  const m = parseInt((seconds - h * 60 * 60) / 60); // 分
  const s = seconds - h * 60 * 60 - m * 60;
  let data = [];
  if (h > 0) data.push(h < 10 ? `0${h}` : h);
  data.push(m < 10 ? `0${m}` : m);
  data.push(s < 10 ? `0${s}` : s);
  return data.join(':');
};
// 初始化课件类型名称，scorm&html支持自定义设置
export const initKngTypeName = async() => {
  _customKngTypeName = {};
  const [res] = await getCustomKngTypeNameConfig();
  if (res) {
    const {nameSwitch, htmlLan, scormLan} = res;
    if (~~nameSwitch === 1) {
      htmlLan && (_customKngTypeName[KNG_TYPE.HTML] = htmlLan);
      scormLan && (_customKngTypeName[KNG_TYPE.SCORM] = scormLan);
    }
  }
};
// 获取课件类型名称
export const getKngTypeName = type => {
  const map = {
    [KNG_TYPE.COURSE]: i18n.t('pc_kng_common_lbl_course'/** 课程 */),
    [KNG_TYPE.DOC]: i18n.t('pc_kng_common_lbl_doc'/* 文档 */),
    [KNG_TYPE.VIDEO]: i18n.t('pc_kng_common_lbl_video'/* 视频 */),
    [KNG_TYPE.AUDIO]: i18n.t('pc_kng_common_lbl_audio'/* 音频 */),
    [KNG_TYPE.WEIKE]: i18n.t('pc_kng_common_lbl_weike'/* 微课 */),
    [KNG_TYPE.SCORM]: _customKngTypeName[KNG_TYPE.SCORM] || 'SCORM',
    [KNG_TYPE.HTML]: _customKngTypeName[KNG_TYPE.HTML] || 'HTML',
    [KNG_TYPE.ZIP]: i18n.t('pc_kng_common_lbl_zipFile'/* 压缩包 */),
    [KNG_TYPE.NEWLINK]: i18n.t('pc_kng_common_lbl_newlink'/** 外链课 */),
    [KNG_TYPE.PRACTICE_IN_CLASS]: '随堂练习', // 未用到
    [KNG_TYPE.EXAMINE]: '问卷', // 未用到
    [KNG_TYPE.PRACTICE]: i18n.t('pc_kng_course_lbl_exercise'/* 练习 */),
    [KNG_TYPE.EXAM]: i18n.t('pc_kng_common_lbl_exam'/* 考试 */),
    [KNG_TYPE.SURVEY]: i18n.t('pc_kng_common_lbl_survey'/* 问卷 */),
    [KNG_TYPE.DISCUSS]: i18n.t('pc_kng_common_lbl_discuss'/* 讨论 */),
  };
  return map[type] || i18n.t('pc_ulcdsdk_lbl_unknown'/** 未知*/);
};
// 新开第三方课程
export const openThirdCourse = url => {
  const a = document.createElement('a');
  a.style.display = 'none';
  a.setAttribute('href', url);
  a.setAttribute('target', '_blank');
  a.setAttribute('referrerpolicy', 'no-referrer');
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export const cacheDownloadingKng = kngId => {
  const list = JSON.parse(localStorage.kng_docdownloadings || '[]');
  if (!list.includes(kngId)) list.push(kngId);
  localStorage.kng_docdownloadings = JSON.stringify(list);
};
export const removeDownloadingKng = kngId => {
  const list = JSON.parse(localStorage.kng_docdownloadings || '[]');
  const index = list.indexOf(kngId);
  index > -1 && list.splice(index, 1);
  localStorage.kng_docdownloadings = JSON.stringify(list);
};
export const getDownloadingKng = () => {
  const list = JSON.parse(localStorage.kng_docdownloadings || '[]');
  return list;
};

export const continueDownload = (kngId, callback) => {
  const list = getDownloadingKng();
  if (list.includes(kngId)) {
    callback(kngId);
  }
};

/**
 * 下载课件
 * @param url
 * @param name
 */
export const downloadUrlName = (url, name) => {
  try {
    const preUrl = url.split('?')[0]
    const arr = preUrl.split('.')
    name += '.' + arr[arr.length - 1]

    const xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload =  () => {
      if (xhr.status === 200) {
        saveAs(xhr.response, name)
      } else {
        console.error('Download failed')
      }
    }
    xhr.send()
  } catch (error) { }
}

/**
* 下载
* 并对文件重新命名
* @param {String} url 目标文件地址
* @param {String} filename 想要保存的文件名称
*/
const saveAs = (blob, filename) => {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')
    const body = document.querySelector('body')

    link.href = window.URL.createObjectURL(blob)
    link.download = filename

    link.style.display = 'none'
    body.appendChild(link)

    link.click()
    body.removeChild(link)

    window.URL.revokeObjectURL(link.href)
  };
}

export const setNoteCacheWithTime = (id, content) => {
  const data = JSON.parse(localStorage.UNSAVED_NOTE_CONTENT || '{}');
  const key = `${localStorage.userId}-${id}`;
  data[key] = {
    time: new Date().getTime(),
    content
  };
  localStorage.UNSAVED_NOTE_CONTENT = JSON.stringify(data);
};
export const getNoteCache = id => {
  const data = JSON.parse(localStorage.UNSAVED_NOTE_CONTENT || '{}');
  const key = `${localStorage.userId}-${id}`;
  const res = data[key];
  delete data[key];
  localStorage.UNSAVED_NOTE_CONTENT = JSON.stringify(data);
  const expire = 7 * 24 * 60 * 60 * 1000; // 缓存7天
  try {
    if (res && res.content && (new Date().getTime() - res.time) < expire) {
      return res.content;
    }
  } catch (e) {}
  return null;
};

// 活跃时长提交
export const activeDurationSubmit = function() {
  window.YxtFeLog && window.YxtFeLog.track('e_heartbeat', { lib: 'js', properties: { name: 'JXYY-2806-PC' } });
};

// 获取可滚动的父亲
export const getScrollParent = (element, passNoScroll) => {
  let node = element;
  while (node &&
    node.nodeType === document.ELEMENT_NODE &&
    node.tagName !== 'HTML' &&
    node.tagName !== 'BODY' &&
    node !== window
  ) {
    const { overflowY } = window.getComputedStyle(node);

    if (/scroll|auto/i.test(overflowY) && !(passNoScroll && node.scrollHeight <= node.clientHeight)) {
      return node;
    }

    node = node.parentNode;
  }

  return window;
};

// 核心监控埋点
export const yxtReportLog = ({ category, msg, w_succ }) => {
  try {
    window.yxtRPT && window.yxtRPT.report({ category, msg, w_succ });
  } catch (error) { }
};
