import { <PERSON>rowser } from 'packages/_utils/config/const.js';

// 创建Worker失败或者不支持Worker走window对象中的定时器
let workerTimer;
const use_custom = true;
if (typeof (Worker) !== 'undefined' && use_custom && !Browser.ie) {
  try {
    const blobURL = URL.createObjectURL(
      new Blob(
        [
          '(',

          function() {
            const timers = {};

            // 监听message 开始执行定时器或者销毁
            self.onmessage = function onMsgFunc(e) {
              switch (e.data.command) {
              // 开启定时器
                case 'interval:start':
                  timers[e.data.id] = setInterval(function() {
                    postMessage({
                      message: 'interval:tick',
                      id: e.data.id
                    });
                  }, e.data.interval);
                  break;
                  // 销毁定时器
                case 'interval:clear':
                  clearInterval(timers[e.data.id]);
                  delete timers[e.data.id];

                  postMessage({
                    message: 'interval:cleared',
                    id: e.data.id
                  });
                  break;
                  // 开启timeout
                case 'timeout:start':
                  timers[e.data.id] = setTimeout(function() {
                    postMessage({
                      message: 'timeout:tick',
                      id: e.data.id
                    });
                  }, e.data.interval);
                  break;
                  // 销毁timeout
                case 'timeout:clear':
                  clearTimeout(timers[e.data.id]);
                  delete timers[e.data.id];

                  postMessage({
                    message: 'timeout:cleared',
                    id: e.data.id
                  });
                  break;
              }
            };
          }.toString(),

          ')()'
        ],
        { type: 'application/javascript' }
      )
    );
    const worker = new Worker(blobURL);
    URL.revokeObjectURL(blobURL);

    workerTimer = {
      id: 0,
      callbacks: {},
      // 往worker里面发送定时器开始指令
      setInterval: function(cb, interval, context) {
        this.id++;
        const id = this.id;
        this.callbacks[id] = { fn: cb, context: context };
        worker.postMessage({
          command: 'interval:start',
          interval: interval,
          id: id
        });
        return id;
      },
      // 往worker里面发送定时器销毁指令
      clearInterval: function(id) {
        worker.postMessage({ command: 'interval:clear', id: id });
      },
      // 往worker里面发送timeout开始指令
      setTimeout: function(cb, interval, context) {
        this.id++;
        const id = this.id;
        this.callbacks[id] = { fn: cb, context: context };
        worker.postMessage({ command: 'timeout:start', interval: interval, id: id });
        return id;
      },
      // 往worker里面发送timeout销毁指令
      clearTimeout: function(id) {
        worker.postMessage({ command: 'timeout:clear', id: id });
      },

      // 监听worker 里面的定时器发送的message 然后执行回调函数
      onMessage: function(e) {
        switch (e.data.message) {
          case 'interval:tick':
          case 'timeout:tick': {
            const callbackItem = this.callbacks[e.data.id];
            if (callbackItem && callbackItem.fn) { callbackItem.fn.apply(callbackItem.context); }
            break;
          }

          case 'interval:cleared':
          case 'timeout:cleared':
            delete this.callbacks[e.data.id];
            break;
        }
      }
    };
    worker.onmessage = workerTimer.onMessage.bind(workerTimer);
  } catch (e) {
    // timer创建失败，使用window对象上的
    workerTimer = window;
  }
} else {
  workerTimer = window;
}

export default workerTimer;
