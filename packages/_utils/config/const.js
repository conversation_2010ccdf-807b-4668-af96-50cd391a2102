
const u = navigator.userAgent.toLowerCase();
const p = navigator.platform.toLowerCase();
const d = /firefox/.test(u); // isFirefox
const r = /opera|opr\/[\d]+/.test(u); // isOpera
const k = !r && /(msie|trident)/.test(u); // isIE
const e = /edge\/(\d+)/.test(u); // isEdge
const b = !r && !e && /chrome/.test(u) && /webkit/.test(u); // isChrome
const o = !r && !e && !b && /safari/.test(u); // isSafari

const userAgents = navigator.userAgent.split(' ');
const language = userAgents[userAgents.indexOf('language')] || '';

export const Browser = {
  // 移动终端浏览器版本信息
  win: p ? /win/.test(p) : /win/.test(u),
  mac: p ? /mac/.test(p) : /mac/.test(u),
  trident: u.indexOf('trident') > -1, // IE内核
  opera: r, // opera
  webKit: u.includes('applewebkit'), // 苹果、谷歌内核
  firefox: d, // 火狐
  ie: k,
  ieVersion: '',
  safari: o,
  edge: e,
  chrome: b,
  mobile: !!u.match(/applewebkit.*mobile.*/), // 是否为移动终端
  ios: !!u.match(/\(i[^;]+;?( u;)? cpu.+mac os x/), // ios终端
  android: u.includes('android') || u.includes('linux'), // android终端或者uc浏览器
  iPhone: u.includes('iphone') || u.includes('mac'), // 是否为iPhone
  iPad: u.includes('ipad'), // 是否iPad
  webApp: !u.includes('safari'), // 是否web应该程序，没有头部与底部
  weixin: !!u.match(/micromessenger/i),
  winwechat: u.includes('windowswechat'), // windows 微信客户端
  dingtalk: u.includes('dingtalk'), // 钉钉
  qqbrowser:
    !u.match(/micromessenger/i) &&
    u.includes('mqqbrowser') &&
    u.includes('yxtapp'), // 手机qq浏览器
  ios9: u.match(/os [9]_\d[_\d]* like mac os x/i),
  qq: u.includes('qq/'), // qq壳
  fxiaoke: u.includes('fsbrowser'),
  lanxin: u.toLowerCase().includes('lanxin'), // 蓝信
  language: (language.split('/')[1] || navigator.language || navigator.userLanguage).toLowerCase(),
  qyweixin: u.includes('micromessenger') && u.includes('wxwork')
};

export const OJT_ULCD_REQUEST_HEADER = { 'X-Ulcd-App-Code': 'ojt' }; // 带教请求ulcd 请求头处理

export const SOURCE_CODES = {
  wecom: '100', // 企业微信ISV
  feishu: '106', // 飞书ISV
  dingtalk: '104' // 钉钉ISV
};
