export const TASK_TYPE = {
  SIGN: 0, // 考勤
  OFFLINE: 1, // 面授
  EXAM: 2, // 考试
  SURVEY: 3, // 调查
  EVALUATE: 4, // 评价
  VOTE: 5, // 投票
  HOMEWORK: 6, // 作业
  ACTIVITY: 7, // 活动
  DOC: 8, // 文档
  COURSE: 9, // 课程
  WEIKE: 10, // 微课
  VIDEO: 11, // 视频
  AUDIO: 12, // 音频
  SCORM: 13, // scorm
  OTHER: 14, // 其他
  TASK_GROUP: 15, // 任务组
  HTML: 16,
  LIVE: 17, // 直播
  KNG: 18, // 虚拟类型 (ULCD 课件)(2021.3.26号之前 是知识 后@ligz要求改为课程 不同于9)
  EXERCISE: 19, // 练习
  QNR: 20, // 虚拟类型 (ULCD 问卷)
  APPRAISAL: 21,
  NEW_LINK: 22
};

export const CERTIFICATE_CONDITION = {
  TASK: 0, // 完成任务
  EXAM_QUALIFIED: 1, // 考试合格
  EXAM_EXCELLENT: 2, // 考试优秀
  CREDIT: 3, // 总学分
  ALL_TASK: 4, // 所有任务通过或者合格
  STAGE: 5, // 完成阶段
  MANUAL: 6, // 手动
  END_STAGE_TRAINEE: 7, // 学员阶段出师后颁发证书
  END_PROJECT_TRAINEE: 8, // 学员项目出师后颁发证书
  END_TASK_TRAINEE: 9, // 学员任务出师后颁发证书
  PROJECT: 10, // 完成项目
  GROUP: 11 // 完成项目组
};

export const OJT_MODE = {
  STAGE: 0, // 阶段制
  PROJECT: 1, // 项目制
  TASK: 2 // 任务之
};

export const PROJECT_STATUS = {
  UNSAVED: 0, // 未保存
  DRAFT: 1, // 草稿 未发布
  GOING: 2, // 进行中
  END: 3, // 结束
  ARCHIVE: 4, // 归档
  DELETE: 5, // 删除
  PAUSE: 6, // 暂停
  REVOKE: 7 // 撤回
};

// 师徒互选的方式
export const MatchType = {
  selectTeacher: 0, // 学员选导师
  selectStudent: 1 // 导师选学员
};

// 学员选导师,学员的范围类型
export const MatchTeacherWithStuType = {
  all: 0, // 项目内所有学员
  designated: 1 // 指定学员
};

// 学员选导师,导师的范围 ；
export const MatchTeacherRangeType = {
  all: 2, // 平台内的所有导师
  parentDept: 1, // 上级部门的导师
  sameDept: 0 // 同部门的导师
};
// 导师选学员，学员的范围
export const MatchStudentRangeType = {
  all: 12, // 项目内所有学员
  parentDept: 11, // 本项目内，上级部门的学员
  sameDept: 10 // 本项目内，同部门的学员
};

export const CERTIFICATE_STATUS = {
  normal: 0, // 生效中
  revoke: 1, // 吊销
  expired: 2 // 过期
};
