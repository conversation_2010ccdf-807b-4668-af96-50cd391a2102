
import { commonUtil } from 'yxt-biz-pc';

export const getShortUrl = commonUtil.getShortUrl;
/**
 * 获取地址信息
 * @param {string} h5Url h5地址
 * @param {string} pcUrl pc地址
 * @param {number} isv 是否匿名 0 内部 1 游客
 */
export const generateShortUrl = async(h5Url, pcUrl, isv = 0) => {
  try {
    const shortUrl = await getShortUrl(pcUrl, h5Url, isv);
    return { url: shortUrl };
  } catch (error) {
    return { url: '' };
  }
};

export const getSimpleQrCode = async function(pcUrl, h5Url, visitor) {
  pcUrl = pcUrl.trim().startsWith('/') ? pcUrl : '/' + pcUrl;
  h5Url = h5Url.trim().startsWith('/') ? h5Url : '/' + h5Url;

  const protocol = window.location.protocol;
  const domain = window.localStorage.domain;

  // http下需要拼接
  if (!protocol.includes('https')) {
    pcUrl = `${protocol}//${domain}${pcUrl}`;
  }
  const res = await generateShortUrl(h5Url, pcUrl, visitor);
  return res;
};
