
import { commonUtil } from 'yxt-biz-pc';
import qs from 'qs';

export const i18n = commonUtil.i18n;
export const VOICE_TIME = 5; // 录音时间（分钟）
export const MIN_VOICE_TIME = 2; // 最小录音时间（秒）

export const trident = navigator.userAgent.toLowerCase().indexOf('trident') > -1; // IE内核

// 取缓存value
window.getLocalStorage = function(key) {
  let value;
  try {
    value = localStorage.getItem(key);
  } catch (e) {
    value = window.getCookie(key);
  }
  return value;
};

export const isNullOrEmpty = s => {
  return s === null || s === '' || s === undefined;
};

export const loadScript = src => {
  return new window.Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.defer = true;
    script.src = src;
    script.onload = () => resolve();
    document.head.appendChild(script);
  });
};

// 媒体地址
export const getMediaUrl = () => {
  // 迁移media文件
  return `${commonUtil.getStaticCdnUrl().staticBaseUrl}ufd/55a3e0/`;
};

/**
 * 地址上加上集团标记
 */
export const addGroupTagOnUrl = (url) => {
  if (url) {
    url += `${url.indexOf('?') > 0 ? '&' : '?'}grouplink=1`;
  }
  return url;
};

export const sleep = timeout => new Promise(resolve => setTimeout(resolve, timeout));

// 任务名称
export const getTaskTypeName = (typeName, type) => {
  if (typeof typeName === 'object') {
    type = typeName.type;
    typeName = typeName.typeName;
  }
  return typeName || getTaskType(type).name || '-';
};

// 0考勤,1面授,2考试,3调查,4评价,5投票,6作业,7活动,8知识文档,9课程包,10知识微课,11知识视频,12知识音频,13知识scorm,14其他任务,15任务组, 16Html 17直播 18知识（大类型） 19练习 21鉴定 22知识外链 23 考核 25 自定义调查 28 报名登记
// ulcd 映射：1-考勤 2-课件 3-课程 4-练习 5-作业 6-考试 7-直播 8-问卷(投票、调查、评价) 9-面授 10-线下活动 11-外部链接 21-鉴定 15-知识外链 101-文档 102-视频 103-音频 104-微课 105-scorm 106-html
export const getTaskType = type => {
  let taskType = {
    code: '',
    name: '',
    ulcdKey: 0
  };
  switch (type) {
    case 0:
      taskType.code = 'sign';
      taskType.name = i18n.t('pc_o2o_lbl_sign');
      taskType.ulcdKey = 1;
      break;
    case 1:
      taskType.code = 'offline';
      taskType.name = i18n.t('pc_o2o_lbl_facetoface');
      taskType.ulcdKey = 9;
      break;
    case 2:
      taskType.code = 'exam';
      taskType.name = i18n.t('pc_o2o_lbl_exam');
      taskType.ulcdKey = 6;
      break;
    case 3:
    case 25:
      taskType.code = 'survey';
      taskType.name = i18n.t('pc_o2o_lbl_survey');
      taskType.ulcdKey = 8;
      break;
    case 4:
      taskType.code = 'evaluate';
      taskType.name = i18n.t('pc_o2o_lbl_evaluation');
      taskType.ulcdKey = 8;
      break;
    case 5:
      taskType.code = 'vote';
      taskType.name = i18n.t('pc_o2o_lbl_vote');
      taskType.ulcdKey = 8;
      break;
    case 6:
      taskType.code = 'homework';
      taskType.name = i18n.t('pc_o2o_lbl_homework');
      taskType.ulcdKey = 5;
      break;
    case 7:
      taskType.code = 'activity';
      taskType.name = i18n.t('pc_o2o_lbl_activity');
      taskType.ulcdKey = 10;
      break;
    case 8:
      taskType.code = 'doc';
      taskType.name = i18n.t('pc_o2o_lbl_doc');
      taskType.ulcdKey = 101;
      break;
    case 9:
      taskType.code = 'course';
      taskType.name = i18n.t('pc_o2o_lbl_course');
      taskType.ulcdKey = 3;
      break;
    case 10:
      taskType.code = 'weike';
      taskType.name = i18n.t('pc_o2o_lbl_weike');
      taskType.ulcdKey = 104;
      break;
    case 11:
      taskType.code = 'video';
      taskType.name = i18n.t('pc_o2o_task_videoName');
      taskType.ulcdKey = 102;
      break;
    case 12:
      taskType.code = 'audio';
      taskType.name = i18n.t('pc_o2o_lbl_audio');
      taskType.ulcdKey = 103;
      break;
    case 13:
      taskType.code = 'scorm';
      taskType.name = 'Scorm';
      taskType.ulcdKey = 105;
      break;
    case 14:
      taskType.code = 'other';
      taskType.name = i18n.t('pc_o2o_lbl_outerlink');
      taskType.ulcdKey = 11;
      break;
    case 15:
      taskType.code = 'taskgroup';
      taskType.name = i18n.t('pc_o2o_lbl_taskgroup');
      break;
    case 16:
      taskType.code = 'html';
      taskType.name = 'html';
      taskType.ulcdKey = 106;
      break;
    case 17:
      taskType.code = 'live';
      taskType.name = i18n.t('pc_o2o_lbl_live');
      taskType.ulcdKey = 7;
      break;
    case 19:
      taskType.code = 'exercise';
      taskType.name = i18n.t('pc_o2o_lbl_practice');
      taskType.ulcdKey = 4;
      break;
    case 21: // 鉴定任务
      taskType.code = 'evaluation';
      taskType.name = i18n.t('pc_o2o_lbl_verification');
      taskType.ulcdKey = 14;
      break;
    case 22: // 外链课
      taskType.code = 'link';
      taskType.name = i18n.t('pc_o2o_lbl_kngouterlink'/** 外链课 */);
      taskType.ulcdKey = 108;
      break;
    case 23: // 考核
      taskType.code = 'examine';
      taskType.name = i18n.t('pc_o2o_lbl_examine'/* 考核 */);
      taskType.ulcdKey = 112;
      break;
    case 26: // 话术训练
      taskType.code = 'sparring';
      taskType.name = i18n.t('pc_o2o_lbl_sparring'/* 话术训练 */);
      taskType.ulcdKey = 113;
      break;
    case 27:
      taskType.code = 'discuss';
      taskType.name = i18n.t('pc_o2o_lbl_discuss'); /* 讨论 */
      taskType.ulcdKey = 1001; // 注册制后为了防止占用，直接4位
      break;
    case 28:
      taskType.code = 'survey';
      taskType.name = i18n.t('pc_survey_lbl_sign'); // 报名登记
      taskType.ulcdKey = 8;
      break;

    case 29: // 演讲训练
      taskType.code = 'speech';
      taskType.name = i18n.t('pc_pd_lbl_speech'/* 演讲训练 */);
      taskType.ulcdKey = 1004;
      break;

    case 30: // 实战演练
      taskType.code = 'drill';
      taskType.name = i18n.t('pc_pd_lbl_drill'/* 实战演练 */);
      taskType.ulcdKey = 1005;
      break;
  }
  return taskType;
};

export const getLanguage = () => {
  return commonUtil.getLanguage() || 'zh';
};

// 字符串时间格式，返回时间戳
export const getTimeStamp = function(date) {
  if (!date) return;
  return new Date(date.substring(0, 19).replace(/-/g, '/')).getTime();
};

// 跳转到o2o
export const goO2OPage = (path, params, newWindow)=> {
  const url = `${window.location.origin}/o2o/#/${path}?${qs.stringify(params)}`;
  openUrl(url, newWindow);
};

export const openUrl = (url, newWindow)=> {
  if (newWindow) {
    window.open(url);
  } else {
    window.location.href = url;
  }
};

export const goGwnlPage = (path, params, extra, newWindow, isSp)=> {
  const url = `${window.location.origin}/${isSp ? 'spgwnl' : 'gwnl'}/#/${path}?${qs.stringify(params)}${extra ? '&' + extra : ''}`;
  openUrl(url, newWindow);
};

export const getQueryString = name => {
  try {
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    r = window.location.hash
      .substr(window.location.hash.indexOf('?') + 1)
      .match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    return null;
  } catch (e) {
    return null;
  }
};

/**
 * 上传附件图标类型
 * @param {String} filename 附件名称
 */
export const getAttachIconType = function(filename) {
  const suffix = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
  // .pdf,.ppt,.pptx,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.zip
  switch (suffix) {
    case 'doc':
    case 'docx':
      return 'word';
    case 'ppt':
    case 'pptx':
    case 'pps':
      return 'ppt';
    case 'pdf':
      return 'pdf';
    case 'xls':
    case 'xlsx':
      return 'excel';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
      return 'img';
    case 'zip':
    case 'rar':
      return 'zip';
    default:
      return 'doc';
  }
};

/**
 * 获取阶段编号名称
 * taskItem 任务对象
 * serialNum 序号，从1开始
 * assignKey 渲染名称key
 */
export const getPeriodCodeName = function(taskItem, serialNum, assignKey = 'periodCode') {
  if (taskItem && taskItem[assignKey]) {
    return taskItem && taskItem[assignKey];
  }
  return i18n.t('pc_o2o_lbl_period').d('阶段') + serialNum;
};
