{"name": "yxt-ulcd-sdk", "version": "1.3.19", "description": "ulcd sdk", "main": "lib/yxt-ulcd-sdk.js", "files": ["lib", "src", "packages", "types"], "typings": "types/index.d.ts", "scripts": {"bootstrap": "yxtnpm i && npm run bootstrap:sub", "build:file": "node build/bin/build-entry.js & node build/bin/i18n.js & node build/bin/version.js", "build:theme": "node build/bin/gen-cssfile && gulp build --gulpfile packages/theme-chalk/gulpfile.js && cp-cli packages/theme-chalk/lib lib/theme-chalk", "build:theme:umd": "node build/bin/gen-cssfile && gulp build --gulpfile packages/theme-chalk/gulpfile.cdn.js && cp-cli packages/theme-chalk/lib lib/theme-chalk ", "build:utils": "cross-env BABEL_ENV=utils babel src --out-dir lib --ignore src/index.js", "build:umd": "node build/bin/build-locale.js", "build:demo": "npm run bootstrap && npm run build:file && cross-env NODE_ENV=production webpack --config build/webpack.build.demo.js", "build:prod": "npm run build:demo", "clean": "rimraf lib && rimraf packages/*/lib && rimraf test/**/coverage", "copy:umd": "node build/bin/copy-umd", "deploy:build": "npm run build:file && cross-env NODE_ENV=production webpack --config build/webpack.demo.js && echo element.eleme.io>>examples/element-ui/CNAME", "deploy:extension": "cross-env NODE_ENV=production webpack --config build/webpack.extension.js", "dev:extension": "rimraf examples/extension/dist && cross-env NODE_ENV=development webpack --watch --config build/webpack.extension.js", "dev": "npm run build:file && cross-env NODE_ENV=development webpack-dev-server --config build/webpack.demo.js & node build/bin/template.js", "dev:play": "npm run build:file && cross-env NODE_ENV=development PLAY_ENV=true webpack-dev-server --config build/webpack.demo.js", "dist": "npm run clean && npm run build:file && npm run lint && webpack --config build/webpack.conf.js && webpack --config build/webpack.common.js && webpack --config build/webpack.component.js && npm run build:utils && npm run build:umd && npm run build:theme", "i18n": "node build/bin/i18n.js", "lint": "eslint src/**/* test/**/* packages/**/* build/**/* --quiet", "build:changelog": "node build/bin/build-changelog.js", "build:changelog:beta": "node build/bin/build-changelog.js --beta", "test": "npm run lint && npm run build:theme && cross-env CI_ENV=/dev/ BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "test:watch": "npm run build:theme && cross-env BABEL_ENV=test karma start test/unit/karma.conf.js", "new": "node build/bin/new.js", "build:unpkg": "npm run clean && npm run build:file && webpack --config build/webpack.unpkg.js && npm run build:umd && npm run build:theme:umd && lerna run build && npm run copy:umd", "publish:unpkg:beta": "npm run build:unpkg", "publish:unpkg": "npm run build:unpkg", "media:sync": "node build/bin/sync-gitlab.js", "media:sync:stable": "node build/bin/sync-gitlab.js --stable", "sync:version": "node build/bin/sync-version.js", "analyze": "cross-env report=yes webpack --config build/webpack.unpkg.js", "testme": "webpack --config build/webpack.unpkg.js", "ulcd:build": "npm run clean && webpack --config build/ulcd-com/webpack.ulcd.com.config.js", "ulcd:server": "node build/ulcd-com/ulcd.com.exec.js", "static": "webpack-dev-server --open --host 0.0.0.0 --port 3000  --contentBase unpkg --hot", "analyze:sub": "lerna run analyze", "bootstrap:sub": "lerna bootstrap"}, "faas": [{"domain": "element", "public": "temp_web/element"}, {"domain": "element-theme", "public": "examples/element-ui", "build": ["yarn", "npm run deploy:build"]}], "homepage": "http://xuanxing-pc-ui.yunxuetang.com.cn", "keywords": ["eleme", "vue", "components"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,vue}": ["eslint --fix", "git add"], "*.{vue,css,scss}": ["stylelint --fix", "git add"]}, "license": "MIT", "unpkg": "lib/index.js", "style": "lib/theme-chalk/index.css", "dependencies": {"ali-oss": "^6.4.0", "async-validator": "~1.8.1", "babel-helper-vue-jsx-merge-props": "^2.0.0", "cropperjs": "^1.5.6", "crypto-js": "^4.0.0", "dayjs": "^1.11.7", "deepmerge": "^1.2.0", "dotenv": "^10.0.0", "js-audio-recorder": "^1.0.7", "lamejs": "1.2.0", "lodash": "^4.17.21", "lottie-web": "^5.6.5", "moment": "^2.29.1", "normalize-wheel": "^1.0.1", "resize-observer-polyfill": "^1.5.0", "screenfull": "^5.0.0", "spark-md5": "^3.0.2", "throttle-debounce": "^1.0.1", "viewerjs": "^1.5.0", "vue-clamp": "^0.4.0", "vue-i18n": "^8.24.5", "vue-dompurify-html": "^2.3.0", "vuedraggable": "^2.24.3", "xss": "1.0.13", "yxt-factor": "^2.0.3", "yxt-i18n": "^2.2.39", "yxt-pc": "^1.3.43"}, "peerDependencies": {"axios": "^0.18.0", "vue": "2.5.21", "vuex": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/runtime": "^7.12.5", "@vue/component-compiler-utils": "^2.6.0", "algoliasearch": "^3.24.5", "axios": "^0.18.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-component": "^1.1.1", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-module-resolver": "^2.2.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.24.1", "babel-regenerator-runtime": "^6.5.0", "chai": "^4.2.0", "chokidar": "^1.7.0", "conventional-changelog": "^3.1.24", "copy-webpack-plugin": "^5.0.0", "coveralls": "^3.0.3", "cp-cli": "^1.0.2", "cross-env": "^3.1.3", "css-loader": "^2.1.0", "es6-promise": "^4.0.5", "eslint": "8.23.0", "eslint-config-elemefe": "0.1.1", "eslint-plugin-html": "^7.1.0", "eslint-plugin-json": "^1.2.0", "eslint-plugin-vue": "^9.4.0", "eslint-plugin-yxtcom": "^0.2.3", "eslint-webpack-plugin": "^2.7.0", "file-loader": "^1.1.11", "file-save": "^0.2.0", "gulp": "^4.0.0", "gulp-autoprefixer": "^6.0.0", "gulp-cssmin": "^0.2.0", "gulp-postcss": "^10.0.0", "gulp-sass": "^5.1.0", "highlight.js": "^9.3.0", "html-webpack-plugin": "^3.2.0", "husky": "^4.2.5", "json-loader": "^0.5.7", "json-templater": "^1.0.4", "karma": "^4.0.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-mocha": "^1.3.0", "karma-sinon-chai": "^2.0.2", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "^0.0.32", "karma-webpack": "^3.0.5", "lerna": "^5.6.2", "lint-staged": "^10.2.10", "markdown-it": "^8.4.1", "markdown-it-anchor": "^5.0.2", "markdown-it-chain": "^1.3.0", "markdown-it-container": "^2.0.0", "mini-css-extract-plugin": "^0.4.1", "mocha": "^6.0.2", "mockjs": "^1.1.0", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss": "^7.0.14", "postcss-loader": "^4.3.0", "postcss-rtlcss": "^1.8.2", "progress-bar-webpack-plugin": "^1.11.0", "rimraf": "^2.5.4", "sass": "^1.54.7", "sass-loader": "^7.1.0", "select-version-cli": "^0.0.2", "semver": "^5.7.1", "sinon": "^7.2.7", "sinon-chai": "^3.3.0", "style-loader": "^0.23.1", "stylelint": "^13.11.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0", "svg-sprite-loader": "^4.1.6", "terser-webpack-plugin": "^4.2.3", "tiny-sass-compiler": "^0.12.2", "transliteration": "^1.1.11", "typescript": "^4.5.5", "uppercamelcase": "^1.1.0", "url-loader": "^1.0.1", "viser-vue": "^2.4.7", "vue-i18n": "^8.24.4", "vue-loader": "^15.7.0", "vue-router": "^3.0.1", "vue-template-compiler": "2.5.21", "vue-template-es2015-compiler": "^1.6.0", "vuex": "^3.0.1", "webpack": "^4.14.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.11", "webpack-node-externals": "^1.7.2", "worker-loader": "^3.0.8", "yxt-css-assets-localizer": "0.0.1"}, "workspaces": ["subpackages/*"]}