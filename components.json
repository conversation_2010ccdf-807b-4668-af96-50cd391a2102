{"api": "./packages/api/index.js", "svg": "./packages/svg/index.js", "common-util": "./packages/common-util/index.js", "examing": "./packages/examing/index.js", "practicing": "./packages/practicing/index.js", "playframe": "./packages/playframe/index.js", "course-player": "./packages/course-player/index.js", "course-page": "./packages/course-page/index.js", "points-exchange": "./packages/points-exchange/index.js", "o2o-play-frame": "./packages/o2o-play-frame/index.js", "activity": "./packages/activity/index.js", "outlink": "./packages/outlink/index.js", "o2o-homework-task": "./packages/o2o-homework-task/index.js", "attend": "./packages/attend/index.js", "o2o-evaluation-task": "./packages/o2o-evaluation-task/index.js", "examine": "./packages/examine/index.js", "surveying": "./packages/surveying/index.js", "wrong-item": "./packages/wrong-item/index.js", "simple-course-page": "./packages/simple-course-page/index.js", "o2o-ident-task": "./packages/o2o-ident-task/index.js", "develop-view-plan": "./packages/develop-view-plan/index.js", "add-students-to-team": "./packages/add-students-to-team/index.js", "live-task": "./packages/live-task/index.js", "multi-task": "./packages/multi-task/index.js", "o2o-offline-task": "./packages/o2o-offline-task/index.js", "survey-ques": "./packages/survey-ques/index.js", "verbal-sparring": "./packages/verbal-sparring/index.js", "discuss-task": "./packages/discuss-task/index.js", "uacd-play-frame": "./packages/uacd-play-frame/index.js", "speech": "./packages/speech/index.js", "drill": "./packages/drill/index.js", "evaluation": "./packages/evaluation/index.js"}