import { addTime0 } from 'packages/_utils/core/utils.js';
import { commonUtil } from 'yxt-biz-pc';

const i18n = commonUtil.i18n;

export default {
  install: Vue => {
    // 格式化时间
    Vue.filter('formatTime', function(date) {
      if (date) {
        date = new Date(date.replace(/-/g, '/'));
      } else {
        return i18n.t('pc_o2o_lbl_nolimittime');
      }
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      // const second = date.getSeconds()
      // const millisecond = date.getMilliseconds()
      return (
        [year, month, day].map(addTime0).join('-') +
        ' ' +
        [hour, minute].map(addTime0).join(':')
      );
    });

    Vue.filter('emptyToDash', value => value == null ? '--' : value);

    Vue.filter('date', function(input, format = 'yyyy-MM-dd HH:mm', emptyChar = '') {
      if (input) {
        if (typeof input === 'string') {
          input = input.replace(/-/g, '/');
        }
        let date = new Date(input);
        return date.Format(format);
      } else {
        return emptyChar;
      }
    });

    Vue.filter('parseToThousandth', function(num) {
      num = String((num || 0));
      return num.replace(/\d(?=(\d{3})+$)/g, '$&,');
    });

    Vue.filter('scoreFilter', function(score) {
      if (!score) return 0;
      score = +score;
      let result = score;
      if (score >= 1000 && score < 10000) {
        result = (score / 1000).toFixed(1) + 'K';
      } else if (score >= 10000 && score < 990000) {
        result = (score / 10000).toFixed(1) + 'W';
      } else if (score >= 990000) {
        result = '99W+';
      }
      return result;
    });

    Vue.filter('formatFileSize', function(value, form) {
      if (!value) {
        if (form) {
          return '0 Bytes';
        } else {
          return ['0 Bytes', 0, ' Bytes'];
        }
      }

      let unitArr = [' Bytes', ' KB', ' MB', ' GB', ' TB'];
      let index = 0;
      let srcsize = parseFloat(value);
      index = Math.floor(Math.log(srcsize) / Math.log(1024));
      var size = srcsize / Math.pow(1024, index);
      size = size.toFixed(2); // 保留的小数位数
      if (form === 'size') {
        return size + unitArr[index];
      } else {
        return [size + unitArr[index], size, unitArr[index]];
      }
    });

    /**
     * 若为一分钟之内评论回复的，显示“刚刚”
     * 若为1个小时内评论回复的，显示X分钟前；不满一分钟不算一分钟，取整数；
     * 若为当天评论回复的，显示X小时前；不满一小时不算一小时，取整数；
     * 若为3天内评论回复的，显示X天前；
     * 其他显示评论回复的日期，精确到日：2021-10-10
     */
    Vue.filter('parseDate', function(value) {
      if (!value) return '';

      value = new Date(Date.parse(value.replace(/\.\d+/, '').replace(/-/g, '/'))).getTime();
      var comTime = new Date(value);
      var currentTime = new Date();
      var total = currentTime.getTime() - comTime.getTime();
      var minutesCount = 1000 * 60;

      var minutes = Math.round(total / minutesCount);
      var hour = Math.round(minutes / 60);
      var day = Math.round(hour / 24);
      if (minutes >= 0 && minutes < 1) {
        return i18n.t('pc_o2o_lbl_just' /* 刚刚 */);
      } else if (minutes >= 1 && minutes < 59) {
        return minutes + i18n.t('pc_o2o_lbl_before_minutes' /* 分钟前 */);
      } else if (minutes >= 60 && minutes < 1380) {
        return hour + i18n.t('pc_o2o_lbl_hoursAgo' /* 小时前 */);
      } else if (minutes >= 1380 && minutes <= 4320) {
        return day + i18n.t('pc_o2o_lbl_daybefore' /* 天前 */);
      } else if (minutes > 4320) {
        return window.formatDate(comTime, 'yyyy-MM-dd HH:mm');
      } else {
        return i18n.t('pc_o2o_lbl_just' /* 刚刚 */);
      }
    });

    /**
     * 显示已过天数（已过天数=当前时间-最近学习时间
     */
    Vue.filter('parseDayDate', function(value) {
      if (!value) return '';

      value = new Date(Date.parse(value.replace(/\.\d+/, '').replace(/-/g, '/'))).getTime();
      var comTime = new Date(value);
      var currentTime = new Date();
      var total = currentTime.getTime() - comTime.getTime();
      var minutesCount = 1000 * 60;

      var minutes = Math.round(total / minutesCount);
      var hour = Math.round(minutes / 60);
      var day = Math.round(hour / 24);
      if (minutes >= 0 && minutes < 1) {
        return i18n.t('pc_o2o_lbl_just' /* 刚刚 */);
      } else if (minutes >= 1 && minutes < 59) {
        return i18n.t('pc_o2o_lbl_pass_minute', [minutes] /* 已过${minutes}分钟 */);
      } else if (minutes > 0 && minutes < 1380) {
        return i18n.t('pc_o2o_lbl_pass_hours', [hour] /* `已过${hour}小时 */);
      } else if (minutes >= 1380) {
        return i18n.t('pc_o2o_lbl_pass_days', [day] /* 已过${day}天 */);
      } else {
        return '';
      }
    });
  }

};
