import { Browser } from 'packages/_utils/config/const.js';

/**
* 创建只读的代理对象
* @param needProxyObj 需要代理的对象
* @param needProxyObj
* @returns {boolean|any}
*/
export function readonlyPoxy(needProxyObj) {
  if (Browser.ie) {
    return needProxyObj;
  }

  return new Proxy(needProxyObj, {
    set(target, propertyKey, value) {
      console.error(`${propertyKey} is readonly `);
      return true;
    },
    get(target, propertyKey) {
      if (target[propertyKey] instanceof Object) {
        return readonlyPoxy(target[propertyKey]);
      }
      return Reflect.get(target, propertyKey);
    }
  });
}

