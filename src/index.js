/* Automatically generated by './build/bin/build-entry.js' */

import Svg from '../packages/svg/index.js';
import Examing from '../packages/examing/index.js';
import Practicing from '../packages/practicing/index.js';
import Playframe from '../packages/playframe/index.js';
import CoursePlayer from '../packages/course-player/index.js';
import CoursePage from '../packages/course-page/index.js';
import PointsExchange from '../packages/points-exchange/index.js';
import O2oPlayFrame from '../packages/o2o-play-frame/index.js';
import Activity from '../packages/activity/index.js';
import Outlink from '../packages/outlink/index.js';
import O2oHomeworkTask from '../packages/o2o-homework-task/index.js';
import Attend from '../packages/attend/index.js';
import O2oEvaluationTask from '../packages/o2o-evaluation-task/index.js';
import Examine from '../packages/examine/index.js';
import Surveying from '../packages/surveying/index.js';
import WrongItem from '../packages/wrong-item/index.js';
import SimpleCoursePage from '../packages/simple-course-page/index.js';
import O2oIdentTask from '../packages/o2o-ident-task/index.js';
import DevelopViewPlan from '../packages/develop-view-plan/index.js';
import AddStudentsToTeam from '../packages/add-students-to-team/index.js';
import LiveTask from '../packages/live-task/index.js';
import MultiTask from '../packages/multi-task/index.js';
import O2oOfflineTask from '../packages/o2o-offline-task/index.js';
import SurveyQues from '../packages/survey-ques/index.js';
import VerbalSparring from '../packages/verbal-sparring/index.js';
import DiscussTask from '../packages/discuss-task/index.js';
import UacdPlayFrame from '../packages/uacd-play-frame/index.js';
import Speech from '../packages/speech/index.js';
import Drill from '../packages/drill/index.js';
import Evaluation from '../packages/evaluation/index.js';
import commonUtil from '../packages/common-util/index.js';
import xss, { whiteList } from 'xss';
const components = [
  Svg,
  Examing,
  Practicing,
  Playframe,
  CoursePlayer,
  CoursePage,
  PointsExchange,
  O2oPlayFrame,
  Activity,
  Outlink,
  O2oHomeworkTask,
  Attend,
  O2oEvaluationTask,
  Examine,
  Surveying,
  WrongItem,
  SimpleCoursePage,
  O2oIdentTask,
  DevelopViewPlan,
  AddStudentsToTeam,
  LiveTask,
  MultiTask,
  O2oOfflineTask,
  SurveyQues,
  VerbalSparring,
  DiscussTask,
  UacdPlayFrame,
  Speech,
  Drill,
  Evaluation
];

components.forEach(Component => {
  Component.mixins = Component.mixins || [];
  Component.mixins.push({
    created() {
      // 业务组件埋点统计
      // from：组件名
      // aspect：事件发生描述
      // version：组件库版本
      window.YxtFeLog && window.YxtFeLog.track('e_component', {
        properties: {
          from: Component.name,
          aspect: 'load',
          version: '1.3.19'
        }
      });
    }
  });
});

try {
  for (const key in whiteList) Object.hasOwnProperty.call(whiteList, key) && whiteList[key].push('style');
} catch (error) {
}

const setStaticCdnUrl = function(Vue) {
  try {
    if (Vue) {
      const baseCommon = (typeof window !== 'undefined' && window.feConfig && window.feConfig.common);
      Vue.prototype.xss = (html, options) => xss(html, Object.assign({ stripIgnoreTag: true }, options || {}));
      Vue.prototype.$imagesBaseUrl = (baseCommon && window.feConfig.common.imagesBaseUrl) || 'https://images.yxt.com/';
      Vue.prototype.$staticBaseUrl = (baseCommon && window.feConfig.common.staticBaseUrl) || 'https://stc.yxt.com/';
    }
  } catch (e) {
    console.log(e);
  }
};

const install = function(Vue, config = {}) { // config: {env: 'dev', domain: {orginit: ''}}
  setStaticCdnUrl(Vue);

  components.forEach(component => {
    Vue.component(component.name, component);
  });

  Vue.use(commonUtil.bindDirectives);
};

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  version: '1.3.19',
  install,
  Svg,
  Examing,
  Practicing,
  Playframe,
  CoursePlayer,
  CoursePage,
  PointsExchange,
  O2oPlayFrame,
  Activity,
  Outlink,
  O2oHomeworkTask,
  Attend,
  O2oEvaluationTask,
  Examine,
  Surveying,
  WrongItem,
  SimpleCoursePage,
  O2oIdentTask,
  DevelopViewPlan,
  AddStudentsToTeam,
  LiveTask,
  MultiTask,
  O2oOfflineTask,
  SurveyQues,
  VerbalSparring,
  DiscussTask,
  UacdPlayFrame,
  Speech,
  Drill,
  Evaluation,
  commonUtil
};
