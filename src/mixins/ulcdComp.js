export default {
  props: {
    visible: { type: Boolean, default: false }, // 业务组件抽屉是否显示
    titleInternalKey: { type: String, default: '' }, // 对应2.0平台中业务组件模块下的国际化key
    oprateType: {// 组件操作类型：1-添加 2-编辑 3-草稿状态编辑
      type: Number,
      default: 1
    },
    pagePropertyConfig: {
      type: Object,
      default: () => {
        return {
        };
      }
    }, // 接收JSON对象，业务组件内属性配置-对应业务方特需定的配置，比如别名，是否必填，是否显示字段，可对业务组件内的各个字段进行配置
    bizComponentConfig: { type: Object,
      default: () => {
        return {
        };
      }
    }, // 接收JSON对象，业务组件的常规配置-对应组件库中的常规配置项，比如functionCode,dataPermissionCode
    appCode: { type: String, default: '' }, // 业务来源，使用此业务组件的具体业务方的来源信息，业务组件可通过此属性进行特定的业务逻辑判定
    taskInfo: { type: Object, default: () => { return {}; } }, // 用于业务数据修改时的回显，此值即任务应用添加时返回的用户数据JSON
    taskBase: { type: Object, default: () => { return {}; } }, // 用于业务数据修改时的回显,一般结合taskInfo使用，切里面的字段是互斥的
    isPublished: { type: Number, default: 0 }, // 是否为已发布状态，代表该任务活动是否已发布，用于控制某些表单字段是否禁用，需要业务方的页面属性配置进行禁用控制
    groupOrgFlag: { type: Number, default: 0 }, // 当前ULCD环境是否为集团版环境，0-否 1-是，默认为0，此值为方便业务组件拿到当前ULCD环境状态，具体是否使用到此值以任务应用方为准
    isMiddlePlat: {type: Number, default: 0 }, // 当前所处平台是否为中间代理平台(内容一体化)：0-否  1-是 ，任务应用方可根据此值进行中间平台的判断，也可自行获取进行判断
    maxWidth: { type: String, default: ' 960px' }, // 抽屉的最大宽度，抽屉的实际宽度是任务应用方根据组件内容自行定义的，但最大不可超过maxWidth定义的宽度，默认是‘960px’
    taskVersion: { type: String, default: '' },
    loading: {type: Boolean, default: false} // 数据加载前loading
  },
  data() {
    return {
      bizData: { meta: { taskId: '', taskVersion: '' },
        taskInfo: {},
        taskBase: {
          name: ''
        }
      }
    };
  },
  methods: {
    // TODO 集合应用和单应用是否拆不同的mixins，如果不拆的话这里应处理默认值和传值的兼容问题
    submitEmit(bizData) { // 完整业务数据，可为单条数据，也可为多条组合数据
      this.$emit('submitHandle', bizData);
    },
    cancelEmit(bizData) { // 业务数据，可为null
      this.$emit('cancelHandle', bizData);
    },
    draftSaveEmit(bizData) { // 业务数据
      this.$emit('draftSaveHandle', bizData);
    },
    setBizData() {
      // meta数据在打开的时候就应该有，不能依靠监听taskbase不不然容易导致meta没有值
      this.bizData.meta = { taskId: this.AppID, taskVersion: this.taskVersion };
    }
  }
};
