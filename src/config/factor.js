import { commonUtil } from 'yxt-biz-pc';

export const FACTOR_FUCTIONS = {
  // 线下+混合式培训模块
  'O2O_TRAIN_TASK': 'o2o_train_task', // 培训中心-项目控制台-任务设计，【面授】【报名】按钮，o2o_train_task，要素未启用时，隐藏按钮，要素禁用后，按钮隐藏；
  'PROJECT_OVERVIEW_O2O': 'project_overview_o2o', // 培训中心-项目控制台-项目概览，【面授】【活动】选项，project_overview_o2o，要素未启用时不显示选项，要素禁用后不显示选项；
  'TEACHING_MGMT_O2O': 'teaching_mgmt_o2o', // 学员pc端教学管理-我的授课，【面授】选项，teaching_mgmt_o2o，要素未启用时隐藏选项，要素禁用后自定义处理；

  'SPEECH_TO_TEXT': 'o2o_transfer_text', //  语言转文字，要素未启用时隐藏选项，要素禁用后自定义处理；
  'ATTENDANCE_LOCATION': 'attendance_location', //  考勤
  'O2O_LOCATION': 'o2o_location', //  面授任务考勤的考勤管理

  // OJT带教模块
  'PROJECT_OJT': 'project_ojt', // 培训中心-项目控制台-带教，【带教】选项，project_ojt，要素未启用时选项隐藏，要素启用后选项显示，要素禁用后选项自定义（前端按仍然显示处理）
  'PROJECT_OJT_ADD_TRAINER': 'project_ojt_add_trainer', // 培训中心-项目控制台-带教，【添加指派】按钮，project_ojt_add_trainer，要素未启用时按钮隐藏，要素启用后按钮显示，要素禁用后按钮置灰
  'TEACHING_MGMT_OJT': 'teaching_mgmt_ojt', // 学员pc端教学管理-我的带教，【我的带教】选项，teaching_mgmt_ojt，要素未启用时隐藏选项，要素禁用后选项自定义（前端仍然按显示处理）；

  // 协同作业
  'O2O_TRAIN_COORDINATION_TASK': 'o2o_train_coordination_task', // 培训中心-项目控制台-任务设计、任务管理，【协同作业】选项，o2o_train_coordination_task，要素未启用时，隐藏选项，要素禁用后，按钮自定义；
  'O2O_TRAIN_GROUP_COORDINATION_TASK': 'o2o_train_group_coordination_task', // 项目中心-项目控制台-学员-小组-小组详情，【协同作业】选项，o2o_train_group_coordination_task，要素禁用后，选项自定义；
  'O2O_TRAIN_JFXF_COORDINATION_TASK': 'o2o_train_jfxf_coordination_task', // 项目中心-项目控制台-积分、学分设置，【协同作业】选项，o2o_train_jfxf_coordination_task，要素未启用时隐藏选项，要素禁用后自定义；
  'O2O_TRAIN_APPRAISAL_CALENDAR': 'o2o_train_appraisal_calendar', // 项目中心-项目控制台-概览，概览的日历筛选，o2o_train_appraisal_calendar，要素未启用时隐藏选项，要素禁用后自定义；

  // 模版库
  'PROJECT_TEMPLATEDEPOSITORY_SELECT': 'project_templatedepository_select', // 培训中心-模板库-创建项目，右侧【选择模板】，project_templatedepository_select，要素未启用时隐藏区域
  'PROJECT_TEMPLATEDEPOSITORY_SAVE': 'project_templatedepository_save', // 项目中心，项目控制台【存为模板】，项目列表【存为模板】，project_templatedepository_save，要素未启用时隐藏按钮

  // 培训报名模块
  'ENROLL_CONTROL': 'enroll_control', // 培训中心-项目控制台，【报名】选项，enroll_control，要素未启用时选项隐藏，禁用后选项显示；
  'ENROLL_SET': 'enroll_set', // 培训中心-项目控制-报名，【去设置】、【报名设置】按钮，enroll_set，要素禁用后按钮置灰；

  // 培训项目概览
  'PROJECT_OVERVIEW': 'project_overview', // 培训中心-项目控制台-项目概览，【概览】选项，project_overview，要素未启用时选项隐藏，要素禁用后选项隐藏；

  // 培训计划模块
  'PROJECT_O2O_PLAN': 'project_o2o_plan', // 培训中心-创建培训项目，【关联培训计划】字段，project_o2o_plan，要素未启用时隐藏字段；
  'PROJECT_TRAIN_PLAN_ADD': 'project_train_plan_add', // 培训中心-培训计划，【创建培训计划】【导入计划】，project_train_plan_add，要素未启用时隐藏按钮，要素启用后显示按钮，要素禁用后按钮置灰；

  // 小组群聊模块
  'PROJECT_GROUP_CHAT_ENABLE': 'project_group_chat_enable', // 培训中心-控制台-学员-小组管理，【开启群聊】按钮，每个小组下【开启群聊】【头像】按钮，project_group_chat_enable，要素未启用时不显示按钮；
  'STUDENT_GROUP_CHAT': 'student_group_chat', // 学员pc端-小组详情，【小组群聊】按钮，学员移动端-小组详情，小组列表，【小组群聊】按钮，student_group_chat，要素未启用时不显示按钮"

  // 选择岗位能力标签
  'PROJECT_SKILL_LABEL_CHOOSE': 'project_skill_label_choose', // 发布项目-能力标签，【选择能力】按钮，project_skill_label_choose，要素禁用后，按钮隐藏；

  // 教学管理-直播
  'TEACHING_MGMT_TLIVE': 'teaching_mgmt_tlive', // 教学管理直播处理
  // 教学管理-我的带教-创建任务
  'OJT_CREATE_ACTIVITY': 'ojt_create_activity', // 项目详情-创建活动任务
  'OJT_EDIT_ACTIVITY': 'ojt_edit_activity', // 项目详情-活动任务管理-编辑按钮
  'OJT_CREATE_QUESTIONNAIRE': 'ojt_create_questionnaire', // 项目详情-创建问卷任务
  'OJT_EDIT_QUESTIONNAIRE': 'ojt_edit_questionnaire', // 项目详情-问卷任务管理-编辑按钮
  'FACE_ENABLE': 'face_enable', // 【开启人脸识别】字段，face_enable，人脸识别能力禁用后置灰（已开启的功能仍然有效）
  'SWITCH_SCREEN_SET': 'prevent_switch_screen_set', // 防作弊限制，【考试切屏限制】选项，prevent_switch_screen_set，模块失效后，选项置灰
  'COMPULSORY_SUBMISSION_SET': 'compulsory_submission_set', // 防作弊限制，【无操作强制交卷】选项，compulsory_submission_set，无作弊防切屏时，选项置灰

  // 调研中心
  'FACETOFACETASK_EVALUATE': 'facetofacetask_evaluate', // 培训中心-项目中心，面授任务【学员评价】字段，facetofacetask_evaluate，要素未购买时字段隐藏，要素禁用时按钮隐藏处理
  // 证书模块
  'TRAIN_PROJECT_CERTIFICATE': 'train_project_certificate', // 培训项目控制台，左侧【证书】入口，train_project_certificate，要素未购买，入口隐藏；要素购买启用，入口显示；要素过期禁用，入口自定义（前端处理成显示）；
  'PROJECT_CERTIFICATE_EDIT': 'project_certificate_edit', // 培训项目控制台-左侧证书模块，【添加证书】【编辑】【删除】按钮，project_certificate_edit，要素未购买，按钮隐藏；要素购买启用，按钮显示；要素过期禁用，按钮置灰；
  'PROJECT_CERTIFICATE_AWARD': 'project_certificate_award', // 培训项目控制台-学员管理，【颁发证书】按钮，project_certificate_award，要素未购买，按钮隐藏；要素购买启用，按钮显示；要素过期禁用，按钮隐藏；
  'PROJECT_CERTIFICATE_CONDITION': 'project_certificate_condition', // 培训项目控制台-报名-前置条件，【拥有证书】字段，project_certificate_condition，要素未购买，字段隐藏；要素购买启用，字段显示；要素过期禁用，字段置灰；
  // 皮肤化
  'SKIN_CONTROL': 'skin_control', // 培训项目控制台，左侧【皮肤】入口，skin_control，要素未购买，入口隐藏；要素购买启用，入口显示；要素过期禁用，入口自定义（前端处理成显示）
  // 互动
  'INTERACT_CONTROL': 'interact_control', // 培训项目控制台，左侧【互动】入口，interact_control，要素未购买，入口隐藏；要素购买启用，入口显示；要素过期禁用，入口自定义（前端处理成显示）

  // 测练中心
  'PROJECT_CX_SETTING': 'project_cx_setting', // 项目中心-项目控制台-项目设计器，【测训设置】按钮，project_cx_setting，要素未启用时，按钮隐藏；要素禁用后不处理；

  // '讨论区'
  'DISCUSSION_ENABLE': 'discussion_enable', // 课件、课程编辑，功能设置，【讨论区】字段，discussion_enable，模块未启用时设置项隐藏，禁用后设置项隐藏
  'DISCUSSION_ENTRANCE': 'discussion_entrance', // 学员端，课件、课程详情【课程讨论专区】按钮，discussion_entrance，模块未启用时按钮隐藏，禁用后按钮隐藏
  // 购买的项目
  'PURCHASED_ITEMS': 'purchased_items', // 左侧菜单【购买的项目入口】入口
  // 任务
  'CEIBS_SKILL_MALL_CONTROL': 'ceibs_skill_mall_control', // 商城链接跳转
  'OJT_EVALUATE_QUESTIONNAIRE_PATTERN': 'ojt_evaluate_questionnaire_pattern', // 培训中心-项目控制台内【指派关系-评价-问卷_模式】
  'PROJECT_OJT_CONFIGURE': 'project_ojt_configure', // 培训中心-项目控制台内【通用设置-带教设置】
  'OJT_EVALUATE_QUESTIONNAIRE_WEIGHT': 'ojt_evaluate_questionnaire_weight', // 培训中心-项目控制台-带教-【指派关系-权重】
  'OJT_EVALUATE_QUESTIONNAIRE': 'ojt_evaluate_questionnaire', // 培训中心-项目控制台-带教-【添加评价 - 问卷】
  'PROJECT_OJT_ADD_EVALUATE': 'project_ojt_add_evaluate', // 培训中心-项目控制台-带教，【添加评价】
  'PROJECT_OJT_INTEGRAL_CREDIT': 'project_ojt_integral_credit', // 培训中心-项目控制台-带教-学分
  'PROJECT_CHANGE_OJT': 'project_change_ojt', // 培训中心-项目控制台-带教-复制
  'KNOWLEDGE_MALL_SWITCH': 'knowledge_mall_switch', // 项目管理台-创建任务，商城入口控制
  // IM群聊
  'IM_CHAT': 'IM_chat',
  'O2O_EDITOR': 'o2o_editor', // 非集团化创建/编辑项目-使用高级图文编辑器
  'GROUP_EDITOR': 'group_editor', // 集团化，创建/编辑项目-使用高级图文编辑器
  'TEACHING_MGMT_O2O_APPRAISAL': 'teaching_mgmt_o2o_appraisal', // 教学管理-鉴定评价-我的鉴定
  'O2O_SURVEY_APPRAISAL': 'o2o_survey_appraisal', // 调研要素点对鉴定模版按钮影响
  // 资源管理
  'PROJECT_RESOURCE_SWITCHES': 'Project_resource_switches', // 创建项目-添加资源开关
  'PROJECT_BUDGET_ADD': 'project_budget_add', // 创建项目-添加预算开关
  'SCHEDULE_RESOURCE_SWITCHES': 'Schedule_resource_switches', // 创建计划-添加资源开关
  'PLAN_BUDGET_ADD': 'plan_budget_add', // 创建计划-添加预算开关
  'CREATE_RESOURCE': 'Create_resource', // 培训资源-资源管理-明细列表-创建资源
  'CREATE_RESOURCE_TYPE': 'Create_resource_type', // 培训资源-资源管理-类型管理-创建类型
  'CREATE_RESOURCE_TEMPLATE': 'Create_resource_template', // 培训资源-资源模板-创建模板
  'CREATE_DEPARTMENTAL_BUDGET': 'Create_departmental_budget', // 培训资源-资源管理-培训预算-创建部门预算
  'CREATE_ANNUAL_BUDGET': 'create_annual_budget', // 培训资源-资源管理-培训预算-创建年度预算
  'CREATE_TYPE': 'create_type', // 培训资源-资源管理-培训预算-创建预算类型
  'O2O_TRAIN_APPRAISAL_MOULD': 'o2o_train_appraisal_mould',
  // 选择动态用户组
  'UDP_DYNAMIC_USERGROUP': 'udp_dynamic_usergroup',
  'MULTI-SHIFT_TASK_SWITCH': 'Multi-shift_task_switch', // 多班次开关
  'MULTI_SHIFT_ASSIGNMENT_FACE_TO_FACE': 'Multi_shift_assignment_face_to_face', // 多班次面授任务
  'MULTI_SHIFT_TASK_EXAM': 'Multi_shift_task_exam', // 多班次考试
  'MULTI_SHIFT_MISSION_LIVE': 'Multi_shift_mission_live', // 多班次直播
  'MULTI_SHIFT_TASKS': 'Multishift_Task_Add_Learners', // 多班次管理页
  'O2O_TRAIN_GATHER_CONTROL': 'o2o_train_gather_control' // 项目集 - 项目控制台部分功能
};
// 功能权限异常的key
// apis.o2o.common.factor.check.disabled
// apis.o2o.common.factor.check.need.upgrade
export const FACTOR_FUCTION_ERRORKEY = ['apis.o2o.common.factor.check.disabled', 'apis.o2o.common.factor.check.need.upgrade'];

// 6.要素上架-已购买已过期（禁用）-置灰
const OBJ_DISABLED_VALUES = [6]; // 功能禁用的所有值

// 0.要素下架-隐藏 1.要素上架-未购买-隐藏 3.要素上架-已购买未过期（启用）-隐藏 5.要素上架-已购买已过期（禁用）-隐藏
const OBJ_HIDDEN_VALUES = [0, 1, 3, 5]; // 功能隐藏的所有值

// 4.要素上架-已购买未过期（启用）-自定义 7.要素上架-已购买已过期（禁用）-自定义
const OBJ_CUSTOM_VALUES = [4, 7]; // 功能自定义的所有值

// 2.要素上架-已购买未过期（启用）-显示 3.要素上架-已购买未过期（启用）-隐藏 4.要素上架-已购买未过期（启用）-自定义
const OBJ_ENABLED_VALUES = [2, 3, 4]; // 功能可用状态

/**
 * 获取按钮类的状态控制
 * @param {String} funCode 功能点
 * @returns 状态控制策略
 */
export const getFactorFunctionStatus = (funCode) => {
  try {
    // 0.要素下架-隐藏 1.要素上架-未购买-隐藏 2.要素上架-已购买未过期（启用）-显示
    // 3.要素上架-已购买未过期（启用）-隐藏 4.要素上架-已购买未过期（启用）-自定义 5.要素上架-已购买已过期（禁用）-隐藏
    // 6.要素上架-已购买已过期（禁用）-置灰 7.要素上架-已购买已过期（禁用）-自定义
    const objStatus = commonUtil.checkTimeOutFnc(funCode);
    return {
      disabled: OBJ_DISABLED_VALUES.indexOf(objStatus) >= 0,
      hidden: OBJ_HIDDEN_VALUES.indexOf(objStatus) >= 0,
      custom: OBJ_CUSTOM_VALUES.indexOf(objStatus) >= 0,
      enabled: OBJ_ENABLED_VALUES.indexOf(objStatus) >= 0,
      value: objStatus
    };
  } catch (error) {
    return {};
  }
};
