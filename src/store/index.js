import Vue from 'vue';
import { readonlyPoxy } from 'main/utils-package';

// 初始化数据只能内部修改，外部只能通过mutations中的方法才能访问
let _state = Vue.observable({
  functionCode: '',
  permissionPoint: [],
  pid: '',
  projectStatus: 1, // 项目状态  0未保存 1未发布 2进行中 3结束 4归档 5删除 6暂停 7已撤回, 默认未发布
  projectIsImport: false,
  isCreatePlatform: false, // 登录平台是否为项目创建平台
  timeType: '', // 项目模式 0起止 1周期
  imId: '',
  imTypeProject: '',
  isCreate: '',
  period: 0, // 是否有阶段
  projectOrgId: null,
  isTemplate: false, // 是否是项目模版

  isProjectCenter: false, // 内容一体化项目 是否是中间平台
  isProjectSpu: false, // 内容一体化项目 是否是spu下发

  modeStatus: 1, // 当前带教模式 0 阶段 1 项目 2 任务, 默认是项目模式
  assignedStatus: 1, // 是否存在指派关系(0:否;1:是;)
  weightStatus: 0, // 是否开启权重(0:否;1:是;)
  ojtModeWasSet: 1, // 是否已设置带教模式(0:否;1:是;),可用值:0,1
  allowMultiQnr: 0, // 是否支持多问卷(0:否;1:是;),可用值:0,1
  sourceType: null, // 数据来源 0培训数据 1手动归档 2学习计划-1.0迁移 3混合培训-1.0迁移 4线下培训-1.0迁移 5师徒制度(OJT)-1.0迁移
  fromType: 0 // 来源 0:项目， 1: 人才发展, 默认0
});

/**
* 使用proxy代理对象更好的监听数据变化
* 设置只读对象
* @type {{name: string}}
*/
export const state = readonlyPoxy(_state);

export const mutations = {
  initStoreData(key, val) {
    if (key === 'init') {
      const { functionCode, permissionPoint, pid, projectStatus, projectIsImport, isCreatePlatform, imId, imTypeProject, timeType, isCreate, period, modeStatus, assignedStatus, weightStatus, ojtModeWasSet, allowMultiQnr, sourceType, isProjectCenter, isProjectSpu, projectOrgId, isTemplate, fromType } = val;

      _state['functionCode'] = functionCode;
      _state['permissionPoint'] = permissionPoint;
      _state['pid'] = pid;
      _state['projectStatus'] = projectStatus;
      _state['projectIsImport'] = projectIsImport;
      _state['isCreatePlatform'] = isCreatePlatform;
      _state['imId'] = imId;
      _state['imTypeProject'] = imTypeProject;
      _state['timeType'] = timeType;
      _state['isCreate'] = isCreate;
      _state['period'] = period;
      _state['modeStatus'] = modeStatus;
      _state['assignedStatus'] = assignedStatus;
      _state['weightStatus'] = weightStatus;
      _state['ojtModeWasSet'] = ojtModeWasSet;
      _state['allowMultiQnr'] = allowMultiQnr;
      _state['sourceType'] = sourceType;
      _state['projectOrgId'] = projectOrgId;
      _state['isProjectCenter'] = isProjectCenter;
      _state['isProjectSpu'] = isProjectSpu;
      _state['isTemplate'] = isTemplate;
      _state['fromType'] = fromType;
      return;
    }

    if (_state[key] !== undefined) {
      _state[key] = val;
    } else {
      throw new Error(`The key ${key} does not exist`);
    }
  }
};

export const getters = {
  // 来判断进入带教模式页面后是否有更新了页面的值
  originData() {
    return {
      weightStatus: _state.weightStatus,
      allowMultiQnr: _state.allowMultiQnr
    };
  }
};
