import { FACTOR_FUCTIONS, getFactorFunctionStatus } from 'main/config/factor.js';
import { commonUtil } from 'yxt-biz-pc';
import Vue from 'vue';
import { readonlyPoxy } from 'main/utils-package';

const initStatus = (state) => {
  Object.values(FACTOR_FUCTIONS).forEach((e) => {
    if (!state.status[e]) {
      state.status[e] = {};
    }
    Object.assign(state.status[e], getFactorFunctionStatus(e));
  });
  state.last = (new Date()).getTime();
};

let _state = Vue.observable({
  status: {},
  last: 0
});

export const state = readonlyPoxy(_state);

export const mutations = {
  INIT_FUN_STATUS() {
    initStatus(_state);
  },
  RESET_FUN_STATUS() {
    const dt = (new Date()).getTime();
    // 2s内重复触发
    if ((dt - _state.last) < 1000 * 2) {
      return;
    }
    _state.last = dt;
    commonUtil.refreshFactors(FACTOR_FUCTIONS).then(() => {
      initStatus(_state);
    });
  }
};

export const actions = {
  initFunctionStatus() {
    mutations.INIT_FUN_STATUS();
  },
  resetFunctionStatus() {
    mutations.RESET_FUN_STATUS();
  }
};

const initFactor = () => {
  Promise.all([commonUtil.preCheckFunctions(FACTOR_FUCTIONS)]).then(() => {
    actions.initFunctionStatus();
  });
};

export default {
  initFactor
};
