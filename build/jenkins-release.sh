#!/usr/bin/env sh
set -e
# 提取sourcemap到另外一个文件夹
# $1 源目录
# $2 目标目录
# $3 是否删除源
function pickupSourceMap() {
  if [ ! -d "$1" ]; then
    return
  fi
  if [ ! -d "$2" ]; then
    mkdir "$2"
  fi

  for item in $(ls "$1"); do
    if [ -d "$1"/"$item" ]; then
      pickupSourceMap "$1/$item" "$2/$item" "$3"
    elif [ -f "$1"/"$item" ] && [[ "$item" =~ .*map$ ]]; then
      if [ "$3" = "true" ]; then
        mv "$1/$item" "$2/$item"
      else
        cp "$1/$item" "$2/$item"
      fi
    fi
  done
}
VERSION=$1
IS_NPM=$2
PROJECT="yxt-ulcd-sdk"

echo "Releasing $VERSION ..."

# build
if [[ $IS_NPM == "true" ]]; then
  VERSION=$VERSION npm run dist
fi

npm version "$VERSION" --no-git-tag-version

git add -A
git commit -m "[build] $VERSION"
git tag -a v"$VERSION" -m "[release] $VERSION"

# publish
git push origin release
git push origin refs/tags/v"$VERSION"

if [[ $IS_NPM == "true" ]]; then
  yxtnpm publish
fi

IS_DEBUG=no USESOURCEMAP=true npm run publish:unpkg
npx css-assets-localizer --dir="./unpkg/${VERSION}/lib/theme-chalk/"

if [ -d $PROJECT ]; then
  rm -rf $PROJECT
fi
# 提取sourcemap到当前文件层
mkdir $PROJECT
# 将unpkg/$VERSION/lib/产物提出
pickupSourceMap unpkg/$(ls unpkg) $PROJECT true
# Delete all .map files in the unpkg folder
echo "删除unpkg下的sourcemap文件"
find unpkg -type f -name "*.map" -exec rm -f {} \;

# 上传$PROJECT文件夹下内容，到 /data/sourcemap/component 下

ossutil64 -c /root/.osssourcemapconfig cp -r -f "$PROJECT"/ oss://fe-rd-private/sourcemap/component/"$PROJECT"/ || true
# 清理现场
rm -rf $PROJECT
