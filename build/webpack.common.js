const path = require('path');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');

const config = require('./config');
const useSourceMap = process.env.USESOURCEMAP;

module.exports = {
  mode: 'production',
  devtool: useSourceMap === 'true' ? 'module-source-map' : 'none',
  entry: {
    app: ['./src/index.js']
  },
  output: {
    path: path.resolve(process.cwd(), './lib'),
    publicPath: '/dist/',
    filename: 'yxt-ulcd-sdk.js',
    chunkFilename: '[id].js',
    libraryExport: 'default',
    library: 'ELEMENT',
    libraryTarget: 'commonjs2'
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: config.alias,
    modules: ['node_modules']
  },
  externals: config.externals,
  performance: {
    hints: false
  },
  stats: {
    children: false
  },
  optimization: {
    minimize: false
  },
  module: {
    rules: [
      {
        test: /\.worker\.js$/,
        include: process.cwd(),
        exclude: /node_modules/,
        loader: 'worker-loader',
        options: {
          inline: 'fallback'
        }
      },
      {
        test: /\.(jsx?|babel|es6)$/,
        include: process.cwd(),
        exclude: config.jsexclude,
        loader: 'babel-loader'
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          compilerOptions: {
            preserveWhitespace: false,
            directives: {
              html(node, directiveMeta) {
                (node.props || (node.props = [])).push({
                  name: 'innerHTML',
                  value: `xss(_s(${directiveMeta.value}))`
                });

                (node.attrs || (node.attrs = [])).push({
                  name: 'data-rich-text',
                  value: '1'
                });
              },
              unsafehtml(node, directiveMeta) {
                (node.props || (node.props = [])).push({
                  name: 'innerHTML',
                  value: `_s(${directiveMeta.value})`
                });
              }
            }
          }
        }
      },
      {
        test: /\.(scss|css)$/,
        loaders: ['style-loader', 'css-loader', 'postcss-loader', 'sass-loader']
      },
      {
        test: /\.svg$/,
        loader: 'svg-sprite-loader',
        include: [path.resolve(__dirname, '../packages/svg-icon/icons/svg')],
        options: {
          symbolId: 'yxt-ulcd-sdk-[name]'
        }
      },
      {
        test: /\.(svg|otf|ttf|woff2?|eot|gif|png|jpe?g)(\?\S*)?$/,
        loader: 'url-loader',
        exclude: [path.resolve(__dirname, '../packages/svg-icon/icons/svg')],
        query: {
          limit: 10000,
          name: path.posix.join('static', '[name].[hash:7].[ext]')
        }
      }
    ]
  },
  plugins: [
    new ProgressBarPlugin(),
    new VueLoaderPlugin()
  ]
};
