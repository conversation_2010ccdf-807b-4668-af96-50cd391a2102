const conventionalChangelog = require('conventional-changelog');
const fs = require('fs');
const path = require('path');
const valid = require('semver').valid;
const exec = require('child_process').exec;
const str = 'git for-each-ref refs/tags --sort=taggerdate --format=\'%(refname)\'';
let currentTag = '';
let prevTag = '';

const isBeta = process.argv.includes('--beta'); // beta包changelog
const tagRegep = isBeta ? /^refs\/tags\/(v.*)$/ : /^refs\/tags\/(v[\d\.]*)$/;

exec(str, '', function(err, stdout) {
  if (err) {return;}
  let arr = stdout.split('\n');
  let currentIndex = 0;
  arr.reverse();
  for (let i = 0; i < arr.length; i++) {
    let matches = arr[i].match(tagRegep);
    if (matches) {
      currentTag = matches[1];
      currentIndex = i;
      break;
    }
  }

  if (currentIndex < arr.length - 1) {
    for (let j = currentIndex + 1; j < arr.length; j++) {
      let matches = arr[j].match(tagRegep);
      if (matches) {
        prevTag = matches[1];
        break;
      }
    }
  }
  convert();
});

function convert() {
  let upkg = path.resolve(__dirname, '../../examples');
  if (currentTag) {
    upkg = path.resolve(__dirname, `../../unpkg/${currentTag.split('v')[1]}/lib`);
  }

  const stats = fs.statSync(upkg);
  if (!stats.isDirectory()) return;

  const outputFile = path.resolve(upkg, 'changelog.md');
  conventionalChangelog({
    preset: 'angular'
  }, {
    linkCompare: false,
    linkReferences: false
  }, {
    from: prevTag,
    to: currentTag
  }, null, {
    generateOn(commit) {
      const reg = /^[\d\.]*$/;
      return isBeta ? valid(commit.version) : valid(commit.version) && reg.exec(commit.version); // 正式包拉取两次正式发布间的commit信息
    }
  })
    .pipe(fs.createWriteStream(outputFile, {flags: 'w'}));
}
