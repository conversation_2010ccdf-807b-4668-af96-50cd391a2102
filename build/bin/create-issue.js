const https = require('https');
const chalk = require('chalk');

/**
 *
 * @param {*} isPreview  是否为预发布测试
 */
function create() {
  let req;
  let d = new Date();
  const data = {
    'fields': {
      'project': {
        'id': '13721'
      },

      'issuetype': {
        'id': '10200'
      },
      'summary': '发布业务组件到stable环境',
      'customfield_13107': {
        'id': '12327'
      },
      'customfield_13105': {
        'id': '12324'
      },
      'customfield_13106': '无', // Jira单号清单
      'customfield_13111': `${d.getFullYear()}-${String.prototype.padStart.call(d.getMonth() + 1, 2, '0')}-${String.prototype.padStart.call(d.getDate(), 2, '0')}`, // 发布操作时间
      'customfield_13109': 'Tab: 公共支撑\napi:tf.env-yxt.xxv2.op.media.totally.deploy',
      'assignee': {
        'name': 'jiajj',
        'key': 'jiajj'
      }
    }
  };
  req = https.request({
    method: 'POST',
    hostname: 'jira.yunxuetang.com.cn',
    path: '/rest/api/2/issue',
    headers: {
      Authorization: 'Basic cWxtOjI3NTc4OXR0',
      'Content-Type': 'application/json'
    }
  }, () => {
    console.log(chalk.green('issue created'));
  }).on('error', e => {
    console.log(chalk.red('failed to create issus'));
  });

  req.write(JSON.stringify(data));
  req.end();
}

module.exports = create;
