/* eslint-disable no-undef */
const https = require('https');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const { exit } = require('process');
const { version } = require('../../package.json');

const Token = 'zu2rpsx37ZmTm6g1M9wx'; // jenkins的gitlab Token
const build = require('/var/jenkins_home/jenkins_shell/nodeTools/trigger-mediajob'); // 触发jenkins job
// const createJira = require('./create-issue');
const { argv } = process;
// let targetFiles = [];
const pid = 260;
const gitlabUrl = 'https://git-inner.yunxuetang.com.cn/api/v4/';
// const versionData = require(path.resolve(__dirname, '../../version.json'));
let BRANCH = 'dev';
const basePath = 'common/basepc/yxt-ulcd-sdk/';// 基础路径

if (argv.includes('--stable')) {
  BRANCH = 'stable';
}
const textFilter = /\.(md|js|css|svg|vue|json|txt)$/;
// const dirs = ['lib', 'foreground', 'backstage'];

// function isPreviewVersion() {
//   return !/^[\d\.]*$/.exec(version);
// }

// 是否为组件版本
function isVersion(name) {
  // eslint-disable-next-line no-useless-escape
  return /^[\d\.'-beta''stable']*$/.exec(name);
}

function getPageData(branch, pageNum) {
  // console.log(chalk.red('getPageData ================= pageNum: '), pageNum);
  return new Promise((resolve, reject) => {
    // console.log(chalk.red('getPageData ++++++++++++++ pageNum: '), pageNum, branch);
    https.get(`${gitlabUrl}projects/${pid}/repository/tree?recursive=true&ref=${branch}&path=${basePath}&page=${pageNum}`, {
      headers: {
        'PRIVATE-TOKEN': Token,
        'Content-Type': 'application/json'
      }
    }, res => {
      const totalPage = res.headers['x-total-pages'];
      res.on('data', (d) => {
        let files = JSON.parse(d.toString());
        if (files instanceof Array && files.length > 0) {
          files = files.filter(item => item.type !== 'tree').map(item => item.path);
        } else {
          files = [];
        }
        const fileInfoList = [];
        const remoteVersionDirList = [];
        files.forEach((filePath) => {
        // for (let [index, filePath] of files.entries()) {
          const version = filePath.replace(basePath, '').split('/')[0];
          // console.log('pageNum:', pageNum, ' - index: ', index, ' - version: ', version, ' - filePath', filePath);
          const fileItem = {
            path: filePath,
            version: isVersion(version) ? version : null
          };
          fileInfoList.push(fileItem);
          if (fileItem.version) remoteVersionDirList.push(version);
        // }
        });
        resolve({ fileInfoList, remoteVersionDirList, totalPage });
      });
    }).on('error', (e) => {
      console.log(chalk.red('get files tree failed'));
      reject(e);
      exit();
    });
  });
}

// 获取仓库中已存在的文件树
async function getFilesTree(branch) {
  let currentPage = 1;
  let totalPage = 1;
  let remoteVersionDirList = [];
  const fileInfoList = [];

  // console.log('获取远端文件 ...');
  while (currentPage <= totalPage) {
  // for (let currentPage = 0; currentPage <= totalPage; currentPage++) {
    // eslint-disable-next-line no-await-in-loop
    const pageDataInfo = await getPageData(branch, currentPage);
    // console.log('pageDataInfo - fileInfoList', pageDataInfo.fileInfoList);
    // 总页数
    // eslint-disable-next-line prefer-destructuring
    totalPage = pageDataInfo.totalPage;
    // 远端的文件列表
    fileInfoList.push(...pageDataInfo.fileInfoList);
    // 从远端文件目录获取的版本号，有重复情况，用于去重排序后留下最近三个，其他的删除
    remoteVersionDirList.push(...pageDataInfo.remoteVersionDirList);
    currentPage++;
  // }
  }
  // console.log('获取远端文件 完成 ...');
  // remoteVersionDirList.push(...['2.3.11', '2.3.10']);
  // 版本号去重
  remoteVersionDirList = Array.from(new Set(remoteVersionDirList));
  // console.log('排序前版本列表: ', remoteVersionDirList);
  // 版本号排序
  remoteVersionDirList.sort((v1, v2) => {
    // console.log('------v1: ', v1, ' - v2: ', v2);
    // 版本号以.分组
    const v1StrArr = v1.split('.');
    // 修订号和版本类型
    const v1ReviseArr = v1StrArr[2].split('-');
    // 截取出正式版本： 主版本号.次版本号.修订号
    const v1MainVersion = `${v1StrArr[0]}.${v1StrArr[1]}.${v1ReviseArr[0]}`;
    // 版本类型：主版本没类型，还有stable、beta
    const v1Type = v1ReviseArr[1] || null;

    // 版本号以.分组
    const v2StrArr = v2.split('.');
    // 修订号和版本类型
    const v2ReviseArr = v2StrArr[2].split('-');
    // 截取出正式版本： 主版本号.次版本号.修订号
    const v2MainVersion = `${v2StrArr[0]}.${v2StrArr[1]}.${v2ReviseArr[0]}`;
    // 版本类型：主版本没类型，还有stable、beta
    const v2Type = v2ReviseArr[1] || null;

    // let [major1, minor1, revise1] = v1MainVersion.split('.');
    const major1 = Number(v1StrArr[0]);
    const minor1 = Number(v1StrArr[1]);
    const revise1 = Number(v1ReviseArr[0]);
    // let [major2, minor2, revise2] = v2MainVersion.split('.');
    const major2 = Number(v2StrArr[0]);
    const minor2 = Number(v2StrArr[1]);
    const revise2 = Number(v2ReviseArr[0]);
    // console.log('------v1MainVersion: ', v1MainVersion, ' - v2MainVersion: ', v2MainVersion);
    let mainVersionRes = false;
    if (major1 > major2) {
      mainVersionRes = true;
    } else if (major1 === major2) {
      if (minor1 > minor2) {
        mainVersionRes = true;
      } else if (minor1 === minor2) {
        if (revise1 > revise2) {
          mainVersionRes = true;
        }
      }
    }
    // console.log('mainVersionRes: ', mainVersionRes);
    let res = false;
    if (mainVersionRes) {
      // 正式版本对比
      res = true;
      // console.log('11111');
    } else if (v1MainVersion === v2MainVersion) {
      // 正式版本相同时，比较具体版本小号
      if (v1Type && v2Type) {
        // 如果存在stable 或者 beta号则对此进行详细对比
        if (v1Type > v2Type) {
          // stable 大于 beta
          res = true;
          // console.log('22222');
        }
        if (v1Type === v2Type) {
          // 如果测试版本类型相同，即：均为beta 或 均为stable，则对比测试版本号（最后的小版本号）
          const v1test = Number(v1StrArr[3]) || '';
          const v2test = Number(v2StrArr[3]) || '';
          res = v1test > v2test;
          // console.log('33333');
        }
      } else {
        // console.log('v1Type: ', v1Type, ' - v1Type: ', v2Type);
        // 正式版本相同，beta或者stable有一个号存在需要判断
        // 不存在beta\stable则代表灰度及产线使此版本，为正式版本的最终版
        if (!v1Type) {
          // 如果v1没有beta\stable，代表此为灰度产线使用的最终版本，需要放到后边
          res = true;
          // console.log('44444');
        }
        if (!v2Type) {
          // 如果v1没有beta\stable，代表此为灰度产线使用的最终版本，需要放到后边
          res = false;
          // console.log('55555');
        }
      }
    }
    // console.log(v1, ' > ', v2, ' ---> ', res);
    // return -1;
    return res ? 1 : -1;
  });

  // console.log(chalk.green('远端文件'), fileInfoList.length, '个...');
  // console.log(chalk.green('远端组件版本列表'), remoteVersionDirList);
  // 保留最近的3个版本，其他版本需要清除
  remoteVersionDirList.splice(-3);
  console.info(chalk.green('需要清除的远端UMD组件版本：'), remoteVersionDirList);
  // // 反转元素顺序
  // remoteVersionDirList.reverse();

  return Promise.resolve({ fileInfoList, remoteVersionDirList });
}

// 获取指定目录的文件列表
function getFiles(dir) {
  const filesList = [];
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const source = path.join(dir, file);
    if (fs.statSync(source).isDirectory()) {
      const subFilesList = getFiles(source);
      filesList.push(...subFilesList);
    } else {
      // targetFiles.push(source);
      filesList.push(source);
    }
  });
  return filesList;
}

/**
 *  提交参数包括删除、新增、更新
 * @param {*} localFiles 本地需要提交的文件
 * @param {*} fileInfoList 远端文件（包括需要更新及删除）
 * @param {*} remoteVersionDirList 需要清除的组件版本
 */
function generateActions(localFiles, fileInfoList, remoteVersionDirList) {
  // let actions = [{
  //   'action': 'delete',
  //   'file_path': 'common/basepc/yxt-pc/2.3.10-beta.5/lib/locale/lang/zh-CN.js'
  // }, {
  //   'action': 'delete',
  //   'file_path': 'common/basepc/yxt-pc/2.3.10-beta.5/lib/locale/lang/zh-TW.js'
  // }];

  // const actions = [{
  //   action: 'update',
  //   file_path: `${basePath}versions.json`,
  //   content: JSON.stringify(versionData)
  // }];

  const actions = [];
  const existsFiles = [];
  // console.log('generateActions +++++ : ', fileInfoList);

  fileInfoList.forEach(fileInfo => {
    // console.log(file);
    if (fileInfo.version) {
      // 有版本号就代表是远端已经存储过此版本组件
      if (remoteVersionDirList.includes(fileInfo.version)) {
        // 如果版本号在需要清除的版本列表，则需要添加一个delete的action
        actions.push({
          action: 'delete',
          file_path: fileInfo.path
        });
      }
    } else {
      // 没有版本号就代表此文件位于'lib', 'foreground', 'backstage'之类的目录，需要更新，会在之后的逻辑里进行处理
      existsFiles.push(fileInfo.path);
    }
  });

  // 遍历本地需要提交的文件
  localFiles.forEach(file => {
    // 新增当前版本，单独存储到版本号命名的目录中
    const action = {
      action: 'create',
      file_path: `${basePath}${version}/${file}`
    };

    // 当前的lib、backstage、foreground目录包含当前文件时，需要用新文件对老文件进行update
    const updatePath = `${basePath}${file}`;
    const updateAction = {
      action: existsFiles.includes(updatePath) ? 'update' : 'create',
      file_path: updatePath
    };

    // 文件内容处理
    const buffer = fs.readFileSync(path.resolve(__dirname, '../../unpkg/', version, file));
    if (textFilter.exec(file)) {
      action.content = buffer.toString();
      updateAction.content = buffer.toString();
    } else {
      action.content = buffer.toString('base64');
      action.encoding = 'base64';

      updateAction.content = buffer.toString('base64');
      updateAction.encoding = 'base64';
    }

    actions.push(action, updateAction);
  });

  return actions;
}

// 更新media1项目相应分支
function upload(actions, msg, env = BRANCH) {
  // console.info('upload actions - ');
  return new Promise((resolve, reject) => {
    const postData = {
      branch: env,
      // commit_message: msg || 'yxt-ulcd-sdk 组件升级版本',
      commit_message: msg || `yxt-ulcd-sdk 组件升级版本 ${version}`,
      actions
    };

    const req = https.request(`${gitlabUrl}projects/${pid}/repository/commits`, {
      headers: {
        'PRIVATE-TOKEN': Token,
        'Content-Type': 'application/json'
      },
      method: 'POST'
    }, res => {
      console.log('success');
      res.on('data', d => {
        const str = d.toString();
        const data = JSON.parse(str);
        console.log(str);
        if (!data.id) throw new Error(str);
      });
      setTimeout(() => build(env, basePath), 2000);
      if (env === 'stable') {
        // createJira(isPreviewVersion());
      }
      resolve('success');
    }).on('error', (e) => {
      console.error('POST ERROR');
      reject(e);
      throw new Error('POST ERROR');
    });
    req.write(JSON.stringify(postData));
    req.end();
  });
}

// git提交的入口函数
async function uploadFiles(branch = BRANCH) {
  const src = path.join(__dirname, '../../unpkg', version);
  let stat;
  try {
    stat = fs.statSync(src);
  } catch (e) {
    console.log(chalk.red('path error'));
    exit();
  }

  if (stat.isDirectory()) {
    const targetFiles = getFiles(src);
    // 路径片段分隔符统一，Windows 上是 \，POSIX 上是 /
    // 本地上传的组件文件列表
    const localFiles = targetFiles.map(file => path.relative(src, file)).map(file => file.split(path.sep).join('/'));
    // console.log('++++++++++', localFiles);

    // {远端的文件列表, 需要清除的版本号列表}
    const { fileInfoList, remoteVersionDirList } = await getFilesTree(branch);
    // console.log('uploadFiles - getFilesTree - fileInfoList', fileInfoList);
    // console.log('uploadFiles - getFilesTree - remoteVersionDirList', remoteVersionDirList);

    // git提交的action参数
    const actions = generateActions(localFiles, fileInfoList, remoteVersionDirList);
    // 执行git提交
    upload(actions, null, branch);
  }
}

// 默认执行git提交
uploadFiles();
