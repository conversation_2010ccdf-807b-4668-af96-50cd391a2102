var Components = require('../../components.json');
var fs = require('fs');
var render = require('json-templater/string');
var uppercamelcase = require('uppercamelcase');
var path = require('path');
var endOfLine = require('os').EOL;

var OUTPUT_PATH = path.join(__dirname, '../../src/index.js');
var IMPORT_TEMPLATE =
  "import {{name}} from '../packages/{{package}}/index.js';";
var INSTALL_COMPONENT_TEMPLATE = '  {{name}}';
var MAIN_TEMPLATE = `/* Automatically generated by './build/bin/build-entry.js' */

{{include}}
import commonUtil from '../packages/common-util/index.js';
import xss, { whiteList } from 'xss';
const components = [
{{install}}
];

components.forEach(Component => {
  Component.mixins = Component.mixins || [];
  Component.mixins.push({
    created() {
      // 业务组件埋点统计
      // from：组件名
      // aspect：事件发生描述
      // version：组件库版本
      window.YxtFeLog && window.YxtFeLog.track('e_component', {
        properties: {
          from: Component.name,
          aspect: 'load',
          version: '{{version}}'
        }
      });
    }
  });
});

try {
  for (const key in whiteList) Object.hasOwnProperty.call(whiteList, key) && whiteList[key].push('style');
} catch (error) {
}

const setStaticCdnUrl = function(Vue) {
  try {
    if (Vue) {
      const baseCommon = (typeof window !== 'undefined' && window.feConfig && window.feConfig.common);
      Vue.prototype.xss = (html, options) => xss(html, Object.assign({ stripIgnoreTag: true }, options || {}));
      Vue.prototype.$imagesBaseUrl = (baseCommon && window.feConfig.common.imagesBaseUrl) || 'https://images.yxt.com/';
      Vue.prototype.$staticBaseUrl = (baseCommon && window.feConfig.common.staticBaseUrl) || 'https://stc.yxt.com/';
    }
  } catch (e) {
    console.log(e);
  }
};

const install = function(Vue, config = {}) { // config: {env: 'dev', domain: {orginit: ''}}
  setStaticCdnUrl(Vue);

  components.forEach(component => {
    Vue.component(component.name, component);
  });

  Vue.use(commonUtil.bindDirectives);
};

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  version: '{{version}}',
  install,
{{list}}
};
`;

delete Components.font;
delete Components.api;
delete Components['common-util'];
delete Components['change-langs'];

var ComponentNames = Object.keys(Components);

var openDataComponentNames = [
  'open-data',
  'user-name',
  'dept-name',
  'open-data-dd',
  'position-name'
];

var includeComponentTemplate = [];
var installTemplate = [];
var listTemplate = [];
var excludeInstallTemplate = ['nav-manage-store', 'title-util'];
ComponentNames.forEach(name => {
  var componentName = uppercamelcase(name);
  // open-data: skip import
  if (openDataComponentNames.includes(name) === false) {
    includeComponentTemplate.push(
      render(IMPORT_TEMPLATE, {
        name: componentName,
        package: name
      })
    );
  }

  // open-data: skip registry
  if (!excludeInstallTemplate.includes(name)) {
    installTemplate.push(
      render(INSTALL_COMPONENT_TEMPLATE, {
        name: componentName,
        component: name
      })
    );
  }

  listTemplate.push(`  ${componentName}`);
});

[
  'commonUtil'
].forEach(name => {
  listTemplate.push(`  ${name}`);
});

var template = render(MAIN_TEMPLATE, {
  include: includeComponentTemplate.join(endOfLine),
  install: installTemplate.join(',' + endOfLine),
  version: process.env.VERSION || require('../../package.json').version,
  list: listTemplate.join(',' + endOfLine)
});

fs.writeFileSync(OUTPUT_PATH, template);
console.log('[build entry] DONE:', OUTPUT_PATH);
