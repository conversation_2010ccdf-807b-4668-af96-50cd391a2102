const fs = require('fs');
const fse = require('fs-extra');
const path = require('path');

const packageInfo = require('../../package.json');
const versionJson = path.resolve(__dirname, '../../versions.json');
const currentVersion = packageInfo.version;
const isRelease = !/alpha|beta/.test(currentVersion);
const outPath = path.resolve(__dirname, `../../unpkg/${currentVersion}/lib`);
const srcDir = path.resolve(__dirname, '../../lib');

function logSuccess(msg) {
  console.log('\x1B[36m%s\x1B[0m', msg);
}

// 更新 versions.json 记录umd发布版本信息
function updateVersion() {
  const rawdata = fs.readFileSync(versionJson);
  const versionData = JSON.parse(rawdata);
  if (isRelease) {
    versionData['dist-tags']['latest'] = currentVersion;
  } else {
    versionData['dist-tags']['beta'] = currentVersion;
  }
  versionData['versions'][currentVersion] = {
    version: currentVersion
  };
  versionData['time'][currentVersion] = new Date();

  const str = JSON.stringify(versionData);

  fs.writeFile(versionJson, str, function(err) {
    if (err) {
      console.error(err);
    }
    const msg = 'versions.json is updated \n';
    logSuccess(msg);
  });
}

function copyFiles() {
  try {
    fse.copySync(srcDir, outPath, { overwrite: true });
    console.log('\x1B[2m%s\x1B[0m', `${new Date()}`);
    const msg = `Success! umd files were copied to /unpkg/${currentVersion}/lib`;
    logSuccess(msg);
    updateVersion();
  } catch (e) {
    console.error(e);
  }
}
copyFiles();
