const fs = require('fs');
const path = require('path');
const version = require('../../package.json').version;
const versionFile = path.resolve(__dirname, '../../versions.json');
const versionJson = require(versionFile);

function logSuccess(msg) {
  console.log('\x1B[36m%s\x1B[0m', msg);
}

versionJson.versions[version] = {'version': version};
const isRelease = !/alpha|beta/.test(version);

if (isRelease) {
  versionJson['dist-tags']['latest'] = version;
} else {
  versionJson['dist-tags']['beta'] = version;
}

const str = JSON.stringify(versionJson);

fs.writeFile(versionFile, str, function(err) {
  if (err) {
    console.error(err);
  }
  const msg = 'versions.json is updated \n';
  logSuccess(msg);
});

