const path = require('path');
const { VueLoaderPlugin } = require('vue-loader');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');

const fs = require('fs');
const config = require('./ulcd.com.config');
const wabpackConfig = require('../config');
const rootPath = process.cwd();
module.exports = (env, argv)=>{
  let {entryPath, resolevPath, entryName} = config;
  entryPath = `${rootPath}/${entryPath}`;
  resolevPath = `${rootPath}${resolevPath}`;
  console.log(entryPath, resolevPath);
  if (entryPath && resolevPath) {
    if (!fs.existsSync(entryPath) || !fs.existsSync(resolevPath)) {
      console.error('Directory not found.');
      process.exit(1);
    }
  }
  return {
    mode: 'production',
    entry: {
      [entryName]: entryPath
    },
    devtool: 'eval-cheap-module-source-map', // 如果选用其它的可能会导致引用组件后无法进行调试
    output: {
      filename: '[name].js',
      path: path.resolve(resolevPath, './dist'),
      library: 'MyComponent',
      libraryTarget: 'umd'
    },
    optimization: {
      minimize: false
    },
    resolve: {
      alias: wabpackConfig.alias,
      extensions: ['.js', '.vue']
    },
    externals: {
      'yxt-pc': 'YXTPC',
      'vue': 'Vue',
      'vuex': 'Vuex',
      'vue-router': 'VueRouter',
      'yxt-biz-pc': 'YXTBIZ',
      lottie: 'lottie',
      'yxt-project-designer': 'YXTPD'
    },
    module: {
      rules: [
        {
          test: /\.worker\.js$/,
          include: process.cwd(),
          exclude: /node_modules/,
          loader: 'worker-loader',
          options: {
            inline: 'fallback'
          }
        },
        {
          test: /\.js$/,
          exclude: /node_modules/,
          loader: 'babel-loader'
        },
        {
          test: /\.vue$/,
          loader: 'vue-loader',
          options: {
            esModule: false
          }
        },
        {
          test: /\.(sa|sc|c)ss$/,
          use: [
            'style-loader',
            'css-loader',
            'postcss-loader',
            'sass-loader'
          ]
        },
        {
          test: /\.(jpg|jpeg|png|gif)$/,
          use: ['url-loader']
        }
      ]
    },
    plugins: [
      new ProgressBarPlugin(),
      new VueLoaderPlugin()
    ],
    watch: true,
    watchOptions: {
      ignored: /node_modules/
    }
  };
};
