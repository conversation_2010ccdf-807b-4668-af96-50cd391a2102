
let portOrder = 'lsof -i :8888';
let exec = require('child_process').exec;
const config = require('./ulcd.com.config');
const rootPath = process.cwd();
let resolevPath = `${rootPath}${config.resolevPath}`;
let httpOrder = `http-server ${resolevPath}/dist  --cors -p 8888 -c-1`;
const emitServer = ()=>{
  console.log('emitServer');
  const httpServer = exec(httpOrder);
  httpServer.stdout.on('data', data => {
    console.log('stdout 输出:', data);
  });
  httpServer.stderr.on('data', err => {
    console.log('error 输出:', err);
  });
};
exec(portOrder, function(err, stdout, stderr) {
  if (err) {
    emitServer();
  } else {
    stdout.split('\n').filter(function(line) {
      let p = line.trim().split(/\s+/);
      let address = p[1];
      if (address !== undefined && address !== 'PID') {
        exec('kill ' + address, function(err, stdout, stderr) {
          if (err) {
            return console.log('释放指定端口失败！！');
          }
          console.log('占用指定端口的程序被成功杀掉！');
          emitServer();
        });
      }
    });
  }

});
