#!/usr/bin/env sh
set -e

VERSION=$1
IS_NPM=$2

if [[ $VERSION =~ "beta" ]]
then
  echo "Releasing $VERSION ..."

  # build
  if [[ $IS_NPM == "true" ]]
  then
    VERSION=$VERSION npm run dist
  fi

  npm version $VERSION --no-git-tag-version

  git add -A
  git commit -m "[build] $VERSION"
  git tag -a v$VERSION -m "[release] $VERSION"

  # publish
  git push origin $BRANCH
  git push origin refs/tags/v$VERSION

  if [[ $IS_NPM == "true" ]]
  then
    yxtnpm publish --tag test
  fi

  IS_DEBUG=yes npm run publish:unpkg:beta
  npx css-assets-localizer --dir="./unpkg/${VERSION}/lib/theme-chalk/"
  npm run media:sync
else
  echo "version must contains 'beta' !"
fi
