var path = require('path');
var fs = require('fs');
var nodeExternals = require('webpack-node-externals');
var Components = require('../components.json');

var utilsList = fs.readdirSync(path.resolve(__dirname, '../src/utils'));
var mixinsList = fs.readdirSync(path.resolve(__dirname, '../src/mixins'));
var transitionList = fs.readdirSync(path.resolve(__dirname, '../src/transitions'));
var externals = {};

Object.keys(Components).forEach(function(key) {
  externals[`yxt-ulcd-sdk/packages/${key}`] = `yxt-ulcd-sdk/lib/${key}`;
});

externals['yxt-ulcd-sdk/src/locale'] = 'yxt-ulcd-sdk/lib/locale';
externals['yxt-ulcd-sdk/src/index'] = 'yxt-ulcd-sdk/lib/index';
utilsList.forEach(function(file) {
  file = path.basename(file, '.js');
  externals[`yxt-ulcd-sdk/src/utils/${file}`] = `yxt-ulcd-sdk/lib/utils/${file}`;
});
mixinsList.forEach(function(file) {
  file = path.basename(file, '.js');
  externals[`yxt-ulcd-sdk/src/mixins/${file}`] = `yxt-ulcd-sdk/lib/mixins/${file}`;
});
transitionList.forEach(function(file) {
  file = path.basename(file, '.js');
  externals[`yxt-ulcd-sdk/src/transitions/${file}`] = `yxt-ulcd-sdk/lib/transitions/${file}`;
});

externals = [Object.assign({
  vue: 'vue'
}, externals), nodeExternals()];

exports.externals = externals;

exports.alias = {
  main: path.resolve(__dirname, '../src'),
  packages: path.resolve(__dirname, '../packages'),
  examples: path.resolve(__dirname, '../examples'),
  'yxt-ulcd-sdk': path.resolve(__dirname, '../'),
  '@': path.resolve(__dirname, '../examples')
  // 'vue-lottie': 'vue-lottie/dist/build.js'
};

exports.vue = {
  root: 'Vue',
  commonjs: 'vue',
  commonjs2: 'vue',
  amd: 'vue'
};

exports.jsexclude = /node_modules|utils\/popper\.js|utils\/htmlEncode\.js|utils\/date\.js/;
