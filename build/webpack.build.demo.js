const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const ProgressBarPlugin = require('progress-bar-webpack-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const UglifyJsPlugin = require('terser-webpack-plugin');

const config = require('./config');

const isProd = process.env.NODE_ENV === 'production';
const isPlay = !!process.env.PLAY_ENV;

const webpackConfig = {
  mode: process.env.NODE_ENV,
  entry: isProd ? {
    docs: './examples/entry.js'
  } : (isPlay ? './examples/play.js' : './examples/entry.js'),
  output: {
    path: path.resolve(process.cwd(), './dist/'),
    publicPath: process.env.CI_ENV || '',
    filename: '[name].[hash:7].js',
    chunkFilename: isProd ? '[name].[hash:7].js' : '[name].js'
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: config.alias,
    modules: ['node_modules']
  },
  devServer: {
    host: '0.0.0.0',
    port: 8099,
    publicPath: '/',
    hot: true
  },
  performance: {
    hints: false
  },
  stats: {
    children: false
  },
  module: {
    rules: [
      {
        test: /\.worker\.js$/,
        include: process.cwd(),
        exclude: /node_modules/,
        loader: 'worker-loader',
        options: {
          inline: 'fallback'
        }
      },
      {
        test: /\.(jsx?|babel|es6)$/,
        include: process.cwd(),
        exclude: config.jsexclude,
        loader: 'babel-loader'
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          compilerOptions: {
            preserveWhitespace: false,
            directives: {
              html(node, directiveMeta) {
                (node.props || (node.props = [])).push({
                  name: 'innerHTML',
                  value: `xss(_s(${directiveMeta.value}))`
                });

                (node.attrs || (node.attrs = [])).push({
                  name: 'data-rich-text',
                  value: '1'
                });
              },
              unsafehtml(node, directiveMeta) {
                (node.props || (node.props = [])).push({
                  name: 'innerHTML',
                  value: `_s(${directiveMeta.value})`
                });
              }
            }
          }
        }
      },
      {
        test: /\.(scss|css)$/,
        use: [
          isProd ? MiniCssExtractPlugin.loader : 'style-loader',
          'css-loader',
          'postcss-loader',
          'sass-loader'
        ]
      },
      {
        test: /\.md$/,
        use: [
          {
            loader: 'vue-loader',
            options: {
              compilerOptions: {
                preserveWhitespace: false,
                directives: {
                  html(node, directiveMeta) {
                    (node.props || (node.props = [])).push({
                      name: 'innerHTML',
                      value: `xss(_s(${directiveMeta.value}))`
                    });

                    (node.attrs || (node.attrs = [])).push({
                      name: 'data-rich-text',
                      value: '1'
                    });
                  },
                  unsafehtml(node, directiveMeta) {
                    (node.props || (node.props = [])).push({
                      name: 'innerHTML',
                      value: `_s(${directiveMeta.value})`
                    });
                  }
                }
              }
            }
          },
          {
            loader: path.resolve(__dirname, './md-loader/index.js')
          }
        ]
      },
      {
        test: /\.svg$/,
        loader: 'svg-sprite-loader',
        include: [path.resolve(__dirname, '../packages/svg-icon/icons/svg')],
        options: {
          symbolId: 'yxt-ulcd-sdk-[name]'
        }
      },
      {
        test: /\.(svg|otf|ttf|woff2?|eot|gif|png|jpe?g)(\?\S*)?$/,
        loader: 'url-loader',
        exclude: [path.resolve(__dirname, '../packages/svg-icon/icons')],
        // todo: 这种写法有待调整
        query: {
          limit: 10000,
          name: path.posix.join('static', '[name].[hash:7].[ext]')
        }
      }
    ]
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(),
    new HtmlWebpackPlugin({
      template: './examples/index.tpl',
      filename: './index.html',
      favicon: './examples/favicon.ico'
    }),
    new CopyWebpackPlugin([
      { from: 'examples/versions.json' },
      { from: 'examples/WW_verify_4Ca7whsz7sFIsSfb.txt' },
      { from: 'examples/WW_verify_bjLKqhncmiZBDghu.txt' },
      { from: 'examples/WW_verify_johLYqep89JgwWN0.txt' }
    ]),
    new ProgressBarPlugin(),
    new VueLoaderPlugin(),
    new webpack.DefinePlugin({
      'process.env.FAAS_ENV': JSON.stringify(process.env.FAAS_ENV)
    }),
    new webpack.LoaderOptionsPlugin({
      vue: {
        compilerOptions: {
          preserveWhitespace: false,
          directives: {
            html(node, directiveMeta) {
              (node.props || (node.props = [])).push({
                name: 'innerHTML',
                value: `xss(_s(${directiveMeta.value}))`
              });

              (node.attrs || (node.attrs = [])).push({
                name: 'data-rich-text',
                value: '1'
              });
            },
            unsafehtml(node, directiveMeta) {
              (node.props || (node.props = [])).push({
                name: 'innerHTML',
                value: `_s(${directiveMeta.value})`
              });
            }
          }
        }
      }
    })
  ],
  optimization: {
    minimizer: []
  },
  devtool: '#eval-source-map'
};

if (isProd) {
  webpackConfig.externals = {
    'yxt-pc': 'YXTPC',
    'vue': 'Vue',
    'vuex': 'Vuex',
    'vue-router': 'VueRouter',
    'yxt-biz-pc': 'YXTBIZ',
    lottie: 'lottie',
    'yxt-project-designer': 'YXTPD'
  };
  webpackConfig.plugins.push(
    new MiniCssExtractPlugin({
      filename: '[name].[contenthash:7].css'
    })
  );
  webpackConfig.optimization.minimizer.push(
    new UglifyJsPlugin({
      cache: true,
      parallel: true,
      sourceMap: false
    }),
    new OptimizeCSSAssetsPlugin({})
  );
  // https://webpack.js.org/configuration/optimization/#optimizationsplitchunks
  webpackConfig.optimization.splitChunks = {
    cacheGroups: {
      vendor: {
        test: /\/src\//,
        name: 'element-ui',
        chunks: 'all'
      }
    }
  };
  webpackConfig.devtool = false;
} else {
  // webpackConfig.externals = {
  //   'yxt-pc': 'YXTPC',
  //   'vue': 'Vue',
  //   'vuex': 'Vuex',
  //   'vue-router': 'VueRouter'
  // };
}

module.exports = webpackConfig;
