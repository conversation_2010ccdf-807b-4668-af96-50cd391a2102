const path = require('path');

const ProgressBarPlugin = require('progress-bar-webpack-plugin');
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const TerserPlugin = require('terser-webpack-plugin');

const isDebug = process.env.IS_DEBUG;
const useSourceMap = process.env.USESOURCEMAP;
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const config = require('./config');
const isReport = !!process.env.report;

const cwd = process.cwd();
const pkgsPath = path.resolve(cwd, 'package.json');
const pkgs = require(pkgsPath);
const packageName = pkgs.name;

const rootPath = path.resolve(__dirname, '../');

let libName = 'YXTULCDSDK';
if (![].includes(packageName)) {
  libName += packageName.toUpperCase();
}

function hashPort(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash += str.charCodeAt(i);
  }
  return hash % 10000;
}
const port = hashPort(packageName);

module.exports = {
  mode: 'production',
  devtool: isDebug === 'no' && useSourceMap !== 'true' ? 'none' : 'module-source-map',
  entry: {
    app: [path.resolve(cwd, 'index.js')]
  },
  output: {
    path: path.resolve(rootPath, './lib'),
    library: libName,
    libraryTarget: 'umd',
    libraryExport: 'default',
    filename: `${packageName}.js`
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: config.alias,
    modules: ['node_modules']
  },
  // externals: config.externals,
  performance: {
    hints: false
  },
  stats: {
    children: false
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        sourceMap: true,
        terserOptions: {
          compress: {
            drop_console: isDebug === 'no'
          }
        }
      })
    ]
  },
  externals: {
    'vue': 'Vue',
    'vuex': 'Vuex',
    'vue-router': 'VueRouter',
    'axios': 'axios',
    'vue-i18n': 'VueI18n',
    'yxt-pc': 'YXTPC',
    'yxt-biz-pc': 'YXTBIZ',
    lottie: 'lottie',
    'yxt-project-designer': 'YXTPD'
  },
  module: {
    rules: [
      {
        test: /\.worker\.js$/,
        exclude: /node_modules/,
        loader: 'worker-loader',
        options: {
          inline: 'fallback'
        }
      },
      {
        test: /\.(jsx?|babel|es6)$/,
        exclude: config.jsexclude,
        loader: 'babel-loader'
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          compilerOptions: {
            preserveWhitespace: false,
            directives: {
              html(node, directiveMeta) {
                (node.props || (node.props = [])).push({
                  name: 'innerHTML',
                  value: `xss(_s(${directiveMeta.value}))`
                });

                (node.attrs || (node.attrs = [])).push({
                  name: 'data-rich-text',
                  value: '1'
                });
              },
              unsafehtml(node, directiveMeta) {
                (node.props || (node.props = [])).push({
                  name: 'innerHTML',
                  value: `_s(${directiveMeta.value})`
                });
              }
            }
          }
        }
      },
      {
        test: /\.(scss|css)$/,
        use: [
          'style-loader',
          'css-loader',
          'postcss-loader',
          'sass-loader'
        ]
      },
      {
        test: /\.(svg|otf|ttf|woff2?|eot|gif|png|jpe?g)(\?\S*)?$/,
        loader: 'url-loader',
        query: {
          limit: 10000,
          name: path.posix.join('static', '[name].[hash:7].[ext]')
        }
      }
    ]
  },
  plugins: [new ProgressBarPlugin(), new VueLoaderPlugin()].concat(isReport ? [new BundleAnalyzerPlugin({analyzerPort: port})] : [])
};
