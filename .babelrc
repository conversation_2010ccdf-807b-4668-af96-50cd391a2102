{"presets": [["env", {"loose": true, "modules": false, "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}}], "stage-2"], "plugins": ["transform-vue-jsx"], "env": {"utils": {"presets": [["env", {"loose": true, "modules": "commonjs", "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}}]], "plugins": [["module-resolver", {"root": ["yxt-ulcd-sdk"], "alias": {"yxt-ulcd-sdk/src": "yxt-ulcd-sdk/lib"}}]]}, "test": {"plugins": ["istanbul"]}}}