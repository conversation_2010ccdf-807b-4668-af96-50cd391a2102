/* Automatically generated by './build/lerna/build-entry.js' */

import CoursePage from '../../packages/course-page/index.js';
import DevelopViewPlan from '../../packages/develop-view-plan/index.js';
import SimpleCoursePage from '../../packages/simple-course-page/index.js';
import commonUtil from '../../packages/common-util/index.js';
import xss, { whiteList } from 'xss';
const components = [
  CoursePage,
  DevelopViewPlan,
  SimpleCoursePage
];

components.forEach(Component => {
  Component.mixins = Component.mixins || [];
  Component.mixins.push({
    created() {
      // 业务组件埋点统计
      // from：组件名
      // aspect：事件发生描述
      // version：组件库版本
      window.YxtFeLog && window.YxtFeLog.track('e_component', {
        properties: {
          from: Component.name,
          aspect: 'load',
          version: '1.1.53'
        }
      });
    }
  });
});

try {
  for (const key in whiteList) Object.hasOwnProperty.call(whiteList, key) && whiteList[key].push('style');
} catch (error) {
}

const setStaticCdnUrl = function(Vue) {
  try {
    if (Vue) {
      const baseCommon = (typeof window !== 'undefined' && window.feConfig && window.feConfig.common);
      Vue.prototype.xss = (html, options) => xss(html, Object.assign({ stripIgnoreTag: true }, options || {}));
      Vue.prototype.$imagesBaseUrl = (baseCommon && window.feConfig.common.imagesBaseUrl) || 'https://images.yxt.com/';
      Vue.prototype.$staticBaseUrl = (baseCommon && window.feConfig.common.staticBaseUrl) || 'https://stc.yxt.com/';
    }
  } catch (e) {
    console.log(e);
  }
};

let installed = false;
const install = function(Vue, config = {}) { // config: {env: 'dev', domain: {orginit: ''}}
  if (installed) return;

  setStaticCdnUrl(Vue);
  components.forEach(component => {
    Vue.component(component.name, component);
  });
  installed = true;
};

/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

const list = {
  version: '1.1.53',
  CoursePage,
  DevelopViewPlan,
  SimpleCoursePage,
  commonUtil
};

if (!window.YXTULCDSDK) {
  window.YXTULCDSDK = {};
}
Object.assign(window.YXTULCDSDK, list);

export default {
  install,
  ...list
};
