{"name": "examination", "version": "0.0.1", "description": "> TODO: description", "author": "liyadong <<EMAIL>>", "license": "ISC", "main": "index.js", "files": ["index.js"], "repository": {"type": "git", "url": "https://git-inner.yunxuetang.com.cn/xxv2/xuanxing2-ms-ulcd/frontend/yxt-ulcd-sdk.git"}, "packages": ["examing", "practicing", "wrong-item"], "scripts": {"build:file": "node ../../build/lerna/build-entry.js $(basename $PWD)", "build:umd": "cross-env ../../node_modules/webpack/bin/webpack.js --config ../../build/webpack.lerna.unpkg.js", "build": "npm run build:file && npm run build:umd", "analyze": "cross-env report=yes ../../node_modules/webpack/bin/webpack.js --config ../../build/webpack.lerna.unpkg.js"}, "devDependencies": {"cross-env": "^7.0.3"}}