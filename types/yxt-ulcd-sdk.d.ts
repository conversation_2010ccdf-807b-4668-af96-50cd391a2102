/** Header Component */
import { YxtbizApi } from './api';
/** Api Component */
import { YxtUlcdSdkExaming } from './examing';
import { YxtUlcdSdkPracticing } from './practicing';
import { YxtUlcdSdkPlayframe } from './playframe';
import { YxtUlcdSdkCoursePlayer } from './course-player';
import { YxtUlcdSdkCoursePage } from './course-page';

import { YxtUlcdSdkPointsExchange } from './points-exchange';
import { YxtUlcdSdkO2oPlayFrame } from './o2o-play-frame';
import { YxtUlcdSdkActivity } from './activity';
import { YxtUlcdSdkOutlink } from './outlink';

import { YxtUlcdSdkO2oHomeworkTask } from './o2o-homework-task';
import { YxtUlcdSdkAttend } from './attend';

import { YxtUlcdSdkO2oEvaluationTask } from './o2o-evaluation-task';
import { YxtUlcdSdkExamine } from './examine';

import { YxtUlcdSdkO2oIdentTask } from './o2o-ident-task';

import { YxtUlcdSdkDevelopViewPlan } from './develop-view-plan';
import { YxtUlcdSdkAddStudentsToTeam } from './add-students-to-team';

import { YxtUlcdSdkLiveTask } from './live-task';
import { YxtUlcdSdkMultiTask } from './multi-task';
import { YxtUlcdSdkO2oOfflineTask } from './o2o-offline-task';
import { YxtUlcdSdkSurveyQues } from './survey-ques';
import { YxtUlcdSdkVerbalSparring } from './verbal-sparring';
import { YxtUlcdSdkDiscussTask } from './discuss-task';
import { YxtUlcdSdkUacdPlayFrame } from './uacd-play-frame';
import { YxtUlcdSdkSpeech } from './speech';
import { YxtUlcdSdkDrill } from './drill';
import { YxtUlcdSdkEvaluation } from './evaluation';

export class Api extends YxtbizApi { }

/** Examing Component */
export class Examing extends YxtUlcdSdkExaming {}

/** Practicing Component */
export class Practicing extends YxtUlcdSdkPracticing {}

/** Playframe Component */
export class Playframe extends YxtUlcdSdkPlayframe {}

/** CoursePlayer Component */
export class CoursePlayer extends YxtUlcdSdkCoursePlayer {}

/** CoursePage Component */
export class CoursePage extends YxtUlcdSdkCoursePage {}

/** PointsExchange Component */
export class PointsExchange extends YxtUlcdSdkPointsExchange {}

/** O2oPlayFrame Component */
export class O2oPlayFrame extends YxtUlcdSdkO2oPlayFrame {}

/** Activity Component */
export class Activity extends YxtUlcdSdkActivity {}

/** Outlink Component */
export class Outlink extends YxtUlcdSdkOutlink {}
/** O2oHomeworkTask Component */
export class O2oHomeworkTask extends YxtUlcdSdkO2oHomeworkTask {}

/** Attend Component */
export class Attend extends YxtUlcdSdkAttend {}
import { YxtUlcdSdkSurveying } from './surveying';
import { YxtUlcdSdkWrongItem } from './wrong-item';
import { YxtUlcdSdkSimpleCoursePage } from './simple-course-page';

/** O2oEvaluationTask Component */
export class O2oEvaluationTask extends YxtUlcdSdkO2oEvaluationTask {}

/** Examine Component */
export class Examine extends YxtUlcdSdkExamine {}
/** Examing Component */

/** Surveying Component */
export class Surveying extends YxtUlcdSdkSurveying {}
/** WrongItem Component */
export class WrongItem extends YxtUlcdSdkWrongItem {}
/** SimpleCoursePage Component */
export class SimpleCoursePage extends YxtUlcdSdkSimpleCoursePage {}

/** O2oIdentTask Component */
export class O2oIdentTask extends YxtUlcdSdkO2oIdentTask {}

/** DevelopViewPlan Component */
export class DevelopViewPlan extends YxtUlcdSdkDevelopViewPlan {}

/** AddStudentsToTeam Component */
export class AddStudentsToTeam extends YxtUlcdSdkAddStudentsToTeam {}

/** LiveTask Component */
export class LiveTask extends YxtUlcdSdkLiveTask {}

/** MultiTask Component */
export class MultiTask extends YxtUlcdSdkMultiTask {}

/** O2oOfflineTask Component */
export class O2oOfflineTask extends YxtUlcdSdkO2oOfflineTask {}

/** SurveyQues Component */
export class SurveyQues extends YxtUlcdSdkSurveyQues {}

/** VerbalSparring Component */
export class VerbalSparring extends YxtUlcdSdkVerbalSparring {}

/** DiscussTask Component */
export class DiscussTask extends YxtUlcdSdkDiscussTask {}

/** UacdPlayFrame Component */
export class UacdPlayFrame extends YxtUlcdSdkUacdPlayFrame {}

/** Speech Component */
export class Speech extends YxtUlcdSdkSpeech {}

/** Drill Component */
export class Drill extends YxtUlcdSdkDrill {}

/** Evaluation Component */
export class Evaluation extends YxtUlcdSdkEvaluation {}
